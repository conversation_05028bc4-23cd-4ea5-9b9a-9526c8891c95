<?php
/**
 * API Oracle Manager للعقود المحسنة
 * Enhanced Oracle Manager API
 */

require_once __DIR__ . '/../cors.php';
require_once __DIR__ . '/../config/database.php';

class OracleManagerAPI {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';
        
        try {
            switch ($method) {
                case 'GET':
                    $this->handleGet($action);
                    break;
                case 'POST':
                    $this->handlePost($action);
                    break;
                case 'PUT':
                    $this->handlePut($action);
                    break;
                default:
                    $this->sendError('Method not allowed', 405);
            }
        } catch (Exception $e) {
            $this->sendError($e->getMessage(), 500);
        }
    }
    
    private function handleGet($action) {
        switch ($action) {
            case 'prices':
                $this->getPrices();
                break;
            case 'exchange-rates':
                $this->getExchangeRates();
                break;
            case 'supported-tokens':
                $this->getSupportedTokens();
                break;
            case 'supported-currencies':
                $this->getSupportedCurrencies();
                break;
            case 'price-history':
                $this->getPriceHistory();
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    private function handlePost($action) {
        switch ($action) {
            case 'update-price':
                $this->updatePrice();
                break;
            case 'update-exchange-rate':
                $this->updateExchangeRate();
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    private function handlePut($action) {
        switch ($action) {
            case 'bulk-update-prices':
                $this->bulkUpdatePrices();
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    /**
     * جلب أسعار العملات المستقرة
     */
    private function getPrices() {
        try {
            $networkId = $_GET['network_id'] ?? 1;
            $tokenId = $_GET['token_id'] ?? null;
            $currency = $_GET['currency'] ?? null;
            
            $conn = $this->db->getConnection();
            
            $sql = "
                SELECT 
                    opf.*,
                    st.token_symbol,
                    st.token_name,
                    sn.network_name
                FROM oracle_price_feeds opf
                JOIN supported_tokens st ON opf.token_id = st.id
                JOIN supported_networks sn ON opf.network_id = sn.id
                WHERE opf.network_id = ? AND opf.is_active = 1
            ";
            
            $params = [$networkId];
            
            if ($tokenId) {
                $sql .= " AND opf.token_id = ?";
                $params[] = $tokenId;
            }
            
            if ($currency) {
                $sql .= " AND opf.currency = ?";
                $params[] = $currency;
            }
            
            $sql .= " ORDER BY st.token_symbol, opf.currency";
            
            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
            $prices = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // تجميع الأسعار حسب العملة المستقرة
            $groupedPrices = [];
            foreach ($prices as $price) {
                $tokenSymbol = $price['token_symbol'];
                if (!isset($groupedPrices[$tokenSymbol])) {
                    $groupedPrices[$tokenSymbol] = [
                        'token_id' => $price['token_id'],
                        'token_symbol' => $price['token_symbol'],
                        'token_name' => $price['token_name'],
                        'network_name' => $price['network_name'],
                        'prices' => []
                    ];
                }
                
                $groupedPrices[$tokenSymbol]['prices'][] = [
                    'currency' => $price['currency'],
                    'price' => floatval($price['price']),
                    'confidence_level' => $price['confidence_level'],
                    'last_updated_at' => $price['last_updated_at'],
                    'is_stale' => $price['is_stale']
                ];
            }
            
            $this->sendSuccess(array_values($groupedPrices));
            
        } catch (Exception $e) {
            $this->sendError('Failed to fetch prices: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * جلب أسعار صرف العملات المحلية
     */
    private function getExchangeRates() {
        try {
            $baseCurrency = $_GET['base_currency'] ?? 'USD';
            $targetCurrency = $_GET['target_currency'] ?? null;
            
            $conn = $this->db->getConnection();
            
            $sql = "
                SELECT * FROM fiat_exchange_rates 
                WHERE base_currency = ? AND is_active = 1
            ";
            
            $params = [$baseCurrency];
            
            if ($targetCurrency) {
                $sql .= " AND target_currency = ?";
                $params[] = $targetCurrency;
            }
            
            $sql .= " ORDER BY target_currency";
            
            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
            $rates = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // تحويل البيانات للتنسيق المطلوب
            $formattedRates = [];
            foreach ($rates as $rate) {
                $formattedRates[] = [
                    'currency_pair' => $rate['base_currency'] . '/' . $rate['target_currency'],
                    'base_currency' => $rate['base_currency'],
                    'target_currency' => $rate['target_currency'],
                    'exchange_rate' => floatval($rate['exchange_rate']),
                    'inverse_rate' => floatval($rate['inverse_rate']),
                    'confidence_level' => $rate['confidence_level'],
                    'last_updated_at' => $rate['last_updated_at'],
                    'source' => $rate['source']
                ];
            }
            
            $this->sendSuccess($formattedRates);
            
        } catch (Exception $e) {
            $this->sendError('Failed to fetch exchange rates: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * جلب العملات المستقرة المدعومة
     */
    private function getSupportedTokens() {
        try {
            $networkId = $_GET['network_id'] ?? 1;
            
            $conn = $this->db->getConnection();
            
            $stmt = $conn->prepare("
                SELECT 
                    st.*,
                    sn.network_name,
                    COUNT(DISTINCT opf.currency) as supported_currencies_count
                FROM supported_tokens st
                JOIN supported_networks sn ON st.network_id = sn.id
                LEFT JOIN oracle_price_feeds opf ON st.id = opf.token_id AND opf.is_active = 1
                WHERE st.network_id = ? AND st.is_active = 1
                GROUP BY st.id
                ORDER BY st.token_symbol
            ");
            
            $stmt->execute([$networkId]);
            $tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $this->sendSuccess($tokens);
            
        } catch (Exception $e) {
            $this->sendError('Failed to fetch supported tokens: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * جلب العملات المحلية المدعومة
     */
    private function getSupportedCurrencies() {
        try {
            $conn = $this->db->getConnection();
            
            // جلب العملات من أسعار الصرف
            $stmt = $conn->prepare("
                SELECT DISTINCT target_currency as currency
                FROM fiat_exchange_rates 
                WHERE is_active = 1
                UNION
                SELECT DISTINCT currency
                FROM oracle_price_feeds 
                WHERE is_active = 1
                ORDER BY currency
            ");
            
            $stmt->execute();
            $currencies = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $this->sendSuccess($currencies);
            
        } catch (Exception $e) {
            $this->sendError('Failed to fetch supported currencies: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * تحديث سعر عملة مستقرة
     */
    private function updatePrice() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            $networkId = $input['network_id'] ?? null;
            $tokenId = $input['token_id'] ?? null;
            $currency = $input['currency'] ?? null;
            $price = $input['price'] ?? null;
            $confidenceLevel = $input['confidence_level'] ?? 9500;
            $updatedBy = $input['updated_by'] ?? null;
            
            if (!$networkId || !$tokenId || !$currency || !$price) {
                $this->sendError('Missing required fields', 400);
                return;
            }
            
            $conn = $this->db->getConnection();
            
            // تحديث أو إدراج السعر
            $stmt = $conn->prepare("
                INSERT INTO oracle_price_feeds 
                (network_id, token_id, currency, price, confidence_level, updated_by, last_updated_at, is_active)
                VALUES (?, ?, ?, ?, ?, ?, NOW(), 1)
                ON DUPLICATE KEY UPDATE
                price = VALUES(price),
                confidence_level = VALUES(confidence_level),
                updated_by = VALUES(updated_by),
                last_updated_at = NOW(),
                is_stale = 0
            ");
            
            $stmt->execute([$networkId, $tokenId, $currency, $price, $confidenceLevel, $updatedBy]);
            
            $this->sendSuccess(['message' => 'Price updated successfully']);
            
        } catch (Exception $e) {
            $this->sendError('Failed to update price: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * تحديث سعر صرف عملة محلية
     */
    private function updateExchangeRate() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            $baseCurrency = $input['base_currency'] ?? 'USD';
            $targetCurrency = $input['target_currency'] ?? null;
            $exchangeRate = $input['exchange_rate'] ?? null;
            $updatedBy = $input['updated_by'] ?? null;
            $source = $input['source'] ?? 'manual';
            
            if (!$targetCurrency || !$exchangeRate) {
                $this->sendError('Missing required fields', 400);
                return;
            }
            
            $inverseRate = 1 / $exchangeRate;
            
            $conn = $this->db->getConnection();
            
            $stmt = $conn->prepare("
                INSERT INTO fiat_exchange_rates 
                (base_currency, target_currency, exchange_rate, inverse_rate, updated_by, source, last_updated_at, is_active)
                VALUES (?, ?, ?, ?, ?, ?, NOW(), 1)
                ON DUPLICATE KEY UPDATE
                exchange_rate = VALUES(exchange_rate),
                inverse_rate = VALUES(inverse_rate),
                updated_by = VALUES(updated_by),
                source = VALUES(source),
                last_updated_at = NOW()
            ");
            
            $stmt->execute([$baseCurrency, $targetCurrency, $exchangeRate, $inverseRate, $updatedBy, $source]);
            
            $this->sendSuccess(['message' => 'Exchange rate updated successfully']);
            
        } catch (Exception $e) {
            $this->sendError('Failed to update exchange rate: ' . $e->getMessage(), 500);
        }
    }
    
    private function sendSuccess($data) {
        echo json_encode([
            'success' => true,
            'data' => $data
        ]);
    }
    
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message
        ]);
    }
}

// تشغيل API
$api = new OracleManagerAPI();
$api->handleRequest();
?>
