<?php
/**
 * إدارة إعدادات العروض المجانية
 * Free Offers Settings Management API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: http://localhost:3000');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../utils/response.php';

// التحقق من صلاحيات المدير
$auth = new AuthMiddleware();
$user = $auth->authenticate();

if (!$user || !$user['is_admin']) {
    sendErrorResponse('غير مصرح لك بالوصول', 403);
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    switch ($method) {
        case 'GET':
            getFreeOffersSettings($connection);
            break;
            
        case 'POST':
        case 'PUT':
            updateFreeOffersSettings($connection);
            break;
            
        default:
            sendErrorResponse('طريقة غير مدعومة', 405);
    }
    
} catch (Exception $e) {
    logError("Free offers settings API error: " . $e->getMessage());
    sendErrorResponse('خطأ في الخادم: ' . $e->getMessage(), 500);
}

/**
 * جلب إعدادات العروض المجانية
 */
function getFreeOffersSettings($connection) {
    try {
        // جلب الإعدادات من قاعدة البيانات
        $stmt = $connection->prepare("
            SELECT setting_key, setting_value, setting_type, description 
            FROM platform_settings 
            WHERE setting_key IN (
                'freeOffersEnabled', 'maxFreeOffersPerUser', 'maxFreeOfferAmount',
                'freeOfferTimeLimit', 'freeOfferRequireVerification', 
                'freeOfferMinAmount', 'freeOfferCooldownPeriod'
            )
        ");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تحويل النتائج إلى تنسيق مناسب
        $formattedSettings = [];
        foreach ($settings as $setting) {
            $value = $setting['setting_value'];
            
            // تحويل القيم حسب النوع
            switch ($setting['setting_type']) {
                case 'boolean':
                    $value = (bool)$value;
                    break;
                case 'integer':
                    $value = (int)$value;
                    break;
                case 'float':
                    $value = (float)$value;
                    break;
            }
            
            $formattedSettings[$setting['setting_key']] = [
                'value' => $value,
                'type' => $setting['setting_type'],
                'description' => $setting['description']
            ];
        }
        
        // إضافة الإعدادات الافتراضية إذا لم تكن موجودة
        $defaultSettings = getDefaultFreeOffersSettings();
        foreach ($defaultSettings as $key => $default) {
            if (!isset($formattedSettings[$key])) {
                $formattedSettings[$key] = $default;
            }
        }
        
        // جلب إحصائيات العروض المجانية
        $stats = getFreeOffersStats($connection);
        
        sendSuccessResponse([
            'settings' => $formattedSettings,
            'stats' => $stats
        ], 'تم جلب إعدادات العروض المجانية بنجاح');
        
    } catch (Exception $e) {
        throw new Exception('خطأ في جلب إعدادات العروض المجانية: ' . $e->getMessage());
    }
}

/**
 * تحديث إعدادات العروض المجانية
 */
function updateFreeOffersSettings($connection) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['settings'])) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $settings = $input['settings'];
        $allowedSettings = [
            'freeOffersEnabled', 'maxFreeOffersPerUser', 'maxFreeOfferAmount',
            'freeOfferTimeLimit', 'freeOfferRequireVerification', 
            'freeOfferMinAmount', 'freeOfferCooldownPeriod'
        ];
        
        $connection->beginTransaction();
        
        foreach ($settings as $key => $value) {
            if (!in_array($key, $allowedSettings)) {
                continue;
            }
            
            // التحقق من صحة القيم
            validateSettingValue($key, $value);
            
            // تحديث أو إدراج الإعداد
            $stmt = $connection->prepare("
                INSERT INTO platform_settings (setting_key, setting_value, setting_type, description) 
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value),
                updated_at = CURRENT_TIMESTAMP
            ");
            
            $settingInfo = getSettingInfo($key);
            $stmt->execute([
                $key,
                $settingInfo['type'] === 'boolean' ? ($value ? '1' : '0') : (string)$value,
                $settingInfo['type'],
                $settingInfo['description']
            ]);
        }
        
        $connection->commit();
        
        // تسجيل النشاط
        logAdminActivity($connection, $_SESSION['user_id'], 'update_free_offers_settings', [
            'settings' => $settings
        ]);
        
        sendSuccessResponse(null, 'تم تحديث إعدادات العروض المجانية بنجاح');
        
    } catch (Exception $e) {
        $connection->rollBack();
        throw new Exception('خطأ في تحديث إعدادات العروض المجانية: ' . $e->getMessage());
    }
}

/**
 * جلب إحصائيات العروض المجانية
 */
function getFreeOffersStats($connection) {
    try {
        $stats = [];
        
        // إجمالي العروض المجانية
        $stmt = $connection->prepare("SELECT COUNT(*) as total FROM offers WHERE is_free = 1");
        $stmt->execute();
        $stats['totalFreeOffers'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // العروض المجانية النشطة
        $stmt = $connection->prepare("SELECT COUNT(*) as active FROM offers WHERE is_free = 1 AND is_active = 1");
        $stmt->execute();
        $stats['activeFreeOffers'] = $stmt->fetch(PDO::FETCH_ASSOC)['active'];
        
        // المستخدمون الذين لديهم عروض مجانية
        $stmt = $connection->prepare("
            SELECT COUNT(DISTINCT user_id) as users 
            FROM offers 
            WHERE is_free = 1 AND is_active = 1
        ");
        $stmt->execute();
        $stats['usersWithFreeOffers'] = $stmt->fetch(PDO::FETCH_ASSOC)['users'];
        
        // العروض المجانية المنتهية الصلاحية
        $stmt = $connection->prepare("
            SELECT COUNT(*) as expired 
            FROM user_free_offers 
            WHERE expires_at < NOW() AND is_active = 1
        ");
        $stmt->execute();
        $stats['expiredFreeOffers'] = $stmt->fetch(PDO::FETCH_ASSOC)['expired'];
        
        return $stats;
        
    } catch (Exception $e) {
        logError("Error getting free offers stats: " . $e->getMessage());
        return [];
    }
}

/**
 * التحقق من صحة قيمة الإعداد
 */
function validateSettingValue($key, $value) {
    switch ($key) {
        case 'maxFreeOffersPerUser':
            if (!is_int($value) || $value < 1 || $value > 10) {
                throw new Exception('عدد العروض المجانية يجب أن يكون بين 1 و 10');
            }
            break;
            
        case 'maxFreeOfferAmount':
            if (!is_int($value) || $value < 10 || $value > 10000) {
                throw new Exception('مبلغ العرض المجاني يجب أن يكون بين 10 و 10000 USDT');
            }
            break;
            
        case 'freeOfferMinAmount':
            if (!is_int($value) || $value < 1 || $value > 1000) {
                throw new Exception('أقل مبلغ للعرض المجاني يجب أن يكون بين 1 و 1000 USDT');
            }
            break;
            
        case 'freeOfferTimeLimit':
            if (!is_int($value) || $value < 3600 || $value > 604800) { // 1 hour to 1 week
                throw new Exception('مدة صلاحية العرض المجاني يجب أن تكون بين ساعة واحدة وأسبوع');
            }
            break;
            
        case 'freeOfferCooldownPeriod':
            if (!is_int($value) || $value < 0 || $value > 86400) { // 0 to 24 hours
                throw new Exception('فترة الانتظار يجب أن تكون بين 0 و 24 ساعة');
            }
            break;
    }
}

/**
 * الحصول على معلومات الإعداد
 */
function getSettingInfo($key) {
    $settings = [
        'freeOffersEnabled' => ['type' => 'boolean', 'description' => 'تفعيل العروض المجانية'],
        'maxFreeOffersPerUser' => ['type' => 'integer', 'description' => 'أقصى عدد عروض مجانية لكل مستخدم'],
        'maxFreeOfferAmount' => ['type' => 'integer', 'description' => 'أقصى مبلغ للعرض المجاني (USDT)'],
        'freeOfferTimeLimit' => ['type' => 'integer', 'description' => 'مدة صلاحية العرض المجاني بالثواني'],
        'freeOfferRequireVerification' => ['type' => 'boolean', 'description' => 'إلزامية التحقق للعروض المجانية'],
        'freeOfferMinAmount' => ['type' => 'integer', 'description' => 'أقل مبلغ للعرض المجاني (USDT)'],
        'freeOfferCooldownPeriod' => ['type' => 'integer', 'description' => 'فترة الانتظار بين العروض المجانية بالثواني']
    ];
    
    return $settings[$key] ?? ['type' => 'string', 'description' => ''];
}

/**
 * الحصول على الإعدادات الافتراضية
 */
function getDefaultFreeOffersSettings() {
    return [
        'freeOffersEnabled' => [
            'value' => true,
            'type' => 'boolean',
            'description' => 'تفعيل العروض المجانية'
        ],
        'maxFreeOffersPerUser' => [
            'value' => 3,
            'type' => 'integer',
            'description' => 'أقصى عدد عروض مجانية لكل مستخدم'
        ],
        'maxFreeOfferAmount' => [
            'value' => 1000,
            'type' => 'integer',
            'description' => 'أقصى مبلغ للعرض المجاني (USDT)'
        ],
        'freeOfferTimeLimit' => [
            'value' => 86400,
            'type' => 'integer',
            'description' => 'مدة صلاحية العرض المجاني بالثواني (24 ساعة)'
        ],
        'freeOfferRequireVerification' => [
            'value' => false,
            'type' => 'boolean',
            'description' => 'إلزامية التحقق للعروض المجانية'
        ],
        'freeOfferMinAmount' => [
            'value' => 10,
            'type' => 'integer',
            'description' => 'أقل مبلغ للعرض المجاني (USDT)'
        ],
        'freeOfferCooldownPeriod' => [
            'value' => 3600,
            'type' => 'integer',
            'description' => 'فترة الانتظار بين العروض المجانية بالثواني (ساعة واحدة)'
        ]
    ];
}

/**
 * تسجيل نشاط المدير
 */
function logAdminActivity($connection, $adminId, $action, $details = []) {
    try {
        $stmt = $connection->prepare("
            INSERT INTO admin_activity_log (admin_id, action, details, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $adminId,
            $action,
            json_encode($details, JSON_UNESCAPED_UNICODE),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        // تجاهل أخطاء تسجيل النشاط
        logError("Failed to log admin activity: " . $e->getMessage());
    }
}

/**
 * تسجيل الأخطاء
 */
function logError($message) {
    error_log("[" . date('Y-m-d H:i:s') . "] Free Offers Settings API: " . $message);
}
?>
