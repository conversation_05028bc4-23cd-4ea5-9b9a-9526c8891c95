# العقود الذكية المحسنة - iKAROS P2P

## نظرة عامة

هذا المجلد يحتوي على العقود الذكية المحسنة لمنصة iKAROS P2P للتداول اللامركزي للعملات المستقرة.

## العقود المتاحة

### 1. CoreEscrow.sol
**العقد الرئيسي للضمان**
- إدارة الصفقات والضمان
- دعم متعدد العملات
- نظام أمان متقدم
- إدارة النزاعات

**الوظائف الرئيسية:**
- `createTrade()` - إنشاء صفقة جديدة
- `joinTrade()` - الانضمام لصفقة
- `confirmPaymentSent()` - تأكيد إرسال الدفع
- `confirmPaymentReceived()` - تأكيد استلام الدفع
- `requestDispute()` - طلب نزاع

### 2. ReputationManager.sol
**نظام إدارة السمعة**
- تتبع سمعة المستخدمين
- مستويات سمعة متدرجة
- تقييمات الصفقات
- إحصائيات شاملة

**الوظائف الرئيسية:**
- `updateReputation()` - تحديث السمعة
- `recordSuccessfulTrade()` - تسجيل صفقة ناجحة
- `rateUser()` - تقييم مستخدم
- `getUserReputation()` - جلب سمعة المستخدم

### 3. OracleManager.sol
**نظام إدارة الأسعار**
- تغذية أسعار العملات
- أسعار صرف العملات الورقية
- التحقق من صحة الأسعار
- مراقبة الانحرافات

**الوظائف الرئيسية:**
- `updatePrice()` - تحديث سعر العملة
- `getTokenPrice()` - جلب سعر العملة
- `updateExchangeRate()` - تحديث سعر الصرف
- `convertCurrency()` - تحويل العملات

### 4. AdminManager.sol
**نظام الإدارة**
- إدارة المديرين
- إعدادات النظام
- حل النزاعات
- القائمة السوداء

**الوظائف الرئيسية:**
- `addAdmin()` - إضافة مدير
- `resolveDispute()` - حل نزاع
- `blacklistUser()` - إضافة للقائمة السوداء
- `updateSystemSetting()` - تحديث إعدادات النظام

### 5. EscrowIntegrator.sol
**نظام التكامل**
- تنسيق جميع العقود
- التحقق المتكامل
- إدارة الشبكات
- الإعدادات المتقدمة

**الوظائف الرئيسية:**
- `createIntegratedTrade()` - إنشاء صفقة متكاملة
- `joinIntegratedTrade()` - الانضمام لصفقة متكاملة
- `completeIntegratedTrade()` - إكمال صفقة متكاملة
- `handleIntegratedDispute()` - إدارة النزاعات المتكاملة

## العناوين المنشورة

### BSC Testnet
- **CoreEscrow**: `0xAb25F1fFDB7fC4EA75abDcc662CeD61E4b20F7f2`
- **ReputationManager**: `0x56A6914523413b0e7344f57466A6239fCC97b913`
- **OracleManager**: `0xB70715392F62628Ccd1258AAF691384bE8C023b6`
- **AdminManager**: `0x5A9FD8082ADA38678721D59AAB4d4F76883c5575`
- **EscrowIntegrator**: `0xc0Ec65ffA95aca485e4C2e9b35B20C85FA36F0e0`

## المتطلبات

### التبعيات
```bash
npm install @openzeppelin/contracts
```

### إصدار Solidity
```
^0.8.19
```

### الشبكات المدعومة
- BSC Testnet (Chain ID: 97)
- BSC Mainnet (Chain ID: 56)

## التثبيت والنشر

### 1. إعداد البيئة
```bash
npm install -g hardhat
npm install
```

### 2. تكوين الشبكة
```javascript
// hardhat.config.js
networks: {
  bscTestnet: {
    url: "https://data-seed-prebsc-1-s1.binance.org:8545/",
    chainId: 97,
    accounts: [process.env.PRIVATE_KEY]
  }
}
```

### 3. النشر
```bash
npx hardhat run scripts/deploy-enhanced.js --network bscTestnet
```

## الاستخدام

### مثال على إنشاء صفقة
```javascript
const coreEscrow = await ethers.getContractAt("CoreEscrow", contractAddress);

const tx = await coreEscrow.createTrade(
  tokenAddress,
  ethers.parseUnits("100", 18), // 100 tokens
  ethers.parseUnits("3.75", 18), // 3.75 SAR per token
  "SAR"
);
```

### مثال على تحديث السمعة
```javascript
const reputationManager = await ethers.getContractAt("ReputationManager", contractAddress);

await reputationManager.recordSuccessfulTrade(userAddress, networkId);
```

## الأمان

### ميزات الأمان
- **ReentrancyGuard**: حماية من هجمات إعادة الدخول
- **Pausable**: إمكانية إيقاف العقد في حالات الطوارئ
- **AccessControl**: نظام أذونات متدرج
- **SafeERC20**: تحويلات آمنة للعملات

### التدقيق
- تم مراجعة جميع العقود للثغرات الأمنية الشائعة
- استخدام مكتبات OpenZeppelin المختبرة
- اختبارات شاملة للوظائف الحرجة

## الاختبار

### تشغيل الاختبارات
```bash
npx hardhat test
```

### اختبارات التكامل
```bash
npx hardhat test --network bscTestnet
```

## المراقبة

### الأحداث المهمة
- `TradeCreated` - إنشاء صفقة جديدة
- `TradeCompleted` - إكمال صفقة
- `ReputationUpdated` - تحديث السمعة
- `PriceUpdated` - تحديث الأسعار

### المقاييس
- إجمالي الصفقات
- معدل نجاح الصفقات
- متوسط السمعة
- حجم التداول

## الصيانة

### التحديثات
- تحديثات أمنية دورية
- تحسينات الأداء
- إضافة ميزات جديدة

### النسخ الاحتياطي
- نسخ احتياطية دورية لحالة العقود
- مراقبة الأحداث المهمة
- تسجيل جميع المعاملات

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- مراجعة التوثيق
- فحص الاختبارات

## الترخيص

MIT License - راجع ملف LICENSE للتفاصيل.

---

**تم تطوير هذه العقود بعناية فائقة لضمان الأمان والأداء العالي في تداول العملات المستقرة اللامركزي.**
