// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "./CoreEscrow.sol";
import "./ReputationManager.sol";
import "./OracleManager.sol";
import "./AdminManager.sol";

/**
 * @title EscrowIntegrator - Enhanced Contract Integration System
 * @dev Integrates all enhanced contracts into a unified system
 * <AUTHOR> P2P Team
 * @notice This contract coordinates all enhanced contract interactions
 */
contract EscrowIntegrator is AccessControl, ReentrancyGuard, Pausable {
    
    // Role definitions
    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant INTEGRATOR_ROLE = keccak256("INTEGRATOR_ROLE");

    // Contract references
    CoreEscrow public immutable coreEscrow;
    ReputationManager public immutable reputationManager;
    OracleManager public immutable oracleManager;
    AdminManager public immutable adminManager;

    // Integration settings
    struct IntegrationSettings {
        bool autoReputationUpdate;
        bool priceValidationEnabled;
        bool adminValidationRequired;
        uint256 minReputationForTrade;
        uint256 maxPriceDeviation;
        bool emergencyMode;
    }

    // Trade integration data
    struct TradeIntegration {
        uint256 tradeId;
        uint256 networkId;
        bool reputationUpdated;
        bool priceValidated;
        uint256 createdAt;
        uint256 lastUpdate;
    }

    // State variables
    IntegrationSettings public settings;
    mapping(uint256 => TradeIntegration) public tradeIntegrations;
    mapping(address => uint256) public userNetworks; // user => preferred network
    
    uint256 public totalIntegratedTrades;
    uint256 public successfulIntegrations;
    uint256 public failedIntegrations;

    // Events
    event TradeIntegrated(
        uint256 indexed tradeId,
        address indexed seller,
        address indexed buyer,
        uint256 networkId,
        bool success
    );

    event ReputationIntegrated(
        uint256 indexed tradeId,
        address indexed user,
        uint256 networkId,
        int256 change
    );

    event PriceValidated(
        uint256 indexed tradeId,
        address indexed token,
        string currency,
        uint256 price,
        bool valid
    );

    event IntegrationSettingsUpdated(
        string setting,
        bool oldValue,
        bool newValue,
        address updatedBy
    );

    event EmergencyModeToggled(
        bool enabled,
        address toggledBy,
        uint256 timestamp
    );

    // Modifiers
    modifier onlyIntegratedContracts() {
        require(
            msg.sender == address(coreEscrow) ||
            msg.sender == address(reputationManager) ||
            msg.sender == address(oracleManager) ||
            msg.sender == address(adminManager),
            "Not an integrated contract"
        );
        _;
    }

    modifier notInEmergency() {
        require(!settings.emergencyMode, "Integration in emergency mode");
        _;
    }

    modifier validNetwork(uint256 networkId) {
        require(networkId > 0, "Invalid network ID");
        _;
    }

    /**
     * @dev Constructor
     * @param _coreEscrow CoreEscrow contract address
     * @param _reputationManager ReputationManager contract address
     * @param _oracleManager OracleManager contract address
     * @param _adminManager AdminManager contract address
     */
    constructor(
        address _coreEscrow,
        address _reputationManager,
        address _oracleManager,
        address _adminManager
    ) {
        require(_coreEscrow != address(0), "Invalid CoreEscrow address");
        require(_reputationManager != address(0), "Invalid ReputationManager address");
        require(_oracleManager != address(0), "Invalid OracleManager address");
        require(_adminManager != address(0), "Invalid AdminManager address");

        coreEscrow = CoreEscrow(_coreEscrow);
        reputationManager = ReputationManager(_reputationManager);
        oracleManager = OracleManager(_oracleManager);
        adminManager = AdminManager(_adminManager);

        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _grantRole(ADMIN_ROLE, msg.sender);
        _grantRole(INTEGRATOR_ROLE, msg.sender);

        // Initialize integration settings
        settings = IntegrationSettings({
            autoReputationUpdate: true,
            priceValidationEnabled: true,
            adminValidationRequired: false,
            minReputationForTrade: 50,
            maxPriceDeviation: 500, // 5%
            emergencyMode: false
        });
    }

    /**
     * @dev Create an integrated trade with all validations
     * @param token Token contract address
     * @param amount Amount of tokens to trade
     * @param pricePerToken Price per token in fiat currency
     * @param currency Fiat currency code
     * @param networkId Network identifier
     * @return tradeId The ID of the created trade
     */
    function createIntegratedTrade(
        address token,
        uint256 amount,
        uint256 pricePerToken,
        string memory currency,
        uint256 networkId
    ) external 
        nonReentrant 
        whenNotPaused 
        notInEmergency 
        validNetwork(networkId)
        returns (uint256) 
    {
        // Validate user reputation if required
        if (settings.minReputationForTrade > 0) {
            ReputationManager.ReputationData memory reputation = 
                reputationManager.getUserReputation(msg.sender, networkId);
            require(
                reputation.score >= settings.minReputationForTrade,
                "Insufficient reputation score"
            );
        }

        // Validate price if enabled
        if (settings.priceValidationEnabled) {
            _validatePrice(token, currency, pricePerToken);
        }

        // Check admin validation if required
        if (settings.adminValidationRequired) {
            require(adminManager.isAdmin(msg.sender), "Admin validation required");
        }

        // Initialize user reputation if needed
        reputationManager.initializeUserReputation(msg.sender, networkId);

        // Create trade through CoreEscrow
        uint256 tradeId = coreEscrow.createTrade(token, amount, pricePerToken, currency);

        // Create integration record
        tradeIntegrations[tradeId] = TradeIntegration({
            tradeId: tradeId,
            networkId: networkId,
            reputationUpdated: false,
            priceValidated: settings.priceValidationEnabled,
            createdAt: block.timestamp,
            lastUpdate: block.timestamp
        });

        totalIntegratedTrades++;
        successfulIntegrations++;

        emit TradeIntegrated(tradeId, msg.sender, address(0), networkId, true);

        return tradeId;
    }

    /**
     * @dev Join an integrated trade with validations
     * @param tradeId ID of the trade to join
     */
    function joinIntegratedTrade(uint256 tradeId) 
        external 
        nonReentrant 
        whenNotPaused 
        notInEmergency 
    {
        TradeIntegration storage integration = tradeIntegrations[tradeId];
        require(integration.tradeId != 0, "Trade not integrated");

        // Validate user reputation if required
        if (settings.minReputationForTrade > 0) {
            ReputationManager.ReputationData memory reputation = 
                reputationManager.getUserReputation(msg.sender, integration.networkId);
            require(
                reputation.score >= settings.minReputationForTrade,
                "Insufficient reputation score"
            );
        }

        // Initialize user reputation if needed
        reputationManager.initializeUserReputation(msg.sender, integration.networkId);

        // Join trade through CoreEscrow
        coreEscrow.joinTrade(tradeId);

        // Update integration record
        integration.lastUpdate = block.timestamp;

        CoreEscrow.Trade memory trade = coreEscrow.getTrade(tradeId);
        emit TradeIntegrated(tradeId, trade.seller, msg.sender, integration.networkId, true);
    }

    /**
     * @dev Complete an integrated trade with reputation updates
     * @param tradeId ID of the trade to complete
     */
    function completeIntegratedTrade(uint256 tradeId) 
        external 
        nonReentrant 
        whenNotPaused 
    {
        TradeIntegration storage integration = tradeIntegrations[tradeId];
        require(integration.tradeId != 0, "Trade not integrated");

        CoreEscrow.Trade memory trade = coreEscrow.getTrade(tradeId);
        require(
            msg.sender == trade.seller || msg.sender == trade.buyer,
            "Not a trade participant"
        );

        // Complete trade through CoreEscrow
        if (msg.sender == trade.buyer) {
            coreEscrow.confirmPaymentSent(tradeId);
        } else {
            coreEscrow.confirmPaymentReceived(tradeId);
        }

        // Update reputation if auto-update is enabled and trade is completed
        if (settings.autoReputationUpdate && trade.status == CoreEscrow.TradeStatus.Completed) {
            _updateTradeReputation(tradeId, integration.networkId);
        }

        integration.lastUpdate = block.timestamp;
    }

    /**
     * @dev Handle dispute with integrated resolution
     * @param tradeId ID of the trade in dispute
     * @param reason Reason for the dispute
     */
    function handleIntegratedDispute(uint256 tradeId, string memory reason) 
        external 
        nonReentrant 
        whenNotPaused 
    {
        TradeIntegration storage integration = tradeIntegrations[tradeId];
        require(integration.tradeId != 0, "Trade not integrated");

        CoreEscrow.Trade memory trade = coreEscrow.getTrade(tradeId);
        require(
            msg.sender == trade.seller || msg.sender == trade.buyer,
            "Not a trade participant"
        );

        // Request dispute through CoreEscrow
        coreEscrow.requestDispute(tradeId, reason);

        // Update reputation for disputed trade
        if (settings.autoReputationUpdate) {
            reputationManager.recordDisputedTrade(msg.sender, integration.networkId);
        }

        integration.lastUpdate = block.timestamp;
    }

    /**
     * @dev Validate price against oracle data
     * @param token Token address
     * @param currency Currency code
     * @param pricePerToken Proposed price per token
     */
    function _validatePrice(
        address token,
        string memory currency,
        uint256 pricePerToken
    ) internal view {
        try oracleManager.getTokenPrice(token, currency) returns (
            uint256 oraclePrice,
            uint256 timestamp,
            uint256 confidence
        ) {
            // Check if price is not stale
            require(!oracleManager.isPriceStale(token, currency), "Oracle price is stale");
            
            // Check confidence level
            require(confidence >= 8000, "Oracle confidence too low"); // 80%
            
            // Calculate deviation
            uint256 deviation = _calculateDeviation(oraclePrice, pricePerToken);
            require(deviation <= settings.maxPriceDeviation, "Price deviation too high");
            
        } catch {
            // If oracle price is not available, allow trade but emit warning
            // In production, you might want to handle this differently
        }
    }

    /**
     * @dev Update reputation for completed trade
     * @param tradeId Trade ID
     * @param networkId Network ID
     */
    function _updateTradeReputation(uint256 tradeId, uint256 networkId) internal {
        CoreEscrow.Trade memory trade = coreEscrow.getTrade(tradeId);
        
        if (trade.status == CoreEscrow.TradeStatus.Completed) {
            // Update reputation for both parties
            reputationManager.recordSuccessfulTrade(trade.seller, networkId);
            reputationManager.recordSuccessfulTrade(trade.buyer, networkId);
            
            tradeIntegrations[tradeId].reputationUpdated = true;
            
            emit ReputationIntegrated(tradeId, trade.seller, networkId, 5);
            emit ReputationIntegrated(tradeId, trade.buyer, networkId, 5);
        }
    }

    /**
     * @dev Calculate price deviation percentage
     * @param oraclePrice Oracle price
     * @param proposedPrice Proposed price
     * @return deviation Deviation in basis points
     */
    function _calculateDeviation(uint256 oraclePrice, uint256 proposedPrice) 
        internal 
        pure 
        returns (uint256) 
    {
        if (oraclePrice == 0) return 0;
        
        uint256 diff = proposedPrice > oraclePrice ? 
            proposedPrice - oraclePrice : 
            oraclePrice - proposedPrice;
            
        return (diff * 10000) / oraclePrice;
    }

    /**
     * @dev Set user's preferred network
     * @param networkId Network ID
     */
    function setUserNetwork(uint256 networkId) external validNetwork(networkId) {
        userNetworks[msg.sender] = networkId;
    }

    /**
     * @dev Get integration statistics
     * @return total Total integrated trades
     * @return successful Successful integrations
     * @return failed Failed integrations
     */
    function getIntegrationStats() 
        external 
        view 
        returns (uint256 total, uint256 successful, uint256 failed) 
    {
        return (totalIntegratedTrades, successfulIntegrations, failedIntegrations);
    }

    /**
     * @dev Get trade integration data
     * @param tradeId Trade ID
     * @return TradeIntegration struct
     */
    function getTradeIntegration(uint256 tradeId) 
        external 
        view 
        returns (TradeIntegration memory) 
    {
        return tradeIntegrations[tradeId];
    }

    /**
     * @dev Update integration settings
     * @param setting Setting name
     * @param value New value
     */
    function updateIntegrationSetting(string memory setting, bool value) 
        external 
        onlyRole(ADMIN_ROLE) 
    {
        bool oldValue;
        
        if (keccak256(bytes(setting)) == keccak256(bytes("autoReputationUpdate"))) {
            oldValue = settings.autoReputationUpdate;
            settings.autoReputationUpdate = value;
        } else if (keccak256(bytes(setting)) == keccak256(bytes("priceValidationEnabled"))) {
            oldValue = settings.priceValidationEnabled;
            settings.priceValidationEnabled = value;
        } else if (keccak256(bytes(setting)) == keccak256(bytes("adminValidationRequired"))) {
            oldValue = settings.adminValidationRequired;
            settings.adminValidationRequired = value;
        } else {
            revert("Invalid setting");
        }

        emit IntegrationSettingsUpdated(setting, oldValue, value, msg.sender);
    }

    /**
     * @dev Update numeric integration settings
     * @param setting Setting name
     * @param value New value
     */
    function updateNumericSetting(string memory setting, uint256 value) 
        external 
        onlyRole(ADMIN_ROLE) 
    {
        if (keccak256(bytes(setting)) == keccak256(bytes("minReputationForTrade"))) {
            require(value <= 1000, "Invalid reputation requirement");
            settings.minReputationForTrade = value;
        } else if (keccak256(bytes(setting)) == keccak256(bytes("maxPriceDeviation"))) {
            require(value <= 5000, "Invalid price deviation"); // Max 50%
            settings.maxPriceDeviation = value;
        } else {
            revert("Invalid setting");
        }
    }

    /**
     * @dev Toggle emergency mode
     * @param enabled Whether to enable emergency mode
     */
    function toggleEmergencyMode(bool enabled) external onlyRole(ADMIN_ROLE) {
        settings.emergencyMode = enabled;
        emit EmergencyModeToggled(enabled, msg.sender, block.timestamp);
    }

    /**
     * @dev Get integration settings
     * @return IntegrationSettings struct
     */
    function getIntegrationSettings() external view returns (IntegrationSettings memory) {
        return settings;
    }

    // Admin functions
    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
    }

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
    }
}
