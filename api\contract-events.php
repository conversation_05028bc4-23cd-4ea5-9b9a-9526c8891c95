<?php
/**
 * API لإدارة أحداث العقد الذكي
 * Smart Contract Events Management API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../config/development.php';
require_once __DIR__ . '/../config/database.php';

try {
    // Get database connection
    $db = DatabaseManager::getInstance()->getDatabase();
    $pdo = $db->getConnection();

    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';

    switch ($method) {
        case 'GET':
            handleGetRequest($action, $pdo);
            break;

        case 'POST':
            handlePostRequest($pdo);
            break;

        case 'PUT':
            handlePutRequest($pdo);
            break;

        case 'DELETE':
            handleDeleteRequest($pdo);
            break;

        default:
            throw new Exception('طريقة الطلب غير مدعومة');
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => 'SERVER_ERROR'
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * معالجة طلبات GET
 */
function handleGetRequest($action, $pdo) {
    
    switch ($action) {
        case 'list':
            getEventsList($pdo);
            break;

        case 'check':
            checkEventExists($pdo);
            break;

        case 'last':
            getLastEvent($pdo);
            break;

        case 'stats':
            getEventsStats($pdo);
            break;

        default:
            getEventsList($pdo);
            break;
    }
}

/**
 * جلب قائمة الأحداث
 */
function getEventsList($pdo) {

    try {
        $page = (int)($_GET['page'] ?? 1);
        $limit = min((int)($_GET['limit'] ?? 20), 100);
        $offset = ($page - 1) * $limit;

        $eventType = $_GET['event_type'] ?? '';
        $tradeId = $_GET['trade_id'] ?? '';
        $fromDate = $_GET['from_date'] ?? '';
        $toDate = $_GET['to_date'] ?? '';
        $processed = $_GET['processed'] ?? null;

        // بناء الاستعلام
        $whereConditions = [];
        $params = [];

        if (!empty($eventType)) {
            $whereConditions[] = "event_type = ?";
            $params[] = $eventType;
        }

        if (!empty($tradeId)) {
            $whereConditions[] = "blockchain_trade_id = ?";
            $params[] = $tradeId;
        }

        if (!empty($fromDate)) {
            $whereConditions[] = "event_timestamp >= ?";
            $params[] = $fromDate;
        }

        if (!empty($toDate)) {
            $whereConditions[] = "event_timestamp <= ?";
            $params[] = $toDate;
        }

        // إضافة فلتر processed
        if ($processed !== null) {
            $whereConditions[] = "processed = ?";
            $params[] = $processed === 'true' ? 1 : 0;
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // جلب الأحداث
        $sql = "SELECT * FROM contract_events
                $whereClause
                ORDER BY event_timestamp DESC
                LIMIT ? OFFSET ?";

        $params[] = $limit;
        $params[] = $offset;

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $events = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // جلب العدد الإجمالي
        $countSql = "SELECT COUNT(*) FROM contract_events $whereClause";
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute(array_slice($params, 0, -2)); // إزالة limit و offset
        $totalCount = $countStmt->fetchColumn();

        echo json_encode([
            'success' => true,
            'data' => $events,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => (int)$totalCount,
                'pages' => ceil($totalCount / $limit)
            ]
        ], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        throw new Exception('خطأ في جلب الأحداث: ' . $e->getMessage());
    }
}

/**
 * التحقق من وجود حدث
 */
function checkEventExists($pdo) {
    
    try {
        $transactionHash = $_GET['tx'] ?? '';
        $eventType = $_GET['type'] ?? '';
        
        if (empty($transactionHash) || empty($eventType)) {
            throw new Exception('معاملات مطلوبة مفقودة');
        }
        
        $sql = "SELECT COUNT(*) FROM contract_events 
                WHERE transaction_hash = ? AND event_type = ?";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$transactionHash, $eventType]);
        $exists = $stmt->fetchColumn() > 0;
        
        echo json_encode([
            'success' => true,
            'exists' => $exists
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        throw new Exception('خطأ في التحقق من الحدث: ' . $e->getMessage());
    }
}

/**
 * جلب آخر حدث
 */
function getLastEvent($pdo) {
    
    try {
        $sql = "SELECT * FROM contract_events 
                ORDER BY block_number DESC, event_timestamp DESC 
                LIMIT 1";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $event = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => $event
        ]);
        
    } catch (Exception $e) {
        throw new Exception('خطأ في جلب آخر حدث: ' . $e->getMessage());
    }
}

/**
 * جلب إحصائيات الأحداث
 */
function getEventsStats($pdo) {
    
    try {
        // إحصائيات عامة
        $sql = "SELECT 
                    COUNT(*) as total_events,
                    COUNT(DISTINCT blockchain_trade_id) as unique_trades,
                    COUNT(DISTINCT event_type) as event_types,
                    MIN(event_timestamp) as first_event,
                    MAX(event_timestamp) as last_event
                FROM contract_events";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $generalStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // إحصائيات حسب نوع الحدث
        $sql = "SELECT 
                    event_type,
                    COUNT(*) as count,
                    MAX(event_timestamp) as last_occurrence
                FROM contract_events 
                GROUP BY event_type 
                ORDER BY count DESC";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $eventTypeStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // إحصائيات يومية (آخر 30 يوم)
        $sql = "SELECT 
                    DATE(event_timestamp) as date,
                    COUNT(*) as events_count
                FROM contract_events 
                WHERE event_timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY DATE(event_timestamp)
                ORDER BY date DESC";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $dailyStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => [
                'general' => $generalStats,
                'by_event_type' => $eventTypeStats,
                'daily_stats' => $dailyStats
            ]
        ]);
        
    } catch (Exception $e) {
        throw new Exception('خطأ في جلب إحصائيات الأحداث: ' . $e->getMessage());
    }
}

/**
 * معالجة طلبات POST
 */
function handlePostRequest($pdo) {
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صالحة');
        }
        
        // التحقق من البيانات المطلوبة
        $requiredFields = [
            'event_type', 'blockchain_trade_id', 'transaction_hash', 
            'block_number', 'block_hash', 'event_timestamp'
        ];
        
        foreach ($requiredFields as $field) {
            if (!isset($input[$field])) {
                throw new Exception("الحقل المطلوب مفقود: $field");
            }
        }
        
        // التحقق من عدم وجود الحدث مسبقاً
        $checkSql = "SELECT COUNT(*) FROM contract_events 
                     WHERE transaction_hash = ? AND event_type = ?";
        $checkStmt = $pdo->prepare($checkSql);
        $checkStmt->execute([$input['transaction_hash'], $input['event_type']]);
        
        if ($checkStmt->fetchColumn() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'الحدث موجود مسبقاً',
                'duplicate' => true
            ]);
            return;
        }
        
        // إدراج الحدث الجديد
        $sql = "INSERT INTO contract_events (
                    event_type, blockchain_trade_id, transaction_hash, 
                    block_number, block_hash, seller_address, buyer_address,
                    amount, fee_amount, event_data, event_timestamp, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $input['event_type'],
            $input['blockchain_trade_id'],
            $input['transaction_hash'],
            $input['block_number'],
            $input['block_hash'],
            $input['seller_address'] ?? null,
            $input['buyer_address'] ?? null,
            $input['amount'] ?? null,
            $input['fee_amount'] ?? null,
            isset($input['event_data']) ? json_encode($input['event_data']) : null,
            $input['event_timestamp']
        ]);
        
        $eventId = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ الحدث بنجاح',
            'event_id' => $eventId
        ]);
        
    } catch (Exception $e) {
        throw new Exception('خطأ في حفظ الحدث: ' . $e->getMessage());
    }
}

/**
 * معالجة طلبات PUT
 */
function handlePutRequest($pdo) {
    // يمكن إضافة تحديث الأحداث هنا إذا لزم الأمر
    echo json_encode([
        'success' => false,
        'message' => 'تحديث الأحداث غير مدعوم'
    ]);
}

/**
 * معالجة طلبات DELETE
 */
function handleDeleteRequest($pdo) {
    // يمكن إضافة حذف الأحداث هنا إذا لزم الأمر (للمدراء فقط)
    echo json_encode([
        'success' => false,
        'message' => 'حذف الأحداث غير مدعوم'
    ]);
}
?>
