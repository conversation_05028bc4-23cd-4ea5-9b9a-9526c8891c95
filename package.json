{"name": "ikaros-p2p", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:fast": "NEXT_TELEMETRY_DISABLED=1 next dev --port 3000 --turbo", "dev:clean": "npm run clean && npm run dev:fast", "dev:debug": "NODE_OPTIONS='--inspect' next dev", "build": "next build", "build:fast": "NEXT_TELEMETRY_DISABLED=1 next build", "build:prod": "NODE_ENV=production next build", "start": "next start", "start:prod": "NODE_ENV=production next start", "lint": "next lint", "clean": "rimraf .next && rimraf node_modules/.cache && rimraf .swc", "clean:cache": "rimraf .next node_modules/.cache .swc tsconfig.tsbuildinfo", "clean:all": "rimraf .next && rimraf node_modules/.cache && rimraf .swc && rimraf tsconfig.tsbuildinfo", "reset": "npm run clean:all && npm install", "deploy:build": "npm run env:prod && npm run build:prod", "test:ui": "jest src/tests/admin/ui", "test:integration": "jest src/tests/admin/Integration.test.tsx", "type-check": "tsc --noEmit", "validate": "npm run type-check && npm run lint && npm run test:ci"}, "dependencies": {"critters": "^0.0.25", "dotenv": "^16.5.0", "ethers": "^6.14.4", "framer-motion": "^12.18.1", "i18next": "^25.2.1", "lucide-react": "^0.514.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8.5.5", "rimraf": "^6.0.1", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwindcss": "^3.4.0", "typescript": "^5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}