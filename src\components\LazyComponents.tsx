/**
 * مكونات التحميل التدريجي (Lazy Loading Components)
 * Lazy Loading Components for Performance Optimization
 */

import { lazy, Suspense, ComponentType } from 'react';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorBoundary from '@/components/ErrorBoundary';

// مكون Loading مخصص للصفحات
const PageLoader = ({ message = 'جاري التحميل...' }: { message?: string }) => (
  <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
    <div className="text-center">
      <LoadingSpinner size="lg" />
      <p className="mt-4 text-gray-600 dark:text-gray-400">{message}</p>
    </div>
  </div>
);

// مكون Loading مخصص للمكونات الصغيرة
const ComponentLoader = ({ message = 'جاري التحميل...' }: { message?: string }) => (
  <div className="flex items-center justify-center p-8">
    <div className="text-center">
      <LoadingSpinner />
      <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">{message}</p>
    </div>
  </div>
);

// دالة مساعدة لإنشاء مكونات lazy مع معالجة الأخطاء
const createLazyComponent = <T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: React.ReactNode,
  errorFallback?: React.ReactNode
) => {
  const LazyComponent = lazy(importFunc);
  
  return (props: React.ComponentProps<T>) => (
    <ErrorBoundary fallback={errorFallback}>
      <Suspense fallback={fallback || <ComponentLoader />}>
        <LazyComponent {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

// الصفحات الرئيسية
export const LazyUserDashboard = createLazyComponent(
  () => import('@/components/UserDashboard'),
  <PageLoader message="جاري تحميل لوحة التحكم..." />
);

export const LazyAdminDashboard = createLazyComponent(
  () => import('@/components/AdminDashboard'),
  <PageLoader message="جاري تحميل لوحة الإدارة..." />
);

export const LazyTradePage = createLazyComponent(
  () => import('@/components/TradePage'),
  <PageLoader message="جاري تحميل صفحة التداول..." />
);

export const LazyOffersPage = createLazyComponent(
  () => import('@/components/OffersPage'),
  <PageLoader message="جاري تحميل العروض..." />
);

// WalletPage تم نقلها إلى user-dashboard

export const LazyProfilePage = createLazyComponent(
  () => import('@/components/ProfilePage'),
  <PageLoader message="جاري تحميل الملف الشخصي..." />
);

// المكونات المتقدمة
export const LazyTradeForm = createLazyComponent(
  () => import('@/components/TradeForm'),
  <ComponentLoader message="جاري تحميل نموذج التداول..." />
);

export const LazyOfferForm = createLazyComponent(
  () => import('@/components/OfferForm'),
  <ComponentLoader message="جاري تحميل نموذج العرض..." />
);

export const LazyWalletConnector = createLazyComponent(
  () => import('@/components/WalletConnector'),
  <ComponentLoader message="جاري تحميل موصل المحفظة..." />
);

export const LazyContractInteraction = createLazyComponent(
  () => import('@/components/ContractInteraction'),
  <ComponentLoader message="جاري تحميل واجهة العقد الذكي..." />
);

// مكونات التحليلات والإحصائيات
export const LazyAnalyticsDashboard = createLazyComponent(
  () => import('@/components/AnalyticsDashboard'),
  <ComponentLoader message="جاري تحميل التحليلات..." />
);

export const LazyChartComponent = createLazyComponent(
  () => import('@/components/ChartComponent'),
  <ComponentLoader message="جاري تحميل الرسوم البيانية..." />
);

// مكونات الإعدادات
export const LazySettingsPage = createLazyComponent(
  () => import('@/components/SettingsPage'),
  <ComponentLoader message="جاري تحميل الإعدادات..." />
);

export const LazySecuritySettings = createLazyComponent(
  () => import('@/components/SecuritySettings'),
  <ComponentLoader message="جاري تحميل إعدادات الأمان..." />
);

export const LazyNotificationSettings = createLazyComponent(
  () => import('@/components/NotificationSettings'),
  <ComponentLoader message="جاري تحميل إعدادات الإشعارات..." />
);

// مكونات المراسلة والدعم
export const LazyMessagingComponent = createLazyComponent(
  () => import('@/components/MessagingComponent'),
  <ComponentLoader message="جاري تحميل المراسلة..." />
);

export const LazySupportChat = createLazyComponent(
  () => import('@/components/SupportChat'),
  <ComponentLoader message="جاري تحميل الدعم الفني..." />
);

// مكونات التقارير
export const LazyReportsPage = createLazyComponent(
  () => import('@/components/ReportsPage'),
  <ComponentLoader message="جاري تحميل التقارير..." />
);

export const LazyTransactionHistory = createLazyComponent(
  () => import('@/components/TransactionHistory'),
  <ComponentLoader message="جاري تحميل سجل المعاملات..." />
);

// مكونات الإدارة المتقدمة
export const LazyUserManagement = createLazyComponent(
  () => import('@/components/UserManagement'),
  <ComponentLoader message="جاري تحميل إدارة المستخدمين..." />
);

export const LazyTradeManagement = createLazyComponent(
  () => import('@/components/TradeManagement'),
  <ComponentLoader message="جاري تحميل إدارة الصفقات..." />
);

export const LazyDisputeResolution = createLazyComponent(
  () => import('@/components/DisputeResolution'),
  <ComponentLoader message="جاري تحميل حل النزاعات..." />
);

// مكونات التحقق والأمان
export const LazyKYCVerification = createLazyComponent(
  () => import('@/components/KYCVerification'),
  <ComponentLoader message="جاري تحميل التحقق من الهوية..." />
);

export const LazyTwoFactorAuth = createLazyComponent(
  () => import('@/components/TwoFactorAuth'),
  <ComponentLoader message="جاري تحميل المصادقة الثنائية..." />
);

// مكونات التعليم والمساعدة
export const LazyTutorialComponent = createLazyComponent(
  () => import('@/components/TutorialComponent'),
  <ComponentLoader message="جاري تحميل الدليل التعليمي..." />
);

export const LazyHelpCenter = createLazyComponent(
  () => import('@/components/HelpCenter'),
  <ComponentLoader message="جاري تحميل مركز المساعدة..." />
);

// Hook مخصص لإدارة التحميل التدريجي
export const useLazyLoading = () => {
  const preloadComponent = async (componentName: string) => {
    try {
      switch (componentName) {
        case 'UserDashboard':
          await import('@/components/UserDashboard');
          break;
        case 'AdminDashboard':
          await import('@/components/AdminDashboard');
          break;
        case 'TradePage':
          await import('@/components/TradePage');
          break;
        case 'OffersPage':
          await import('@/components/OffersPage');
          break;
        // WalletPage moved to user-dashboard
        default:
          console.warn(`Unknown component for preloading: ${componentName}`);
      }
    } catch (error) {
      console.error(`Failed to preload component ${componentName}:`, error);
    }
  };

  const preloadCriticalComponents = async () => {
    // تحميل المكونات الحرجة مسبقاً
    const criticalComponents = ['UserDashboard', 'TradePage', 'WalletPage'];
    
    await Promise.allSettled(
      criticalComponents.map(component => preloadComponent(component))
    );
  };

  return {
    preloadComponent,
    preloadCriticalComponents
  };
};

// مكون Wrapper للتحميل التدريجي مع إدارة الحالة
export const LazyWrapper = ({ 
  children, 
  fallback, 
  errorFallback,
  onLoad,
  onError 
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  errorFallback?: React.ReactNode;
  onLoad?: () => void;
  onError?: (error: Error) => void;
}) => {
  return (
    <ErrorBoundary 
      fallback={errorFallback}
      onError={onError}
    >
      <Suspense 
        fallback={fallback || <ComponentLoader />}
      >
        {children}
      </Suspense>
    </ErrorBoundary>
  );
};

export default {
  LazyUserDashboard,
  LazyAdminDashboard,
  LazyTradePage,
  LazyOffersPage,
  LazyWalletPage,
  LazyProfilePage,
  LazyTradeForm,
  LazyOfferForm,
  LazyWalletConnector,
  LazyContractInteraction,
  LazyAnalyticsDashboard,
  LazyChartComponent,
  LazySettingsPage,
  LazySecuritySettings,
  LazyNotificationSettings,
  LazyMessagingComponent,
  LazySupportChat,
  LazyReportsPage,
  LazyTransactionHistory,
  LazyUserManagement,
  LazyTradeManagement,
  LazyDisputeResolution,
  LazyKYCVerification,
  LazyTwoFactorAuth,
  LazyTutorialComponent,
  LazyHelpCenter,
  LazyWrapper,
  useLazyLoading
};
