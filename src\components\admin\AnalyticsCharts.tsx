'use client';

import React from 'react';
import {
  BarChart3,
  TrendingUp,
  DollarSign,
  Users,
  Activity,
  PieChart,
  LineChart,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Maximize2,
  Info
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';

interface AnalyticsChartsProps {
  className?: string;
}

export default function AnalyticsCharts({ className = '' }: AnalyticsChartsProps) {
  const { t, language } = useAdminTranslation();
  const isRTL = language === 'ar';

  const chartData = {
    tradingVolume: {
      title: t('analytics.charts.tradingVolume'),
      value: '$2.5M',
      change: '+12.5%',
      trend: 'up',
      color: 'blue',
      icon: TrendingUp
    },
    userGrowth: {
      title: t('analytics.charts.userGrowth'),
      value: '1,234',
      change: '+8.3%',
      trend: 'up',
      color: 'green',
      icon: Users
    },
    revenueAnalysis: {
      title: t('analytics.charts.revenueAnalysis'),
      value: '$125K',
      change: '+15.7%',
      trend: 'up',
      color: 'purple',
      icon: DollarSign
    },
    networkActivity: {
      title: t('analytics.charts.networkActivity'),
      value: '98.5%',
      change: '-0.2%',
      trend: 'down',
      color: 'orange',
      icon: Activity
    }
  };

  const ChartCard = ({ 
    title, 
    children, 
    actions = true,
    fullHeight = false 
  }: { 
    title: string; 
    children: React.ReactNode; 
    actions?: boolean;
    fullHeight?: boolean;
  }) => (
    <div className={`bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 ${fullHeight ? 'h-full' : ''}`}>
      <div className={`flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{title}</h4>
        {actions && (
          <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
              <RefreshCw className="w-4 h-4" />
            </button>
            <button className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
              <Download className="w-4 h-4" />
            </button>
            <button className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
              <Maximize2 className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>
      <div className="p-6">
        {children}
      </div>
    </div>
  );

  const MetricCard = ({ data, size = 'normal' }: { data: any; size?: 'normal' | 'large' }) => (
    <div className={`bg-gradient-to-br from-${data.color}-50 to-${data.color}-100 dark:from-${data.color}-900/20 dark:to-${data.color}-800/20 rounded-xl p-${size === 'large' ? '8' : '6'} border border-${data.color}-200 dark:border-${data.color}-800`}>
      <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`w-${size === 'large' ? '16' : '12'} h-${size === 'large' ? '16' : '12'} bg-${data.color}-500 rounded-lg flex items-center justify-center`}>
          <data.icon className={`w-${size === 'large' ? '8' : '6'} h-${size === 'large' ? '8' : '6'} text-white`} />
        </div>
        <span className={`text-xs font-medium text-${data.color}-600 dark:text-${data.color}-400 bg-${data.color}-100 dark:bg-${data.color}-900/30 px-2 py-1 rounded-full`}>
          {data.change}
        </span>
      </div>
      <div className={isRTL ? 'text-right' : 'text-left'}>
        <p className={`text-sm font-medium text-${data.color}-600 dark:text-${data.color}-400`}>{data.title}</p>
        <p className={`text-${size === 'large' ? '3xl' : '2xl'} font-bold text-${data.color}-700 dark:text-${data.color}-300`}>{data.value}</p>
        <p className={`text-xs text-${data.color}-600 dark:text-${data.color}-400 mt-1`}>
          {t('analytics.comparison')} {t('analytics.periods.yesterday')}
        </p>
      </div>
    </div>
  );

  const ChartPlaceholder = ({ type, height = 'h-64' }: { type: string; height?: string }) => (
    <div className={`${height} bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center`}>
      <div className={`text-center ${isRTL ? 'text-right' : 'text-left'}`}>
        {type === 'bar' && <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-2" />}
        {type === 'line' && <LineChart className="w-12 h-12 text-gray-400 mx-auto mb-2" />}
        {type === 'pie' && <PieChart className="w-12 h-12 text-gray-400 mx-auto mb-2" />}
        <p className="text-gray-600 dark:text-gray-400 text-sm font-medium">{type.charAt(0).toUpperCase() + type.slice(1)} Chart</p>
        <p className="text-gray-500 dark:text-gray-500 text-xs mt-1">{t('analytics.realTimeData')} - {t('common.status.loading')}</p>
      </div>
    </div>
  );

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Analytics Header */}
      <div className={`flex flex-col sm:flex-row sm:items-center justify-between gap-4 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
        <div className={isRTL ? 'text-right' : 'text-left'}>
          <h3 className={`text-xl font-bold text-gray-900 dark:text-white flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <BarChart3 className={`w-6 h-6 text-blue-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            {t('analytics.title')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('analytics.subtitle')}</p>
        </div>
        <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
            <Calendar className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('analytics.periods.last30Days')}
          </button>
          <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
            <Filter className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('analytics.filters.dateRange')}
          </button>
          <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors">
            {t('common.actions.export')}
          </button>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Object.values(chartData).map((data, index) => (
          <MetricCard key={index} data={data} />
        ))}
      </div>

      {/* Main Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartCard title={t('analytics.charts.tradingVolume')}>
          <ChartPlaceholder type="bar" />
        </ChartCard>
        
        <ChartCard title={t('analytics.charts.userGrowth')}>
          <ChartPlaceholder type="line" />
        </ChartCard>
      </div>

      {/* Revenue Analysis - Full Width */}
      <ChartCard title={t('analytics.charts.revenueAnalysis')}>
        <ChartPlaceholder type="line" height="h-80" />
      </ChartCard>

      {/* Additional Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <ChartCard title={t('analytics.charts.geographicDistribution')}>
          <ChartPlaceholder type="pie" height="h-48" />
        </ChartCard>
        
        <ChartCard title={t('analytics.charts.conversionRates')}>
          <ChartPlaceholder type="bar" height="h-48" />
        </ChartCard>
        
        <ChartCard title={t('analytics.charts.userRetention')}>
          <ChartPlaceholder type="line" height="h-48" />
        </ChartCard>
      </div>

      {/* Insights Panel */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
        <div className={`flex items-start gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
            <Info className="w-6 h-6 text-white" />
          </div>
          <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
            <h4 className="text-lg font-semibold text-blue-900 dark:text-blue-300 mb-2">{t('analytics.insights')}</h4>
            <div className="space-y-2 text-sm text-blue-800 dark:text-blue-400">
              <p>• {t('stats.dailyVolume')} زاد بنسبة 12.5% مقارنة بالأمس</p>
              <p>• {t('stats.activeUsers')} وصل إلى أعلى مستوى هذا الشهر</p>
              <p>• {t('stats.averageTradeTime')} انخفض بنسبة 2.1% مما يشير إلى تحسن الكفاءة</p>
              <p>• {t('analytics.charts.conversionRates')} تحسنت بنسبة 8.3% هذا الأسبوع</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
