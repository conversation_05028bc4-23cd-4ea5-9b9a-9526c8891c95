'use client';

import { Star, Shield, Clock, TrendingUp } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { Offer } from '@/services/testDataService';

interface OfferCardProps {
  offer: Offer;
  onBuy: (offerId: string | number) => void;
  featured?: boolean;
}

export default function OfferCard({ offer, onBuy, featured = false }: OfferCardProps) {
  const { t } = useTranslation();
  return (
    <div className={`group relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border overflow-hidden ${
      featured ? 'border-2 border-blue-200 dark:border-blue-600 bg-gradient-to-br from-blue-50 to-white dark:from-blue-900/20 dark:to-gray-800' : 'border-gray-100 dark:border-gray-700'
    }`}>
      {/* Colored top bar */}
      <div className={`h-1 ${featured ? 'bg-gradient-to-r from-blue-500 to-purple-500' : 'bg-gradient-to-r from-gray-300 to-gray-400'}`}></div>

      {/* Featured badge */}
      {featured && (
        <div className="absolute top-3 ltr:left-3 rtl:right-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg body-arabic">
          {t('offers.featured')}
        </div>
      )}
      
      <div className="p-6">
        {/* Seller information */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className={`w-12 h-12 rounded-full flex items-center justify-center shadow-lg ${
              featured 
                ? 'bg-gradient-to-br from-blue-500 to-purple-600' 
                : 'bg-gradient-to-br from-gray-500 to-gray-600'
            }`}>
              <span className="text-white font-bold text-lg">
                {offer.seller?.username?.charAt(0) || offer.seller?.name?.charAt(0) || 'S'}
              </span>
            </div>
            <div className="ltr:ml-3 rtl:mr-3">
              <h4 className={`font-semibold group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors heading-arabic ${
                featured ? 'text-blue-900 dark:text-blue-300' : 'text-gray-900 dark:text-white'
              }`}>
                {offer.seller?.username || offer.seller?.name || 'مستخدم غير معروف'}
              </h4>
              <div className="flex items-center">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-600 dark:text-gray-300 ltr:ml-1 rtl:mr-1 body-arabic">
                  {offer.rating} ({offer.trades} {t('offers.trades')})
                </span>
                {offer.rating >= 4.8 && (
                  <Shield className="w-4 h-4 text-green-500 ltr:ml-1 rtl:mr-1" />
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center">
            <div className={`w-3 h-3 rounded-full ${offer.isOnline ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
            <span className={`text-xs ltr:ml-2 rtl:mr-2 font-medium ${offer.isOnline ? 'text-green-600' : 'text-gray-500'}`}>
              {offer.isOnline ? t('common.online') : t('common.offline')}
            </span>
          </div>
        </div>

        {/* Offer details */}
        <div className={`rounded-lg p-4 mb-4 ${featured ? 'bg-blue-50 dark:bg-blue-900/20' : 'bg-gray-50 dark:bg-gray-700'}`}>
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-600 dark:text-gray-300 text-sm">{t('offers.availableAmount')}</span>
            <span className="font-bold text-lg text-gray-900 dark:text-white">{offer.amount.toLocaleString()} USDT</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600 dark:text-gray-300 text-sm">{t('offers.price')}</span>
            <span className={`font-bold text-xl ${featured ? 'text-blue-600 dark:text-blue-400' : 'text-blue-600 dark:text-blue-400'}`}>
              {offer.price} {offer.currency}
            </span>
          </div>
        </div>

        {/* Payment method */}
        <div className="flex items-center justify-between mb-6">
          <span className="text-gray-600 dark:text-gray-300 text-sm">{t('offers.paymentMethod')}:</span>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
            featured
              ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
          }`}>
            {offer.paymentMethods?.[0] || 'تحويل بنكي'}
          </span>
        </div>

        {/* Additional statistics */}
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-4">
          <div className="flex items-center">
            <Clock className="w-3 h-3 ltr:mr-1 rtl:ml-1" />
            <span>{t('offers.avgTime')}: {offer.avgTime || '5 دقائق'}</span>
          </div>
          <div className="flex items-center">
            <TrendingUp className="w-3 h-3 ltr:mr-1 rtl:ml-1" />
            <span>{t('offers.completionRate')}: {offer.completionRate || 98}%</span>
          </div>
        </div>

        {/* Buy button */}
        <button
          onClick={() => onBuy(offer.id)}
          disabled={!offer.isOnline}
          className={`w-full font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl ${
            offer.isOnline
              ? featured
                ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700'
                : 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          {offer.isOnline ? t('offers.buyNow') : t('offers.unavailable')}
        </button>
      </div>

      {/* Hover effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
    </div>
  );
}
