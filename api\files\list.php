<?php
/**
 * API endpoint لعرض قائمة الملفات
 * File List API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";
require_once __DIR__ . "/../middleware/auth.php";

// التحقق من طريقة الطلب
validateRequestMethod(['GET']);

try {
    // التحقق من المصادقة
    $auth = requireAuth();
    $userId = $auth->getCurrentUserId();
    $isAdmin = $auth->isAdmin();
    
    if (!$userId) {
        sendErrorResponse('المصادقة مطلوبة', 401);
    }
    
    // الحصول على معاملات البحث والترقيم
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(50, max(10, (int)($_GET['limit'] ?? 20))); // بين 10 و 50
    $offset = ($page - 1) * $limit;
    
    $fileType = $_GET['fileType'] ?? '';
    $relatedId = $_GET['relatedId'] ?? '';
    $search = trim($_GET['search'] ?? '');
    $sortBy = $_GET['sortBy'] ?? 'created_at';
    $sortOrder = strtoupper($_GET['sortOrder'] ?? 'DESC') === 'ASC' ? 'ASC' : 'DESC';
    
    // التحقق من صحة حقل الترتيب
    $allowedSortFields = ['created_at', 'original_name', 'file_size', 'download_count'];
    if (!in_array($sortBy, $allowedSortFields)) {
        $sortBy = 'created_at';
    }
    
    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // بناء الاستعلام
    $whereConditions = [];
    $params = [];
    
    // المدراء يمكنهم رؤية جميع الملفات، المستخدمون العاديون يرون ملفاتهم فقط
    if (!$isAdmin) {
        $whereConditions[] = "user_id = ?";
        $params[] = $userId;
    }
    
    // تصفية حسب نوع الملف
    if (!empty($fileType)) {
        $whereConditions[] = "file_type = ?";
        $params[] = $fileType;
    }
    
    // تصفية حسب المعرف المرتبط
    if (!empty($relatedId) && is_numeric($relatedId)) {
        $whereConditions[] = "related_id = ?";
        $params[] = $relatedId;
    }
    
    // البحث في اسم الملف أو الوصف
    if (!empty($search)) {
        $whereConditions[] = "(original_name LIKE ? OR description LIKE ?)";
        $searchTerm = '%' . $search . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // استعلام العد الإجمالي
    $countQuery = "SELECT COUNT(*) as total FROM uploaded_files $whereClause";
    $countStmt = $connection->prepare($countQuery);
    $countStmt->execute($params);
    $totalFiles = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // استعلام البيانات مع الترقيم
    $dataQuery = "
        SELECT uf.id, uf.user_id, uf.original_name, uf.safe_filename, 
               uf.file_size, uf.file_type, uf.mime_type, uf.description, 
               uf.related_id, uf.created_at, uf.download_count, uf.last_downloaded,
               u.username, u.email
        FROM uploaded_files uf
        LEFT JOIN users u ON uf.user_id = u.id
        $whereClause
        ORDER BY uf.$sortBy $sortOrder
        LIMIT ? OFFSET ?
    ";
    
    $dataParams = array_merge($params, [$limit, $offset]);
    $dataStmt = $connection->prepare($dataQuery);
    $dataStmt->execute($dataParams);
    $files = $dataStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تنسيق البيانات
    $formattedFiles = array_map(function($file) {
        return [
            'id' => (int)$file['id'],
            'userId' => (int)$file['user_id'],
            'username' => $file['username'],
            'originalName' => $file['original_name'],
            'safeFilename' => $file['safe_filename'],
            'fileSize' => (int)$file['file_size'],
            'fileSizeFormatted' => formatFileSize($file['file_size']),
            'fileType' => $file['file_type'],
            'mimeType' => $file['mime_type'],
            'description' => $file['description'],
            'relatedId' => $file['related_id'] ? (int)$file['related_id'] : null,
            'createdAt' => $file['created_at'],
            'downloadCount' => (int)$file['download_count'],
            'lastDownloaded' => $file['last_downloaded'],
            'downloadUrl' => '/api/files/download.php?id=' . $file['id'],
            'deleteUrl' => '/api/files/delete.php?id=' . $file['id']
        ];
    }, $files);
    
    // حساب معلومات الترقيم
    $totalPages = ceil($totalFiles / $limit);
    $hasNextPage = $page < $totalPages;
    $hasPrevPage = $page > 1;
    
    sendSuccessResponse([
        'files' => $formattedFiles,
        'pagination' => [
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalFiles' => (int)$totalFiles,
            'limit' => $limit,
            'hasNextPage' => $hasNextPage,
            'hasPrevPage' => $hasPrevPage,
            'nextPage' => $hasNextPage ? $page + 1 : null,
            'prevPage' => $hasPrevPage ? $page - 1 : null
        ],
        'filters' => [
            'fileType' => $fileType,
            'relatedId' => $relatedId,
            'search' => $search,
            'sortBy' => $sortBy,
            'sortOrder' => $sortOrder
        ]
    ], 'تم جلب قائمة الملفات بنجاح');
    
} catch (Exception $e) {
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    error_log('Database error in list.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}

/**
 * تنسيق حجم الملف
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}
?>
