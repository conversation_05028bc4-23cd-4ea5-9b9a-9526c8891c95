'use client';

import { useState, useEffect } from 'react';
import { 
  User,
  Edit,
  Save,
  X,
  Camera,
  Shield,
  CheckCircle,
  AlertCircle,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Globe,
  MessageSquare,
  Star,
  Upload
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { useAuth } from '@/contexts/AuthContext';

interface UserProfile {
  id: string;
  username: string;
  fullName: string;
  email: string;
  phone: string;
  country: string;
  city: string;
  bio: string;
  profileImage: string;
  telegramUsername: string;
  whatsappNumber: string;
  joinDate: string;
  lastActivity: string;
  isVerified: boolean;
  verificationLevel: 'none' | 'basic' | 'advanced' | 'premium';
  rating: number;
  totalTrades: number;
  completedTrades: number;
  languages: string[];
  timezone: string;
}

export default function ProfilePage() {
  const { t, formatDate } = useUserDashboardTranslation();
  const { user } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [editForm, setEditForm] = useState<Partial<UserProfile>>({});

  // جلب بيانات الملف الشخصي
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);
        
        // محاكاة البيانات - يجب استبدالها بـ API حقيقي
        const mockProfile: UserProfile = {
          id: user?.id || '1',
          username: user?.username || 'المستخدم',
          fullName: user?.fullName || 'أحمد محمد السعودي',
          email: user?.email || '<EMAIL>',
          phone: '+966501234567',
          country: 'المملكة العربية السعودية',
          city: 'الرياض',
          bio: 'متداول محترف في العملات المشفرة مع خبرة 5 سنوات في السوق السعودي',
          profileImage: '',
          telegramUsername: '@ahmed_trader',
          whatsappNumber: '+966501234567',
          joinDate: '2023-01-15T00:00:00Z',
          lastActivity: new Date().toISOString(),
          isVerified: true,
          verificationLevel: 'advanced',
          rating: 4.8,
          totalTrades: 156,
          completedTrades: 148,
          languages: ['العربية', 'English'],
          timezone: 'Asia/Riyadh'
        };

        setProfile(mockProfile);
        setEditForm(mockProfile);
      } catch (error) {
        console.error('Error fetching profile:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [user]);

  // حفظ التغييرات
  const handleSave = async () => {
    if (!profile) return;

    try {
      setSaving(true);
      
      // محاكاة حفظ البيانات - يجب استبدالها بـ API حقيقي
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setProfile({ ...profile, ...editForm });
      setEditing(false);
    } catch (error) {
      console.error('Error saving profile:', error);
    } finally {
      setSaving(false);
    }
  };

  // إلغاء التعديل
  const handleCancel = () => {
    setEditForm(profile || {});
    setEditing(false);
  };

  // تحديث حقل في النموذج
  const updateField = (field: keyof UserProfile, value: any) => {
    setEditForm(prev => ({ ...prev, [field]: value }));
  };

  // الحصول على لون مستوى التحقق
  const getVerificationColor = (level: UserProfile['verificationLevel']) => {
    switch (level) {
      case 'premium':
        return 'text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/30';
      case 'advanced':
        return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      case 'basic':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  // الحصول على نص مستوى التحقق
  const getVerificationText = (level: UserProfile['verificationLevel']) => {
    switch (level) {
      case 'premium':
        return 'مميز';
      case 'advanced':
        return 'متقدم';
      case 'basic':
        return 'أساسي';
      default:
        return 'غير محقق';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          خطأ في تحميل الملف الشخصي
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          حدث خطأ أثناء تحميل بيانات الملف الشخصي
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            {t('profile.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            إدارة معلوماتك الشخصية وإعدادات الحساب
          </p>
        </div>

        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          {editing ? (
            <>
              <button
                onClick={handleCancel}
                disabled={saving}
                className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors disabled:opacity-50"
              >
                <X className="w-4 h-4" />
                <span>إلغاء</span>
              </button>
              <button
                onClick={handleSave}
                disabled={saving}
                className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                <Save className="w-4 h-4" />
                <span>{saving ? 'جاري الحفظ...' : 'حفظ'}</span>
              </button>
            </>
          ) : (
            <button
              onClick={() => setEditing(true)}
              className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <Edit className="w-4 h-4" />
              <span>تعديل الملف الشخصي</span>
            </button>
          )}
        </div>
      </div>

      {/* معلومات أساسية */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col md:flex-row md:items-start gap-6">
          {/* صورة الملف الشخصي */}
          <div className="flex flex-col items-center">
            <div className="relative w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center mb-4">
              {profile.profileImage ? (
                <img 
                  src={profile.profileImage} 
                  alt={profile.fullName}
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <User className="w-12 h-12 text-blue-600 dark:text-blue-400" />
              )}
              
              {profile.isVerified && (
                <div className="absolute -bottom-1 -right-1 w-8 h-8 bg-green-500 rounded-full border-4 border-white dark:border-gray-800 flex items-center justify-center">
                  <CheckCircle className="w-4 h-4 text-white" />
                </div>
              )}
              
              {editing && (
                <button className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white opacity-0 hover:opacity-100 transition-opacity">
                  <Camera className="w-6 h-6" />
                </button>
              )}
            </div>
            
            <div className="text-center">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                {profile.fullName}
              </h2>
              <p className="text-gray-600 dark:text-gray-400">@{profile.username}</p>
              
              <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse mt-2">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getVerificationColor(profile.verificationLevel)}`}>
                  {getVerificationText(profile.verificationLevel)}
                </span>
                
                <div className="flex items-center space-x-1 rtl:space-x-reverse">
                  <Star className="w-4 h-4 text-yellow-500 fill-current" />
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {profile.rating}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* المعلومات الشخصية */}
          <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* الاسم الكامل */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                الاسم الكامل
              </label>
              {editing ? (
                <input
                  type="text"
                  value={editForm.fullName || ''}
                  onChange={(e) => updateField('fullName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              ) : (
                <p className="text-gray-900 dark:text-white">{profile.fullName}</p>
              )}
            </div>

            {/* البريد الإلكتروني */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                البريد الإلكتروني
              </label>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Mail className="w-4 h-4 text-gray-400" />
                <span className="text-gray-900 dark:text-white">{profile.email}</span>
                {profile.isVerified && (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                )}
              </div>
            </div>

            {/* رقم الهاتف */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                رقم الهاتف
              </label>
              {editing ? (
                <input
                  type="tel"
                  value={editForm.phone || ''}
                  onChange={(e) => updateField('phone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              ) : (
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Phone className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-900 dark:text-white">{profile.phone}</span>
                </div>
              )}
            </div>

            {/* البلد */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                البلد
              </label>
              {editing ? (
                <select
                  value={editForm.country || ''}
                  onChange={(e) => updateField('country', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="المملكة العربية السعودية">المملكة العربية السعودية</option>
                  <option value="الإمارات العربية المتحدة">الإمارات العربية المتحدة</option>
                  <option value="الكويت">الكويت</option>
                  <option value="قطر">قطر</option>
                  <option value="البحرين">البحرين</option>
                  <option value="عمان">عمان</option>
                </select>
              ) : (
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <MapPin className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-900 dark:text-white">{profile.country}</span>
                </div>
              )}
            </div>

            {/* تليجرام */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                تليجرام
              </label>
              {editing ? (
                <input
                  type="text"
                  value={editForm.telegramUsername || ''}
                  onChange={(e) => updateField('telegramUsername', e.target.value)}
                  placeholder="@username"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              ) : (
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <MessageSquare className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-900 dark:text-white">{profile.telegramUsername || 'غير محدد'}</span>
                </div>
              )}
            </div>

            {/* واتساب */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                واتساب
              </label>
              {editing ? (
                <input
                  type="tel"
                  value={editForm.whatsappNumber || ''}
                  onChange={(e) => updateField('whatsappNumber', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              ) : (
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <Phone className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-900 dark:text-white">{profile.whatsappNumber || 'غير محدد'}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* النبذة الشخصية */}
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            النبذة الشخصية
          </label>
          {editing ? (
            <textarea
              value={editForm.bio || ''}
              onChange={(e) => updateField('bio', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="اكتب نبذة عن نفسك..."
            />
          ) : (
            <p className="text-gray-900 dark:text-white">{profile.bio || 'لا توجد نبذة شخصية'}</p>
          )}
        </div>
      </div>

      {/* إحصائيات التداول */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          إحصائيات التداول
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {profile.totalTrades}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الصفقات</p>
          </div>
          
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">
              {profile.completedTrades}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">الصفقات المكتملة</p>
          </div>
          
          <div className="text-center">
            <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
              {profile.rating}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">التقييم</p>
          </div>
          
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {Math.round((profile.completedTrades / profile.totalTrades) * 100)}%
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">معدل الإكمال</p>
          </div>
        </div>
      </div>

      {/* معلومات إضافية */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          معلومات إضافية
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
              <Calendar className="w-4 h-4 text-gray-400" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">تاريخ الانضمام</span>
            </div>
            <p className="text-gray-900 dark:text-white">
              {formatDate(profile.joinDate, { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
          
          <div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
              <Globe className="w-4 h-4 text-gray-400" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">آخر نشاط</span>
            </div>
            <p className="text-gray-900 dark:text-white">
              {formatDate(profile.lastActivity, { 
                month: 'short', 
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </p>
          </div>
        </div>
      </div>

      {/* روابط سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <a
          href="/user-dashboard/profile/security"
          className="flex items-center space-x-3 rtl:space-x-reverse p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <Shield className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">الأمان</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">إدارة كلمة المرور والأمان</p>
          </div>
        </a>
        
        <a
          href="/user-dashboard/profile/verification"
          className="flex items-center space-x-3 rtl:space-x-reverse p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">التحقق</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">التحقق من الهوية والوثائق</p>
          </div>
        </a>
        
        <a
          href="/user-dashboard/settings"
          className="flex items-center space-x-3 rtl:space-x-reverse p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <User className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">الإعدادات</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">تخصيص إعدادات الحساب</p>
          </div>
        </a>
      </div>
    </div>
  );
}
