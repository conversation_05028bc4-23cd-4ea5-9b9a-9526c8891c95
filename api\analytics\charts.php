<?php
/**
 * API endpoint لبيانات الرسوم البيانية والتحليلات
 * Charts and Analytics Data API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";
require_once __DIR__ . "/../middleware/auth.php";

// التحقق من طريقة الطلب
validateRequestMethod(['GET']);

/**
 * الحصول على بيانات حجم التداول
 */
function getTradingVolumeData($connection, $period = '30d') {
    $dateCondition = '';
    $groupBy = '';
    
    switch ($period) {
        case '24h':
            $dateCondition = "WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";
            $groupBy = "DATE_FORMAT(t.created_at, '%H:00')";
            break;
        case '7d':
            $dateCondition = "WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            $groupBy = "DATE(t.created_at)";
            break;
        case '30d':
            $dateCondition = "WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            $groupBy = "DATE(t.created_at)";
            break;
        case '1y':
            $dateCondition = "WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)";
            $groupBy = "DATE_FORMAT(t.created_at, '%Y-%m')";
            break;
        default:
            $dateCondition = "WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            $groupBy = "DATE(t.created_at)";
    }
    
    $stmt = $connection->prepare("
        SELECT 
            $groupBy as period,
            COUNT(*) as trade_count,
            SUM(t.amount) as total_volume,
            AVG(t.amount) as avg_amount,
            COUNT(DISTINCT t.seller_id) + COUNT(DISTINCT t.buyer_id) as unique_users
        FROM trades t
        $dateCondition
        AND t.status IN ('completed', 'payment_sent', 'payment_received')
        GROUP BY $groupBy
        ORDER BY period ASC
    ");
    
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $labels = [];
    $volumes = [];
    $counts = [];
    $avgAmounts = [];
    
    foreach ($results as $row) {
        $labels[] = $row['period'];
        $volumes[] = (float)$row['total_volume'];
        $counts[] = (int)$row['trade_count'];
        $avgAmounts[] = (float)$row['avg_amount'];
    }
    
    return [
        'labels' => $labels,
        'volumes' => $volumes,
        'counts' => $counts,
        'avgAmounts' => $avgAmounts
    ];
}

/**
 * الحصول على بيانات الأسعار
 */
function getPriceData($connection, $currency = 'USDT', $period = '30d') {
    $dateCondition = '';
    $groupBy = '';
    
    switch ($period) {
        case '24h':
            $dateCondition = "WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";
            $groupBy = "DATE_FORMAT(t.created_at, '%H:00')";
            break;
        case '7d':
            $dateCondition = "WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            $groupBy = "DATE(t.created_at)";
            break;
        case '30d':
            $dateCondition = "WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            $groupBy = "DATE(t.created_at)";
            break;
        default:
            $dateCondition = "WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            $groupBy = "DATE(t.created_at)";
    }
    
    $stmt = $connection->prepare("
        SELECT 
            $groupBy as period,
            AVG(t.price) as avg_price,
            MIN(t.price) as min_price,
            MAX(t.price) as max_price,
            COUNT(*) as trade_count
        FROM trades t
        $dateCondition
        AND t.currency = ?
        AND t.status IN ('completed', 'payment_sent', 'payment_received')
        GROUP BY $groupBy
        ORDER BY period ASC
    ");
    
    $stmt->execute([$currency]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $labels = [];
    $prices = [];
    $minPrices = [];
    $maxPrices = [];
    
    foreach ($results as $row) {
        $labels[] = $row['period'];
        $prices[] = (float)$row['avg_price'];
        $minPrices[] = (float)$row['min_price'];
        $maxPrices[] = (float)$row['max_price'];
    }
    
    return [
        'labels' => $labels,
        'prices' => $prices,
        'minPrices' => $minPrices,
        'maxPrices' => $maxPrices
    ];
}

/**
 * الحصول على بيانات نشاط المستخدمين
 */
function getUserActivityData($connection, $period = '30d') {
    $dateCondition = '';
    $groupBy = '';
    
    switch ($period) {
        case '7d':
            $dateCondition = "WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            $groupBy = "DATE(u.created_at)";
            break;
        case '30d':
            $dateCondition = "WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            $groupBy = "DATE(u.created_at)";
            break;
        case '1y':
            $dateCondition = "WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)";
            $groupBy = "DATE_FORMAT(u.created_at, '%Y-%m')";
            break;
        default:
            $dateCondition = "WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            $groupBy = "DATE(u.created_at)";
    }
    
    // المستخدمون الجدد
    $newUsersStmt = $connection->prepare("
        SELECT 
            $groupBy as period,
            COUNT(*) as new_users
        FROM users u
        $dateCondition
        GROUP BY $groupBy
        ORDER BY period ASC
    ");
    $newUsersStmt->execute();
    $newUsersResults = $newUsersStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // المستخدمون النشطون (الذين لديهم صفقات)
    $activeUsersStmt = $connection->prepare("
        SELECT 
            $groupBy as period,
            COUNT(DISTINCT t.seller_id) + COUNT(DISTINCT t.buyer_id) as active_users
        FROM trades t
        $dateCondition
        GROUP BY $groupBy
        ORDER BY period ASC
    ");
    $activeUsersStmt->execute();
    $activeUsersResults = $activeUsersStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // دمج البيانات
    $labels = [];
    $newUsers = [];
    $activeUsers = [];
    
    // إنشاء مصفوفة للمستخدمين النشطين
    $activeUsersMap = [];
    foreach ($activeUsersResults as $row) {
        $activeUsersMap[$row['period']] = (int)$row['active_users'];
    }
    
    foreach ($newUsersResults as $row) {
        $period = $row['period'];
        $labels[] = $period;
        $newUsers[] = (int)$row['new_users'];
        $activeUsers[] = $activeUsersMap[$period] ?? 0;
    }
    
    return [
        'labels' => $labels,
        'newUsers' => $newUsers,
        'activeUsers' => $activeUsers
    ];
}

/**
 * الحصول على توزيع العملات
 */
function getCurrencyDistribution($connection) {
    $stmt = $connection->prepare("
        SELECT 
            t.currency,
            COUNT(*) as trade_count,
            SUM(t.amount) as total_volume,
            (COUNT(*) * 100.0 / (SELECT COUNT(*) FROM trades WHERE status IN ('completed', 'payment_sent', 'payment_received'))) as percentage
        FROM trades t
        WHERE t.status IN ('completed', 'payment_sent', 'payment_received')
        GROUP BY t.currency
        ORDER BY trade_count DESC
        LIMIT 10
    ");
    
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $currencies = [];
    $percentages = [];
    $volumes = [];
    
    foreach ($results as $row) {
        $currencies[] = $row['currency'];
        $percentages[] = (float)$row['percentage'];
        $volumes[] = (float)$row['total_volume'];
    }
    
    return [
        'currencies' => $currencies,
        'percentages' => $percentages,
        'volumes' => $volumes
    ];
}

/**
 * الحصول على إحصائيات عامة
 */
function getGeneralStats($connection) {
    // إجمالي الصفقات
    $totalTradesStmt = $connection->prepare("
        SELECT COUNT(*) as total FROM trades WHERE status IN ('completed', 'payment_sent', 'payment_received')
    ");
    $totalTradesStmt->execute();
    $totalTrades = $totalTradesStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // إجمالي الحجم
    $totalVolumeStmt = $connection->prepare("
        SELECT SUM(amount) as total FROM trades WHERE status IN ('completed', 'payment_sent', 'payment_received')
    ");
    $totalVolumeStmt->execute();
    $totalVolume = $totalVolumeStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // إجمالي المستخدمين
    $totalUsersStmt = $connection->prepare("SELECT COUNT(*) as total FROM users WHERE is_active = 1");
    $totalUsersStmt->execute();
    $totalUsers = $totalUsersStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // المستخدمون النشطون (آخر 30 يوم)
    $activeUsersStmt = $connection->prepare("
        SELECT COUNT(DISTINCT seller_id) + COUNT(DISTINCT buyer_id) as active
        FROM trades 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $activeUsersStmt->execute();
    $activeUsers = $activeUsersStmt->fetch(PDO::FETCH_ASSOC)['active'];
    
    return [
        'totalTrades' => (int)$totalTrades,
        'totalVolume' => (float)$totalVolume,
        'totalUsers' => (int)$totalUsers,
        'activeUsers' => (int)$activeUsers
    ];
}

try {
    // التحقق من المصادقة
    $auth = requireAuth();
    $userId = $auth->getCurrentUserId();
    $isAdmin = $auth->isAdmin();
    
    if (!$userId) {
        sendErrorResponse('المصادقة مطلوبة', 401);
    }
    
    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // الحصول على معاملات الطلب
    $chartType = $_GET['type'] ?? 'all';
    $period = $_GET['period'] ?? '30d';
    $currency = $_GET['currency'] ?? 'USDT';
    
    $response = [];
    
    switch ($chartType) {
        case 'volume':
            $response = getTradingVolumeData($connection, $period);
            break;
            
        case 'price':
            $response = getPriceData($connection, $currency, $period);
            break;
            
        case 'users':
            $response = getUserActivityData($connection, $period);
            break;
            
        case 'distribution':
            $response = getCurrencyDistribution($connection);
            break;
            
        case 'stats':
            $response = getGeneralStats($connection);
            break;
            
        case 'all':
        default:
            $response = [
                'volume' => getTradingVolumeData($connection, $period),
                'price' => getPriceData($connection, $currency, $period),
                'users' => getUserActivityData($connection, $period),
                'distribution' => getCurrencyDistribution($connection),
                'stats' => getGeneralStats($connection)
            ];
            break;
    }
    
    // إضافة معلومات إضافية
    $response['metadata'] = [
        'period' => $period,
        'currency' => $currency,
        'generatedAt' => date('Y-m-d H:i:s'),
        'timezone' => date_default_timezone_get()
    ];
    
    sendSuccessResponse($response, 'تم جلب بيانات الرسوم البيانية بنجاح');
    
} catch (Exception $e) {
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    error_log('Database error in charts.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}
?>
