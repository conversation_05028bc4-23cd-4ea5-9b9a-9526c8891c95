import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { describe, it, expect, jest, beforeEach } from '@jest/globals';

// Mock the translation hook
jest.mock('@/hooks/useAdminTranslation', () => ({
  useAdminTranslation: () => ({
    t: (key: string) => key,
    language: 'en',
    isRTL: false,
    getDirectionClasses: () => ({ textAlign: 'left', direction: 'ltr' }),
    formatNumber: (num: number) => num.toLocaleString(),
    formatCurrency: (amount: number) => `$${amount.toFixed(2)}`,
    formatDate: (date: string) => new Date(date).toLocaleDateString(),
    formatRelativeTime: (date: string) => 'just now'
  })
}));

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    pathname: '/admin',
    query: {},
    asPath: '/admin',
  }),
}));

import AdminDashboard from '@/components/AdminDashboard';

describe('Admin Dashboard Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock localStorage for admin data
    const mockAdminData = {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'super_admin',
      walletAddress: '******************************************'
    };
    
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(() => JSON.stringify(mockAdminData)),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      },
      writable: true,
    });
  });

  it('renders admin dashboard with all main components', () => {
    render(<AdminDashboard />);
    
    // Check if main dashboard elements are present
    expect(screen.getByText('dashboard.title')).toBeInTheDocument();
    expect(screen.getByText('dashboard.subtitle')).toBeInTheDocument();
  });

  it('displays admin statistics cards', () => {
    render(<AdminDashboard />);
    
    // Check for statistics cards
    expect(screen.getByText('dashboard.stats.totalUsers')).toBeInTheDocument();
    expect(screen.getByText('dashboard.stats.activeTrades')).toBeInTheDocument();
    expect(screen.getByText('dashboard.stats.totalVolume')).toBeInTheDocument();
    expect(screen.getByText('dashboard.stats.pendingDisputes')).toBeInTheDocument();
  });

  it('handles tab navigation correctly', async () => {
    render(<AdminDashboard />);
    
    // Test navigation to users tab
    const usersTab = screen.getByText('navigation.users');
    fireEvent.click(usersTab);
    
    await waitFor(() => {
      expect(screen.getByText('users.title')).toBeInTheDocument();
    });
    
    // Test navigation to trades tab
    const tradesTab = screen.getByText('navigation.trades');
    fireEvent.click(tradesTab);
    
    await waitFor(() => {
      expect(screen.getByText('trades.title')).toBeInTheDocument();
    });
  });

  it('displays user management interface when users tab is active', async () => {
    render(<AdminDashboard />);
    
    const usersTab = screen.getByText('navigation.users');
    fireEvent.click(usersTab);
    
    await waitFor(() => {
      expect(screen.getByText('users.title')).toBeInTheDocument();
      expect(screen.getByText('users.stats.totalUsers')).toBeInTheDocument();
      expect(screen.getByText('users.tabs.overview')).toBeInTheDocument();
    });
  });

  it('displays trade management interface when trades tab is active', async () => {
    render(<AdminDashboard />);
    
    const tradesTab = screen.getByText('navigation.trades');
    fireEvent.click(tradesTab);
    
    await waitFor(() => {
      expect(screen.getByText('trades.title')).toBeInTheDocument();
      expect(screen.getByText('trades.stats.totalTrades')).toBeInTheDocument();
    });
  });

  it('displays dispute management interface when disputes tab is active', async () => {
    render(<AdminDashboard />);
    
    const disputesTab = screen.getByText('navigation.disputes');
    fireEvent.click(disputesTab);
    
    await waitFor(() => {
      expect(screen.getByText('disputes.title')).toBeInTheDocument();
      expect(screen.getByText('disputes.stats.totalDisputes')).toBeInTheDocument();
    });
  });

  it('displays network management interface when networks tab is active', async () => {
    render(<AdminDashboard />);
    
    const networksTab = screen.getByText('navigation.networks');
    fireEvent.click(networksTab);
    
    await waitFor(() => {
      expect(screen.getByText('networks.title')).toBeInTheDocument();
      expect(screen.getByText('networks.stats.totalNetworks')).toBeInTheDocument();
    });
  });

  it('displays notification system when notifications tab is active', async () => {
    render(<AdminDashboard />);
    
    const notificationsTab = screen.getByText('navigation.notifications');
    fireEvent.click(notificationsTab);
    
    await waitFor(() => {
      expect(screen.getByText('notifications.title')).toBeInTheDocument();
      expect(screen.getByText('notifications.stats.totalNotifications')).toBeInTheDocument();
    });
  });

  it('displays system monitoring when monitoring tab is active', async () => {
    render(<AdminDashboard />);
    
    const monitoringTab = screen.getByText('navigation.monitoring');
    fireEvent.click(monitoringTab);
    
    await waitFor(() => {
      expect(screen.getByText('monitoring.title')).toBeInTheDocument();
      expect(screen.getByText('monitoring.subtitle')).toBeInTheDocument();
    });
  });

  it('displays testing tools when testing tab is active', async () => {
    render(<AdminDashboard />);
    
    const testingTab = screen.getByText('Testing Tools');
    fireEvent.click(testingTab);
    
    await waitFor(() => {
      expect(screen.getByText('testing.title')).toBeInTheDocument();
      expect(screen.getByText('testing.subtitle')).toBeInTheDocument();
    });
  });

  it('displays security interface when security tab is active', async () => {
    render(<AdminDashboard />);
    
    const securityTab = screen.getByText('Security');
    fireEvent.click(securityTab);
    
    await waitFor(() => {
      expect(screen.getByText('auth.title')).toBeInTheDocument();
      expect(screen.getByText('auth.subtitle')).toBeInTheDocument();
    });
  });

  it('displays settings interface when settings tab is active', async () => {
    render(<AdminDashboard />);
    
    const settingsTab = screen.getByText('navigation.settings');
    fireEvent.click(settingsTab);
    
    await waitFor(() => {
      expect(screen.getByText('settings.title')).toBeInTheDocument();
      expect(screen.getByText('settings.subtitle')).toBeInTheDocument();
    });
  });

  it('handles admin logout correctly', async () => {
    render(<AdminDashboard />);
    
    // Find and click logout button
    const logoutButton = screen.getByText('navigation.logout');
    fireEvent.click(logoutButton);
    
    // Should clear localStorage and redirect
    expect(localStorage.removeItem).toHaveBeenCalledWith('adminData');
  });

  it('displays admin profile information', () => {
    render(<AdminDashboard />);
    
    // Check if admin profile info is displayed
    expect(screen.getByText('admin')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('handles theme switching', () => {
    render(<AdminDashboard />);
    
    // Find theme toggle button
    const themeToggle = screen.getByRole('button', { name: /theme/i });
    fireEvent.click(themeToggle);
    
    // Should toggle theme class
    expect(document.documentElement).toHaveClass('dark');
  });

  it('handles language switching', () => {
    render(<AdminDashboard />);
    
    // Find language toggle button
    const languageToggle = screen.getByRole('button', { name: /language/i });
    fireEvent.click(languageToggle);
    
    // Should change language
    expect(languageToggle).toBeInTheDocument();
  });

  it('displays real-time notifications', async () => {
    render(<AdminDashboard />);
    
    // Check for notification bell
    const notificationBell = screen.getByRole('button', { name: /notifications/i });
    fireEvent.click(notificationBell);
    
    await waitFor(() => {
      expect(screen.getByText('notifications.recent')).toBeInTheDocument();
    });
  });

  it('handles search functionality', () => {
    render(<AdminDashboard />);
    
    // Find global search input
    const searchInput = screen.getByPlaceholderText('dashboard.search.placeholder');
    fireEvent.change(searchInput, { target: { value: 'test search' } });
    
    expect(searchInput).toHaveValue('test search');
  });

  it('displays breadcrumb navigation', () => {
    render(<AdminDashboard />);
    
    // Check for breadcrumb
    expect(screen.getByText('dashboard.breadcrumb.home')).toBeInTheDocument();
  });

  it('handles responsive design', () => {
    // Mock window.innerWidth for mobile view
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768,
    });
    
    render(<AdminDashboard />);
    
    // Should show mobile menu toggle
    const mobileMenuToggle = screen.getByRole('button', { name: /menu/i });
    expect(mobileMenuToggle).toBeInTheDocument();
  });

  it('handles error states gracefully', () => {
    // Mock console.error to suppress error logs in tests
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    // Mock a component that throws an error
    const ErrorComponent = () => {
      throw new Error('Test error');
    };
    
    // Should not crash the entire dashboard
    expect(() => {
      render(<ErrorComponent />);
    }).toThrow('Test error');
    
    consoleSpy.mockRestore();
  });

  it('persists user preferences', () => {
    render(<AdminDashboard />);
    
    // Change a preference (e.g., theme)
    const themeToggle = screen.getByRole('button', { name: /theme/i });
    fireEvent.click(themeToggle);
    
    // Should save to localStorage
    expect(localStorage.setItem).toHaveBeenCalledWith('theme', 'dark');
  });

  it('handles keyboard navigation', () => {
    render(<AdminDashboard />);
    
    // Test tab navigation
    const firstTab = screen.getByText('navigation.dashboard');
    firstTab.focus();
    
    fireEvent.keyDown(firstTab, { key: 'Tab' });
    
    // Should move focus to next element
    expect(document.activeElement).not.toBe(firstTab);
  });

  it('displays loading states during data fetching', async () => {
    render(<AdminDashboard />);
    
    // Should show loading indicators for async data
    expect(screen.getByText('common.loading')).toBeInTheDocument();
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByText('common.loading')).not.toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('handles data refresh correctly', async () => {
    render(<AdminDashboard />);
    
    // Find refresh button
    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    fireEvent.click(refreshButton);
    
    // Should trigger data refresh
    await waitFor(() => {
      expect(screen.getByText('common.loading')).toBeInTheDocument();
    });
  });

  it('validates admin permissions', () => {
    // Mock admin with limited permissions
    const limitedAdminData = {
      id: 2,
      username: 'moderator',
      email: '<EMAIL>',
      role: 'moderator',
      permissions: ['user_management', 'dispute_resolution']
    };
    
    localStorage.getItem = jest.fn(() => JSON.stringify(limitedAdminData));
    
    render(<AdminDashboard />);
    
    // Should only show tabs for allowed permissions
    expect(screen.getByText('navigation.users')).toBeInTheDocument();
    expect(screen.getByText('navigation.disputes')).toBeInTheDocument();
    
    // Should not show restricted tabs
    expect(screen.queryByText('navigation.settings')).not.toBeInTheDocument();
  });
});

describe('Component Integration', () => {
  it('integrates data table with form for editing', async () => {
    render(<AdminDashboard />);
    
    // Navigate to users tab
    fireEvent.click(screen.getByText('navigation.users'));
    
    await waitFor(() => {
      // Click edit button on a user
      const editButton = screen.getAllByTitle('common.actions.edit')[0];
      fireEvent.click(editButton);
      
      // Should open edit form
      expect(screen.getByText('users.editUser')).toBeInTheDocument();
    });
  });

  it('integrates charts with data filtering', async () => {
    render(<AdminDashboard />);
    
    // Navigate to analytics
    fireEvent.click(screen.getByText('navigation.analytics'));
    
    await waitFor(() => {
      // Apply date filter
      const dateFilter = screen.getByLabelText('analytics.dateRange');
      fireEvent.change(dateFilter, { target: { value: 'last-7-days' } });
      
      // Charts should update
      expect(screen.getByText('analytics.charts.updated')).toBeInTheDocument();
    });
  });

  it('integrates notifications with action buttons', async () => {
    render(<AdminDashboard />);
    
    // Open notifications
    const notificationBell = screen.getByRole('button', { name: /notifications/i });
    fireEvent.click(notificationBell);
    
    await waitFor(() => {
      // Click on a notification action
      const actionButton = screen.getByText('notifications.actions.markAsRead');
      fireEvent.click(actionButton);
      
      // Should update notification state
      expect(screen.getByText('notifications.marked')).toBeInTheDocument();
    });
  });
});
