import { SUCCESS_MESSAGE_KEYS } from '@/constants';

// دالة للحصول على ترجمة رسالة النجاح
export const getSuccessMessageTranslation = (messageKey: string, t: (key: string) => string): string => {
  const key = SUCCESS_MESSAGE_KEYS[messageKey as keyof typeof SUCCESS_MESSAGE_KEYS];
  if (!key) return messageKey;
  
  return t(`messages.${key}`);
};

// دالة للحصول على جميع رسائل النجاح مترجمة
export const getTranslatedSuccessMessages = (t: (key: string) => string) => {
  return Object.entries(SUCCESS_MESSAGE_KEYS).map(([key, value]) => ({
    key,
    value,
    translation: t(`messages.${value}`)
  }));
};
