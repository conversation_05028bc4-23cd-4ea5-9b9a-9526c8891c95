<?php
/**
 * API endpoint لمزامنة الصفقات مع العقد الذكي
 * Trades Smart Contract Synchronization API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, PUT, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // جلب الصفقات غير المتزامنة مع العقد الذكي
        $syncStatus = $_GET['sync_status'] ?? 'pending';
        $limit = min(100, max(10, intval($_GET['limit'] ?? 50)));
        
        $stmt = $connection->prepare("
            SELECT 
                t.id,
                t.blockchain_trade_id,
                t.seller_id,
                t.buyer_id,
                t.amount,
                t.total_value,
                t.platform_fee,
                t.status,
                t.contract_status,
                t.sync_status,
                t.transaction_hash,
                t.created_at,
                t.last_sync_at,
                o.blockchain_trade_id as offer_blockchain_id,
                us.wallet_address as seller_address,
                ub.wallet_address as buyer_address
            FROM trades t
            LEFT JOIN offers o ON t.offer_id = o.id
            LEFT JOIN users us ON t.seller_id = us.id
            LEFT JOIN users ub ON t.buyer_id = ub.id
            WHERE t.sync_status = ?
            ORDER BY t.created_at ASC
            LIMIT ?
        ");
        
        $stmt->execute([$syncStatus, $limit]);
        $trades = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تنسيق البيانات
        foreach ($trades as &$trade) {
            $trade['amount'] = floatval($trade['amount']);
            $trade['total_value'] = floatval($trade['total_value']);
            $trade['platform_fee'] = floatval($trade['platform_fee']);
        }
        
        echo json_encode([
            'success' => true,
            'data' => $trades,
            'count' => count($trades)
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // ربط صفقة موجودة بالعقد الذكي
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        // التحقق من البيانات المطلوبة
        $requiredFields = ['trade_id', 'blockchain_trade_id', 'transaction_hash'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field])) {
                throw new Exception("الحقل $field مطلوب");
            }
        }
        
        $tradeId = $input['trade_id'];
        $blockchainTradeId = $input['blockchain_trade_id'];
        $transactionHash = $input['transaction_hash'];
        $contractStatus = $input['contract_status'] ?? 'created';
        
        // التحقق من وجود الصفقة
        $stmt = $connection->prepare("
            SELECT id, seller_id, buyer_id, blockchain_trade_id, status 
            FROM trades 
            WHERE id = ?
        ");
        $stmt->execute([$tradeId]);
        $trade = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$trade) {
            throw new Exception('الصفقة غير موجودة');
        }
        
        // التحقق من عدم ربط الصفقة مسبقاً
        if ($trade['blockchain_trade_id']) {
            throw new Exception('الصفقة مربوطة بالعقد الذكي مسبقاً');
        }
        
        // تحديث الصفقة بمعلومات العقد الذكي
        $stmt = $connection->prepare("
            UPDATE trades 
            SET blockchain_trade_id = ?, 
                transaction_hash = ?, 
                contract_status = ?,
                sync_status = 'synced',
                contract_created_at = CURRENT_TIMESTAMP,
                last_sync_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        
        $stmt->execute([$blockchainTradeId, $transactionHash, $contractStatus, $tradeId]);
        
        // تسجيل النشاط
        $stmt = $connection->prepare("
            INSERT INTO activity_logs (
                action, entity_type, entity_id, user_id, data, created_at
            ) VALUES (?, 'trade', ?, ?, ?, CURRENT_TIMESTAMP)
        ");
        
        $activityData = json_encode([
            'blockchain_trade_id' => $blockchainTradeId,
            'transaction_hash' => $transactionHash,
            'contract_status' => $contractStatus
        ]);
        
        $stmt->execute([
            'trade_synced_with_contract',
            $tradeId,
            $trade['seller_id'],
            $activityData
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم ربط الصفقة بالعقد الذكي بنجاح',
            'data' => [
                'trade_id' => $tradeId,
                'blockchain_trade_id' => $blockchainTradeId,
                'transaction_hash' => $transactionHash,
                'contract_status' => $contractStatus
            ]
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // تحديث حالة الصفقة من العقد الذكي
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        // يمكن التحديث بمعرف الصفقة أو معرف العقد الذكي
        if (isset($input['trade_id'])) {
            $whereClause = 'id = ?';
            $whereParam = $input['trade_id'];
        } elseif (isset($input['blockchain_trade_id'])) {
            $whereClause = 'blockchain_trade_id = ?';
            $whereParam = $input['blockchain_trade_id'];
        } else {
            throw new Exception('معرف الصفقة أو معرف العقد الذكي مطلوب');
        }
        
        // جلب الصفقة
        $stmt = $connection->prepare("
            SELECT id, seller_id, buyer_id, blockchain_trade_id, status, contract_status 
            FROM trades 
            WHERE $whereClause
        ");
        $stmt->execute([$whereParam]);
        $trade = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$trade) {
            throw new Exception('الصفقة غير موجودة');
        }
        
        // تحضير حقول التحديث
        $updateFields = [];
        $params = [];
        
        if (isset($input['status'])) {
            $updateFields[] = 'status = ?';
            $params[] = $input['status'];
        }
        
        if (isset($input['contract_status'])) {
            $updateFields[] = 'contract_status = ?';
            $params[] = $input['contract_status'];
        }
        
        // تحديث معلومات المعاملات حسب نوع الحدث
        if (isset($input['event_type'])) {
            switch ($input['event_type']) {
                case 'BuyerJoined':
                    if (isset($input['transaction_hash'])) {
                        $updateFields[] = 'join_transaction_hash = ?';
                        $params[] = $input['transaction_hash'];
                        $updateFields[] = 'contract_joined_at = CURRENT_TIMESTAMP';
                    }
                    break;
                    
                case 'PaymentSent':
                    if (isset($input['transaction_hash'])) {
                        $updateFields[] = 'payment_sent_transaction_hash = ?';
                        $params[] = $input['transaction_hash'];
                        $updateFields[] = 'contract_payment_sent_at = CURRENT_TIMESTAMP';
                    }
                    break;
                    
                case 'TradeCompleted':
                    if (isset($input['transaction_hash'])) {
                        $updateFields[] = 'complete_transaction_hash = ?';
                        $params[] = $input['transaction_hash'];
                        $updateFields[] = 'contract_completed_at = CURRENT_TIMESTAMP';
                        $updateFields[] = 'completed_at = CURRENT_TIMESTAMP';
                    }
                    break;
                    
                case 'TradeCancelled':
                    if (isset($input['transaction_hash'])) {
                        $updateFields[] = 'cancel_transaction_hash = ?';
                        $params[] = $input['transaction_hash'];
                    }
                    break;
            }
        }
        
        if (empty($updateFields)) {
            throw new Exception('لا توجد حقول للتحديث');
        }
        
        // إضافة حقول المزامنة
        $updateFields[] = 'sync_status = ?';
        $updateFields[] = 'last_sync_at = CURRENT_TIMESTAMP';
        $updateFields[] = 'updated_at = CURRENT_TIMESTAMP';
        $params[] = 'synced';
        
        $params[] = $trade['id'];
        
        // تنفيذ التحديث
        $stmt = $connection->prepare("
            UPDATE trades 
            SET " . implode(', ', $updateFields) . "
            WHERE id = ?
        ");
        
        $stmt->execute($params);
        
        // تحديث إحصائيات المستخدمين إذا تمت الصفقة
        if (isset($input['status']) && $input['status'] === 'completed' && $trade['status'] !== 'completed') {
            updateUserStats($connection, $trade['id']);
        }
        
        // تسجيل النشاط
        $stmt = $connection->prepare("
            INSERT INTO activity_logs (
                action, entity_type, entity_id, user_id, data, created_at
            ) VALUES (?, 'trade', ?, ?, ?, CURRENT_TIMESTAMP)
        ");
        
        $activityData = json_encode([
            'old_status' => $trade['status'],
            'new_status' => $input['status'] ?? $trade['status'],
            'old_contract_status' => $trade['contract_status'],
            'new_contract_status' => $input['contract_status'] ?? $trade['contract_status'],
            'event_type' => $input['event_type'] ?? null,
            'transaction_hash' => $input['transaction_hash'] ?? null,
            'source' => 'smart_contract'
        ]);
        
        $stmt->execute([
            'trade_status_updated_from_contract',
            $trade['id'],
            $trade['seller_id'],
            $activityData
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث حالة الصفقة من العقد الذكي',
            'data' => [
                'trade_id' => $trade['id'],
                'blockchain_trade_id' => $trade['blockchain_trade_id'],
                'old_status' => $trade['status'],
                'new_status' => $input['status'] ?? $trade['status'],
                'event_type' => $input['event_type'] ?? null
            ]
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    error_log('Database error in trades/sync-contract.php: ' . $e->getMessage());
}

/**
 * تحديث إحصائيات المستخدمين
 */
function updateUserStats($connection, $tradeId) {
    try {
        // جلب معلومات الصفقة
        $stmt = $connection->prepare("
            SELECT seller_id, buyer_id, total_value 
            FROM trades 
            WHERE id = ?
        ");
        $stmt->execute([$tradeId]);
        $trade = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($trade) {
            // تحديث إحصائيات البائع
            $stmt = $connection->prepare("
                UPDATE users 
                SET completed_trades = completed_trades + 1,
                    total_volume = total_volume + ?
                WHERE id = ?
            ");
            $stmt->execute([$trade['total_value'], $trade['seller_id']]);
            
            // تحديث إحصائيات المشتري
            $stmt = $connection->prepare("
                UPDATE users 
                SET completed_trades = completed_trades + 1,
                    total_volume = total_volume + ?
                WHERE id = ?
            ");
            $stmt->execute([$trade['total_value'], $trade['buyer_id']]);
        }
    } catch (Exception $e) {
        error_log('Error updating user stats: ' . $e->getMessage());
    }
}
?>
