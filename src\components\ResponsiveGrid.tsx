'use client';

import { ReactNode } from 'react';

interface ResponsiveGridProps {
  children: ReactNode;
  cols?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  autoFit?: boolean;
  minItemWidth?: string;
}

export default function ResponsiveGrid({
  children,
  cols = { default: 1, sm: 2, md: 3, lg: 4 },
  gap = 'md',
  className = '',
  autoFit = false,
  minItemWidth = '250px'
}: ResponsiveGridProps) {
  const gapClasses = {
    none: 'gap-0',
    sm: 'gap-2 sm:gap-3',
    md: 'gap-4 sm:gap-6',
    lg: 'gap-6 sm:gap-8',
    xl: 'gap-8 sm:gap-10'
  };

  const getGridCols = () => {
    if (autoFit) {
      return {
        gridTemplateColumns: `repeat(auto-fit, minmax(${minItemWidth}, 1fr))`
      };
    }

    const colClasses = [];
    if (cols.default) colClasses.push(`grid-cols-${cols.default}`);
    if (cols.sm) colClasses.push(`sm:grid-cols-${cols.sm}`);
    if (cols.md) colClasses.push(`md:grid-cols-${cols.md}`);
    if (cols.lg) colClasses.push(`lg:grid-cols-${cols.lg}`);
    if (cols.xl) colClasses.push(`xl:grid-cols-${cols.xl}`);
    
    return { className: colClasses.join(' ') };
  };

  const gridProps = getGridCols();

  return (
    <div
      className={`grid ${gapClasses[gap]} ${gridProps.className || ''} ${className}`}
      style={gridProps.gridTemplateColumns ? { gridTemplateColumns: gridProps.gridTemplateColumns } : undefined}
    >
      {children}
    </div>
  );
}

// مكون شبكة الإحصائيات
interface StatsGridProps {
  stats: Array<{
    title: string;
    value: string | number;
    change?: {
      value: number;
      type: 'increase' | 'decrease';
    };
    icon?: ReactNode;
    color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple';
  }>;
  loading?: boolean;
  className?: string;
}

export function StatsGrid({ stats, loading = false, className = '' }: StatsGridProps) {
  const colorClasses = {
    blue: 'from-blue-500 to-blue-600',
    green: 'from-green-500 to-green-600',
    yellow: 'from-yellow-500 to-yellow-600',
    red: 'from-red-500 to-red-600',
    purple: 'from-purple-500 to-purple-600'
  };

  if (loading) {
    return (
      <ResponsiveGrid
        cols={{ default: 1, sm: 2, lg: 4 }}
        gap="md"
        className={className}
      >
        {[...Array(4)].map((_, index) => (
          <div key={index} className="bg-gray-200 dark:bg-gray-700 rounded-xl p-6 animate-pulse">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-24"></div>
                <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-16"></div>
              </div>
              <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
            </div>
          </div>
        ))}
      </ResponsiveGrid>
    );
  }

  return (
    <ResponsiveGrid
      cols={{ default: 1, sm: 2, lg: 4 }}
      gap="md"
      className={className}
    >
      {stats.map((stat, index) => (
        <div
          key={index}
          className={`bg-gradient-to-r ${colorClasses[stat.color || 'blue']} rounded-xl p-6 text-white`}
        >
          <div className="flex items-center justify-between">
            <div className="min-w-0 flex-1">
              <p className="text-sm font-medium opacity-90 truncate">
                {stat.title}
              </p>
              <p className="text-2xl sm:text-3xl font-bold mt-1 truncate">
                {typeof stat.value === 'number' ? stat.value.toLocaleString() : stat.value}
              </p>
              
              {stat.change && (
                <div className="flex items-center mt-2">
                  <span className={`text-xs font-medium ${
                    stat.change.type === 'increase' ? 'text-green-200' : 'text-red-200'
                  }`}>
                    {stat.change.type === 'increase' ? '+' : '-'}{Math.abs(stat.change.value)}%
                  </span>
                </div>
              )}
            </div>
            
            {stat.icon && (
              <div className="w-8 h-8 sm:w-10 sm:h-10 opacity-80 flex-shrink-0">
                {stat.icon}
              </div>
            )}
          </div>
        </div>
      ))}
    </ResponsiveGrid>
  );
}

// مكون شبكة البطاقات
interface CardGridProps {
  children: ReactNode;
  minCardWidth?: string;
  maxCols?: number;
  className?: string;
  gap?: 'sm' | 'md' | 'lg';
}

export function CardGrid({
  children,
  minCardWidth = '300px',
  maxCols = 4,
  className = '',
  gap = 'md'
}: CardGridProps) {
  return (
    <ResponsiveGrid
      autoFit
      minItemWidth={minCardWidth}
      gap={gap}
      className={`max-cols-${maxCols} ${className}`}
    >
      {children}
    </ResponsiveGrid>
  );
}

// مكون شبكة المنتجات/العروض
interface ProductGridProps {
  products: Array<{
    id: string;
    title: string;
    price: string;
    image?: string;
    badge?: string;
    onClick?: () => void;
  }>;
  loading?: boolean;
  emptyMessage?: string;
  className?: string;
}

export function ProductGrid({
  products,
  loading = false,
  emptyMessage = 'لا توجد عناصر',
  className = ''
}: ProductGridProps) {
  if (loading) {
    return (
      <ResponsiveGrid
        cols={{ default: 1, sm: 2, md: 3, lg: 4 }}
        gap="md"
        className={className}
      >
        {[...Array(8)].map((_, index) => (
          <div key={index} className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 animate-pulse">
            <div className="aspect-square bg-gray-200 dark:bg-gray-700 rounded-lg mb-4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </ResponsiveGrid>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 dark:text-gray-400">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <ResponsiveGrid
      cols={{ default: 1, sm: 2, md: 3, lg: 4 }}
      gap="md"
      className={className}
    >
      {products.map((product) => (
        <div
          key={product.id}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-all duration-200 cursor-pointer group"
          onClick={product.onClick}
        >
          <div className="relative">
            {product.image ? (
              <img
                src={product.image}
                alt={product.title}
                className="w-full aspect-square object-cover rounded-lg"
              />
            ) : (
              <div className="w-full aspect-square bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                <span className="text-gray-400 text-4xl">📦</span>
              </div>
            )}
            
            {product.badge && (
              <span className="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                {product.badge}
              </span>
            )}
          </div>
          
          <div className="mt-4">
            <h3 className="font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors truncate">
              {product.title}
            </h3>
            <p className="text-lg font-bold text-blue-600 dark:text-blue-400 mt-1">
              {product.price}
            </p>
          </div>
        </div>
      ))}
    </ResponsiveGrid>
  );
}
