@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    نقل node_modules إلى الهارد D
echo    Moving node_modules to D Drive
echo ========================================
echo.

echo جاري فحص المجلدات...
echo Checking folders...
echo.

REM فحص وجود node_modules
if not exist "node_modules" (
    echo ❌ مجلد node_modules غير موجود
    echo ❌ node_modules folder not found
    pause
    exit /b 1
)

REM عرض حجم المجلد الحالي
echo 📊 حجم node_modules الحالي:
echo 📊 Current node_modules size:
for /f "tokens=3" %%a in ('dir node_modules /s /-c ^| find "File(s)"') do echo %%a bytes
echo.

REM إنشاء مجلد التخزين في D
echo 📁 إنشاء مجلد التخزين في D:\node_modules_storage...
echo 📁 Creating storage folder in D:\node_modules_storage...
if not exist "D:\node_modules_storage" mkdir "D:\node_modules_storage"

REM فحص وجود مجلد سابق
if exist "D:\node_modules_storage\ikaros-p2p-node_modules" (
    echo ⚠️ يوجد مجلد سابق، جاري الحذف...
    echo ⚠️ Previous folder exists, deleting...
    rmdir /s /q "D:\node_modules_storage\ikaros-p2p-node_modules"
)

echo.
echo 🚀 جاري نقل node_modules إلى الهارد D...
echo 🚀 Moving node_modules to D drive...
echo هذا قد يستغرق بضع دقائق...
echo This may take a few minutes...
echo.

REM نقل المجلد
move "node_modules" "D:\node_modules_storage\ikaros-p2p-node_modules"

if %errorlevel% equ 0 (
    echo ✅ تم نقل المجلد بنجاح!
    echo ✅ Folder moved successfully!
    echo.
    
    echo 🔗 جاري إنشاء رابط رمزي...
    echo 🔗 Creating symbolic link...
    
    REM إنشاء رابط رمزي
    mklink /D "node_modules" "D:\node_modules_storage\ikaros-p2p-node_modules"
    
    if %errorlevel% equ 0 (
        echo ✅ تم إنشاء الرابط الرمزي بنجاح!
        echo ✅ Symbolic link created successfully!
        echo.
        
        echo 📊 النتائج:
        echo 📊 Results:
        echo ✓ المجلد منقول إلى: D:\node_modules_storage\ikaros-p2p-node_modules
        echo ✓ Folder moved to: D:\node_modules_storage\ikaros-p2p-node_modules
        echo ✓ رابط رمزي تم إنشاؤه في المشروع
        echo ✓ Symbolic link created in project
        echo ✓ المشروع سيعمل بشكل طبيعي
        echo ✓ Project will work normally
        echo.
        
        echo 💾 المساحة المحررة: ~539 ميجابايت
        echo 💾 Space freed: ~539 MB
        echo.
        
    ) else (
        echo ❌ فشل في إنشاء الرابط الرمزي
        echo ❌ Failed to create symbolic link
        echo يمكنك استخدام manage-node-modules.bat لإنشاء الرابط
        echo You can use manage-node-modules.bat to create the link
    )
    
) else (
    echo ❌ فشل في نقل المجلد
    echo ❌ Failed to move folder
    echo تأكد من وجود مساحة كافية في الهارد D
    echo Make sure there's enough space on D drive
)

echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause >nul
