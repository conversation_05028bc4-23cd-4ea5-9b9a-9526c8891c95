<?php
/**
 * مساعد قاعدة البيانات المحسن للعقود الذكية
 * Enhanced Database Helper for Smart Contracts
 */

require_once __DIR__ . '/../config/database.php';

class EnhancedDatabaseHelper {
    
    private static $instance = null;
    private $connection = null;
    
    private function __construct() {
        $this->connection = $this->getConnection();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function getConnection() {
        try {
            $database = new Database();
            return $database->getConnection();
        } catch (Exception $e) {
            error_log("Enhanced Database connection error: " . $e->getMessage());
            throw new Exception("فشل الاتصال بقاعدة البيانات المحسنة");
        }
    }
    
    /**
     * جلب الشبكات المدعومة
     * Get supported networks
     */
    public function getSupportedNetworks($activeOnly = true) {
        try {
            $whereClause = $activeOnly ? "WHERE is_active = 1" : "";
            
            $stmt = $this->connection->prepare("
                SELECT id, network_name, network_symbol, chain_id, rpc_url, 
                       explorer_url, is_testnet, is_active, gas_price_gwei,
                       block_time_seconds, confirmation_blocks, created_at
                FROM supported_networks 
                $whereClause
                ORDER BY is_testnet ASC, network_name ASC
            ");
            $stmt->execute();
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error getting supported networks: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * جلب العملات المدعومة لشبكة معينة
     * Get supported tokens for a network
     */
    public function getSupportedTokens($networkId = null, $activeOnly = true) {
        try {
            $whereConditions = [];
            $params = [];
            
            if ($networkId !== null) {
                $whereConditions[] = "st.network_id = ?";
                $params[] = $networkId;
            }
            
            if ($activeOnly) {
                $whereConditions[] = "st.is_active = 1";
                $whereConditions[] = "sn.is_active = 1";
            }
            
            $whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";
            
            $stmt = $this->connection->prepare("
                SELECT st.id, st.network_id, st.token_address, st.token_symbol, 
                       st.token_name, st.decimals, st.is_stablecoin, st.is_active,
                       st.icon_url, st.coingecko_id, st.min_trade_amount, 
                       st.max_trade_amount, st.daily_volume_limit, st.platform_fee_rate,
                       sn.network_name, sn.network_symbol as network_symbol, sn.chain_id
                FROM supported_tokens st
                JOIN supported_networks sn ON st.network_id = sn.id
                $whereClause
                ORDER BY sn.network_name ASC, st.token_symbol ASC
            ");
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error getting supported tokens: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * جلب عناوين العقود المحسنة
     * Get enhanced contract addresses
     */
    public function getEnhancedContracts($networkId = null, $contractType = null, $activeOnly = true) {
        try {
            $whereConditions = [];
            $params = [];
            
            if ($networkId !== null) {
                $whereConditions[] = "ec.network_id = ?";
                $params[] = $networkId;
            }
            
            if ($contractType !== null) {
                $whereConditions[] = "ec.contract_type = ?";
                $params[] = $contractType;
            }
            
            if ($activeOnly) {
                $whereConditions[] = "ec.is_active = 1";
                $whereConditions[] = "sn.is_active = 1";
            }
            
            $whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";
            
            $stmt = $this->connection->prepare("
                SELECT ec.id, ec.network_id, ec.contract_type, ec.contract_address,
                       ec.contract_version, ec.deployment_block, ec.deployment_tx_hash,
                       ec.is_active, ec.abi_hash, ec.created_at,
                       sn.network_name, sn.chain_id, sn.network_symbol
                FROM enhanced_contracts ec
                JOIN supported_networks sn ON ec.network_id = sn.id
                $whereClause
                ORDER BY sn.network_name ASC, ec.contract_type ASC
            ");
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error getting enhanced contracts: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * إضافة شبكة جديدة
     * Add new network
     */
    public function addNetwork($data) {
        try {
            $stmt = $this->connection->prepare("
                INSERT INTO supported_networks 
                (network_name, network_symbol, chain_id, rpc_url, explorer_url, 
                 is_testnet, is_active, gas_price_gwei, block_time_seconds, confirmation_blocks)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['network_name'],
                $data['network_symbol'],
                $data['chain_id'],
                $data['rpc_url'],
                $data['explorer_url'],
                $data['is_testnet'] ?? false,
                $data['is_active'] ?? true,
                $data['gas_price_gwei'] ?? 5.0,
                $data['block_time_seconds'] ?? 3,
                $data['confirmation_blocks'] ?? 12
            ]);
            
            return $this->connection->lastInsertId();
            
        } catch (Exception $e) {
            error_log("Error adding network: " . $e->getMessage());
            throw new Exception("فشل في إضافة الشبكة");
        }
    }
    
    /**
     * إضافة عملة جديدة
     * Add new token
     */
    public function addToken($data) {
        try {
            $stmt = $this->connection->prepare("
                INSERT INTO supported_tokens 
                (network_id, token_address, token_symbol, token_name, decimals,
                 is_stablecoin, is_active, icon_url, coingecko_id, min_trade_amount,
                 max_trade_amount, daily_volume_limit, platform_fee_rate)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $data['network_id'],
                $data['token_address'],
                $data['token_symbol'],
                $data['token_name'],
                $data['decimals'] ?? 18,
                $data['is_stablecoin'] ?? false,
                $data['is_active'] ?? true,
                $data['icon_url'] ?? null,
                $data['coingecko_id'] ?? null,
                $data['min_trade_amount'] ?? 1.0,
                $data['max_trade_amount'] ?? 1000000.0,
                $data['daily_volume_limit'] ?? 100000.0,
                $data['platform_fee_rate'] ?? 0.0050
            ]);
            
            return $this->connection->lastInsertId();
            
        } catch (Exception $e) {
            error_log("Error adding token: " . $e->getMessage());
            throw new Exception("فشل في إضافة العملة");
        }
    }
    
    /**
     * تحديث حالة الشبكة
     * Update network status
     */
    public function updateNetworkStatus($networkId, $isActive) {
        try {
            $stmt = $this->connection->prepare("
                UPDATE supported_networks 
                SET is_active = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");
            
            return $stmt->execute([$isActive, $networkId]);
            
        } catch (Exception $e) {
            error_log("Error updating network status: " . $e->getMessage());
            throw new Exception("فشل في تحديث حالة الشبكة");
        }
    }
    
    /**
     * تحديث حالة العملة
     * Update token status
     */
    public function updateTokenStatus($tokenId, $isActive) {
        try {
            $stmt = $this->connection->prepare("
                UPDATE supported_tokens 
                SET is_active = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");
            
            return $stmt->execute([$isActive, $tokenId]);
            
        } catch (Exception $e) {
            error_log("Error updating token status: " . $e->getMessage());
            throw new Exception("فشل في تحديث حالة العملة");
        }
    }
    
    /**
     * التحقق من وجود الجداول المحسنة
     * Check if enhanced tables exist
     */
    public function checkEnhancedTables() {
        try {
            $tables = [
                'supported_networks',
                'supported_tokens', 
                'enhanced_contracts',
                'enhanced_contract_events',
                'enhanced_user_reputation'
            ];
            
            $existingTables = [];
            
            foreach ($tables as $table) {
                $stmt = $this->connection->prepare("
                    SELECT COUNT(*) as count 
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE() AND table_name = ?
                ");
                $stmt->execute([$table]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $existingTables[$table] = $result['count'] > 0;
            }
            
            return $existingTables;
            
        } catch (Exception $e) {
            error_log("Error checking enhanced tables: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * إغلاق الاتصال
     * Close connection
     */
    public function closeConnection() {
        $this->connection = null;
    }
}
?>
