// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";

/**
 * @title OracleManager - Enhanced Price Oracle System
 * @dev Manages price feeds and external data for P2P trading platform
 * <AUTHOR> P2P Team
 * @notice This contract handles price data and oracle functionality
 */
contract OracleManager is AccessControl, ReentrancyGuard, Pausable {
    
    // Role definitions
    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant ORACLE_ROLE = keccak256("ORACLE_ROLE");
    bytes32 public constant PRICE_UPDATER_ROLE = keccak256("PRICE_UPDATER_ROLE");

    // Price data structure
    struct PriceData {
        uint256 price;          // Price in USD with 8 decimals
        uint256 timestamp;      // Last update timestamp
        uint256 confidence;     // Confidence level (0-10000, representing 0-100%)
        address updater;        // Address that updated the price
        bool isActive;          // Whether this price feed is active
        uint256 deviation;      // Maximum allowed deviation percentage
        uint256 heartbeat;      // Maximum time between updates
    }

    // Token price feed structure
    struct TokenPriceFeed {
        mapping(string => PriceData) currencyPrices; // currency => PriceData
        string[] supportedCurrencies;
        bool isSupported;
        uint256 lastGlobalUpdate;
    }

    // Exchange rate structure for fiat currencies
    struct ExchangeRate {
        uint256 rate;           // Exchange rate with 8 decimals
        uint256 timestamp;      // Last update timestamp
        bool isActive;          // Whether this rate is active
    }

    // State variables
    mapping(address => TokenPriceFeed) public tokenPriceFeeds;
    mapping(string => ExchangeRate) public fiatExchangeRates; // e.g., "USD/SAR"
    mapping(address => bool) public supportedTokens;
    
    address[] public tokenList;
    string[] public supportedFiatCurrencies;
    mapping(string => bool) public isFiatSupported;
    
    // Oracle parameters
    uint256 public constant PRICE_DECIMALS = 8;
    uint256 public constant MAX_PRICE_AGE = 1 hours;
    uint256 public constant DEFAULT_CONFIDENCE = 9500; // 95%
    uint256 public constant MAX_DEVIATION = 1000; // 10%
    
    string public baseCurrency = "USD";

    // Events
    event PriceUpdated(
        address indexed token,
        string indexed currency,
        uint256 oldPrice,
        uint256 newPrice,
        uint256 confidence,
        address updater
    );

    event ExchangeRateUpdated(
        string indexed currencyPair,
        uint256 oldRate,
        uint256 newRate,
        address updater
    );

    event TokenAdded(address indexed token, string[] currencies);
    event TokenRemoved(address indexed token);
    event CurrencyAdded(string currency);
    event CurrencyRemoved(string currency);

    event PriceDeviation(
        address indexed token,
        string indexed currency,
        uint256 oldPrice,
        uint256 newPrice,
        uint256 deviation
    );

    // Modifiers
    modifier tokenSupported(address token) {
        require(supportedTokens[token], "Token not supported");
        _;
    }

    modifier currencySupported(string memory currency) {
        require(isFiatSupported[currency], "Currency not supported");
        _;
    }

    modifier validPrice(uint256 price) {
        require(price > 0, "Invalid price");
        _;
    }

    modifier validConfidence(uint256 confidence) {
        require(confidence <= 10000, "Invalid confidence level");
        _;
    }

    /**
     * @dev Constructor
     */
    constructor() {
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _grantRole(ADMIN_ROLE, msg.sender);
        _grantRole(ORACLE_ROLE, msg.sender);
        _grantRole(PRICE_UPDATER_ROLE, msg.sender);
        
        // Add default fiat currencies
        _addFiatCurrency("USD");
        _addFiatCurrency("SAR");
        _addFiatCurrency("AED");
        _addFiatCurrency("EUR");
    }

    /**
     * @dev Add support for a new token
     * @param token Token contract address
     * @param currencies Array of supported fiat currencies
     */
    function addToken(address token, string[] memory currencies) 
        external 
        onlyRole(ADMIN_ROLE) 
    {
        require(token != address(0), "Invalid token address");
        require(!supportedTokens[token], "Token already supported");
        require(currencies.length > 0, "No currencies provided");

        supportedTokens[token] = true;
        tokenList.push(token);
        
        TokenPriceFeed storage feed = tokenPriceFeeds[token];
        feed.isSupported = true;
        feed.lastGlobalUpdate = block.timestamp;
        
        for (uint256 i = 0; i < currencies.length; i++) {
            require(isFiatSupported[currencies[i]], "Currency not supported");
            
            feed.supportedCurrencies.push(currencies[i]);
            feed.currencyPrices[currencies[i]] = PriceData({
                price: 0,
                timestamp: 0,
                confidence: DEFAULT_CONFIDENCE,
                updater: address(0),
                isActive: true,
                deviation: MAX_DEVIATION,
                heartbeat: MAX_PRICE_AGE
            });
        }

        emit TokenAdded(token, currencies);
    }

    /**
     * @dev Remove support for a token
     * @param token Token contract address
     */
    function removeToken(address token) external onlyRole(ADMIN_ROLE) tokenSupported(token) {
        supportedTokens[token] = false;
        tokenPriceFeeds[token].isSupported = false;
        
        // Remove from token list
        for (uint256 i = 0; i < tokenList.length; i++) {
            if (tokenList[i] == token) {
                tokenList[i] = tokenList[tokenList.length - 1];
                tokenList.pop();
                break;
            }
        }

        emit TokenRemoved(token);
    }

    /**
     * @dev Add support for a new fiat currency
     * @param currency Currency code (e.g., "SAR", "AED")
     */
    function addFiatCurrency(string memory currency) external onlyRole(ADMIN_ROLE) {
        _addFiatCurrency(currency);
    }

    /**
     * @dev Internal function to add fiat currency
     * @param currency Currency code
     */
    function _addFiatCurrency(string memory currency) internal {
        require(!isFiatSupported[currency], "Currency already supported");
        require(bytes(currency).length > 0, "Invalid currency");

        isFiatSupported[currency] = true;
        supportedFiatCurrencies.push(currency);

        emit CurrencyAdded(currency);
    }

    /**
     * @dev Remove support for a fiat currency
     * @param currency Currency code
     */
    function removeFiatCurrency(string memory currency) 
        external 
        onlyRole(ADMIN_ROLE) 
        currencySupported(currency) 
    {
        require(keccak256(bytes(currency)) != keccak256(bytes(baseCurrency)), "Cannot remove base currency");
        
        isFiatSupported[currency] = false;
        
        // Remove from supported currencies array
        for (uint256 i = 0; i < supportedFiatCurrencies.length; i++) {
            if (keccak256(bytes(supportedFiatCurrencies[i])) == keccak256(bytes(currency))) {
                supportedFiatCurrencies[i] = supportedFiatCurrencies[supportedFiatCurrencies.length - 1];
                supportedFiatCurrencies.pop();
                break;
            }
        }

        emit CurrencyRemoved(currency);
    }

    /**
     * @dev Update token price for a specific currency
     * @param token Token contract address
     * @param currency Fiat currency code
     * @param price New price with 8 decimals
     * @param confidence Confidence level (0-10000)
     */
    function updatePrice(
        address token,
        string memory currency,
        uint256 price,
        uint256 confidence
    ) external 
        onlyRole(PRICE_UPDATER_ROLE) 
        tokenSupported(token) 
        currencySupported(currency)
        validPrice(price)
        validConfidence(confidence)
    {
        TokenPriceFeed storage feed = tokenPriceFeeds[token];
        PriceData storage priceData = feed.currencyPrices[currency];
        
        require(priceData.isActive, "Price feed not active");
        
        uint256 oldPrice = priceData.price;
        
        // Check for significant price deviation
        if (oldPrice > 0) {
            uint256 deviation = _calculateDeviation(oldPrice, price);
            if (deviation > priceData.deviation) {
                emit PriceDeviation(token, currency, oldPrice, price, deviation);
                // In production, you might want to require additional confirmations
            }
        }
        
        priceData.price = price;
        priceData.timestamp = block.timestamp;
        priceData.confidence = confidence;
        priceData.updater = msg.sender;
        
        feed.lastGlobalUpdate = block.timestamp;

        emit PriceUpdated(token, currency, oldPrice, price, confidence, msg.sender);
    }

    /**
     * @dev Update exchange rate between fiat currencies
     * @param currencyPair Currency pair (e.g., "USD/SAR")
     * @param rate Exchange rate with 8 decimals
     */
    function updateExchangeRate(string memory currencyPair, uint256 rate) 
        external 
        onlyRole(PRICE_UPDATER_ROLE) 
        validPrice(rate)
    {
        uint256 oldRate = fiatExchangeRates[currencyPair].rate;
        
        fiatExchangeRates[currencyPair] = ExchangeRate({
            rate: rate,
            timestamp: block.timestamp,
            isActive: true
        });

        emit ExchangeRateUpdated(currencyPair, oldRate, rate, msg.sender);
    }

    /**
     * @dev Get current token price in specified currency
     * @param token Token contract address
     * @param currency Fiat currency code
     * @return price Current price
     * @return timestamp Last update timestamp
     * @return confidence Confidence level
     */
    function getTokenPrice(address token, string memory currency) 
        external 
        view 
        tokenSupported(token) 
        currencySupported(currency)
        returns (uint256 price, uint256 timestamp, uint256 confidence) 
    {
        PriceData memory priceData = tokenPriceFeeds[token].currencyPrices[currency];
        require(priceData.isActive, "Price feed not active");
        
        return (priceData.price, priceData.timestamp, priceData.confidence);
    }

    /**
     * @dev Get exchange rate between currencies
     * @param currencyPair Currency pair (e.g., "USD/SAR")
     * @return rate Exchange rate
     * @return timestamp Last update timestamp
     */
    function getExchangeRate(string memory currencyPair) 
        external 
        view 
        returns (uint256 rate, uint256 timestamp) 
    {
        ExchangeRate memory exchangeRate = fiatExchangeRates[currencyPair];
        require(exchangeRate.isActive, "Exchange rate not available");
        
        return (exchangeRate.rate, exchangeRate.timestamp);
    }

    /**
     * @dev Check if price data is stale
     * @param token Token contract address
     * @param currency Fiat currency code
     * @return bool True if price is stale
     */
    function isPriceStale(address token, string memory currency) 
        external 
        view 
        tokenSupported(token) 
        currencySupported(currency)
        returns (bool) 
    {
        PriceData memory priceData = tokenPriceFeeds[token].currencyPrices[currency];
        return block.timestamp - priceData.timestamp > priceData.heartbeat;
    }

    /**
     * @dev Get all supported tokens
     * @return Array of token addresses
     */
    function getSupportedTokens() external view returns (address[] memory) {
        return tokenList;
    }

    /**
     * @dev Get supported currencies for a token
     * @param token Token contract address
     * @return Array of currency codes
     */
    function getTokenCurrencies(address token) 
        external 
        view 
        tokenSupported(token) 
        returns (string[] memory) 
    {
        return tokenPriceFeeds[token].supportedCurrencies;
    }

    /**
     * @dev Get all supported fiat currencies
     * @return Array of currency codes
     */
    function getSupportedCurrencies() external view returns (string[] memory) {
        return supportedFiatCurrencies;
    }

    /**
     * @dev Convert price between currencies
     * @param amount Amount to convert
     * @param fromCurrency Source currency
     * @param toCurrency Target currency
     * @return convertedAmount Converted amount
     */
    function convertCurrency(
        uint256 amount,
        string memory fromCurrency,
        string memory toCurrency
    ) external view returns (uint256 convertedAmount) {
        if (keccak256(bytes(fromCurrency)) == keccak256(bytes(toCurrency))) {
            return amount;
        }
        
        string memory currencyPair = string(abi.encodePacked(fromCurrency, "/", toCurrency));
        ExchangeRate memory rate = fiatExchangeRates[currencyPair];
        
        if (rate.isActive && rate.rate > 0) {
            return (amount * rate.rate) / (10 ** PRICE_DECIMALS);
        }
        
        // Try reverse pair
        currencyPair = string(abi.encodePacked(toCurrency, "/", fromCurrency));
        rate = fiatExchangeRates[currencyPair];
        
        if (rate.isActive && rate.rate > 0) {
            return (amount * (10 ** PRICE_DECIMALS)) / rate.rate;
        }
        
        revert("Exchange rate not available");
    }

    /**
     * @dev Calculate price deviation percentage
     * @param oldPrice Previous price
     * @param newPrice New price
     * @return deviation Deviation percentage in basis points
     */
    function _calculateDeviation(uint256 oldPrice, uint256 newPrice) 
        internal 
        pure 
        returns (uint256 deviation) 
    {
        if (oldPrice == 0) return 0;
        
        uint256 diff = newPrice > oldPrice ? newPrice - oldPrice : oldPrice - newPrice;
        return (diff * 10000) / oldPrice;
    }

    /**
     * @dev Set price feed parameters
     * @param token Token contract address
     * @param currency Currency code
     * @param deviation Maximum allowed deviation
     * @param heartbeat Maximum time between updates
     */
    function setPriceFeedParams(
        address token,
        string memory currency,
        uint256 deviation,
        uint256 heartbeat
    ) external onlyRole(ADMIN_ROLE) tokenSupported(token) currencySupported(currency) {
        require(deviation <= 5000, "Deviation too high"); // Max 50%
        require(heartbeat >= 300, "Heartbeat too short"); // Min 5 minutes
        
        PriceData storage priceData = tokenPriceFeeds[token].currencyPrices[currency];
        priceData.deviation = deviation;
        priceData.heartbeat = heartbeat;
    }

    /**
     * @dev Emergency pause price feed
     * @param token Token contract address
     * @param currency Currency code
     */
    function pausePriceFeed(address token, string memory currency) 
        external 
        onlyRole(ADMIN_ROLE) 
        tokenSupported(token) 
        currencySupported(currency) 
    {
        tokenPriceFeeds[token].currencyPrices[currency].isActive = false;
    }

    /**
     * @dev Resume price feed
     * @param token Token contract address
     * @param currency Currency code
     */
    function resumePriceFeed(address token, string memory currency) 
        external 
        onlyRole(ADMIN_ROLE) 
        tokenSupported(token) 
        currencySupported(currency) 
    {
        tokenPriceFeeds[token].currencyPrices[currency].isActive = true;
    }

    // Admin functions
    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
    }

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
    }
}
