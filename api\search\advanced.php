<?php
/**
 * API endpoint للبحث المتقدم
 * Advanced Search API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";
require_once __DIR__ . "/../middleware/auth.php";

// التحقق من طريقة الطلب
validateRequestMethod(['GET', 'POST']);

/**
 * بناء استعلام البحث المتقدم
 */
function buildAdvancedSearchQuery($searchParams, $userRole = 'user') {
    $baseQuery = "
        SELECT DISTINCT 
            'offer' as result_type,
            o.id as result_id,
            o.title as title,
            o.description as description,
            o.amount as amount,
            o.price as price,
            o.currency as currency,
            o.payment_method as payment_method,
            o.offer_type as offer_type,
            o.status as status,
            o.created_at as created_at,
            u.username as username,
            u.rating as user_rating,
            NULL as trade_id,
            NULL as message_content
        FROM offers o
        JOIN users u ON o.user_id = u.id
        WHERE o.status = 'active'
    ";
    
    $conditions = [];
    $params = [];
    
    // البحث النصي
    if (!empty($searchParams['query'])) {
        $conditions[] = "(o.title LIKE ? OR o.description LIKE ?)";
        $searchTerm = '%' . $searchParams['query'] . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    // نوع العرض
    if (!empty($searchParams['offerType'])) {
        $conditions[] = "o.offer_type = ?";
        $params[] = $searchParams['offerType'];
    }
    
    // العملة
    if (!empty($searchParams['currency'])) {
        $conditions[] = "o.currency = ?";
        $params[] = $searchParams['currency'];
    }
    
    // طريقة الدفع
    if (!empty($searchParams['paymentMethod'])) {
        $conditions[] = "o.payment_method = ?";
        $params[] = $searchParams['paymentMethod'];
    }
    
    // نطاق المبلغ
    if (!empty($searchParams['minAmount'])) {
        $conditions[] = "o.amount >= ?";
        $params[] = $searchParams['minAmount'];
    }
    
    if (!empty($searchParams['maxAmount'])) {
        $conditions[] = "o.amount <= ?";
        $params[] = $searchParams['maxAmount'];
    }
    
    // نطاق السعر
    if (!empty($searchParams['minPrice'])) {
        $conditions[] = "o.price >= ?";
        $params[] = $searchParams['minPrice'];
    }
    
    if (!empty($searchParams['maxPrice'])) {
        $conditions[] = "o.price <= ?";
        $params[] = $searchParams['maxPrice'];
    }
    
    // تقييم المستخدم
    if (!empty($searchParams['minRating'])) {
        $conditions[] = "u.rating >= ?";
        $params[] = $searchParams['minRating'];
    }
    
    // نطاق التاريخ
    if (!empty($searchParams['dateFrom'])) {
        $conditions[] = "o.created_at >= ?";
        $params[] = $searchParams['dateFrom'];
    }
    
    if (!empty($searchParams['dateTo'])) {
        $conditions[] = "o.created_at <= ?";
        $params[] = $searchParams['dateTo'];
    }
    
    // إضافة الشروط للاستعلام
    if (!empty($conditions)) {
        $baseQuery .= " AND " . implode(" AND ", $conditions);
    }
    
    // البحث في الصفقات إذا كان المستخدم مخولاً
    if ($userRole === 'admin' && !empty($searchParams['includeTrades'])) {
        $tradeQuery = "
            UNION ALL
            SELECT DISTINCT 
                'trade' as result_type,
                t.id as result_id,
                CONCAT('صفقة #', t.id) as title,
                t.notes as description,
                t.amount as amount,
                t.price as price,
                t.currency as currency,
                t.payment_method as payment_method,
                'trade' as offer_type,
                t.status as status,
                t.created_at as created_at,
                u.username as username,
                u.rating as user_rating,
                t.id as trade_id,
                NULL as message_content
            FROM trades t
            JOIN users u ON (t.buyer_id = u.id OR t.seller_id = u.id)
            WHERE 1=1
        ";
        
        // إضافة شروط البحث للصفقات
        if (!empty($searchParams['query'])) {
            $tradeQuery .= " AND t.notes LIKE ?";
        }
        
        $baseQuery .= $tradeQuery;
    }
    
    // البحث في الرسائل إذا كان مطلوباً
    if ($userRole === 'admin' && !empty($searchParams['includeMessages'])) {
        $messageQuery = "
            UNION ALL
            SELECT DISTINCT 
                'message' as result_type,
                m.id as result_id,
                CONCAT('رسالة في صفقة #', m.trade_id) as title,
                m.message as description,
                NULL as amount,
                NULL as price,
                NULL as currency,
                NULL as payment_method,
                'message' as offer_type,
                'active' as status,
                m.created_at as created_at,
                u.username as username,
                u.rating as user_rating,
                m.trade_id as trade_id,
                m.message as message_content
            FROM messages m
            JOIN users u ON m.user_id = u.id
            WHERE 1=1
        ";
        
        if (!empty($searchParams['query'])) {
            $messageQuery .= " AND m.message LIKE ?";
        }
        
        $baseQuery .= $messageQuery;
    }
    
    return [$baseQuery, $params];
}

try {
    // التحقق من المصادقة
    $auth = requireAuth();
    $userId = $auth->getCurrentUserId();
    $isAdmin = $auth->isAdmin();
    
    if (!$userId) {
        sendErrorResponse('المصادقة مطلوبة', 401);
    }
    
    // الحصول على معاملات البحث
    $searchParams = [];
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $searchParams = $input ?? [];
    } else {
        $searchParams = $_GET;
    }
    
    // معاملات الترقيم والترتيب
    $page = max(1, (int)($searchParams['page'] ?? 1));
    $limit = min(50, max(10, (int)($searchParams['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    $sortBy = $searchParams['sortBy'] ?? 'created_at';
    $sortOrder = strtoupper($searchParams['sortOrder'] ?? 'DESC') === 'ASC' ? 'ASC' : 'DESC';
    
    // التحقق من صحة حقل الترتيب
    $allowedSortFields = ['created_at', 'amount', 'price', 'user_rating', 'title'];
    if (!in_array($sortBy, $allowedSortFields)) {
        $sortBy = 'created_at';
    }
    
    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // بناء استعلام البحث
    $userRole = $isAdmin ? 'admin' : 'user';
    list($searchQuery, $params) = buildAdvancedSearchQuery($searchParams, $userRole);
    
    // إضافة الترتيب والترقيم
    $finalQuery = "($searchQuery) ORDER BY $sortBy $sortOrder LIMIT ? OFFSET ?";
    $finalParams = array_merge($params, [$limit, $offset]);
    
    // تنفيذ الاستعلام
    $stmt = $connection->prepare($finalQuery);
    $stmt->execute($finalParams);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // حساب العدد الإجمالي
    $countQuery = "SELECT COUNT(*) as total FROM ($searchQuery) as search_results";
    $countStmt = $connection->prepare($countQuery);
    $countStmt->execute($params);
    $totalResults = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // تنسيق النتائج
    $formattedResults = array_map(function($result) {
        return [
            'type' => $result['result_type'],
            'id' => (int)$result['result_id'],
            'title' => $result['title'],
            'description' => $result['description'],
            'amount' => $result['amount'] ? (float)$result['amount'] : null,
            'price' => $result['price'] ? (float)$result['price'] : null,
            'currency' => $result['currency'],
            'paymentMethod' => $result['payment_method'],
            'offerType' => $result['offer_type'],
            'status' => $result['status'],
            'createdAt' => $result['created_at'],
            'username' => $result['username'],
            'userRating' => $result['user_rating'] ? (float)$result['user_rating'] : 0,
            'tradeId' => $result['trade_id'] ? (int)$result['trade_id'] : null,
            'messageContent' => $result['message_content']
        ];
    }, $results);
    
    // حساب معلومات الترقيم
    $totalPages = ceil($totalResults / $limit);
    
    sendSuccessResponse([
        'results' => $formattedResults,
        'pagination' => [
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalResults' => (int)$totalResults,
            'limit' => $limit,
            'hasNextPage' => $page < $totalPages,
            'hasPrevPage' => $page > 1
        ],
        'searchParams' => $searchParams,
        'searchStats' => [
            'totalOffers' => count(array_filter($formattedResults, fn($r) => $r['type'] === 'offer')),
            'totalTrades' => count(array_filter($formattedResults, fn($r) => $r['type'] === 'trade')),
            'totalMessages' => count(array_filter($formattedResults, fn($r) => $r['type'] === 'message'))
        ]
    ], 'تم تنفيذ البحث المتقدم بنجاح');
    
} catch (Exception $e) {
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    error_log('Database error in advanced.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}
?>
