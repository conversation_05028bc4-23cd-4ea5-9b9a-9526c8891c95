'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import AdminDashboard from '@/components/AdminDashboard';

export default function AdminPage() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkAuth = () => {
      try {
        // التحقق من وجود جلسة صالحة
        const adminToken = sessionStorage.getItem('admin_token');
        const adminSession = sessionStorage.getItem('admin_session');
        const walletSession = sessionStorage.getItem('admin_wallet_session');

        if (adminToken && adminSession) {
          // التحقق من انتهاء صلاحية الجلسة
          const session = JSON.parse(adminSession);
          if (session.expiresAt && new Date() < new Date(session.expiresAt)) {
            setIsAuthenticated(true);
            setIsChecking(false);
            return;
          }
        } else if (walletSession) {
          setIsAuthenticated(true);
          setIsChecking(false);
          return;
        }

        // لا توجد جلسة صالحة، إعادة توجيه لصفحة تسجيل الدخول
        router.push('/admin/login');
      } catch (error) {
        console.error('Error checking authentication:', error);
        router.push('/admin/login');
      }
    };

    checkAuth();
  }, [router]);

  if (isChecking) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">
            جاري التحقق من صلاحيات الدخول...
          </p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // سيتم إعادة التوجيه
  }

  return <AdminDashboard />;
}
