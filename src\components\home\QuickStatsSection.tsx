'use client';

import { Shield, Zap, Users, TrendingUp } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { motion } from 'framer-motion';

export default function QuickStatsSection() {
  const { t } = useTranslation();

  const quickStats = [
    {
      icon: Shield,
      title: t('home.platformStats.secureProtection'),
      description: t('home.platformStats.secureProtectionDesc'),
      color: 'from-green-500 to-emerald-600'
    },
    {
      icon: Zap,
      title: t('home.platformStats.fastTransactions'),
      description: t('home.platformStats.fastTransactionsDesc'),
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: Users,
      title: t('home.platformStats.trustedPlatform'),
      description: t('home.platformStats.trustedPlatformDesc'),
      color: 'from-purple-500 to-purple-600'
    },
    {
      icon: TrendingUp,
      title: t('home.platformStats.profitableTrading'),
      description: t('home.platformStats.profitableTradingDesc'),
      color: 'from-orange-500 to-red-600'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <section className="py-20 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 md:gap-8"
        >
          {quickStats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <motion.div
                key={index}
                variants={itemVariants}
                className="text-center px-2"
              >
                <div className={`w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg`}>
                  <IconComponent className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                </div>
                <h4 className="text-lg sm:text-xl font-bold text-white mb-1 sm:mb-2 leading-tight">{stat.title}</h4>
                <p className="text-blue-200 text-sm sm:text-base leading-relaxed">{stat.description}</p>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
    </section>
  );
}
