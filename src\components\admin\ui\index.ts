// Admin UI Components - Reusable components for admin interface
// مكونات واجهة الإدارة - مكونات قابلة للإعادة الاستخدام للواجهة الإدارية

// Data Table Component
export { default as AdminDataTable } from './AdminDataTable';
export type { Column, AdminDataTableProps } from './AdminDataTable';

// Chart Component
export { default as AdminChart } from './AdminChart';
export type { ChartData, AdminChartProps } from './AdminChart';
export {
  createLineChartData,
  createBarChartData,
  createPieChartData,
  createAreaChartData
} from './AdminChart';

// Form Component
export { default as AdminForm } from './AdminForm';
export type { FormField, AdminFormProps } from './AdminForm';

// Modal Components
export { default as AdminModal } from './AdminModal';
export { ConfirmationModal, AlertModal, LoadingModal } from './AdminModal';
export type {
  AdminModalProps,
  ConfirmationModalProps,
  AlertModalProps,
  LoadingModalProps
} from './AdminModal';

// Button Components
export { default as AdminButton } from './AdminButton';
export { AdminButtonGroup, AdminIconButton, AdminFAB } from './AdminButton';
export type {
  AdminButtonProps,
  AdminButtonGroupProps,
  AdminIconButtonProps,
  AdminFABProps
} from './AdminButton';

// Stats Card Components
export { default as AdminStatsCard } from './AdminStatsCard';
export { AdminStatsGrid, AdminCompactStats } from './AdminStatsCard';
export type {
  AdminStatsCardProps,
  AdminStatsGridProps,
  AdminCompactStatsProps
} from './AdminStatsCard';

// Re-export common types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface LoadingProps {
  loading?: boolean;
}

export interface DisabledProps {
  disabled?: boolean;
}

export interface SizeProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

export interface VariantProps {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'ghost' | 'outline';
}

// Common utility types
export type ComponentSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type ComponentVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'ghost' | 'outline';
export type ComponentStatus = 'default' | 'success' | 'warning' | 'error' | 'info';
