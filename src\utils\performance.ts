/**
 * أدوات تحسين الأداء والمراقبة
 * Performance Optimization and Monitoring Utilities
 */

// Cache للـ API responses
class ApiCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  set(key: string, data: any, ttl: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

export const apiCache = new ApiCache();

// Debounce function
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Throttle function
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// مراقب الأداء
class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();

  startTimer(label: string): () => number {
    const start = performance.now();
    
    return () => {
      const duration = performance.now() - start;
      this.recordMetric(label, duration);
      return duration;
    };
  }

  recordMetric(label: string, value: number): void {
    if (!this.metrics.has(label)) {
      this.metrics.set(label, []);
    }
    
    const values = this.metrics.get(label)!;
    values.push(value);
    
    // الاحتفاظ بآخر 100 قياس فقط
    if (values.length > 100) {
      values.shift();
    }
  }

  getMetrics(label: string): { avg: number; min: number; max: number; count: number } | null {
    const values = this.metrics.get(label);
    if (!values || values.length === 0) return null;

    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    return { avg, min, max, count: values.length };
  }

  getAllMetrics(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const result: Record<string, any> = {};
    
    for (const [label] of this.metrics) {
      const metrics = this.getMetrics(label);
      if (metrics) {
        result[label] = metrics;
      }
    }
    
    return result;
  }

  clear(): void {
    this.metrics.clear();
  }
}

export const performanceMonitor = new PerformanceMonitor();

// مُحسن للصور
export function optimizeImageUrl(url: string, width?: number, height?: number, quality = 80): string {
  if (!url) return '';
  
  // إذا كان الرابط خارجي، إرجاعه كما هو
  if (url.startsWith('http')) return url;
  
  // إضافة معاملات التحسين للصور المحلية
  const params = new URLSearchParams();
  if (width) params.append('w', width.toString());
  if (height) params.append('h', height.toString());
  params.append('q', quality.toString());
  
  return `${url}?${params.toString()}`;
}

// تحسين الطلبات المتتالية
export class RequestBatcher {
  private batches = new Map<string, { requests: any[]; timer: NodeJS.Timeout }>();
  private batchDelay: number;

  constructor(batchDelay = 100) {
    this.batchDelay = batchDelay;
  }

  add<T>(batchKey: string, request: T, processor: (requests: T[]) => Promise<void>): void {
    if (!this.batches.has(batchKey)) {
      this.batches.set(batchKey, {
        requests: [],
        timer: setTimeout(() => this.processBatch(batchKey, processor), this.batchDelay)
      });
    }

    const batch = this.batches.get(batchKey)!;
    batch.requests.push(request);
  }

  private async processBatch<T>(batchKey: string, processor: (requests: T[]) => Promise<void>): Promise<void> {
    const batch = this.batches.get(batchKey);
    if (!batch) return;

    this.batches.delete(batchKey);
    
    try {
      await processor(batch.requests);
    } catch (error) {
      console.error(`Error processing batch ${batchKey}:`, error);
    }
  }
}

// مراقب استخدام الذاكرة
export function getMemoryUsage(): { used: number; total: number; percentage: number } | null {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    return {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
    };
  }
  return null;
}

// تحسين التمرير
export function createVirtualScrolling(
  containerHeight: number,
  itemHeight: number,
  items: any[],
  buffer = 5
): {
  visibleItems: any[];
  startIndex: number;
  endIndex: number;
  totalHeight: number;
  offsetY: number;
} {
  const totalHeight = items.length * itemHeight;
  const visibleCount = Math.ceil(containerHeight / itemHeight);
  
  // حساب العناصر المرئية مع buffer
  const startIndex = Math.max(0, Math.floor(window.scrollY / itemHeight) - buffer);
  const endIndex = Math.min(items.length - 1, startIndex + visibleCount + buffer * 2);
  
  const visibleItems = items.slice(startIndex, endIndex + 1);
  const offsetY = startIndex * itemHeight;

  return {
    visibleItems,
    startIndex,
    endIndex,
    totalHeight,
    offsetY
  };
}

// تحسين الشبكة
export function preloadResource(url: string, type: 'script' | 'style' | 'image' = 'image'): Promise<void> {
  return new Promise((resolve, reject) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = url;
    
    switch (type) {
      case 'script':
        link.as = 'script';
        break;
      case 'style':
        link.as = 'style';
        break;
      case 'image':
        link.as = 'image';
        break;
    }
    
    link.onload = () => resolve();
    link.onerror = () => reject(new Error(`Failed to preload ${url}`));
    
    document.head.appendChild(link);
  });
}

// مراقب الاتصال
export function getConnectionInfo(): {
  effectiveType: string;
  downlink: number;
  rtt: number;
  saveData: boolean;
} | null {
  if ('connection' in navigator) {
    const connection = (navigator as any).connection;
    return {
      effectiveType: connection.effectiveType || 'unknown',
      downlink: connection.downlink || 0,
      rtt: connection.rtt || 0,
      saveData: connection.saveData || false
    };
  }
  return null;
}

// تحسين الصور التكيفية
export function getOptimalImageSize(): { width: number; height: number } {
  const dpr = window.devicePixelRatio || 1;
  const connection = getConnectionInfo();
  
  // تقليل جودة الصور للاتصالات البطيئة
  const qualityMultiplier = connection?.effectiveType === 'slow-2g' || connection?.effectiveType === '2g' 
    ? 0.5 
    : connection?.effectiveType === '3g' 
    ? 0.75 
    : 1;

  return {
    width: Math.round(window.innerWidth * dpr * qualityMultiplier),
    height: Math.round(window.innerHeight * dpr * qualityMultiplier)
  };
}

// تقرير الأداء
export function generatePerformanceReport(): {
  metrics: Record<string, any>;
  memory: any;
  connection: any;
  cacheSize: number;
  recommendations: string[];
} {
  const metrics = performanceMonitor.getAllMetrics();
  const memory = getMemoryUsage();
  const connection = getConnectionInfo();
  const cacheSize = apiCache.size();
  
  const recommendations: string[] = [];
  
  // توصيات بناءً على الأداء
  if (memory && memory.percentage > 80) {
    recommendations.push('استخدام الذاكرة مرتفع - فكر في تحسين إدارة البيانات');
  }
  
  if (connection && connection.effectiveType === 'slow-2g') {
    recommendations.push('الاتصال بطيء - قم بتحسين الصور وتقليل حجم البيانات');
  }
  
  if (cacheSize > 100) {
    recommendations.push('حجم الـ cache كبير - فكر في تنظيف البيانات القديمة');
  }

  return {
    metrics,
    memory,
    connection,
    cacheSize,
    recommendations
  };
}

// تصدير جميع الأدوات
export default {
  apiCache,
  debounce,
  throttle,
  performanceMonitor,
  optimizeImageUrl,
  RequestBatcher,
  getMemoryUsage,
  createVirtualScrolling,
  preloadResource,
  getConnectionInfo,
  getOptimalImageSize,
  generatePerformanceReport
};
