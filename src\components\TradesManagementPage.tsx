'use client';

import { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye,
  MessageSquare,
  DollarSign,
  User,
  Calendar,
  TrendingUp,
  RefreshCw,
  Download,
  MoreHorizontal,
  Shield,
  Wallet
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { contractService, TradeStatus } from '@/services/contractService';
import { databaseService } from '@/services/databaseService';
import { walletService } from '@/services/walletService';
import { notificationService } from '@/services/notificationService';
import { handleContractError, handleWalletError } from '@/utils/errorHandler';
import TradeDetailsModal from './TradeDetailsModal';

// واجهة بيانات الصفقة
interface Trade {
  id: string;
  blockchain_trade_id: number;
  offer_id: string;
  seller_id: string;
  buyer_id: string;
  seller_name: string;
  buyer_name: string;
  amount: number;
  price: number;
  currency: string;
  stablecoin: string;
  total_value: number;
  platform_fee: number;
  net_amount: number;
  status: string;
  contract_status: TradeStatus;
  payment_methods: string[];
  created_at: string;
  updated_at: string;
  join_transaction_hash?: string;
  payment_sent_hash?: string;
  completion_hash?: string;
  contract_created_at?: string;
  contract_joined_at?: string;
  payment_sent_at?: string;
  completed_at?: string;
  last_sync_at: string;
  is_seller: boolean;
  is_buyer: boolean;
  can_confirm_payment: boolean;
  can_release_funds: boolean;
  can_cancel: boolean;
  can_dispute: boolean;
}

// واجهة إحصائيات الصفقات
interface TradeStats {
  total: number;
  active: number;
  completed: number;
  cancelled: number;
  disputed: number;
  totalVolume: number;
  myTotalTrades: number;
  myActiveTrades: number;
  myCompletedTrades: number;
  myTotalVolume: number;
}

export default function TradesManagementPage() {
  const { t, language } = useTranslation();

  // الحالات الأساسية
  const [trades, setTrades] = useState<Trade[]>([]);
  const [filteredTrades, setFilteredTrades] = useState<Trade[]>([]);
  const [stats, setStats] = useState<TradeStats>({
    total: 0,
    active: 0,
    completed: 0,
    cancelled: 0,
    disputed: 0,
    totalVolume: 0,
    myTotalTrades: 0,
    myActiveTrades: 0,
    myCompletedTrades: 0,
    myTotalVolume: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // حالات الفلترة والبحث
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all'); // all, seller, buyer
  const [dateFilter, setDateFilter] = useState('all'); // all, today, week, month
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // حالات المحفظة والمستخدم
  const [isWalletConnected, setIsWalletConnected] = useState(false);
  const [userAddress, setUserAddress] = useState<string | null>(null);
  const [selectedTrade, setSelectedTrade] = useState<Trade | null>(null);

  // تحميل البيانات عند تحميل المكون
  useEffect(() => {
    checkWalletConnection();
    loadTrades();
  }, []);

  // تطبيق الفلاتر عند تغيير المعايير
  useEffect(() => {
    applyFilters();
  }, [trades, searchTerm, statusFilter, roleFilter, dateFilter, sortBy, sortOrder]);

  /**
   * التحقق من اتصال المحفظة
   */
  const checkWalletConnection = async () => {
    try {
      const connected = walletService.isWalletConnected();
      setIsWalletConnected(connected);

      if (connected) {
        const address = walletService.getCurrentAccount();
        setUserAddress(address);
      }
    } catch (error) {
      console.error('خطأ في التحقق من المحفظة:', error);
    }
  };

  /**
   * تحميل الصفقات من قاعدة البيانات والعقد الذكي
   */
  const loadTrades = async () => {
    try {
      setIsLoading(true);

      // جلب الصفقات من قاعدة البيانات
      const dbTrades = await databaseService.getTradesWithDetails();

      // إذا كانت المحفظة متصلة، جلب البيانات من العقد الذكي أيضاً
      if (isWalletConnected && contractService.isConnected()) {
        await syncWithContract(dbTrades);
      }

      // حساب الإحصائيات
      const calculatedStats = calculateStats(dbTrades);
      setStats(calculatedStats);
      setTrades(dbTrades);

    } catch (error) {
      handleContractError(error, 'LoadTrades');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * مزامنة البيانات مع العقد الذكي
   */
  const syncWithContract = async (dbTrades: Trade[]) => {
    try {
      for (const trade of dbTrades) {
        if (trade.blockchain_trade_id) {
          // جلب حالة الصفقة من العقد الذكي
          const contractTrade = await contractService.getTrade(trade.blockchain_trade_id);
          
          if (contractTrade && contractTrade.status !== trade.contract_status) {
            // تحديث الحالة في قاعدة البيانات
            await databaseService.updateTradeStatus(trade.id, {
              contract_status: contractTrade.status,
              last_sync_at: new Date().toISOString()
            });
          }
        }
      }
    } catch (error) {
      console.error('خطأ في مزامنة البيانات:', error);
    }
  };

  /**
   * حساب الإحصائيات
   */
  const calculateStats = (tradesData: Trade[]): TradeStats => {
    const userAddress = walletService.getCurrentAccount()?.toLowerCase();
    
    const myTrades = tradesData.filter(trade => 
      trade.seller_id.toLowerCase() === userAddress || 
      trade.buyer_id.toLowerCase() === userAddress
    );

    return {
      total: tradesData.length,
      active: tradesData.filter(t => ['created', 'joined', 'payment_sent'].includes(t.status)).length,
      completed: tradesData.filter(t => t.status === 'completed').length,
      cancelled: tradesData.filter(t => t.status === 'cancelled').length,
      disputed: tradesData.filter(t => t.status === 'disputed').length,
      totalVolume: tradesData.reduce((sum, t) => sum + t.total_value, 0),
      myTotalTrades: myTrades.length,
      myActiveTrades: myTrades.filter(t => ['created', 'joined', 'payment_sent'].includes(t.status)).length,
      myCompletedTrades: myTrades.filter(t => t.status === 'completed').length,
      myTotalVolume: myTrades.reduce((sum, t) => sum + t.total_value, 0)
    };
  };

  /**
   * تطبيق الفلاتر
   */
  const applyFilters = () => {
    let filtered = [...trades];
    const userAddress = walletService.getCurrentAccount()?.toLowerCase();

    // فلتر البحث
    if (searchTerm) {
      filtered = filtered.filter(trade =>
        trade.seller_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trade.buyer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trade.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trade.blockchain_trade_id.toString().includes(searchTerm)
      );
    }

    // فلتر الحالة
    if (statusFilter !== 'all') {
      filtered = filtered.filter(trade => trade.status === statusFilter);
    }

    // فلتر الدور
    if (roleFilter !== 'all' && userAddress) {
      if (roleFilter === 'seller') {
        filtered = filtered.filter(trade => trade.seller_id.toLowerCase() === userAddress);
      } else if (roleFilter === 'buyer') {
        filtered = filtered.filter(trade => trade.buyer_id.toLowerCase() === userAddress);
      }
    }

    // فلتر التاريخ
    if (dateFilter !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
      }
      
      filtered = filtered.filter(trade => new Date(trade.created_at) >= filterDate);
    }

    // الترتيب
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof Trade];
      let bValue: any = b[sortBy as keyof Trade];

      if (sortBy === 'created_at' || sortBy === 'updated_at') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredTrades(filtered);
  };

  /**
   * تحديث البيانات
   */
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadTrades();
    setIsRefreshing(false);
    notificationService.success(t('trades.messages.dataRefreshed'));
  };

  /**
   * ربط المحفظة
   */
  const handleConnectWallet = async () => {
    try {
      const walletInfo = await walletService.connectWallet('metamask');
      setIsWalletConnected(true);
      setUserAddress(walletInfo.address);
      notificationService.walletConnected(walletInfo.address);
      
      // إعادة تحميل البيانات مع المحفظة المتصلة
      await loadTrades();
    } catch (error) {
      handleWalletError(error, 'ConnectWallet');
    }
  };

  /**
   * الحصول على لون الحالة
   */
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'created':
        return 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20';
      case 'joined':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20';
      case 'payment_sent':
        return 'text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-900/20';
      case 'completed':
        return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20';
      case 'cancelled':
        return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/20';
      case 'disputed':
        return 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  /**
   * الحصول على أيقونة الحالة
   */
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'created':
        return <Clock className="w-4 h-4" />;
      case 'joined':
        return <User className="w-4 h-4" />;
      case 'payment_sent':
        return <DollarSign className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4" />;
      case 'disputed':
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="container-custom">
        {/* العنوان */}
        <div className="mb-8">
          <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4 heading-arabic">
            {t('trades.title')}
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 body-arabic">
            {t('trades.subtitle')}
          </p>
        </div>

        {/* تحذير عدم ربط المحفظة */}
        {!isWalletConnected && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-6 mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Wallet className="w-6 h-6 text-yellow-600 dark:text-yellow-400 ml-3" />
                <div>
                  <h3 className="font-semibold text-yellow-800 dark:text-yellow-300">
                    {t('trades.wallet.notConnected')}
                  </h3>
                  <p className="text-yellow-700 dark:text-yellow-400 text-sm">
                    {t('trades.wallet.notConnectedDesc')}
                  </p>
                </div>
              </div>
              <button
                onClick={handleConnectWallet}
                className="btn btn-warning"
              >
                {t('trades.wallet.connect')}
              </button>
            </div>
          </div>
        )}

        {/* الإحصائيات */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center border border-gray-200 dark:border-gray-700">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{stats.total}</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">{t('trades.stats.total')}</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center border border-gray-200 dark:border-gray-700">
            <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{stats.active}</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">{t('trades.stats.active')}</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center border border-gray-200 dark:border-gray-700">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">{stats.completed}</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">{t('trades.stats.completed')}</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center border border-gray-200 dark:border-gray-700">
            <div className="text-2xl font-bold text-gray-600 dark:text-gray-400">{stats.cancelled}</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">{t('trades.stats.cancelled')}</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center border border-gray-200 dark:border-gray-700">
            <div className="text-2xl font-bold text-red-600 dark:text-red-400">{stats.disputed}</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">{t('trades.stats.disputed')}</div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center border border-gray-200 dark:border-gray-700">
            <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
              ${stats.totalVolume.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">{t('trades.stats.volume')}</div>
          </div>
        </div>

        {/* أدوات البحث والفلترة */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              {t('trades.filters.title')}
            </h2>
            <div className="flex items-center gap-3">
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="btn btn-secondary flex items-center gap-2"
              >
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                {t('trades.actions.refresh')}
              </button>
              <button className="btn btn-secondary flex items-center gap-2">
                <Download className="w-4 h-4" />
                {t('trades.actions.export')}
              </button>
            </div>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            {/* البحث */}
            <div className="input-with-icon">
              <Search className="input-icon-right w-5 h-5 text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                placeholder={t('trades.search.placeholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input text-base-ar"
              />
            </div>

            {/* فلتر الحالة */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="form-select"
            >
              <option value="all">{t('trades.filters.allStatuses')}</option>
              <option value="created">{t('trades.status.created')}</option>
              <option value="joined">{t('trades.status.joined')}</option>
              <option value="payment_sent">{t('trades.status.paymentSent')}</option>
              <option value="completed">{t('trades.status.completed')}</option>
              <option value="cancelled">{t('trades.status.cancelled')}</option>
              <option value="disputed">{t('trades.status.disputed')}</option>
            </select>

            {/* فلتر الدور */}
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              className="form-select"
            >
              <option value="all">{t('trades.filters.allRoles')}</option>
              <option value="seller">{t('trades.filters.seller')}</option>
              <option value="buyer">{t('trades.filters.buyer')}</option>
            </select>

            {/* فلتر التاريخ */}
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="form-select"
            >
              <option value="all">{t('trades.filters.allDates')}</option>
              <option value="today">{t('trades.filters.today')}</option>
              <option value="week">{t('trades.filters.thisWeek')}</option>
              <option value="month">{t('trades.filters.thisMonth')}</option>
            </select>
          </div>

          {/* خيارات الترتيب */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-4">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {t('trades.sort.sortBy')}:
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="form-select text-sm"
              >
                <option value="created_at">{t('trades.sort.date')}</option>
                <option value="amount">{t('trades.sort.amount')}</option>
                <option value="total_value">{t('trades.sort.totalValue')}</option>
                <option value="status">{t('trades.sort.status')}</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {t('trades.sort.order')}:
              </label>
              <select
                value={sortOrder}
                onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
                className="form-select text-sm"
              >
                <option value="desc">{t('trades.sort.descending')}</option>
                <option value="asc">{t('trades.sort.ascending')}</option>
              </select>
            </div>
          </div>
        </div>

        {/* جدول الصفقات */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                {t('trades.table.title')} ({filteredTrades.length})
              </h2>
              {isWalletConnected && (
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  {t('trades.table.myTrades')}: {stats.myTotalTrades} |
                  {t('trades.table.myVolume')}: ${stats.myTotalVolume.toLocaleString()}
                </div>
              )}
            </div>
          </div>

          {isLoading ? (
            <div className="p-12 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-300">{t('trades.loading')}</p>
            </div>
          ) : filteredTrades.length === 0 ? (
            <div className="p-12 text-center">
              <div className="text-gray-500 dark:text-gray-400 text-lg mb-4">
                {t('trades.noTrades.title')}
              </div>
              <p className="text-gray-400 dark:text-gray-500 mb-6">
                {t('trades.noTrades.description')}
              </p>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setRoleFilter('all');
                  setDateFilter('all');
                }}
                className="btn btn-primary"
              >
                {t('trades.noTrades.clearFilters')}
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('trades.table.headers.id')}
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('trades.table.headers.participants')}
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('trades.table.headers.amount')}
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('trades.table.headers.status')}
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('trades.table.headers.date')}
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {t('trades.table.headers.actions')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredTrades.map((trade) => (
                    <tr key={trade.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          #{trade.blockchain_trade_id}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {trade.id.slice(0, 8)}...
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm">
                          <div className="flex items-center mb-1">
                            <span className="text-gray-600 dark:text-gray-400 text-xs ml-2">
                              {t('trades.table.seller')}:
                            </span>
                            <span className="font-medium text-gray-900 dark:text-white">
                              {trade.seller_name}
                            </span>
                            {trade.is_seller && (
                              <span className="mr-1 px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded">
                                {t('trades.table.you')}
                              </span>
                            )}
                          </div>
                          <div className="flex items-center">
                            <span className="text-gray-600 dark:text-gray-400 text-xs ml-2">
                              {t('trades.table.buyer')}:
                            </span>
                            <span className="font-medium text-gray-900 dark:text-white">
                              {trade.buyer_name}
                            </span>
                            {trade.is_buyer && (
                              <span className="mr-1 px-2 py-1 text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded">
                                {t('trades.table.you')}
                              </span>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {trade.amount.toLocaleString()} {trade.stablecoin}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {trade.total_value.toLocaleString()} {trade.currency}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(trade.status)}`}>
                          {getStatusIcon(trade.status)}
                          <span className="mr-1">{t(`trades.status.${trade.status}`)}</span>
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {new Date(trade.created_at).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => setSelectedTrade(trade)}
                            className="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300"
                            title={t('trades.actions.viewDetails')}
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                            title={t('trades.actions.chat')}
                          >
                            <MessageSquare className="w-4 h-4" />
                          </button>
                          <button
                            className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300"
                            title={t('trades.actions.more')}
                          >
                            <MoreHorizontal className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* مودال تفاصيل الصفقة */}
        <TradeDetailsModal
          trade={selectedTrade}
          isOpen={selectedTrade !== null}
          onClose={() => setSelectedTrade(null)}
          onUpdate={loadTrades}
        />
      </div>
    </div>
  );
}
