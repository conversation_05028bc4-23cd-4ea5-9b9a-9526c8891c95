<?php
/**
 * API العروض المحسنة للعقد متعدد العملات
 * Enhanced Offers API for Multi-Token Contract
 */

require_once __DIR__ . '/../cors.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/response.php';

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetOffers();
            break;
        case 'POST':
            handleCreateOffer();
            break;
        case 'PUT':
            handleUpdateOffer();
            break;
        case 'DELETE':
            handleDeleteOffer();
            break;
        default:
            sendErrorResponse('طريقة طلب غير مدعومة', 405);
    }
    
} catch (Exception $e) {
    logError("Enhanced Offers API error: " . $e->getMessage());
    sendErrorResponse('خطأ في الخادم: ' . $e->getMessage(), 500);
}

/**
 * جلب العروض المحسنة
 */
function handleGetOffers() {
    $db = new Database();
    $connection = $db->getConnection();
    
    try {
        // معاملات البحث والتصفية
        $networkId = $_GET['network_id'] ?? null;
        $tokenId = $_GET['token_id'] ?? null;
        $offerType = $_GET['offer_type'] ?? null;
        $currency = $_GET['currency'] ?? null;
        $minAmount = $_GET['min_amount'] ?? null;
        $maxAmount = $_GET['max_amount'] ?? null;
        $minPrice = $_GET['min_price'] ?? null;
        $maxPrice = $_GET['max_price'] ?? null;
        $isActive = $_GET['active'] ?? '1';
        $userId = $_GET['user_id'] ?? null;
        
        // معاملات الترقيم
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(100, max(1, intval($_GET['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        
        // معاملات الترتيب
        $sortBy = $_GET['sort_by'] ?? 'created_at';
        $sortOrder = strtoupper($_GET['sort_order'] ?? 'DESC') === 'ASC' ? 'ASC' : 'DESC';
        
        // بناء شروط البحث
        $whereConditions = ['o.is_active = 1'];
        $params = [];
        
        if ($networkId) {
            $whereConditions[] = "o.network_id = ?";
            $params[] = $networkId;
        }
        
        if ($tokenId) {
            $whereConditions[] = "o.token_id = ?";
            $params[] = $tokenId;
        }
        
        if ($offerType) {
            $whereConditions[] = "o.offer_type = ?";
            $params[] = $offerType;
        }
        
        if ($currency) {
            $whereConditions[] = "o.currency = ?";
            $params[] = $currency;
        }
        
        if ($minAmount) {
            $whereConditions[] = "o.amount >= ?";
            $params[] = $minAmount;
        }
        
        if ($maxAmount) {
            $whereConditions[] = "o.amount <= ?";
            $params[] = $maxAmount;
        }
        
        if ($minPrice) {
            $whereConditions[] = "o.price >= ?";
            $params[] = $minPrice;
        }
        
        if ($maxPrice) {
            $whereConditions[] = "o.price <= ?";
            $params[] = $maxPrice;
        }
        
        if ($userId) {
            $whereConditions[] = "o.user_id = ?";
            $params[] = $userId;
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        
        // استعلام العد الإجمالي
        $countQuery = "
            SELECT COUNT(*) as total 
            FROM offers o
            LEFT JOIN supported_networks n ON o.network_id = n.id
            LEFT JOIN supported_tokens t ON o.token_id = t.id
            LEFT JOIN users u ON o.user_id = u.id
            $whereClause
        ";
        
        $countStmt = $connection->prepare($countQuery);
        $countStmt->execute($params);
        $totalOffers = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // استعلام البيانات الرئيسي
        $dataQuery = "
            SELECT 
                o.id,
                o.user_id,
                o.offer_type,
                o.network_id,
                o.token_id,
                o.amount,
                o.min_amount,
                o.max_amount,
                o.price,
                o.currency,
                o.payment_methods,
                o.terms,
                o.auto_reply,
                o.is_active,
                o.is_premium,
                o.is_free,
                o.views_count,
                o.time_limit,
                o.blockchain_trade_id,
                o.contract_status,
                o.escrow_amount,
                o.platform_fee,
                o.net_amount,
                o.core_escrow_address,
                o.reputation_score_at_creation,
                o.oracle_price_at_creation,
                o.last_sync_at,
                o.contract_created_at,
                o.sync_status,
                o.created_at,
                o.updated_at,
                o.expires_at,
                (o.amount * o.price) as total_value,
                
                -- معلومات الشبكة
                n.network_name,
                n.network_symbol,
                n.chain_id,
                n.is_testnet as network_is_testnet,
                
                -- معلومات العملة
                t.token_address,
                t.token_symbol,
                t.token_name,
                t.decimals,
                t.is_stablecoin,
                t.icon_url as token_icon,
                t.min_trade_amount as token_min_amount,
                t.max_trade_amount as token_max_amount,
                t.platform_fee_rate as token_fee_rate,
                
                -- معلومات المستخدم
                u.username,
                u.full_name,
                u.wallet_address,
                u.rating,
                u.total_trades,
                u.completed_trades,
                u.is_verified,
                u.last_login,
                u.country_code,
                u.profile_image,
                
                -- حالة الاتصال
                CASE
                    WHEN u.last_login > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 1
                    ELSE 0
                END as is_online,
                
                -- السمعة المحسنة
                ur.reputation_score,
                ur.reputation_level,
                ur.average_rating as user_average_rating,
                ur.response_rate
                
            FROM offers o
            LEFT JOIN supported_networks n ON o.network_id = n.id
            LEFT JOIN supported_tokens t ON o.token_id = t.id
            LEFT JOIN users u ON o.user_id = u.id
            LEFT JOIN enhanced_user_reputation ur ON (u.id = ur.user_id AND o.network_id = ur.network_id)
            $whereClause
            ORDER BY o.$sortBy $sortOrder
            LIMIT ? OFFSET ?
        ";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $dataStmt = $connection->prepare($dataQuery);
        $dataStmt->execute($params);
        $offers = $dataStmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تحويل البيانات وتنسيقها
        foreach ($offers as &$offer) {
            // تحويل القيم المنطقية
            $offer['is_active'] = (bool)$offer['is_active'];
            $offer['is_premium'] = (bool)$offer['is_premium'];
            $offer['is_free'] = (bool)$offer['is_free'];
            $offer['is_stablecoin'] = (bool)$offer['is_stablecoin'];
            $offer['network_is_testnet'] = (bool)$offer['network_is_testnet'];
            $offer['is_verified'] = (bool)$offer['is_verified'];
            $offer['is_online'] = (bool)$offer['is_online'];
            
            // تحويل القيم الرقمية
            $offer['amount'] = (float)$offer['amount'];
            $offer['min_amount'] = (float)$offer['min_amount'];
            $offer['max_amount'] = (float)$offer['max_amount'];
            $offer['price'] = (float)$offer['price'];
            $offer['total_value'] = (float)$offer['total_value'];
            $offer['escrow_amount'] = $offer['escrow_amount'] ? (float)$offer['escrow_amount'] : null;
            $offer['platform_fee'] = $offer['platform_fee'] ? (float)$offer['platform_fee'] : null;
            $offer['net_amount'] = $offer['net_amount'] ? (float)$offer['net_amount'] : null;
            $offer['oracle_price_at_creation'] = $offer['oracle_price_at_creation'] ? (float)$offer['oracle_price_at_creation'] : null;
            $offer['rating'] = $offer['rating'] ? (float)$offer['rating'] : 0;
            $offer['token_min_amount'] = (float)$offer['token_min_amount'];
            $offer['token_max_amount'] = (float)$offer['token_max_amount'];
            $offer['token_fee_rate'] = (float)$offer['token_fee_rate'];
            $offer['reputation_score'] = $offer['reputation_score'] ? (int)$offer['reputation_score'] : 100;
            $offer['user_average_rating'] = $offer['user_average_rating'] ? (float)$offer['user_average_rating'] : 0;
            $offer['response_rate'] = $offer['response_rate'] ? (float)$offer['response_rate'] : 100;
            
            // تحويل JSON
            $offer['payment_methods'] = $offer['payment_methods'] ? json_decode($offer['payment_methods'], true) : [];
            
            // إضافة معلومات إضافية
            $offer['can_trade'] = $offer['is_active'] && $offer['amount'] > 0;
            $offer['estimated_fee'] = $offer['amount'] * $offer['token_fee_rate'];
            $offer['net_receive'] = $offer['amount'] - $offer['estimated_fee'];
        }
        
        // حساب معلومات الترقيم
        $totalPages = ceil($totalOffers / $limit);
        $hasNextPage = $page < $totalPages;
        $hasPrevPage = $page > 1;
        
        sendSuccessResponse([
            'offers' => $offers,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_offers' => (int)$totalOffers,
                'per_page' => $limit,
                'has_next_page' => $hasNextPage,
                'has_prev_page' => $hasPrevPage
            ],
            'filters' => [
                'network_id' => $networkId,
                'token_id' => $tokenId,
                'offer_type' => $offerType,
                'currency' => $currency,
                'sort_by' => $sortBy,
                'sort_order' => $sortOrder
            ]
        ]);
        
    } catch (Exception $e) {
        sendErrorResponse('خطأ في جلب العروض: ' . $e->getMessage(), 500);
    }
}

/**
 * إنشاء عرض جديد محسن
 */
function handleCreateOffer() {
    $db = new Database();
    $connection = $db->getConnection();
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            sendErrorResponse('بيانات غير صحيحة', 400);
            return;
        }
        
        // التحقق من الحقول المطلوبة
        $requiredFields = ['user_id', 'offer_type', 'network_id', 'token_id', 'amount', 'price', 'currency'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || $input[$field] === '') {
                sendErrorResponse("الحقل $field مطلوب", 400);
                return;
            }
        }
        
        // التحقق من صحة الشبكة والعملة
        $networkStmt = $connection->prepare("
            SELECT id FROM supported_networks 
            WHERE id = ? AND is_active = 1
        ");
        $networkStmt->execute([$input['network_id']]);
        if (!$networkStmt->fetch()) {
            sendErrorResponse('الشبكة غير صحيحة أو غير نشطة', 400);
            return;
        }
        
        $tokenStmt = $connection->prepare("
            SELECT * FROM supported_tokens 
            WHERE id = ? AND network_id = ? AND is_active = 1
        ");
        $tokenStmt->execute([$input['token_id'], $input['network_id']]);
        $token = $tokenStmt->fetch(PDO::FETCH_ASSOC);
        if (!$token) {
            sendErrorResponse('العملة غير صحيحة أو غير مدعومة في هذه الشبكة', 400);
            return;
        }
        
        // التحقق من حدود المبلغ
        if ($input['amount'] < $token['min_trade_amount']) {
            sendErrorResponse("المبلغ أقل من الحد الأدنى المسموح ({$token['min_trade_amount']} {$token['token_symbol']})", 400);
            return;
        }
        
        if ($input['amount'] > $token['max_trade_amount']) {
            sendErrorResponse("المبلغ أكبر من الحد الأقصى المسموح ({$token['max_trade_amount']} {$token['token_symbol']})", 400);
            return;
        }
        
        // إنشاء العرض
        $stmt = $connection->prepare("
            INSERT INTO offers (
                user_id, offer_type, network_id, token_id, amount, min_amount, max_amount,
                price, currency, payment_methods, terms, auto_reply, time_limit,
                is_free, contract_status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ");
        
        $paymentMethods = json_encode($input['payment_methods'] ?? []);
        
        $stmt->execute([
            $input['user_id'],
            $input['offer_type'],
            $input['network_id'],
            $input['token_id'],
            $input['amount'],
            $input['min_amount'] ?? $input['amount'],
            $input['max_amount'] ?? $input['amount'],
            $input['price'],
            $input['currency'],
            $paymentMethods,
            $input['terms'] ?? '',
            $input['auto_reply'] ?? '',
            $input['time_limit'] ?? 1800,
            $input['is_free'] ?? false,
            'pending'
        ]);
        
        $offerId = $connection->lastInsertId();
        
        sendSuccessResponse([
            'offer_id' => (int)$offerId,
            'message' => 'تم إنشاء العرض بنجاح',
            'next_step' => 'يمكنك الآن ربط العرض بالعقد الذكي'
        ]);
        
    } catch (Exception $e) {
        sendErrorResponse('خطأ في إنشاء العرض: ' . $e->getMessage(), 500);
    }
}

/**
 * تحديث عرض موجود
 */
function handleUpdateOffer() {
    // سيتم تنفيذها لاحقاً
    sendErrorResponse('غير مدعوم حالياً', 501);
}

/**
 * حذف عرض
 */
function handleDeleteOffer() {
    // سيتم تنفيذها لاحقاً
    sendErrorResponse('غير مدعوم حالياً', 501);
}

?>
