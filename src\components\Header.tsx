'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { Menu, X, User, Bell, Settings, LogOut, Home, DollarSign, BarChart3, TrendingUp, Wallet, ChevronDown, Shield, Star, MessageSquare, Activity, Zap } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuth } from '@/contexts/AuthContext';
import LanguageSelector from './LanguageSelector';
import ThemeToggle from './ThemeToggle';
import NotificationButton from './NotificationButton';
import UnifiedWalletManager from './UnifiedWalletManager';
import AuthButton from './AuthButton';



interface HeaderProps {
  className?: string;
}

export default function Header({}: HeaderProps) {
  const { t } = useTranslation();
  const { isAuthenticated, user, logout, isWalletConnected } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  const toggleMenu = useCallback(() => setIsMenuOpen(prev => !prev), []);
  const toggleProfile = useCallback(() => setIsProfileOpen(prev => !prev), []);

  // تحسين أداء عناصر التنقل باستخدام useMemo
  const navigationItems = useMemo(() => [
    { href: '/', label: t('navigation.home'), icon: Home, isPublic: true, priority: 1 },
    { href: '/offers', label: t('navigation.offers'), icon: DollarSign, isPublic: true, priority: 2 },
    { href: '/create-offer', label: t('navigation.createOffer'), icon: TrendingUp, isPublic: true, priority: 3 },
    { href: '/subscriptions', label: 'الباقات', icon: Star, isPublic: true, priority: 4 }
  ], [t]);

  // تحسين معالجة التمرير مع throttling محسن
  useEffect(() => {
    let ticking = false;
    let lastScrollY = 0;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const currentScrollY = window.scrollY;
          setScrolled(currentScrollY > 10);
          lastScrollY = currentScrollY;
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // تحسين معالجة النقر خارج القوائم
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (isMenuOpen && !target.closest('.mobile-menu-container')) {
        setIsMenuOpen(false);
      }
      if (isProfileOpen && !target.closest('.profile-menu-container')) {
        setIsProfileOpen(false);
      }
    };

    if (isMenuOpen || isProfileOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [isMenuOpen, isProfileOpen]);

  // تحسين معالجة مفتاح الهروب
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsMenuOpen(false);
        setIsProfileOpen(false);
      }
    };

    if (isMenuOpen || isProfileOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isMenuOpen, isProfileOpen]);

  return (
    <header className={`header-enhanced backdrop-enhanced border-enhanced shadow-enhanced sticky top-0 z-50 transition-enhanced ${
      scrolled ? 'shadow-xl bg-white/98 dark:bg-gray-900/98 backdrop-blur-lg' : ''
    }`}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="layout-enhanced h-16 lg:h-20">
          {/* الشعار المحسن مع تأثيرات متقدمة */}
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <a href="/" className="flex items-center group" aria-label={t('header.title')}>
              <div className="logo-enhanced relative w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-enhanced transform group-hover:scale-105 transition-enhanced group-hover:shadow-xl overflow-hidden">
                <span className="text-white font-bold text-lg sm:text-xl font-arabic relative z-10">إ</span>
                <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                {/* تأثير الوميض */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-in-out"></div>
              </div>
              <div className="ltr:ml-2 ltr:sm:ml-3 rtl:mr-2 rtl:sm:mr-3">
                <h1 className="text-enhanced arabic-text-enhanced text-lg sm:text-xl font-bold heading-arabic group-hover:from-blue-700 group-hover:to-purple-700 transition-enhanced">
                  {t('header.title')}
                </h1>
                <p className="text-xs text-gray-500 dark:text-gray-400 body-arabic hidden sm:block group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors duration-300">
                  {t('header.subtitle')}
                </p>
              </div>
            </a>
          </div>

          {/* القائمة الرئيسية المحسنة - سطح المكتب */}
          <nav className="hidden lg:flex items-center space-x-6 xl:space-x-8 rtl:space-x-reverse" role="navigation" aria-label={t('navigation.menu')}>
            {navigationItems.map((item, index) => (
              <a
                key={item.href}
                href={item.href}
                className="relative flex items-center space-x-2 rtl:space-x-reverse text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 font-medium transition-all duration-300 group body-arabic text-sm xl:text-base px-3 py-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
                style={{ animationDelay: `${index * 100}ms` }}
                aria-label={item.label}
              >
                <item.icon className="w-4 h-4 opacity-70 group-hover:opacity-100 group-hover:scale-110 transition-all duration-300" />
                <span className="group-hover:translate-x-0.5 rtl:group-hover:-translate-x-0.5 transition-transform duration-300">{item.label}</span>
                <span className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-3/4 transition-all duration-300 rounded-full"></span>
                {/* مؤشر الأولوية للعناصر المهمة */}
                {item.priority <= 2 && (
                  <Zap className="w-3 h-3 text-yellow-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 absolute -top-1 -right-1" />
                )}
              </a>
            ))}
          </nav>

          {/* أزرار المستخدم المحسنة مع تحسينات الأداء */}
          <div className="flex items-center space-x-2 sm:space-x-3 lg:space-x-4 rtl:space-x-reverse">
            {/* مكون اختيار اللغة - مخفي على الجوال الصغير */}
            <div className="hidden sm:block">
              <LanguageSelector />
            </div>

            {/* مفتاح تبديل الوضع المظلم مع تحسينات */}
            <div className="relative">
              <ThemeToggle />
            </div>

            {/* عرض حالة المحفظة */}
            <div className="hidden md:flex items-center">
              <UnifiedWalletManager
                variant="header"
                showBalance={false}
                showNetwork={true}
                showActions={true}
                showConnectionOptions={true}
              />
            </div>



            {isAuthenticated ? (
              <div className="flex items-center space-x-2 sm:space-x-3 lg:space-x-4 rtl:space-x-reverse">
                {/* الإشعارات */}
                <div className="relative">
                  <NotificationButton />
                </div>

                {/* قائمة المستخدم المحسنة مع تحسينات الأداء */}
                <div className="relative profile-menu-container">
                  <button
                    onClick={toggleProfile}
                    className="flex items-center space-x-2 rtl:space-x-reverse p-2 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900/20 dark:hover:to-purple-900/20 transition-all duration-300 group border border-transparent hover:border-blue-200 dark:hover:border-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
                    aria-label={t('header.profile')}
                    aria-expanded={isProfileOpen}
                    aria-haspopup="true"
                  >
                    <div className="relative w-8 h-8 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/30 dark:to-primary-800/30 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-sm">
                      <User className="w-4 h-4 text-primary-600 dark:text-primary-400" />
                      {user?.isVerified && (
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center">
                          <Shield className="w-2 h-2 text-white" />
                        </div>
                      )}
                      {/* مؤشر الحالة النشطة */}
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white dark:border-gray-800 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <span className="hidden lg:block text-sm font-medium text-gray-700 dark:text-gray-300 max-w-24 truncate group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                      {user?.fullName || user?.username || t('header.user')}
                    </span>
                    <ChevronDown className={`w-4 h-4 text-gray-500 dark:text-gray-400 transition-all duration-300 group-hover:text-blue-500 ${isProfileOpen ? 'rotate-180' : ''}`} />
                  </button>

                  {/* قائمة منسدلة محسنة للمستخدم */}
                  {isProfileOpen && (
                    <>
                      {/* خلفية شفافة */}
                      <div className="fixed inset-0 z-40" onClick={() => setIsProfileOpen(false)} />

                      <div className="absolute ltr:right-0 rtl:left-0 mt-3 w-64 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 py-3 z-50 animate-in slide-in-from-top-2 duration-200">
                        {/* معلومات المستخدم */}
                        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                          <div className="flex items-center space-x-3 rtl:space-x-reverse">
                            <div className="relative w-10 h-10 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/30 dark:to-primary-800/30 rounded-full flex items-center justify-center">
                              <User className="w-5 h-5 text-primary-600 dark:text-primary-400" />
                              {user?.isVerified && (
                                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center">
                                  <Shield className="w-2.5 h-2.5 text-white" />
                                </div>
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                                {user?.fullName || user?.username || t('header.user')}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{user?.email || t('header.profile')}</p>
                              {user?.isVerified && (
                                <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 mt-1">
                                  <Shield className="w-3 h-3 ltr:mr-1 rtl:ml-1" />
                                  {t('header.verified')}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* رابط لوحة التحكم */}
                        <div className="px-2 py-2">
                          <a href="/user-dashboard" className="flex items-center px-3 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 rounded-xl transition-all duration-200 group">
                            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                              <BarChart3 className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div className="ltr:ml-3 rtl:mr-3">
                              <p className="font-semibold text-gray-900 dark:text-white">{t('navigation.dashboard')}</p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">إدارة حسابك وجميع أنشطتك</p>
                            </div>
                          </a>
                        </div>

                        <hr className="my-2 border-gray-200 dark:border-gray-600" />

                        {/* زر تسجيل الخروج */}
                        <div className="px-2 pb-2">
                          <button
                            onClick={logout}
                            className="flex items-center w-full px-3 py-3 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-xl transition-all duration-200 group"
                          >
                            <div className="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                              <LogOut className="w-4 h-4 text-red-600 dark:text-red-400" />
                            </div>
                            <span className="ltr:ml-3 rtl:mr-3 font-medium">{t('navigation.logout')}</span>
                          </button>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            ) : (
              <AuthButton variant="header" />
            )}

            {/* زر القائمة المحسن للجوال مع تحسينات الأداء */}
            <button
              onClick={toggleMenu}
              className="lg:hidden p-2.5 text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-all duration-300 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900/20 dark:hover:to-purple-900/20 mobile-menu-container group border border-transparent hover:border-blue-200 dark:hover:border-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
              aria-label={isMenuOpen ? t('common.close') : t('navigation.menu')}
              aria-expanded={isMenuOpen}
            >
              <div className="relative w-6 h-6">
                <Menu className={`absolute inset-0 w-6 h-6 transition-all duration-300 ${isMenuOpen ? 'opacity-0 rotate-180 scale-75' : 'opacity-100 rotate-0 scale-100'}`} />
                <X className={`absolute inset-0 w-6 h-6 transition-all duration-300 ${isMenuOpen ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 -rotate-180 scale-75'}`} />
                {/* تأثير النبضة عند الفتح */}
                {isMenuOpen && (
                  <div className="absolute inset-0 bg-blue-500/20 rounded-full animate-ping"></div>
                )}
              </div>
            </button>
          </div>
        </div>

        {/* القائمة المنسدلة المحسنة للجوال مع تحسينات الأداء */}
        {isMenuOpen && (
          <div className="lg:hidden border-t border-gray-200 dark:border-gray-700 bg-white/98 dark:bg-gray-900/98 backdrop-blur-md mobile-menu-container animate-in slide-in-from-top-2 duration-300 shadow-lg">
            <div className="px-4 py-6">
              {/* اختيار اللغة للجوال مع تحسينات */}
              <div className="sm:hidden mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 rounded-xl border border-gray-200 dark:border-gray-700">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full ltr:mr-2 rtl:ml-2 animate-pulse"></span>
                    {t('header.language')}
                  </span>
                  <LanguageSelector />
                </div>
              </div>

              {/* عرض حالة المحفظة للجوال */}
              <div className="md:hidden mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
                <UnifiedWalletManager
                  variant="mobile"
                  showBalance={false}
                  showNetwork={true}
                  showActions={true}
                  showConnectionOptions={true}
                />
              </div>

              <nav className="space-y-2" role="navigation" aria-label={t('navigation.menu')}>
                {navigationItems.map((item, index) => (
                  <a
                    key={item.href}
                    href={item.href}
                    className="flex items-center space-x-3 rtl:space-x-reverse px-4 py-4 text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900/20 dark:hover:to-purple-900/20 font-medium transition-all duration-300 body-arabic rounded-xl border border-transparent hover:border-blue-200 dark:hover:border-blue-800 group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
                    onClick={() => setIsMenuOpen(false)}
                    style={{ animationDelay: `${index * 50}ms` }}
                    aria-label={item.label}
                  >
                    <div className="relative w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30">
                      <item.icon className="w-5 h-5 opacity-70 group-hover:opacity-100 transition-opacity duration-300" />
                      {/* مؤشر الأولوية للعناصر المهمة */}
                      {item.priority <= 2 && (
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      )}
                    </div>
                    <span className="flex-1">{item.label}</span>
                    <ChevronDown className="w-4 h-4 opacity-50 ltr:rotate-[-90deg] rtl:rotate-90 group-hover:opacity-100 transition-all duration-300" />
                  </a>
                ))}
              </nav>

              {/* أزرار تسجيل الدخول للجوال */}
              {!isAuthenticated && (
                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <AuthButton variant="mobile" />
                </div>
              )}

              {/* معلومات المستخدم المحسنة للجوال مع تحسينات */}
              {isAuthenticated && (
                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl border border-blue-200 dark:border-blue-800 shadow-sm">
                    <div className="relative w-12 h-12 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/30 dark:to-primary-800/30 rounded-full flex items-center justify-center shadow-sm">
                      <User className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                      {user?.isVerified && (
                        <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center animate-pulse">
                          <Shield className="w-2.5 h-2.5 text-white" />
                        </div>
                      )}
                      {/* مؤشر الحالة النشطة */}
                      <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-semibold text-gray-900 dark:text-white truncate">{user?.fullName || user?.username || t('header.user')}</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400 truncate">{user?.email || t('header.profile')}</p>
                      {user?.isVerified && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 mt-1">
                          <Shield className="w-3 h-3 ltr:mr-1 rtl:ml-1" />
                          {t('header.verified')}
                        </span>
                      )}
                    </div>
                  </div>
                  <button
                    className="flex items-center space-x-3 rtl:space-x-reverse w-full px-4 py-4 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 font-medium transition-all duration-300 rounded-xl border border-transparent hover:border-red-200 dark:hover:border-red-800 group focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
                    onClick={() => {
                      logout();
                      setIsMenuOpen(false);
                    }}
                    aria-label={t('navigation.logout')}
                  >
                    <div className="relative w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <LogOut className="w-5 h-5" />
                      {/* تأثير التحذير */}
                      <div className="absolute inset-0 bg-red-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <span className="flex-1 text-left rtl:text-right">{t('navigation.logout')}</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </header>
  );
}