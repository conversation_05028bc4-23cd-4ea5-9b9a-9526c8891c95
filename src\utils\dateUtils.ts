// دوال مساعدة للتاريخ والوقت

// تنسيق التاريخ حسب الدولة واللغة
export const formatDateByCountry = (date: Date | string, countryCode: string, language: string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // تحديد المنطقة الزمنية والتنسيق حسب الدولة
  const countryFormats: { [key: string]: { locale: string; timeZone: string } } = {
    'SA': { locale: 'ar-SA', timeZone: 'Asia/Riyadh' },
    'AE': { locale: 'ar-AE', timeZone: 'Asia/Dubai' },
    'KW': { locale: 'ar-KW', timeZone: 'Asia/Kuwait' },
    'QA': { locale: 'ar-QA', timeZone: 'Asia/Qatar' },
    'BH': { locale: 'ar-BH', timeZone: 'Asia/Bahrain' },
    'OM': { locale: 'ar-OM', timeZone: 'Asia/Muscat' },
    'JO': { locale: 'ar-JO', timeZone: 'Asia/Amman' },
    'LB': { locale: 'ar-LB', timeZone: 'Asia/Beirut' },
    'EG': { locale: 'ar-EG', timeZone: 'Africa/Cairo' },
    'MA': { locale: 'ar-MA', timeZone: 'Africa/Casablanca' }
  };

  const format = countryFormats[countryCode] || { locale: 'en-US', timeZone: 'UTC' };
  
  // استخدام اللغة المحددة
  const locale = language === 'ar' ? format.locale : 'en-US';
  
  try {
    return dateObj.toLocaleDateString(locale, {
      timeZone: format.timeZone,
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      calendar: 'gregory' // التقويم الميلادي دائماً
    });
  } catch (error) {
    // في حالة الخطأ، استخدم التنسيق الافتراضي
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
};

// تنسيق التاريخ والوقت معاً
export const formatDateTimeByCountry = (date: Date | string, countryCode: string, language: string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const countryFormats: { [key: string]: { locale: string; timeZone: string } } = {
    'SA': { locale: 'ar-SA', timeZone: 'Asia/Riyadh' },
    'AE': { locale: 'ar-AE', timeZone: 'Asia/Dubai' },
    'KW': { locale: 'ar-KW', timeZone: 'Asia/Kuwait' },
    'QA': { locale: 'ar-QA', timeZone: 'Asia/Qatar' },
    'BH': { locale: 'ar-BH', timeZone: 'Asia/Bahrain' },
    'OM': { locale: 'ar-OM', timeZone: 'Asia/Muscat' },
    'JO': { locale: 'ar-JO', timeZone: 'Asia/Amman' },
    'LB': { locale: 'ar-LB', timeZone: 'Asia/Beirut' },
    'EG': { locale: 'ar-EG', timeZone: 'Africa/Cairo' },
    'MA': { locale: 'ar-MA', timeZone: 'Africa/Casablanca' }
  };

  const format = countryFormats[countryCode] || { locale: 'en-US', timeZone: 'UTC' };
  const locale = language === 'ar' ? format.locale : 'en-US';
  
  try {
    return dateObj.toLocaleString(locale, {
      timeZone: format.timeZone,
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      calendar: 'gregory'
    });
  } catch (error) {
    return dateObj.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
};

// تنسيق التاريخ المختصر
export const formatShortDate = (date: Date | string, language: string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const locale = language === 'ar' ? 'ar-SA' : 'en-US';
  
  try {
    return dateObj.toLocaleDateString(locale, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      calendar: 'gregory'
    });
  } catch (error) {
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  }
};

// حساب الوقت المنقضي (منذ كم من الوقت)
export const getTimeAgo = (date: Date | string, language: string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
  
  const intervals = {
    ar: {
      year: 'سنة',
      years: 'سنوات',
      month: 'شهر',
      months: 'أشهر',
      week: 'أسبوع',
      weeks: 'أسابيع',
      day: 'يوم',
      days: 'أيام',
      hour: 'ساعة',
      hours: 'ساعات',
      minute: 'دقيقة',
      minutes: 'دقائق',
      second: 'ثانية',
      seconds: 'ثوان',
      ago: 'منذ',
      now: 'الآن'
    },
    en: {
      year: 'year',
      years: 'years',
      month: 'month',
      months: 'months',
      week: 'week',
      weeks: 'weeks',
      day: 'day',
      days: 'days',
      hour: 'hour',
      hours: 'hours',
      minute: 'minute',
      minutes: 'minutes',
      second: 'second',
      seconds: 'seconds',
      ago: 'ago',
      now: 'now'
    }
  };
  
  const t = intervals[language as keyof typeof intervals] || intervals.en;
  
  if (diffInSeconds < 60) {
    return language === 'ar' ? t.now : `${diffInSeconds} ${diffInSeconds === 1 ? t.second : t.seconds} ${t.ago}`;
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return language === 'ar' 
      ? `${t.ago} ${diffInMinutes} ${diffInMinutes === 1 ? t.minute : t.minutes}`
      : `${diffInMinutes} ${diffInMinutes === 1 ? t.minute : t.minutes} ${t.ago}`;
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return language === 'ar'
      ? `${t.ago} ${diffInHours} ${diffInHours === 1 ? t.hour : t.hours}`
      : `${diffInHours} ${diffInHours === 1 ? t.hour : t.hours} ${t.ago}`;
  }
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return language === 'ar'
      ? `${t.ago} ${diffInDays} ${diffInDays === 1 ? t.day : t.days}`
      : `${diffInDays} ${diffInDays === 1 ? t.day : t.days} ${t.ago}`;
  }
  
  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return language === 'ar'
      ? `${t.ago} ${diffInWeeks} ${diffInWeeks === 1 ? t.week : t.weeks}`
      : `${diffInWeeks} ${diffInWeeks === 1 ? t.week : t.weeks} ${t.ago}`;
  }
  
  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return language === 'ar'
      ? `${t.ago} ${diffInMonths} ${diffInMonths === 1 ? t.month : t.months}`
      : `${diffInMonths} ${diffInMonths === 1 ? t.month : t.months} ${t.ago}`;
  }
  
  const diffInYears = Math.floor(diffInDays / 365);
  return language === 'ar'
    ? `${t.ago} ${diffInYears} ${diffInYears === 1 ? t.year : t.years}`
    : `${diffInYears} ${diffInYears === 1 ? t.year : t.years} ${t.ago}`;
};

// تحديد الدولة من رمز العملة
export const getCountryFromCurrency = (currency: string): string => {
  const currencyToCountry: { [key: string]: string } = {
    'SAR': 'SA',
    'AED': 'AE',
    'KWD': 'KW',
    'QAR': 'QA',
    'BHD': 'BH',
    'OMR': 'OM',
    'JOD': 'JO',
    'LBP': 'LB',
    'EGP': 'EG',
    'MAD': 'MA'
  };
  
  return currencyToCountry[currency] || 'SA'; // افتراضي السعودية
};
