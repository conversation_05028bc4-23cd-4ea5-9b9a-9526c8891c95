@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    إدارة مجلد node_modules
echo    Node Modules Management
echo ========================================
echo.

:menu
echo اختر العملية المطلوبة:
echo Choose operation:
echo.
echo [1] حذف node_modules (Delete node_modules)
echo [2] نقل إلى مجلد خارجي (Move to external folder)
echo [3] إنشاء رابط رمزي (Create symbolic link)
echo [4] استعادة من المجلد الخارجي (Restore from external)
echo [5] تثبيت التبعيات (Install dependencies)
echo [6] عرض حجم المجلد (Show folder size)
echo [0] خروج (Exit)
echo.

set /p choice="أدخل اختيارك (Enter choice): "

if "%choice%"=="1" goto delete_modules
if "%choice%"=="2" goto move_modules
if "%choice%"=="3" goto create_link
if "%choice%"=="4" goto restore_modules
if "%choice%"=="5" goto install_deps
if "%choice%"=="6" goto show_size
if "%choice%"=="0" goto exit
goto menu

:delete_modules
echo.
echo جاري حذف node_modules...
echo Deleting node_modules...
if exist node_modules (
    rmdir /s /q node_modules
    echo ✅ تم حذف node_modules بنجاح
    echo ✅ node_modules deleted successfully
) else (
    echo ⚠️ مجلد node_modules غير موجود
    echo ⚠️ node_modules folder not found
)
pause
goto menu

:move_modules
echo.
echo جاري نقل node_modules إلى مجلد خارجي...
echo Moving node_modules to external folder...

if not exist node_modules (
    echo ❌ مجلد node_modules غير موجود
    echo ❌ node_modules folder not found
    pause
    goto menu
)

if not exist "..\node_modules_storage" mkdir "..\node_modules_storage"

if exist "..\node_modules_storage\ikaros-p2p-node_modules" (
    echo ⚠️ المجلد الخارجي موجود مسبقاً، جاري الاستبدال...
    echo ⚠️ External folder exists, replacing...
    rmdir /s /q "..\node_modules_storage\ikaros-p2p-node_modules"
)

move node_modules "..\node_modules_storage\ikaros-p2p-node_modules"
echo ✅ تم نقل node_modules بنجاح
echo ✅ node_modules moved successfully
pause
goto menu

:create_link
echo.
echo جاري إنشاء رابط رمزي...
echo Creating symbolic link...

if exist node_modules (
    echo ❌ مجلد node_modules موجود، احذفه أولاً
    echo ❌ node_modules exists, delete it first
    pause
    goto menu
)

if not exist "..\node_modules_storage\ikaros-p2p-node_modules" (
    echo ❌ المجلد الخارجي غير موجود
    echo ❌ External folder not found
    pause
    goto menu
)

mklink /D node_modules "..\node_modules_storage\ikaros-p2p-node_modules"
echo ✅ تم إنشاء الرابط الرمزي بنجاح
echo ✅ Symbolic link created successfully
pause
goto menu

:restore_modules
echo.
echo جاري استعادة node_modules...
echo Restoring node_modules...

if exist node_modules (
    echo ⚠️ مجلد node_modules موجود، جاري الحذف...
    echo ⚠️ node_modules exists, deleting...
    rmdir /s /q node_modules
)

if not exist "..\node_modules_storage\ikaros-p2p-node_modules" (
    echo ❌ المجلد الخارجي غير موجود
    echo ❌ External folder not found
    pause
    goto menu
)

move "..\node_modules_storage\ikaros-p2p-node_modules" node_modules
echo ✅ تم استعادة node_modules بنجاح
echo ✅ node_modules restored successfully
pause
goto menu

:install_deps
echo.
echo جاري تثبيت التبعيات...
echo Installing dependencies...
npm install
echo ✅ تم تثبيت التبعيات بنجاح
echo ✅ Dependencies installed successfully
pause
goto menu

:show_size
echo.
echo جاري حساب حجم المجلدات...
echo Calculating folder sizes...
echo.

if exist node_modules (
    echo 📁 حجم node_modules الحالي:
    echo 📁 Current node_modules size:
    dir node_modules /s /-c | find "File(s)"
) else (
    echo ⚠️ مجلد node_modules غير موجود
    echo ⚠️ node_modules folder not found
)

if exist "..\node_modules_storage\ikaros-p2p-node_modules" (
    echo.
    echo 📁 حجم المجلد الخارجي:
    echo 📁 External folder size:
    dir "..\node_modules_storage\ikaros-p2p-node_modules" /s /-c | find "File(s)"
)

echo.
pause
goto menu

:exit
echo.
echo شكراً لاستخدام أداة إدارة node_modules
echo Thank you for using node_modules management tool
echo.
pause
exit
