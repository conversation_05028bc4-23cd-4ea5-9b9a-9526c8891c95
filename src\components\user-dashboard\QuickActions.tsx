'use client';

import { 
  Plus,
  Search,
  MessageSquare,
  Star,
  Settings,
  Shield,
  TrendingUp,
  Wallet,
  Bell,
  HelpCircle,
  FileText,
  Users,
  BarChart3,
  Zap
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  href: string;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  isNew?: boolean;
  isPro?: boolean;
  badge?: string | number;
}

export default function QuickActions() {
  const { t } = useUserDashboardTranslation();

  const quickActions: QuickAction[] = [
    {
      id: 'create-offer',
      title: 'إنشاء عرض جديد',
      description: 'أنشئ عرض شراء أو بيع',
      href: '/user-dashboard/offers/create',
      icon: Plus,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30',
      isNew: true
    },
    {
      id: 'browse-offers',
      title: 'تصفح العروض',
      description: 'ابحث عن أفضل العروض',
      href: '/offers',
      icon: Search,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30'
    },
    {
      id: 'wallet-manage',
      title: 'إدارة المحفظة',
      description: 'إيداع وسحب الأموال',
      href: '/user-dashboard/wallet',
      icon: Wallet,
      color: 'text-purple-600 dark:text-purple-400',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30'
    },
    {
      id: 'verify-account',
      title: 'التحقق من الحساب',
      description: 'أكمل التحقق لزيادة الثقة',
      href: '/user-dashboard/profile/verification',
      icon: Shield,
      color: 'text-orange-600 dark:text-orange-400',
      bgColor: 'bg-orange-50 dark:bg-orange-900/20 hover:bg-orange-100 dark:hover:bg-orange-900/30',
      isNew: true
    },
    {
      id: 'analytics',
      title: 'التحليلات المتقدمة',
      description: 'تحليل أداء التداول',
      href: '/user-dashboard/analytics',
      icon: BarChart3,
      color: 'text-indigo-600 dark:text-indigo-400',
      bgColor: 'bg-indigo-50 dark:bg-indigo-900/20 hover:bg-indigo-100 dark:hover:bg-indigo-900/30',
      isPro: true
    },
    {
      id: 'support',
      title: 'الدعم الفني',
      description: 'تواصل مع فريق الدعم',
      href: '/support',
      icon: HelpCircle,
      color: 'text-gray-600 dark:text-gray-400',
      bgColor: 'bg-gray-50 dark:bg-gray-900/20 hover:bg-gray-100 dark:hover:bg-gray-900/30'
    }
  ];

  const popularActions: QuickAction[] = [
    {
      id: 'notifications',
      title: 'الإشعارات',
      description: 'إدارة التنبيهات',
      href: '/user-dashboard/notifications',
      icon: Bell,
      color: 'text-yellow-600 dark:text-yellow-400',
      bgColor: 'bg-yellow-50 dark:bg-yellow-900/20 hover:bg-yellow-100 dark:hover:bg-yellow-900/30',
      badge: 3
    },
    {
      id: 'reviews',
      title: 'التقييمات',
      description: 'عرض وإدارة التقييمات',
      href: '/user-dashboard/reviews',
      icon: Star,
      color: 'text-pink-600 dark:text-pink-400',
      bgColor: 'bg-pink-50 dark:bg-pink-900/20 hover:bg-pink-100 dark:hover:bg-pink-900/30'
    },
    {
      id: 'settings',
      title: 'الإعدادات',
      description: 'تخصيص حسابك',
      href: '/user-dashboard/settings',
      icon: Settings,
      color: 'text-gray-600 dark:text-gray-400',
      bgColor: 'bg-gray-50 dark:bg-gray-900/20 hover:bg-gray-100 dark:hover:bg-gray-900/30'
    }
  ];

  const ActionCard = ({ action }: { action: QuickAction }) => (
    <a
      href={action.href}
      className={`block p-4 rounded-lg border border-gray-200 dark:border-gray-600 transition-all duration-200 group ${action.bgColor}`}
    >
      <div className="flex items-start space-x-3 rtl:space-x-reverse">
        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${action.bgColor.replace('hover:', '').replace('dark:hover:', 'dark:')} group-hover:scale-110 transition-transform duration-200`}>
          <action.icon className={`w-5 h-5 ${action.color}`} />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {action.title}
            </h4>
            {action.badge && (
              <span className="bg-red-500 text-white text-xs font-bold rounded-full min-w-[16px] h-[16px] flex items-center justify-center">
                {action.badge}
              </span>
            )}
            {action.isNew && (
              <span className="bg-green-500 text-white text-xs font-bold px-1.5 py-0.5 rounded-full">
                جديد
              </span>
            )}
            {action.isPro && (
              <span className="bg-purple-500 text-white text-xs font-bold px-1.5 py-0.5 rounded-full">
                Pro
              </span>
            )}
          </div>
          <p className="text-xs text-gray-600 dark:text-gray-400">
            {action.description}
          </p>
        </div>
      </div>
    </a>
  );

  return (
    <div className="space-y-6">
      {/* الإجراءات السريعة الرئيسية */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2 rtl:space-x-reverse mb-4">
          <Zap className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            إجراءات سريعة
          </h3>
        </div>
        
        <div className="grid grid-cols-1 gap-3">
          {quickActions.slice(0, 4).map((action) => (
            <ActionCard key={action.id} action={action} />
          ))}
        </div>
      </div>

      {/* الإجراءات الشائعة */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2 rtl:space-x-reverse mb-4">
          <TrendingUp className="w-5 h-5 text-green-600 dark:text-green-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            الأكثر استخداماً
          </h3>
        </div>
        
        <div className="grid grid-cols-1 gap-3">
          {popularActions.map((action) => (
            <ActionCard key={action.id} action={action} />
          ))}
        </div>
      </div>

      {/* إجراءات إضافية */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
        <div className="flex items-center space-x-2 rtl:space-x-reverse mb-4">
          <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-300">
            المجتمع والدعم
          </h3>
        </div>
        
        <div className="grid grid-cols-1 gap-3">
          <a
            href="/community"
            className="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg hover:bg-white/70 dark:hover:bg-gray-800/70 transition-colors"
          >
            <MessageSquare className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            <div>
              <p className="text-sm font-medium text-blue-900 dark:text-blue-300">
                انضم للمجتمع
              </p>
              <p className="text-xs text-blue-700 dark:text-blue-400">
                تفاعل مع المتداولين الآخرين
              </p>
            </div>
          </a>
          
          <a
            href="/help"
            className="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-white/50 dark:bg-gray-800/50 rounded-lg hover:bg-white/70 dark:hover:bg-gray-800/70 transition-colors"
          >
            <FileText className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            <div>
              <p className="text-sm font-medium text-blue-900 dark:text-blue-300">
                مركز المساعدة
              </p>
              <p className="text-xs text-blue-700 dark:text-blue-400">
                أدلة وأسئلة شائعة
              </p>
            </div>
          </a>
        </div>
      </div>
    </div>
  );
}
