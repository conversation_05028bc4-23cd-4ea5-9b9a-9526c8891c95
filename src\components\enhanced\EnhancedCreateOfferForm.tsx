'use client';

import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  DollarSign, 
  Clock, 
  AlertCircle, 
  CheckCircle, 
  Loader2,
  Calculator,
  Info
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { enhancedContractApiService } from '@/services/enhancedContractApiService';
import { enhancedContractService } from '@/services/enhancedContractService';
import NetworkTokenSelector from './NetworkTokenSelector';

interface TokenInfo {
  id: number;
  network_id: number;
  token_address: string;
  token_symbol: string;
  token_name: string;
  decimals: number;
  is_stablecoin: boolean;
  is_active: boolean;
  icon_url?: string;
  min_trade_amount: number;
  max_trade_amount: number;
  platform_fee_rate: number;
}

interface EnhancedCreateOfferFormProps {
  userId: number;
  onSuccess?: (offerId: number) => void;
  onCancel?: () => void;
  className?: string;
}

const EnhancedCreateOfferForm: React.FC<EnhancedCreateOfferFormProps> = ({
  userId,
  onSuccess,
  onCancel,
  className = ''
}) => {
  const { t, isRTL } = useTranslation();
  
  // حالة النموذج
  const [formData, setFormData] = useState({
    offerType: 'sell' as 'buy' | 'sell',
    networkId: 0,
    tokenId: 0,
    amount: '',
    minAmount: '',
    maxAmount: '',
    price: '',
    currency: 'SAR',
    paymentMethods: [] as string[],
    terms: '',
    autoReply: '',
    timeLimit: 1800
  });

  // حالة العملة المختارة
  const [selectedToken, setSelectedToken] = useState<TokenInfo | null>(null);
  
  // حالة التحميل والأخطاء
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // حالة التحقق من الرصيد والموافقة
  const [userBalance, setUserBalance] = useState<string>('0');
  const [tokenAllowance, setTokenAllowance] = useState<string>('0');
  const [needsApproval, setNeedsApproval] = useState(false);
  
  // حالة الحسابات
  const [calculations, setCalculations] = useState({
    totalValue: 0,
    platformFee: 0,
    netAmount: 0
  });

  // طرق الدفع المتاحة
  const availablePaymentMethods = [
    'bank_transfer',
    'cash',
    'paypal',
    'wise',
    'western_union',
    'moneygram'
  ];

  // العملات المدعومة
  const supportedCurrencies = [
    { code: 'SAR', name: 'ريال سعودي' },
    { code: 'USD', name: 'دولار أمريكي' },
    { code: 'EUR', name: 'يورو' },
    { code: 'GBP', name: 'جنيه إسترليني' },
    { code: 'AED', name: 'درهم إماراتي' }
  ];

  // تحديث الحسابات عند تغيير المبلغ أو السعر أو العملة
  useEffect(() => {
    if (formData.amount && formData.price && selectedToken) {
      const amount = parseFloat(formData.amount);
      const price = parseFloat(formData.price);
      const totalValue = amount * price;
      const platformFee = amount * selectedToken.platform_fee_rate;
      const netAmount = amount - platformFee;

      setCalculations({
        totalValue,
        platformFee,
        netAmount
      });
    } else {
      setCalculations({
        totalValue: 0,
        platformFee: 0,
        netAmount: 0
      });
    }
  }, [formData.amount, formData.price, selectedToken]);

  // جلب رصيد المستخدم والموافقة عند تغيير العملة
  useEffect(() => {
    if (selectedToken && enhancedContractService.isConnected()) {
      loadUserBalanceAndAllowance();
    }
  }, [selectedToken]);

  // تحديث حالة الموافقة عند تغيير المبلغ
  useEffect(() => {
    if (selectedToken && formData.amount && formData.offerType === 'sell') {
      const amount = parseFloat(formData.amount);
      const allowance = parseFloat(tokenAllowance);
      setNeedsApproval(amount > allowance);
    } else {
      setNeedsApproval(false);
    }
  }, [formData.amount, formData.offerType, tokenAllowance, selectedToken]);

  const loadUserBalanceAndAllowance = async () => {
    if (!selectedToken) return;

    try {
      const userAddress = await getCurrentUserAddress();
      
      // جلب الرصيد
      const balance = await enhancedContractService.getTokenBalance(
        selectedToken.token_address,
        userAddress
      );
      setUserBalance(balance);

      // جلب الموافقة (للبائعين فقط)
      if (formData.offerType === 'sell') {
        const allowance = await enhancedContractService.getTokenAllowance(
          selectedToken.token_address,
          userAddress
        );
        setTokenAllowance(allowance);
      }
    } catch (error) {
      console.error('خطأ في جلب الرصيد والموافقة:', error);
    }
  };

  const getCurrentUserAddress = async (): Promise<string> => {
    if (typeof window !== 'undefined' && window.ethereum) {
      const accounts = await window.ethereum.request({ method: 'eth_accounts' });
      if (accounts.length > 0) {
        return accounts[0];
      }
    }
    throw new Error('لا يوجد حساب متصل');
  };

  const handleNetworkChange = (networkId: number) => {
    setFormData(prev => ({ ...prev, networkId, tokenId: 0 }));
    setSelectedToken(null);
  };

  const handleTokenChange = (tokenId: number, tokenInfo: TokenInfo) => {
    setFormData(prev => ({ 
      ...prev, 
      tokenId,
      minAmount: tokenInfo.min_trade_amount.toString(),
      maxAmount: tokenInfo.max_trade_amount.toString()
    }));
    setSelectedToken(tokenInfo);
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError(null);
  };

  const handlePaymentMethodToggle = (method: string) => {
    setFormData(prev => ({
      ...prev,
      paymentMethods: prev.paymentMethods.includes(method)
        ? prev.paymentMethods.filter(m => m !== method)
        : [...prev.paymentMethods, method]
    }));
  };

  const validateForm = (): string | null => {
    if (!formData.networkId) return 'يرجى اختيار الشبكة';
    if (!formData.tokenId || !selectedToken) return 'يرجى اختيار العملة';
    if (!formData.amount) return 'يرجى إدخال المبلغ';
    if (!formData.price) return 'يرجى إدخال السعر';
    if (formData.paymentMethods.length === 0) return 'يرجى اختيار طريقة دفع واحدة على الأقل';

    const amount = parseFloat(formData.amount);
    const minAmount = parseFloat(formData.minAmount);
    const maxAmount = parseFloat(formData.maxAmount);

    if (amount < selectedToken.min_trade_amount) {
      return `المبلغ أقل من الحد الأدنى (${selectedToken.min_trade_amount} ${selectedToken.token_symbol})`;
    }

    if (amount > selectedToken.max_trade_amount) {
      return `المبلغ أكبر من الحد الأقصى (${selectedToken.max_trade_amount} ${selectedToken.token_symbol})`;
    }

    if (minAmount > amount) {
      return 'الحد الأدنى لا يمكن أن يكون أكبر من المبلغ الإجمالي';
    }

    if (maxAmount < amount) {
      return 'الحد الأقصى لا يمكن أن يكون أقل من المبلغ الإجمالي';
    }

    // التحقق من الرصيد للبائعين
    if (formData.offerType === 'sell') {
      const userBalanceNum = parseFloat(userBalance);
      if (amount > userBalanceNum) {
        return `رصيدك غير كافي. الرصيد المتاح: ${userBalance} ${selectedToken.token_symbol}`;
      }
    }

    return null;
  };

  const handleApproveToken = async () => {
    if (!selectedToken) return;

    try {
      setLoading(true);
      setError(null);

      const txHash = await enhancedContractService.approveToken(
        selectedToken.token_address,
        formData.amount
      );

      setSuccess('تم إرسال معاملة الموافقة. يرجى انتظار التأكيد...');

      // انتظار تأكيد المعاملة
      await waitForTransaction(txHash);

      // إعادة جلب الموافقة
      await loadUserBalanceAndAllowance();

      setSuccess('تم منح الموافقة بنجاح!');
    } catch (error) {
      setError(`خطأ في منح الموافقة: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    if (needsApproval) {
      setError('يرجى منح الموافقة للعملة أولاً');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const offerData = {
        userId,
        offerType: formData.offerType,
        networkId: formData.networkId,
        tokenId: formData.tokenId,
        amount: formData.amount,
        minAmount: formData.minAmount,
        maxAmount: formData.maxAmount,
        price: formData.price,
        currency: formData.currency,
        paymentMethods: formData.paymentMethods,
        terms: formData.terms,
        autoReply: formData.autoReply,
        timeLimit: formData.timeLimit
      };

      const result = await enhancedContractApiService.createEnhancedOfferWithContract(offerData);

      if (result.success) {
        setSuccess(result.message);
        if (onSuccess && result.data?.offerId) {
          onSuccess(result.data.offerId);
        }
      } else {
        setError(result.message || 'فشل في إنشاء العرض');
      }
    } catch (error) {
      setError(`خطأ في إنشاء العرض: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const waitForTransaction = async (txHash: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const checkTransaction = async () => {
        try {
          const provider = enhancedContractService['provider'];
          if (!provider) {
            reject(new Error('المزود غير متوفر'));
            return;
          }

          const receipt = await provider.getTransactionReceipt(txHash);
          if (receipt) {
            if (receipt.status === 1) {
              resolve();
            } else {
              reject(new Error('فشلت المعاملة'));
            }
          } else {
            setTimeout(checkTransaction, 2000);
          }
        } catch (error) {
          reject(error);
        }
      };

      checkTransaction();
    });
  };

  return (
    <div className={`max-w-2xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg ${className}`}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {t('createEnhancedOffer')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {t('createOfferDescription')}
        </p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <AlertCircle className="w-5 h-5" />
            <span className="text-sm">{error}</span>
          </div>
        </div>
      )}

      {success && (
        <div className="mb-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center gap-2 text-green-600 dark:text-green-400">
            <CheckCircle className="w-5 h-5" />
            <span className="text-sm">{success}</span>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* نوع العرض */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            {t('offerType')}
          </label>
          <div className="grid grid-cols-2 gap-4">
            <button
              type="button"
              onClick={() => handleInputChange('offerType', 'buy')}
              className={`p-4 border-2 rounded-lg text-center transition-colors ${
                formData.offerType === 'buy'
                  ? 'border-green-500 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                  : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
              }`}
            >
              <div className="font-medium">{t('buyOffer')}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {t('buyOfferDescription')}
              </div>
            </button>
            <button
              type="button"
              onClick={() => handleInputChange('offerType', 'sell')}
              className={`p-4 border-2 rounded-lg text-center transition-colors ${
                formData.offerType === 'sell'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                  : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
              }`}
            >
              <div className="font-medium">{t('sellOffer')}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {t('sellOfferDescription')}
              </div>
            </button>
          </div>
        </div>

        {/* اختيار الشبكة والعملة */}
        <NetworkTokenSelector
          selectedNetworkId={formData.networkId || undefined}
          selectedTokenId={formData.tokenId || undefined}
          onNetworkChange={handleNetworkChange}
          onTokenChange={handleTokenChange}
          disabled={loading}
          showOnlyStablecoins={true}
        />

        {/* معلومات الرصيد (للبائعين) */}
        {formData.offerType === 'sell' && selectedToken && (
          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {t('yourBalance')}:
              </span>
              <span className="text-sm font-bold text-gray-900 dark:text-white">
                {userBalance} {selectedToken.token_symbol}
              </span>
            </div>
            {needsApproval && (
              <div className="mt-3">
                <button
                  type="button"
                  onClick={handleApproveToken}
                  disabled={loading}
                  className="w-full px-4 py-2 bg-yellow-500 hover:bg-yellow-600 disabled:opacity-50 text-white rounded-lg transition-colors"
                >
                  {loading ? (
                    <Loader2 className="w-4 h-4 animate-spin mx-auto" />
                  ) : (
                    t('approveToken')
                  )}
                </button>
                <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                  {t('approveTokenDescription')}
                </p>
              </div>
            )}
          </div>
        )}

        {/* المبلغ والسعر */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('amount')} {selectedToken && `(${selectedToken.token_symbol})`}
            </label>
            <input
              type="number"
              step="0.000001"
              value={formData.amount}
              onChange={(e) => handleInputChange('amount', e.target.value)}
              disabled={loading}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              placeholder={selectedToken ? `${selectedToken.min_trade_amount} - ${selectedToken.max_trade_amount}` : '0.00'}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('pricePerUnit')} ({formData.currency})
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.price}
              onChange={(e) => handleInputChange('price', e.target.value)}
              disabled={loading}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              placeholder="0.00"
            />
          </div>
        </div>

        {/* الحدود الدنيا والعليا */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('minAmount')} {selectedToken && `(${selectedToken.token_symbol})`}
            </label>
            <input
              type="number"
              step="0.000001"
              value={formData.minAmount}
              onChange={(e) => handleInputChange('minAmount', e.target.value)}
              disabled={loading}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('maxAmount')} {selectedToken && `(${selectedToken.token_symbol})`}
            </label>
            <input
              type="number"
              step="0.000001"
              value={formData.maxAmount}
              onChange={(e) => handleInputChange('maxAmount', e.target.value)}
              disabled={loading}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            />
          </div>
        </div>

        {/* العملة */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('currency')}
          </label>
          <select
            value={formData.currency}
            onChange={(e) => handleInputChange('currency', e.target.value)}
            disabled={loading}
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            {supportedCurrencies.map((currency) => (
              <option key={currency.code} value={currency.code}>
                {currency.name} ({currency.code})
              </option>
            ))}
          </select>
        </div>

        {/* طرق الدفع */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            {t('paymentMethods')}
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {availablePaymentMethods.map((method) => (
              <button
                key={method}
                type="button"
                onClick={() => handlePaymentMethodToggle(method)}
                disabled={loading}
                className={`p-3 border-2 rounded-lg text-center transition-colors ${
                  formData.paymentMethods.includes(method)
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                }`}
              >
                <div className="text-sm font-medium">
                  {t(`paymentMethod.${method}`)}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* الشروط والأحكام */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('terms')} ({t('optional')})
          </label>
          <textarea
            value={formData.terms}
            onChange={(e) => handleInputChange('terms', e.target.value)}
            disabled={loading}
            rows={3}
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            placeholder={t('termsPlaceholder')}
          />
        </div>

        {/* الرد التلقائي */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t('autoReply')} ({t('optional')})
          </label>
          <textarea
            value={formData.autoReply}
            onChange={(e) => handleInputChange('autoReply', e.target.value)}
            disabled={loading}
            rows={2}
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            placeholder={t('autoReplyPlaceholder')}
          />
        </div>

        {/* مهلة العرض */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <Clock className="w-4 h-4 inline-block ml-1" />
            {t('timeLimit')}
          </label>
          <select
            value={formData.timeLimit}
            onChange={(e) => handleInputChange('timeLimit', parseInt(e.target.value))}
            disabled={loading}
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value={900}>15 {t('minutes')}</option>
            <option value={1800}>30 {t('minutes')}</option>
            <option value={3600}>60 {t('minutes')}</option>
            <option value={7200}>120 {t('minutes')}</option>
          </select>
        </div>

        {/* ملخص الحسابات */}
        {calculations.totalValue > 0 && (
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <Calculator className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              <span className="font-medium text-blue-800 dark:text-blue-200">
                {t('calculations')}
              </span>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-blue-700 dark:text-blue-300">{t('totalValue')}:</span>
                <span className="font-medium text-blue-900 dark:text-blue-100">
                  {calculations.totalValue.toFixed(2)} {formData.currency}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-700 dark:text-blue-300">{t('platformFee')}:</span>
                <span className="font-medium text-blue-900 dark:text-blue-100">
                  {calculations.platformFee.toFixed(6)} {selectedToken?.token_symbol}
                </span>
              </div>
              <div className="flex justify-between border-t border-blue-200 dark:border-blue-700 pt-2">
                <span className="text-blue-700 dark:text-blue-300">{t('netAmount')}:</span>
                <span className="font-bold text-blue-900 dark:text-blue-100">
                  {calculations.netAmount.toFixed(6)} {selectedToken?.token_symbol}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* أزرار الإجراءات */}
        <div className="flex gap-4 pt-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              disabled={loading}
              className="flex-1 px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 transition-colors"
            >
              {t('cancel')}
            </button>
          )}
          <button
            type="submit"
            disabled={loading || needsApproval}
            className="flex-1 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white rounded-lg transition-colors flex items-center justify-center gap-2"
          >
            {loading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Plus className="w-5 h-5" />
            )}
            {loading ? t('creating') : t('createOffer')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EnhancedCreateOfferForm;
