<?php
/**
 * API endpoint لنسيان كلمة المرور
 * Forgot Password API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";

// التحقق من طريقة الطلب
validateRequestMethod(['POST']);

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/../../.env')) {
    $lines = file(__DIR__ . '/../../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

/**
 * إنشاء رمز إعادة تعيين كلمة المرور
 */
function generateResetToken() {
    return bin2hex(random_bytes(32));
}

/**
 * إرسال بريد إلكتروني لإعادة تعيين كلمة المرور
 * (في الوقت الحالي، سنقوم بتسجيل الرمز فقط)
 */
function sendResetEmail($email, $resetToken, $username) {
    // TODO: تطبيق إرسال البريد الإلكتروني الفعلي
    // يمكن استخدام PHPMailer أو خدمة بريد إلكتروني خارجية
    
    $resetLink = "https://ikaros-p2p.com/reset-password?token=" . $resetToken;
    
    // تسجيل الرمز في السجلات للتطوير
    error_log("Password reset token for $email ($username): $resetToken");
    error_log("Reset link: $resetLink");
    
    // في الإنتاج، استبدل هذا بإرسال بريد إلكتروني فعلي
    return true;
}

try {
    // الحصول على البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendErrorResponse('بيانات غير صحيحة');
    }
    
    $email = trim($input['email'] ?? '');
    
    if (empty($email)) {
        sendErrorResponse('البريد الإلكتروني مطلوب');
    }
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        sendErrorResponse('البريد الإلكتروني غير صحيح');
    }
    
    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // البحث عن المستخدم
    $stmt = $connection->prepare("
        SELECT id, username, email, is_active 
        FROM users 
        WHERE email = ? AND is_active = 1
    ");
    
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // دائماً إرجاع نفس الرسالة لأسباب أمنية (لمنع تعداد المستخدمين)
    $successMessage = 'إذا كان البريد الإلكتروني موجود في نظامنا، ستتلقى رسالة لإعادة تعيين كلمة المرور';
    
    if ($user) {
        // إنشاء رمز إعادة التعيين
        $resetToken = generateResetToken();
        $expiryTime = date('Y-m-d H:i:s', time() + (60 * 60)); // ساعة واحدة
        
        // حفظ رمز إعادة التعيين في قاعدة البيانات
        $insertStmt = $connection->prepare("
            INSERT INTO password_resets (user_id, email, token, expires_at, created_at)
            VALUES (?, ?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE
            token = VALUES(token),
            expires_at = VALUES(expires_at),
            created_at = NOW(),
            used = 0
        ");
        
        $insertStmt->execute([$user['id'], $email, $resetToken, $expiryTime]);
        
        // إرسال البريد الإلكتروني
        $emailSent = sendResetEmail($email, $resetToken, $user['username']);
        
        // تسجيل النشاط
        try {
            $checkTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
            $checkTable->execute();
            
            if ($checkTable->rowCount() > 0) {
                $checkColumns = $connection->prepare("SHOW COLUMNS FROM activity_logs LIKE 'entity_type'");
                $checkColumns->execute();
                
                if ($checkColumns->rowCount() > 0) {
                    $logStmt = $connection->prepare("
                        INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data)
                        VALUES (?, 'password_reset_requested', 'user', ?, ?, ?, ?)
                    ");
                    
                    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                    $resetData = json_encode([
                        'request_time' => date('Y-m-d H:i:s'),
                        'email' => $email,
                        'token_expires' => $expiryTime,
                        'email_sent' => $emailSent
                    ]);
                    
                    $logStmt->execute([$user['id'], $user['id'], $ipAddress, $userAgent, $resetData]);
                }
            }
        } catch (Exception $logError) {
            // تجاهل أخطاء تسجيل النشاط
            error_log('Activity log error in forgot-password: ' . $logError->getMessage());
        }
    }
    
    // إرجاع نفس الرسالة دائماً لأسباب أمنية
    sendSuccessResponse([
        'message' => $successMessage,
        'timestamp' => date('Y-m-d H:i:s')
    ], $successMessage);
    
} catch (Exception $e) {
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    error_log('Database error in forgot-password.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}
?>
