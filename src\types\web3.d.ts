// تعريفات TypeScript للـ Web3 والمحافظ الرقمية

interface Window {
  ethereum?: {
    isMetaMask?: boolean;
    isTrust?: boolean;
    request: (args: { method: string; params?: any[] }) => Promise<any>;
    on: (event: string, callback: (...args: any[]) => void) => void;
    removeListener: (event: string, callback: (...args: any[]) => void) => void;
    selectedAddress?: string;
    chainId?: string;
    networkVersion?: string;
  };
}

// أنواع العقود الذكية
export interface TradeStruct {
  id: bigint;
  seller: string;
  buyer: string;
  amount: bigint;
  price: bigint;
  status: number;
  createdAt: bigint;
  timeLimit: bigint;
}

export interface ContractEvent {
  transactionHash: string;
  blockNumber: number;
  args: any[];
}

// حالات الصفقة
export enum TradeStatus {
  CREATED = 0,
  PAYMENT_SENT = 1,
  PAYMENT_RECEIVED = 2,
  COMPLETED = 3,
  CANCELLED = 4,
  DISPUTED = 5
}

// أنواع الشبكات
export interface NetworkConfig {
  chainId: string;
  chainName: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  rpcUrls: string[];
  blockExplorerUrls: string[];
}

// أنواع المحفظة
export type WalletType = 'metamask' | 'walletconnect' | 'trustwallet';

export interface WalletInfo {
  address: string;
  balance: string;
  network: string;
  isConnected: boolean;
  chainId?: string;
}

// أنواع المعاملات
export interface TransactionResponse {
  hash: string;
  from: string;
  to: string;
  value: string;
  gasLimit: string;
  gasPrice: string;
  nonce: number;
  data: string;
  chainId: number;
}

export interface TransactionReceipt {
  transactionHash: string;
  blockNumber: number;
  blockHash: string;
  gasUsed: string;
  status: number;
  logs: any[];
}

// أخطاء Web3
export interface Web3Error {
  code: number;
  message: string;
  data?: any;
}

// أكواد الأخطاء الشائعة
export enum ErrorCodes {
  USER_REJECTED = 4001,
  UNAUTHORIZED = 4100,
  UNSUPPORTED_METHOD = 4200,
  DISCONNECTED = 4900,
  CHAIN_DISCONNECTED = 4901
}

// أنواع الأحداث
export type EthereumEventType = 
  | 'accountsChanged'
  | 'chainChanged'
  | 'connect'
  | 'disconnect';

export interface EthereumEvent {
  type: EthereumEventType;
  data: any;
}

// معلومات الغاز
export interface GasEstimate {
  gasLimit: string;
  gasPrice: string;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
}

// معلومات الرصيد
export interface TokenBalance {
  address: string;
  symbol: string;
  decimals: number;
  balance: string;
  formattedBalance: string;
}

export default {};
