'use client';

import React, { useEffect, useState, useRef } from 'react';
import { Bell, Wifi, WifiOff, AlertCircle } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

interface RealtimeNotification {
  id: string;
  type: 'trade' | 'message' | 'security' | 'system';
  title: string;
  message: string;
  timestamp: Date;
  data?: any;
}

interface RealtimeNotificationsProps {
  userId: string;
  onNotificationReceived?: (notification: RealtimeNotification) => void;
  className?: string;
}

export default function RealtimeNotifications({
  userId,
  onNotificationReceived,
  className = ''
}: RealtimeNotificationsProps) {
  const { t } = useTranslation();
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const [notifications, setNotifications] = useState<RealtimeNotification[]>([]);
  const [showToast, setShowToast] = useState(false);
  const [currentToast, setCurrentToast] = useState<RealtimeNotification | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  useEffect(() => {
    if (userId) {
      connectWebSocket();
    }

    return () => {
      disconnectWebSocket();
    };
  }, [userId]);

  const connectWebSocket = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setConnectionStatus('connecting');

    try {
      // في بيئة الإنتاج، استخدم wss://
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/ws/notifications?userId=${userId}`;
      
      // للتطوير، يمكن استخدام Server-Sent Events بدلاً من WebSocket
      if (process.env.NODE_ENV === 'development') {
        setupServerSentEvents();
        return;
      }

      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('✅ WebSocket connected');
        setIsConnected(true);
        setConnectionStatus('connected');
        reconnectAttempts.current = 0;

        // إرسال رسالة تأكيد الاتصال
        wsRef.current?.send(JSON.stringify({
          type: 'auth',
          userId: userId,
          timestamp: new Date().toISOString()
        }));
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleNotificationReceived(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('🔌 WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        setConnectionStatus('disconnected');

        // إعادة الاتصال التلقائي
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.pow(2, reconnectAttempts.current) * 1000; // Exponential backoff
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            connectWebSocket();
          }, delay);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
        setConnectionStatus('error');
      };

    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      setConnectionStatus('error');
    }
  };

  const setupServerSentEvents = () => {
    // استخدام Server-Sent Events كبديل للتطوير
    const eventSource = new EventSource(`/api/notifications/stream.php?userId=${userId}`);

    eventSource.onopen = () => {
      console.log('✅ SSE connected');
      setIsConnected(true);
      setConnectionStatus('connected');
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        handleNotificationReceived(data);
      } catch (error) {
        console.error('Error parsing SSE message:', error);
      }
    };

    eventSource.onerror = () => {
      console.error('❌ SSE error');
      setIsConnected(false);
      setConnectionStatus('error');
      eventSource.close();
    };

    // حفظ المرجع للتنظيف
    wsRef.current = eventSource as any;
  };

  const disconnectWebSocket = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    setIsConnected(false);
    setConnectionStatus('disconnected');
  };

  const handleNotificationReceived = (data: any) => {
    const notification: RealtimeNotification = {
      id: data.id || Date.now().toString(),
      type: data.type || 'system',
      title: data.title || '',
      message: data.message || '',
      timestamp: new Date(data.timestamp || Date.now()),
      data: data.data
    };

    setNotifications(prev => [notification, ...prev.slice(0, 9)]); // الاحتفاظ بآخر 10 إشعارات

    // إظهار Toast
    setCurrentToast(notification);
    setShowToast(true);

    // إخفاء Toast بعد 5 ثوانٍ
    setTimeout(() => {
      setShowToast(false);
    }, 5000);

    // استدعاء callback
    onNotificationReceived?.(notification);

    // إظهار إشعار المتصفح إذا كان مسموحاً
    if (Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-72x72.png',
        tag: notification.id,
        renotify: true
      });
    }
  };

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <Wifi className="w-4 h-4 text-green-500" />;
      case 'connecting':
        return <Wifi className="w-4 h-4 text-yellow-500 animate-pulse" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <WifiOff className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return t('realtime.connected');
      case 'connecting':
        return t('realtime.connecting');
      case 'error':
        return t('realtime.error');
      default:
        return t('realtime.disconnected');
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'trade':
        return '💰';
      case 'message':
        return '💬';
      case 'security':
        return '🔒';
      case 'system':
        return '⚙️';
      default:
        return '📢';
    }
  };

  return (
    <div className={className}>
      {/* مؤشر حالة الاتصال */}
      <div className="fixed bottom-4 left-4 z-40">
        <div className={`flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
          isConnected 
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
            : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
        }`}>
          {getStatusIcon()}
          <span>{getStatusText()}</span>
        </div>
      </div>

      {/* Toast للإشعارات الجديدة */}
      {showToast && currentToast && (
        <div className="fixed top-4 right-4 z-50 max-w-sm w-full">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 transform transition-all duration-300 ease-in-out">
            <div className="flex items-start space-x-3 rtl:space-x-reverse">
              <div className="flex-shrink-0 text-2xl">
                {getNotificationIcon(currentToast.type)}
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  {currentToast.title}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {currentToast.message}
                </p>
                <div className="flex items-center justify-between mt-2">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {t('realtime.justNow')}
                  </span>
                  <button
                    onClick={() => setShowToast(false)}
                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    <span className="sr-only">{t('common.close')}</span>
                    ×
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* قائمة الإشعارات الحديثة (مخفية افتراضياً) */}
      <div className="hidden">
        {notifications.map((notification) => (
          <div key={notification.id} className="notification-item">
            {/* يمكن استخدامها لاحقاً */}
          </div>
        ))}
      </div>
    </div>
  );
}

// Hook لاستخدام الإشعارات الفورية
export function useRealtimeNotifications(userId: string) {
  const [notifications, setNotifications] = useState<RealtimeNotification[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  const addNotification = (notification: RealtimeNotification) => {
    setNotifications(prev => [notification, ...prev]);
    setUnreadCount(prev => prev + 1);
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId 
          ? { ...n, isRead: true }
          : n
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const clearAll = () => {
    setNotifications([]);
    setUnreadCount(0);
  };

  return {
    notifications,
    isConnected,
    unreadCount,
    addNotification,
    markAsRead,
    clearAll,
    setIsConnected
  };
}

// مكون مبسط لمؤشر الإشعارات
export function NotificationIndicator({ 
  count, 
  onClick, 
  className = '' 
}: { 
  count: number; 
  onClick?: () => void; 
  className?: string; 
}) {
  return (
    <button
      onClick={onClick}
      className={`relative p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white transition-colors duration-200 ${className}`}
    >
      <Bell className="w-6 h-6" />
      {count > 0 && (
        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center animate-pulse">
          {count > 99 ? '99+' : count}
        </span>
      )}
    </button>
  );
}
