'use client';

import React, { useState, useEffect } from 'react';
import { Bell, BellRing } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuth } from '@/contexts/AuthContext';
import SmartNotificationCenter from './SmartNotificationCenter';
import { smartNotificationService, NotificationStats } from '@/services/smartNotificationService';

interface SmartNotificationButtonProps {
  className?: string;
}

export default function SmartNotificationButton({ className = '' }: SmartNotificationButtonProps) {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [stats, setStats] = useState<NotificationStats | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (user?.id && !isInitialized) {
      initializeNotifications();
    }
  }, [user, isInitialized]);

  useEffect(() => {
    if (user?.id) {
      loadStats();
      
      // تحديث الإحصائيات كل 30 ثانية
      const interval = setInterval(loadStats, 30000);
      return () => clearInterval(interval);
    }
  }, [user]);

  /**
   * تهيئة نظام الإشعارات
   */
  const initializeNotifications = async () => {
    try {
      await smartNotificationService.initialize();
      
      // إضافة مستمعين للأحداث
      smartNotificationService.addEventListener('notificationSent', handleNewNotification);
      smartNotificationService.addEventListener('notificationRead', handleNotificationRead);
      smartNotificationService.addEventListener('allNotificationsRead', handleAllNotificationsRead);
      
      setIsInitialized(true);
      console.log('✅ تم تهيئة نظام الإشعارات الذكية');
    } catch (error) {
      console.error('❌ خطأ في تهيئة نظام الإشعارات:', error);
    }
  };

  /**
   * تحميل إحصائيات الإشعارات
   */
  const loadStats = () => {
    if (!user?.id) return;

    try {
      const currentStats = smartNotificationService.getNotificationStats(parseInt(user.id.toString()));
      setStats(currentStats);
    } catch (error) {
      console.error('خطأ في تحميل إحصائيات الإشعارات:', error);
    }
  };

  /**
   * معالجة إشعار جديد
   */
  const handleNewNotification = (notification: any) => {
    loadStats();
    
    // تأثير بصري للإشعار الجديد
    if (notification.priority === 'urgent' || notification.priority === 'high') {
      // إضافة تأثير اهتزاز للإشعارات المهمة
      const button = document.querySelector('[data-notification-button]');
      if (button) {
        button.classList.add('animate-bounce');
        setTimeout(() => {
          button.classList.remove('animate-bounce');
        }, 1000);
      }
    }
  };

  /**
   * معالجة قراءة إشعار
   */
  const handleNotificationRead = () => {
    loadStats();
  };

  /**
   * معالجة قراءة جميع الإشعارات
   */
  const handleAllNotificationsRead = () => {
    loadStats();
  };

  /**
   * تبديل حالة مركز الإشعارات
   */
  const toggleNotificationCenter = () => {
    setIsOpen(!isOpen);
  };

  /**
   * الحصول على لون العداد بناءً على الأولوية
   */
  const getCounterColor = () => {
    if (!stats) return 'bg-blue-500';
    
    const urgentCount = stats.byPriority.urgent || 0;
    const highCount = stats.byPriority.high || 0;
    
    if (urgentCount > 0) {
      return 'bg-red-500 animate-pulse';
    } else if (highCount > 0) {
      return 'bg-orange-500';
    } else {
      return 'bg-blue-500';
    }
  };

  /**
   * تحديد ما إذا كان يجب إظهار مؤشر النشاط
   */
  const shouldShowActivityIndicator = () => {
    if (!stats) return false;
    
    const urgentCount = stats.byPriority.urgent || 0;
    const highCount = stats.byPriority.high || 0;
    
    return urgentCount > 0 || highCount > 0;
  };

  // إذا لم يكن المستخدم مسجل دخول، لا تظهر الزر
  if (!user?.id) {
    return null;
  }

  const unreadCount = stats?.unread || 0;

  return (
    <>
      <button
        data-notification-button
        onClick={toggleNotificationCenter}
        className={`
          relative p-2 text-gray-600 dark:text-gray-300 
          hover:text-primary-600 dark:hover:text-primary-400 
          transition-all duration-200 rounded-lg 
          hover:bg-gray-100 dark:hover:bg-gray-700
          ${shouldShowActivityIndicator() ? 'text-orange-600 dark:text-orange-400' : ''}
          ${className}
        `}
        title={`${unreadCount} إشعار غير مقروء`}
      >
        {/* أيقونة الجرس */}
        {shouldShowActivityIndicator() ? (
          <BellRing className="w-5 h-5" />
        ) : (
          <Bell className="w-5 h-5" />
        )}
        
        {/* عداد الإشعارات غير المقروءة */}
        {unreadCount > 0 && (
          <span className={`
            absolute -top-1 -right-1 rtl:-right-1 ltr:-left-1 
            min-w-[18px] h-[18px] text-white text-xs 
            rounded-full flex items-center justify-center
            font-medium shadow-sm
            ${getCounterColor()}
          `}>
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
        
        {/* نقطة النشاط للإشعارات الحرجة */}
        {shouldShowActivityIndicator() && (
          <span className="absolute top-0 right-0 rtl:right-0 ltr:left-0 w-3 h-3 bg-red-500 rounded-full animate-ping"></span>
        )}

        {/* مؤشر الإشعارات الجديدة في آخر 5 دقائق */}
        {stats && stats.last24Hours > 0 && unreadCount > 0 && (
          <span className="absolute -bottom-1 -right-1 rtl:-right-1 ltr:-left-1 w-2 h-2 bg-green-500 rounded-full"></span>
        )}
      </button>

      {/* مركز الإشعارات الذكي */}
      <SmartNotificationCenter
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </>
  );
}

// Hook للوصول إلى إحصائيات الإشعارات
export function useNotificationStats() {
  const { user } = useAuth();
  const [stats, setStats] = useState<NotificationStats | null>(null);

  useEffect(() => {
    if (user?.id) {
      const loadStats = () => {
        try {
          const currentStats = smartNotificationService.getNotificationStats(parseInt(user.id.toString()));
          setStats(currentStats);
        } catch (error) {
          console.error('خطأ في تحميل إحصائيات الإشعارات:', error);
        }
      };

      loadStats();
      
      // تحديث الإحصائيات عند حدوث تغييرات
      smartNotificationService.addEventListener('notificationSent', loadStats);
      smartNotificationService.addEventListener('notificationRead', loadStats);
      smartNotificationService.addEventListener('allNotificationsRead', loadStats);

      // تحديث دوري
      const interval = setInterval(loadStats, 30000);

      return () => {
        clearInterval(interval);
        smartNotificationService.removeEventListener('notificationSent', loadStats);
        smartNotificationService.removeEventListener('notificationRead', loadStats);
        smartNotificationService.removeEventListener('allNotificationsRead', loadStats);
      };
    }
  }, [user]);

  return stats;
}

// Hook لإرسال إشعارات مخصصة
export function useSmartNotifications() {
  const { user } = useAuth();

  const sendNotification = (
    type: 'trade' | 'security' | 'system' | 'payment' | 'kyc' | 'admin' | 'contract',
    title: string,
    message: string,
    options?: {
      priority?: 'low' | 'medium' | 'high' | 'urgent';
      actionUrl?: string;
      actionLabel?: string;
      metadata?: Record<string, any>;
      tradeId?: number;
      contractEventType?: string;
    }
  ) => {
    if (!user?.id) {
      console.warn('لا يمكن إرسال إشعار بدون مستخدم مسجل دخول');
      return null;
    }

    return smartNotificationService.createSmartNotification({
      type,
      title,
      message,
      priority: options?.priority || 'medium',
      userId: parseInt(user.id.toString()),
      actionUrl: options?.actionUrl,
      actionLabel: options?.actionLabel,
      metadata: options?.metadata,
      tradeId: options?.tradeId,
      contractEventType: options?.contractEventType as any,
      isPersistent: options?.priority === 'urgent'
    });
  };

  const sendContractNotification = (
    eventType: 'TradeCreated' | 'BuyerJoined' | 'PaymentSent' | 'PaymentConfirmed' | 'TradeCompleted' | 'TradeCancelled' | 'TradeDisputed' | 'DisputeResolved',
    tradeId: number,
    additionalData?: Record<string, any>
  ) => {
    if (!user?.id) {
      console.warn('لا يمكن إرسال إشعار عقد ذكي بدون مستخدم مسجل دخول');
      return null;
    }

    return smartNotificationService.createContractEventNotification(
      eventType,
      tradeId,
      parseInt(user.id.toString()),
      additionalData
    );
  };

  return {
    sendNotification,
    sendContractNotification
  };
}
