<?php
/**
 * API endpoint لتسجيل المستخدمين الجدد - محدث
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";

// التحقق من طريقة الطلب
validateRequestMethod(['POST']);

try {
    $input = getJsonInput();

    if (!$input) {
        sendErrorResponse("بيانات غير صحيحة");
    }
    
    $email = filter_var($input["email"] ?? "", FILTER_VALIDATE_EMAIL);
    $password = $input["password"] ?? "";
    $fullName = trim($input["fullName"] ?? "");
    $phone = trim($input["phone"] ?? "");
    $walletAddress = trim($input["walletAddress"] ?? "");
    
    if (!$email) {
        sendErrorResponse("البريد الإلكتروني غير صحيح");
    }

    if (strlen($password) < 8) {
        sendErrorResponse("كلمة المرور يجب أن تكون 8 أحرف على الأقل");
    }

    if (empty($fullName)) {
        sendErrorResponse("الاسم الكامل مطلوب");
    }
    
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // التحقق من البريد الإلكتروني المكرر
    $checkStmt = $connection->prepare("SELECT id FROM users WHERE email = ?");
    $checkStmt->execute([$email]);
    
    if ($checkStmt->fetch()) {
        sendErrorResponse("البريد الإلكتروني مستخدم بالفعل");
    }
    
    // إنشاء اسم مستخدم فريد
    $username = "user_" . substr(md5($email . time()), 0, 8);
    $usernameCheckStmt = $connection->prepare("SELECT id FROM users WHERE username = ?");
    $usernameCheckStmt->execute([$username]);
    
    if ($usernameCheckStmt->fetch()) {
        $username = $username . "_" . substr(md5(time()), 0, 4);
    }
    
    // إنشاء عنوان محفظة إذا لم يتم توفيره
    if (empty($walletAddress)) {
        $walletAddress = "0x" . bin2hex(random_bytes(20));
    } else {
        // التحقق من عنوان المحفظة المكرر
        $walletCheckStmt = $connection->prepare("SELECT id FROM users WHERE wallet_address = ?");
        $walletCheckStmt->execute([$walletAddress]);
        
        if ($walletCheckStmt->fetch()) {
            sendErrorResponse("عنوان المحفظة مستخدم بالفعل");
        }
    }
    
    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
    
    $insertStmt = $connection->prepare("
        INSERT INTO users (
            wallet_address, username, email, full_name, phone, password_hash,
            is_verified, is_admin, is_active, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, 0, 0, 1, CURRENT_TIMESTAMP)
    ");
    
    $insertStmt->execute([
        $walletAddress,
        $username,
        $email,
        $fullName,
        $phone,
        $passwordHash
    ]);
    
    $userId = $connection->lastInsertId();
    
    // تسجيل النشاط (مع التحقق من وجود الجدول)
    try {
        $checkTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
        $checkTable->execute();

        if ($checkTable->rowCount() > 0) {
            $checkColumns = $connection->prepare("SHOW COLUMNS FROM activity_logs LIKE 'entity_type'");
            $checkColumns->execute();

            if ($checkColumns->rowCount() > 0) {
                $logStmt = $connection->prepare("
                    INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data)
                    VALUES (?, 'user_register', 'user', ?, ?, ?, ?)
                ");

                $ipAddress = $_SERVER["REMOTE_ADDR"] ?? "unknown";
                $userAgent = $_SERVER["HTTP_USER_AGENT"] ?? "unknown";
                $registerData = json_encode([
                    "registration_time" => date("Y-m-d H:i:s"),
                    "method" => "email_password"
                ]);

                $logStmt->execute([$userId, $userId, $ipAddress, $userAgent, $registerData]);
            }
        }
    } catch (Exception $logError) {
        // تجاهل أخطاء تسجيل النشاط
        error_log('Activity log error in register: ' . $logError->getMessage());
    }
    
    $userData = [
        "id" => $userId,
        "walletAddress" => $walletAddress,
        "username" => $username,
        "email" => $email,
        "fullName" => $fullName,
        "phone" => $phone,
        "isVerified" => false,
        "isAdmin" => false,
        "rating" => 0.0,
        "totalTrades" => 0,
        "completedTrades" => 0,
        "totalVolume" => 0.0,
        "joinDate" => date("Y-m-d H:i:s"),
        "lastLogin" => null
    ];
    
    sendSuccessResponse([
        "user" => $userData
    ], "تم إنشاء الحساب بنجاح");

} catch (Exception $e) {
    logError("Registration error: " . $e->getMessage(), [
        'input' => $input ?? null,
        'file' => __FILE__,
        'line' => __LINE__
    ]);
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    logError("Database error in register.php: " . $e->getMessage(), [
        'input' => $input ?? null,
        'file' => __FILE__,
        'line' => __LINE__
    ]);
    sendErrorResponse("خطأ في قاعدة البيانات", 500);
}
?>