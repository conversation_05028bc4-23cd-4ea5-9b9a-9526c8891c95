[{"inputs": [{"internalType": "address", "name": "_coreEscrow", "type": "address"}, {"internalType": "address", "name": "_reputationManager", "type": "address"}, {"internalType": "address", "name": "_oracleManager", "type": "address"}, {"internalType": "address", "name": "_adminManager", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "uint256", "name": "tradeId", "type": "uint256"}], "name": "completeIntegratedTrade", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "pricePerToken", "type": "uint256"}, {"internalType": "string", "name": "currency", "type": "string"}, {"internalType": "uint256", "name": "networkId", "type": "uint256"}], "name": "createIntegratedTrade", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getIntegrationSettings", "outputs": [{"components": [{"internalType": "bool", "name": "autoReputationUpdate", "type": "bool"}, {"internalType": "bool", "name": "priceValidationEnabled", "type": "bool"}, {"internalType": "bool", "name": "adminValidationRequired", "type": "bool"}, {"internalType": "uint256", "name": "minReputationForTrade", "type": "uint256"}, {"internalType": "uint256", "name": "maxPriceDeviation", "type": "uint256"}, {"internalType": "bool", "name": "emergencyMode", "type": "bool"}], "internalType": "struct EscrowIntegrator.IntegrationSettings", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getIntegrationStats", "outputs": [{"internalType": "uint256", "name": "total", "type": "uint256"}, {"internalType": "uint256", "name": "successful", "type": "uint256"}, {"internalType": "uint256", "name": "failed", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tradeId", "type": "uint256"}], "name": "getTradeIntegration", "outputs": [{"components": [{"internalType": "uint256", "name": "tradeId", "type": "uint256"}, {"internalType": "uint256", "name": "networkId", "type": "uint256"}, {"internalType": "bool", "name": "reputationUpdated", "type": "bool"}, {"internalType": "bool", "name": "priceValidated", "type": "bool"}, {"internalType": "uint256", "name": "createdAt", "type": "uint256"}, {"internalType": "uint256", "name": "lastUpdate", "type": "uint256"}], "internalType": "struct EscrowIntegrator.TradeIntegration", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tradeId", "type": "uint256"}, {"internalType": "string", "name": "reason", "type": "string"}], "name": "handleIntegratedDispute", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tradeId", "type": "uint256"}], "name": "joinIntegratedTrade", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "networkId", "type": "uint256"}], "name": "setUserNetwork", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "enabled", "type": "bool"}], "name": "toggleEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "setting", "type": "string"}, {"internalType": "bool", "name": "value", "type": "bool"}], "name": "updateIntegrationSetting", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "setting", "type": "string"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "updateNumericSetting", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bool", "name": "enabled", "type": "bool"}, {"indexed": false, "internalType": "address", "name": "to<PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "EmergencyModeToggled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "setting", "type": "string"}, {"indexed": false, "internalType": "bool", "name": "oldValue", "type": "bool"}, {"indexed": false, "internalType": "bool", "name": "newValue", "type": "bool"}, {"indexed": false, "internalType": "address", "name": "updatedBy", "type": "address"}], "name": "IntegrationSettingsUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "string", "name": "currency", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "valid", "type": "bool"}], "name": "PriceValidated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "networkId", "type": "uint256"}, {"indexed": false, "internalType": "int256", "name": "change", "type": "int256"}], "name": "ReputationIntegrated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "seller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "networkId", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "success", "type": "bool"}], "name": "TradeIntegrated", "type": "event"}]