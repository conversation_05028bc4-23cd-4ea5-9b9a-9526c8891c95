'use client';

import { useContext } from 'react';
import { LanguageContext } from '@/contexts/LanguageContext';

export function useTranslation() {
  const context = useContext(LanguageContext);

  if (!context) {
    throw new Error('useTranslation must be used within a LanguageProvider');
  }

  const { language, translations, setLanguage, isInitialized } = context;

  // دالة للحصول على الترجمة بناءً على المفتاح
  const t = (key: string, params?: Record<string, string | number>): string => {
    // أثناء SSR أو قبل التهيئة، استخدم الإنجليزية كافتراضي لتجنب مشاكل hydration
    const currentLanguage = isInitialized ? language : 'en';
    const keys = key.split('.');
    let value: any = translations[currentLanguage];

    // البحث عن القيمة في الكائن المتداخل
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // إذا لم توجد الترجمة في اللغة الحالية، جرب الإنجليزية
        if (currentLanguage !== 'en') {
          let fallbackValue: any = translations.en;
          for (const fallbackKey of keys) {
            if (fallbackValue && typeof fallbackValue === 'object' && fallbackKey in fallbackValue) {
              fallbackValue = fallbackValue[fallbackKey];
            } else {
              fallbackValue = null;
              break;
            }
          }
          if (typeof fallbackValue === 'string') {
            return fallbackValue;
          }
        }
        // إذا لم توجد الترجمة، أرجع المفتاح نفسه
        if (process.env.NODE_ENV === 'development') {
          console.warn(`Translation key not found: ${key} for language: ${currentLanguage}`);
        }
        return key;
      }
    }

    // إذا كانت القيمة نص، قم بتطبيق المعاملات إن وجدت
    if (typeof value === 'string' && params) {
      return Object.entries(params).reduce((text, [param, val]) => {
        return text.replace(new RegExp(`{{${param}}}`, 'g'), String(val));
      }, value);
    }

    return typeof value === 'string' ? value : key;
  };

  // دالة للتحقق من اتجاه اللغة
  const isRTL = () => language === 'ar';

  // دالة لتبديل اللغة
  const toggleLanguage = () => {
    const newLanguage = language === 'ar' ? 'en' : 'ar';
    setLanguage(newLanguage);
  };

  // دالة للحصول على اسم اللغة
  const getLanguageName = (lang?: string) => {
    const targetLang = lang || language;
    return targetLang === 'ar' ? 'العربية' : 'English';
  };

  // دالة للحصول على رمز اللغة
  const getLanguageFlag = (lang?: string) => {
    const targetLang = lang || language;
    return targetLang === 'ar' ? '🇸🇦' : '🇺🇸';
  };

  return {
    t,
    language,
    setLanguage,
    isRTL,
    toggleLanguage,
    getLanguageName,
    getLanguageFlag,
    translations: translations[language],
    isInitialized
  };
}

export default useTranslation;
