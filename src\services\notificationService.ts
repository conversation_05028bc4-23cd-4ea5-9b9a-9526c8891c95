// خدمة الإشعارات باستخدام react-hot-toast
import toast from 'react-hot-toast';

export type NotificationType = 'success' | 'error' | 'loading' | 'info' | 'warning';

export interface NotificationOptions {
  duration?: number;
  position?: 'top-center' | 'top-right' | 'top-left' | 'bottom-center' | 'bottom-right' | 'bottom-left';
  style?: React.CSSProperties;
  className?: string;
  icon?: string;
  id?: string;
}

class NotificationService {
  private defaultOptions: NotificationOptions = {
    duration: 4000,
    position: 'top-center',
    style: {
      fontFamily: 'var(--font-tajawal)',
      direction: 'rtl',
      textAlign: 'right'
    }
  };

  // إشعار نجاح
  success(message: string, options?: NotificationOptions): string {
    return toast.success(message, {
      ...this.defaultOptions,
      ...options,
      style: {
        ...this.defaultOptions.style,
        ...options?.style,
        background: '#10B981',
        color: '#fff',
        borderRadius: '8px',
        padding: '16px',
        fontSize: '14px'
      },
      icon: options?.icon || '✅'
    });
  }

  // إشعار خطأ
  error(message: string, options?: NotificationOptions): string {
    return toast.error(message, {
      ...this.defaultOptions,
      ...options,
      duration: options?.duration || 6000, // مدة أطول للأخطاء
      style: {
        ...this.defaultOptions.style,
        ...options?.style,
        background: '#EF4444',
        color: '#fff',
        borderRadius: '8px',
        padding: '16px',
        fontSize: '14px'
      },
      icon: options?.icon || '❌'
    });
  }

  // إشعار تحميل
  loading(message: string, options?: NotificationOptions): string {
    return toast.loading(message, {
      ...this.defaultOptions,
      ...options,
      style: {
        ...this.defaultOptions.style,
        ...options?.style,
        background: '#3B82F6',
        color: '#fff',
        borderRadius: '8px',
        padding: '16px',
        fontSize: '14px'
      }
    });
  }

  // إشعار معلومات
  info(message: string, options?: NotificationOptions): string {
    return toast(message, {
      ...this.defaultOptions,
      ...options,
      style: {
        ...this.defaultOptions.style,
        ...options?.style,
        background: '#3B82F6',
        color: '#fff',
        borderRadius: '8px',
        padding: '16px',
        fontSize: '14px'
      },
      icon: options?.icon || 'ℹ️'
    });
  }

  // إشعار تحذير
  warning(message: string, options?: NotificationOptions): string {
    return toast(message, {
      ...this.defaultOptions,
      ...options,
      style: {
        ...this.defaultOptions.style,
        ...options?.style,
        background: '#F59E0B',
        color: '#fff',
        borderRadius: '8px',
        padding: '16px',
        fontSize: '14px'
      },
      icon: options?.icon || '⚠️'
    });
  }

  // إشعار مخصص
  custom(message: string, type: NotificationType, options?: NotificationOptions): string {
    switch (type) {
      case 'success':
        return this.success(message, options);
      case 'error':
        return this.error(message, options);
      case 'loading':
        return this.loading(message, options);
      case 'info':
        return this.info(message, options);
      case 'warning':
        return this.warning(message, options);
      default:
        return this.info(message, options);
    }
  }

  // إزالة إشعار محدد
  dismiss(toastId: string): void {
    toast.dismiss(toastId);
  }

  // إزالة جميع الإشعارات
  dismissAll(): void {
    toast.dismiss();
  }

  // تحديث إشعار موجود
  update(toastId: string, message: string, type: NotificationType, options?: NotificationOptions): void {
    toast.dismiss(toastId);
    this.custom(message, type, { ...options, id: toastId });
  }

  // إشعارات خاصة بالتداول
  tradeCreated(tradeId: string): string {
    return this.success(`Trade #${tradeId} created successfully`, {
      icon: '🤝',
      duration: 5000
    });
  }

  paymentSent(tradeId: string): string {
    return this.info(`Payment sent confirmed for trade #${tradeId}`, {
      icon: '💸',
      duration: 5000
    });
  }

  paymentReceived(tradeId: string): string {
    return this.success(`Payment received confirmed for trade #${tradeId}`, {
      icon: '💰',
      duration: 5000
    });
  }

  tradeCompleted(tradeId: string): string {
    return this.success(`Trade #${tradeId} completed successfully!`, {
      icon: '🎉',
      duration: 6000
    });
  }

  tradeCancelled(tradeId: string): string {
    return this.warning(`Trade #${tradeId} cancelled`, {
      icon: '🚫',
      duration: 5000
    });
  }

  walletConnected(address: string): string {
    const shortAddress = `${address.slice(0, 6)}...${address.slice(-4)}`;
    return this.success(`Wallet connected successfully: ${shortAddress}`, {
      icon: '🔗',
      duration: 4000
    });
  }

  walletDisconnected(): string {
    return this.info('Wallet disconnected', {
      icon: '🔌',
      duration: 3000
    });
  }

  networkSwitched(networkName: string): string {
    return this.info(`Switched to ${networkName} network`, {
      icon: '🌐',
      duration: 4000
    });
  }

  transactionPending(txHash: string): string {
    const shortHash = `${txHash.slice(0, 6)}...${txHash.slice(-4)}`;
    return this.loading(`جاري معالجة المعاملة: ${shortHash}`, {
      duration: 0 // لا تختفي تلقائياً
    });
  }

  transactionConfirmed(txHash: string): string {
    const shortHash = `${txHash.slice(0, 6)}...${txHash.slice(-4)}`;
    return this.success(`تم تأكيد المعاملة: ${shortHash}`, {
      icon: '✅',
      duration: 5000
    });
  }

  transactionFailed(error: string): string {
    return this.error(`فشلت المعاملة: ${error}`, {
      duration: 8000
    });
  }

  // إشعارات خاصة بالأخطاء الشائعة
  insufficientBalance(): string {
    return this.error('الرصيد غير كافي لإتمام هذه العملية', {
      icon: '💳',
      duration: 5000
    });
  }

  userRejectedTransaction(): string {
    return this.warning('تم رفض المعاملة من قبل المستخدم', {
      icon: '🚫',
      duration: 4000
    });
  }

  networkError(): string {
    return this.error('خطأ في الشبكة، يرجى المحاولة مرة أخرى', {
      icon: '🌐',
      duration: 5000
    });
  }

  contractError(error: string): string {
    return this.error(`خطأ في العقد الذكي: ${error}`, {
      duration: 6000
    });
  }

  // إشعار تأكيد بسيط
  confirm(message: string, onConfirm: () => void, onCancel?: () => void): boolean {
    const result = window.confirm(message);
    if (result) {
      onConfirm();
    } else if (onCancel) {
      onCancel();
    }
    return result;
  }
}

// إنشاء مثيل واحد من الخدمة
export const notificationService = new NotificationService();

// تصدير الخدمة كـ default أيضاً
export default notificationService;
