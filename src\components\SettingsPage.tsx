'use client';

import { useState, useEffect } from 'react';
import {
  Settings,
  Network,
  Shield,
  Bell,
  Globe,
  Moon,
  Sun,
  Wallet,
  Info,
  CheckCircle,
  AlertTriangle,
  Loader2,
  User,
  Edit,
  Save,
  Camera,
  Mail,
  Phone
} from 'lucide-react';
import { walletService } from '@/services/walletService';
import { notificationService } from '@/services/notificationService';
import { useTranslation } from '@/hooks/useTranslation';

import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { BLOCKCHAIN_CONFIG } from '@/constants';
import UnifiedWalletManager from './UnifiedWalletManager';

export default function SettingsPage() {
  const { t } = useTranslation();
  const { theme, toggleTheme, isDark } = useTheme();
  const { user } = useAuth();
  const [isWalletConnected, setIsWalletConnected] = useState(false);
  const [currentNetwork, setCurrentNetwork] = useState<string>('');
  const [isTestnet, setIsTestnet] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [walletAddress, setWalletAddress] = useState<string>('');
  const [settings, setSettings] = useState({
    notifications: true,
    darkMode: isDark,
    language: 'ar',
    autoConnect: true,
    showBalances: true
  });
  const [userSettings, setUserSettings] = useState<any>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [profileData, setProfileData] = useState({
    full_name: '',
    email: '',
    phone: '',
    bio: '',
    avatar: ''
  });
  const [profileErrors, setProfileErrors] = useState<any>({});

  // تحميل إعدادات المستخدم
  useEffect(() => {
    const loadUserSettings = async () => {
      if (!user?.id) return;

      try {
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost/ikaros-p2p/api';
        const response = await fetch(`${apiUrl}/users/settings.php?user_id=${user.id}`);
        const result = await response.json();

        if (result.success) {
          setUserSettings(result.data);
          // تحديث الإعدادات المحلية
          setSettings(prev => ({
            ...prev,
            notifications: result.data.preferences?.notifications?.trade_updates ?? true,
            language: result.data.preferences?.language ?? 'ar',
            showBalances: result.data.privacy?.show_profile_to_public ?? true,
            autoConnect: result.data.preferences?.notifications?.push_notifications ?? true
          }));
          // تحديث بيانات الملف الشخصي
          setProfileData({
            full_name: result.data.full_name || '',
            email: result.data.email || '',
            phone: result.data.phone || '',
            bio: result.data.bio || '',
            avatar: result.data.avatar || ''
          });
        }
      } catch (error) {
        console.error('Error loading user settings:', error);
      }
    };

    loadUserSettings();
  }, [user?.id]);

  // التحقق من حالة المحفظة عند تحميل المكون
  useEffect(() => {
    const checkWalletConnection = async () => {
      const connected = walletService.isWalletConnected();
      setIsWalletConnected(connected);

      if (connected) {
        const address = walletService.getCurrentAccount();
        setWalletAddress(address || '');

        // جلب معلومات الشبكة الحالية
        try {
          const walletInfo = await walletService.getWalletInfo();
          if (walletInfo) {
            setCurrentNetwork(walletInfo.network);
            setIsTestnet(walletInfo.chainId === BLOCKCHAIN_CONFIG.BSC_TESTNET.chainId);
          }
        } catch (error) {
          console.error(t('settings.messages.networkInfoError'), error);
        }
      }
    };

    checkWalletConnection();
  }, []);

  // دالة تبديل الشبكة
  const handleNetworkSwitch = async (useTestnet: boolean) => {
    if (!isWalletConnected) {
      notificationService.error(t('settings.messages.walletRequired'));
      return;
    }

    setIsLoading(true);
    const loadingToast = notificationService.loading(t('settings.messages.switchingNetwork'));

    try {
      const targetChainId = useTestnet 
        ? BLOCKCHAIN_CONFIG.BSC_TESTNET.chainId 
        : BLOCKCHAIN_CONFIG.BSC_MAINNET.chainId;

      const success = await walletService.switchNetwork(targetChainId);
      
      if (success) {
        setIsTestnet(useTestnet);
        const networkName = useTestnet ? 'BSC Testnet' : 'BSC Mainnet';
        setCurrentNetwork(networkName);
        
        notificationService.dismiss(loadingToast);
        notificationService.networkSwitched(networkName);
      } else {
        throw new Error(t('settings.messages.networkSwitchFailed'));
      }
    } catch (error: any) {
      notificationService.dismiss(loadingToast);
      notificationService.error(error.message || t('settings.messages.networkSwitchError'));
    } finally {
      setIsLoading(false);
    }
  };

  // دالة ربط المحفظة
  const handleConnectWallet = async () => {
    try {
      const walletInfo = await walletService.connectWallet('metamask');
      setIsWalletConnected(true);
      setWalletAddress(walletInfo.address);
      setCurrentNetwork(walletInfo.network);
      setIsTestnet(walletInfo.chainId === BLOCKCHAIN_CONFIG.BSC_TESTNET.chainId);
      notificationService.walletConnected(walletInfo.address);
    } catch (error: any) {
      notificationService.error(error.message || t('settings.messages.walletConnectFailed'));
    }
  };

  // دالة قطع الاتصال
  const handleDisconnectWallet = () => {
    walletService.disconnectWallet();
    setIsWalletConnected(false);
    setWalletAddress('');
    setCurrentNetwork('');
    notificationService.walletDisconnected();
  };

  // دالة تحديث بيانات الملف الشخصي
  const handleProfileInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));

    // إزالة الخطأ عند التعديل
    if (profileErrors[name]) {
      setProfileErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // دالة حفظ الملف الشخصي
  const handleSaveProfile = async () => {
    if (!user?.id) return;

    // التحقق من صحة البيانات
    const errors: any = {};
    if (!profileData.full_name.trim()) {
      errors.full_name = 'الاسم الكامل مطلوب';
    }
    if (!profileData.email.trim()) {
      errors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/\S+@\S+\.\S+/.test(profileData.email)) {
      errors.email = 'البريد الإلكتروني غير صحيح';
    }

    if (Object.keys(errors).length > 0) {
      setProfileErrors(errors);
      return;
    }

    try {
      setIsSaving(true);
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost/ikaros-p2p/api';

      const response = await fetch(`${apiUrl}/users/profile.php`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: user.id,
          ...profileData
        }),
      });

      const result = await response.json();

      if (result.success) {
        notificationService.success('تم تحديث الملف الشخصي بنجاح');
        setIsEditingProfile(false);
        setUserSettings(prev => ({
          ...prev,
          ...profileData
        }));
      } else {
        throw new Error(result.error || 'فشل في تحديث الملف الشخصي');
      }
    } catch (error: any) {
      console.error('Error updating profile:', error);
      notificationService.error(error.message || 'خطأ في تحديث الملف الشخصي');
    } finally {
      setIsSaving(false);
    }
  };

  // دالة إلغاء تعديل الملف الشخصي
  const handleCancelEdit = () => {
    setIsEditingProfile(false);
    setProfileErrors({});
    // إرجاع البيانات للحالة الأصلية
    if (userSettings) {
      setProfileData({
        full_name: userSettings.full_name || '',
        email: userSettings.email || '',
        phone: userSettings.phone || '',
        bio: userSettings.bio || '',
        avatar: userSettings.avatar || ''
      });
    }
  };

  // دالة تحديث الإعدادات
  const updateSetting = async (key: string, value: any) => {
    if (!user?.id) return;

    setSettings(prev => ({
      ...prev,
      [key]: value
    }));

    try {
      setIsSaving(true);
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost/ikaros-p2p/api';

      // تحديد نوع الإعداد وتحديث البيانات المناسبة
      let updateData: any = { user_id: user.id };

      if (key === 'notifications') {
        updateData.preferences = {
          ...userSettings?.preferences,
          notifications: {
            ...userSettings?.preferences?.notifications,
            trade_updates: value
          }
        };
      } else if (key === 'language') {
        updateData.preferences = {
          ...userSettings?.preferences,
          language: value
        };
      } else if (key === 'showBalances') {
        updateData.privacy = {
          ...userSettings?.privacy,
          show_profile_to_public: value
        };
      } else if (key === 'autoConnect') {
        updateData.preferences = {
          ...userSettings?.preferences,
          notifications: {
            ...userSettings?.preferences?.notifications,
            push_notifications: value
          }
        };
      }

      const response = await fetch(`${apiUrl}/users/settings.php`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      const result = await response.json();

      if (result.success) {
        notificationService.success(t('settings.messages.settingsSaved'));
        // تحديث الإعدادات المحلية
        setUserSettings(prev => ({
          ...prev,
          ...updateData
        }));
      } else {
        throw new Error(result.error || 'فشل في حفظ الإعدادات');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      notificationService.error('خطأ في حفظ الإعدادات');
      // إرجاع القيمة السابقة في حالة الخطأ
      setSettings(prev => ({
        ...prev,
        [key]: !value
      }));
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* العنوان */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center">
          <Settings className="w-8 h-8 ml-3 text-primary-600 dark:text-primary-400" />
          {t('settings.title')}
        </h1>
        <p className="text-gray-600 dark:text-gray-300">{t('settings.subtitle')}</p>
      </div>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* القائمة الجانبية */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h2 className="font-semibold text-gray-900 dark:text-white mb-4">{t('settings.sections.title')}</h2>
            <nav className="space-y-2">
              <a href="#profile" className="flex items-center p-3 rounded-lg bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300">
                <User className="w-5 h-5 ml-3" />
                المعلومات الشخصية
              </a>
              <a href="#wallet" className="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300">
                <Wallet className="w-5 h-5 ml-3" />
                {t('settings.sections.wallet')}
              </a>
              <a href="#notifications" className="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300">
                <Bell className="w-5 h-5 ml-3" />
                {t('settings.sections.notifications')}
              </a>
              <a href="#appearance" className="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300">
                <Moon className="w-5 h-5 ml-3" />
                {t('settings.sections.appearance')}
              </a>
              <a href="#security" className="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300">
                <Shield className="w-5 h-5 ml-3" />
                {t('settings.sections.security')}
              </a>
            </nav>
          </div>
        </div>

        {/* المحتوى الرئيسي */}
        <div className="lg:col-span-2 space-y-8">
          {/* قسم المعلومات الشخصية */}
          <div id="profile" className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                <User className="w-6 h-6 ml-3 text-primary-600 dark:text-primary-400" />
                المعلومات الشخصية
              </h3>
              {!isEditingProfile && (
                <button
                  onClick={() => setIsEditingProfile(true)}
                  className="btn btn-secondary btn-sm flex items-center"
                >
                  <Edit className="w-4 h-4 ml-2" />
                  تعديل
                </button>
              )}
            </div>

            {isEditingProfile ? (
              <div className="space-y-6">
                {/* نموذج التعديل */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      الاسم الكامل *
                    </label>
                    <input
                      type="text"
                      name="full_name"
                      value={profileData.full_name}
                      onChange={handleProfileInputChange}
                      className={`input-field ${profileErrors.full_name ? 'error' : ''}`}
                      placeholder="أدخل اسمك الكامل"
                    />
                    {profileErrors.full_name && (
                      <p className="mt-1 text-sm text-red-600">{profileErrors.full_name}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      البريد الإلكتروني *
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={profileData.email}
                      onChange={handleProfileInputChange}
                      className={`input-field ${profileErrors.email ? 'error' : ''}`}
                      placeholder="<EMAIL>"
                    />
                    {profileErrors.email && (
                      <p className="mt-1 text-sm text-red-600">{profileErrors.email}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      رقم الهاتف
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={profileData.phone}
                      onChange={handleProfileInputChange}
                      className="input-field"
                      placeholder="+966 50 123 4567"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      الصورة الشخصية
                    </label>
                    <div className="flex items-center space-x-4 rtl:space-x-reverse">
                      <div className="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                        {profileData.avatar ? (
                          <img
                            src={profileData.avatar}
                            alt="الصورة الشخصية"
                            className="w-16 h-16 rounded-full object-cover"
                          />
                        ) : (
                          <User className="w-8 h-8 text-gray-400" />
                        )}
                      </div>
                      <button className="btn btn-secondary btn-sm flex items-center">
                        <Camera className="w-4 h-4 ml-2" />
                        تغيير الصورة
                      </button>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    نبذة شخصية
                  </label>
                  <textarea
                    name="bio"
                    value={profileData.bio}
                    onChange={handleProfileInputChange}
                    rows={4}
                    className="input-field"
                    placeholder="اكتب نبذة مختصرة عنك..."
                  />
                </div>

                {/* أزرار الحفظ والإلغاء */}
                <div className="flex justify-end space-x-4 rtl:space-x-reverse">
                  <button
                    onClick={handleCancelEdit}
                    className="btn btn-secondary"
                    disabled={isSaving}
                  >
                    إلغاء
                  </button>
                  <button
                    onClick={handleSaveProfile}
                    className="btn btn-primary flex items-center"
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin ml-2" />
                        جاري الحفظ...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 ml-2" />
                        حفظ التغييرات
                      </>
                    )}
                  </button>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* عرض المعلومات */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <User className="w-5 h-5 text-gray-500 ml-2" />
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">الاسم الكامل</span>
                    </div>
                    <p className="text-gray-900 dark:text-white font-medium">
                      {userSettings?.full_name || 'غير محدد'}
                    </p>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Mail className="w-5 h-5 text-gray-500 ml-2" />
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">البريد الإلكتروني</span>
                    </div>
                    <p className="text-gray-900 dark:text-white font-medium">
                      {userSettings?.email || 'غير محدد'}
                    </p>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <Phone className="w-5 h-5 text-gray-500 ml-2" />
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">رقم الهاتف</span>
                    </div>
                    <p className="text-gray-900 dark:text-white font-medium">
                      {userSettings?.phone || 'غير محدد'}
                    </p>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <User className="w-5 h-5 text-gray-500 ml-2" />
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">الصورة الشخصية</span>
                    </div>
                    <div className="w-12 h-12 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                      {userSettings?.avatar ? (
                        <img
                          src={userSettings.avatar}
                          alt="الصورة الشخصية"
                          className="w-12 h-12 rounded-full object-cover"
                        />
                      ) : (
                        <User className="w-6 h-6 text-gray-400" />
                      )}
                    </div>
                  </div>
                </div>

                {userSettings?.bio && (
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">نبذة شخصية</h4>
                    <p className="text-gray-900 dark:text-white">{userSettings.bio}</p>
                  </div>
                )}
              </div>
            )}
          </div>



          {/* إعدادات المحفظة والشبكة */}
            <div id="wallet" className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                <Wallet className="w-6 h-6 ml-3 text-primary-600 dark:text-primary-400" />
                {t('settings.wallet.title')}
              </h3>

              {/* مكون المحفظة الموحد */}
              <div className="mb-6">
                <UnifiedWalletManager
                  variant="card"
                  showBalance={true}
                  showNetwork={true}
                  showActions={true}
                  showConnectionOptions={true}
                  onConnectionChange={(isConnected, address) => {
                    setIsWalletConnected(isConnected);
                    setWalletAddress(address || '');
                    if (isConnected && address) {
                      // تحديث معلومات الشبكة
                      walletService.getWalletInfo().then(walletInfo => {
                        if (walletInfo) {
                          setCurrentNetwork(walletInfo.network);
                          setIsTestnet(walletInfo.chainId === BLOCKCHAIN_CONFIG.BSC_TESTNET.chainId);
                        }
                      }).catch(console.error);
                    } else {
                      setCurrentNetwork('');
                    }
                  }}
                />
              </div>

              {/* تبديل الشبكة */}
              {isWalletConnected && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    {t('settings.network.current')}
                  </label>
                  <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg mb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Network className="w-5 h-5 text-gray-600 ml-3" />
                        <div>
                          <div className="font-medium text-gray-900">{currentNetwork}</div>
                          <div className="text-sm text-gray-600">
                            {isTestnet ? t('settings.network.testnet') : t('settings.network.mainnet')}
                          </div>
                        </div>
                      </div>
                      <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                        isTestnet 
                          ? 'bg-yellow-100 text-yellow-800' 
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {isTestnet ? 'Testnet' : 'Mainnet'}
                      </div>
                    </div>
                  </div>

                  {/* سويتش تبديل الشبكة */}
                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{t('settings.network.testMode')}</div>
                      <div className="text-sm text-gray-600">
                        {t('settings.network.testModeDesc')}
                      </div>
                    </div>
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <span className={`text-sm ${!isTestnet ? 'text-gray-500' : 'font-medium text-primary-600'}`}>
                        {t('settings.network.testnetLabel')}
                      </span>
                      <button
                        onClick={() => handleNetworkSwitch(!isTestnet)}
                        disabled={isLoading}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${
                          isTestnet ? 'bg-primary-600' : 'bg-gray-200'
                        }`}
                      >
                        {isLoading ? (
                          <Loader2 className="w-4 h-4 animate-spin text-white mx-auto" />
                        ) : (
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              isTestnet ? 'translate-x-1' : 'translate-x-6'
                            }`}
                          />
                        )}
                      </button>
                      <span className={`text-sm ${isTestnet ? 'text-gray-500' : 'font-medium text-green-600'}`}>
                        {t('settings.network.mainnetLabel')}
                      </span>
                    </div>
                  </div>

                  {/* تحذير الشبكة الحقيقية */}
                  {!isTestnet && (
                    <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-start">
                        <AlertTriangle className="w-5 h-5 text-red-600 ml-3 mt-0.5" />
                        <div>
                          <div className="font-medium text-red-800">{t('settings.network.mainnetWarning')}</div>
                          <div className="text-sm text-red-600 mt-1">
                            {t('settings.network.mainnetWarningDesc')}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* إعدادات المظهر */}
            <div id="appearance" className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                <Moon className="w-6 h-6 ml-3 text-primary-600 dark:text-primary-400" />
                {t('settings.sections.appearance')}
              </h3>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">{t('settings.appearance.theme')}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">{t('settings.appearance.themeDesc')}</div>
                  </div>
                  <button
                    onClick={toggleTheme}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      isDark ? 'bg-primary-600' : 'bg-gray-200'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        isDark ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                {/* معاينة الألوان */}
                <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-4">معاينة الألوان</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="w-12 h-12 bg-primary-500 rounded-lg mx-auto mb-2"></div>
                      <span className="text-xs text-gray-600 dark:text-gray-300">أساسي</span>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 bg-success-500 rounded-lg mx-auto mb-2"></div>
                      <span className="text-xs text-gray-600 dark:text-gray-300">نجاح</span>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 bg-warning-500 rounded-lg mx-auto mb-2"></div>
                      <span className="text-xs text-gray-600 dark:text-gray-300">تحذير</span>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 bg-danger-500 rounded-lg mx-auto mb-2"></div>
                      <span className="text-xs text-gray-600 dark:text-gray-300">خطر</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* إعدادات الإشعارات */}
            <div id="notifications" className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                <Bell className="w-6 h-6 ml-3 text-primary-600 dark:text-primary-400" />
                {t('settings.notifications.title')}
              </h3>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">{t('settings.notifications.trades')}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">{t('settings.notifications.tradesDesc')}</div>
                  </div>
                  <button
                    onClick={() => updateSetting('notifications', !settings.notifications)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      settings.notifications ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.notifications ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">{t('settings.notifications.showBalances')}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">{t('settings.notifications.showBalancesDesc')}</div>
                  </div>
                  <button
                    onClick={() => updateSetting('showBalances', !settings.showBalances)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      settings.showBalances ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.showBalances ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">{t('settings.notifications.autoConnect')}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">{t('settings.notifications.autoConnectDesc')}</div>
                  </div>
                  <button
                    onClick={() => updateSetting('autoConnect', !settings.autoConnect)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      settings.autoConnect ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        settings.autoConnect ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              </div>
            </div>

            {/* معلومات الشبكة */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6">
              <div className="flex items-start">
                <Info className="w-6 h-6 text-blue-600 dark:text-blue-400 ml-3 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-2">{t('settings.info.title')}</h4>
                  <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <li>• {t('settings.info.tip1')}</li>
                    <li>• {t('settings.info.tip2')}</li>
                    <li>• {t('settings.info.tip3')}</li>
                    <li>• {t('settings.info.tip4')}</li>
                  </ul>
                </div>
              </div>
          </div>
        </div>
      </div>
    </div>
  );
}
