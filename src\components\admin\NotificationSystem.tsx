'use client';

import React, { useState } from 'react';
import {
  Bell,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Check,
  X,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  Zap,
  Shield,
  Settings,
  Users,
  TrendingUp,
  DollarSign,
  Activity,
  RefreshCw,
  MoreHorizontal,
  Archive,
  Trash2,
  Volume2,
  VolumeX,
  Calendar,
  MessageSquare,
  Mail,
  Smartphone,
  Webhook,
  Slack,
  Send,
  Pause,
  Play,
  RotateCcw,
  Flag,
  Star,
  Bookmark,
  Share,
  Copy
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success' | 'critical' | 'urgent';
  category: 'system' | 'user' | 'trade' | 'dispute' | 'security' | 'maintenance' | 'financial' | 'technical';
  priority: 'low' | 'medium' | 'high' | 'critical' | 'urgent';
  status: 'unread' | 'read' | 'acknowledged' | 'dismissed' | 'snoozed' | 'escalated' | 'resolved';
  createdAt: string;
  updatedAt: string;
  readAt?: string;
  acknowledgedAt?: string;
  dismissedAt?: string;
  snoozedUntil?: string;
  escalatedAt?: string;
  resolvedAt?: string;
  channels: string[];
  recipients: string[];
  metadata?: {
    userId?: string;
    tradeId?: string;
    disputeId?: string;
    networkId?: string;
    amount?: number;
    severity?: number;
    source?: string;
    relatedEntity?: string;
  };
  actions?: {
    primary?: string;
    secondary?: string;
    url?: string;
  };
}

interface NotificationSystemProps {
  className?: string;
}

export default function NotificationSystem({ className = '' }: NotificationSystemProps) {
  const { t, language, isRTL, getDirectionClasses, formatNumber, formatCurrency, formatDate, formatRelativeTime } = useAdminTranslation();
  const dirClasses = getDirectionClasses();
  
  const [activeTab, setActiveTab] = useState<'all' | 'unread' | 'alerts' | 'settings'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  // Mock data - replace with real data
  const notifications: Notification[] = [
    {
      id: '1',
      title: 'نزاع جديد تم إنشاؤه',
      message: 'تم إنشاء نزاع جديد للصفقة TRD-2024-001 بين المستخدمين أحمد محمد وفاطمة علي',
      type: 'warning',
      category: 'dispute',
      priority: 'high',
      status: 'unread',
      createdAt: '2024-01-20T10:00:00Z',
      updatedAt: '2024-01-20T10:00:00Z',
      channels: ['inApp', 'email'],
      recipients: ['admin1', 'admin2'],
      metadata: {
        tradeId: 'TRD-2024-001',
        disputeId: 'DSP-001',
        amount: 1500,
        severity: 7
      },
      actions: {
        primary: 'Review Dispute',
        secondary: 'View Trade',
        url: '/admin/disputes/DSP-001'
      }
    },
    {
      id: '2',
      title: 'نشاط مشبوه تم اكتشافه',
      message: 'تم اكتشاف نشاط مشبوه من المستخدم محمد عبدالله - محاولات دخول متعددة فاشلة',
      type: 'critical',
      category: 'security',
      priority: 'critical',
      status: 'unread',
      createdAt: '2024-01-20T09:30:00Z',
      updatedAt: '2024-01-20T09:30:00Z',
      channels: ['inApp', 'email', 'sms'],
      recipients: ['admin1', 'security-team'],
      metadata: {
        userId: 'user123',
        severity: 9,
        source: 'login-monitor'
      },
      actions: {
        primary: 'Investigate',
        secondary: 'Block User',
        url: '/admin/security/incidents'
      }
    },
    {
      id: '3',
      title: 'صفقة عالية القيمة مكتملة',
      message: 'تم إكمال صفقة بقيمة $50,000 بنجاح بين المستخدمين خالد يوسف ونور الدين',
      type: 'success',
      category: 'trade',
      priority: 'medium',
      status: 'read',
      createdAt: '2024-01-20T08:15:00Z',
      updatedAt: '2024-01-20T08:15:00Z',
      readAt: '2024-01-20T08:20:00Z',
      channels: ['inApp'],
      recipients: ['admin1'],
      metadata: {
        tradeId: 'TRD-2024-003',
        amount: 50000,
        severity: 3
      },
      actions: {
        primary: 'View Trade',
        url: '/admin/trades/TRD-2024-003'
      }
    },
    {
      id: '4',
      title: 'انقطاع في شبكة Ethereum',
      message: 'تم اكتشاف انقطاع في شبكة Ethereum - زمن الاستجابة مرتفع والمعاملات متأخرة',
      type: 'error',
      category: 'technical',
      priority: 'high',
      status: 'acknowledged',
      createdAt: '2024-01-20T07:45:00Z',
      updatedAt: '2024-01-20T08:00:00Z',
      acknowledgedAt: '2024-01-20T08:00:00Z',
      channels: ['inApp', 'webhook'],
      recipients: ['admin1', 'tech-team'],
      metadata: {
        networkId: 'ethereum',
        severity: 8,
        source: 'network-monitor'
      },
      actions: {
        primary: 'Check Network',
        secondary: 'Switch RPC',
        url: '/admin/networks/ethereum'
      }
    },
    {
      id: '5',
      title: 'مستخدم جديد مسجل',
      message: 'مستخدم جديد سارة أحمد قام بالتسجيل وأكمل التحقق من الهوية',
      type: 'info',
      category: 'user',
      priority: 'low',
      status: 'read',
      createdAt: '2024-01-20T06:30:00Z',
      updatedAt: '2024-01-20T06:30:00Z',
      readAt: '2024-01-20T07:00:00Z',
      channels: ['inApp'],
      recipients: ['admin1'],
      metadata: {
        userId: 'user456',
        severity: 2
      },
      actions: {
        primary: 'View Profile',
        url: '/admin/users/user456'
      }
    }
  ];

  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.message.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesTab = activeTab === 'all' ||
                      (activeTab === 'unread' && notification.status === 'unread') ||
                      (activeTab === 'alerts' && ['critical', 'urgent'].includes(notification.priority));
    
    const matchesFilter = selectedFilter === 'all' ||
                         (selectedFilter === 'unread' && notification.status === 'unread') ||
                         (selectedFilter === 'critical' && ['critical', 'urgent'].includes(notification.priority)) ||
                         (selectedFilter === 'today' && new Date(notification.createdAt).toDateString() === new Date().toDateString()) ||
                         (selectedFilter === notification.category);
    
    return matchesSearch && matchesTab && matchesFilter;
  });

  const stats = {
    total: notifications.length,
    unread: notifications.filter(n => n.status === 'unread').length,
    critical: notifications.filter(n => ['critical', 'urgent'].includes(n.priority)).length,
    today: notifications.filter(n => new Date(n.createdAt).toDateString() === new Date().toDateString()).length,
    acknowledged: notifications.filter(n => n.status === 'acknowledged').length,
    avgResponseTime: 15 // minutes
  };

  const handleNotificationAction = (notificationId: string, action: string) => {
    console.log(`Action ${action} for notification ${notificationId}`);
    // Implement notification actions
  };

  const handleBulkAction = (action: string) => {
    console.log(`Bulk action ${action} for notifications:`, selectedNotifications);
    setShowBulkActions(false);
    setSelectedNotifications([]);
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success': return 'green';
      case 'warning': return 'yellow';
      case 'error': return 'red';
      case 'critical': return 'red';
      case 'urgent': return 'purple';
      case 'info': return 'blue';
      default: return 'gray';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'green';
      case 'medium': return 'yellow';
      case 'high': return 'orange';
      case 'critical': return 'red';
      case 'urgent': return 'purple';
      default: return 'gray';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'system': return Settings;
      case 'user': return Users;
      case 'trade': return TrendingUp;
      case 'dispute': return AlertTriangle;
      case 'security': return Shield;
      case 'maintenance': return Settings;
      case 'financial': return DollarSign;
      case 'technical': return Activity;
      default: return Bell;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'success': return CheckCircle;
      case 'warning': return AlertTriangle;
      case 'error': return XCircle;
      case 'critical': return AlertTriangle;
      case 'urgent': return Zap;
      case 'info': return Info;
      default: return Bell;
    }
  };

  const TypeBadge = ({ type }: { type: string }) => {
    const color = getTypeColor(type);
    const TypeIcon = getTypeIcon(type);
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${color}-100 text-${color}-800 dark:bg-${color}-900/30 dark:text-${color}-400`}>
        <TypeIcon className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
        {t(`notifications.types.${type}`)}
      </span>
    );
  };

  const PriorityBadge = ({ priority }: { priority: string }) => {
    const color = getPriorityColor(priority);
    const PriorityIcon = priority === 'critical' || priority === 'urgent' ? Flag : 
                        priority === 'high' ? AlertTriangle :
                        priority === 'medium' ? Clock : CheckCircle;
    
    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-${color}-100 text-${color}-800 dark:bg-${color}-900/30 dark:text-${color}-400`}>
        <PriorityIcon className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
        {t(`notifications.priority.${priority}`)}
      </span>
    );
  };

  const StatusBadge = ({ status }: { status: string }) => {
    const StatusIcon = status === 'read' ? CheckCircle : 
                      status === 'acknowledged' ? Check :
                      status === 'dismissed' ? X :
                      status === 'snoozed' ? Clock : Bell;
    
    const color = status === 'unread' ? 'blue' :
                 status === 'read' ? 'green' :
                 status === 'acknowledged' ? 'purple' :
                 status === 'dismissed' ? 'gray' :
                 status === 'snoozed' ? 'yellow' : 'gray';
    
    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-${color}-100 text-${color}-800 dark:bg-${color}-900/30 dark:text-${color}-400`}>
        <StatusIcon className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
        {t(`notifications.status.${status}`)}
      </span>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className={`flex flex-col sm:flex-row sm:items-center justify-between gap-4 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
        <div className={isRTL ? 'text-right' : 'text-left'}>
          <h3 className={`text-xl font-bold text-gray-900 dark:text-white flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Bell className={`w-6 h-6 text-blue-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            {t('notifications.title')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('notifications.subtitle')}</p>
        </div>
        <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button 
            onClick={() => handleBulkAction('export')}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            <Download className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.export')}
          </button>
          <button className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
            <Plus className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('notifications.actions.createAlert')}
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('notifications.stats.totalNotifications')}</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatNumber(stats.total)}</p>
            </div>
            <Bell className="w-8 h-8 text-gray-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('notifications.stats.unreadCount')}</p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{formatNumber(stats.unread)}</p>
            </div>
            <MessageSquare className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('notifications.stats.criticalCount')}</p>
              <p className="text-2xl font-bold text-red-600 dark:text-red-400">{formatNumber(stats.critical)}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('notifications.stats.todayCount')}</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">{formatNumber(stats.today)}</p>
            </div>
            <Calendar className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('notifications.stats.acknowledgedRate')}</p>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">{((stats.acknowledged / stats.total) * 100).toFixed(1)}%</p>
            </div>
            <Check className="w-8 h-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('notifications.stats.averageResponseTime')}</p>
              <p className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">{stats.avgResponseTime}m</p>
            </div>
            <Clock className="w-8 h-8 text-indigo-500" />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className={`flex space-x-8 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          <button
            onClick={() => setActiveTab('all')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'all'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Bell className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('notifications.allNotifications')} ({stats.total})
            </div>
          </button>
          <button
            onClick={() => setActiveTab('unread')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'unread'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <MessageSquare className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('notifications.unreadNotifications')} ({stats.unread})
            </div>
          </button>
          <button
            onClick={() => setActiveTab('alerts')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'alerts'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <AlertTriangle className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('notifications.systemAlerts')} ({stats.critical})
            </div>
          </button>
          <button
            onClick={() => setActiveTab('settings')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'settings'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Settings className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('notifications.settings.title')}
            </div>
          </button>
        </nav>
      </div>

      {/* Search and Filters */}
      {activeTab !== 'settings' && (
        <div className={`flex flex-col lg:flex-row gap-4 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
          <div className="relative flex-1">
            <Search className={`absolute top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 ${isRTL ? 'right-3' : 'left-3'}`} />
            <input
              type="text"
              placeholder={t('common.actions.search')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full ${isRTL ? 'pr-10 pl-4 text-right' : 'pl-10 pr-4 text-left'} py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
            />
          </div>

          <div className="flex items-center gap-3">
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
              className={`px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
            >
              <option value="all">{t('notifications.filters.all')}</option>
              <option value="unread">{t('notifications.filters.unread')}</option>
              <option value="critical">{t('notifications.filters.critical')}</option>
              <option value="today">{t('notifications.filters.today')}</option>
              <option value="system">{t('notifications.filters.system')}</option>
              <option value="dispute">{t('notifications.filters.disputes')}</option>
              <option value="trade">{t('notifications.filters.trades')}</option>
              <option value="user">{t('notifications.filters.users')}</option>
              <option value="security">{t('notifications.filters.security')}</option>
            </select>

            <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
              <Filter className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('common.actions.filter')}
            </button>

            <button
              onClick={() => handleBulkAction('markAllAsRead')}
              className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
            >
              <Check className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('notifications.actions.markAllAsRead')}
            </button>
          </div>
        </div>
      )}

      {/* Notifications List */}
      {activeTab !== 'settings' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    <input
                      type="checkbox"
                      className="rounded"
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedNotifications(filteredNotifications.map(n => n.id));
                          setShowBulkActions(true);
                        } else {
                          setSelectedNotifications([]);
                          setShowBulkActions(false);
                        }
                      }}
                    />
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('notifications.title')} & {t('common.labels.type')}
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('notifications.priority.high')} & {t('common.labels.status')}
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('notifications.channels.inApp')} & {t('common.labels.time')}
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('common.actions.title')}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredNotifications.map((notification) => {
                  const CategoryIcon = getCategoryIcon(notification.category);
                  return (
                    <tr key={notification.id} className={`hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${notification.status === 'unread' ? 'bg-blue-50 dark:bg-blue-900/10' : ''}`}>
                      <td className="py-4 px-4">
                        <input
                          type="checkbox"
                          className="rounded"
                          checked={selectedNotifications.includes(notification.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedNotifications([...selectedNotifications, notification.id]);
                              setShowBulkActions(true);
                            } else {
                              setSelectedNotifications(selectedNotifications.filter(id => id !== notification.id));
                              if (selectedNotifications.length === 1) setShowBulkActions(false);
                            }
                          }}
                        />
                      </td>
                      <td className="py-4 px-4">
                        <div className={isRTL ? 'text-right' : 'text-left'}>
                          <div className={`font-medium text-gray-900 dark:text-white mb-1 flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <CategoryIcon className={`w-4 h-4 text-gray-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                            {notification.title}
                            {notification.status === 'unread' && <div className={`w-2 h-2 bg-blue-500 rounded-full ${isRTL ? 'mr-2' : 'ml-2'}`} />}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-300 mb-2 line-clamp-2">
                            {notification.message}
                          </div>
                          <div className="flex items-center gap-2">
                            <TypeBadge type={notification.type} />
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {t(`notifications.types.${notification.category}`)}
                            </span>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={`space-y-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                          <PriorityBadge priority={notification.priority} />
                          <StatusBadge status={notification.status} />
                          {notification.metadata?.severity && (
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              Severity: {notification.metadata.severity}/10
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={isRTL ? 'text-right' : 'text-left'}>
                          <div className="flex items-center gap-1 mb-2">
                            {notification.channels.map((channel) => {
                              const ChannelIcon = channel === 'email' ? Mail :
                                                channel === 'sms' ? Smartphone :
                                                channel === 'webhook' ? Webhook :
                                                channel === 'slack' ? Slack : Bell;
                              return (
                                <ChannelIcon key={channel} className="w-3 h-3 text-gray-500" title={t(`notifications.channels.${channel}`)} />
                              );
                            })}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-300">
                            {formatRelativeTime(notification.createdAt)}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {formatDate(notification.createdAt)}
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <button
                            onClick={() => {
                              setSelectedNotification(notification);
                              setShowDetails(true);
                            }}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 p-1 rounded transition-colors"
                            title={t('notifications.actions.viewDetails')}
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          {notification.status === 'unread' && (
                            <button
                              onClick={() => handleNotificationAction(notification.id, 'markAsRead')}
                              className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 p-1 rounded transition-colors"
                              title={t('notifications.actions.markAsRead')}
                            >
                              <Check className="w-4 h-4" />
                            </button>
                          )}
                          {notification.status !== 'acknowledged' && ['critical', 'urgent', 'high'].includes(notification.priority) && (
                            <button
                              onClick={() => handleNotificationAction(notification.id, 'acknowledge')}
                              className="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 p-1 rounded transition-colors"
                              title={t('notifications.actions.acknowledge')}
                            >
                              <Flag className="w-4 h-4" />
                            </button>
                          )}
                          <button
                            onClick={() => handleNotificationAction(notification.id, 'dismiss')}
                            className="text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 p-1 rounded transition-colors"
                            title={t('notifications.actions.dismiss')}
                          >
                            <X className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleNotificationAction(notification.id, 'delete')}
                            className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 p-1 rounded transition-colors"
                            title={t('notifications.actions.deleteNotification')}
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* Empty State */}
          {filteredNotifications.length === 0 && (
            <div className="text-center py-12">
              <Bell className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{t('common.messages.noData')}</h3>
              <p className="text-gray-600 dark:text-gray-400">{t('common.actions.search')}</p>
            </div>
          )}
        </div>
      )}

      {/* Settings Tab */}
      {activeTab === 'settings' && (
        <div className="space-y-6">
          {/* Notification Preferences */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('notifications.settings.preferences')}
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Email Notifications */}
              <div className="space-y-4">
                <h5 className="font-medium text-gray-900 dark:text-white flex items-center">
                  <Mail className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('notifications.channels.email')}
                </h5>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {t('notifications.disputeAlerts')}
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {t('notifications.securityAlerts')}
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded" />
                    <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {t('notifications.tradeAlerts')}
                    </span>
                  </label>
                </div>
              </div>

              {/* SMS Notifications */}
              <div className="space-y-4">
                <h5 className="font-medium text-gray-900 dark:text-white flex items-center">
                  <Smartphone className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('notifications.channels.sms')}
                </h5>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {t('notifications.securityAlerts')}
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded" />
                    <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {t('notifications.systemAlerts')}
                    </span>
                  </label>
                </div>
              </div>

              {/* Push Notifications */}
              <div className="space-y-4">
                <h5 className="font-medium text-gray-900 dark:text-white flex items-center">
                  <Bell className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('notifications.channels.push')}
                </h5>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {t('notifications.allNotifications')}
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {t('notifications.systemAlerts')}
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Notification Templates */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('notifications.settings.templates')}
            </h4>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                  <h5 className="font-medium text-gray-900 dark:text-white mb-2">
                    {t('notifications.templates.disputeCreated')}
                  </h5>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    Template for new dispute notifications
                  </p>
                  <div className="flex items-center gap-2">
                    <button className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm">
                      <Edit className="w-4 h-4 inline mr-1" />
                      {t('notifications.actions.editAlert')}
                    </button>
                    <button className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 text-sm">
                      <Send className="w-4 h-4 inline mr-1" />
                      {t('notifications.actions.testAlert')}
                    </button>
                  </div>
                </div>

                <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                  <h5 className="font-medium text-gray-900 dark:text-white mb-2">
                    {t('notifications.templates.securityBreach')}
                  </h5>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    Template for security breach alerts
                  </p>
                  <div className="flex items-center gap-2">
                    <button className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm">
                      <Edit className="w-4 h-4 inline mr-1" />
                      {t('notifications.actions.editAlert')}
                    </button>
                    <button className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 text-sm">
                      <Send className="w-4 h-4 inline mr-1" />
                      {t('notifications.actions.testAlert')}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Notifications List */}
      {activeTab !== 'settings' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    <input
                      type="checkbox"
                      className="rounded"
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedNotifications(filteredNotifications.map(n => n.id));
                          setShowBulkActions(true);
                        } else {
                          setSelectedNotifications([]);
                          setShowBulkActions(false);
                        }
                      }}
                    />
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('notifications.title')} & {t('common.labels.type')}
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('notifications.priority.high')} & {t('common.labels.status')}
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('notifications.channels.inApp')} & {t('common.labels.time')}
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('common.actions.title')}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredNotifications.map((notification) => {
                  const CategoryIcon = getCategoryIcon(notification.category);
                  return (
                    <tr key={notification.id} className={`hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${notification.status === 'unread' ? 'bg-blue-50 dark:bg-blue-900/10' : ''}`}>
                      <td className="py-4 px-4">
                        <input
                          type="checkbox"
                          className="rounded"
                          checked={selectedNotifications.includes(notification.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedNotifications([...selectedNotifications, notification.id]);
                              setShowBulkActions(true);
                            } else {
                              setSelectedNotifications(selectedNotifications.filter(id => id !== notification.id));
                              if (selectedNotifications.length === 1) setShowBulkActions(false);
                            }
                          }}
                        />
                      </td>
                      <td className="py-4 px-4">
                        <div className={isRTL ? 'text-right' : 'text-left'}>
                          <div className={`font-medium text-gray-900 dark:text-white mb-1 flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <CategoryIcon className={`w-4 h-4 text-gray-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                            {notification.title}
                            {notification.status === 'unread' && <div className={`w-2 h-2 bg-blue-500 rounded-full ${isRTL ? 'mr-2' : 'ml-2'}`} />}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-300 mb-2 line-clamp-2">
                            {notification.message}
                          </div>
                          <div className="flex items-center gap-2">
                            <TypeBadge type={notification.type} />
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {t(`notifications.types.${notification.category}`)}
                            </span>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={`space-y-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                          <PriorityBadge priority={notification.priority} />
                          <StatusBadge status={notification.status} />
                          {notification.metadata?.severity && (
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              Severity: {notification.metadata.severity}/10
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={isRTL ? 'text-right' : 'text-left'}>
                          <div className="flex items-center gap-1 mb-2">
                            {notification.channels.map((channel) => {
                              const ChannelIcon = channel === 'email' ? Mail :
                                                channel === 'sms' ? Smartphone :
                                                channel === 'webhook' ? Webhook :
                                                channel === 'slack' ? Slack : Bell;
                              return (
                                <ChannelIcon key={channel} className="w-3 h-3 text-gray-500" title={t(`notifications.channels.${channel}`)} />
                              );
                            })}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-300">
                            {formatRelativeTime(notification.createdAt)}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {formatDate(notification.createdAt)}
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <button
                            onClick={() => {
                              setSelectedNotification(notification);
                              setShowDetails(true);
                            }}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 p-1 rounded transition-colors"
                            title={t('notifications.actions.viewDetails')}
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          {notification.status === 'unread' && (
                            <button
                              onClick={() => handleNotificationAction(notification.id, 'markAsRead')}
                              className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 p-1 rounded transition-colors"
                              title={t('notifications.actions.markAsRead')}
                            >
                              <Check className="w-4 h-4" />
                            </button>
                          )}
                          {notification.status !== 'acknowledged' && ['critical', 'urgent', 'high'].includes(notification.priority) && (
                            <button
                              onClick={() => handleNotificationAction(notification.id, 'acknowledge')}
                              className="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 p-1 rounded transition-colors"
                              title={t('notifications.actions.acknowledge')}
                            >
                              <Flag className="w-4 h-4" />
                            </button>
                          )}
                          <button
                            onClick={() => handleNotificationAction(notification.id, 'dismiss')}
                            className="text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 p-1 rounded transition-colors"
                            title={t('notifications.actions.dismiss')}
                          >
                            <X className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleNotificationAction(notification.id, 'delete')}
                            className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 p-1 rounded transition-colors"
                            title={t('notifications.actions.deleteNotification')}
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* Empty State */}
          {filteredNotifications.length === 0 && (
            <div className="text-center py-12">
              <Bell className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{t('common.messages.noData')}</h3>
              <p className="text-gray-600 dark:text-gray-400">{t('common.actions.search')}</p>
            </div>
          )}
        </div>
      )}

      {/* Settings Tab */}
      {activeTab === 'settings' && (
        <div className="space-y-6">
          {/* Notification Preferences */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('notifications.settings.preferences')}
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Email Notifications */}
              <div className="space-y-4">
                <h5 className="font-medium text-gray-900 dark:text-white flex items-center">
                  <Mail className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('notifications.channels.email')}
                </h5>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {t('notifications.disputeAlerts')}
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {t('notifications.securityAlerts')}
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded" />
                    <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {t('notifications.tradeAlerts')}
                    </span>
                  </label>
                </div>
              </div>

              {/* SMS Notifications */}
              <div className="space-y-4">
                <h5 className="font-medium text-gray-900 dark:text-white flex items-center">
                  <Smartphone className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('notifications.channels.sms')}
                </h5>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {t('notifications.securityAlerts')}
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded" />
                    <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {t('notifications.systemAlerts')}
                    </span>
                  </label>
                </div>
              </div>

              {/* Push Notifications */}
              <div className="space-y-4">
                <h5 className="font-medium text-gray-900 dark:text-white flex items-center">
                  <Bell className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('notifications.channels.push')}
                </h5>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {t('notifications.allNotifications')}
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {t('notifications.systemAlerts')}
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Notification Templates */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('notifications.settings.templates')}
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                <h5 className="font-medium text-gray-900 dark:text-white mb-2">
                  {t('notifications.templates.disputeCreated')}
                </h5>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  Template for new dispute notifications
                </p>
                <div className="flex items-center gap-2">
                  <button className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm">
                    <Edit className="w-4 h-4 inline mr-1" />
                    {t('notifications.actions.editAlert')}
                  </button>
                  <button className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 text-sm">
                    <Send className="w-4 h-4 inline mr-1" />
                    {t('notifications.actions.testAlert')}
                  </button>
                </div>
              </div>

              <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                <h5 className="font-medium text-gray-900 dark:text-white mb-2">
                  {t('notifications.templates.securityBreach')}
                </h5>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  Template for security breach alerts
                </p>
                <div className="flex items-center gap-2">
                  <button className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm">
                    <Edit className="w-4 h-4 inline mr-1" />
                    {t('notifications.actions.editAlert')}
                  </button>
                  <button className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 text-sm">
                    <Send className="w-4 h-4 inline mr-1" />
                    {t('notifications.actions.testAlert')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bulk Actions Panel */}
      {showBulkActions && selectedNotifications.length > 0 && (
        <div className={`fixed bottom-6 ${isRTL ? 'left-6' : 'right-6'} bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 z-50`}>
          <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {selectedNotifications.length} {t('common.actions.selected')}
            </span>
            <button
              onClick={() => handleBulkAction('markAsRead')}
              className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors"
            >
              {t('notifications.actions.markAsRead')}
            </button>
            <button
              onClick={() => handleBulkAction('acknowledge')}
              className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm transition-colors"
            >
              {t('notifications.actions.acknowledge')}
            </button>
            <button
              onClick={() => handleBulkAction('dismiss')}
              className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition-colors"
            >
              {t('notifications.actions.dismiss')}
            </button>
            <button
              onClick={() => handleBulkAction('delete')}
              className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors"
            >
              {t('notifications.actions.deleteNotification')}
            </button>
            <button
              onClick={() => {
                setSelectedNotifications([]);
                setShowBulkActions(false);
              }}
              className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition-colors"
            >
              {t('common.actions.cancel')}
            </button>
          </div>
        </div>
      )}

      {/* Notification Details Modal */}
      {showDetails && selectedNotification && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className={`flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t('notifications.actions.viewDetails')}
              </h3>
              <button
                onClick={() => setShowDetails(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-2">
                    {selectedNotification.title}
                  </h4>
                  <p className="text-gray-700 dark:text-gray-300">
                    {selectedNotification.message}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Type:</span>
                    <div className="mt-1">
                      <TypeBadge type={selectedNotification.type} />
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Priority:</span>
                    <div className="mt-1">
                      <PriorityBadge priority={selectedNotification.priority} />
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Status:</span>
                    <div className="mt-1">
                      <StatusBadge status={selectedNotification.status} />
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Category:</span>
                    <div className="mt-1 text-sm text-gray-900 dark:text-white">
                      {t(`notifications.types.${selectedNotification.category}`)}
                    </div>
                  </div>
                </div>

                <div>
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Channels:</span>
                  <div className="mt-1 flex items-center gap-2">
                    {selectedNotification.channels.map((channel) => (
                      <span key={channel} className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs">
                        {t(`notifications.channels.${channel}`)}
                      </span>
                    ))}
                  </div>
                </div>

                <div>
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Created:</span>
                  <div className="mt-1 text-sm text-gray-900 dark:text-white">
                    {formatDate(selectedNotification.createdAt)} ({formatRelativeTime(selectedNotification.createdAt)})
                  </div>
                </div>

                {selectedNotification.metadata && (
                  <div>
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Metadata:</span>
                    <div className="mt-1 bg-gray-50 dark:bg-gray-700 rounded p-3 text-sm">
                      <pre className="text-gray-900 dark:text-white">
                        {JSON.stringify(selectedNotification.metadata, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}
              </div>

              <div className={`flex items-center gap-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
                {selectedNotification.status === 'unread' && (
                  <button
                    onClick={() => handleNotificationAction(selectedNotification.id, 'markAsRead')}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                  >
                    {t('notifications.actions.markAsRead')}
                  </button>
                )}
                {selectedNotification.status !== 'acknowledged' && ['critical', 'urgent', 'high'].includes(selectedNotification.priority) && (
                  <button
                    onClick={() => handleNotificationAction(selectedNotification.id, 'acknowledge')}
                    className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                  >
                    {t('notifications.actions.acknowledge')}
                  </button>
                )}
                <button
                  onClick={() => handleNotificationAction(selectedNotification.id, 'dismiss')}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                >
                  {t('notifications.actions.dismiss')}
                </button>
                {selectedNotification.actions?.url && (
                  <a
                    href={selectedNotification.actions.url}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  >
                    {selectedNotification.actions.primary || 'View'}
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
