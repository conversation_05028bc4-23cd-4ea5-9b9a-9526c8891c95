import { SUPPORTED_COUNTRIES, CURRENCY_SYMBOLS } from '@/constants';

// دالة للحصول على ترجمة اسم الدولة
export const getCountryTranslation = (countryCode: string, t: (key: string) => string): string => {
  const country = SUPPORTED_COUNTRIES.find(c => c.code === countryCode);
  if (!country) return countryCode;
  
  return t(country.nameKey);
};

// دالة للحصول على جميع الدول مترجمة
export const getTranslatedCountries = (t: (key: string) => string) => {
  return SUPPORTED_COUNTRIES.map(country => ({
    ...country,
    translatedName: t(country.nameKey)
  }));
};

// دالة للحصول على علم الدولة
export const getCountryFlag = (countryCode: string): string => {
  const country = SUPPORTED_COUNTRIES.find(c => c.code === countryCode);
  return country?.flag || '🌍';
};

// دالة للحصول على عملة الدولة
export const getCountryCurrency = (countryCode: string): string => {
  const country = SUPPORTED_COUNTRIES.find(c => c.code === countryCode);
  return country?.currency || 'USD';
};

// دالة للحصول على رمز العملة حسب اللغة
export const getCurrencySymbol = (currencyCode: string, language: string): string => {
  const lang = language === 'ar' ? 'ar' : 'en';
  return CURRENCY_SYMBOLS[lang][currencyCode as keyof typeof CURRENCY_SYMBOLS.ar] || currencyCode;
};

// دالة للحصول على رمز السهم المناسب حسب اللغة
export const getDirectionalArrow = (language: string, direction: 'next' | 'previous' = 'next'): string => {
  const isRTL = language === 'ar';

  if (direction === 'next') {
    return isRTL ? '←' : '→';
  } else {
    return isRTL ? '→' : '←';
  }
};
