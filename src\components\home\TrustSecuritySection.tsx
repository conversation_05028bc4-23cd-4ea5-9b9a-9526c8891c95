'use client';

import { useState } from 'react';
import { 
  Shield, 
  Lock, 
  CheckCircle, 
  Award,
  Globe,
  Eye,
  FileText,
  Zap,
  Users,
  Star,
  TrendingUp,
  Layers
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { motion, AnimatePresence } from 'framer-motion';

interface SecurityFeature {
  id: string;
  icon: React.ElementType;
  title: string;
  description: string;
  details: string;
  color: string;
  bgColor: string;
  stats: {
    label: string;
    value: string;
  };
}

export default function TrustSecuritySection() {
  const { t } = useTranslation();
  const [activeFeature, setActiveFeature] = useState<string>('blockchain');

  const securityFeatures: SecurityFeature[] = [
    {
      id: 'blockchain',
      icon: Layers,
      title: t('home.trustSecurity.features.blockchain.title'),
      description: t('home.trustSecurity.features.blockchain.description'),
      details: t('home.trustSecurity.features.blockchain.details'),
      color: 'text-blue-600',
      bgColor: 'from-blue-500 to-blue-600',
      stats: {
        label: t('home.trustSecurity.stats.smartContracts'),
        value: '100%'
      }
    },
    {
      id: 'encryption',
      icon: Lock,
      title: t('home.trustSecurity.features.encryption.title'),
      description: t('home.trustSecurity.features.encryption.description'),
      details: t('home.trustSecurity.features.encryption.details'),
      color: 'text-green-600',
      bgColor: 'from-green-500 to-green-600',
      stats: {
        label: t('home.trustSecurity.stats.encryptionLevel'),
        value: 'AES-256'
      }
    },
    {
      id: 'verification',
      icon: CheckCircle,
      title: t('home.trustSecurity.features.verification.title'),
      description: t('home.trustSecurity.features.verification.description'),
      details: t('home.trustSecurity.features.verification.details'),
      color: 'text-purple-600',
      bgColor: 'from-purple-500 to-purple-600',
      stats: {
        label: t('home.trustSecurity.stats.verifiedUsers'),
        value: '95%'
      }
    },
    {
      id: 'insurance',
      icon: Shield,
      title: t('home.trustSecurity.features.insurance.title'),
      description: t('home.trustSecurity.features.insurance.description'),
      details: t('home.trustSecurity.features.insurance.details'),
      color: 'text-orange-600',
      bgColor: 'from-orange-500 to-orange-600',
      stats: {
        label: t('home.trustSecurity.stats.insuranceCoverage'),
        value: '$10M'
      }
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const activeFeatureData = securityFeatures.find(f => f.id === activeFeature) || securityFeatures[0];

  return (
    <section className="py-12 lg:py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 dark:from-black dark:via-gray-900 dark:to-gray-800 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        {/* Animated Security Grid */}
        <div className="absolute inset-0 opacity-10">
          <div className="grid grid-cols-8 gap-4 h-full">
            {[...Array(64)].map((_, i) => (
              <motion.div
                key={i}
                className="bg-white/20 rounded-sm"
                animate={{
                  opacity: [0.1, 0.3, 0.1],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  delay: i * 0.1
                }}
              />
            ))}
          </div>
        </div>

        {/* Floating Security Icons - Hidden on mobile */}
        <motion.div
          className="hidden lg:flex absolute top-20 left-20 w-16 h-16 bg-blue-500/20 rounded-full items-center justify-center"
          animate={{
            y: [-10, 10, -10],
            rotate: [0, 180, 360]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Shield className="w-8 h-8 text-blue-400" />
        </motion.div>

        <motion.div
          className="hidden lg:flex absolute bottom-20 right-20 w-12 h-12 bg-green-500/20 rounded-full items-center justify-center"
          animate={{
            y: [10, -10, 10],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Lock className="w-6 h-6 text-green-400" />
        </motion.div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {/* Section Header */}
          <motion.div className="text-center mb-12 lg:mb-16 px-4" variants={itemVariants}>
            <div className="inline-flex items-center bg-white/10 backdrop-blur-sm text-white rounded-full px-4 lg:px-6 py-2 lg:py-3 mb-4 lg:mb-6 border border-white/20">
              <Shield className="w-4 h-4 lg:w-5 lg:h-5 ml-2" />
              <span className="text-xs lg:text-sm font-medium">{t('home.trustSecurity.subtitle')}</span>
            </div>
            <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-6xl font-bold text-white mb-4 lg:mb-6">
              {t('home.trustSecurity.title')}
            </h2>
            <p className="text-base lg:text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
              {t('home.trustSecurity.subtitle')}
            </p>
          </motion.div>

          <div className="max-w-7xl mx-auto px-4">
            <div className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
              {/* Security Features */}
              <motion.div className="space-y-4 lg:space-y-6" variants={itemVariants}>
                {securityFeatures.map((feature, index) => {
                  const IconComponent = feature.icon;
                  const isActive = activeFeature === feature.id;
                  
                  return (
                    <motion.div
                      key={feature.id}
                      className={`group cursor-pointer transition-all duration-300 ${
                        isActive ? 'scale-105' : 'hover:scale-102'
                      }`}
                      onClick={() => setActiveFeature(feature.id)}
                      whileHover={{ x: 10 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <div className={`relative p-4 lg:p-6 rounded-2xl border-2 transition-all duration-300 ${
                        isActive
                          ? 'border-blue-400 bg-white/10 shadow-2xl'
                          : 'border-white/20 bg-white/5 hover:border-white/40 hover:bg-white/10'
                      }`}>
                        <div className="flex items-start gap-3 lg:gap-4">
                          {/* Icon */}
                          <div className={`w-12 h-12 lg:w-16 lg:h-16 rounded-xl bg-gradient-to-br ${feature.bgColor} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                            <IconComponent className="w-6 h-6 lg:w-8 lg:h-8 text-white" />
                          </div>

                          {/* Content */}
                          <div className="flex-1 min-w-0">
                            <h3 className={`text-lg lg:text-xl font-bold mb-2 transition-colors ${
                              isActive ? feature.color.replace('600', '400') : 'text-white group-hover:' + feature.color.replace('600', '400')
                            }`}>
                              {feature.title}
                            </h3>
                            <p className="text-blue-100 mb-3 leading-relaxed text-sm lg:text-base">
                              {feature.description}
                            </p>

                            {/* Stats */}
                            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
                              <div className={`px-3 py-1 rounded-full text-xs font-bold bg-gradient-to-r ${feature.bgColor} text-white`}>
                                {feature.stats.value}
                              </div>
                              <span className="text-blue-200 text-xs lg:text-sm">
                                {feature.stats.label}
                              </span>
                            </div>
                          </div>

                          {/* Active Indicator */}
                          {isActive && (
                            <motion.div
                              className="flex-shrink-0"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ type: "spring", stiffness: 300 }}
                            >
                              <div className={`w-3 h-3 rounded-full bg-gradient-to-br ${feature.bgColor}`} />
                            </motion.div>
                          )}
                        </div>

                        {/* Hover Effect */}
                        <div className={`absolute inset-0 bg-gradient-to-br ${feature.bgColor} rounded-2xl opacity-0 group-hover:opacity-5 transition-opacity duration-300`} />
                      </div>
                    </motion.div>
                  );
                })}
              </motion.div>

              {/* Active Feature Showcase */}
              <motion.div className="relative mt-8 lg:mt-0" variants={itemVariants}>
                <AnimatePresence mode="wait">
                  <motion.div
                    key={activeFeature}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    transition={{ duration: 0.3 }}
                    className="bg-white/10 backdrop-blur-lg rounded-2xl lg:rounded-3xl p-6 lg:p-8 border border-white/20 shadow-2xl"
                  >
                    {/* Feature Icon and Title */}
                    <div className="text-center mb-6 lg:mb-8">
                      <div className={`w-16 h-16 lg:w-24 lg:h-24 rounded-xl lg:rounded-2xl bg-gradient-to-br ${activeFeatureData.bgColor} flex items-center justify-center mx-auto mb-4 lg:mb-6 shadow-lg`}>
                        <activeFeatureData.icon className="w-8 h-8 lg:w-12 lg:h-12 text-white" />
                      </div>
                      <h3 className="text-xl lg:text-2xl font-bold text-white mb-3">
                        {activeFeatureData.title}
                      </h3>
                      <p className="text-blue-100 leading-relaxed text-sm lg:text-base">
                        {activeFeatureData.details}
                      </p>
                    </div>

                    {/* Security Visualization */}
                    <div className="relative">
                      {activeFeature === 'blockchain' && (
                        <div className="space-y-4">
                          <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10">
                            <span className="text-blue-200">Smart Contract</span>
                            <div className="flex items-center gap-2">
                              <CheckCircle className="w-5 h-5 text-green-400" />
                              <span className="text-green-400 font-bold">{t('home.trustSecurity.status.audited')}</span>
                            </div>
                          </div>
                          <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10">
                            <span className="text-blue-200">{t('home.trustSecurity.hardcodedValues.bscNetwork')}</span>
                            <div className="flex items-center gap-2">
                              <CheckCircle className="w-5 h-5 text-green-400" />
                              <span className="text-green-400 font-bold">{t('home.trustSecurity.status.connected')}</span>
                            </div>
                          </div>
                          <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10">
                            <span className="text-blue-200">{t('home.trustSecurity.hardcodedValues.fundProtectionLabel')}</span>
                            <div className="flex items-center gap-2">
                              <CheckCircle className="w-5 h-5 text-green-400" />
                              <span className="text-green-400 font-bold">100%</span>
                            </div>
                          </div>
                        </div>
                      )}

                      {activeFeature === 'encryption' && (
                        <div className="space-y-4">
                          <div className="p-4 bg-white/5 rounded-xl border border-white/10">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-blue-200">{t('home.trustSecurity.hardcodedValues.dataEncryption')}</span>
                              <span className="text-green-400 font-bold">AES-256</span>
                            </div>
                            <div className="w-full bg-white/10 rounded-full h-2">
                              <motion.div
                                className="bg-gradient-to-r from-green-500 to-green-400 h-2 rounded-full"
                                initial={{ width: 0 }}
                                animate={{ width: '100%' }}
                                transition={{ duration: 2, delay: 0.5 }}
                              />
                            </div>
                          </div>
                          <div className="p-4 bg-white/5 rounded-xl border border-white/10">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-blue-200">{t('home.trustSecurity.hardcodedValues.sslTls')}</span>
                              <span className="text-green-400 font-bold">Active</span>
                            </div>
                            <div className="w-full bg-white/10 rounded-full h-2">
                              <motion.div 
                                className="bg-gradient-to-r from-blue-500 to-blue-400 h-2 rounded-full"
                                initial={{ width: 0 }}
                                animate={{ width: '100%' }}
                                transition={{ duration: 2, delay: 1 }}
                              />
                            </div>
                          </div>
                        </div>
                      )}

                      {activeFeature === 'verification' && (
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                          <div className="text-center p-4 bg-white/5 rounded-xl border border-white/10">
                            <Users className="w-6 h-6 lg:w-8 lg:h-8 text-purple-400 mx-auto mb-2" />
                            <div className="text-xl lg:text-2xl font-bold text-white">95%</div>
                            <div className="text-purple-200 text-xs lg:text-sm">{t('home.trustSecurity.stats.verifiedUsers')}</div>
                          </div>
                          <div className="text-center p-4 bg-white/5 rounded-xl border border-white/10">
                            <CheckCircle className="w-6 h-6 lg:w-8 lg:h-8 text-green-400 mx-auto mb-2" />
                            <div className="text-lg lg:text-2xl font-bold text-white">{t('home.trustSecurity.hardcodedValues.kycVerification')}</div>
                            <div className="text-green-200 text-xs lg:text-sm">{t('home.trustSecurity.status.identityVerified')}</div>
                          </div>
                        </div>
                      )}

                      {activeFeature === 'insurance' && (
                        <div className="text-center">
                          <div className="w-16 h-16 lg:w-20 lg:h-20 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Shield className="w-8 h-8 lg:w-10 lg:h-10 text-white" />
                          </div>
                          <div className="text-2xl lg:text-3xl font-bold text-white mb-2">$10M</div>
                          <div className="text-orange-200 text-sm lg:text-base">{t('home.trustSecurity.insurance.comprehensive')}</div>
                          <div className="mt-4 p-3 bg-white/5 rounded-xl border border-white/10">
                            <div className="text-xs lg:text-sm text-orange-200">
                              {t('home.trustSecurity.insurance.fullProtection')}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Trust Badge */}
                    <div className="mt-8 text-center">
                      <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 rounded-full border border-white/20">
                        <Award className="w-5 h-5 text-yellow-400" />
                        <span className="text-white font-medium">{t('home.trustSecurity.badge.certified')}</span>
                      </div>
                    </div>
                  </motion.div>
                </AnimatePresence>
              </motion.div>
            </div>
          </div>

          {/* Bottom Trust Indicators - Responsive Grid */}
          <motion.div
            className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 mt-12 lg:mt-20 pt-8 lg:pt-16 border-t border-white/20 px-4"
            variants={itemVariants}
          >
            <div className="text-center trust-security-card trust-float p-4 lg:p-6">
              <div className="trust-indicator-icon bg-gradient-to-br from-green-500 to-emerald-600 mb-3 lg:mb-4">
                <Shield className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" />
              </div>
              <h4 className="trust-indicator-title">100%</h4>
              <p className="trust-indicator-description">{t('home.trustSecurity.quickStats.fundProtection')}</p>
            </div>

            <div className="text-center trust-security-card trust-float p-4 lg:p-6">
              <div className="trust-indicator-icon bg-gradient-to-br from-blue-500 to-blue-600 mb-3 lg:mb-4">
                <Lock className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" />
              </div>
              <h4 className="trust-indicator-title">AES-256</h4>
              <p className="trust-indicator-description">{t('home.trustSecurity.quickStats.advancedEncryption')}</p>
            </div>

            <div className="text-center trust-security-card trust-float p-4 lg:p-6">
              <div className="trust-indicator-icon bg-gradient-to-br from-purple-500 to-purple-600 mb-3 lg:mb-4">
                <CheckCircle className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" />
              </div>
              <h4 className="trust-indicator-title">95%</h4>
              <p className="trust-indicator-description">{t('home.trustSecurity.quickStats.verifiedUsers')}</p>
            </div>

            <div className="text-center trust-security-card trust-float p-4 lg:p-6 col-span-2 lg:col-span-1">
              <div className="trust-indicator-icon bg-gradient-to-br from-orange-500 to-red-600 mb-3 lg:mb-4">
                <Award className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" />
              </div>
              <h4 className="trust-indicator-title">$10M</h4>
              <p className="trust-indicator-description">{t('home.trustSecurity.quickStats.comprehensiveInsurance')}</p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
