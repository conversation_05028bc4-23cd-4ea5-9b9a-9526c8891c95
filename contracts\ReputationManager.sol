// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";

/**
 * @title ReputationManager - Enhanced Reputation System
 * @dev Manages user reputation scores and levels across multiple networks
 * <AUTHOR> P2P Team
 * @notice This contract handles reputation tracking for P2P trading platform
 */
contract ReputationManager is AccessControl, ReentrancyGuard, Pausable {
    
    // Role definitions
    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant ESCROW_ROLE = keccak256("ESCROW_ROLE");
    bytes32 public constant ORACLE_ROLE = keccak256("ORACLE_ROLE");

    // Reputation levels
    enum ReputationLevel {
        Beginner,    // 0-99 points
        Intermediate,// 100-299 points
        Advanced,    // 300-599 points
        Expert,      // 600-899 points
        Master       // 900+ points
    }

    // Reputation data structure
    struct ReputationData {
        uint256 score;
        ReputationLevel level;
        uint256 totalTrades;
        uint256 successfulTrades;
        uint256 disputedTrades;
        uint256 resolvedDisputes;
        uint256 averageRating; // Rating out of 1000 (100.0%)
        uint256 lastUpdated;
        bool isActive;
    }

    // Network-specific reputation
    struct NetworkReputation {
        mapping(address => ReputationData) userReputation;
        uint256 totalUsers;
        uint256 averageScore;
    }

    // Rating structure
    struct Rating {
        address rater;
        address rated;
        uint256 networkId;
        uint256 tradeId;
        uint256 rating; // 1-5 stars (multiplied by 200 for precision)
        string comment;
        uint256 timestamp;
        bool isValid;
    }

    // State variables
    mapping(uint256 => NetworkReputation) private networkReputations;
    mapping(bytes32 => Rating) public ratings; // keccak256(rater, rated, tradeId) => Rating
    mapping(address => mapping(uint256 => bool)) public hasRated; // user => tradeId => hasRated
    
    uint256[] public supportedNetworks;
    mapping(uint256 => bool) public isNetworkSupported;
    
    // Reputation parameters
    uint256 public constant MIN_SCORE = 0;
    uint256 public constant MAX_SCORE = 1000;
    uint256 public constant INITIAL_SCORE = 100;
    uint256 public constant SUCCESSFUL_TRADE_BONUS = 5;
    uint256 public constant DISPUTED_TRADE_PENALTY = 10;
    uint256 public constant RESOLVED_DISPUTE_BONUS = 3;
    uint256 public constant RATING_WEIGHT = 2;

    // Events
    event ReputationUpdated(
        address indexed user,
        uint256 indexed networkId,
        uint256 oldScore,
        uint256 newScore,
        ReputationLevel newLevel,
        string reason
    );

    event UserRated(
        address indexed rater,
        address indexed rated,
        uint256 indexed tradeId,
        uint256 networkId,
        uint256 rating,
        string comment
    );

    event NetworkAdded(uint256 indexed networkId, string name);
    event NetworkRemoved(uint256 indexed networkId);

    event ReputationReset(address indexed user, uint256 indexed networkId, string reason);

    // Modifiers
    modifier networkSupported(uint256 networkId) {
        require(isNetworkSupported[networkId], "Network not supported");
        _;
    }

    modifier validRating(uint256 rating) {
        require(rating >= 200 && rating <= 1000, "Invalid rating"); // 1-5 stars
        _;
    }

    modifier hasNotRated(address user, uint256 tradeId) {
        require(!hasRated[user][tradeId], "User has already rated this trade");
        _;
    }

    /**
     * @dev Constructor
     */
    constructor() {
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _grantRole(ADMIN_ROLE, msg.sender);
        
        // Add default networks
        _addNetwork(1); // BSC Testnet
        _addNetwork(2); // BSC Mainnet
    }

    /**
     * @dev Add support for a new network
     * @param networkId Network identifier
     */
    function addNetwork(uint256 networkId) external onlyRole(ADMIN_ROLE) {
        _addNetwork(networkId);
    }

    /**
     * @dev Internal function to add network
     * @param networkId Network identifier
     */
    function _addNetwork(uint256 networkId) internal {
        require(!isNetworkSupported[networkId], "Network already supported");
        
        isNetworkSupported[networkId] = true;
        supportedNetworks.push(networkId);
        
        emit NetworkAdded(networkId, "");
    }

    /**
     * @dev Remove support for a network
     * @param networkId Network identifier
     */
    function removeNetwork(uint256 networkId) external onlyRole(ADMIN_ROLE) networkSupported(networkId) {
        isNetworkSupported[networkId] = false;
        
        // Remove from supported networks array
        for (uint256 i = 0; i < supportedNetworks.length; i++) {
            if (supportedNetworks[i] == networkId) {
                supportedNetworks[i] = supportedNetworks[supportedNetworks.length - 1];
                supportedNetworks.pop();
                break;
            }
        }
        
        emit NetworkRemoved(networkId);
    }

    /**
     * @dev Initialize user reputation for a network
     * @param user User address
     * @param networkId Network identifier
     */
    function initializeUserReputation(address user, uint256 networkId) 
        external 
        onlyRole(ESCROW_ROLE) 
        networkSupported(networkId) 
    {
        ReputationData storage reputation = networkReputations[networkId].userReputation[user];
        
        if (!reputation.isActive) {
            reputation.score = INITIAL_SCORE;
            reputation.level = ReputationLevel.Beginner;
            reputation.totalTrades = 0;
            reputation.successfulTrades = 0;
            reputation.disputedTrades = 0;
            reputation.resolvedDisputes = 0;
            reputation.averageRating = 0;
            reputation.lastUpdated = block.timestamp;
            reputation.isActive = true;
            
            networkReputations[networkId].totalUsers++;
        }
    }

    /**
     * @dev Update user reputation after a trade
     * @param user User address
     * @param networkId Network identifier
     * @param change Score change (can be negative)
     * @param reason Reason for the change
     */
    function updateReputation(
        address user,
        uint256 networkId,
        int256 change,
        string memory reason
    ) external onlyRole(ESCROW_ROLE) networkSupported(networkId) {
        ReputationData storage reputation = networkReputations[networkId].userReputation[user];
        
        // Initialize if not active
        if (!reputation.isActive) {
            initializeUserReputation(user, networkId);
        }
        
        uint256 oldScore = reputation.score;
        
        // Apply change with bounds checking
        if (change > 0) {
            reputation.score = _min(reputation.score + uint256(change), MAX_SCORE);
        } else if (change < 0) {
            uint256 decrease = uint256(-change);
            reputation.score = reputation.score > decrease ? reputation.score - decrease : MIN_SCORE;
        }
        
        // Update level based on new score
        reputation.level = _calculateLevel(reputation.score);
        reputation.lastUpdated = block.timestamp;
        
        emit ReputationUpdated(user, networkId, oldScore, reputation.score, reputation.level, reason);
    }

    /**
     * @dev Record a successful trade
     * @param user User address
     * @param networkId Network identifier
     */
    function recordSuccessfulTrade(address user, uint256 networkId) 
        external 
        onlyRole(ESCROW_ROLE) 
        networkSupported(networkId) 
    {
        ReputationData storage reputation = networkReputations[networkId].userReputation[user];
        
        if (!reputation.isActive) {
            initializeUserReputation(user, networkId);
        }
        
        reputation.totalTrades++;
        reputation.successfulTrades++;
        
        // Apply successful trade bonus
        updateReputation(user, networkId, int256(SUCCESSFUL_TRADE_BONUS), "Successful trade completed");
    }

    /**
     * @dev Record a disputed trade
     * @param user User address
     * @param networkId Network identifier
     */
    function recordDisputedTrade(address user, uint256 networkId) 
        external 
        onlyRole(ESCROW_ROLE) 
        networkSupported(networkId) 
    {
        ReputationData storage reputation = networkReputations[networkId].userReputation[user];
        
        if (!reputation.isActive) {
            initializeUserReputation(user, networkId);
        }
        
        reputation.totalTrades++;
        reputation.disputedTrades++;
        
        // Apply dispute penalty
        updateReputation(user, networkId, -int256(DISPUTED_TRADE_PENALTY), "Trade disputed");
    }

    /**
     * @dev Record a resolved dispute
     * @param user User address
     * @param networkId Network identifier
     * @param inFavor Whether the dispute was resolved in user's favor
     */
    function recordResolvedDispute(address user, uint256 networkId, bool inFavor) 
        external 
        onlyRole(ADMIN_ROLE) 
        networkSupported(networkId) 
    {
        ReputationData storage reputation = networkReputations[networkId].userReputation[user];
        
        if (!reputation.isActive) {
            initializeUserReputation(user, networkId);
        }
        
        reputation.resolvedDisputes++;
        
        if (inFavor) {
            // Apply bonus for favorable resolution
            updateReputation(user, networkId, int256(RESOLVED_DISPUTE_BONUS), "Dispute resolved favorably");
        }
    }

    /**
     * @dev Rate a user after a trade
     * @param rated Address of user being rated
     * @param tradeId Trade identifier
     * @param networkId Network identifier
     * @param rating Rating value (200-1000, representing 1-5 stars)
     * @param comment Optional comment
     */
    function rateUser(
        address rated,
        uint256 tradeId,
        uint256 networkId,
        uint256 rating,
        string memory comment
    ) external 
        nonReentrant 
        whenNotPaused 
        networkSupported(networkId) 
        validRating(rating)
        hasNotRated(msg.sender, tradeId)
    {
        require(rated != msg.sender, "Cannot rate yourself");
        require(rated != address(0), "Invalid rated address");
        
        bytes32 ratingId = keccak256(abi.encodePacked(msg.sender, rated, tradeId));
        
        ratings[ratingId] = Rating({
            rater: msg.sender,
            rated: rated,
            networkId: networkId,
            tradeId: tradeId,
            rating: rating,
            comment: comment,
            timestamp: block.timestamp,
            isValid: true
        });
        
        hasRated[msg.sender][tradeId] = true;
        
        // Update average rating
        _updateAverageRating(rated, networkId, rating);
        
        emit UserRated(msg.sender, rated, tradeId, networkId, rating, comment);
    }

    /**
     * @dev Update user's average rating
     * @param user User address
     * @param networkId Network identifier
     * @param newRating New rating to include
     */
    function _updateAverageRating(address user, uint256 networkId, uint256 newRating) internal {
        ReputationData storage reputation = networkReputations[networkId].userReputation[user];
        
        if (!reputation.isActive) {
            initializeUserReputation(user, networkId);
        }
        
        // Simple moving average calculation
        // In production, you might want a more sophisticated approach
        if (reputation.averageRating == 0) {
            reputation.averageRating = newRating;
        } else {
            reputation.averageRating = (reputation.averageRating + newRating) / 2;
        }
        
        // Apply rating-based reputation change
        int256 ratingChange = int256((newRating * RATING_WEIGHT) / 200) - int256(RATING_WEIGHT * 2); // Normalize around 0
        updateReputation(user, networkId, ratingChange, "Rating received");
    }

    /**
     * @dev Get user reputation data
     * @param user User address
     * @param networkId Network identifier
     * @return ReputationData struct
     */
    function getUserReputation(address user, uint256 networkId) 
        external 
        view 
        networkSupported(networkId) 
        returns (ReputationData memory) 
    {
        return networkReputations[networkId].userReputation[user];
    }

    /**
     * @dev Get reputation level for a score
     * @param score Reputation score
     * @return ReputationLevel enum
     */
    function getReputationLevel(uint256 score) external pure returns (ReputationLevel) {
        return _calculateLevel(score);
    }

    /**
     * @dev Calculate reputation level based on score
     * @param score Reputation score
     * @return ReputationLevel enum
     */
    function _calculateLevel(uint256 score) internal pure returns (ReputationLevel) {
        if (score < 100) return ReputationLevel.Beginner;
        if (score < 300) return ReputationLevel.Intermediate;
        if (score < 600) return ReputationLevel.Advanced;
        if (score < 900) return ReputationLevel.Expert;
        return ReputationLevel.Master;
    }

    /**
     * @dev Get supported networks
     * @return Array of supported network IDs
     */
    function getSupportedNetworks() external view returns (uint256[] memory) {
        return supportedNetworks;
    }

    /**
     * @dev Get network statistics
     * @param networkId Network identifier
     * @return totalUsers Total users on network
     * @return averageScore Average reputation score
     */
    function getNetworkStats(uint256 networkId) 
        external 
        view 
        networkSupported(networkId) 
        returns (uint256 totalUsers, uint256 averageScore) 
    {
        NetworkReputation storage network = networkReputations[networkId];
        return (network.totalUsers, network.averageScore);
    }

    /**
     * @dev Reset user reputation (admin only)
     * @param user User address
     * @param networkId Network identifier
     * @param reason Reason for reset
     */
    function resetUserReputation(address user, uint256 networkId, string memory reason) 
        external 
        onlyRole(ADMIN_ROLE) 
        networkSupported(networkId) 
    {
        ReputationData storage reputation = networkReputations[networkId].userReputation[user];
        
        reputation.score = INITIAL_SCORE;
        reputation.level = ReputationLevel.Beginner;
        reputation.lastUpdated = block.timestamp;
        
        emit ReputationReset(user, networkId, reason);
    }

    /**
     * @dev Utility function to get minimum of two values
     * @param a First value
     * @param b Second value
     * @return Minimum value
     */
    function _min(uint256 a, uint256 b) internal pure returns (uint256) {
        return a < b ? a : b;
    }

    // Admin functions
    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
    }

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
    }
}
