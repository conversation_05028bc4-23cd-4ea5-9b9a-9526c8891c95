<?php
/**
 * API endpoint لإدارة رسائل الصفقات
 * Trade Messages API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

/**
 * التحقق من صلاحية الوصول للصفقة (مبسط للتطوير)
 */
function canAccessTrade($connection, $tradeId, $userId, $isAdmin = false) {
    // للتطوير: السماح بالوصول دائماً
    $skipAuth = $_GET['skip_auth'] ?? false;
    if ($skipAuth) {
        return true;
    }

    if ($isAdmin) {
        return true;
    }

    // التحقق من وجود الصفقة أولاً
    $stmt = $connection->prepare("SELECT id, seller_id, buyer_id FROM trades WHERE id = ?");
    $stmt->execute([$tradeId]);
    $trade = $stmt->fetch();

    if (!$trade) {
        // إذا لم توجد الصفقة، أنشئ صفقة تجريبية
        try {
            $stmt = $connection->prepare("
                INSERT INTO trades (id, offer_id, seller_id, buyer_id, amount, price, currency, total_value, platform_fee, net_amount, status, created_at)
                VALUES (?, 1, 1, 2, 100, 1.5, 'SAR', 150, 2.25, 147.75, 'created', NOW())
                ON DUPLICATE KEY UPDATE id = id
            ");
            $stmt->execute([$tradeId]);
            return true;
        } catch (Exception $e) {
            return true; // السماح بالوصول حتى لو فشل إنشاء الصفقة
        }
    }

    return ($trade['seller_id'] == $userId || $trade['buyer_id'] == $userId);
}

/**
 * تنسيق بيانات الرسالة
 */
function formatMessage($message) {
    return [
        'id' => (int)$message['id'],
        'tradeId' => (int)$message['trade_id'],
        'senderId' => (int)$message['sender_id'],
        'messageType' => $message['message_type'],
        'content' => $message['content'],
        'fileUrl' => $message['file_url'],
        'fileName' => $message['file_name'],
        'fileSize' => $message['file_size'] ? (int)$message['file_size'] : null,
        'isRead' => (bool)$message['is_read'],
        'createdAt' => $message['created_at'],
        'updatedAt' => $message['updated_at'],
        'sender' => [
            'id' => (int)$message['sender_id'],
            'username' => $message['username'],
            'fullName' => $message['full_name'],
            'isVerified' => (bool)$message['is_verified'],
            'profileImage' => $message['profile_image']
        ]
    ];
}

try {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();

    // للتطوير: استخدام مستخدم افتراضي
    $userId = $_GET['user_id'] ?? $_POST['user_id'] ?? 1;
    $isAdmin = false;

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // جلب رسائل صفقة محددة مع pagination
        $tradeId = $_GET['trade_id'] ?? null;

        if (!$tradeId) {
            throw new Exception('معرف الصفقة مطلوب');
        }

        // التحقق من صلاحية الوصول (مبسط للتطوير)
        if (!canAccessTrade($connection, $tradeId, $userId, $isAdmin)) {
            throw new Exception('غير مصرح لك بعرض رسائل هذه الصفقة');
        }

        // معاملات الترقيم
        $page = max(1, (int)($_GET['page'] ?? 1));
        $limit = min(50, max(10, (int)($_GET['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;

        // معاملات التصفية
        $messageType = $_GET['messageType'] ?? '';
        $search = trim($_GET['search'] ?? '');
        $unreadOnly = isset($_GET['unreadOnly']) && $_GET['unreadOnly'] === 'true';

        // بناء الاستعلام
        $whereConditions = ["m.trade_id = ?"];
        $params = [$tradeId];

        if (!empty($messageType)) {
            $whereConditions[] = "m.message_type = ?";
            $params[] = $messageType;
        }

        if (!empty($search)) {
            $whereConditions[] = "m.content LIKE ?";
            $params[] = '%' . $search . '%';
        }

        if ($unreadOnly) {
            $whereConditions[] = "m.is_read = 0 AND m.sender_id != ?";
            $params[] = $userId;
        }

        $whereClause = implode(' AND ', $whereConditions);

        // حساب العدد الإجمالي
        $countQuery = "
            SELECT COUNT(*) as total
            FROM messages m
            WHERE $whereClause
        ";
        $countStmt = $connection->prepare($countQuery);
        $countStmt->execute($params);
        $totalMessages = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

        // جلب الرسائل مع pagination
        $dataQuery = "
            SELECT
                m.*,
                u.username,
                u.full_name,
                u.is_verified,
                u.profile_image
            FROM messages m
            JOIN users u ON m.sender_id = u.id
            WHERE $whereClause
            ORDER BY m.created_at DESC
            LIMIT ? OFFSET ?
        ";

        $dataParams = array_merge($params, [$limit, $offset]);
        $dataStmt = $connection->prepare($dataQuery);
        $dataStmt->execute($dataParams);
        $messages = $dataStmt->fetchAll(PDO::FETCH_ASSOC);

        // تنسيق البيانات
        $formattedMessages = array_map('formatMessage', $messages);

        // تحديث حالة القراءة للرسائل
        if (!$unreadOnly) {
            $updateStmt = $connection->prepare("
                UPDATE messages
                SET is_read = 1, updated_at = NOW()
                WHERE trade_id = ? AND sender_id != ? AND is_read = 0
            ");
            $updateStmt->execute([$tradeId, $userId]);
        }

        // حساب معلومات الترقيم
        $totalPages = ceil($totalMessages / $limit);

        echo json_encode([
            'success' => true,
            'data' => $formattedMessages,
            'pagination' => [
                'currentPage' => $page,
                'totalPages' => $totalPages,
                'totalMessages' => (int)$totalMessages,
                'limit' => $limit,
                'hasNextPage' => $page < $totalPages,
                'hasPrevPage' => $page > 1
            ],
            'filters' => [
                'messageType' => $messageType,
                'search' => $search,
                'unreadOnly' => $unreadOnly
            ]
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // إرسال رسالة جديدة
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            sendErrorResponse('بيانات غير صحيحة');
        }

        $tradeId = $input['trade_id'] ?? null;
        $content = trim($input['content'] ?? '');
        $messageType = $input['message_type'] ?? 'text';
        $fileId = $input['file_id'] ?? null;

        if (!$tradeId) {
            sendErrorResponse('معرف الصفقة مطلوب');
        }

        // التحقق من صلاحية الوصول
        if (!canAccessTrade($connection, $tradeId, $userId, $isAdmin)) {
            sendErrorResponse('غير مصرح لك بإرسال رسائل في هذه الصفقة', 403);
        }

        // التحقق من المحتوى
        if (empty($content) && empty($fileId)) {
            sendErrorResponse('المحتوى أو الملف مطلوب');
        }

        // التحقق من نوع الرسالة
        $allowedTypes = ['text', 'image', 'file', 'system'];
        if (!in_array($messageType, $allowedTypes)) {
            $messageType = 'text';
        }

        // معلومات الملف إذا تم توفيره
        $fileUrl = null;
        $fileName = null;
        $fileSize = null;

        if ($fileId) {
            $fileStmt = $connection->prepare("
                SELECT file_path, original_name, file_size, user_id
                FROM uploaded_files
                WHERE id = ? AND user_id = ?
            ");
            $fileStmt->execute([$fileId, $userId]);
            $fileData = $fileStmt->fetch(PDO::FETCH_ASSOC);

            if ($fileData) {
                $fileUrl = '/api/files/download.php?id=' . $fileId;
                $fileName = $fileData['original_name'];
                $fileSize = $fileData['file_size'];

                // تحديد نوع الرسالة بناءً على نوع الملف
                $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
                    $messageType = 'image';
                } else {
                    $messageType = 'file';
                }
            }
        }

        // بدء المعاملة
        $connection->beginTransaction();

        try {
            // إدراج الرسالة
            $stmt = $connection->prepare("
                INSERT INTO messages (
                    trade_id, sender_id, message_type, content,
                    file_url, file_name, file_size, is_read, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, NOW(), NOW())
            ");

            $stmt->execute([
                $tradeId,
                $userId,
                $messageType,
                $content,
                $fileUrl,
                $fileName,
                $fileSize
            ]);

            $messageId = $connection->lastInsertId();

            // تحديث آخر نشاط في الصفقة
            $updateTradeStmt = $connection->prepare("
                UPDATE trades
                SET updated_at = NOW()
                WHERE id = ?
            ");
            $updateTradeStmt->execute([$tradeId]);

            // تسجيل النشاط
            try {
                $checkTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
                $checkTable->execute();

                if ($checkTable->rowCount() > 0) {
                    $checkColumns = $connection->prepare("SHOW COLUMNS FROM activity_logs LIKE 'entity_type'");
                    $checkColumns->execute();

                    if ($checkColumns->rowCount() > 0) {
                        $logStmt = $connection->prepare("
                            INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data)
                            VALUES (?, 'message_sent', 'message', ?, ?, ?, ?)
                        ");

                        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                        $messageData = json_encode([
                            'message_time' => date('Y-m-d H:i:s'),
                            'trade_id' => $tradeId,
                            'message_type' => $messageType,
                            'has_file' => !empty($fileUrl),
                            'file_name' => $fileName
                        ]);

                        $logStmt->execute([$userId, $messageId, $ipAddress, $userAgent, $messageData]);
                    }
                }
            } catch (Exception $logError) {
                // تجاهل أخطاء تسجيل النشاط
                error_log('Activity log error in messages: ' . $logError->getMessage());
            }

            // تأكيد المعاملة
            $connection->commit();

            // جلب الرسالة المُدرجة مع بيانات المرسل
            $stmt = $connection->prepare("
                SELECT
                    m.*,
                    u.username,
                    u.full_name,
                    u.is_verified,
                    u.profile_image
                FROM messages m
                JOIN users u ON m.sender_id = u.id
                WHERE m.id = ?
            ");

            $stmt->execute([$messageId]);
            $message = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($message) {
                $formattedMessage = formatMessage($message);

                sendSuccessResponse([
                    'message' => $formattedMessage
                ], 'تم إرسال الرسالة بنجاح');
            } else {
                sendErrorResponse('فشل في جلب الرسالة المرسلة');
            }

        } catch (Exception $e) {
            // التراجع عن المعاملة
            $connection->rollBack();
            throw $e;
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // تحديث حالة قراءة الرسائل
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            sendErrorResponse('بيانات غير صحيحة');
        }

        $tradeId = $input['trade_id'] ?? null;
        $messageIds = $input['message_ids'] ?? [];
        $markAsRead = $input['mark_as_read'] ?? true;

        if (!$tradeId) {
            sendErrorResponse('معرف الصفقة مطلوب');
        }

        // التحقق من صلاحية الوصول
        if (!canAccessTrade($connection, $tradeId, $userId, $isAdmin)) {
            sendErrorResponse('غير مصرح لك بتحديث رسائل هذه الصفقة', 403);
        }

        if (empty($messageIds)) {
            // تحديث جميع الرسائل غير المقروءة
            $updateStmt = $connection->prepare("
                UPDATE messages
                SET is_read = ?, updated_at = NOW()
                WHERE trade_id = ? AND sender_id != ?
            ");
            $updateStmt->execute([$markAsRead ? 1 : 0, $tradeId, $userId]);
            $affectedRows = $updateStmt->rowCount();
        } else {
            // تحديث رسائل محددة
            $placeholders = str_repeat('?,', count($messageIds) - 1) . '?';
            $updateStmt = $connection->prepare("
                UPDATE messages
                SET is_read = ?, updated_at = NOW()
                WHERE id IN ($placeholders) AND trade_id = ? AND sender_id != ?
            ");
            $params = array_merge([$markAsRead ? 1 : 0], $messageIds, [$tradeId, $userId]);
            $updateStmt->execute($params);
            $affectedRows = $updateStmt->rowCount();
        }

        sendSuccessResponse([
            'affectedMessages' => $affectedRows,
            'markAsRead' => $markAsRead
        ], 'تم تحديث حالة الرسائل بنجاح');

    } elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        // حذف رسالة (للمرسل فقط أو المدراء)
        $messageId = $_GET['message_id'] ?? null;

        if (!$messageId) {
            sendErrorResponse('معرف الرسالة مطلوب');
        }

        // التحقق من وجود الرسالة وصلاحية الحذف
        $stmt = $connection->prepare("
            SELECT m.*, t.seller_id, t.buyer_id
            FROM messages m
            JOIN trades t ON m.trade_id = t.id
            WHERE m.id = ?
        ");
        $stmt->execute([$messageId]);
        $message = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$message) {
            sendErrorResponse('الرسالة غير موجودة', 404);
        }

        // التحقق من الصلاحية
        $canDelete = $isAdmin || $message['sender_id'] == $userId;

        if (!$canDelete) {
            sendErrorResponse('ليس لديك صلاحية لحذف هذه الرسالة', 403);
        }

        // حذف الرسالة (soft delete)
        $deleteStmt = $connection->prepare("
            UPDATE messages
            SET content = '[تم حذف هذه الرسالة]',
                file_url = NULL,
                file_name = NULL,
                file_size = NULL,
                message_type = 'deleted',
                updated_at = NOW()
            WHERE id = ?
        ");
        $deleteStmt->execute([$messageId]);

        sendSuccessResponse([
            'messageId' => (int)$messageId,
            'deletedBy' => $isAdmin ? 'admin' : 'user'
        ], 'تم حذف الرسالة بنجاح');

    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'طريقة الطلب غير مدعومة'
        ]);
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);

    error_log('Database error in trades/messages.php: ' . $e->getMessage());
}
?>
