<?php
/**
 * API endpoint للمصادقة الثنائية (2FA)
 * Two-Factor Authentication API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";
require_once __DIR__ . "/../middleware/auth.php";

// التحقق من طريقة الطلب
validateRequestMethod(['GET', 'POST', 'PUT', 'DELETE']);

/**
 * إنشاء سر TOTP جديد
 */
function generateTOTPSecret($length = 32) {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    $secret = '';
    for ($i = 0; $i < $length; $i++) {
        $secret .= $chars[random_int(0, strlen($chars) - 1)];
    }
    return $secret;
}

/**
 * إنشاء رمز TOTP
 */
function generateTOTPCode($secret, $timeSlice = null) {
    if ($timeSlice === null) {
        $timeSlice = floor(time() / 30);
    }
    
    $secretkey = base32_decode($secret);
    $time = pack('N*', 0) . pack('N*', $timeSlice);
    $hm = hash_hmac('SHA1', $time, $secretkey, true);
    $offset = ord(substr($hm, -1)) & 0x0F;
    $hashpart = substr($hm, $offset, 4);
    $value = unpack('N', $hashpart);
    $value = $value[1];
    $value = $value & 0x7FFFFFFF;
    $modulo = pow(10, 6);
    return str_pad($value % $modulo, 6, '0', STR_PAD_LEFT);
}

/**
 * التحقق من رمز TOTP
 */
function verifyTOTPCode($secret, $code, $discrepancy = 1) {
    $currentTimeSlice = floor(time() / 30);
    
    for ($i = -$discrepancy; $i <= $discrepancy; $i++) {
        $calculatedCode = generateTOTPCode($secret, $currentTimeSlice + $i);
        if ($calculatedCode === $code) {
            return true;
        }
    }
    
    return false;
}

/**
 * فك تشفير Base32
 */
function base32_decode($input) {
    $alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    $output = '';
    $v = 0;
    $vbits = 0;
    
    for ($i = 0, $j = strlen($input); $i < $j; $i++) {
        $v <<= 5;
        $v += strpos($alphabet, $input[$i]);
        $vbits += 5;
        
        if ($vbits >= 8) {
            $output .= chr($v >> ($vbits - 8));
            $vbits -= 8;
        }
    }
    
    return $output;
}

/**
 * إنشاء رمز SMS
 */
function generateSMSCode() {
    return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
}

/**
 * إرسال رمز SMS (محاكاة)
 */
function sendSMSCode($phoneNumber, $code) {
    // TODO: تطبيق إرسال SMS الفعلي
    error_log("SMS Code for $phoneNumber: $code");
    return true;
}

/**
 * إنشاء QR Code URL لـ TOTP
 */
function generateQRCodeURL($secret, $username, $issuer = 'Ikaros P2P') {
    $label = urlencode($issuer . ':' . $username);
    $issuer = urlencode($issuer);
    return "otpauth://totp/$label?secret=$secret&issuer=$issuer";
}

try {
    // التحقق من المصادقة
    $auth = requireAuth();
    $userId = $auth->getCurrentUserId();
    
    if (!$userId) {
        sendErrorResponse('المصادقة مطلوبة', 401);
    }
    
    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // جلب حالة المصادقة الثنائية للمستخدم
        $stmt = $connection->prepare("
            SELECT two_factor_enabled, two_factor_type, phone, username, email
            FROM users 
            WHERE id = ?
        ");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            sendErrorResponse('المستخدم غير موجود', 404);
        }
        
        // جلب رموز الاسترداد
        $backupStmt = $connection->prepare("
            SELECT COUNT(*) as backup_codes_count
            FROM two_factor_backup_codes 
            WHERE user_id = ? AND used = 0
        ");
        $backupStmt->execute([$userId]);
        $backupCount = $backupStmt->fetch(PDO::FETCH_ASSOC)['backup_codes_count'];
        
        sendSuccessResponse([
            'twoFactorEnabled' => (bool)$user['two_factor_enabled'],
            'twoFactorType' => $user['two_factor_type'],
            'hasPhone' => !empty($user['phone']),
            'phoneNumber' => $user['phone'] ? substr($user['phone'], 0, 3) . '****' . substr($user['phone'], -2) : null,
            'backupCodesCount' => (int)$backupCount,
            'username' => $user['username'],
            'email' => $user['email']
        ], 'تم جلب حالة المصادقة الثنائية بنجاح');
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // تفعيل المصادقة الثنائية
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            sendErrorResponse('بيانات غير صحيحة');
        }
        
        $action = $input['action'] ?? '';
        $type = $input['type'] ?? 'totp'; // totp أو sms
        
        if ($action === 'setup') {
            // إعداد المصادقة الثنائية
            if ($type === 'totp') {
                // إنشاء سر TOTP جديد
                $secret = generateTOTPSecret();
                
                // حفظ السر مؤقتاً (غير مفعل بعد)
                $stmt = $connection->prepare("
                    UPDATE users 
                    SET two_factor_secret = ?, two_factor_type = 'totp', updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$secret, $userId]);
                
                // جلب اسم المستخدم
                $userStmt = $connection->prepare("SELECT username FROM users WHERE id = ?");
                $userStmt->execute([$userId]);
                $username = $userStmt->fetch(PDO::FETCH_ASSOC)['username'];
                
                $qrCodeURL = generateQRCodeURL($secret, $username);
                
                sendSuccessResponse([
                    'secret' => $secret,
                    'qrCodeURL' => $qrCodeURL,
                    'manualEntryKey' => $secret,
                    'type' => 'totp'
                ], 'تم إنشاء إعداد TOTP بنجاح');
                
            } elseif ($type === 'sms') {
                // التحقق من وجود رقم هاتف
                $stmt = $connection->prepare("SELECT phone FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (empty($user['phone'])) {
                    sendErrorResponse('يجب إضافة رقم هاتف أولاً');
                }
                
                // إنشاء وإرسال رمز SMS
                $smsCode = generateSMSCode();
                $expiryTime = date('Y-m-d H:i:s', time() + (5 * 60)); // 5 دقائق
                
                // حفظ الرمز
                $stmt = $connection->prepare("
                    INSERT INTO sms_verification_codes (user_id, code, expires_at, created_at)
                    VALUES (?, ?, ?, NOW())
                    ON DUPLICATE KEY UPDATE
                    code = VALUES(code),
                    expires_at = VALUES(expires_at),
                    created_at = NOW(),
                    verified = 0
                ");
                $stmt->execute([$userId, $smsCode, $expiryTime]);
                
                // إرسال SMS
                $smsSent = sendSMSCode($user['phone'], $smsCode);
                
                sendSuccessResponse([
                    'codeSent' => $smsSent,
                    'phoneNumber' => substr($user['phone'], 0, 3) . '****' . substr($user['phone'], -2),
                    'expiresIn' => 300, // 5 دقائق
                    'type' => 'sms'
                ], 'تم إرسال رمز التحقق بنجاح');
            }
            
        } elseif ($action === 'verify') {
            // التحقق من الرمز وتفعيل المصادقة الثنائية
            $code = $input['code'] ?? '';
            
            if (empty($code)) {
                sendErrorResponse('رمز التحقق مطلوب');
            }
            
            if ($type === 'totp') {
                // التحقق من رمز TOTP
                $stmt = $connection->prepare("
                    SELECT two_factor_secret 
                    FROM users 
                    WHERE id = ? AND two_factor_secret IS NOT NULL
                ");
                $stmt->execute([$userId]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$user || !verifyTOTPCode($user['two_factor_secret'], $code)) {
                    sendErrorResponse('رمز التحقق غير صحيح');
                }
                
                // تفعيل المصادقة الثنائية
                $stmt = $connection->prepare("
                    UPDATE users 
                    SET two_factor_enabled = 1, two_factor_type = 'totp', updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$userId]);
                
            } elseif ($type === 'sms') {
                // التحقق من رمز SMS
                $stmt = $connection->prepare("
                    SELECT code, expires_at 
                    FROM sms_verification_codes 
                    WHERE user_id = ? AND verified = 0
                    ORDER BY created_at DESC 
                    LIMIT 1
                ");
                $stmt->execute([$userId]);
                $smsData = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$smsData || $smsData['code'] !== $code || strtotime($smsData['expires_at']) < time()) {
                    sendErrorResponse('رمز التحقق غير صحيح أو منتهي الصلاحية');
                }
                
                // تفعيل المصادقة الثنائية
                $stmt = $connection->prepare("
                    UPDATE users 
                    SET two_factor_enabled = 1, two_factor_type = 'sms', updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$userId]);
                
                // تمييز الرمز كمستخدم
                $stmt = $connection->prepare("
                    UPDATE sms_verification_codes 
                    SET verified = 1 
                    WHERE user_id = ? AND code = ?
                ");
                $stmt->execute([$userId, $code]);
            }
            
            // إنشاء رموز الاسترداد
            $backupCodes = [];
            for ($i = 0; $i < 10; $i++) {
                $backupCodes[] = bin2hex(random_bytes(4));
            }
            
            // حفظ رموز الاسترداد
            $stmt = $connection->prepare("
                INSERT INTO two_factor_backup_codes (user_id, code, created_at)
                VALUES (?, ?, NOW())
            ");
            
            foreach ($backupCodes as $backupCode) {
                $stmt->execute([$userId, password_hash($backupCode, PASSWORD_DEFAULT)]);
            }
            
            sendSuccessResponse([
                'twoFactorEnabled' => true,
                'type' => $type,
                'backupCodes' => $backupCodes
            ], 'تم تفعيل المصادقة الثنائية بنجاح');
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        // إلغاء تفعيل المصادقة الثنائية
        $input = json_decode(file_get_contents('php://input'), true);
        $password = $input['password'] ?? '';
        
        if (empty($password)) {
            sendErrorResponse('كلمة المرور مطلوبة لإلغاء التفعيل');
        }
        
        // التحقق من كلمة المرور
        $stmt = $connection->prepare("SELECT password_hash FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user || !password_verify($password, $user['password_hash'])) {
            sendErrorResponse('كلمة المرور غير صحيحة');
        }
        
        // إلغاء تفعيل المصادقة الثنائية
        $stmt = $connection->prepare("
            UPDATE users 
            SET two_factor_enabled = 0, 
                two_factor_type = NULL, 
                two_factor_secret = NULL,
                updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$userId]);
        
        // حذف رموز الاسترداد
        $stmt = $connection->prepare("DELETE FROM two_factor_backup_codes WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        // حذف رموز SMS
        $stmt = $connection->prepare("DELETE FROM sms_verification_codes WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        sendSuccessResponse([
            'twoFactorEnabled' => false
        ], 'تم إلغاء تفعيل المصادقة الثنائية بنجاح');
    }
    
} catch (Exception $e) {
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    error_log('Database error in two-factor.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}
?>
