'use client';

import { useState, useEffect } from 'react';
import {
  MessageCircle,
  Clock,
  Shield,
  AlertTriangle,
  CheckCircle,
  User,
  Star,
  Loader2
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuth } from '@/contexts/AuthContext';

interface TradePageProps {
  tradeId?: string;
}

export default function TradePage({ tradeId }: TradePageProps = {}) {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // محاكاة تحميل البيانات
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="container-custom max-w-6xl">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
            <div className="h-64 bg-gray-300 dark:bg-gray-700 rounded mb-6"></div>
            <div className="h-96 bg-gray-300 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="container-custom max-w-6xl">
        {/* معلومات الصفقة */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                صفحة التداول #{tradeId || '001'}
              </h1>
              <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600 dark:text-gray-300">
                <span>شراء 1000 USDT</span>
                <span>•</span>
                <span>بسعر 3.75 SAR</span>
                <span>•</span>
                <span>تحويل بنكي</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 space-x-reverse mt-4 lg:mt-0">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Clock className="w-4 h-4 text-orange-500" />
                <span className="text-sm font-medium text-orange-600 dark:text-orange-400">
                  25:00
                </span>
              </div>
              <div className="px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 rounded-full text-sm font-medium">
                في انتظار الدفع
              </div>
            </div>
          </div>

          {/* تفاصيل الصفقة */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h3 className="font-medium text-gray-900 dark:text-white">تفاصيل العرض</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">الكمية:</span>
                  <span className="font-medium text-gray-900 dark:text-white">1,000 USDT</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">السعر:</span>
                  <span className="font-medium text-gray-900 dark:text-white">3.75 SAR</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">المجموع:</span>
                  <span className="font-bold text-lg text-gray-900 dark:text-white">3,750 SAR</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="font-medium text-gray-900 dark:text-white">معلومات البائع</h3>
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">أحمد محمد</p>
                  <div className="flex items-center space-x-1 space-x-reverse">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">4.8 (127)</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="font-medium text-gray-900 dark:text-white">حالة الصفقة</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">تم إنشاء الصفقة</span>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <Clock className="w-4 h-4 text-orange-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">في انتظار الدفع</span>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <div className="w-4 h-4 border-2 border-gray-300 dark:border-gray-600 rounded-full"></div>
                  <span className="text-sm text-gray-400">تأكيد الاستلام</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* منطقة الدردشة والإجراءات */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* الدردشة */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 space-x-reverse mb-4">
                <MessageCircle className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                <h3 className="font-medium text-gray-900 dark:text-white">الدردشة</h3>
              </div>
              
              <div className="h-96 border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4 overflow-y-auto">
                <div className="space-y-4">
                  <div className="flex justify-start">
                    <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 max-w-xs">
                      <p className="text-sm text-gray-900 dark:text-white">مرحباً، أنا مستعد لإتمام الصفقة</p>
                      <span className="text-xs text-gray-500 dark:text-gray-400">منذ 5 دقائق</span>
                    </div>
                  </div>
                  
                  <div className="flex justify-end">
                    <div className="bg-blue-600 rounded-lg p-3 max-w-xs">
                      <p className="text-sm text-white">ممتاز، سأقوم بالتحويل الآن</p>
                      <span className="text-xs text-blue-100">منذ 3 دقائق</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-2 space-x-reverse">
                <input
                  type="text"
                  placeholder="اكتب رسالتك..."
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  إرسال
                </button>
              </div>
            </div>
          </div>

          {/* الإجراءات */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="font-medium text-gray-900 dark:text-white mb-4">الإجراءات</h3>
              
              <div className="space-y-3">
                <button className="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium">
                  تأكيد إرسال الدفع
                </button>
                
                <button className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                  رفع إثبات الدفع
                </button>
                
                <button className="w-full px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium">
                  إلغاء الصفقة
                </button>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-2 space-x-reverse mb-3">
                <Shield className="w-5 h-5 text-green-600" />
                <h3 className="font-medium text-gray-900 dark:text-white">الأمان</h3>
              </div>
              
              <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <p>• الأموال محمية بالعقد الذكي</p>
                <p>• جميع المحادثات مراقبة</p>
                <p>• نظام حل النزاعات متاح</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
