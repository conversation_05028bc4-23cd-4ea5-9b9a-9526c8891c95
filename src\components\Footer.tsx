'use client';

import {
  Mail,
  Phone,
  MapPin,
  Shield,
  Clock,
  Users,
  MessageCircle,
  Globe,
  Heart
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

export default function Footer() {
  const { t } = useTranslation();
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 dark:bg-black text-white">
      {/* القسم الرئيسي */}
      <div className="container-custom py-8">
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* معلومات الشركة */}
          <div>
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">إ</span>
              </div>
              <div className="ltr:ml-3 rtl:mr-3">
                <h3 className="text-xl font-bold heading-arabic">{t('footer.company.title')}</h3>
              </div>
            </div>

            <p className="text-gray-300 mb-4 leading-relaxed body-arabic text-sm">
              منصة تداول العملات المشفرة الأكثر أماناً وموثوقية في المنطقة
            </p>

            <div className="flex space-x-3 rtl:space-x-reverse">
              <a href="#" className="w-8 h-8 bg-gray-800 dark:bg-gray-700 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors">
                <MessageCircle className="w-4 h-4" />
              </a>
              <a href="#" className="w-8 h-8 bg-gray-800 dark:bg-gray-700 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors">
                <Globe className="w-4 h-4" />
              </a>
              <a href="#" className="w-8 h-8 bg-gray-800 dark:bg-gray-700 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors">
                <Mail className="w-4 h-4" />
              </a>
            </div>
          </div>

          {/* روابط سريعة */}
          <div>
            <h4 className="text-lg font-bold mb-4 heading-arabic">روابط سريعة</h4>
            <ul className="space-y-2">
              <li>
                <a href="/" className="text-gray-300 hover:text-primary-400 transition-colors text-sm">
                  الرئيسية
                </a>
              </li>
              <li>
                <a href="/offers" className="text-gray-300 hover:text-primary-400 transition-colors text-sm">
                  العروض
                </a>
              </li>
              <li>
                <a href="/how-it-works" className="text-gray-300 hover:text-primary-400 transition-colors text-sm">
                  كيف يعمل
                </a>
              </li>
              <li>
                <a href="/fees" className="text-gray-300 hover:text-primary-400 transition-colors text-sm">
                  الرسوم
                </a>
              </li>
              <li>
                <a href="/support" className="text-gray-300 hover:text-primary-400 transition-colors text-sm">
                  الدعم
                </a>
              </li>
              <li>
                <a href="/contact" className="text-gray-300 hover:text-primary-400 transition-colors text-sm">
                  اتصل بنا
                </a>
              </li>
            </ul>
          </div>

          {/* معلومات الاتصال */}
          <div>
            <h4 className="text-lg font-bold mb-4 heading-arabic">تواصل معنا</h4>
            <div className="space-y-3">
              <div className="flex items-center">
                <Mail className="w-4 h-4 text-primary-400 ltr:mr-2 rtl:ml-2" />
                <span className="text-gray-300 text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center">
                <Phone className="w-4 h-4 text-primary-400 ltr:mr-2 rtl:ml-2" />
                <span className="text-gray-300 text-sm">+966 11 234 5678</span>
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 text-primary-400 ltr:mr-2 rtl:ml-2" />
                <span className="text-gray-300 text-sm">الرياض، المملكة العربية السعودية</span>
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 text-primary-400 ltr:mr-2 rtl:ml-2" />
                <span className="text-gray-300 text-sm">دعم 24/7</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* قسم الإحصائيات المبسط */}
      <div className="border-t border-gray-800 dark:border-gray-700">
        <div className="container-custom py-6">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="flex flex-col items-center">
              <Shield className="w-6 h-6 text-primary-400 mb-2" />
              <div className="text-lg font-bold">100%</div>
              <div className="text-gray-400 text-xs">أمان</div>
            </div>
            <div className="flex flex-col items-center">
              <Users className="w-6 h-6 text-primary-400 mb-2" />
              <div className="text-lg font-bold">8,932+</div>
              <div className="text-gray-400 text-xs">مستخدم نشط</div>
            </div>
            <div className="flex flex-col items-center">
              <Clock className="w-6 h-6 text-primary-400 mb-2" />
              <div className="text-lg font-bold">&lt; 5 دقائق</div>
              <div className="text-gray-400 text-xs">متوسط الوقت</div>
            </div>
          </div>
        </div>
      </div>

      {/* قسم الروابط القانونية والحقوق */}
      <div className="border-t border-gray-800 dark:border-gray-700">
        <div className="container-custom py-4">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex items-center text-gray-400 text-sm">
              <Heart className="w-4 h-4 text-red-500 mx-1" />
              <span>© {currentYear} إيكاروس. جميع الحقوق محفوظة</span>
            </div>
            <div className="flex flex-wrap justify-center gap-4 text-xs">
              <a href="/privacy" className="text-gray-400 hover:text-primary-400 transition-colors">
                سياسة الخصوصية
              </a>
              <a href="/terms" className="text-gray-400 hover:text-primary-400 transition-colors">
                الشروط والأحكام
              </a>
              <a href="/security" className="text-gray-400 hover:text-primary-400 transition-colors">
                الأمان
              </a>
              <a href="/disclaimer" className="text-gray-400 hover:text-primary-400 transition-colors">
                إخلاء المسؤولية
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* تحذير قانوني مبسط */}
      <div className="bg-gray-800 dark:bg-gray-900 border-t border-gray-700 dark:border-gray-600">
        <div className="container-custom py-3">
          <div className="text-center text-xs text-gray-500">
            <p>
              <strong>تحذير:</strong> تداول العملات المشفرة ينطوي على مخاطر عالية. تأكد من فهمك للمخاطر قبل التداول.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}