/**
 * Next.js API Route Proxy for Admin Login
 * بروكسي Next.js لتسجيل دخول المدراء
 */

import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    console.log('🔄 Proxying admin login request...');

    // إرسال الطلب إلى PHP API
    const response = await fetch('http://localhost/ikaros-p2p/api/admin/login.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(body),
    });

    console.log('📡 PHP API Response Status:', response.status);

    if (!response.ok) {
      throw new Error(`PHP API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('📊 PHP API Response received');

    return NextResponse.json(data, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error: any) {
    console.error('❌ Admin Login Proxy Error:', error);

    return NextResponse.json({
      success: false,
      error: 'فشل في الاتصال بخادم المصادقة',
      error_en: 'Failed to connect to authentication server',
      details: error.message,
      timestamp: new Date().toISOString()
    }, {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
