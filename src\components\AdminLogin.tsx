import React, { useState, useEffect } from 'react';
import { Shield, Wallet, Key, User, AlertCircle, CheckCircle, Lock, Eye, EyeOff, Loader2 } from 'lucide-react';
import { getAdminWalletService } from '@/services/AdminWalletService';
import { notificationService } from '@/services/notificationService';
import { useTranslation } from '@/hooks/useTranslation';


interface AdminLoginProps {
  onLoginSuccess: (adminData: any) => void;
}

export default function AdminLogin({ onLoginSuccess }: AdminLoginProps) {
  const { t } = useTranslation();

  // الحصول على خدمة المحفظة الآمنة
  const adminWalletService = getAdminWalletService();

  const [loginMethod, setLoginMethod] = useState<'wallet' | 'credentials'>('credentials');
  const [isLoading, setIsLoading] = useState(false);
  const [credentials, setCredentials] = useState({
    username: '',
    password: '',
    walletAddress: ''
  });
  const [connectedWallet, setConnectedWallet] = useState<string>('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  // التحقق من المحفظة المربوطة
  useEffect(() => {
    // التأكد من أننا في بيئة المتصفح
    if (typeof window === 'undefined') return;

    const checkWallet = async () => {
      try {
        if (adminWalletService.isConnected && adminWalletService.isConnected()) {
          const walletInfo = await adminWalletService.getCurrentWalletInfo();
          setConnectedWallet(walletInfo?.address || '');
        }
      } catch (error) {
        console.error('Error checking wallet:', error);
      }
    };
    checkWallet();
  }, [adminWalletService]);

  // دخول عبر المحفظة
  const handleWalletLogin = async () => {
    setIsLoading(true);
    setError('');
    try {
      let walletAddress = connectedWallet;

      // إذا لم تكن المحفظة مربوطة، اربطها
      if (!walletAddress) {
        const walletInfo = await adminWalletService.connectAdminWallet();
        walletAddress = walletInfo.address;
        setConnectedWallet(walletAddress);
      }

      // التحقق من صلاحيات الإدارة
      const isAdmin = await checkAdminPermissions(walletAddress);

      if (isAdmin) {
        const adminData = {
          walletAddress,
          loginMethod: 'wallet',
          timestamp: new Date().toISOString()
        };
        
        notificationService.success(t('adminLogin.messages.loginSuccess'));
        onLoginSuccess(adminData);
      } else {
        notificationService.error(t('adminLogin.messages.noAdminPermissions'));
      }
    } catch (error: any) {
      notificationService.error(error.message || t('adminLogin.messages.loginFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  // دخول عبر بيانات الاعتماد
  const handleCredentialsLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // التحقق من بيانات الاعتماد
      const response = await validateCredentials(credentials);

      if (response.success) {
        const adminData = {
          ...response.adminData,
          loginMethod: 'credentials',
          timestamp: new Date().toISOString()
        };

        notificationService.success('تم تسجيل الدخول بنجاح');
        onLoginSuccess(adminData);
      } else {
        setError(response.message || 'بيانات الاعتماد غير صحيحة');
        notificationService.error(response.message || 'بيانات الاعتماد غير صحيحة');
      }
    } catch (error: any) {
      const errorMessage = error.message || 'فشل في تسجيل الدخول';
      setError(errorMessage);
      notificationService.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // التحقق من صلاحيات الإدارة
  const checkAdminPermissions = async (walletAddress: string): Promise<boolean> => {
    try {
      // محاكاة استدعاء API للتحقق من صلاحيات الإدارة
      // في التطبيق الحقيقي، ستستدعي API endpoint
      const response = await fetch('/api/admin/check-permissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ walletAddress }),
      });

      if (response.ok) {
        const data = await response.json();
        return data.isAdmin;
      }
      
      // محاكاة للتطوير - تحقق من العناوين المعروفة
      const adminAddresses = [
        '******************************************', // عنوان المدير الرئيسي الجديد
        '******************************************', // عنوان المدير السابق
        process.env.NEXT_PUBLIC_ADMIN_WALLET_ADDRESS, // من متغيرات البيئة
      ].filter(Boolean); // إزالة القيم الفارغة
      
      return adminAddresses.includes(walletAddress.toLowerCase());
    } catch (error) {
      console.error(t('adminLogin.messages.permissionCheckError'), error);
      return false;
    }
  };

  // التحقق من بيانات الاعتماد
  const validateCredentials = async (creds: typeof credentials): Promise<{success: boolean, adminData?: any, message?: string}> => {
    try {
      // تنظيف البيانات قبل الإرسال
      const cleanCredentials = {
        username: creds.username.trim(),
        password: creds.password.trim(),
        walletAddress: creds.walletAddress.trim()
      };

      // التحقق من أن جميع الحقول مملوءة
      if (!cleanCredentials.username || !cleanCredentials.password || !cleanCredentials.walletAddress) {
        throw new Error('جميع الحقول مطلوبة: اسم المستخدم، كلمة المرور، وعنوان المحفظة');
      }

      // استدعاء API للتحقق من بيانات الاعتماد
      const response = await fetch('/api/admin/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cleanCredentials),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.sessionToken) {
          // حفظ رمز الجلسة بشكل آمن في sessionStorage
          sessionStorage.setItem('admin_token', data.sessionToken);
          sessionStorage.setItem('admin_session', JSON.stringify({
            adminData: data.adminData,
            sessionToken: data.sessionToken,
            expiresAt: data.expiresAt,
            permissions: data.permissions
          }));

          return {
            success: true,
            adminData: data.adminData,
            message: 'تم تسجيل الدخول بنجاح'
          };
        }
        return {
          success: false,
          message: data.message || 'فشل في تسجيل الدخول'
        };
      } else {
        const errorData = await response.json();
        console.error('❌ Login failed:', {
          status: response.status,
          statusText: response.statusText,
          errorData
        });
        return {
          success: false,
          message: errorData.error || errorData.message || 'فشل في تسجيل الدخول'
        };
      }
    } catch (error: any) {
      console.error('❌ Admin login error:', error);
      return {
        success: false,
        message: error.message || 'خطأ في الاتصال بالخادم'
      };
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-200 dark:border-gray-700">
          {/* Header */}
          <div className="bg-gradient-to-r from-red-600 to-red-700 dark:from-red-700 dark:to-red-800 px-6 py-8 text-center">
            <Shield className="w-16 h-16 text-white mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-white mb-2">دخول المدراء</h1>
            <p className="text-red-100 dark:text-red-200">منصة إيكاروس P2P - لوحة الإدارة</p>
          </div>

          {/* Login Method Selector */}
          <div className="p-6">
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1 mb-6">
              <button
                onClick={() => setLoginMethod('wallet')}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  loginMethod === 'wallet'
                    ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400'
                }`}
              >
                <Wallet className="w-4 h-4 inline ml-2" />
                المحفظة
              </button>
              <button
                onClick={() => setLoginMethod('credentials')}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  loginMethod === 'credentials'
                    ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow-sm'
                    : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400'
                }`}
              >
                <Key className="w-4 h-4 inline ml-2" />
                بيانات الدخول
              </button>
            </div>

            {/* Wallet Login */}
            {loginMethod === 'wallet' && (
              <div className="space-y-4">
                {/* رسالة الخطأ */}
                {error && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <div className="flex items-center">
                      <AlertCircle className="w-5 h-5 text-red-500 ml-2" />
                      <span className="text-red-700 dark:text-red-400 text-sm">{error}</span>
                    </div>
                  </div>
                )}

                {/* واجهة ربط المحفظة */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <Wallet className="w-5 h-5 text-blue-500 ml-2" />
                      <span className="font-medium text-gray-900 dark:text-white">
                        {t('adminLogin.walletConnection')}
                      </span>
                    </div>
                    {connectedWallet && (
                      <div className="flex items-center">
                        <CheckCircle className="w-4 h-4 text-green-500 ml-1" />
                        <span className="text-green-600 dark:text-green-400 text-sm">
                          {t('adminLogin.connected')}
                        </span>
                      </div>
                    )}
                  </div>

                  {connectedWallet ? (
                    <div className="space-y-2">
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {t('adminLogin.connectedAddress')}:
                      </div>
                      <div className="font-mono text-sm bg-white dark:bg-gray-800 p-2 rounded border">
                        {connectedWallet.slice(0, 6)}...{connectedWallet.slice(-4)}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center">
                      <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">
                        {t('adminLogin.connectWalletDesc')}
                      </p>
                    </div>
                  )}
                </div>

                {connectedWallet && (
                  <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                    <div className="flex items-center">
                      <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 ml-2" />
                      <span className="text-green-800 dark:text-green-300 font-medium">محفظة مدير مربوطة</span>
                    </div>
                    <p className="text-green-700 dark:text-green-400 text-sm mt-1 font-mono">
                      {connectedWallet.slice(0, 6)}...{connectedWallet.slice(-4)}
                    </p>
                  </div>
                )}

                <button
                  onClick={handleWalletLogin}
                  disabled={isLoading}
                  className="w-full bg-blue-600 dark:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 dark:hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
                      جاري التحقق...
                    </div>
                  ) : connectedWallet ? (
                    'دخول كمدير'
                  ) : (
                    'ربط المحفظة والدخول'
                  )}
                </button>
              </div>
            )}

            {/* Credentials Login */}
            {loginMethod === 'credentials' && (
              <form onSubmit={handleCredentialsLogin} className="space-y-4">
                {/* رسالة الخطأ */}
                {error && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <div className="flex items-center">
                      <AlertCircle className="w-5 h-5 text-red-500 ml-2" />
                      <span className="text-red-700 dark:text-red-400 text-sm">{error}</span>
                    </div>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    اسم المستخدم أو البريد الإلكتروني
                  </label>
                  <div className="relative">
                    <User className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-5 h-5" />
                    <input
                      type="text"
                      value={credentials.username}
                      onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
                      className="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="admin أو <EMAIL>"
                      required
                      disabled={isLoading}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    كلمة المرور
                  </label>
                  <div className="relative">
                    <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-5 h-5" />
                    <input
                      type={showPassword ? 'text' : 'password'}
                      value={credentials.password}
                      onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                      className="w-full pr-10 pl-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="••••••••"
                      required
                      disabled={isLoading}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                      disabled={isLoading}
                    >
                      {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    عنوان المحفظة <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <Wallet className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-5 h-5" />
                    <input
                      type="text"
                      value={credentials.walletAddress}
                      onChange={(e) => setCredentials(prev => ({ ...prev, walletAddress: e.target.value }))}
                      className="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-sm"
                      placeholder="0x..."
                      required
                      disabled={isLoading}
                    />
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    أدخل عنوان المحفظة المرتبط بحساب المدير. للاختبار استخدم: ******************************************
                  </p>
                  <button
                    type="button"
                    onClick={() => setCredentials({
                      username: 'admin',
                      password: 'admin123',
                      walletAddress: '******************************************'
                    })}
                    className="mt-2 text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline"
                    disabled={isLoading}
                  >
                    ملء البيانات الافتراضية للاختبار
                  </button>
                </div>

                <button
                  type="submit"
                  disabled={isLoading || !credentials.username.trim() || !credentials.password.trim() || !credentials.walletAddress.trim()}
                  className="w-full bg-red-600 dark:bg-red-700 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-700 dark:hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2 rtl:space-x-reverse"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin" />
                      <span>جاري تسجيل الدخول...</span>
                    </>
                  ) : (
                    <>
                      <Shield className="w-5 h-5" />
                      <span>تسجيل الدخول</span>
                    </>
                  )}
                </button>
              </form>
            )}

            {/* Security Warning */}
            <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <div className="flex items-start space-x-2 rtl:space-x-reverse">
                <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                <div className="text-sm text-yellow-700 dark:text-yellow-300">
                  <p className="font-medium mb-1">تحذير أمني:</p>
                  <ul className="space-y-1">
                    <li>• هذه منطقة آمنة للمدراء فقط</li>
                    <li>• جميع الأنشطة مراقبة ومسجلة</li>
                    <li>• استخدم محفظة آمنة ومعتمدة</li>
                    <li>• لا تشارك بيانات الدخول مع أحد</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
