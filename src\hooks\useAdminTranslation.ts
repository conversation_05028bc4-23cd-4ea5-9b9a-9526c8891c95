'use client';

import { useContext } from 'react';
import { LanguageContext } from '@/contexts/LanguageContext';

// Import admin translation files
import adminAr from '@/locales/admin/ar.json';
import adminEn from '@/locales/admin/en.json';

// Type definitions for admin translations
type AdminTranslations = typeof adminAr;

// Translation function type
type TranslationFunction = (key: string, params?: Record<string, string | number>) => string;

/**
 * Custom hook for admin dashboard translations
 * Provides access to admin-specific translations separate from main app translations
 */
export function useAdminTranslation() {
  const context = useContext(LanguageContext);
  const language = context?.language || 'ar'; // Default to Arabic if context is undefined

  // Get the appropriate translation object based on current language
  const translations: AdminTranslations = language === 'ar' ? adminAr : adminEn;

  /**
   * Translation function that supports nested keys and parameter interpolation
   * @param key - Translation key (e.g., 'dashboard.title' or 'users.actions.view')
   * @param params - Optional parameters for string interpolation
   * @returns Translated string
   */
  const t: TranslationFunction = (key: string, params?: Record<string, string | number>) => {
    try {
      // Split the key by dots to navigate nested objects
      const keys = key.split('.');
      let value: any = translations;

      // Navigate through the nested object
      for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
          value = value[k];
        } else {
          // Return the key if translation not found
          console.warn(`Admin translation not found for key: ${key}`);
          return key;
        }
      }

      // If value is not a string, return the key
      if (typeof value !== 'string') {
        console.warn(`Admin translation value is not a string for key: ${key}`);
        return key;
      }

      // Replace parameters in the string if provided
      if (params) {
        return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
          return params[paramKey]?.toString() || match;
        });
      }

      return value;
    } catch (error) {
      console.error(`Error getting admin translation for key: ${key}`, error);
      return key;
    }
  };

  /**
   * Get translation for common actions
   */
  const getActionText = (action: string) => {
    return t(`common.actions.${action}`);
  };

  /**
   * Get translation for status messages
   */
  const getStatusText = (status: string) => {
    return t(`common.status.${status}`);
  };

  /**
   * Get translation for error messages
   */
  const getErrorText = (error: string) => {
    return t(`errors.${error}`);
  };

  /**
   * Get translation for success messages
   */
  const getSuccessText = (success: string) => {
    return t(`success.${success}`);
  };

  /**
   * Get navigation item text
   */
  const getNavText = (navItem: string) => {
    return t(`navigation.${navItem}`);
  };

  /**
   * Get stats text
   */
  const getStatsText = (stat: string) => {
    return t(`stats.${stat}`);
  };

  /**
   * Get RTL/LTR direction
   */
  const isRTL = language === 'ar';
  const direction = isRTL ? 'rtl' : 'ltr';

  /**
   * Get direction-aware CSS classes
   */
  const getDirectionClasses = () => ({
    textAlign: isRTL ? 'text-right' : 'text-left',
    marginLeft: isRTL ? 'mr-' : 'ml-',
    marginRight: isRTL ? 'ml-' : 'mr-',
    paddingLeft: isRTL ? 'pr-' : 'pl-',
    paddingRight: isRTL ? 'pl-' : 'pr-',
    flexDirection: isRTL ? 'flex-row-reverse' : 'flex-row',
    spaceReverse: isRTL ? 'space-x-reverse' : '',
    dir: direction,
    rtl: isRTL
  });

  /**
   * Format numbers based on locale
   */
  const formatNumber = (num: number, options?: Intl.NumberFormatOptions) => {
    const locale = isRTL ? 'ar-SA' : 'en-US';
    return new Intl.NumberFormat(locale, options).format(num);
  };

  /**
   * Format currency based on locale
   */
  const formatCurrency = (amount: number, currency = 'USD') => {
    const locale = isRTL ? 'ar-SA' : 'en-US';
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  /**
   * Format date based on locale
   */
  const formatDate = (date: Date | string, options?: Intl.DateTimeFormatOptions) => {
    const locale = isRTL ? 'ar-SA' : 'en-US';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      ...options
    }).format(dateObj);
  };

  /**
   * Format relative time (e.g., "2 hours ago")
   */
  const formatRelativeTime = (date: Date | string | null | undefined) => {
    if (!date) {
      return isRTL ? 'غير محدد' : 'unknown';
    }

    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // التحقق من صحة التاريخ
    if (!dateObj || isNaN(dateObj.getTime())) {
      return isRTL ? 'تاريخ غير صحيح' : 'invalid date';
    }

    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return isRTL ? 'منذ لحظات' : 'just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return isRTL ? `منذ ${minutes} دقيقة` : `${minutes} minutes ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return isRTL ? `منذ ${hours} ساعة` : `${hours} hours ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return isRTL ? `منذ ${days} يوم` : `${days} days ago`;
    }
  };

  return {
    t,
    language,
    translations,
    isRTL,
    direction,
    getActionText,
    getStatusText,
    getErrorText,
    getSuccessText,
    getNavText,
    getStatsText,
    getDirectionClasses,
    formatNumber,
    formatCurrency,
    formatDate,
    formatRelativeTime,
  };
}

// Export type for use in components
export type { TranslationFunction, AdminTranslations };
