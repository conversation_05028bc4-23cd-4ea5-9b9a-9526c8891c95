<?php
/**
 * API لفحص حالة المزامنة مع العقد الذكي
 * API for checking smart contract sync status
 */

require_once '../includes/cors.php';
require_once '../config/database.php';

try {
    // التحقق من صلاحيات الإدارة
    checkAdminPermissions();
    
    $database = new Database();
    $db = $database->getConnection();
    
    // فحص آخر مزامنة
    $syncQuery = "SELECT 
                    last_sync_at,
                    sync_status,
                    last_block_number,
                    total_events_synced,
                    failed_sync_attempts,
                    TIMESTAMPDIFF(SECOND, last_sync_at, NOW()) as last_sync_delay
                  FROM contract_sync_status 
                  ORDER BY last_sync_at DESC 
                  LIMIT 1";
    
    $syncStmt = $db->prepare($syncQuery);
    $syncStmt->execute();
    $syncStatus = $syncStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$syncStatus) {
        // إنشاء سجل أولي إذا لم يكن موجود
        $insertQuery = "INSERT INTO contract_sync_status 
                        (last_sync_at, sync_status, last_block_number, total_events_synced, failed_sync_attempts) 
                        VALUES (NOW(), 'unknown', 0, 0, 0)";
        $db->prepare($insertQuery)->execute();
        
        $syncStatus = [
            'last_sync_at' => date('Y-m-d H:i:s'),
            'sync_status' => 'unknown',
            'last_block_number' => 0,
            'total_events_synced' => 0,
            'failed_sync_attempts' => 0,
            'last_sync_delay' => 0
        ];
    }
    
    // فحص الأحداث الأخيرة
    $eventsQuery = "SELECT 
                      COUNT(*) as total_events,
                      COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as recent_events,
                      MAX(created_at) as last_event_time
                    FROM contract_events 
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";
    
    $eventsStmt = $db->prepare($eventsQuery);
    $eventsStmt->execute();
    $eventsStats = $eventsStmt->fetch(PDO::FETCH_ASSOC);
    
    // فحص الصفقات غير المتزامنة
    $unsyncedQuery = "SELECT 
                        COUNT(*) as unsynced_trades,
                        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as recent_unsynced
                      FROM trades 
                      WHERE (blockchain_trade_id IS NULL OR blockchain_trade_id = '') 
                      AND status != 'cancelled'";
    
    $unsyncedStmt = $db->prepare($unsyncedQuery);
    $unsyncedStmt->execute();
    $unsyncedStats = $unsyncedStmt->fetch(PDO::FETCH_ASSOC);
    
    // تحديد حالة الصحة العامة
    $healthStatus = 'healthy';
    $healthIssues = [];
    
    if ($syncStatus['last_sync_delay'] > 300) { // أكثر من 5 دقائق
        $healthStatus = 'warning';
        $healthIssues[] = 'تأخر في المزامنة';
    }
    
    if ($syncStatus['last_sync_delay'] > 900) { // أكثر من 15 دقيقة
        $healthStatus = 'critical';
    }
    
    if ($syncStatus['failed_sync_attempts'] > 5) {
        $healthStatus = 'warning';
        $healthIssues[] = 'محاولات مزامنة فاشلة متكررة';
    }
    
    if ($unsyncedStats['unsynced_trades'] > 10) {
        $healthStatus = 'warning';
        $healthIssues[] = 'صفقات غير متزامنة';
    }
    
    // إحصائيات الأداء
    $performanceQuery = "SELECT 
                           AVG(TIMESTAMPDIFF(SECOND, created_at, synced_at)) as avg_sync_time,
                           COUNT(*) as synced_today
                         FROM trades 
                         WHERE synced_at IS NOT NULL 
                         AND DATE(synced_at) = CURDATE()";
    
    $performanceStmt = $db->prepare($performanceQuery);
    $performanceStmt->execute();
    $performance = $performanceStmt->fetch(PDO::FETCH_ASSOC);
    
    // تسجيل النشاط
    logActivity('sync_status_check', [
        'last_sync_delay' => $syncStatus['last_sync_delay'],
        'health_status' => $healthStatus,
        'unsynced_trades' => $unsyncedStats['unsynced_trades']
    ]);
    
    sendSuccess([
        'lastSyncDelay' => (int)$syncStatus['last_sync_delay'],
        'syncStatus' => $syncStatus['sync_status'],
        'lastSyncAt' => $syncStatus['last_sync_at'],
        'lastBlockNumber' => (int)$syncStatus['last_block_number'],
        'totalEventsSynced' => (int)$syncStatus['total_events_synced'],
        'failedSyncAttempts' => (int)$syncStatus['failed_sync_attempts'],
        'healthStatus' => $healthStatus,
        'healthIssues' => $healthIssues,
        'eventsStats' => [
            'totalEvents' => (int)$eventsStats['total_events'],
            'recentEvents' => (int)$eventsStats['recent_events'],
            'lastEventTime' => $eventsStats['last_event_time']
        ],
        'unsyncedStats' => [
            'unsyncedTrades' => (int)$unsyncedStats['unsynced_trades'],
            'recentUnsynced' => (int)$unsyncedStats['recent_unsynced']
        ],
        'performance' => [
            'avgSyncTime' => round($performance['avg_sync_time'] ?? 0, 1),
            'syncedToday' => (int)($performance['synced_today'] ?? 0)
        ],
        'timestamp' => date('c')
    ]);
    
} catch (Exception $e) {
    error_log("Error in sync-status.php: " . $e->getMessage());
    sendError('فشل في جلب حالة المزامنة', 500, [
        'error' => $e->getMessage(),
        'file' => __FILE__
    ]);
}
?>
