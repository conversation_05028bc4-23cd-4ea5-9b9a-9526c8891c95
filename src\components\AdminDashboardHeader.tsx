'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Menu,
  X,
  User,
  Bell,
  Settings,
  LogOut,
  Home,
  BarChart3,
  Users,
  Shield,
  AlertTriangle,
  Activity,
  Database,
  FileText,
  TrendingUp,
  ChevronDown,

  Crown,
  Zap,
  Eye
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuth } from '@/contexts/AuthContext';
import LanguageSelector from './LanguageSelector';
import ThemeToggle from './ThemeToggle';


interface AdminDashboardHeaderProps {
  activeTab?: string;
  onTabChange?: (tab: string) => void;
  className?: string;
  systemStatus?: {
    users: number;
    trades: number;
    disputes: number;
    alerts: number;
  };
}

export default function AdminDashboardHeader({
  activeTab = 'dashboard',
  onTabChange,
  className,
  systemStatus = { users: 0, trades: 0, disputes: 0, alerts: 0 }
}: AdminDashboardHeaderProps) {
  const { t } = useTranslation();
  const { user, logout } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isSystemMenuOpen, setIsSystemMenuOpen] = useState(false);


  const toggleMenu = useCallback(() => setIsMenuOpen(prev => !prev), []);
  const toggleProfile = useCallback(() => setIsProfileOpen(prev => !prev), []);


  // تحسين أداء عناصر التنقل الخاصة بلوحة الإدارة
  const adminTabs = useMemo(() => [
    {
      id: 'dashboard',
      name: t('admin.dashboard'),
      icon: BarChart3,
      description: 'نظرة عامة على النظام',
      priority: 1,
      badge: null,
      color: 'blue'
    },
    {
      id: 'users',
      name: t('admin.users'),
      icon: Users,
      description: 'إدارة المستخدمين',
      priority: 1,
      badge: systemStatus.users > 0 ? systemStatus.users.toString() : null,
      color: 'green'
    },
    {
      id: 'trades',
      name: t('admin.trades'),
      icon: TrendingUp,
      description: 'إدارة الصفقات',
      priority: 1,
      badge: systemStatus.trades > 0 ? systemStatus.trades.toString() : null,
      color: 'purple'
    },
    {
      id: 'disputes',
      name: t('admin.disputes'),
      icon: AlertTriangle,
      description: 'حل النزاعات',
      priority: 1,
      badge: systemStatus.disputes > 0 ? systemStatus.disputes.toString() : null,
      color: 'red'
    },
    {
      id: 'contracts',
      name: t('admin.contracts'),
      icon: Shield,
      description: 'إدارة العقود الذكية',
      priority: 2,
      badge: null,
      color: 'indigo'
    },
    {
      id: 'analytics',
      name: t('admin.analytics'),
      icon: Activity,
      description: 'التحليلات والتقارير',
      priority: 2,
      badge: null,
      color: 'yellow'
    },
    {
      id: 'database',
      name: t('admin.database'),
      icon: Database,
      description: 'إدارة قاعدة البيانات',
      priority: 3,
      badge: null,
      color: 'gray'
    },
    {
      id: 'settings',
      name: t('admin.settings'),
      icon: Settings,
      description: 'إعدادات النظام',
      priority: 3,
      badge: null,
      color: 'slate'
    }
  ], [t, systemStatus]);





  // تحسين معالجة النقر خارج القوائم
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (isMenuOpen && !target.closest('.mobile-menu-container')) {
        setIsMenuOpen(false);
      }
      if (isProfileOpen && !target.closest('.profile-menu-container')) {
        setIsProfileOpen(false);
      }
      if (isSystemMenuOpen && !target.closest('.system-menu-container')) {
        setIsSystemMenuOpen(false);
      }
    };

    if (isMenuOpen || isProfileOpen || isSystemMenuOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [isMenuOpen, isProfileOpen, isSystemMenuOpen]);

  // معالجة تغيير التبويب
  const handleTabChange = useCallback((tabId: string) => {
    if (onTabChange) {
      onTabChange(tabId);
    }
    setIsMenuOpen(false);
  }, [onTabChange]);

  return (
    <header className={`header-enhanced backdrop-enhanced border-enhanced shadow-enhanced sticky top-0 z-50 transition-enhanced bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border-red-200 dark:border-red-800 ${className}`}>
      <div className="container mx-auto px-3 sm:px-4 lg:px-6 xl:px-8">
        <div className="layout-enhanced h-14 sm:h-16 lg:h-20">
          {/* الشعار والعودة للرئيسية */}
          <div className="flex items-center space-x-2 sm:space-x-3 rtl:space-x-reverse min-w-0 flex-shrink-0">
            <a href="/" className="flex items-center group" aria-label={t('navigation.home')}>
              <div className="logo-enhanced relative w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-gradient-to-br from-red-500 via-red-600 to-orange-600 rounded-lg sm:rounded-xl flex items-center justify-center shadow-enhanced transform group-hover:scale-105 transition-enhanced group-hover:shadow-xl overflow-hidden">
                <Crown className="text-white w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 relative z-10" />
                <div className="absolute inset-0 bg-gradient-to-br from-red-400 to-orange-500 rounded-lg sm:rounded-xl opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-in-out"></div>
              </div>
              <div className="ltr:ml-1.5 ltr:sm:ml-2 ltr:lg:ml-3 rtl:mr-1.5 rtl:sm:mr-2 rtl:lg:mr-3 min-w-0">
                <h1 className="text-enhanced arabic-text-enhanced text-sm sm:text-lg lg:text-xl font-bold heading-arabic group-hover:from-red-700 group-hover:to-orange-700 transition-enhanced text-red-600 dark:text-red-400 truncate">
                  {t('admin.title')}
                </h1>
                <p className="text-xs text-red-500 dark:text-red-400 body-arabic hidden sm:block group-hover:text-red-600 dark:group-hover:text-red-300 transition-colors duration-300 truncate">
                  {user?.fullName || user?.username || t('admin.administrator')}
                </p>
              </div>
            </a>
          </div>

          {/* التنقل السريع للإدارة - سطح المكتب */}
          <nav className="hidden md:flex items-center space-x-2 lg:space-x-4 xl:space-x-6 rtl:space-x-reverse flex-1 justify-center max-w-2xl mx-4" role="navigation" aria-label={t('navigation.menu')}>
            {adminTabs.slice(0, 4).map((tab, index) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`nav-item-enhanced relative flex items-center space-x-1.5 lg:space-x-2 rtl:space-x-reverse font-medium transition-enhanced text-xs lg:text-sm xl:text-base px-2 lg:px-3 py-1.5 lg:py-2 rounded-lg focus-enhanced group ${
                  activeTab === tab.id
                    ? `text-${tab.color}-600 dark:text-${tab.color}-400 bg-${tab.color}-50 dark:bg-${tab.color}-900/20 shadow-sm`
                    : 'text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20'
                }`}
                style={{ animationDelay: `${index * 100}ms` }}
                aria-label={tab.description}
              >
                <tab.icon className="w-3.5 h-3.5 lg:w-4 lg:h-4 opacity-70 group-hover:opacity-100 group-hover:scale-110 transition-all duration-300 flex-shrink-0" />
                <span className="group-hover:translate-x-0.5 rtl:group-hover:-translate-x-0.5 transition-transform duration-300 hidden lg:inline truncate">
                  {tab.name}
                </span>

                {/* شارة العدد */}
                {tab.badge && (
                  <span className={`absolute -top-1 -right-1 min-w-[16px] h-[16px] lg:min-w-[18px] lg:h-[18px] bg-${tab.color === 'red' ? 'red' : 'blue'}-500 text-white text-xs font-bold rounded-full flex items-center justify-center animate-pulse`}>
                    {tab.badge}
                  </span>
                )}

                {/* مؤشر الأولوية */}
                {tab.priority === 1 && (
                  <Zap className="w-2.5 h-2.5 lg:w-3 lg:h-3 text-yellow-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 absolute -top-1 -left-1" />
                )}
              </button>
            ))}
          </nav>

          {/* أزرار الإدارة والأدوات */}
          <div className="flex items-center space-x-1.5 sm:space-x-2 lg:space-x-3 rtl:space-x-reverse flex-shrink-0">
            {/* اختيار اللغة - مرئي على جميع الأحجام */}
            <div className="flex items-center bg-white/50 dark:bg-gray-800/50 rounded-lg border border-red-200 dark:border-red-700 backdrop-blur-sm">
              <LanguageSelector />
            </div>

            {/* مفتاح تبديل الوضع المظلم */}
            <ThemeToggle />




            {/* قائمة المدير */}
            <div className="relative profile-menu-container">
              <button
                onClick={toggleProfile}
                className="button-enhanced flex items-center space-x-1.5 sm:space-x-2 rtl:space-x-reverse p-1.5 sm:p-2 rounded-xl hover:bg-gradient-to-r hover:from-red-50 hover:to-orange-50 dark:hover:from-red-900/20 dark:hover:to-orange-900/20 transition-enhanced group border border-transparent hover:border-red-200 dark:hover:border-red-800 focus-enhanced"
                aria-label={t('header.profile')}
                aria-expanded={isProfileOpen}
              >
                <div className="relative w-7 h-7 sm:w-8 sm:h-8 bg-gradient-to-br from-red-100 to-orange-200 dark:from-red-900/30 dark:to-orange-800/30 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-sm">
                  <Crown className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-red-600 dark:text-red-400" />
                  <div className="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 sm:w-3 sm:h-3 bg-red-500 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center">
                    <Shield className="w-1.5 h-1.5 sm:w-2 sm:h-2 text-white" />
                  </div>
                </div>
                <span className="hidden lg:block text-sm font-medium text-gray-700 dark:text-gray-300 max-w-20 xl:max-w-24 truncate group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors duration-300">
                  {user?.fullName || user?.username || t('admin.administrator')}
                </span>
                <ChevronDown className={`w-3.5 h-3.5 sm:w-4 sm:h-4 text-gray-500 dark:text-gray-400 transition-all duration-300 group-hover:text-red-500 hidden sm:block ${isProfileOpen ? 'rotate-180' : ''}`} />
              </button>

              {/* قائمة منسدلة للمدير */}
              {isProfileOpen && (
                <>
                  <div className="fixed inset-0 z-40" onClick={() => setIsProfileOpen(false)} />
                  <div className="dropdown-enhanced absolute ltr:right-0 rtl:left-0 mt-3 w-64 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-red-200 dark:border-red-700 py-3 z-50 animate-in slide-in-from-top-2 duration-200">
                    {/* معلومات المدير */}
                    <div className="px-4 py-3 border-b border-red-200 dark:border-red-600">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <div className="relative w-10 h-10 bg-gradient-to-br from-red-100 to-orange-200 dark:from-red-900/30 dark:to-orange-800/30 rounded-full flex items-center justify-center">
                          <Crown className="w-5 h-5 text-red-600 dark:text-red-400" />
                          <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center">
                            <Shield className="w-2.5 h-2.5 text-white" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                            {user?.fullName || user?.username || t('admin.administrator')}
                          </p>
                          <p className="text-xs text-red-600 dark:text-red-400 truncate">{t('admin.superAdmin')}</p>
                        </div>
                      </div>
                    </div>

                    {/* روابط إدارية سريعة */}
                    <div className="px-2 py-2">
                      <a href="/" className="flex items-center px-3 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-red-50 hover:to-orange-50 dark:hover:from-red-900/20 dark:hover:to-orange-900/20 rounded-xl transition-all duration-200 group">
                        <Home className="w-4 h-4 text-red-600 dark:text-red-400 ltr:mr-3 rtl:ml-3" />
                        <span>{t('navigation.home')}</span>
                      </a>
                      <button className="flex items-center w-full px-3 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-red-50 hover:to-orange-50 dark:hover:from-red-900/20 dark:hover:to-orange-900/20 rounded-xl transition-all duration-200 group">
                        <Eye className="w-4 h-4 text-red-600 dark:text-red-400 ltr:mr-3 rtl:ml-3" />
                        <span>{t('admin.systemMonitor')}</span>
                      </button>
                    </div>

                    <hr className="my-2 border-red-200 dark:border-red-600" />

                    {/* زر تسجيل الخروج */}
                    <div className="px-2 pb-2">
                      <button
                        onClick={logout}
                        className="flex items-center w-full px-3 py-3 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-xl transition-all duration-200 group focus-enhanced"
                      >
                        <LogOut className="w-4 h-4 text-red-600 dark:text-red-400 ltr:mr-3 rtl:ml-3" />
                        <span className="font-medium">{t('navigation.logout')}</span>
                      </button>
                    </div>
                  </div>
                </>
              )}
            </div>

            {/* زر القائمة للجوال */}
            <button
              onClick={toggleMenu}
              className="md:hidden button-enhanced p-2 sm:p-2.5 text-gray-600 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 transition-enhanced rounded-xl hover:bg-gradient-to-r hover:from-red-50 hover:to-orange-50 dark:hover:from-red-900/20 dark:hover:to-orange-900/20 mobile-menu-container group border border-transparent hover:border-red-200 dark:hover:border-red-800 focus-enhanced"
              aria-label={isMenuOpen ? t('common.close') : t('navigation.menu')}
              aria-expanded={isMenuOpen}
            >
              <div className="relative w-5 h-5 sm:w-6 sm:h-6">
                <Menu className={`absolute inset-0 w-5 h-5 sm:w-6 sm:h-6 transition-all duration-300 ${isMenuOpen ? 'opacity-0 rotate-180 scale-75' : 'opacity-100 rotate-0 scale-100'}`} />
                <X className={`absolute inset-0 w-5 h-5 sm:w-6 sm:h-6 transition-all duration-300 ${isMenuOpen ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 -rotate-180 scale-75'}`} />
              </div>
            </button>
          </div>
        </div>

        {/* القائمة المنسدلة للجوال */}
        {isMenuOpen && (
          <div className="mobile-menu-enhanced md:hidden border-t border-red-200 dark:border-red-700 bg-white/98 dark:bg-gray-900/98 backdrop-blur-md mobile-menu-container animate-in slide-in-from-top-2 duration-300 shadow-lg">
            <div className="px-3 sm:px-4 py-4 sm:py-6 max-h-[calc(100vh-4rem)] overflow-y-auto">


              {/* تبويبات لوحة الإدارة */}
              <nav className="space-y-1.5 sm:space-y-2 mb-4 sm:mb-6" role="navigation">
                {adminTabs.map((tab, index) => (
                  <button
                    key={tab.id}
                    onClick={() => handleTabChange(tab.id)}
                    className={`flex items-center space-x-2.5 sm:space-x-3 rtl:space-x-reverse w-full px-3 sm:px-4 py-3 sm:py-4 font-medium transition-enhanced body-arabic rounded-lg sm:rounded-xl border border-transparent group focus-enhanced ${
                      activeTab === tab.id
                        ? 'text-red-600 dark:text-red-400 bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border-red-200 dark:border-red-800'
                        : 'text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 hover:bg-gradient-to-r hover:from-red-50 hover:to-orange-50 dark:hover:from-red-900/20 dark:hover:to-orange-900/20 hover:border-red-200 dark:hover:border-red-800'
                    }`}
                    style={{ animationDelay: `${index * 50}ms` }}
                    aria-label={tab.description}
                  >
                    <div className="relative w-8 h-8 sm:w-10 sm:h-10 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300 group-hover:bg-red-100 dark:group-hover:bg-red-900/30 flex-shrink-0">
                      <tab.icon className="w-4 h-4 sm:w-5 sm:h-5 opacity-70 group-hover:opacity-100 transition-opacity duration-300" />
                      {tab.badge && (
                        <div className="absolute -top-1 -right-1 min-w-[16px] h-[16px] sm:min-w-[18px] sm:h-[18px] bg-red-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs font-bold">{tab.badge}</span>
                        </div>
                      )}
                    </div>
                    <span className="flex-1 text-left rtl:text-right text-sm sm:text-base truncate">{tab.name}</span>
                    <ChevronDown className="w-3.5 h-3.5 sm:w-4 sm:h-4 opacity-50 ltr:rotate-[-90deg] rtl:rotate-90 group-hover:opacity-100 transition-all duration-300 flex-shrink-0" />
                  </button>
                ))}
              </nav>






            </div>
          </div>
        )}
      </div>
    </header>
  );
}