'use client';

import { useState, useEffect } from 'react';
import { TrendingUp, Users, DollarSign, Star } from 'lucide-react';

interface StatItem {
  id: string;
  label: string;
  value: number;
  icon: React.ReactNode;
  color: string;
  prefix?: string;
  suffix?: string;
}

interface AnimatedStatsProps {
  stats: StatItem[];
  duration?: number;
}

export default function AnimatedStats({ stats, duration = 2000 }: AnimatedStatsProps) {
  const [animatedValues, setAnimatedValues] = useState<{[key: string]: number}>({});
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // تهيئة القيم بصفر
    const initialValues: {[key: string]: number} = {};
    stats.forEach(stat => {
      initialValues[stat.id] = 0;
    });
    setAnimatedValues(initialValues);

    // بدء الأنيميشن بعد تحميل المكون
    const timer = setTimeout(() => {
      setIsVisible(true);
      animateValues();
    }, 500);

    return () => clearTimeout(timer);
  }, [stats]);

  const animateValues = () => {
    const startTime = Date.now();
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // استخدام easing function للحصول على حركة أكثر سلاسة
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      
      const newValues: {[key: string]: number} = {};
      stats.forEach(stat => {
        newValues[stat.id] = Math.floor(stat.value * easeOutQuart);
      });
      
      setAnimatedValues(newValues);
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    
    requestAnimationFrame(animate);
  };

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <div
          key={stat.id}
          className={`stats-card group text-center bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:scale-105 ${
            isVisible ? 'animate-fade-in' : 'opacity-0'
          }`}
          style={{ animationDelay: `${index * 0.1}s` }}
        >
          <div className={`w-10 h-10 mx-auto mb-3 rounded-full flex items-center justify-center ${stat.color} group-hover:scale-110 transition-transform duration-300`}>
            {stat.icon}
          </div>
          <div className="stats-number text-white mb-2">
            {stat.prefix}
            {animatedValues[stat.id]?.toLocaleString() || 0}
            {stat.suffix}
          </div>
          <div className="stats-label text-blue-100 font-medium body-arabic">
            {stat.label}
          </div>
        </div>
      ))}
    </div>
  );
}

// مكون للإحصائيات الافتراضية
export function DefaultPlatformStats() {
  const stats: StatItem[] = [
    {
      id: 'trades',
      label: 'إجمالي الصفقات',
      value: 12547,
      icon: <TrendingUp className="w-6 h-6 text-white" />,
      color: 'bg-yellow-500/20'
    },
    {
      id: 'users',
      label: 'المستخدمون النشطون',
      value: 8932,
      icon: <Users className="w-6 h-6 text-white" />,
      color: 'bg-green-500/20'
    },
    {
      id: 'volume',
      label: 'حجم التداول',
      value: 2847,
      icon: <DollarSign className="w-6 h-6 text-white" />,
      color: 'bg-orange-500/20',
      prefix: '$',
      suffix: 'K'
    },
    {
      id: 'rating',
      label: 'متوسط التقييم',
      value: 4.8,
      icon: <Star className="w-6 h-6 text-white" />,
      color: 'bg-purple-500/20'
    }
  ];

  return <AnimatedStats stats={stats} />;
}
