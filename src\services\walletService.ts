// خدمة ربط المحفظة المحسنة مع معالجة أخطاء محسنة
import { ethers } from 'ethers';
import { BLOCKCHAIN_CONFIG } from '@/constants';

export interface WalletInfo {
  address: string;
  balance: string;
  network: string;
  isConnected: boolean;
  chainId?: string;
}

export interface WalletError {
  code: string | number;
  message: string;
  type: 'USER_REJECTED' | 'WALLET_NOT_FOUND' | 'NETWORK_ERROR' | 'CONNECTION_ERROR' | 'UNKNOWN';
}

export interface WalletService {
  connectWallet(walletType: 'metamask' | 'walletconnect' | 'trustwallet'): Promise<WalletInfo>;
  disconnectWallet(): void;
  switchNetwork(chainId: string): Promise<boolean>;
  getWalletInfo(): Promise<WalletInfo | null>;
  getConnectionStatus(): Promise<{ isConnected: boolean; address: string | null; network: string }>;
  isWalletConnected(): boolean;
  getCurrentAccount(): string | null;
  getBalance(): Promise<string>;
  onAccountChange(callback: (accounts: string[]) => void): void;
  onChainChange(callback: (chainId: string) => void): void;

}

class EthersWalletService implements WalletService {
  private provider: ethers.BrowserProvider | null = null;
  private signer: ethers.Signer | null = null;
  private currentAccount: string | null = null;
  private currentChainId: string | null = null;

  private connectionRetryCount: number = 0;
  private maxRetries: number = 3;

  constructor() {
    this.initializeEventListeners();
    // لا نقوم بالاتصال التلقائي لتجنب أخطاء المحفظة
    // this.initializeConnection();
  }



  // معالجة الأخطاء المحسنة
  private handleWalletError(error: any): WalletError {
    let walletError: WalletError;

    if (error.code === 4001 || error.code === -32002) {
      walletError = {
        code: error.code,
        message: 'تم رفض طلب الاتصال من قبل المستخدم',
        type: 'USER_REJECTED'
      };
    } else if (error.code === 4902) {
      walletError = {
        code: error.code,
        message: 'الشبكة غير مضافة إلى المحفظة',
        type: 'NETWORK_ERROR'
      };
    } else if (error.message?.includes('MetaMask') || error.message?.includes('ethereum')) {
      walletError = {
        code: error.code || 'WALLET_NOT_FOUND',
        message: 'المحفظة غير مثبتة أو غير متاحة',
        type: 'WALLET_NOT_FOUND'
      };
    } else if (error.message?.includes('network') || error.message?.includes('connection')) {
      walletError = {
        code: error.code || 'NETWORK_ERROR',
        message: 'خطأ في الاتصال بالشبكة',
        type: 'NETWORK_ERROR'
      };
    } else {
      walletError = {
        code: error.code || 'UNKNOWN',
        message: error.message || 'خطأ غير معروف في المحفظة',
        type: 'UNKNOWN'
      };
    }

    return walletError;
  }

  // تهيئة الاتصال عند بدء التشغيل
  private async initializeConnection() {
    if (typeof window !== 'undefined') {
      // انتظار قليل للتأكد من تحميل ethereum provider
      setTimeout(async () => {
        await this.checkSavedConnection();
      }, 100);
    }
  }

  private initializeEventListeners() {
    if (typeof window !== 'undefined' && window.ethereum) {
      // مراقبة تغيير الحسابات
      window.ethereum.on('accountsChanged', (accounts: string[]) => {
        if (accounts.length === 0) {
          this.disconnectWallet();
        } else {
          this.currentAccount = accounts[0];
          this.setupProvider();
        }
      });

      // مراقبة تغيير الشبكة
      window.ethereum.on('chainChanged', (chainId: string) => {
        this.currentChainId = chainId;
        window.location.reload(); // إعادة تحميل الصفحة عند تغيير الشبكة
      });

      // مراقبة قطع الاتصال
      window.ethereum.on('disconnect', () => {
        this.disconnectWallet();
      });
    }
  }

  private async setupProvider() {
    if (typeof window !== 'undefined' && window.ethereum) {
      try {
        this.provider = new ethers.BrowserProvider(window.ethereum);
        this.signer = await this.provider.getSigner();
        this.currentAccount = await this.signer.getAddress();

        const network = await this.provider.getNetwork();
        this.currentChainId = `0x${network.chainId.toString(16)}`;

        // التحقق من محفظة الأدمن
        const isAdmin = this.isAdminWallet(this.currentAccount);
        if (isAdmin) {
          console.log('✅ تم اكتشاف محفظة الأدمن:', this.currentAccount);
          localStorage.setItem('admin_wallet_connected', 'true');
        } else {
          console.log('ℹ️ محفظة عادية متصلة:', this.currentAccount);
          localStorage.removeItem('admin_wallet_connected');
        }

        console.log('✅ تم إعداد المزود بنجاح:', {
          account: this.currentAccount,
          chainId: this.currentChainId,
          isAdmin
        });
      } catch (error) {
        console.error('❌ خطأ في إعداد المزود:', error);
        throw error;
      }
    }
  }

  async connectWallet(walletType: 'metamask' | 'walletconnect' | 'trustwallet'): Promise<WalletInfo> {
    try {
      if (walletType === 'metamask') {
        return await this.connectMetaMask();
      } else if (walletType === 'walletconnect') {
        throw new Error('WalletConnect غير مدعوم حالياً.');
      } else if (walletType === 'trustwallet') {
        return await this.connectTrustWallet();
      } else {
        throw new Error('نوع المحفظة غير مدعوم');
      }
    } catch (error: any) {
      const walletError = this.handleWalletError(error);
      console.error('خطأ في ربط المحفظة:', walletError);

      // اقتراح الوضع التوضيحي في حالة الفشل
      if (walletError.type === 'WALLET_NOT_FOUND' || walletError.type === 'USER_REJECTED') {
        console.log('💡 نصيحة: يمكنك تفعيل الوضع التوضيحي للمتابعة بدون محفظة');
      }

      throw error;
    }
  }

  private async connectMetaMask(): Promise<WalletInfo> {
    // التحقق من وجود ethereum provider
    if (typeof window === 'undefined' || !window.ethereum) {
      const error = this.handleWalletError({
        message: 'MetaMask غير مثبت. يرجى تثبيت MetaMask أولاً.'
      });
      throw new Error(error.message);
    }

    // التحقق من MetaMask تحديداً
    if (!window.ethereum.isMetaMask) {
      const error = this.handleWalletError({
        message: 'MetaMask غير مثبت. يرجى تثبيت MetaMask أولاً.'
      });
      throw new Error(error.message);
    }

    try {
      this.connectionRetryCount = 0;

      // طلب الإذن للوصول للحسابات مع إعادة المحاولة
      const accounts = await this.requestAccountsWithRetry();

      if (accounts.length === 0) {
        throw new Error('لم يتم اختيار أي حساب');
      }

      await this.setupProvider();

      // التحقق من الشبكة والتبديل إذا لزم الأمر
      const chainId = await window.ethereum.request({ method: 'eth_chainId' });

      // التبديل إلى BSC Testnet إذا لم تكن متصلة
      if (chainId !== BLOCKCHAIN_CONFIG.BSC_TESTNET.chainId) {
        const switched = await this.switchNetwork(BLOCKCHAIN_CONFIG.BSC_TESTNET.chainId);
        if (!switched) {
          console.warn('فشل في تبديل الشبكة، سيتم المتابعة بالشبكة الحالية');
        }
      }

      const walletInfo = await this.getWalletInfo();
      if (!walletInfo) {
        throw new Error('فشل في جلب معلومات المحفظة');
      }

      // حفظ حالة الاتصال
      localStorage.setItem('wallet_connected', 'true');
      localStorage.setItem('wallet_type', 'metamask');
      localStorage.setItem('wallet_address', walletInfo.address);
      localStorage.removeItem('wallet_demo_mode'); // إزالة الوضع التوضيحي

      return walletInfo;
    } catch (error: any) {
      const walletError = this.handleWalletError(error);
      console.error('خطأ في ربط MetaMask:', walletError);

      // في حالة رفض المستخدم، اقترح الوضع التوضيحي
      if (walletError.type === 'USER_REJECTED') {
        console.log('يمكن تفعيل الوضع التوضيحي للمتابعة بدون محفظة');
      }

      throw new Error(walletError.message);
    }
  }

  // دالة مساعدة لطلب الحسابات مع إعادة المحاولة
  private async requestAccountsWithRetry(): Promise<string[]> {
    while (this.connectionRetryCount < this.maxRetries) {
      try {
        const accounts = await window.ethereum.request({
          method: 'eth_requestAccounts'
        });
        return accounts;
      } catch (error: any) {
        this.connectionRetryCount++;

        if (error.code === -32002) {
          // طلب معلق، انتظر قليلاً وأعد المحاولة
          console.log(`طلب الاتصال معلق، إعادة المحاولة ${this.connectionRetryCount}/${this.maxRetries}`);
          await new Promise(resolve => setTimeout(resolve, 2000));
          continue;
        } else {
          // خطأ آخر، لا تعيد المحاولة
          throw error;
        }
      }
    }

    throw new Error('فشل في الاتصال بعد عدة محاولات. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى.');
  }

  private async connectTrustWallet(): Promise<WalletInfo> {
    // Trust Wallet يستخدم نفس واجهة MetaMask
    if (!window.ethereum) {
      throw new Error('Trust Wallet غير مثبت أو غير متاح');
    }

    return await this.connectMetaMask();
  }

  async switchNetwork(chainId: string): Promise<boolean> {
    if (!window.ethereum) {
      throw new Error('المحفظة غير متصلة');
    }

    try {
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId }]
      });
      return true;
    } catch (error: any) {
      // إذا لم تكن الشبكة مضافة، أضفها
      if (error.code === 4902) {
        try {
          const networkConfig = chainId === BLOCKCHAIN_CONFIG.BSC_TESTNET.chainId 
            ? BLOCKCHAIN_CONFIG.BSC_TESTNET 
            : BLOCKCHAIN_CONFIG.BSC_MAINNET;

          await window.ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [networkConfig]
          });
          return true;
        } catch (addError) {
          console.error('خطأ في إضافة الشبكة:', addError);
          return false;
        }
      }
      console.error('خطأ في تبديل الشبكة:', error);
      return false;
    }
  }

  async getWalletInfo(): Promise<WalletInfo | null> {
    if (!this.provider || !this.signer) {
      return null;
    }

    try {
      const address = await this.signer.getAddress();
      const balance = await this.provider.getBalance(address);
      const network = await this.provider.getNetwork();

      const networkName = network.chainId === BigInt(97) ? 'BSC Testnet' :
                         network.chainId === BigInt(56) ? 'BSC Mainnet' :
                         `Chain ${network.chainId}`;

      return {
        address,
        balance: ethers.formatEther(balance),
        network: networkName,
        isConnected: true,
        chainId: `0x${network.chainId.toString(16)}`
      };
    } catch (error) {
      console.error('خطأ في جلب معلومات المحفظة:', error);
      return null;
    }
  }

  disconnectWallet(): void {
    this.provider = null;
    this.signer = null;
    this.currentAccount = null;
    this.currentChainId = null;

    this.connectionRetryCount = 0;

    // مسح البيانات المحفوظة
    localStorage.removeItem('wallet_connected');
    localStorage.removeItem('wallet_type');
    localStorage.removeItem('wallet_address');
    localStorage.removeItem('wallet_demo_mode');
  }

  isWalletConnected(): boolean {
    // التحقق من الحالة الحالية
    const isCurrentlyConnected = this.provider !== null && this.signer !== null && this.currentAccount !== null;

    // التحقق من الحالة المحفوظة
    const isSavedConnected = localStorage.getItem('wallet_connected') === 'true';

    // إذا كانت محفوظة كمتصلة لكن غير متصلة حالياً، حاول استعادة الاتصال
    if (isSavedConnected && !isCurrentlyConnected && typeof window !== 'undefined' && window.ethereum) {
      this.checkSavedConnection().catch(console.error);
    }

    return isCurrentlyConnected || isSavedConnected;
  }

  getCurrentAccount(): string | null {
    // إذا كان الحساب الحالي متاح، أرجعه
    if (this.currentAccount) {
      return this.currentAccount;
    }

    // إذا لم يكن متاح، تحقق من الحالة المحفوظة
    const savedAddress = localStorage.getItem('wallet_address');
    if (savedAddress && localStorage.getItem('wallet_connected') === 'true') {
      return savedAddress;
    }

    return null;
  }

  onAccountChange(callback: (accounts: string[]) => void): void {
    if (window.ethereum) {
      window.ethereum.on('accountsChanged', callback);
    }
  }

  onChainChange(callback: (chainId: string) => void): void {
    if (window.ethereum) {
      window.ethereum.on('chainChanged', callback);
    }
  }

  // دالة للتحقق من الاتصال المحفوظ عند تحميل الصفحة
  async checkSavedConnection(): Promise<WalletInfo | null> {
    const isConnected = localStorage.getItem('wallet_connected');
    const walletType = localStorage.getItem('wallet_type');
    const savedAddress = localStorage.getItem('wallet_address');

    if (isConnected === 'true' && walletType && savedAddress && window.ethereum) {
      try {
        // التحقق من الحسابات المتاحة
        const accounts = await window.ethereum.request({ method: 'eth_accounts' });

        if (accounts.length > 0) {
          // التحقق من أن العنوان المحفوظ موجود في الحسابات المتاحة
          const normalizedSavedAddress = savedAddress.toLowerCase();
          const accountExists = accounts.some((account: string) =>
            account.toLowerCase() === normalizedSavedAddress
          );

          if (accountExists) {
            await this.setupProvider();
            const walletInfo = await this.getWalletInfo();

            if (walletInfo) {
              console.log('تم استعادة اتصال المحفظة بنجاح:', walletInfo.address);
              return walletInfo;
            }
          } else {
            console.warn('العنوان المحفوظ غير موجود في الحسابات المتاحة');
            this.disconnectWallet();
          }
        } else {
          console.warn('لا توجد حسابات متاحة في المحفظة');
          this.disconnectWallet();
        }
      } catch (error) {
        console.error('خطأ في استعادة الاتصال:', error);
        this.disconnectWallet();
      }
    }

    return null;
  }

  // دالة لتنسيق العنوان (اختصار)
  formatAddress(address: string): string {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  }

  // دالة لنسخ العنوان
  async copyAddress(): Promise<boolean> {
    const address = this.getCurrentAccount();
    if (!address) return false;

    try {
      await navigator.clipboard.writeText(address);
      return true;
    } catch (error) {
      console.error('خطأ في نسخ العنوان:', error);
      return false;
    }
  }

  // دالة للتحقق الفوري من حالة الاتصال
  async checkConnectionStatus(): Promise<boolean> {
    if (!window.ethereum) {
      return false;
    }

    try {
      const accounts = await window.ethereum.request({ method: 'eth_accounts' });
      const isConnected = accounts.length > 0;

      if (isConnected && !this.currentAccount) {
        // إذا كانت المحفظة متصلة لكن الخدمة لا تعرف ذلك، أعد الإعداد
        await this.setupProvider();
      } else if (!isConnected && this.currentAccount) {
        // إذا كانت الخدمة تعتقد أنها متصلة لكن المحفظة غير متصلة، افصل
        this.disconnectWallet();
      }

      return isConnected;
    } catch (error) {
      console.error('خطأ في التحقق من حالة الاتصال:', error);
      return false;
    }
  }

  // دالة للتحقق من محفظة الأدمن
  isAdminWallet(address?: string): boolean {
    const walletAddress = address || this.currentAccount;
    if (!walletAddress) return false;

    const adminWalletAddress = process.env.ADMIN_WALLET_ADDRESS;
    if (!adminWalletAddress) return false;

    return walletAddress.toLowerCase() === adminWalletAddress.toLowerCase();
  }

  // دالة للحصول على معلومات محفظة الأدمن
  getAdminWalletInfo(): { address: string; isConnected: boolean; isAdmin: boolean } {
    const adminAddress = process.env.ADMIN_WALLET_ADDRESS || '******************************************';
    const isConnected = !!this.currentAccount;
    const isAdmin = this.isAdminWallet();

    return {
      address: adminAddress,
      isConnected,
      isAdmin
    };
  }

  // دالة للتحقق من رصيد USDT
  async getUSDTBalance(usdtContractAddress: string): Promise<string> {
    if (!this.provider || !this.currentAccount) {
      throw new Error('المحفظة غير متصلة');
    }

    try {
      const usdtAbi = [
        "function balanceOf(address owner) view returns (uint256)",
        "function decimals() view returns (uint8)"
      ];

      const usdtContract = new ethers.Contract(usdtContractAddress, usdtAbi, this.provider);
      const balance = await usdtContract.balanceOf(this.currentAccount);
      const decimals = await usdtContract.decimals();

      return ethers.formatUnits(balance, decimals);
    } catch (error) {
      console.error('خطأ في جلب رصيد USDT:', error);
      throw error;
    }
  }

  async getConnectionStatus(): Promise<{ isConnected: boolean; address: string | null; network: string }> {
    try {
      const isConnected = this.isWalletConnected();
      const address = this.getCurrentAccount();
      let network = '';

      if (isConnected && this.provider) {
        const networkInfo = await this.provider.getNetwork();
        network = networkInfo.chainId === BigInt(97) ? 'BSC Testnet' :
                 networkInfo.chainId === BigInt(56) ? 'BSC Mainnet' :
                 `Chain ${networkInfo.chainId}`;
      }

      return {
        isConnected,
        address,
        network
      };
    } catch (error) {
      console.error('Error getting connection status:', error);
      return {
        isConnected: false,
        address: null,
        network: ''
      };
    }
  }

  async getBalance(): Promise<string> {
    try {
      if (!this.provider || !this.signer) {
        return '0';
      }

      const address = await this.signer.getAddress();
      const balance = await this.provider.getBalance(address);
      return ethers.formatEther(balance);
    } catch (error) {
      console.error('Error getting balance:', error);
      return '0';
    }
  }
}

// إنشاء مثيل واحد من الخدمة
export const walletService = new EthersWalletService();

// إضافة أنواع TypeScript للـ window.ethereum
declare global {
  interface Window {
    ethereum?: any;
  }
}
