'use client';

import { ReactNode, useEffect, useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { createTranslationFunction } from '@/utils/translation';

interface AdminGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * مكون حماية الصفحات الإدارية - يتحقق من صلاحيات الإدارة من localStorage
 * Admin Guard Component - Checks admin permissions from localStorage
 */
export default function AdminGuard({ children, fallback }: AdminGuardProps) {
  const { language } = useLanguage();
  const t = createTranslationFunction(language);
  const [isChecking, setIsChecking] = useState(true);
  const [hasAdminAccess, setHasAdminAccess] = useState(false);

  useEffect(() => {
    const checkAdminAccess = async () => {
      try {
        // التحقق من وجود جلسة إدارة محفوظة
        const savedAdminData = localStorage.getItem('adminSession');
        const adminToken = localStorage.getItem('adminToken');

        if (!savedAdminData && !adminToken) {
          setHasAdminAccess(false);
          setIsChecking(false);
          return;
        }

        // إذا كان هناك رمز جلسة، التحقق منه أولاً
        if (adminToken) {
          const isValidToken = await validateAdminToken(adminToken);
          if (isValidToken) {
            setHasAdminAccess(true);
            setIsChecking(false);
            return;
          } else {
            // رمز غير صحيح، إزالته
            localStorage.removeItem('adminToken');
          }
        }

        if (savedAdminData) {
          const parsedData = JSON.parse(savedAdminData);

          // التحقق من انتهاء صلاحية الجلسة
          const sessionExpiry = new Date(parsedData.expiresAt || parsedData.timestamp);
          if (!parsedData.expiresAt) {
            // للجلسات القديمة، إضافة 8 ساعات للوقت المحفوظ
            sessionExpiry.setHours(sessionExpiry.getHours() + 8);
          }

          if (new Date() > sessionExpiry) {
            // انتهت صلاحية الجلسة
            localStorage.removeItem('adminSession');
            localStorage.removeItem('adminToken');
            setHasAdminAccess(false);
            setIsChecking(false);
            return;
          }

          // التحقق من وجود بيانات المدير
          const hasAdminData = parsedData.adminRole || parsedData.isAdmin;
          const hasWalletAddress = parsedData.walletAddress;
          const hasCredentials = parsedData.username;

          if (!hasAdminData && !hasWalletAddress && !hasCredentials) {
            localStorage.removeItem('adminSession');
            localStorage.removeItem('adminToken');
            setHasAdminAccess(false);
            setIsChecking(false);
            return;
          }

          // إذا كان هناك عنوان محفظة، التحقق من صلاحيات الإدارة
          if (hasWalletAddress && !hasAdminData) {
            const isValidAdmin = await checkWalletAdminPermissions(parsedData.walletAddress);
            if (!isValidAdmin) {
              localStorage.removeItem('adminSession');
              localStorage.removeItem('adminToken');
              setHasAdminAccess(false);
              setIsChecking(false);
              return;
            }
          }

          // الجلسة صالحة
          setHasAdminAccess(true);
          setIsChecking(false);
        } else {
          setHasAdminAccess(false);
          setIsChecking(false);
        }

      } catch (error) {
        console.error('Error checking admin access:', error);
        localStorage.removeItem('adminSession');
        localStorage.removeItem('adminToken');
        setHasAdminAccess(false);
        setIsChecking(false);
      }
    };

    checkAdminAccess();
  }, []);

  // التحقق من صحة رمز الجلسة الإدارية
  const validateAdminToken = async (token: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/admin/validate-session.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ token }),
      });

      if (response.ok) {
        const data = await response.json();
        return data.success && data.valid;
      }

      return false;
    } catch (error) {
      console.error('Error validating admin token:', error);
      return false;
    }
  };

  // التحقق من صلاحيات المحفظة
  const checkWalletAdminPermissions = async (walletAddress: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/admin/check-permissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ walletAddress }),
      });

      if (response.ok) {
        const data = await response.json();
        return data.success && data.isAdmin;
      }

      // محاكاة للتطوير - تحقق من العناوين المعروفة
      const adminAddresses = [
        '******************************************',
        '******************************************',
      ];

      return adminAddresses.includes(walletAddress.toLowerCase());
    } catch (error) {
      console.error('Error checking wallet admin permissions:', error);

      // في حالة فشل الاتصال، استخدم قائمة العناوين المعروفة
      const adminAddresses = [
        '******************************************',
        '******************************************',
      ];

      return adminAddresses.includes(walletAddress.toLowerCase());
    }
  };

  // عرض مؤشر التحميل أثناء التحقق
  if (isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">
            {t('admin.checking')}
          </p>
        </div>
      </div>
    );
  }

  // عرض رسالة عدم وجود صلاحيات إدارية
  if (!hasAdminAccess) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="mb-6">
            <svg className="mx-auto h-16 w-16 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 0h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            {t('admin.accessDenied')}
          </h2>
          
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {t('admin.adminAccessRequired')}
          </p>
          
          <div className="space-y-3">
            <button
              onClick={() => window.location.href = '/admin'}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              {t('admin.goToLogin')}
            </button>
            
            <button
              onClick={() => window.location.href = '/'}
              className="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              {t('common.goToHome')}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // عرض المحتوى الإداري إذا كان لدى المستخدم الصلاحيات المطلوبة
  return <>{children}</>;
}
