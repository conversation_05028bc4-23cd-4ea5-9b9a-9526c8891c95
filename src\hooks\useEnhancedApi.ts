import { useState, useEffect, useCallback, useRef } from 'react';
import { notificationService } from '@/services/notificationService';

interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastFetch: Date | null;
}

interface UseEnhancedApiOptions {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
  cacheTime?: number; // مدة التخزين المؤقت بالميلي ثانية
  retryCount?: number; // عدد المحاولات عند الفشل
  retryDelay?: number; // تأخير بين المحاولات
  showNotifications?: boolean; // عرض الإشعارات
}

interface PaginationState {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
  pagination?: PaginationState;
}

export function useEnhancedApi<T>(
  apiFunction: (...args: any[]) => Promise<ApiResponse<T> | T>,
  options: UseEnhancedApiOptions = {}
) {
  const { 
    immediate = true, 
    onSuccess, 
    onError,
    cacheTime = 5 * 60 * 1000, // 5 دقائق افتراضياً
    retryCount = 3,
    retryDelay = 1000,
    showNotifications = true
  } = options;
  
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
    lastFetch: null
  });

  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20
  });

  const retryCountRef = useRef(0);
  const abortControllerRef = useRef<AbortController | null>(null);
  const apiFunctionRef = useRef(apiFunction);

  // تحديث المرجع عند تغيير الدالة
  useEffect(() => {
    apiFunctionRef.current = apiFunction;
  }, [apiFunction]);

  const execute = useCallback(async (...args: any[]) => {
    // إلغاء الطلب السابق إذا كان موجوداً
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // إنشاء controller جديد
    abortControllerRef.current = new AbortController();

    setState(prev => ({ ...prev, loading: true, error: null }));

    const attemptRequest = async (attempt: number): Promise<T> => {
      try {
        const result = await apiFunction(...args);

        // التحقق من نوع النتيجة
        let processedResult: ApiResponse<T>;
        if (result && typeof result === 'object' && 'success' in result) {
          processedResult = result as ApiResponse<T>;
        } else {
          // إذا كانت النتيجة مباشرة، نلفها في ApiResponse
          processedResult = { success: true, data: result as T };
        }

        if (!processedResult.success) {
          throw new Error(processedResult.error || 'API request failed');
        }

        setState({
          data: processedResult.data,
          loading: false,
          error: null,
          lastFetch: new Date()
        });

        if (processedResult.pagination) {
          setPagination(processedResult.pagination);
        }

        onSuccess?.(processedResult.data);
        retryCountRef.current = 0;

        if (showNotifications && attempt > 1) {
          notificationService.success('تم تحميل البيانات بنجاح');
        }

        return processedResult.data;
      } catch (error: any) {
        if (error.name === 'AbortError') {
          throw error; // لا نعيد المحاولة للطلبات الملغاة
        }

        if (attempt < retryCount) {
          if (showNotifications) {
            notificationService.warning(`محاولة ${attempt + 1} من ${retryCount}...`);
          }

          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
          return attemptRequest(attempt + 1);
        }

        throw error;
      }
    };

    try {
      return await attemptRequest(1);
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
        setState({ data: null, loading: false, error: errorMessage, lastFetch: null });
        onError?.(errorMessage);

        if (showNotifications) {
          notificationService.error(`فشل في تحميل البيانات: ${errorMessage}`);
        }
      }
      throw error;
    }
  }, [apiFunction, onSuccess, onError, retryCount, retryDelay, showNotifications]);

  // تحقق من صحة البيانات المخزنة مؤقتاً
  const isDataFresh = useCallback(() => {
    if (!state.lastFetch || !state.data) return false;
    return Date.now() - state.lastFetch.getTime() < cacheTime;
  }, [state.lastFetch, state.data, cacheTime]);

  // تنفيذ الطلب مع التحقق من التخزين المؤقت
  const executeWithCache = useCallback(async (...args: any[]) => {
    if (isDataFresh()) {
      return state.data;
    }
    return execute(...args);
  }, [execute, isDataFresh, state.data]);

  // استخدام useRef لتجنب إعادة إنشاء الدالة
  const executeWithCacheRef = useRef(executeWithCache);
  useEffect(() => {
    executeWithCacheRef.current = executeWithCache;
  }, [executeWithCache]);

  useEffect(() => {
    if (immediate) {
      executeWithCacheRef.current();
    }

    // تنظيف عند إلغاء المكون
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [immediate]); // إزالة executeWithCache من dependencies

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null, lastFetch: null });
    setPagination({ currentPage: 1, totalPages: 1, totalItems: 0, itemsPerPage: 20 });
    retryCountRef.current = 0;
  }, []);

  const refresh = useCallback(async (...args: any[]) => {
    setState(prev => ({ ...prev, lastFetch: null }));
    return execute(...args);
  }, [execute]);

  return {
    ...state,
    pagination,
    execute,
    executeWithCache,
    refresh,
    reset,
    isDataFresh: isDataFresh()
  };
}

// Hook مخصص للتعامل مع قوائم البيانات مع التصفح
export function useApiList<T>(
  apiFunction: (page: number, limit: number, ...args: any[]) => Promise<ApiResponse<T[]>>,
  options: UseEnhancedApiOptions & { itemsPerPage?: number } = {}
) {
  const { itemsPerPage = 20, ...apiOptions } = options;
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState<Record<string, any>>({});

  const apiState = useEnhancedApi(
    useCallback(
      () => apiFunction(currentPage, itemsPerPage, ...Object.values(filters)),
      [apiFunction, currentPage, itemsPerPage, filters]
    ),
    {
      ...apiOptions,
      immediate: false,
      onSuccess: (data) => {
        // تسجيل نوع البيانات المستلمة للتصحيح
        console.log('API data received:', { type: typeof data, isArray: Array.isArray(data), data });
        apiOptions.onSuccess?.(data);
      }
    }
  );

  const loadPage = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const updateFilters = useCallback((newFilters: Record<string, any>) => {
    setFilters(newFilters);
    setCurrentPage(1); // العودة للصفحة الأولى عند تغيير الفلاتر
  }, []);

  const nextPage = useCallback(() => {
    if (currentPage < apiState.pagination.totalPages) {
      setCurrentPage(prev => prev + 1);
    }
  }, [currentPage, apiState.pagination.totalPages]);

  const prevPage = useCallback(() => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1);
    }
  }, [currentPage]);

  // استخدام useRef لتجنب إعادة إنشاء الدالة
  const executeRef = useRef(apiState.execute);
  useEffect(() => {
    executeRef.current = apiState.execute;
  }, [apiState.execute]);

  useEffect(() => {
    executeRef.current();
  }, [currentPage, filters]); // إزالة apiState.execute من dependencies

  return {
    ...apiState,
    currentPage,
    filters,
    loadPage,
    updateFilters,
    nextPage,
    prevPage,
    hasNextPage: currentPage < apiState.pagination.totalPages,
    hasPrevPage: currentPage > 1
  };
}

// Hook للتعامل مع العمليات (إنشاء، تحديث، حذف)
export function useApiMutation<T, P = any>(
  mutationFunction: (params: P) => Promise<ApiResponse<T>>,
  options: UseEnhancedApiOptions = {}
) {
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
    lastFetch: null
  });

  const mutate = useCallback(async (params: P) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const result = await mutationFunction(params);
      
      if (!result.success) {
        throw new Error(result.error || 'Mutation failed');
      }

      setState({
        data: result.data,
        loading: false,
        error: null,
        lastFetch: new Date()
      });

      options.onSuccess?.(result.data);
      
      if (options.showNotifications !== false) {
        notificationService.success('تم تنفيذ العملية بنجاح');
      }

      return result.data;
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
      setState({ data: null, loading: false, error: errorMessage, lastFetch: null });
      options.onError?.(errorMessage);
      
      if (options.showNotifications !== false) {
        notificationService.error(`فشل في تنفيذ العملية: ${errorMessage}`);
      }
      
      throw error;
    }
  }, [mutationFunction, options]);

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null, lastFetch: null });
  }, []);

  return {
    ...state,
    mutate,
    reset
  };
}
