<?php
/**
 * API endpoint لمزامنة العروض مع العقد الذكي
 * Offers Smart Contract Synchronization API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // ربط عرض موجود بالعقد الذكي
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        // التحقق من البيانات المطلوبة
        $requiredFields = ['offer_id', 'blockchain_trade_id', 'transaction_hash'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field])) {
                throw new Exception("الحقل $field مطلوب");
            }
        }
        
        $offerId = $input['offer_id'];
        $blockchainTradeId = $input['blockchain_trade_id'];
        $transactionHash = $input['transaction_hash'];
        $contractStatus = $input['contract_status'] ?? 'created';
        
        // التحقق من وجود العرض
        $stmt = $connection->prepare("
            SELECT id, user_id, amount, blockchain_trade_id 
            FROM offers 
            WHERE id = ?
        ");
        $stmt->execute([$offerId]);
        $offer = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$offer) {
            throw new Exception('العرض غير موجود');
        }
        
        // التحقق من عدم ربط العرض مسبقاً
        if ($offer['blockchain_trade_id']) {
            throw new Exception('العرض مربوط بالعقد الذكي مسبقاً');
        }
        
        // تحديث العرض بمعلومات العقد الذكي
        $stmt = $connection->prepare("
            UPDATE offers 
            SET blockchain_trade_id = ?, 
                transaction_hash = ?, 
                contract_status = ?,
                sync_status = 'synced',
                contract_created_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        
        $stmt->execute([$blockchainTradeId, $transactionHash, $contractStatus, $offerId]);
        
        // تسجيل النشاط
        $stmt = $connection->prepare("
            INSERT INTO activity_logs (
                action, entity_type, entity_id, user_id, data, created_at
            ) VALUES (?, 'offer', ?, ?, ?, CURRENT_TIMESTAMP)
        ");
        
        $activityData = json_encode([
            'blockchain_trade_id' => $blockchainTradeId,
            'transaction_hash' => $transactionHash,
            'contract_status' => $contractStatus
        ]);
        
        $stmt->execute([
            'offer_synced_with_contract',
            $offerId,
            $offer['user_id'],
            $activityData
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم ربط العرض بالعقد الذكي بنجاح',
            'data' => [
                'offer_id' => $offerId,
                'blockchain_trade_id' => $blockchainTradeId,
                'transaction_hash' => $transactionHash,
                'contract_status' => $contractStatus
            ]
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // تحديث حالة العرض من العقد الذكي
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        // يمكن التحديث بمعرف العرض أو معرف العقد الذكي
        if (isset($input['offer_id'])) {
            $whereClause = 'id = ?';
            $whereParam = $input['offer_id'];
        } elseif (isset($input['blockchain_trade_id'])) {
            $whereClause = 'blockchain_trade_id = ?';
            $whereParam = $input['blockchain_trade_id'];
        } else {
            throw new Exception('معرف العرض أو معرف العقد الذكي مطلوب');
        }
        
        // جلب العرض
        $stmt = $connection->prepare("
            SELECT id, user_id, blockchain_trade_id, contract_status 
            FROM offers 
            WHERE $whereClause
        ");
        $stmt->execute([$whereParam]);
        $offer = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$offer) {
            throw new Exception('العرض غير موجود');
        }
        
        // تحضير حقول التحديث
        $updateFields = [];
        $params = [];
        
        if (isset($input['contract_status'])) {
            $updateFields[] = 'contract_status = ?';
            $params[] = $input['contract_status'];
        }
        
        if (isset($input['available_amount'])) {
            $updateFields[] = 'available_amount = ?';
            $params[] = $input['available_amount'];
        }
        
        if (isset($input['is_active'])) {
            $updateFields[] = 'is_active = ?';
            $params[] = $input['is_active'] ? 1 : 0;
        }
        
        if (isset($input['last_activity_hash'])) {
            $updateFields[] = 'last_activity_hash = ?';
            $params[] = $input['last_activity_hash'];
        }
        
        if (empty($updateFields)) {
            throw new Exception('لا توجد حقول للتحديث');
        }
        
        // إضافة حقول المزامنة
        $updateFields[] = 'sync_status = ?';
        $updateFields[] = 'last_sync_at = CURRENT_TIMESTAMP';
        $updateFields[] = 'updated_at = CURRENT_TIMESTAMP';
        $params[] = 'synced';
        
        $params[] = $offer['id'];
        
        // تنفيذ التحديث
        $stmt = $connection->prepare("
            UPDATE offers 
            SET " . implode(', ', $updateFields) . "
            WHERE id = ?
        ");
        
        $stmt->execute($params);
        
        // تسجيل النشاط
        $stmt = $connection->prepare("
            INSERT INTO activity_logs (
                action, entity_type, entity_id, user_id, data, created_at
            ) VALUES (?, 'offer', ?, ?, ?, CURRENT_TIMESTAMP)
        ");
        
        $activityData = json_encode([
            'old_status' => $offer['contract_status'],
            'new_status' => $input['contract_status'] ?? $offer['contract_status'],
            'updated_fields' => array_keys($input),
            'source' => 'smart_contract'
        ]);
        
        $stmt->execute([
            'offer_status_updated_from_contract',
            $offer['id'],
            $offer['user_id'],
            $activityData
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث حالة العرض من العقد الذكي',
            'data' => [
                'offer_id' => $offer['id'],
                'blockchain_trade_id' => $offer['blockchain_trade_id'],
                'old_status' => $offer['contract_status'],
                'new_status' => $input['contract_status'] ?? $offer['contract_status']
            ]
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    error_log('Database error in offers/sync-contract.php: ' . $e->getMessage());
}
?>
