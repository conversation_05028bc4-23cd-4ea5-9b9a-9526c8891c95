'use client';

import { Suspense } from 'react';
import HeroSection from './home/<USER>';
import WhyIkarosSection from './home/<USER>';
import QuickStatsSection from './home/<USER>';
import HowItWorksSection from './home/<USER>';
import SimplePlatformStats from './home/<USER>';
import FeaturesSection from './home/<USER>';
// import TestimonialsSection from './home/<USER>'; // تم حذف المكون
import TrustSecuritySection from './home/<USER>';
import CTASection from './home/<USER>';

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <Suspense fallback={<div className="min-h-screen bg-gradient-to-br from-blue-900 to-indigo-900 animate-pulse" />}>
        <HeroSection />
      </Suspense>

      {/* Why IKAROS Section */}
      <Suspense fallback={<div className="h-96 bg-gray-50 dark:bg-gray-900 animate-pulse" />}>
        <WhyIkarosSection />
      </Suspense>

      {/* Quick Stats Section */}
      <Suspense fallback={<div className="h-96 bg-gradient-to-br from-purple-900 to-indigo-900 animate-pulse" />}>
        <QuickStatsSection />
      </Suspense>

      {/* How It Works Section */}
      <Suspense fallback={<div className="h-96 bg-white dark:bg-gray-900 animate-pulse" />}>
        <HowItWorksSection />
      </Suspense>

      {/* Platform Stats Section */}
      <Suspense fallback={<div className="h-96 bg-gradient-to-br from-gray-900 to-blue-900 animate-pulse" />}>
        <SimplePlatformStats />
      </Suspense>

      {/* Features Section */}
      <Suspense fallback={<div className="h-96 bg-white dark:bg-gray-900 animate-pulse" />}>
        <FeaturesSection />
      </Suspense>

      {/* Testimonials Section - تم حذف المكون مؤقتاً */}
      {/* <Suspense fallback={<div className="h-96 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-purple-900 animate-pulse" />}>
        <TestimonialsSection />
      </Suspense> */}

      {/* Trust & Security Section */}
      <Suspense fallback={<div className="h-96 bg-gradient-to-br from-gray-900 to-indigo-900 animate-pulse" />}>
        <TrustSecuritySection />
      </Suspense>

      {/* CTA Section */}
      <Suspense fallback={<div className="h-96 bg-gradient-to-br from-blue-900 to-purple-900 animate-pulse" />}>
        <CTASection />
      </Suspense>
    </div>
  );
}