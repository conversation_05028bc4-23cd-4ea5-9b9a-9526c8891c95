<?php
/**
 * API Admin Manager للعقود المحسنة
 * Enhanced Admin Manager API
 */

require_once __DIR__ . '/../cors.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../middleware/admin_auth.php';

class AdminManagerAPI {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    public function handleRequest() {
        // التحقق من صلاحيات الإدارة
        $adminAuth = verifyAdminAuth();
        if (!$adminAuth['success']) {
            $this->sendError('Unauthorized access', 401);
            return;
        }
        
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';
        
        try {
            switch ($method) {
                case 'GET':
                    $this->handleGet($action, $adminAuth['admin']);
                    break;
                case 'POST':
                    $this->handlePost($action, $adminAuth['admin']);
                    break;
                case 'PUT':
                    $this->handlePut($action, $adminAuth['admin']);
                    break;
                case 'DELETE':
                    $this->handleDelete($action, $adminAuth['admin']);
                    break;
                default:
                    $this->sendError('Method not allowed', 405);
            }
        } catch (Exception $e) {
            $this->sendError($e->getMessage(), 500);
        }
    }
    
    private function handleGet($action, $admin) {
        switch ($action) {
            case 'system-settings':
                $this->getSystemSettings();
                break;
            case 'admin-profiles':
                $this->getAdminProfiles();
                break;
            case 'dispute-resolutions':
                $this->getDisputeResolutions();
                break;
            case 'blacklisted-users':
                $this->getBlacklistedUsers();
                break;
            case 'admin-stats':
                $this->getAdminStats();
                break;
            case 'activity-logs':
                $this->getActivityLogs();
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    private function handlePost($action, $admin) {
        switch ($action) {
            case 'add-admin':
                $this->addAdmin($admin);
                break;
            case 'resolve-dispute':
                $this->resolveDispute($admin);
                break;
            case 'blacklist-user':
                $this->blacklistUser($admin);
                break;
            case 'emergency-pause':
                $this->emergencyPause($admin);
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    private function handlePut($action, $admin) {
        switch ($action) {
            case 'update-settings':
                $this->updateSystemSettings($admin);
                break;
            case 'update-admin':
                $this->updateAdmin($admin);
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    private function handleDelete($action, $admin) {
        switch ($action) {
            case 'remove-admin':
                $this->removeAdmin($admin);
                break;
            case 'unblacklist-user':
                $this->unblacklistUser($admin);
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    /**
     * جلب إعدادات النظام
     */
    private function getSystemSettings() {
        try {
            $conn = $this->db->getConnection();
            
            $stmt = $conn->prepare("
                SELECT * FROM platform_settings 
                WHERE setting_type IN ('system', 'contract', 'admin')
                ORDER BY setting_key
            ");
            
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // تجميع الإعدادات حسب النوع
            $groupedSettings = [];
            foreach ($settings as $setting) {
                $type = $setting['setting_type'] ?? 'general';
                if (!isset($groupedSettings[$type])) {
                    $groupedSettings[$type] = [];
                }
                $groupedSettings[$type][] = $setting;
            }
            
            $this->sendSuccess($groupedSettings);
            
        } catch (Exception $e) {
            $this->sendError('Failed to fetch system settings: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * جلب ملفات المشرفين
     */
    private function getAdminProfiles() {
        try {
            $conn = $this->db->getConnection();
            
            $stmt = $conn->prepare("
                SELECT 
                    au.*,
                    u.username,
                    u.email,
                    u.created_at as user_created_at,
                    COUNT(aal.id) as total_actions
                FROM admin_users au
                JOIN users u ON au.user_id = u.id
                LEFT JOIN admin_activity_logs aal ON au.user_id = aal.admin_id
                GROUP BY au.id
                ORDER BY au.created_at DESC
            ");
            
            $stmt->execute();
            $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // إضافة إحصائيات لكل مشرف
            foreach ($admins as &$admin) {
                $admin['permissions'] = json_decode($admin['permissions'], true);
                
                // جلب آخر نشاط
                $stmt = $conn->prepare("
                    SELECT created_at 
                    FROM admin_activity_logs 
                    WHERE admin_id = ? 
                    ORDER BY created_at DESC 
                    LIMIT 1
                ");
                
                $stmt->execute([$admin['user_id']]);
                $lastActivity = $stmt->fetch(PDO::FETCH_ASSOC);
                $admin['last_activity'] = $lastActivity['created_at'] ?? null;
            }
            
            $this->sendSuccess($admins);
            
        } catch (Exception $e) {
            $this->sendError('Failed to fetch admin profiles: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * جلب قرارات حل النزاعات
     */
    private function getDisputeResolutions() {
        try {
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(50, max(1, intval($_GET['limit'] ?? 20)));
            $offset = ($page - 1) * $limit;
            
            $conn = $this->db->getConnection();
            
            $stmt = $conn->prepare("
                SELECT 
                    d.*,
                    t.amount,
                    t.currency,
                    st.token_symbol,
                    u_reporter.username as reporter_username,
                    u_admin.username as admin_username
                FROM disputes d
                JOIN trades t ON d.trade_id = t.id
                JOIN supported_tokens st ON t.token_id = st.id
                JOIN users u_reporter ON d.reported_by = u_reporter.id
                LEFT JOIN users u_admin ON d.assigned_admin_id = u_admin.id
                ORDER BY d.created_at DESC
                LIMIT ? OFFSET ?
            ");
            
            $stmt->execute([$limit, $offset]);
            $disputes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // عدد النزاعات الإجمالي
            $stmt = $conn->prepare("SELECT COUNT(*) as total FROM disputes");
            $stmt->execute();
            $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            $this->sendSuccess([
                'disputes' => $disputes,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]);
            
        } catch (Exception $e) {
            $this->sendError('Failed to fetch dispute resolutions: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * جلب المستخدمين في القائمة السوداء
     */
    private function getBlacklistedUsers() {
        try {
            $conn = $this->db->getConnection();
            
            $stmt = $conn->prepare("
                SELECT 
                    u.id,
                    u.username,
                    u.email,
                    u.created_at,
                    aal.created_at as blacklisted_at,
                    aal.details,
                    u_admin.username as blacklisted_by
                FROM users u
                JOIN admin_activity_logs aal ON u.id = aal.target_id 
                    AND aal.action_type = 'blacklist_user' 
                    AND aal.target_type = 'user'
                LEFT JOIN users u_admin ON aal.admin_id = u_admin.id
                WHERE u.is_active = 0
                ORDER BY aal.created_at DESC
            ");
            
            $stmt->execute();
            $blacklistedUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $this->sendSuccess($blacklistedUsers);
            
        } catch (Exception $e) {
            $this->sendError('Failed to fetch blacklisted users: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * جلب إحصائيات الإدارة
     */
    private function getAdminStats() {
        try {
            $conn = $this->db->getConnection();
            
            // إحصائيات عامة
            $stats = [];
            
            // عدد المشرفين
            $stmt = $conn->prepare("SELECT COUNT(*) as total FROM admin_users WHERE is_active = 1");
            $stmt->execute();
            $stats['total_admins'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // عدد النزاعات المحلولة
            $stmt = $conn->prepare("SELECT COUNT(*) as total FROM disputes WHERE status = 'resolved'");
            $stmt->execute();
            $stats['resolved_disputes'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // عدد المستخدمين في القائمة السوداء
            $stmt = $conn->prepare("SELECT COUNT(*) as total FROM users WHERE is_active = 0");
            $stmt->execute();
            $stats['blacklisted_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // نشاط المشرفين في آخر 30 يوم
            $stmt = $conn->prepare("
                SELECT COUNT(*) as total 
                FROM admin_activity_logs 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ");
            $stmt->execute();
            $stats['admin_actions_30d'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // توزيع أنواع النشاط
            $stmt = $conn->prepare("
                SELECT 
                    action_type,
                    COUNT(*) as count
                FROM admin_activity_logs 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY action_type
                ORDER BY count DESC
            ");
            $stmt->execute();
            $stats['action_distribution'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // أكثر المشرفين نشاطاً
            $stmt = $conn->prepare("
                SELECT 
                    u.username,
                    COUNT(aal.id) as actions_count
                FROM admin_activity_logs aal
                JOIN users u ON aal.admin_id = u.id
                WHERE aal.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY aal.admin_id
                ORDER BY actions_count DESC
                LIMIT 10
            ");
            $stmt->execute();
            $stats['top_active_admins'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $this->sendSuccess($stats);
            
        } catch (Exception $e) {
            $this->sendError('Failed to fetch admin stats: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * جلب سجل النشاط
     */
    private function getActivityLogs() {
        try {
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(100, max(1, intval($_GET['limit'] ?? 50)));
            $offset = ($page - 1) * $limit;
            $adminId = $_GET['admin_id'] ?? null;
            $actionType = $_GET['action_type'] ?? null;
            
            $conn = $this->db->getConnection();
            
            $whereConditions = [];
            $params = [];
            
            if ($adminId) {
                $whereConditions[] = "aal.admin_id = ?";
                $params[] = $adminId;
            }
            
            if ($actionType) {
                $whereConditions[] = "aal.action_type = ?";
                $params[] = $actionType;
            }
            
            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
            
            $stmt = $conn->prepare("
                SELECT 
                    aal.*,
                    u.username as admin_username
                FROM admin_activity_logs aal
                JOIN users u ON aal.admin_id = u.id
                $whereClause
                ORDER BY aal.created_at DESC
                LIMIT ? OFFSET ?
            ");
            
            $params[] = $limit;
            $params[] = $offset;
            $stmt->execute($params);
            $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // فك تشفير JSON details
            foreach ($logs as &$log) {
                $log['details'] = json_decode($log['details'], true);
            }
            
            // عدد السجلات الإجمالي
            $countParams = array_slice($params, 0, -2); // إزالة limit و offset
            $stmt = $conn->prepare("
                SELECT COUNT(*) as total 
                FROM admin_activity_logs aal
                JOIN users u ON aal.admin_id = u.id
                $whereClause
            ");
            $stmt->execute($countParams);
            $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            $this->sendSuccess([
                'logs' => $logs,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]);
            
        } catch (Exception $e) {
            $this->sendError('Failed to fetch activity logs: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * إضافة مشرف جديد
     */
    private function addAdmin($currentAdmin) {
        try {
            // التحقق من الصلاحيات
            if ($currentAdmin['admin_role'] !== 'super_admin') {
                $this->sendError('Insufficient permissions', 403);
                return;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            $userId = $input['user_id'] ?? null;
            $adminRole = $input['admin_role'] ?? 'admin';
            $permissions = $input['permissions'] ?? [];
            $department = $input['department'] ?? null;
            
            if (!$userId) {
                $this->sendError('User ID is required', 400);
                return;
            }
            
            $conn = $this->db->getConnection();
            
            // التحقق من عدم وجود المستخدم كمشرف مسبقاً
            $stmt = $conn->prepare("SELECT id FROM admin_users WHERE user_id = ?");
            $stmt->execute([$userId]);
            if ($stmt->fetch()) {
                $this->sendError('User is already an admin', 400);
                return;
            }
            
            // إضافة المشرف
            $stmt = $conn->prepare("
                INSERT INTO admin_users 
                (user_id, admin_role, permissions, department, created_at, is_active)
                VALUES (?, ?, ?, ?, NOW(), 1)
            ");
            
            $stmt->execute([$userId, $adminRole, json_encode($permissions), $department]);
            
            // تسجيل النشاط
            $this->logAdminActivity($currentAdmin['user_id'], 'add_admin', 'admin', $userId, [
                'admin_role' => $adminRole,
                'permissions' => $permissions,
                'department' => $department
            ]);
            
            $this->sendSuccess(['message' => 'Admin added successfully']);
            
        } catch (Exception $e) {
            $this->sendError('Failed to add admin: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * تسجيل نشاط المشرف
     */
    private function logAdminActivity($adminId, $actionType, $targetType, $targetId, $details = []) {
        try {
            $conn = $this->db->getConnection();
            
            $stmt = $conn->prepare("
                INSERT INTO admin_activity_logs 
                (admin_id, action_type, target_type, target_id, details, ip_address, user_agent, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            
            $stmt->execute([
                $adminId,
                $actionType,
                $targetType,
                $targetId,
                json_encode($details),
                $ipAddress,
                $userAgent
            ]);
            
        } catch (Exception $e) {
            // تسجيل الخطأ لكن لا نوقف العملية
            error_log("Failed to log admin activity: " . $e->getMessage());
        }
    }
    
    private function sendSuccess($data) {
        echo json_encode([
            'success' => true,
            'data' => $data
        ]);
    }
    
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message
        ]);
    }
}

// تشغيل API
$api = new AdminManagerAPI();
$api->handleRequest();
?>
