/**
 * خدمة أسعار الصرف
 * تجلب أسعار الصرف الحية من مصادر مجانية
 */

export interface ExchangeRate {
  currency: string;
  rate: number;
  lastUpdated: string;
}

export interface ExchangeRateResponse {
  base: string;
  date: string;
  rates: { [key: string]: number };
}

class ExchangeRateService {
  private cache: Map<string, { rate: number; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 دقائق
  private readonly BASE_CURRENCY = 'USD';

  // مصادر مجانية لأسعار الصرف
  private readonly FREE_APIS = [
    {
      name: 'ExchangeRate-API',
      url: 'https://api.exchangerate-api.com/v4/latest/USD',
      free: true,
      limit: '1500 requests/month'
    },
    {
      name: 'Fixer.io (Free)',
      url: 'https://api.fixer.io/latest?access_key=YOUR_KEY&base=USD',
      free: true,
      limit: '100 requests/month'
    },
    {
      name: 'CurrencyAPI',
      url: 'https://api.currencyapi.com/v3/latest?apikey=YOUR_KEY&base_currency=USD',
      free: true,
      limit: '300 requests/month'
    }
  ];

  /**
   * جلب أسعار الصرف من API مجاني
   */
  private async fetchFromExchangeRateAPI(): Promise<ExchangeRateResponse | null> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 ثوان timeout

      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD', {
        signal: controller.signal,
        headers: {
          'User-Agent': 'Ikaros P2P Platform'
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (!data || !data.rates) {
        throw new Error('Invalid API response format');
      }

      return {
        base: data.base,
        date: data.date,
        rates: data.rates
      };
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.error('ExchangeRate-API request timeout');
      } else if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        console.error('Network error accessing ExchangeRate-API');
      } else {
        console.error('Error fetching from ExchangeRate-API:', error);
      }
      return null;
    }
  }

  /**
   * جلب أسعار الصرف من مصدر احتياطي
   */
  private async fetchFromBackupAPI(): Promise<ExchangeRateResponse | null> {
    try {
      // استخدام API احتياطي مجاني
      const response = await fetch('https://open.er-api.com/v6/latest/USD');
      if (!response.ok) throw new Error('Backup API request failed');
      
      const data = await response.json();
      return {
        base: data.base_code,
        date: data.time_last_update_utc,
        rates: data.rates
      };
    } catch (error) {
      console.error('Error fetching from backup API:', error);
      return null;
    }
  }

  /**
   * أسعار افتراضية في حالة فشل جميع APIs
   */
  private getDefaultRates(): ExchangeRateResponse {
    return {
      base: 'USD',
      date: new Date().toISOString(),
      rates: {
        // العملات التقليدية (مرتبة حسب الأهمية)
        USD: 1.0,
        SAR: 3.75,
        AED: 3.67,
        KWD: 0.31,
        QAR: 3.64,
        BHD: 0.38,
        OMR: 0.38,
        JOD: 0.71,
        EGP: 31.0,
        EUR: 0.92,
        GBP: 0.79,
        JPY: 150.0,
        CAD: 1.35,
        AUD: 1.52,
        CHF: 0.88,
        CNY: 7.25,
        TRY: 27.0,
        LBP: 15000,
        MAD: 10.2,
        TND: 3.1,
        DZD: 135.0,
        IQD: 1310.0,
        SYP: 2512.0,
        YER: 250.0,
        PKR: 280.0,
        INR: 83.0,
        BDT: 110.0,
        MYR: 4.7,
        IDR: 15500.0,
        SGD: 1.35,
        // العملات المستقرة (مرتبة حسب الأهمية)
        USDT: 1.0,
        USDC: 1.0,
        BUSD: 1.0,
        DAI: 1.0,
        FDUSD: 1.0,
        TUSD: 1.0,
        USDD: 1.0,
        FRAX: 1.0,
        USDP: 1.0,
        LUSD: 1.0,
        GUSD: 1.0,
        SUSD: 1.0,
        USTC: 1.0,
        PYUSD: 1.0
      }
    };
  }

  /**
   * جلب سعر الصرف لعملة محددة
   */
  async getExchangeRate(currency: string): Promise<number> {
    if (currency === this.BASE_CURRENCY) return 1.0;

    // العملات المستقرة تساوي 1 USD (مرتبة حسب الأهمية)
    const stablecoins = ['USDT', 'USDC', 'BUSD', 'DAI', 'FDUSD', 'TUSD', 'USDD', 'FRAX', 'USDP', 'LUSD', 'GUSD', 'SUSD', 'USTC', 'PYUSD'];
    if (stablecoins.includes(currency.toUpperCase())) {
      return 1.0;
    }

    // التحقق من الكاش
    const cached = this.cache.get(currency);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.rate;
    }

    try {
      // محاولة جلب من API الرئيسي
      let data = await this.fetchFromExchangeRateAPI();
      
      // إذا فشل، جرب API احتياطي
      if (!data) {
        data = await this.fetchFromBackupAPI();
      }

      // إذا فشل كل شيء، استخدم الأسعار الافتراضية
      if (!data) {
        console.warn('All APIs failed, using default rates');
        data = this.getDefaultRates();
      }

      const rate = data.rates[currency];
      if (!rate) {
        throw new Error(`Currency ${currency} not found`);
      }

      // حفظ في الكاش
      this.cache.set(currency, {
        rate,
        timestamp: Date.now()
      });

      return rate;
    } catch (error) {
      console.error(`Error getting exchange rate for ${currency}:`, error);
      
      // إرجاع سعر افتراضي
      const defaultRates = this.getDefaultRates();
      return defaultRates.rates[currency] || 1.0;
    }
  }

  /**
   * جلب جميع أسعار الصرف للعملات المدعومة
   */
  async getAllExchangeRates(): Promise<{ [key: string]: number }> {
    try {
      let data = await this.fetchFromExchangeRateAPI();
      
      if (!data) {
        data = await this.fetchFromBackupAPI();
      }

      if (!data) {
        console.warn('All APIs failed, using default rates');
        data = this.getDefaultRates();
      }

      // حفظ جميع الأسعار في الكاش
      Object.entries(data.rates).forEach(([currency, rate]) => {
        this.cache.set(currency, {
          rate,
          timestamp: Date.now()
        });
      });

      return data.rates;
    } catch (error) {
      console.error('Error getting all exchange rates:', error);
      return this.getDefaultRates().rates;
    }
  }

  /**
   * تحويل مبلغ من عملة إلى أخرى
   */
  async convertCurrency(
    amount: number,
    fromCurrency: string,
    toCurrency: string
  ): Promise<number> {
    if (fromCurrency === toCurrency) return amount;

    // قائمة العملات المستقرة (مرتبة حسب الأهمية)
    const stablecoins = ['USDT', 'USDC', 'BUSD', 'DAI', 'FDUSD', 'TUSD', 'USDD', 'FRAX', 'USDP', 'LUSD', 'GUSD', 'SUSD', 'USTC', 'PYUSD'];
    const isFromStablecoin = stablecoins.includes(fromCurrency.toUpperCase());
    const isToStablecoin = stablecoins.includes(toCurrency.toUpperCase());

    // إذا كانت كلا العملتين مستقرتين، فلا حاجة للتحويل
    if (isFromStablecoin && isToStablecoin) {
      return amount;
    }

    const fromRate = await this.getExchangeRate(fromCurrency);
    const toRate = await this.getExchangeRate(toCurrency);

    // تحويل إلى USD أولاً ثم إلى العملة المطلوبة
    const usdAmount = amount / fromRate;
    const convertedAmount = usdAmount * toRate;

    return Math.round(convertedAmount * 100) / 100; // تقريب لخانتين عشريتين
  }

  /**
   * حساب سعر USDT بالعملة المحلية
   */
  async getUSDTPrice(currency: string): Promise<number> {
    if (currency === 'USD') return 1.0;
    
    const rate = await this.getExchangeRate(currency);
    return Math.round(rate * 100) / 100;
  }

  /**
   * تحديث الكاش يدوياً
   */
  async refreshCache(): Promise<void> {
    this.cache.clear();
    await this.getAllExchangeRates();
  }

  /**
   * مسح الكاش
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * الحصول على معلومات الكاش
   */
  getCacheInfo(): { [key: string]: { rate: number; age: number } } {
    const info: { [key: string]: { rate: number; age: number } } = {};
    
    this.cache.forEach((value, key) => {
      info[key] = {
        rate: value.rate,
        age: Date.now() - value.timestamp
      };
    });

    return info;
  }

  /**
   * التحقق من صحة العملة
   */
  isSupportedCurrency(currency: string): boolean {
    const supportedCurrencies = [
      'USD', 'EUR', 'SAR', 'AED', 'KWD', 'QAR', 
      'BHD', 'OMR', 'JOD', 'LBP', 'EGP', 'MAD'
    ];
    return supportedCurrencies.includes(currency);
  }

  /**
   * تنسيق السعر حسب العملة
   */
  formatPrice(amount: number, currency: string): string {
    const formatter = new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });

    try {
      return formatter.format(amount);
    } catch (error) {
      // في حالة عدم دعم العملة في Intl
      return `${amount.toFixed(2)} ${currency}`;
    }
  }
}

// إنشاء instance واحد للاستخدام في التطبيق
export const exchangeRateService = new ExchangeRateService();

// دوال مساعدة للاستخدام السريع
export const getUSDTPrice = (currency: string) => exchangeRateService.getUSDTPrice(currency);
export const convertCurrency = (amount: number, from: string, to: string) => 
  exchangeRateService.convertCurrency(amount, from, to);
export const formatPrice = (amount: number, currency: string) => 
  exchangeRateService.formatPrice(amount, currency);
