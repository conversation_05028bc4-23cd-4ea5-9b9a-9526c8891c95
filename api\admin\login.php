<?php
/**
 * API endpoint لتسجيل دخول الإدارة
 * Admin Login API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Max-Age: 86400');

// Include response helper
require_once __DIR__ . '/../helpers/ResponseHelper.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Validate request method
ResponseHelper::validateMethod(['POST']);

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    // Get input data
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        ResponseHelper::error('بيانات غير صحيحة', 'Invalid data', 400);
    }

    // Validate required fields - جميع الحقول مطلوبة للمصادقة الكاملة
    ResponseHelper::validateRequired($input, ['username', 'password', 'walletAddress']);

    $username = $input['username'];
    $password = $input['password'];
    $walletAddress = $input['walletAddress'];

    // تنظيف عنوان المحفظة
    $walletAddress = strtolower(trim($walletAddress));
    
    // Get database connection
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // Check if admin user exists (البحث بالبريد الإلكتروني أو اسم المستخدم)
    $stmt = $connection->prepare("
        SELECT id, username, email, full_name, password_hash, wallet_address, is_admin, is_active
        FROM users
        WHERE (username = ? OR email = ?) AND is_admin = 1 AND is_active = 1
    ");

    $stmt->execute([$username, $username]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        throw new Exception('اسم المستخدم أو كلمة المرور غير صحيحة');
    }
    
    // Verify password
    if (empty($admin['password_hash'])) {
        throw new Exception('كلمة المرور غير محددة لهذا المدير');
    }

    if (!password_verify($password, $admin['password_hash'])) {
        // تسجيل محاولة دخول فاشلة للمدير
        error_log("Failed admin login attempt for user: " . $admin['username'] . " from IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
        throw new Exception('اسم المستخدم أو كلمة المرور غير صحيحة');
    }

    // التحقق من عنوان المحفظة
    if (empty($admin['wallet_address'])) {
        throw new Exception('عنوان المحفظة غير محدد لهذا المدير');
    }

    $adminWalletAddress = strtolower(trim($admin['wallet_address']));
    if ($adminWalletAddress !== $walletAddress) {
        // تسجيل محاولة دخول بمحفظة خاطئة
        error_log("Failed admin login attempt with wrong wallet for user: " . $admin['username'] . " from IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
        throw new Exception('عنوان المحفظة غير صحيح');
    }

    // التحقق من عنوان المحفظة
    if (empty($admin['wallet_address'])) {
        throw new Exception('عنوان المحفظة غير محدد لهذا المدير');
    }

    $adminWalletAddress = strtolower(trim($admin['wallet_address']));
    if ($adminWalletAddress !== $walletAddress) {
        // تسجيل محاولة دخول بمحفظة خاطئة
        error_log("Failed admin login attempt with wrong wallet for user: " . $admin['username'] . " from IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
        throw new Exception('عنوان المحفظة غير صحيح');
    }
    
    // التحقق من وجود المدير في جدول admin_users
    $adminUser = null;
    try {
        $adminUserStmt = $connection->prepare("
            SELECT id, admin_role, permissions, session_timeout_hours
            FROM admin_users
            WHERE user_id = ? AND is_active = 1
        ");
        $adminUserStmt->execute([$admin['id']]);
        $adminUser = $adminUserStmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // إذا لم يكن جدول admin_users موجود، استخدم بيانات افتراضية
        error_log("admin_users table not found, using default admin data: " . $e->getMessage());
        $adminUser = [
            'id' => 1,
            'admin_role' => 'super_admin',
            'permissions' => '["super_admin"]',
            'session_timeout_hours' => 8
        ];
    }

    if (!$adminUser) {
        throw new Exception('المستخدم لا يملك صلاحيات إدارية');
    }

    // إنشاء جلسة إدارية آمنة
    $sessionToken = bin2hex(random_bytes(32));
    $sessionTimeout = $adminUser['session_timeout_hours'] ?: 8;
    $expiresAt = date('Y-m-d H:i:s', time() + ($sessionTimeout * 3600));
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

    // إلغاء الجلسات القديمة للمدير (إذا كان الجدول موجود)
    try {
        $deactivateStmt = $connection->prepare("UPDATE admin_sessions SET is_active = 0 WHERE admin_user_id = ?");
        $deactivateStmt->execute([$adminUser['id']]);

        // إنشاء جلسة جديدة
        $sessionStmt = $connection->prepare("
            INSERT INTO admin_sessions (admin_user_id, session_token, ip_address, user_agent, login_method, expires_at)
            VALUES (?, ?, ?, ?, 'credentials', ?)
        ");
        $sessionStmt->execute([$adminUser['id'], $sessionToken, $ipAddress, $userAgent, $expiresAt]);
    } catch (PDOException $e) {
        // إذا لم يكن جدول admin_sessions موجود، تجاهل الخطأ
        error_log("admin_sessions table not found, skipping session creation: " . $e->getMessage());
    }

    // Update last login time
    $updateStmt = $connection->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $updateStmt->execute([$admin['id']]);

    // إنشاء جلسة PHP عادية أيضاً للتوافق
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    $_SESSION['admin_id'] = $adminUser['id'];
    $_SESSION['admin_logged_in'] = true;
    $_SESSION['admin_role'] = $adminUser['admin_role'];
    $_SESSION['login_time'] = time();

    // Prepare admin data for response
    $adminData = [
        'id' => $admin['id'],
        'admin_id' => $adminUser['id'],
        'username' => $admin['username'],
        'email' => $admin['email'],
        'fullName' => $admin['full_name'],
        'walletAddress' => $admin['wallet_address'],
        'isAdmin' => true,
        'adminRole' => $adminUser['admin_role'],
        'permissions' => json_decode($adminUser['permissions'], true) ?: [],
        'sessionTimeout' => $sessionTimeout,
        'loginMethod' => 'credentials',
        'lastLogin' => date('Y-m-d H:i:s'),
        'expiresAt' => $expiresAt
    ];
    
    // Log the admin login activity (مع التحقق من وجود الجدول)
    try {
        $checkTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
        $checkTable->execute();

        if ($checkTable->rowCount() > 0) {
            $checkColumns = $connection->prepare("SHOW COLUMNS FROM activity_logs LIKE 'entity_type'");
            $checkColumns->execute();

            if ($checkColumns->rowCount() > 0) {
                $logStmt = $connection->prepare("
                    INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data)
                    VALUES (?, 'admin_login', 'user', ?, ?, ?, ?)
                ");

                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                $loginData = json_encode([
                    'login_time' => date('Y-m-d H:i:s'),
                    'method' => 'credentials',
                    'username' => $username
                ]);

                $logStmt->execute([$admin['id'], $admin['id'], $ipAddress, $userAgent, $loginData]);
            }
        }
    } catch (Exception $logError) {
        // تجاهل أخطاء تسجيل النشاط
        error_log('Activity log error in admin login: ' . $logError->getMessage());
    }
    
    // Success response
    ResponseHelper::adminAuth(
        $adminData,
        $sessionToken,
        $expiresAt,
        json_decode($adminUser['permissions'], true) ?: []
    );
    
} catch (Exception $e) {
    ResponseHelper::error($e->getMessage(), 'Login failed', 400);
} catch (PDOException $e) {
    error_log('Database error in admin login: ' . $e->getMessage());
    ResponseHelper::serverError('خطأ في قاعدة البيانات', 'Database error');
}
?>
