<?php
/**
 * API Reputation Manager للعقود المحسنة
 * Enhanced Reputation Manager API
 */

require_once __DIR__ . '/../cors.php';
require_once __DIR__ . '/../config/database.php';

class ReputationManagerAPI {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';
        
        try {
            switch ($method) {
                case 'GET':
                    $this->handleGet($action);
                    break;
                case 'POST':
                    $this->handlePost($action);
                    break;
                case 'PUT':
                    $this->handlePut($action);
                    break;
                default:
                    $this->sendError('Method not allowed', 405);
            }
        } catch (Exception $e) {
            $this->sendError($e->getMessage(), 500);
        }
    }
    
    private function handleGet($action) {
        switch ($action) {
            case 'user-reputation':
                $this->getUserReputation();
                break;
            case 'user-ratings':
                $this->getUserRatings();
                break;
            case 'network-stats':
                $this->getNetworkStats();
                break;
            case 'top-users':
                $this->getTopUsers();
                break;
            case 'reputation-levels':
                $this->getReputationLevels();
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    private function handlePost($action) {
        switch ($action) {
            case 'rate-user':
                $this->rateUser();
                break;
            case 'update-reputation':
                $this->updateReputation();
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    private function handlePut($action) {
        switch ($action) {
            case 'sync-reputation':
                $this->syncReputation();
                break;
            default:
                $this->sendError('Invalid action', 400);
        }
    }
    
    /**
     * جلب سمعة المستخدم
     */
    private function getUserReputation() {
        try {
            $userId = $_GET['user_id'] ?? null;
            $networkId = $_GET['network_id'] ?? 1;
            
            if (!$userId) {
                $this->sendError('User ID is required', 400);
                return;
            }
            
            $conn = $this->db->getConnection();
            
            // جلب بيانات السمعة
            $stmt = $conn->prepare("
                SELECT 
                    eur.*,
                    sn.network_name,
                    u.username,
                    u.email
                FROM enhanced_user_reputation eur
                JOIN supported_networks sn ON eur.network_id = sn.id
                JOIN users u ON eur.user_id = u.id
                WHERE eur.user_id = ? AND eur.network_id = ?
            ");
            
            $stmt->execute([$userId, $networkId]);
            $reputation = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$reputation) {
                // إنشاء سجل سمعة جديد إذا لم يكن موجوداً
                $this->createUserReputation($userId, $networkId);
                
                $stmt->execute([$userId, $networkId]);
                $reputation = $stmt->fetch(PDO::FETCH_ASSOC);
            }
            
            // حساب الإحصائيات الإضافية
            $reputation['success_rate'] = $reputation['total_trades'] > 0 
                ? round(($reputation['successful_trades'] / $reputation['total_trades']) * 100, 2)
                : 0;
                
            $reputation['dispute_rate'] = $reputation['total_trades'] > 0 
                ? round(($reputation['disputed_trades'] / $reputation['total_trades']) * 100, 2)
                : 0;
            
            // جلب آخر التقييمات
            $stmt = $conn->prepare("
                SELECT 
                    eur.*,
                    u.username as rater_username,
                    t.id as trade_id
                FROM enhanced_user_ratings eur
                JOIN users u ON eur.rater_id = u.id
                JOIN trades t ON eur.trade_id = t.id
                WHERE eur.rated_id = ? AND eur.network_id = ? AND eur.is_valid = 1
                ORDER BY eur.created_at DESC
                LIMIT 10
            ");
            
            $stmt->execute([$userId, $networkId]);
            $recentRatings = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $reputation['recent_ratings'] = $recentRatings;
            
            $this->sendSuccess($reputation);
            
        } catch (Exception $e) {
            $this->sendError('Failed to fetch user reputation: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * جلب تقييمات المستخدم
     */
    private function getUserRatings() {
        try {
            $userId = $_GET['user_id'] ?? null;
            $networkId = $_GET['network_id'] ?? 1;
            $type = $_GET['type'] ?? 'received'; // received or given
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(50, max(1, intval($_GET['limit'] ?? 20)));
            $offset = ($page - 1) * $limit;
            
            if (!$userId) {
                $this->sendError('User ID is required', 400);
                return;
            }
            
            $conn = $this->db->getConnection();
            
            $userField = $type === 'given' ? 'rater_id' : 'rated_id';
            $otherUserField = $type === 'given' ? 'rated_id' : 'rater_id';
            
            $stmt = $conn->prepare("
                SELECT 
                    eur.*,
                    u.username as other_user_username,
                    t.amount,
                    t.currency,
                    st.token_symbol
                FROM enhanced_user_ratings eur
                JOIN users u ON eur.$otherUserField = u.id
                JOIN trades t ON eur.trade_id = t.id
                JOIN supported_tokens st ON t.token_id = st.id
                WHERE eur.$userField = ? AND eur.network_id = ? AND eur.is_valid = 1
                ORDER BY eur.created_at DESC
                LIMIT ? OFFSET ?
            ");
            
            $stmt->execute([$userId, $networkId, $limit, $offset]);
            $ratings = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // عدد التقييمات الإجمالي
            $stmt = $conn->prepare("
                SELECT COUNT(*) as total
                FROM enhanced_user_ratings 
                WHERE $userField = ? AND network_id = ? AND is_valid = 1
            ");
            
            $stmt->execute([$userId, $networkId]);
            $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            $this->sendSuccess([
                'ratings' => $ratings,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]);
            
        } catch (Exception $e) {
            $this->sendError('Failed to fetch user ratings: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * جلب إحصائيات الشبكة
     */
    private function getNetworkStats() {
        try {
            $networkId = $_GET['network_id'] ?? 1;
            
            $conn = $this->db->getConnection();
            
            // إحصائيات عامة
            $stmt = $conn->prepare("
                SELECT 
                    COUNT(*) as total_users,
                    AVG(reputation_score) as avg_reputation,
                    SUM(total_trades) as total_trades,
                    SUM(successful_trades) as successful_trades,
                    COUNT(CASE WHEN reputation_level = 'expert' OR reputation_level = 'master' THEN 1 END) as expert_users
                FROM enhanced_user_reputation 
                WHERE network_id = ? AND is_active = 1
            ");
            
            $stmt->execute([$networkId]);
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // توزيع مستويات السمعة
            $stmt = $conn->prepare("
                SELECT 
                    reputation_level,
                    COUNT(*) as count
                FROM enhanced_user_reputation 
                WHERE network_id = ? AND is_active = 1
                GROUP BY reputation_level
            ");
            
            $stmt->execute([$networkId]);
            $levelDistribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $stats['level_distribution'] = $levelDistribution;
            $stats['success_rate'] = $stats['total_trades'] > 0 
                ? round(($stats['successful_trades'] / $stats['total_trades']) * 100, 2)
                : 0;
            
            $this->sendSuccess($stats);
            
        } catch (Exception $e) {
            $this->sendError('Failed to fetch network stats: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * جلب أفضل المستخدمين
     */
    private function getTopUsers() {
        try {
            $networkId = $_GET['network_id'] ?? 1;
            $sortBy = $_GET['sort_by'] ?? 'reputation_score'; // reputation_score, total_trades, average_rating
            $limit = min(100, max(1, intval($_GET['limit'] ?? 20)));
            
            $conn = $this->db->getConnection();
            
            $allowedSortFields = ['reputation_score', 'total_trades', 'average_rating', 'successful_trades'];
            if (!in_array($sortBy, $allowedSortFields)) {
                $sortBy = 'reputation_score';
            }
            
            $stmt = $conn->prepare("
                SELECT 
                    eur.*,
                    u.username,
                    u.created_at as user_joined_at
                FROM enhanced_user_reputation eur
                JOIN users u ON eur.user_id = u.id
                WHERE eur.network_id = ? AND eur.is_active = 1 AND eur.total_trades >= 5
                ORDER BY eur.$sortBy DESC, eur.reputation_score DESC
                LIMIT ?
            ");
            
            $stmt->execute([$networkId, $limit]);
            $topUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // إضافة ترتيب
            foreach ($topUsers as $index => &$user) {
                $user['rank'] = $index + 1;
                $user['success_rate'] = $user['total_trades'] > 0 
                    ? round(($user['successful_trades'] / $user['total_trades']) * 100, 2)
                    : 0;
            }
            
            $this->sendSuccess($topUsers);
            
        } catch (Exception $e) {
            $this->sendError('Failed to fetch top users: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * جلب مستويات السمعة
     */
    private function getReputationLevels() {
        $levels = [
            [
                'level' => 'beginner',
                'name' => 'مبتدئ',
                'name_en' => 'Beginner',
                'min_score' => 0,
                'max_score' => 99,
                'color' => '#6B7280',
                'icon' => '🌱'
            ],
            [
                'level' => 'intermediate',
                'name' => 'متوسط',
                'name_en' => 'Intermediate',
                'min_score' => 100,
                'max_score' => 299,
                'color' => '#3B82F6',
                'icon' => '⭐'
            ],
            [
                'level' => 'advanced',
                'name' => 'متقدم',
                'name_en' => 'Advanced',
                'min_score' => 300,
                'max_score' => 599,
                'color' => '#10B981',
                'icon' => '🏆'
            ],
            [
                'level' => 'expert',
                'name' => 'خبير',
                'name_en' => 'Expert',
                'min_score' => 600,
                'max_score' => 899,
                'color' => '#F59E0B',
                'icon' => '💎'
            ],
            [
                'level' => 'master',
                'name' => 'خبير محترف',
                'name_en' => 'Master',
                'min_score' => 900,
                'max_score' => 1000,
                'color' => '#8B5CF6',
                'icon' => '👑'
            ]
        ];
        
        $this->sendSuccess($levels);
    }
    
    /**
     * تقييم مستخدم
     */
    private function rateUser() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            $tradeId = $input['trade_id'] ?? null;
            $raterId = $input['rater_id'] ?? null;
            $ratedId = $input['rated_id'] ?? null;
            $networkId = $input['network_id'] ?? 1;
            $rating = $input['rating'] ?? null;
            $comment = $input['comment'] ?? '';
            $tradeRole = $input['trade_role'] ?? null;
            
            if (!$tradeId || !$raterId || !$ratedId || !$rating || !$tradeRole) {
                $this->sendError('Missing required fields', 400);
                return;
            }
            
            if ($rating < 1 || $rating > 5) {
                $this->sendError('Rating must be between 1 and 5', 400);
                return;
            }
            
            $ratingNormalized = $rating * 200; // تحويل إلى 200-1000
            
            $conn = $this->db->getConnection();
            
            // التحقق من عدم وجود تقييم سابق
            $stmt = $conn->prepare("
                SELECT id FROM enhanced_user_ratings 
                WHERE trade_id = ? AND rater_id = ?
            ");
            
            $stmt->execute([$tradeId, $raterId]);
            if ($stmt->fetch()) {
                $this->sendError('User already rated for this trade', 400);
                return;
            }
            
            // إدراج التقييم
            $stmt = $conn->prepare("
                INSERT INTO enhanced_user_ratings 
                (trade_id, rater_id, rated_id, network_id, rating, rating_normalized, comment, trade_role, is_verified, is_valid)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, 1)
            ");
            
            $stmt->execute([$tradeId, $raterId, $ratedId, $networkId, $rating, $ratingNormalized, $comment, $tradeRole]);
            
            // تحديث سمعة المستخدم المقيم
            $this->updateUserReputationAfterRating($ratedId, $networkId);
            
            $this->sendSuccess(['message' => 'Rating submitted successfully']);
            
        } catch (Exception $e) {
            $this->sendError('Failed to submit rating: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * إنشاء سجل سمعة جديد للمستخدم
     */
    private function createUserReputation($userId, $networkId) {
        $conn = $this->db->getConnection();
        
        $stmt = $conn->prepare("
            INSERT INTO enhanced_user_reputation 
            (user_id, network_id, reputation_score, reputation_level, is_active)
            VALUES (?, ?, 100, 'beginner', 1)
            ON DUPLICATE KEY UPDATE user_id = user_id
        ");
        
        $stmt->execute([$userId, $networkId]);
    }
    
    /**
     * تحديث سمعة المستخدم بعد التقييم
     */
    private function updateUserReputationAfterRating($userId, $networkId) {
        $conn = $this->db->getConnection();
        
        // حساب متوسط التقييمات الجديد
        $stmt = $conn->prepare("
            SELECT 
                AVG(rating) as avg_rating,
                COUNT(*) as total_ratings
            FROM enhanced_user_ratings 
            WHERE rated_id = ? AND network_id = ? AND is_valid = 1
        ");
        
        $stmt->execute([$userId, $networkId]);
        $ratingStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $avgRating = $ratingStats['avg_rating'] ?? 0;
        $totalRatings = $ratingStats['total_ratings'] ?? 0;
        
        // تحديث السمعة
        $stmt = $conn->prepare("
            UPDATE enhanced_user_reputation 
            SET 
                average_rating = ?,
                total_ratings_received = ?,
                last_updated_at = NOW()
            WHERE user_id = ? AND network_id = ?
        ");
        
        $stmt->execute([$avgRating, $totalRatings, $userId, $networkId]);
    }
    
    private function sendSuccess($data) {
        echo json_encode([
            'success' => true,
            'data' => $data
        ]);
    }
    
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message
        ]);
    }
}

// تشغيل API
$api = new ReputationManagerAPI();
$api->handleRequest();
?>
