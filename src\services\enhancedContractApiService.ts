// خدمة API للعقد الذكي المحسن
import { enhancedContractService } from './enhancedContractService';

// أنواع البيانات
export interface EnhancedOfferData {
  userId: number;
  offerType: 'buy' | 'sell';
  networkId: number;
  tokenId: number;
  amount: string;
  minAmount?: string;
  maxAmount?: string;
  price: string;
  currency: string;
  paymentMethods: string[];
  terms?: string;
  autoReply?: string;
  timeLimit?: number;
}

export interface EnhancedTradeData {
  offerId: number;
  buyerId: number;
  amount: string;
  paymentMethod: string;
}

export interface ContractSyncResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export interface NetworkTokenData {
  networkId: number;
  tokens: any[];
  contracts: any[];
}

class EnhancedContractApiService {
  private apiUrl: string;

  constructor() {
    this.apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '/api';
  }

  /**
   * جلب الشبكات المدعومة
   */
  async getSupportedNetworks(testnet?: boolean, active?: boolean): Promise<any> {
    try {
      const params = new URLSearchParams();
      if (testnet !== undefined) params.append('testnet', testnet ? '1' : '0');
      if (active !== undefined) params.append('active', active ? '1' : '0');

      const response = await fetch(`${this.apiUrl}/enhanced-contracts/network-management.php?action=list&${params}`);
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'فشل في جلب الشبكات المدعومة');
      }

      return result.data;
    } catch (error) {
      console.error('خطأ في جلب الشبكات المدعومة:', error);
      throw error;
    }
  }

  /**
   * جلب العملات المدعومة
   */
  async getSupportedTokens(networkId?: number, stablecoin?: boolean, active?: boolean): Promise<any> {
    try {
      const params = new URLSearchParams();
      if (networkId) params.append('network_id', networkId.toString());
      if (stablecoin !== undefined) params.append('stablecoin', stablecoin ? '1' : '0');
      if (active !== undefined) params.append('active', active ? '1' : '0');

      const response = await fetch(`${this.apiUrl}/enhanced-contracts/networks.php?action=tokens&${params}`);
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'فشل في جلب العملات المدعومة');
      }

      return result.data;
    } catch (error) {
      console.error('خطأ في جلب العملات المدعومة:', error);
      throw error;
    }
  }

  /**
   * جلب عناوين العقود المحسنة
   */
  async getEnhancedContracts(networkId?: number, contractType?: string, active?: boolean): Promise<any> {
    try {
      const params = new URLSearchParams();
      if (networkId) params.append('network_id', networkId.toString());
      if (contractType) params.append('contract_type', contractType);
      if (active !== undefined) params.append('active', active ? '1' : '0');

      const response = await fetch(`${this.apiUrl}/enhanced-contracts/networks.php?action=contracts&${params}`);
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'فشل في جلب عناوين العقود');
      }

      return result.data;
    } catch (error) {
      console.error('خطأ في جلب عناوين العقود:', error);
      throw error;
    }
  }

  /**
   * جلب بيانات الشبكة والعملات
   */
  async getNetworkTokens(networkId: number): Promise<NetworkTokenData> {
    try {
      const response = await fetch(`${this.apiUrl}/enhanced-contracts/networks.php?action=network-tokens&network_id=${networkId}`);
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'فشل في جلب بيانات الشبكة');
      }

      return {
        networkId,
        tokens: result.data.tokens,
        contracts: result.data.contracts
      };
    } catch (error) {
      console.error('خطأ في جلب بيانات الشبكة:', error);
      throw error;
    }
  }

  /**
   * إنشاء عرض محسن مع ربط العقد الذكي
   */
  async createEnhancedOfferWithContract(offerData: EnhancedOfferData): Promise<ContractSyncResult> {
    try {
      // 1. إنشاء العرض في قاعدة البيانات
      const response = await fetch(`${this.apiUrl}/enhanced-contracts/offers.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(offerData)
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'فشل في إنشاء العرض');
      }

      const offerId = result.data.offer_id;

      // 2. إنشاء الصفقة في العقد الذكي
      try {
        // جلب معلومات التوكن
        const tokenInfo = await this.getTokenInfo(offerData.tokenId);
        if (!tokenInfo) {
          throw new Error('معلومات التوكن غير متوفرة');
        }

        // التحقق من الموافقة على التوكن
        const userAddress = await this.getCurrentUserAddress();
        const allowance = await enhancedContractService.getTokenAllowance(tokenInfo.token_address, userAddress);
        
        if (parseFloat(allowance) < parseFloat(offerData.amount)) {
          // طلب الموافقة على التوكن
          const approveHash = await enhancedContractService.approveToken(tokenInfo.token_address, offerData.amount);
          await this.waitForTransaction(approveHash);
        }

        // إنشاء الصفقة في العقد الذكي
        const transactionHash = await enhancedContractService.createTrade(
          tokenInfo.token_address,
          offerData.amount,
          offerData.price,
          offerData.currency
        );

        // 3. انتظار تأكيد المعاملة والحصول على معرف العقد
        const receipt = await this.waitForTransaction(transactionHash);
        const blockchainTradeId = await this.extractTradeIdFromReceipt(receipt);

        // 4. ربط العرض بالعقد الذكي
        await this.syncOfferWithContract({
          offerId,
          blockchainTradeId,
          transactionHash,
          contractStatus: 'created'
        });

        return {
          success: true,
          message: 'تم إنشاء العرض وربطه بالعقد الذكي بنجاح',
          data: {
            offerId,
            blockchainTradeId,
            transactionHash
          }
        };

      } catch (contractError) {
        console.error('خطأ في العقد الذكي:', contractError);
        
        return {
          success: false,
          message: 'تم إنشاء العرض لكن فشل في ربطه بالعقد الذكي',
          error: contractError.message,
          data: { offerId }
        };
      }

    } catch (error) {
      console.error('خطأ في إنشاء العرض:', error);
      return {
        success: false,
        message: 'فشل في إنشاء العرض',
        error: error.message
      };
    }
  }

  /**
   * إنشاء صفقة محسنة مع ربط العقد الذكي
   */
  async createEnhancedTradeWithContract(tradeData: EnhancedTradeData): Promise<ContractSyncResult> {
    try {
      // 1. إنشاء الصفقة في قاعدة البيانات
      const response = await fetch(`${this.apiUrl}/enhanced-contracts/trades.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tradeData)
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'فشل في إنشاء الصفقة');
      }

      const tradeId = result.data.trade_id;

      // 2. الانضمام للصفقة في العقد الذكي
      try {
        // جلب معلومات العرض من العقد الذكي
        const offerInfo = await this.getOfferBlockchainInfo(tradeData.offerId);
        if (!offerInfo || !offerInfo.blockchain_trade_id) {
          throw new Error('العرض غير مرتبط بالعقد الذكي');
        }

        // الانضمام للصفقة
        const transactionHash = await enhancedContractService.joinTrade(offerInfo.blockchain_trade_id);

        // 3. انتظار تأكيد المعاملة
        await this.waitForTransaction(transactionHash);

        // 4. ربط الصفقة بالعقد الذكي
        await this.syncTradeWithContract({
          tradeId,
          blockchainTradeId: offerInfo.blockchain_trade_id,
          transactionHash,
          contractStatus: 'Joined',
          eventType: 'join'
        });

        return {
          success: true,
          message: 'تم إنشاء الصفقة وربطها بالعقد الذكي بنجاح',
          data: {
            tradeId,
            blockchainTradeId: offerInfo.blockchain_trade_id,
            transactionHash
          }
        };

      } catch (contractError) {
        console.error('خطأ في الانضمام للعقد الذكي:', contractError);
        
        return {
          success: false,
          message: 'تم إنشاء الصفقة لكن فشل في ربطها بالعقد الذكي',
          error: contractError.message,
          data: { tradeId }
        };
      }

    } catch (error) {
      console.error('خطأ في إنشاء الصفقة:', error);
      return {
        success: false,
        message: 'فشل في إنشاء الصفقة',
        error: error.message
      };
    }
  }

  /**
   * تأكيد إرسال الدفع
   */
  async confirmPaymentSent(tradeId: number): Promise<ContractSyncResult> {
    try {
      // جلب معلومات الصفقة
      const tradeInfo = await this.getTradeBlockchainInfo(tradeId);
      if (!tradeInfo || !tradeInfo.blockchain_trade_id) {
        throw new Error('الصفقة غير مرتبطة بالعقد الذكي');
      }

      // تأكيد إرسال الدفع في العقد الذكي
      const transactionHash = await enhancedContractService.confirmPaymentSent(tradeInfo.blockchain_trade_id);

      // انتظار تأكيد المعاملة
      await this.waitForTransaction(transactionHash);

      // تحديث حالة الصفقة
      await this.syncTradeWithContract({
        tradeId,
        blockchainTradeId: tradeInfo.blockchain_trade_id,
        transactionHash,
        contractStatus: 'PaymentSent',
        eventType: 'payment_sent'
      });

      return {
        success: true,
        message: 'تم تأكيد إرسال الدفع بنجاح',
        data: { tradeId, transactionHash }
      };

    } catch (error) {
      console.error('خطأ في تأكيد إرسال الدفع:', error);
      return {
        success: false,
        message: 'فشل في تأكيد إرسال الدفع',
        error: error.message
      };
    }
  }

  /**
   * تأكيد استلام الدفع
   */
  async confirmPaymentReceived(tradeId: number): Promise<ContractSyncResult> {
    try {
      // جلب معلومات الصفقة
      const tradeInfo = await this.getTradeBlockchainInfo(tradeId);
      if (!tradeInfo || !tradeInfo.blockchain_trade_id) {
        throw new Error('الصفقة غير مرتبطة بالعقد الذكي');
      }

      // تأكيد استلام الدفع في العقد الذكي
      const transactionHash = await enhancedContractService.confirmPaymentReceived(tradeInfo.blockchain_trade_id);

      // انتظار تأكيد المعاملة
      await this.waitForTransaction(transactionHash);

      // تحديث حالة الصفقة
      await this.syncTradeWithContract({
        tradeId,
        blockchainTradeId: tradeInfo.blockchain_trade_id,
        transactionHash,
        contractStatus: 'Completed',
        eventType: 'complete'
      });

      return {
        success: true,
        message: 'تم تأكيد استلام الدفع وإكمال الصفقة بنجاح',
        data: { tradeId, transactionHash }
      };

    } catch (error) {
      console.error('خطأ في تأكيد استلام الدفع:', error);
      return {
        success: false,
        message: 'فشل في تأكيد استلام الدفع',
        error: error.message
      };
    }
  }

  /**
   * طلب نزاع
   */
  async requestDispute(tradeId: number, reason: string): Promise<ContractSyncResult> {
    try {
      // جلب معلومات الصفقة
      const tradeInfo = await this.getTradeBlockchainInfo(tradeId);
      if (!tradeInfo || !tradeInfo.blockchain_trade_id) {
        throw new Error('الصفقة غير مرتبطة بالعقد الذكي');
      }

      // طلب النزاع في العقد الذكي
      const transactionHash = await enhancedContractService.requestDispute(tradeInfo.blockchain_trade_id);

      // انتظار تأكيد المعاملة
      await this.waitForTransaction(transactionHash);

      // تحديث حالة الصفقة
      await this.syncTradeWithContract({
        tradeId,
        blockchainTradeId: tradeInfo.blockchain_trade_id,
        transactionHash,
        contractStatus: 'Disputed',
        eventType: 'dispute'
      });

      return {
        success: true,
        message: 'تم طلب النزاع بنجاح',
        data: { tradeId, transactionHash }
      };

    } catch (error) {
      console.error('خطأ في طلب النزاع:', error);
      return {
        success: false,
        message: 'فشل في طلب النزاع',
        error: error.message
      };
    }
  }

  /**
   * دوال مساعدة خاصة
   */
  private async getCurrentUserAddress(): Promise<string> {
    if (typeof window !== 'undefined' && window.ethereum) {
      const accounts = await window.ethereum.request({ method: 'eth_accounts' });
      if (accounts.length > 0) {
        return accounts[0];
      }
    }
    throw new Error('لا يوجد حساب متصل');
  }

  private async waitForTransaction(txHash: string): Promise<any> {
    // تنفيذ انتظار تأكيد المعاملة
    return new Promise((resolve, reject) => {
      const checkTransaction = async () => {
        try {
          const provider = enhancedContractService['provider'];
          if (!provider) {
            reject(new Error('المزود غير متوفر'));
            return;
          }

          const receipt = await provider.getTransactionReceipt(txHash);
          if (receipt) {
            if (receipt.status === 1) {
              resolve(receipt);
            } else {
              reject(new Error('فشلت المعاملة'));
            }
          } else {
            setTimeout(checkTransaction, 2000);
          }
        } catch (error) {
          reject(error);
        }
      };

      checkTransaction();
    });
  }

  private async extractTradeIdFromReceipt(receipt: any): Promise<number> {
    // استخراج معرف الصفقة من إيصال المعاملة
    // هذا يتطلب تحليل الأحداث في الإيصال
    try {
      const logs = receipt.logs;
      for (const log of logs) {
        // البحث عن حدث TradeCreated
        if (log.topics && log.topics[0]) {
          // هنا يجب تحليل الحدث للحصول على معرف الصفقة
          // هذا مثال مبسط
          const tradeId = parseInt(log.topics[1], 16);
          if (tradeId > 0) {
            return tradeId;
          }
        }
      }
      throw new Error('لم يتم العثور على معرف الصفقة في الإيصال');
    } catch (error) {
      console.error('خطأ في استخراج معرف الصفقة:', error);
      throw error;
    }
  }

  private async syncOfferWithContract(data: any): Promise<void> {
    const response = await fetch(`${this.apiUrl}/enhanced-contracts/sync.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'sync_offer',
        ...data
      })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'فشل في مزامنة العرض');
    }
  }

  private async syncTradeWithContract(data: any): Promise<void> {
    const response = await fetch(`${this.apiUrl}/enhanced-contracts/sync.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'sync_trade',
        ...data
      })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'فشل في مزامنة الصفقة');
    }
  }

  private async getTokenInfo(tokenId: number): Promise<any> {
    const response = await fetch(`${this.apiUrl}/enhanced-contracts/networks.php?action=tokens&token_id=${tokenId}`);
    const result = await response.json();
    
    if (result.success && result.data.tokens.length > 0) {
      return result.data.tokens[0];
    }
    return null;
  }

  private async getOfferBlockchainInfo(offerId: number): Promise<any> {
    const response = await fetch(`${this.apiUrl}/enhanced-contracts/offers.php?offer_id=${offerId}`);
    const result = await response.json();
    
    if (result.success && result.data.offers.length > 0) {
      return result.data.offers[0];
    }
    return null;
  }

  private async getTradeBlockchainInfo(tradeId: number): Promise<any> {
    const response = await fetch(`${this.apiUrl}/enhanced-contracts/trades.php?trade_id=${tradeId}`);
    const result = await response.json();
    
    if (result.success && result.data.trades.length > 0) {
      return result.data.trades[0];
    }
    return null;
  }
}

// إنشاء مثيل واحد من الخدمة
export const enhancedContractApiService = new EnhancedContractApiService();
export default enhancedContractApiService;
