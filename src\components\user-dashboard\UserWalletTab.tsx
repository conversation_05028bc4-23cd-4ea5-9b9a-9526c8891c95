'use client';

import { useState } from 'react';
import {
  Wallet,
  Plus,
  ArrowUpRight,
  ArrowDownLeft,
  Co<PERSON>,
  RefreshC<PERSON>,
  Eye,
  EyeOff,
  Shield,
  TrendingUp
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';

export default function UserWalletTab() {
  const { user } = useAuth();
  const { formatCurrency } = useUserDashboardTranslation();
  const [showBalance, setShowBalance] = useState(true);

  // بيانات تجريبية للمحفظة
  const walletData = {
    totalBalance: 1250.75,
    availableBalance: 1100.50,
    lockedBalance: 150.25,
    currency: 'USDT'
  };

  const recentTransactions = [
    {
      id: '1',
      type: 'deposit' as const,
      amount: 500,
      currency: 'USDT',
      status: 'completed' as const,
      date: '2024-01-15T10:30:00Z',
      hash: '0x1234...5678'
    },
    {
      id: '2',
      type: 'withdrawal' as const,
      amount: 200,
      currency: 'USDT',
      status: 'pending' as const,
      date: '2024-01-14T15:45:00Z',
      hash: '0x8765...4321'
    }
  ];

  return (
    <div className="space-y-6">
      {/* رأس القسم */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <Wallet className="w-6 h-6 ml-3 text-blue-600" />
            محفظتي
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            إدارة أرصدة ومعاملات المحفظة
          </p>
        </div>
        <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
          <RefreshCw className="w-4 h-4" />
          <span>تحديث</span>
        </button>
      </div>

      {/* نظرة عامة على الرصيد */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">الرصيد الإجمالي</h3>
            <button
              onClick={() => setShowBalance(!showBalance)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              {showBalance ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
            </button>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white">
            {showBalance ? formatCurrency(walletData.totalBalance, walletData.currency) : '****'}
          </div>
          <div className="flex items-center mt-2 text-green-600 dark:text-green-400">
            <TrendingUp className="w-4 h-4 ml-1" />
            <span className="text-sm">+5.2% هذا الشهر</span>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">الرصيد المتاح</h3>
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {showBalance ? formatCurrency(walletData.availableBalance, walletData.currency) : '****'}
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">متاح للتداول</p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">الرصيد المحجوز</h3>
          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
            {showBalance ? formatCurrency(walletData.lockedBalance, walletData.currency) : '****'}
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">في صفقات نشطة</p>
        </div>
      </div>

      {/* الإجراءات السريعة */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">الإجراءات السريعة</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="flex flex-col items-center p-4 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-lg transition-colors">
            <Plus className="w-8 h-8 text-green-600 dark:text-green-400 mb-2" />
            <span className="text-sm font-medium text-green-700 dark:text-green-300">إيداع</span>
          </button>

          <button className="flex flex-col items-center p-4 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-colors">
            <ArrowUpRight className="w-8 h-8 text-red-600 dark:text-red-400 mb-2" />
            <span className="text-sm font-medium text-red-700 dark:text-red-300">سحب</span>
          </button>

          <button className="flex flex-col items-center p-4 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors">
            <ArrowDownLeft className="w-8 h-8 text-blue-600 dark:text-blue-400 mb-2" />
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">تحويل</span>
          </button>

          <button className="flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 rounded-lg transition-colors">
            <Shield className="w-8 h-8 text-purple-600 dark:text-purple-400 mb-2" />
            <span className="text-sm font-medium text-purple-700 dark:text-purple-300">الأمان</span>
          </button>
        </div>
      </div>

      {/* المعاملات الأخيرة */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">المعاملات الأخيرة</h3>
        <div className="space-y-4">
          {recentTransactions.map((transaction) => (
            <div key={transaction.id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className={`p-2 rounded-full ${
                  transaction.type === 'deposit'
                    ? 'bg-green-100 dark:bg-green-900/30'
                    : 'bg-red-100 dark:bg-red-900/30'
                }`}>
                  {transaction.type === 'deposit' ? (
                    <ArrowDownLeft className="w-4 h-4 text-green-600 dark:text-green-400" />
                  ) : (
                    <ArrowUpRight className="w-4 h-4 text-red-600 dark:text-red-400" />
                  )}
                </div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {transaction.type === 'deposit' ? 'إيداع' : 'سحب'}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {new Date(transaction.date).toLocaleDateString('ar-SA')}
                  </p>
                </div>
              </div>
              <div className="text-left">
                <p className={`font-semibold ${
                  transaction.type === 'deposit'
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {transaction.type === 'deposit' ? '+' : '-'}{formatCurrency(transaction.amount, transaction.currency)}
                </p>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    transaction.status === 'completed'
                      ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                      : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300'
                  }`}>
                    {transaction.status === 'completed' ? 'مكتملة' : 'معلقة'}
                  </span>
                  <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200">
                    <Copy className="w-3 h-3" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
