<?php
/**
 * Middleware للتحقق من صلاحيات الأدمن
 * Admin Authentication Middleware
 */

/**
 * التحقق من صلاحيات الأدمن
 * Check admin authentication and permissions
 */
function checkAdminAuth() {
    try {
        // تضمين إعدادات التطوير
        require_once __DIR__ . '/../../config/development.php';

        // في وضع التطوير، السماح بالوصول مباشرة
        if (isDevMode()) {
            return [
                'success' => true,
                'admin_id' => DEV_ADMIN_ID,
                'admin_username' => DEV_ADMIN_USERNAME,
                'admin_role' => 'super_admin',
                'wallet_address' => null,
                'is_development' => true
            ];
        }

        // تضمين ملف المصادقة الإدارية
        require_once __DIR__ . '/../../includes/admin-auth.php';
        require_once __DIR__ . '/../../config/database.php';

        // التحقق من رمز الجلسة في الهيدر
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        $sessionToken = null;

        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $sessionToken = $matches[1];
        }

        // إذا لم يوجد رمز في الهيدر، تحقق من الجلسة العادية
        if (!$sessionToken) {
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }

            if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_logged_in'])) {
                // في وضع التطوير، السماح بالوصول
                if (isDevelopmentMode()) {
                    $adminInfo = getCurrentAdminInfo();
                    return [
                        'success' => true,
                        'admin_id' => $adminInfo['id'],
                        'admin_username' => $adminInfo['username'],
                        'admin_role' => $adminInfo['admin_role'],
                        'wallet_address' => $adminInfo['wallet_address'],
                        'is_development' => true
                    ];
                }

                return [
                    'success' => false,
                    'error' => 'غير مصرح بالوصول',
                    'error_en' => 'Unauthorized access',
                    'code' => 401
                ];
            }

            // التحقق من صحة الجلسة
            $adminInfo = getCurrentAdminInfo();
            if (!$adminInfo) {
                return [
                    'success' => false,
                    'error' => 'جلسة غير صحيحة',
                    'error_en' => 'Invalid session',
                    'code' => 401
                ];
            }

            return [
                'success' => true,
                'admin_id' => $adminInfo['id'],
                'admin_username' => $adminInfo['username'],
                'admin_role' => $adminInfo['admin_role'],
                'wallet_address' => $adminInfo['wallet_address'],
                'permissions' => $adminInfo['all_permissions']
            ];
        }

        // التحقق من رمز الجلسة في قاعدة البيانات
        $db = DatabaseManager::getInstance()->getDatabase();
        $connection = $db->getConnection();

        $stmt = $connection->prepare("
            SELECT as_tbl.admin_user_id, as_tbl.expires_at, as_tbl.is_active,
                   au.admin_role, au.permissions, u.username, u.wallet_address
            FROM admin_sessions as_tbl
            JOIN admin_users au ON as_tbl.admin_user_id = au.id
            JOIN users u ON au.user_id = u.id
            WHERE as_tbl.session_token = ? AND as_tbl.is_active = 1 AND au.is_active = 1
        ");
        $stmt->execute([$sessionToken]);
        $session = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$session) {
            return [
                'success' => false,
                'error' => 'رمز جلسة غير صحيح',
                'error_en' => 'Invalid session token',
                'code' => 401
            ];
        }

        // التحقق من انتهاء صلاحية الجلسة
        if (strtotime($session['expires_at']) < time()) {
            // إلغاء الجلسة المنتهية الصلاحية
            $updateStmt = $connection->prepare("UPDATE admin_sessions SET is_active = 0 WHERE session_token = ?");
            $updateStmt->execute([$sessionToken]);

            return [
                'success' => false,
                'error' => 'انتهت صلاحية الجلسة',
                'error_en' => 'Session expired',
                'code' => 401
            ];
        }

        // تحديث آخر نشاط
        $updateStmt = $connection->prepare("UPDATE admin_sessions SET last_activity = NOW() WHERE session_token = ?");
        $updateStmt->execute([$sessionToken]);

        $permissions = json_decode($session['permissions'], true) ?: [];

        return [
            'success' => true,
            'admin_id' => $session['admin_user_id'],
            'admin_username' => $session['username'],
            'admin_role' => $session['admin_role'],
            'wallet_address' => $session['wallet_address'],
            'permissions' => $permissions
        ];

    } catch (Exception $e) {
        error_log('Admin auth error: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => 'خطأ في التحقق من الصلاحيات',
            'error_en' => 'Authentication error',
            'code' => 500
        ];
    }
}

/**
 * التحقق من صلاحيات الأدمن بواسطة عنوان المحفظة
 * Check admin permissions by wallet address
 */
function checkAdminByWallet($walletAddress) {
    try {
        $auth = new AuthMiddleware();
        
        if (empty($walletAddress)) {
            return [
                'success' => false,
                'error' => 'عنوان المحفظة مطلوب',
                'error_en' => 'Wallet address required'
            ];
        }
        
        // التحقق من صلاحيات الإدارة بواسطة عنوان المحفظة
        $isAdmin = $auth->checkAdminByWallet($walletAddress);
        
        if (!$isAdmin) {
            return [
                'success' => false,
                'error' => 'هذا العنوان لا يملك صلاحيات إدارية',
                'error_en' => 'This address does not have admin privileges'
            ];
        }
        
        return [
            'success' => true,
            'wallet_address' => $walletAddress,
            'is_admin' => true
        ];
        
    } catch (Exception $e) {
        error_log('Admin wallet check error: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => 'خطأ في التحقق من المحفظة',
            'error_en' => 'Wallet verification error'
        ];
    }
}

/**
 * الحصول على رمز المصادقة من مصادر مختلفة
 * Get authentication token from various sources
 */
function getAuthToken() {
    // 1. من Authorization header
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        return $matches[1];
    }
    
    // 2. من Cookie
    if (isset($_COOKIE['admin_session'])) {
        return $_COOKIE['admin_session'];
    }
    
    // 3. من POST data
    $input = json_decode(file_get_contents('php://input'), true);
    if (isset($input['session_token'])) {
        return $input['session_token'];
    }
    
    // 4. من GET parameter (للتطوير فقط)
    if (isset($_GET['token'])) {
        return $_GET['token'];
    }
    
    return null;
}

/**
 * إنشاء جلسة أدمن جديدة
 * Create new admin session
 */
function createAdminSession($userId, $expirationHours = 8) {
    try {
        $auth = new AuthMiddleware();
        
        // التحقق من صلاحيات الإدارة أولاً
        if (!$auth->checkUserPermissions($userId, 'admin')) {
            return [
                'success' => false,
                'error' => 'المستخدم لا يملك صلاحيات إدارية',
                'error_en' => 'User does not have admin privileges'
            ];
        }
        
        // إنشاء جلسة جديدة
        $sessionToken = $auth->createSession($userId, 'admin', $expirationHours);
        
        if (!$sessionToken) {
            return [
                'success' => false,
                'error' => 'فشل في إنشاء الجلسة',
                'error_en' => 'Failed to create session'
            ];
        }
        
        return [
            'success' => true,
            'session_token' => $sessionToken,
            'expires_in' => $expirationHours * 3600,
            'user_id' => $userId
        ];
        
    } catch (Exception $e) {
        error_log('Admin session creation error: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => 'خطأ في إنشاء الجلسة',
            'error_en' => 'Session creation error'
        ];
    }
}

/**
 * إنهاء جلسة الأدمن
 * Terminate admin session
 */
function terminateAdminSession($token = null) {
    try {
        if (!$token) {
            $token = getAuthToken();
        }
        
        if (!$token) {
            return [
                'success' => false,
                'error' => 'لا توجد جلسة نشطة',
                'error_en' => 'No active session'
            ];
        }
        
        $auth = new AuthMiddleware();
        $result = $auth->deactivateSession($token);
        
        return [
            'success' => $result,
            'message' => $result ? 'تم إنهاء الجلسة بنجاح' : 'فشل في إنهاء الجلسة',
            'message_en' => $result ? 'Session terminated successfully' : 'Failed to terminate session'
        ];
        
    } catch (Exception $e) {
        error_log('Admin session termination error: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => 'خطأ في إنهاء الجلسة',
            'error_en' => 'Session termination error'
        ];
    }
}

/**
 * التحقق من صلاحية محددة للأدمن
 * Check specific admin permission
 */
function checkAdminPermission($permission, $adminId = null) {
    try {
        if (!$adminId) {
            $authResult = checkAdminAuth();
            if (!$authResult['success']) {
                return $authResult;
            }
            $adminId = $authResult['admin_id'];
            $adminRole = $authResult['admin_role'] ?? '';
            $permissions = $authResult['permissions'] ?? [];
        } else {
            // جلب معلومات المدير من قاعدة البيانات
            $db = DatabaseManager::getInstance()->getDatabase();
            $connection = $db->getConnection();

            $stmt = $connection->prepare("
                SELECT admin_role, permissions,
                       can_manage_users, can_manage_trades, can_resolve_disputes,
                       can_manage_contracts, can_view_analytics, can_manage_settings,
                       can_emergency_actions, can_manage_admins
                FROM admin_users
                WHERE id = ? AND is_active = 1
            ");
            $stmt->execute([$adminId]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$admin) {
                return [
                    'success' => false,
                    'error' => 'مدير غير موجود',
                    'error_en' => 'Admin not found',
                    'code' => 404
                ];
            }

            $adminRole = $admin['admin_role'];
            $permissions = json_decode($admin['permissions'], true) ?: [];
        }

        // قائمة الصلاحيات المتاحة
        $availablePermissions = [
            'manage_users',
            'manage_trades',
            'resolve_disputes',
            'manage_contracts',
            'view_analytics',
            'manage_settings',
            'emergency_actions',
            'manage_admins'
        ];

        if (!in_array($permission, $availablePermissions)) {
            return [
                'success' => false,
                'error' => 'صلاحية غير معروفة',
                'error_en' => 'Unknown permission',
                'code' => 400
            ];
        }

        // Super admin has all permissions
        if ($adminRole === 'super_admin') {
            return [
                'success' => true,
                'has_permission' => true,
                'admin_role' => $adminRole
            ];
        }

        // Check if permission exists in JSON permissions
        if (in_array($permission, $permissions)) {
            return [
                'success' => true,
                'has_permission' => true,
                'admin_role' => $adminRole
            ];
        }

        // Check boolean permissions if admin data is available
        if (isset($admin)) {
            $permissionMap = [
                'manage_users' => $admin['can_manage_users'],
                'manage_trades' => $admin['can_manage_trades'],
                'resolve_disputes' => $admin['can_resolve_disputes'],
                'manage_contracts' => $admin['can_manage_contracts'],
                'view_analytics' => $admin['can_view_analytics'],
                'manage_settings' => $admin['can_manage_settings'],
                'emergency_actions' => $admin['can_emergency_actions'],
                'manage_admins' => $admin['can_manage_admins']
            ];

            $hasPermission = isset($permissionMap[$permission]) ? (bool)$permissionMap[$permission] : false;

            return [
                'success' => true,
                'has_permission' => $hasPermission,
                'admin_role' => $adminRole
            ];
        }

        return [
            'success' => true,
            'has_permission' => false,
            'admin_role' => $adminRole
        ];
        
        // للآن، جميع الأدمن لديهم جميع الصلاحيات
        // في المستقبل يمكن إضافة نظام صلاحيات أكثر تفصيلاً
        return [
            'success' => true,
            'permission' => $permission,
            'granted' => true
        ];
        
    } catch (Exception $e) {
        error_log('Admin permission check error: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => 'خطأ في التحقق من الصلاحية',
            'error_en' => 'Permission check error'
        ];
    }
}

/**
 * تنظيف الجلسات المنتهية الصلاحية
 * Clean expired admin sessions
 */
function cleanExpiredAdminSessions() {
    try {
        $auth = new AuthMiddleware();
        $result = $auth->cleanExpiredSessions();
        
        return [
            'success' => $result,
            'message' => 'تم تنظيف الجلسات المنتهية الصلاحية',
            'message_en' => 'Expired sessions cleaned'
        ];
        
    } catch (Exception $e) {
        error_log('Admin session cleanup error: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => 'خطأ في تنظيف الجلسات',
            'error_en' => 'Session cleanup error'
        ];
    }
}
?>
