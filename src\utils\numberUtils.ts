/**
 * أدوات التعامل مع الأرقام
 * Number utilities for handling Arabic and English numerals
 */

/**
 * تحويل الأرقام العربية إلى إنجليزية
 * Convert Arabic numerals to English numerals
 */
export function convertArabicToEnglishNumbers(text: string): string {
  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
  
  let result = text;
  
  for (let i = 0; i < arabicNumbers.length; i++) {
    const arabicRegex = new RegExp(arabicNumbers[i], 'g');
    result = result.replace(arabicRegex, englishNumbers[i]);
  }
  
  return result;
}

/**
 * تحويل الأرقام الإنجليزية إلى عربية
 * Convert English numerals to Arabic numerals
 */
export function convertEnglishToArabicNumbers(text: string): string {
  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
  
  let result = text;
  
  for (let i = 0; i < englishNumbers.length; i++) {
    const englishRegex = new RegExp(englishNumbers[i], 'g');
    result = result.replace(englishRegex, arabicNumbers[i]);
  }
  
  return result;
}

/**
 * تنسيق الأرقام مع فواصل الآلاف
 * Format numbers with thousands separators
 */
export function formatNumber(
  num: number | string, 
  locale: string = 'en-US',
  options?: Intl.NumberFormatOptions
): string {
  const number = typeof num === 'string' ? parseFloat(num) : num;
  
  if (isNaN(number)) return '0';
  
  const formatted = new Intl.NumberFormat(locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
    ...options
  }).format(number);
  
  // تأكد من استخدام الأرقام الإنجليزية دائماً
  return convertArabicToEnglishNumbers(formatted);
}

/**
 * تنسيق العملة
 * Format currency
 */
export function formatCurrency(
  amount: number | string,
  currency: string = 'USD',
  locale: string = 'en-US'
): string {
  const number = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(number)) return '0';
  
  // للعملات المشفرة، استخدم تنسيق خاص
  if (['USDT', 'USDC', 'BTC', 'ETH', 'BNB'].includes(currency.toUpperCase())) {
    const formatted = formatNumber(number, locale, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 8
    });
    return `${formatted} ${currency.toUpperCase()}`;
  }
  
  // للعملات التقليدية
  const formatted = new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(number);
  
  return convertArabicToEnglishNumbers(formatted);
}

/**
 * تنسيق النسبة المئوية
 * Format percentage
 */
export function formatPercentage(
  value: number | string,
  locale: string = 'en-US',
  decimals: number = 1
): string {
  const number = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(number)) return '0%';
  
  const formatted = new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(number / 100);
  
  return convertArabicToEnglishNumbers(formatted);
}

/**
 * تقريب الرقم إلى عدد معين من المنازل العشرية
 * Round number to specified decimal places
 */
export function roundToDecimals(num: number, decimals: number = 2): number {
  return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
}

/**
 * تحويل الرقم إلى تنسيق مختصر (K, M, B)
 * Convert number to abbreviated format (K, M, B)
 */
export function abbreviateNumber(num: number, locale: string = 'en-US'): string {
  if (num < 1000) {
    return formatNumber(num, locale);
  }
  
  const units = ['', 'K', 'M', 'B', 'T'];
  const unitIndex = Math.floor(Math.log10(Math.abs(num)) / 3);
  const unitValue = Math.pow(1000, unitIndex);
  const abbreviated = num / unitValue;
  
  const formatted = formatNumber(abbreviated, locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 1
  });
  
  return `${formatted}${units[unitIndex]}`;
}

/**
 * التحقق من صحة الرقم
 * Validate if string is a valid number
 */
export function isValidNumber(value: string): boolean {
  // تحويل الأرقام العربية أولاً
  const converted = convertArabicToEnglishNumbers(value);
  const number = parseFloat(converted);
  return !isNaN(number) && isFinite(number);
}

/**
 * تنظيف النص من الأرقام العربية وتحويلها
 * Clean text by converting Arabic numbers to English
 */
export function cleanNumberText(text: string): string {
  return convertArabicToEnglishNumbers(text.trim());
}

/**
 * تنسيق الوقت (بالدقائق أو الساعات)
 * Format time duration
 */
export function formatDuration(minutes: number, locale: string = 'ar'): string {
  if (minutes < 60) {
    const formatted = formatNumber(minutes, 'en-US');
    return locale === 'ar' ? `${formatted} دقيقة` : `${formatted} min`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  const formattedHours = formatNumber(hours, 'en-US');
  const formattedMinutes = formatNumber(remainingMinutes, 'en-US');
  
  if (locale === 'ar') {
    if (remainingMinutes === 0) {
      return `${formattedHours} ساعة`;
    }
    return `${formattedHours} ساعة و ${formattedMinutes} دقيقة`;
  } else {
    if (remainingMinutes === 0) {
      return `${formattedHours}h`;
    }
    return `${formattedHours}h ${formattedMinutes}m`;
  }
}
