'use client';

import { useState } from 'react';
import { 
  CreditCard, 
  ArrowUpRight, 
  ArrowDownLeft, 
  Filter, 
  Search, 
  Download, 
  Eye, 
  AlertCircle,
  CheckCircle,
  Clock,
  X,
  RefreshCw,
  FileText
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';

interface Transaction {
  id: string;
  type: 'deposit' | 'withdrawal' | 'trade' | 'fee' | 'refund';
  amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  date: string;
  description: string;
  reference?: string;
  fee: number;
  balanceAfter: number;
  relatedTradeId?: string;
}

export default function TransactionsPage() {
  const { t, formatCurrency, formatDate } = useUserDashboardTranslation();
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);

  // بيانات تجريبية للمعاملات
  const transactions: Transaction[] = [
    {
      id: 'TXN-001',
      type: 'trade',
      amount: 1000,
      currency: 'USDT',
      status: 'completed',
      date: new Date().toISOString(),
      description: 'بيع USDT - صفقة #12345',
      reference: 'TRADE-12345',
      fee: 15,
      balanceAfter: 5500,
      relatedTradeId: '12345'
    },
    {
      id: 'TXN-002',
      type: 'deposit',
      amount: 2000,
      currency: 'SAR',
      status: 'completed',
      date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      description: 'إيداع عبر التحويل البنكي',
      reference: 'DEP-67890',
      fee: 0,
      balanceAfter: 4515
    },
    {
      id: 'TXN-003',
      type: 'withdrawal',
      amount: 500,
      currency: 'SAR',
      status: 'processing',
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      description: 'سحب إلى الحساب البنكي',
      reference: 'WTH-11111',
      fee: 10,
      balanceAfter: 2515
    },
    {
      id: 'TXN-004',
      type: 'fee',
      amount: 25,
      currency: 'SAR',
      status: 'completed',
      date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      description: 'رسوم منصة - صفقة #12340',
      reference: 'FEE-22222',
      fee: 0,
      balanceAfter: 3025,
      relatedTradeId: '12340'
    },
    {
      id: 'TXN-005',
      type: 'refund',
      amount: 100,
      currency: 'SAR',
      status: 'completed',
      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      description: 'استرداد رسوم - صفقة ملغية',
      reference: 'REF-33333',
      fee: 0,
      balanceAfter: 3050
    }
  ];

  const filters = [
    { value: 'all', label: t('transactions.filters.all') },
    { value: 'deposits', label: t('transactions.filters.deposits') },
    { value: 'withdrawals', label: t('transactions.filters.withdrawals') },
    { value: 'trades', label: t('transactions.filters.trades') },
    { value: 'fees', label: t('transactions.filters.fees') },
    { value: 'refunds', label: t('transactions.filters.refunds') }
  ];

  const getStatusIcon = (status: Transaction['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'processing':
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'failed':
      case 'cancelled':
        return <X className="w-5 h-5 text-red-500" />;
      case 'refunded':
        return <RefreshCw className="w-5 h-5 text-blue-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: Transaction['status']) => {
    return t(`transactions.status.${status}`);
  };

  const getStatusColor = (status: Transaction['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300';
      case 'processing':
      case 'pending':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
      case 'failed':
      case 'cancelled':
        return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
      case 'refunded':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300';
      default:
        return 'bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300';
    }
  };

  const getTypeIcon = (type: Transaction['type']) => {
    switch (type) {
      case 'deposit':
        return <ArrowDownLeft className="w-5 h-5 text-green-600" />;
      case 'withdrawal':
        return <ArrowUpRight className="w-5 h-5 text-red-600" />;
      case 'trade':
        return <CreditCard className="w-5 h-5 text-blue-600" />;
      case 'fee':
        return <AlertCircle className="w-5 h-5 text-orange-600" />;
      case 'refund':
        return <RefreshCw className="w-5 h-5 text-purple-600" />;
      default:
        return <CreditCard className="w-5 h-5 text-gray-600" />;
    }
  };

  const filteredTransactions = transactions.filter(transaction => {
    const matchesFilter = selectedFilter === 'all' || 
      (selectedFilter === 'deposits' && transaction.type === 'deposit') ||
      (selectedFilter === 'withdrawals' && transaction.type === 'withdrawal') ||
      (selectedFilter === 'trades' && transaction.type === 'trade') ||
      (selectedFilter === 'fees' && transaction.type === 'fee') ||
      (selectedFilter === 'refunds' && transaction.type === 'refund');

    const matchesSearch = searchTerm === '' || 
      transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (transaction.reference && transaction.reference.toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesFilter && matchesSearch;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <CreditCard className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('transactions.title')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {t('transactions.subtitle')}
            </p>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          {/* Filters */}
          <div className="flex flex-wrap gap-2">
            {filters.map((filter) => (
              <button
                key={filter.value}
                onClick={() => setSelectedFilter(filter.value)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedFilter === filter.value
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                {filter.label}
              </button>
            ))}
          </div>

          {/* Search and Export */}
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="relative">
              <Search className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="البحث في المعاملات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 rtl:pr-10 rtl:pl-3 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
              <Download className="w-4 h-4" />
              <span>تصدير</span>
            </button>
          </div>
        </div>
      </div>

      {/* Transactions Table */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('transactions.transaction.type')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('transactions.transaction.description')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('transactions.transaction.amount')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('transactions.transaction.status')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  {t('transactions.transaction.date')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredTransactions.map((transaction) => (
                <tr key={transaction.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      {getTypeIcon(transaction.type)}
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {transaction.id}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {transaction.description}
                      </div>
                      {transaction.reference && (
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {transaction.reference}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm">
                      <div className={`font-medium ${
                        transaction.type === 'deposit' || transaction.type === 'refund' 
                          ? 'text-green-600 dark:text-green-400' 
                          : 'text-red-600 dark:text-red-400'
                      }`}>
                        {transaction.type === 'deposit' || transaction.type === 'refund' ? '+' : '-'}
                        {formatCurrency(transaction.amount, transaction.currency)}
                      </div>
                      {transaction.fee > 0 && (
                        <div className="text-gray-500 dark:text-gray-400">
                          رسوم: {formatCurrency(transaction.fee, transaction.currency)}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      {getStatusIcon(transaction.status)}
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                        {getStatusText(transaction.status)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {formatDate(transaction.date)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <button
                        onClick={() => setSelectedTransaction(transaction)}
                        className="text-blue-600 dark:text-blue-400 hover:underline flex items-center space-x-1 rtl:space-x-reverse"
                      >
                        <Eye className="w-4 h-4" />
                        <span>عرض</span>
                      </button>
                      <button className="text-gray-600 dark:text-gray-400 hover:underline flex items-center space-x-1 rtl:space-x-reverse">
                        <FileText className="w-4 h-4" />
                        <span>إيصال</span>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredTransactions.length === 0 && (
          <div className="text-center py-12">
            <CreditCard className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              لا توجد معاملات تطابق المعايير المحددة
            </p>
          </div>
        )}
      </div>

      {/* Transaction Details Modal */}
      {selectedTransaction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                تفاصيل المعاملة
              </h3>
              <button
                onClick={() => setSelectedTransaction(null)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">رقم المعاملة:</span>
                <span className="font-medium text-gray-900 dark:text-white">{selectedTransaction.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">النوع:</span>
                <span className="font-medium text-gray-900 dark:text-white">{selectedTransaction.type}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">المبلغ:</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {formatCurrency(selectedTransaction.amount, selectedTransaction.currency)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">الحالة:</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedTransaction.status)}`}>
                  {getStatusText(selectedTransaction.status)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">التاريخ:</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {formatDate(selectedTransaction.date)}
                </span>
              </div>
              {selectedTransaction.reference && (
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">المرجع:</span>
                  <span className="font-medium text-gray-900 dark:text-white">{selectedTransaction.reference}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">الرصيد بعد المعاملة:</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {formatCurrency(selectedTransaction.balanceAfter, selectedTransaction.currency)}
                </span>
              </div>
            </div>

            <div className="mt-6 flex space-x-3 rtl:space-x-reverse">
              <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                تحميل الإيصال
              </button>
              <button
                onClick={() => setSelectedTransaction(null)}
                className="flex-1 bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg transition-colors"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
