/**
 * خدمة تكامل APIs مع العقد الذكي
 * Smart Contract API Integration Service
 */

import { contractService } from './contractService';
import { notificationService } from './notificationService';

export interface OfferContractData {
  offerId: number;
  blockchainTradeId?: number;
  transactionHash?: string;
  contractStatus?: string;
  amount: number;
  price: number;
  currency: string;
  stablecoin: string;
}

export interface TradeContractData {
  tradeId: number;
  blockchainTradeId?: number;
  transactionHash?: string;
  contractStatus?: string;
  eventType?: string;
  amount: number;
  sellerAddress?: string;
  buyerAddress?: string;
}

export interface ContractSyncResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

class ContractApiService {
  constructor() {
    // استخدام المسارات النسبية للاستفادة من rewrites في next.config.ts
  }

  /**
   * إنشاء عرض مع ربطه بالعقد الذكي
   */
  async createOfferWithContract(offerData: any): Promise<ContractSyncResult> {
    try {
      // 1. إنشاء العرض في قاعدة البيانات أولاً
      const response = await fetch('/api/offers/index.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(offerData)
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'فشل في إنشاء العرض');
      }

      const offerId = result.data.id;

      // 2. إنشاء الصفقة في العقد الذكي
      try {
        const amount = parseFloat(offerData.amount);
        const transactionHash = await contractService.createTrade(amount);

        // 3. انتظار تأكيد المعاملة والحصول على معرف العقد
        const receipt = await this.waitForTransaction(transactionHash);
        const blockchainTradeId = await this.extractTradeIdFromReceipt(receipt);

        // 4. ربط العرض بالعقد الذكي
        await this.syncOfferWithContract({
          offerId,
          blockchainTradeId,
          transactionHash,
          contractStatus: 'created',
          amount,
          price: parseFloat(offerData.price),
          currency: offerData.currency,
          stablecoin: offerData.stablecoin || 'USDT'
        });

        return {
          success: true,
          message: 'تم إنشاء العرض وربطه بالعقد الذكي بنجاح',
          data: {
            offerId,
            blockchainTradeId,
            transactionHash
          }
        };

      } catch (contractError) {
        console.error('خطأ في إنشاء العقد الذكي:', contractError);
        
        // في حالة فشل العقد الذكي، احتفظ بالعرض في قاعدة البيانات
        // مع تحديد حالة المزامنة كـ "فاشلة"
        await this.updateOfferSyncStatus(offerId, 'failed', contractError.message);

        return {
          success: false,
          message: 'تم إنشاء العرض لكن فشل في ربطه بالعقد الذكي',
          error: contractError.message,
          data: { offerId }
        };
      }

    } catch (error) {
      console.error('خطأ في إنشاء العرض:', error);
      return {
        success: false,
        message: 'فشل في إنشاء العرض',
        error: error.message
      };
    }
  }

  /**
   * إنشاء صفقة مع ربطها بالعقد الذكي
   */
  async createTradeWithContract(tradeData: any): Promise<ContractSyncResult> {
    try {
      // 1. إنشاء الصفقة في قاعدة البيانات أولاً
      const response = await fetch(`${this.apiUrl}/trades/index.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tradeData)
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'فشل في إنشاء الصفقة');
      }

      const tradeId = result.data.id;

      // 2. الانضمام للصفقة في العقد الذكي
      try {
        // الحصول على معرف العقد الذكي من العرض
        const offerBlockchainId = await this.getOfferBlockchainId(tradeData.offer_id);
        if (!offerBlockchainId) {
          throw new Error('العرض غير مربوط بالعقد الذكي');
        }

        const transactionHash = await contractService.joinTrade(offerBlockchainId);

        // 3. انتظار تأكيد المعاملة
        const receipt = await this.waitForTransaction(transactionHash);

        // 4. ربط الصفقة بالعقد الذكي
        await this.syncTradeWithContract({
          tradeId,
          blockchainTradeId: offerBlockchainId,
          transactionHash,
          contractStatus: 'joined',
          eventType: 'BuyerJoined',
          amount: parseFloat(tradeData.amount)
        });

        return {
          success: true,
          message: 'تم إنشاء الصفقة وربطها بالعقد الذكي بنجاح',
          data: {
            tradeId,
            blockchainTradeId: offerBlockchainId,
            transactionHash
          }
        };

      } catch (contractError) {
        console.error('خطأ في الانضمام للعقد الذكي:', contractError);
        
        // في حالة فشل العقد الذكي، احتفظ بالصفقة في قاعدة البيانات
        await this.updateTradeSyncStatus(tradeId, 'failed', contractError.message);

        return {
          success: false,
          message: 'تم إنشاء الصفقة لكن فشل في ربطها بالعقد الذكي',
          error: contractError.message,
          data: { tradeId }
        };
      }

    } catch (error) {
      console.error('خطأ في إنشاء الصفقة:', error);
      return {
        success: false,
        message: 'فشل في إنشاء الصفقة',
        error: error.message
      };
    }
  }

  /**
   * ربط عرض موجود بالعقد الذكي
   */
  async syncOfferWithContract(data: OfferContractData): Promise<ContractSyncResult> {
    try {
      const response = await fetch(`${this.apiUrl}/offers/sync-contract.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          offer_id: data.offerId,
          blockchain_trade_id: data.blockchainTradeId,
          transaction_hash: data.transactionHash,
          contract_status: data.contractStatus
        })
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'فشل في ربط العرض بالعقد الذكي');
      }

      return {
        success: true,
        message: result.message,
        data: result.data
      };

    } catch (error) {
      console.error('خطأ في ربط العرض بالعقد الذكي:', error);
      return {
        success: false,
        message: 'فشل في ربط العرض بالعقد الذكي',
        error: error.message
      };
    }
  }

  /**
   * ربط صفقة موجودة بالعقد الذكي
   */
  async syncTradeWithContract(data: TradeContractData): Promise<ContractSyncResult> {
    try {
      const response = await fetch(`${this.apiUrl}/trades/sync-contract.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          trade_id: data.tradeId,
          blockchain_trade_id: data.blockchainTradeId,
          transaction_hash: data.transactionHash,
          contract_status: data.contractStatus,
          event_type: data.eventType
        })
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'فشل في ربط الصفقة بالعقد الذكي');
      }

      return {
        success: true,
        message: result.message,
        data: result.data
      };

    } catch (error) {
      console.error('خطأ في ربط الصفقة بالعقد الذكي:', error);
      return {
        success: false,
        message: 'فشل في ربط الصفقة بالعقد الذكي',
        error: error.message
      };
    }
  }

  /**
   * تحديث حالة مزامنة العرض
   */
  async updateOfferSyncStatus(offerId: number, status: string, errorMessage?: string): Promise<void> {
    try {
      await fetch(`${this.apiUrl}/offers/sync-contract.php`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          offer_id: offerId,
          sync_status: status,
          sync_error: errorMessage
        })
      });
    } catch (error) {
      console.error('خطأ في تحديث حالة مزامنة العرض:', error);
    }
  }

  /**
   * تحديث حالة مزامنة الصفقة
   */
  async updateTradeSyncStatus(tradeId: number, status: string, errorMessage?: string): Promise<void> {
    try {
      await fetch(`${this.apiUrl}/trades/sync-contract.php`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          trade_id: tradeId,
          sync_status: status,
          sync_error: errorMessage
        })
      });
    } catch (error) {
      console.error('خطأ في تحديث حالة مزامنة الصفقة:', error);
    }
  }

  /**
   * الحصول على معرف العقد الذكي للعرض
   */
  async getOfferBlockchainId(offerId: number): Promise<number | null> {
    try {
      const response = await fetch(`${this.apiUrl}/offers/index.php?id=${offerId}`);
      const result = await response.json();
      
      if (result.success && result.data && result.data.blockchain_trade_id) {
        return parseInt(result.data.blockchain_trade_id);
      }
      
      return null;
    } catch (error) {
      console.error('خطأ في الحصول على معرف العقد الذكي للعرض:', error);
      return null;
    }
  }

  /**
   * انتظار تأكيد المعاملة
   */
  async waitForTransaction(transactionHash: string, timeout: number = 60000): Promise<any> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const receipt = await contractService.getTransactionReceipt(transactionHash);
        if (receipt && receipt.status === 1) {
          return receipt;
        }
      } catch (error) {
        // تجاهل الأخطاء المؤقتة
      }
      
      // انتظار ثانيتين قبل المحاولة التالية
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    throw new Error('انتهت مهلة انتظار تأكيد المعاملة');
  }

  /**
   * استخراج معرف الصفقة من إيصال المعاملة
   */
  async extractTradeIdFromReceipt(receipt: any): Promise<number> {
    try {
      // البحث عن حدث TradeCreated في الإيصال
      const tradeCreatedEvent = receipt.logs.find((log: any) => 
        log.topics && log.topics[0] === contractService.getEventSignature('TradeCreated')
      );
      
      if (tradeCreatedEvent) {
        // فك تشفير البيانات للحصول على معرف الصفقة
        const decodedData = contractService.decodeEventData('TradeCreated', tradeCreatedEvent);
        return parseInt(decodedData.tradeId);
      }
      
      throw new Error('لم يتم العثور على حدث إنشاء الصفقة');
    } catch (error) {
      console.error('خطأ في استخراج معرف الصفقة:', error);
      throw error;
    }
  }

  /**
   * جلب الصفقات غير المتزامنة
   */
  async getUnsyncedTrades(limit: number = 50): Promise<any[]> {
    try {
      const response = await fetch(`${this.apiUrl}/trades/sync-contract.php?sync_status=pending&limit=${limit}`);
      const result = await response.json();
      
      if (result.success) {
        return result.data || [];
      }
      
      return [];
    } catch (error) {
      console.error('خطأ في جلب الصفقات غير المتزامنة:', error);
      return [];
    }
  }
}

// إنشاء مثيل واحد من الخدمة
export const contractApiService = new ContractApiService();

// تصدير الخدمة كافتراضي
export default contractApiService;
