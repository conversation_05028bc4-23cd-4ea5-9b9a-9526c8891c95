'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import {
  ArrowLeft,
  Flag,
  AlertTriangle,
  Shield,
  CheckCircle,
  XCircle
} from 'lucide-react';

const reportReasons = [
  { id: 'fraud', key: 'fraud' },
  { id: 'spam', key: 'spam' },
  { id: 'inappropriate', key: 'inappropriate' },
  { id: 'fake_info', key: 'fakeInfo' },
  { id: 'suspicious', key: 'suspicious' },
  { id: 'other', key: 'other' }
];

export default function ReportOfferPage() {
  const params = useParams();
  const router = useRouter();
  const { t } = useTranslation('common');
  
  const [selectedReason, setSelectedReason] = useState('');
  const [description, setDescription] = useState('');
  const [submitted, setSubmitted] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedReason) return;

    setSubmitting(true);
    
    try {
      // محاكاة إرسال البلاغ
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSubmitted(true);
      
      // العودة للصفحة السابقة بعد 3 ثوان
      setTimeout(() => {
        router.back();
      }, 3000);
      
    } catch (error) {
      console.error('Error submitting report:', error);
    } finally {
      setSubmitting(false);
    }
  };

  if (submitted) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <CheckCircle className="w-16 h-16 mx-auto mb-4 text-green-500" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            تم إرسال البلاغ بنجاح
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            شكراً لك على المساعدة في الحفاظ على أمان المنصة
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            سيتم مراجعة البلاغ من قبل فريق الإدارة
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="container-custom max-w-2xl">
        {/* Header */}
        <div className="flex items-center mb-6">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors mr-4"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            {t('back')}
          </button>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            الإبلاغ عن العرض
          </h1>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          {/* تحذير */}
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-yellow-800 dark:text-yellow-200">
                <p className="font-medium mb-1">تحذير مهم</p>
                <p>يرجى التأكد من صحة البلاغ. البلاغات الكاذبة قد تؤدي إلى تقييد حسابك.</p>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* سبب البلاغ */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                سبب البلاغ *
              </label>
              <div className="space-y-2">
                {reportReasons.map((reason) => (
                  <label key={reason.id} className="flex items-center">
                    <input
                      type="radio"
                      name="reason"
                      value={reason.id}
                      checked={selectedReason === reason.id}
                      onChange={(e) => setSelectedReason(e.target.value)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="mr-2 text-gray-700 dark:text-gray-300">
                      {reason.id === 'fraud' && 'احتيال أو نصب'}
                      {reason.id === 'spam' && 'رسائل مزعجة'}
                      {reason.id === 'inappropriate' && 'محتوى غير مناسب'}
                      {reason.id === 'fake_info' && 'معلومات مزيفة'}
                      {reason.id === 'suspicious' && 'نشاط مشبوه'}
                      {reason.id === 'other' && 'أخرى'}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* وصف المشكلة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                وصف المشكلة (اختياري)
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="يرجى وصف المشكلة بالتفصيل..."
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                كلما كان الوصف أكثر تفصيلاً، كان بإمكاننا مساعدتك بشكل أفضل
              </p>
            </div>

            {/* معلومات إضافية */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start">
                <Shield className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-blue-800 dark:text-blue-200">
                  <p className="font-medium mb-1">حماية الخصوصية</p>
                  <p>جميع البلاغات سرية ولن يتم الكشف عن هويتك للطرف المبلغ عنه</p>
                </div>
              </div>
            </div>

            {/* أزرار الإجراءات */}
            <div className="flex space-x-3 space-x-reverse">
              <button
                type="submit"
                disabled={!selectedReason || submitting}
                className="flex-1 flex items-center justify-center space-x-2 space-x-reverse px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              >
                <Flag className="w-5 h-5" />
                <span>{submitting ? 'جاري الإرسال...' : 'إرسال البلاغ'}</span>
              </button>
              
              <button
                type="button"
                onClick={() => router.back()}
                className="px-6 py-3 text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </form>
        </div>

        {/* معلومات إضافية */}
        <div className="mt-6 text-center text-sm text-gray-500 dark:text-gray-400">
          <p>
            إذا كنت تواجه مشكلة عاجلة، يرجى التواصل مع{' '}
            <a href="/support" className="text-blue-600 dark:text-blue-400 hover:underline">
              فريق الدعم
            </a>
            {' '}مباشرة
          </p>
        </div>
      </div>
    </div>
  );
}
