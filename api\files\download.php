<?php
/**
 * API endpoint لتحميل الملفات
 * File Download API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";
require_once __DIR__ . "/../middleware/auth.php";

// التحقق من طريقة الطلب
validateRequestMethod(['GET']);

/**
 * إرسال الملف للمتصفح
 */
function sendFile($filePath, $originalName, $mimeType) {
    if (!file_exists($filePath)) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'الملف غير موجود'
        ]);
        exit();
    }
    
    // تنظيف اسم الملف للتحميل
    $downloadName = preg_replace('/[^a-zA-Z0-9._-]/', '_', $originalName);
    
    // إعداد headers للتحميل
    header('Content-Type: ' . $mimeType);
    header('Content-Disposition: attachment; filename="' . $downloadName . '"');
    header('Content-Length: ' . filesize($filePath));
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // قراءة وإرسال الملف
    readfile($filePath);
    exit();
}

/**
 * التحقق من صلاحية الوصول للملف
 */
function canAccessFile($fileData, $userId, $isAdmin) {
    // المدراء يمكنهم الوصول لجميع الملفات
    if ($isAdmin) {
        return true;
    }
    
    // صاحب الملف يمكنه الوصول إليه
    if ($fileData['user_id'] == $userId) {
        return true;
    }
    
    // ملفات عامة يمكن للجميع الوصول إليها
    if ($fileData['file_type'] === 'public') {
        return true;
    }
    
    // ملفات التداول يمكن للأطراف المشاركة الوصول إليها
    if ($fileData['file_type'] === 'trade' && $fileData['related_id']) {
        // TODO: التحقق من أن المستخدم طرف في التداول
        // يمكن إضافة استعلام للتحقق من جدول trades
    }
    
    return false;
}

try {
    // التحقق من المصادقة
    $auth = requireAuth();
    $userId = $auth->getCurrentUserId();
    $isAdmin = $auth->isAdmin();
    
    if (!$userId) {
        sendErrorResponse('المصادقة مطلوبة', 401);
    }
    
    // الحصول على معرف الملف
    $fileId = $_GET['id'] ?? '';
    
    if (empty($fileId) || !is_numeric($fileId)) {
        sendErrorResponse('معرف الملف مطلوب ويجب أن يكون رقماً');
    }
    
    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // البحث عن الملف
    $stmt = $connection->prepare("
        SELECT id, user_id, original_name, safe_filename, file_path, 
               file_size, file_type, mime_type, description, related_id,
               created_at, download_count
        FROM uploaded_files 
        WHERE id = ?
    ");
    
    $stmt->execute([$fileId]);
    $fileData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$fileData) {
        sendErrorResponse('الملف غير موجود', 404);
    }
    
    // التحقق من صلاحية الوصول
    if (!canAccessFile($fileData, $userId, $isAdmin)) {
        sendErrorResponse('ليس لديك صلاحية للوصول لهذا الملف', 403);
    }
    
    // تحديث عداد التحميل
    try {
        $updateStmt = $connection->prepare("
            UPDATE uploaded_files 
            SET download_count = download_count + 1, last_downloaded = NOW() 
            WHERE id = ?
        ");
        $updateStmt->execute([$fileId]);
    } catch (Exception $updateError) {
        // تجاهل أخطاء تحديث العداد
        error_log('Download count update error: ' . $updateError->getMessage());
    }
    
    // تسجيل النشاط
    try {
        $checkTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
        $checkTable->execute();
        
        if ($checkTable->rowCount() > 0) {
            $checkColumns = $connection->prepare("SHOW COLUMNS FROM activity_logs LIKE 'entity_type'");
            $checkColumns->execute();
            
            if ($checkColumns->rowCount() > 0) {
                $logStmt = $connection->prepare("
                    INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data)
                    VALUES (?, 'file_downloaded', 'file', ?, ?, ?, ?)
                ");
                
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                $downloadData = json_encode([
                    'download_time' => date('Y-m-d H:i:s'),
                    'original_name' => $fileData['original_name'],
                    'file_size' => $fileData['file_size'],
                    'file_type' => $fileData['file_type'],
                    'file_owner_id' => $fileData['user_id']
                ]);
                
                $logStmt->execute([$userId, $fileId, $ipAddress, $userAgent, $downloadData]);
            }
        }
    } catch (Exception $logError) {
        // تجاهل أخطاء تسجيل النشاط
        error_log('Activity log error in download: ' . $logError->getMessage());
    }
    
    // إرسال الملف
    sendFile($fileData['file_path'], $fileData['original_name'], $fileData['mime_type']);
    
} catch (Exception $e) {
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    error_log('Database error in download.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}
?>
