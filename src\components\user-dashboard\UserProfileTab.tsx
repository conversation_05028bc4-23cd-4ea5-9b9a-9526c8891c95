'use client';

import { useState } from 'react';
import {
  User,
  Edit,
  Camera,
  Shield,
  Star,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Globe,
  Save,
  X
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';

export default function UserProfileTab() {
  const { user } = useAuth();
  const { formatDate } = useUserDashboardTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    fullName: user?.fullName || '',
    email: user?.email || '',
    phone: user?.phone || '',
    bio: user?.bio || '',
    country: user?.countryCode || '',
    telegramUsername: user?.telegramUsername || '',
    whatsappNumber: user?.whatsappNumber || ''
  });

  const handleSave = () => {
    // هنا سيتم حفظ البيانات
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData({
      fullName: user?.fullName || '',
      email: user?.email || '',
      phone: user?.phone || '',
      bio: user?.bio || '',
      country: user?.countryCode || '',
      telegramUsername: user?.telegramUsername || '',
      whatsappNumber: user?.whatsappNumber || ''
    });
    setIsEditing(false);
  };

  return (
    <div className="space-y-6">
      {/* رأس القسم */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <User className="w-6 h-6 ml-3 text-blue-600" />
            الملف الشخصي
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            إدارة معلوماتك الشخصية وإعدادات الحساب
          </p>
        </div>
        {!isEditing ? (
          <button
            onClick={() => setIsEditing(true)}
            className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <Edit className="w-4 h-4" />
            <span>تعديل</span>
          </button>
        ) : (
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <button
              onClick={handleSave}
              className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
            >
              <Save className="w-4 h-4" />
              <span>حفظ</span>
            </button>
            <button
              onClick={handleCancel}
              className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
            >
              <X className="w-4 h-4" />
              <span>إلغاء</span>
            </button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* معلومات أساسية */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">المعلومات الأساسية</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  الاسم الكامل
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    value={formData.fullName}
                    onChange={(e) => setFormData({...formData, fullName: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                ) : (
                  <p className="text-gray-900 dark:text-white">{user?.fullName || 'غير محدد'}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  البريد الإلكتروني
                </label>
                {isEditing ? (
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                ) : (
                  <p className="text-gray-900 dark:text-white">{user?.email || 'غير محدد'}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  رقم الهاتف
                </label>
                {isEditing ? (
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                ) : (
                  <p className="text-gray-900 dark:text-white">{user?.phone || 'غير محدد'}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  البلد
                </label>
                {isEditing ? (
                  <select
                    value={formData.country}
                    onChange={(e) => setFormData({...formData, country: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">اختر البلد</option>
                    <option value="SA">السعودية</option>
                    <option value="AE">الإمارات</option>
                    <option value="KW">الكويت</option>
                    <option value="QA">قطر</option>
                    <option value="BH">البحرين</option>
                    <option value="OM">عمان</option>
                    <option value="JO">الأردن</option>
                    <option value="EG">مصر</option>
                  </select>
                ) : (
                  <p className="text-gray-900 dark:text-white">{user?.countryCode || 'غير محدد'}</p>
                )}
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                نبذة شخصية
              </label>
              {isEditing ? (
                <textarea
                  value={formData.bio}
                  onChange={(e) => setFormData({...formData, bio: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="اكتب نبذة مختصرة عنك..."
                />
              ) : (
                <p className="text-gray-900 dark:text-white">{user?.bio || 'لم يتم إضافة نبذة شخصية'}</p>
              )}
            </div>
          </div>

          {/* معلومات التواصل */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">معلومات التواصل</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  تيليجرام
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    value={formData.telegramUsername}
                    onChange={(e) => setFormData({...formData, telegramUsername: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="@username"
                  />
                ) : (
                  <p className="text-gray-900 dark:text-white">{user?.telegramUsername || 'غير محدد'}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  واتساب
                </label>
                {isEditing ? (
                  <input
                    type="tel"
                    value={formData.whatsappNumber}
                    onChange={(e) => setFormData({...formData, whatsappNumber: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="+966xxxxxxxxx"
                  />
                ) : (
                  <p className="text-gray-900 dark:text-white">{user?.whatsappNumber || 'غير محدد'}</p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* الشريط الجانبي */}
        <div className="space-y-6">
          {/* صورة الملف الشخصي */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">الصورة الشخصية</h3>
            <div className="text-center">
              <div className="relative inline-block">
                <div className="w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  {user?.profileImage ? (
                    <img
                      src={user.profileImage}
                      alt="Profile"
                      className="w-24 h-24 rounded-full object-cover"
                    />
                  ) : (
                    <User className="w-12 h-12 text-gray-400" />
                  )}
                </div>
                <button className="absolute bottom-0 right-0 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full transition-colors">
                  <Camera className="w-4 h-4" />
                </button>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                اضغط لتغيير الصورة
              </p>
            </div>
          </div>

          {/* إحصائيات الحساب */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">إحصائيات الحساب</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">التقييم</span>
                <div className="flex items-center">
                  <Star className="w-4 h-4 text-yellow-400 ml-1" />
                  <span className="font-semibold text-gray-900 dark:text-white">
                    {user?.rating?.toFixed(1) || '0.0'}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">إجمالي الصفقات</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {user?.totalTrades || 0}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">الصفقات المكتملة</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {user?.completedTrades || 0}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">تاريخ التسجيل</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {user?.createdAt ? formatDate(new Date(user.createdAt), {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                  }) : 'غير محدد'}
                </span>
              </div>
            </div>
          </div>

          {/* حالة التحقق */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">حالة التحقق</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">البريد الإلكتروني</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  user?.emailVerifiedAt
                    ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                    : 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                }`}>
                  {user?.emailVerifiedAt ? 'مُحقق' : 'غير مُحقق'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">الهوية</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  user?.isVerified
                    ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                    : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300'
                }`}>
                  {user?.isVerified ? 'مُحقق' : 'قيد المراجعة'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
