'use client';

import { useState, useEffect, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import {
  ArrowLeft,
  Send,
  Paperclip,
  Image,
  Phone,
  Video,
  MoreVertical,
  Shield,
  Star,
  Circle,
  Check,
  CheckCheck,
  Clock,
  AlertTriangle,
  Flag,
  Block,
  Archive,
  Loader2
} from 'lucide-react';

interface User {
  id: string;
  username: string;
  is_verified: boolean;
  is_online: boolean;
  last_seen: string;
  rating: number;
}

interface Message {
  id: string;
  sender_id: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'system';
  timestamp: string;
  status: 'sending' | 'sent' | 'delivered' | 'read';
}

export default function ChatPage() {
  const params = useParams();
  const router = useRouter();
  const { t } = useTranslation('chat');
  const { t: commonT } = useTranslation('common');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const [user, setUser] = useState<User | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [typing, setTyping] = useState(false);

  useEffect(() => {
    fetchUser();
    fetchMessages();
    // محاكاة الاتصال المباشر
    const interval = setInterval(fetchMessages, 3000);
    return () => clearInterval(interval);
  }, [params.id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchUser = async () => {
    try {
      // محاكاة جلب بيانات المستخدم
      setUser({
        id: params.id as string,
        username: 'ahmed_trader',
        is_verified: true,
        is_online: true,
        last_seen: new Date().toISOString(),
        rating: 4.8
      });
    } catch (error) {
      console.error('Error fetching user:', error);
    }
  };

  const fetchMessages = async () => {
    try {
      // محاكاة جلب الرسائل
      if (messages.length === 0) {
        setMessages([
          {
            id: '1',
            sender_id: params.id as string,
            content: 'مرحباً! أنا مهتم بعرضك للتداول',
            type: 'text',
            timestamp: new Date(Date.now() - 300000).toISOString(),
            status: 'read'
          },
          {
            id: '2',
            sender_id: 'current_user',
            content: 'أهلاً وسهلاً! أنا متاح للتداول الآن',
            type: 'text',
            timestamp: new Date(Date.now() - 240000).toISOString(),
            status: 'read'
          },
          {
            id: '3',
            sender_id: params.id as string,
            content: 'ممتاز، هل يمكننا بدء التداول؟',
            type: 'text',
            timestamp: new Date(Date.now() - 180000).toISOString(),
            status: 'read'
          }
        ]);
      }
      setLoading(false);
    } catch (error) {
      console.error('Error fetching messages:', error);
      setLoading(false);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || sending) return;

    const messageToSend = newMessage.trim();
    setNewMessage('');
    setSending(true);

    const tempMessage: Message = {
      id: Date.now().toString(),
      sender_id: 'current_user',
      content: messageToSend,
      type: 'text',
      timestamp: new Date().toISOString(),
      status: 'sending'
    };

    setMessages(prev => [...prev, tempMessage]);

    try {
      // محاكاة إرسال الرسالة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setMessages(prev => 
        prev.map(msg => 
          msg.id === tempMessage.id 
            ? { ...msg, status: 'sent' as const }
            : msg
        )
      );

      // محاكاة رد تلقائي
      setTimeout(() => {
        const autoReply: Message = {
          id: (Date.now() + 1).toString(),
          sender_id: params.id as string,
          content: 'شكراً لك! سأراجع طلبك وأرد عليك قريباً',
          type: 'text',
          timestamp: new Date().toISOString(),
          status: 'sent'
        };
        setMessages(prev => [...prev, autoReply]);
      }, 2000);

    } catch (error) {
      console.error('Error sending message:', error);
      setMessages(prev => 
        prev.map(msg => 
          msg.id === tempMessage.id 
            ? { ...msg, status: 'sent' as const }
            : msg
        )
      );
    } finally {
      setSending(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const getMessageStatus = (status: Message['status']) => {
    switch (status) {
      case 'sending':
        return <Clock className="w-3 h-3 text-gray-400" />;
      case 'sent':
        return <Check className="w-3 h-3 text-gray-400" />;
      case 'delivered':
        return <CheckCheck className="w-3 h-3 text-gray-400" />;
      case 'read':
        return <CheckCheck className="w-3 h-3 text-blue-500" />;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600 dark:text-gray-400">{commonT('loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <button
              onClick={() => router.back()}
              className="mr-3 p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            
            {user && (
              <div className="flex items-center">
                <div className="relative">
                  <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
                    {user.username.charAt(0).toUpperCase()}
                  </div>
                  {user.is_online && (
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
                  )}
                </div>
                
                <div className="ml-3">
                  <div className="flex items-center">
                    <h2 className="font-medium text-gray-900 dark:text-white">
                      {user.username}
                    </h2>
                    {user.is_verified && (
                      <Shield className="w-4 h-4 text-green-500 ml-1" />
                    )}
                    <div className="flex items-center ml-2">
                      <Star className="w-3 h-3 text-yellow-500 fill-current" />
                      <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
                        {user.rating}
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {user.is_online ? (
                      <span className="flex items-center">
                        <Circle className="w-2 h-2 text-green-500 fill-current mr-1" />
                        {t('status.online')}
                      </span>
                    ) : (
                      t('status.lastSeen') + ' ' + new Date(user.last_seen).toLocaleTimeString()
                    )}
                    {typing && (
                      <span className="text-blue-500 ml-2">{t('status.typing')}</span>
                    )}
                  </p>
                </div>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-2 space-x-reverse">
            <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
              <Phone className="w-5 h-5" />
            </button>
            <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
              <Video className="w-5 h-5" />
            </button>
            <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
              <MoreVertical className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* تحذير الأمان */}
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border-b border-yellow-200 dark:border-yellow-800 px-4 py-2">
        <div className="flex items-center text-sm text-yellow-800 dark:text-yellow-200">
          <Shield className="w-4 h-4 mr-2 flex-shrink-0" />
          <span>{t('security.chatMonitored')} - {t('security.endToEnd')}</span>
        </div>
      </div>

      {/* منطقة الرسائل */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender_id === 'current_user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.sender_id === 'current_user'
                  ? 'bg-blue-500 text-white'
                  : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-600'
              }`}
            >
              <p className="text-sm">{message.content}</p>
              <div className={`flex items-center justify-end mt-1 space-x-1 space-x-reverse ${
                message.sender_id === 'current_user' ? 'text-blue-100' : 'text-gray-400'
              }`}>
                <span className="text-xs">
                  {new Date(message.timestamp).toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </span>
                {message.sender_id === 'current_user' && getMessageStatus(message.status)}
              </div>
            </div>
          </div>
        ))}
        
        {typing && (
          <div className="flex justify-start">
            <div className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg px-4 py-2">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* منطقة الإدخال */}
      <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-end space-x-2 space-x-reverse">
          <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
            <Paperclip className="w-5 h-5" />
          </button>
          
          <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
            <Image className="w-5 h-5" />
          </button>
          
          <div className="flex-1">
            <textarea
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={t('messages.placeholder')}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
              rows={1}
              style={{ minHeight: '40px', maxHeight: '120px' }}
            />
          </div>
          
          <button
            onClick={sendMessage}
            disabled={!newMessage.trim() || sending}
            className="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {sending ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
          </button>
        </div>
      </div>

      {/* أزرار الإجراءات السريعة */}
      <div className="bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-3">
        <div className="flex justify-center space-x-4 space-x-reverse">
          <button
            onClick={() => router.push(`/trade/start/${user?.id}`)}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"
          >
            {t('actions.startTrade')}
          </button>
          
          <button
            onClick={() => router.push(`/offers`)}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-sm"
          >
            {t('actions.viewOffer')}
          </button>
          
          <button className="px-4 py-2 text-red-600 border border-red-300 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors text-sm">
            <Flag className="w-4 h-4 inline mr-1" />
            {t('actions.reportUser')}
          </button>
        </div>
      </div>
    </div>
  );
}
