<?php
/**
 * API لفحص الصفقات المعلقة لفترة طويلة
 * API for checking long pending trades
 */

require_once '../includes/cors.php';
require_once '../config/database.php';

try {
    // التحقق من صلاحيات الإدارة
    checkAdminPermissions();
    
    $database = new Database();
    $db = $database->getConnection();
    
    // الحصول على عتبة الوقت من المعاملات (افتراضي 30 دقيقة)
    $thresholdMinutes = $_GET['threshold'] ?? 30;
    
    // البحث عن الصفقات المعلقة لفترة طويلة
    $query = "SELECT 
                t.id,
                t.offer_id,
                t.seller_id,
                t.buyer_id,
                t.amount,
                t.status,
                t.created_at,
                t.last_activity,
                TIMESTAMPDIFF(MINUTE, t.last_activity, NOW()) as minutes_pending,
                u1.username as seller_username,
                u2.username as buyer_username,
                o.currency,
                o.payment_methods
              FROM trades t
              LEFT JOIN users u1 ON t.seller_id = u1.id
              LEFT JOIN users u2 ON t.buyer_id = u2.id
              LEFT JOIN offers o ON t.offer_id = o.id
              WHERE t.status IN ('pending', 'payment_sent', 'in_progress')
              AND TIMESTAMPDIFF(MINUTE, t.last_activity, NOW()) > ?
              ORDER BY minutes_pending DESC
              LIMIT 50";
    
    $stmt = $db->prepare($query);
    $stmt->execute([$thresholdMinutes]);
    $longPendingTrades = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات إضافية
    $statsQuery = "SELECT 
                     COUNT(*) as total_pending,
                     AVG(TIMESTAMPDIFF(MINUTE, last_activity, NOW())) as avg_pending_time,
                     MAX(TIMESTAMPDIFF(MINUTE, last_activity, NOW())) as max_pending_time,
                     COUNT(CASE WHEN TIMESTAMPDIFF(MINUTE, last_activity, NOW()) > ? THEN 1 END) as long_pending_count
                   FROM trades 
                   WHERE status IN ('pending', 'payment_sent', 'in_progress')";
    
    $statsStmt = $db->prepare($statsQuery);
    $statsStmt->execute([$thresholdMinutes]);
    $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);
    
    // تحليل الأسباب المحتملة للتأخير
    $reasonsQuery = "SELECT 
                       status,
                       COUNT(*) as count,
                       AVG(TIMESTAMPDIFF(MINUTE, last_activity, NOW())) as avg_time
                     FROM trades 
                     WHERE status IN ('pending', 'payment_sent', 'in_progress')
                     AND TIMESTAMPDIFF(MINUTE, last_activity, NOW()) > ?
                     GROUP BY status";
    
    $reasonsStmt = $db->prepare($reasonsQuery);
    $reasonsStmt->execute([$thresholdMinutes]);
    $delayReasons = $reasonsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تسجيل النشاط
    logActivity('pending_trades_check', [
        'threshold_minutes' => $thresholdMinutes,
        'long_pending_count' => count($longPendingTrades),
        'total_pending' => $stats['total_pending']
    ]);
    
    sendSuccess([
        'longPendingTrades' => $longPendingTrades,
        'thresholdMinutes' => (int)$thresholdMinutes,
        'stats' => [
            'totalPending' => (int)$stats['total_pending'],
            'longPendingCount' => count($longPendingTrades),
            'avgPendingTime' => round($stats['avg_pending_time'], 1),
            'maxPendingTime' => (int)$stats['max_pending_time']
        ],
        'delayReasons' => $delayReasons,
        'timestamp' => date('c')
    ]);
    
} catch (Exception $e) {
    error_log("Error in pending-trades.php: " . $e->getMessage());
    sendError('فشل في جلب الصفقات المعلقة', 500, [
        'error' => $e->getMessage(),
        'file' => __FILE__
    ]);
}
?>
