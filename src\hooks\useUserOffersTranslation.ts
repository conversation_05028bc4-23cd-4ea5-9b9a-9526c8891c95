import { useState, useEffect } from 'react';

interface UserOffersTranslations {
  title: string;
  subtitle: string;
  tabs: {
    active: string;
    inactive: string;
    expired: string;
    all: string;
  };
  stats: {
    total_offers: string;
    active_offers: string;
    completed_trades: string;
    success_rate: string;
    monthly_usage: string;
    remaining_offers: string;
    plan_limit: string;
    next_reset: string;
  };
  offer: {
    type: string;
    amount: string;
    price: string;
    currency: string;
    stablecoin: string;
    payment_methods: string;
    status: string;
    created: string;
    expires: string;
    views: string;
    trades: string;
  };
  status: {
    active: string;
    inactive: string;
    expired: string;
    completed: string;
    cancelled: string;
    pending: string;
  };
  actions: {
    create_offer: string;
    edit_offer: string;
    delete_offer: string;
    activate_offer: string;
    deactivate_offer: string;
    view_details: string;
    view_trades: string;
    duplicate_offer: string;
    share_offer: string;
  };
  filters: {
    filter_by: string;
    offer_type: string;
    currency: string;
    status: string;
    date_range: string;
    clear_filters: string;
  };
  empty_states: {
    no_offers: string;
    no_active_offers: string;
    no_expired_offers: string;
    create_first_offer: string;
  };
  messages: {
    offer_created: string;
    offer_updated: string;
    offer_deleted: string;
    offer_activated: string;
    offer_deactivated: string;
    monthly_limit_reached: string;
    upgrade_required: string;
  };
  limits: {
    free_offers_used: string;
    subscription_required: string;
    upgrade_plan: string;
    view_plans: string;
    monthly_offers_used: string;
    monthly_offers_remaining: string;
  };
  performance: {
    total_views: string;
    conversion_rate: string;
    average_completion_time: string;
    rating: string;
    response_time: string;
    last_30_days: string;
  };
}

export const useUserOffersTranslation = () => {
  const [translations, setTranslations] = useState<UserOffersTranslations | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadTranslations = async () => {
      try {
        // استخدام اللغة العربية كافتراضي
        const lang = 'ar';
        const response = await fetch(`/locales/${lang}/user-offers.json`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        setTranslations(data);
      } catch (error) {
        console.error('Error loading user offers translations:', error);
        // Fallback to English if Arabic fails
        try {
          const response = await fetch('/locales/en/user-offers.json');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const data = await response.json();
          setTranslations(data);
        } catch (fallbackError) {
          console.error('Error loading fallback translations:', fallbackError);
          // استخدام ترجمات افتراضية
          setTranslations({
            title: "عروضي",
            subtitle: "إدارة عروضك ومراقبة حدود النشر الشهرية",
            tabs: { active: "النشطة", inactive: "غير النشطة", expired: "المنتهية", all: "الكل" },
            stats: { total_offers: "إجمالي العروض", active_offers: "العروض النشطة", completed_trades: "الصفقات المكتملة", success_rate: "معدل النجاح", monthly_usage: "الاستخدام الشهري", remaining_offers: "العروض المتبقية", plan_limit: "حد الخطة", next_reset: "التجديد التالي" },
            offer: { type: "النوع", amount: "المبلغ", price: "السعر", currency: "العملة", stablecoin: "العملة المستقرة", payment_methods: "طرق الدفع", status: "الحالة", created: "تاريخ الإنشاء", expires: "تاريخ الانتهاء", views: "المشاهدات", trades: "الصفقات" },
            status: { active: "نشط", inactive: "غير نشط", expired: "منتهي", completed: "مكتمل", cancelled: "ملغي", pending: "في الانتظار" },
            actions: { create_offer: "إنشاء عرض جديد", edit_offer: "تعديل العرض", delete_offer: "حذف العرض", activate_offer: "تفعيل", deactivate_offer: "إلغاء التفعيل", view_details: "عرض التفاصيل", view_trades: "عرض الصفقات", duplicate_offer: "نسخ", share_offer: "مشاركة" },
            filters: { filter_by: "تصفية حسب", offer_type: "نوع العرض", currency: "العملة", status: "الحالة", date_range: "نطاق التاريخ", clear_filters: "مسح المرشحات" },
            empty_states: { no_offers: "لا توجد عروض", no_active_offers: "لا توجد عروض نشطة", no_expired_offers: "لا توجد عروض منتهية", create_first_offer: "أنشئ عرضك الأول لبدء التداول" },
            messages: { offer_created: "تم إنشاء العرض بنجاح", offer_updated: "تم تحديث العرض بنجاح", offer_deleted: "تم حذف العرض بنجاح", offer_activated: "تم تفعيل العرض بنجاح", offer_deactivated: "تم إلغاء تفعيل العرض بنجاح", monthly_limit_reached: "تم الوصول للحد الشهري للعروض", upgrade_required: "قم بترقية خطتك لإنشاء المزيد من العروض" },
            limits: { free_offers_used: "العروض المجانية المستخدمة هذا الشهر", subscription_required: "مطلوب اشتراك للمزيد من العروض", upgrade_plan: "ترقية الخطة", view_plans: "عرض الخطط", monthly_offers_used: "العروض المستخدمة شهرياً", monthly_offers_remaining: "العروض المتبقية" },
            performance: { total_views: "إجمالي المشاهدات", conversion_rate: "معدل التحويل", average_completion_time: "متوسط وقت الإكمال", rating: "التقييم", response_time: "وقت الاستجابة", last_30_days: "آخر 30 يوم" }
          });
        }
      } finally {
        setLoading(false);
      }
    };

    loadTranslations();
  }, []);

  const t = (key: string): string => {
    if (!translations || loading) return key;
    
    const keys = key.split('.');
    let value: any = translations;
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return key; // Return key if translation not found
      }
    }
    
    return typeof value === 'string' ? value : key;
  };

  return t;
};
