/**
 * تكوين العقود الذكية المحسنة
 * Enhanced Smart Contracts Configuration
 */

// استيراد ABIs
import CoreEscrowABI from './abis/CoreEscrow.json';
import ReputationManagerABI from './abis/ReputationManager.json';
import EscrowIntegratorABI from './abis/EscrowIntegrator.json';

// أنواع البيانات
export interface ContractConfig {
  address: string;
  abi: any[];
  name: string;
  description: string;
}

export interface NetworkConfig {
  chainId: number;
  name: string;
  symbol: string;
  rpcUrl: string;
  explorerUrl: string;
  isTestnet: boolean;
  contracts: {
    [key: string]: ContractConfig;
  };
}

// تكوين الشبكات
export const NETWORKS: { [key: number]: NetworkConfig } = {
  // BSC Testnet
  97: {
    chainId: 97,
    name: 'BSC Testnet',
    symbol: 'tBNB',
    rpcUrl: 'https://data-seed-prebsc-1-s1.binance.org:8545/',
    explorerUrl: 'https://testnet.bscscan.com/',
    isTestnet: true,
    contracts: {
      CoreEscrow: {
        address: '0xAb25F1fFDB7fC4EA75abDcc662CeD61E4b20F7f2',
        abi: CoreEscrowABI,
        name: 'Core Escrow',
        description: 'العقد الرئيسي للضمان والصفقات'
      },
      ReputationManager: {
        address: '0x56A6914523413b0e7344f57466A6239fCC97b913',
        abi: ReputationManagerABI,
        name: 'Reputation Manager',
        description: 'نظام إدارة السمعة والتقييمات'
      },
      OracleManager: {
        address: '0xB70715392F62628Ccd1258AAF691384bE8C023b6',
        abi: [], // سيتم إضافته لاحقاً
        name: 'Oracle Manager',
        description: 'نظام إدارة الأسعار والبيانات الخارجية'
      },
      AdminManager: {
        address: '0x5A9FD8082ADA38678721D59AAB4d4F76883c5575',
        abi: [], // سيتم إضافته لاحقاً
        name: 'Admin Manager',
        description: 'نظام الإدارة والحكم'
      },
      EscrowIntegrator: {
        address: '0xc0Ec65ffA95aca485e4C2e9b35B20C85FA36F0e0',
        abi: EscrowIntegratorABI,
        name: 'Escrow Integrator',
        description: 'نظام التكامل بين جميع العقود'
      }
    }
  },
  
  // BSC Mainnet
  56: {
    chainId: 56,
    name: 'BSC Mainnet',
    symbol: 'BNB',
    rpcUrl: 'https://bsc-dataseed1.binance.org/',
    explorerUrl: 'https://bscscan.com/',
    isTestnet: false,
    contracts: {
      CoreEscrow: {
        address: '', // سيتم تحديثه عند النشر
        abi: CoreEscrowABI,
        name: 'Core Escrow',
        description: 'العقد الرئيسي للضمان والصفقات'
      },
      ReputationManager: {
        address: '', // سيتم تحديثه عند النشر
        abi: ReputationManagerABI,
        name: 'Reputation Manager',
        description: 'نظام إدارة السمعة والتقييمات'
      },
      OracleManager: {
        address: '', // سيتم تحديثه عند النشر
        abi: [],
        name: 'Oracle Manager',
        description: 'نظام إدارة الأسعار والبيانات الخارجية'
      },
      AdminManager: {
        address: '', // سيتم تحديثه عند النشر
        abi: [],
        name: 'Admin Manager',
        description: 'نظام الإدارة والحكم'
      },
      EscrowIntegrator: {
        address: '', // سيتم تحديثه عند النشر
        abi: EscrowIntegratorABI,
        name: 'Escrow Integrator',
        description: 'نظام التكامل بين جميع العقود'
      }
    }
  }
};

// العملات المدعومة
export const SUPPORTED_TOKENS = {
  // BSC Testnet
  97: [
    {
      address: '******************************************',
      symbol: 'USDT',
      name: 'Tether USD',
      decimals: 18,
      isStablecoin: true,
      icon: '/tokens/usdt.svg'
    },
    {
      address: '******************************************',
      symbol: 'USDC',
      name: 'USD Coin',
      decimals: 18,
      isStablecoin: true,
      icon: '/tokens/usdc.svg'
    },
    {
      address: '******************************************',
      symbol: 'BUSD',
      name: 'Binance USD',
      decimals: 18,
      isStablecoin: true,
      icon: '/tokens/busd.svg'
    },
    {
      address: '******************************************',
      symbol: 'DAI',
      name: 'Dai Stablecoin',
      decimals: 18,
      isStablecoin: true,
      icon: '/tokens/dai.svg'
    }
  ],
  
  // BSC Mainnet
  56: [
    {
      address: '******************************************',
      symbol: 'USDT',
      name: 'Tether USD',
      decimals: 18,
      isStablecoin: true,
      icon: '/tokens/usdt.svg'
    },
    {
      address: '******************************************',
      symbol: 'USDC',
      name: 'USD Coin',
      decimals: 18,
      isStablecoin: true,
      icon: '/tokens/usdc.svg'
    },
    {
      address: '******************************************',
      symbol: 'BUSD',
      name: 'Binance USD',
      decimals: 18,
      isStablecoin: true,
      icon: '/tokens/busd.svg'
    }
  ]
};

// العملات الورقية المدعومة
export const SUPPORTED_CURRENCIES = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'SAR', name: 'Saudi Riyal', symbol: 'ر.س' },
  { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' }
];

// إعدادات افتراضية
export const DEFAULT_SETTINGS = {
  // الشبكة الافتراضية (BSC Testnet للتطوير)
  defaultChainId: 97,
  
  // إعدادات الصفقات
  minTradeAmount: '1',
  maxTradeAmount: '1000000',
  defaultCurrency: 'SAR',
  
  // إعدادات الرسوم
  platformFeeRate: 0.005, // 0.5%
  
  // إعدادات الوقت
  tradeTimeLimit: 24 * 60 * 60, // 24 ساعة
  disputeTimeLimit: 7 * 24 * 60 * 60, // 7 أيام
  
  // إعدادات السمعة
  initialReputationScore: 100,
  minReputationForTrade: 50,
  
  // إعدادات التكامل
  autoReputationUpdate: true,
  priceValidationEnabled: true,
  maxPriceDeviation: 0.05 // 5%
};

// دوال مساعدة
export const getNetworkConfig = (chainId: number): NetworkConfig | null => {
  return NETWORKS[chainId] || null;
};

export const getContractConfig = (chainId: number, contractName: string): ContractConfig | null => {
  const network = getNetworkConfig(chainId);
  return network?.contracts[contractName] || null;
};

export const getSupportedTokens = (chainId: number) => {
  return SUPPORTED_TOKENS[chainId as keyof typeof SUPPORTED_TOKENS] || [];
};

export const getTokenByAddress = (chainId: number, address: string) => {
  const tokens = getSupportedTokens(chainId);
  return tokens.find(token => token.address.toLowerCase() === address.toLowerCase());
};

export const getTokenBySymbol = (chainId: number, symbol: string) => {
  const tokens = getSupportedTokens(chainId);
  return tokens.find(token => token.symbol.toLowerCase() === symbol.toLowerCase());
};

export const isNetworkSupported = (chainId: number): boolean => {
  return chainId in NETWORKS;
};

export const isTestnet = (chainId: number): boolean => {
  const network = getNetworkConfig(chainId);
  return network?.isTestnet || false;
};

// متغيرات البيئة
export const ENV_CONFIG = {
  API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost/api',
  DEFAULT_CHAIN_ID: parseInt(process.env.NEXT_PUBLIC_CHAIN_ID || '97'),
  RPC_URL: process.env.NEXT_PUBLIC_RPC_URL || 'https://data-seed-prebsc-1-s1.binance.org:8545/',
  EXPLORER_URL: process.env.NEXT_PUBLIC_EXPLORER_URL || 'https://testnet.bscscan.com/',
  
  // عناوين العقود من متغيرات البيئة
  CORE_ESCROW_ADDRESS: process.env.NEXT_PUBLIC_CORE_ESCROW_ADDRESS || '0xAb25F1fFDB7fC4EA75abDcc662CeD61E4b20F7f2',
  REPUTATION_MANAGER_ADDRESS: process.env.NEXT_PUBLIC_REPUTATION_MANAGER_ADDRESS || '0x56A6914523413b0e7344f57466A6239fCC97b913',
  ORACLE_MANAGER_ADDRESS: process.env.NEXT_PUBLIC_ORACLE_MANAGER_ADDRESS || '0xB70715392F62628Ccd1258AAF691384bE8C023b6',
  ADMIN_MANAGER_ADDRESS: process.env.NEXT_PUBLIC_ADMIN_MANAGER_ADDRESS || '0x5A9FD8082ADA38678721D59AAB4d4F76883c5575',
  ESCROW_INTEGRATOR_ADDRESS: process.env.NEXT_PUBLIC_ESCROW_INTEGRATOR_ADDRESS || '0xc0Ec65ffA95aca485e4C2e9b35B20C85FA36F0e0'
};

// تحديث عناوين العقود من متغيرات البيئة
if (ENV_CONFIG.DEFAULT_CHAIN_ID in NETWORKS) {
  const network = NETWORKS[ENV_CONFIG.DEFAULT_CHAIN_ID];
  
  if (ENV_CONFIG.CORE_ESCROW_ADDRESS) {
    network.contracts.CoreEscrow.address = ENV_CONFIG.CORE_ESCROW_ADDRESS;
  }
  if (ENV_CONFIG.REPUTATION_MANAGER_ADDRESS) {
    network.contracts.ReputationManager.address = ENV_CONFIG.REPUTATION_MANAGER_ADDRESS;
  }
  if (ENV_CONFIG.ORACLE_MANAGER_ADDRESS) {
    network.contracts.OracleManager.address = ENV_CONFIG.ORACLE_MANAGER_ADDRESS;
  }
  if (ENV_CONFIG.ADMIN_MANAGER_ADDRESS) {
    network.contracts.AdminManager.address = ENV_CONFIG.ADMIN_MANAGER_ADDRESS;
  }
  if (ENV_CONFIG.ESCROW_INTEGRATOR_ADDRESS) {
    network.contracts.EscrowIntegrator.address = ENV_CONFIG.ESCROW_INTEGRATOR_ADDRESS;
  }
}

export default {
  NETWORKS,
  SUPPORTED_TOKENS,
  SUPPORTED_CURRENCIES,
  DEFAULT_SETTINGS,
  ENV_CONFIG,
  getNetworkConfig,
  getContractConfig,
  getSupportedTokens,
  getTokenByAddress,
  getTokenBySymbol,
  isNetworkSupported,
  isTestnet
};
