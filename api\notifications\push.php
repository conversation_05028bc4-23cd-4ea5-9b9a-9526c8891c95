<?php
/**
 * API endpoint للإشعارات الفورية
 * Push Notifications API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";
require_once __DIR__ . "/../middleware/auth.php";

// التحقق من طريقة الطلب
validateRequestMethod(['GET', 'POST', 'PUT', 'DELETE']);

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/../../.env')) {
    $lines = file(__DIR__ . '/../../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

// مفاتيح VAPID للإشعارات
$vapidPublicKey = $_ENV['VAPID_PUBLIC_KEY'] ?? '';
$vapidPrivateKey = $_ENV['VAPID_PRIVATE_KEY'] ?? '';
$vapidSubject = $_ENV['VAPID_SUBJECT'] ?? 'mailto:<EMAIL>';

/**
 * إرسال إشعار push
 */
function sendPushNotification($subscription, $payload, $vapidKeys) {
    // تحويل المفاتيح من base64
    $publicKey = base64url_decode($vapidKeys['public']);
    $privateKey = base64url_decode($vapidKeys['private']);
    
    // إنشاء JWT للمصادقة
    $header = json_encode(['typ' => 'JWT', 'alg' => 'ES256']);
    $payload_jwt = json_encode([
        'aud' => $subscription['endpoint'],
        'exp' => time() + 12 * 60 * 60, // 12 ساعة
        'sub' => $vapidKeys['subject']
    ]);
    
    $headerEncoded = base64url_encode($header);
    $payloadEncoded = base64url_encode($payload_jwt);
    
    // إنشاء التوقيع (محاكاة - يحتاج مكتبة crypto فعلية)
    $signature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, $privateKey, true);
    $signatureEncoded = base64url_encode($signature);
    
    $jwt = $headerEncoded . '.' . $payloadEncoded . '.' . $signatureEncoded;
    
    // إعداد البيانات للإرسال
    $postData = $payload;
    
    // إعداد headers
    $headers = [
        'Content-Type: application/octet-stream',
        'Content-Length: ' . strlen($postData),
        'Authorization: vapid t=' . $jwt . ', k=' . $vapidKeys['public'],
        'TTL: 2419200' // 4 أسابيع
    ];
    
    // إرسال الطلب
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $subscription['endpoint']);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'success' => $httpCode >= 200 && $httpCode < 300,
        'httpCode' => $httpCode,
        'response' => $result
    ];
}

/**
 * تشفير base64url
 */
function base64url_encode($data) {
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

/**
 * فك تشفير base64url
 */
function base64url_decode($data) {
    return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
}

/**
 * إنشاء إشعار
 */
function createNotification($connection, $userId, $type, $title, $message, $data = null, $targetUserId = null) {
    $stmt = $connection->prepare("
        INSERT INTO notifications (
            user_id, target_user_id, type, title, message, data, 
            is_read, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, 0, NOW(), NOW())
    ");
    
    $stmt->execute([
        $userId,
        $targetUserId,
        $type,
        $title,
        $message,
        $data ? json_encode($data) : null
    ]);
    
    return $connection->lastInsertId();
}

try {
    // التحقق من المصادقة
    $auth = requireAuth();
    $userId = $auth->getCurrentUserId();
    $isAdmin = $auth->isAdmin();
    
    if (!$userId) {
        sendErrorResponse('المصادقة مطلوبة', 401);
    }
    
    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // جلب الإشعارات
        $page = max(1, (int)($_GET['page'] ?? 1));
        $limit = min(50, max(10, (int)($_GET['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        
        $unreadOnly = isset($_GET['unreadOnly']) && $_GET['unreadOnly'] === 'true';
        $type = $_GET['type'] ?? '';
        
        // بناء الاستعلام
        $whereConditions = ["(n.target_user_id = ? OR n.target_user_id IS NULL)"];
        $params = [$userId];
        
        if ($unreadOnly) {
            $whereConditions[] = "n.is_read = 0";
        }
        
        if (!empty($type)) {
            $whereConditions[] = "n.type = ?";
            $params[] = $type;
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // حساب العدد الإجمالي
        $countQuery = "
            SELECT COUNT(*) as total
            FROM notifications n
            WHERE $whereClause
        ";
        $countStmt = $connection->prepare($countQuery);
        $countStmt->execute($params);
        $totalNotifications = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // جلب الإشعارات
        $dataQuery = "
            SELECT n.*, u.username as sender_username
            FROM notifications n
            LEFT JOIN users u ON n.user_id = u.id
            WHERE $whereClause
            ORDER BY n.created_at DESC
            LIMIT ? OFFSET ?
        ";
        
        $dataParams = array_merge($params, [$limit, $offset]);
        $dataStmt = $connection->prepare($dataQuery);
        $dataStmt->execute($dataParams);
        $notifications = $dataStmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تنسيق البيانات
        $formattedNotifications = array_map(function($notification) {
            return [
                'id' => (int)$notification['id'],
                'userId' => $notification['user_id'] ? (int)$notification['user_id'] : null,
                'targetUserId' => $notification['target_user_id'] ? (int)$notification['target_user_id'] : null,
                'senderUsername' => $notification['sender_username'],
                'type' => $notification['type'],
                'title' => $notification['title'],
                'message' => $notification['message'],
                'data' => $notification['data'] ? json_decode($notification['data'], true) : null,
                'isRead' => (bool)$notification['is_read'],
                'createdAt' => $notification['created_at'],
                'updatedAt' => $notification['updated_at']
            ];
        }, $notifications);
        
        // حساب معلومات الترقيم
        $totalPages = ceil($totalNotifications / $limit);
        
        sendSuccessResponse([
            'notifications' => $formattedNotifications,
            'pagination' => [
                'currentPage' => $page,
                'totalPages' => $totalPages,
                'totalNotifications' => (int)$totalNotifications,
                'limit' => $limit,
                'hasNextPage' => $page < $totalPages,
                'hasPrevPage' => $page > 1
            ],
            'unreadCount' => (int)$totalNotifications
        ], 'تم جلب الإشعارات بنجاح');
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // إنشاء وإرسال إشعار جديد
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            sendErrorResponse('بيانات غير صحيحة');
        }
        
        $action = $input['action'] ?? '';
        
        if ($action === 'subscribe') {
            // تسجيل اشتراك push notification
            $subscription = $input['subscription'] ?? null;
            
            if (!$subscription || !isset($subscription['endpoint'])) {
                sendErrorResponse('بيانات الاشتراك مطلوبة');
            }
            
            // حفظ الاشتراك في قاعدة البيانات
            $stmt = $connection->prepare("
                INSERT INTO push_subscriptions (
                    user_id, endpoint, p256dh_key, auth_key, created_at
                ) VALUES (?, ?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE
                p256dh_key = VALUES(p256dh_key),
                auth_key = VALUES(auth_key),
                updated_at = NOW()
            ");
            
            $stmt->execute([
                $userId,
                $subscription['endpoint'],
                $subscription['keys']['p256dh'] ?? null,
                $subscription['keys']['auth'] ?? null
            ]);
            
            sendSuccessResponse([
                'subscribed' => true,
                'endpoint' => $subscription['endpoint']
            ], 'تم تسجيل الاشتراك بنجاح');
            
        } elseif ($action === 'send') {
            // إرسال إشعار (للمدراء فقط أو للمستخدم نفسه)
            $targetUserId = $input['targetUserId'] ?? null;
            $type = $input['type'] ?? 'general';
            $title = $input['title'] ?? '';
            $message = $input['message'] ?? '';
            $data = $input['data'] ?? null;
            $sendPush = $input['sendPush'] ?? true;
            
            if (empty($title) || empty($message)) {
                sendErrorResponse('العنوان والرسالة مطلوبان');
            }
            
            // التحقق من الصلاحية
            if ($targetUserId && $targetUserId != $userId && !$isAdmin) {
                sendErrorResponse('ليس لديك صلاحية لإرسال إشعارات للمستخدمين الآخرين', 403);
            }
            
            // إنشاء الإشعار في قاعدة البيانات
            $notificationId = createNotification(
                $connection, 
                $userId, 
                $type, 
                $title, 
                $message, 
                $data, 
                $targetUserId
            );
            
            $pushResults = [];
            
            if ($sendPush) {
                // جلب اشتراكات المستخدم المستهدف
                $subscriptionQuery = "
                    SELECT endpoint, p256dh_key, auth_key
                    FROM push_subscriptions
                    WHERE user_id = ? AND is_active = 1
                ";
                
                $targetId = $targetUserId ?? $userId;
                $subscriptionStmt = $connection->prepare($subscriptionQuery);
                $subscriptionStmt->execute([$targetId]);
                $subscriptions = $subscriptionStmt->fetchAll(PDO::FETCH_ASSOC);
                
                // إعداد payload الإشعار
                $pushPayload = json_encode([
                    'title' => $title,
                    'body' => $message,
                    'icon' => '/icons/icon-192x192.png',
                    'badge' => '/icons/badge-72x72.png',
                    'data' => array_merge($data ?? [], [
                        'notificationId' => $notificationId,
                        'type' => $type,
                        'url' => '/'
                    ])
                ]);
                
                // إرسال الإشعار لكل اشتراك
                $vapidKeys = [
                    'public' => $vapidPublicKey,
                    'private' => $vapidPrivateKey,
                    'subject' => $vapidSubject
                ];
                
                foreach ($subscriptions as $subscription) {
                    $subscriptionData = [
                        'endpoint' => $subscription['endpoint'],
                        'keys' => [
                            'p256dh' => $subscription['p256dh_key'],
                            'auth' => $subscription['auth_key']
                        ]
                    ];
                    
                    $result = sendPushNotification($subscriptionData, $pushPayload, $vapidKeys);
                    $pushResults[] = $result;
                    
                    // إزالة الاشتراكات المنتهية الصلاحية
                    if (!$result['success'] && in_array($result['httpCode'], [404, 410])) {
                        $deleteStmt = $connection->prepare("
                            UPDATE push_subscriptions 
                            SET is_active = 0 
                            WHERE endpoint = ?
                        ");
                        $deleteStmt->execute([$subscription['endpoint']]);
                    }
                }
            }
            
            sendSuccessResponse([
                'notificationId' => $notificationId,
                'pushSent' => $sendPush,
                'pushResults' => $pushResults,
                'successfulPushes' => count(array_filter($pushResults, fn($r) => $r['success']))
            ], 'تم إرسال الإشعار بنجاح');
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // تحديث حالة الإشعارات
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            sendErrorResponse('بيانات غير صحيحة');
        }
        
        $notificationIds = $input['notificationIds'] ?? [];
        $markAsRead = $input['markAsRead'] ?? true;
        
        if (empty($notificationIds)) {
            // تحديث جميع الإشعارات
            $updateStmt = $connection->prepare("
                UPDATE notifications 
                SET is_read = ?, updated_at = NOW()
                WHERE (target_user_id = ? OR target_user_id IS NULL)
            ");
            $updateStmt->execute([$markAsRead ? 1 : 0, $userId]);
            $affectedRows = $updateStmt->rowCount();
        } else {
            // تحديث إشعارات محددة
            $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';
            $updateStmt = $connection->prepare("
                UPDATE notifications 
                SET is_read = ?, updated_at = NOW()
                WHERE id IN ($placeholders) AND (target_user_id = ? OR target_user_id IS NULL)
            ");
            $params = array_merge([$markAsRead ? 1 : 0], $notificationIds, [$userId]);
            $updateStmt->execute($params);
            $affectedRows = $updateStmt->rowCount();
        }
        
        sendSuccessResponse([
            'affectedNotifications' => $affectedRows,
            'markAsRead' => $markAsRead
        ], 'تم تحديث حالة الإشعارات بنجاح');
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        // إلغاء اشتراك أو حذف إشعار
        $action = $_GET['action'] ?? '';
        
        if ($action === 'unsubscribe') {
            $endpoint = $_GET['endpoint'] ?? '';
            
            if (empty($endpoint)) {
                sendErrorResponse('endpoint مطلوب');
            }
            
            $stmt = $connection->prepare("
                UPDATE push_subscriptions 
                SET is_active = 0, updated_at = NOW()
                WHERE user_id = ? AND endpoint = ?
            ");
            $stmt->execute([$userId, $endpoint]);
            
            sendSuccessResponse([
                'unsubscribed' => true,
                'endpoint' => $endpoint
            ], 'تم إلغاء الاشتراك بنجاح');
            
        } else {
            // حذف إشعار
            $notificationId = $_GET['id'] ?? '';
            
            if (empty($notificationId)) {
                sendErrorResponse('معرف الإشعار مطلوب');
            }
            
            $stmt = $connection->prepare("
                DELETE FROM notifications 
                WHERE id = ? AND (target_user_id = ? OR target_user_id IS NULL)
            ");
            $stmt->execute([$notificationId, $userId]);
            
            if ($stmt->rowCount() > 0) {
                sendSuccessResponse([
                    'deleted' => true,
                    'notificationId' => (int)$notificationId
                ], 'تم حذف الإشعار بنجاح');
            } else {
                sendErrorResponse('الإشعار غير موجود أو ليس لديك صلاحية لحذفه', 404);
            }
        }
    }
    
} catch (Exception $e) {
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    error_log('Database error in push.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}
?>
