'use client';

import React from 'react';
import {
  TrendingUp,
  TrendingDown,
  Minus,
  MoreHorizontal,
  RefreshCw,
  ExternalLink,
  Info
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';

export interface AdminStatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ComponentType<any>;
  trend?: {
    value: number;
    label?: string;
    period?: string;
  };
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'pink' | 'indigo' | 'gray';
  loading?: boolean;
  clickable?: boolean;
  onClick?: () => void;
  actions?: Array<{
    label: string;
    icon?: React.ComponentType<any>;
    onClick: () => void;
  }>;
  className?: string;
}

export default function AdminStatsCard({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  color = 'blue',
  loading = false,
  clickable = false,
  onClick,
  actions,
  className = ''
}: AdminStatsCardProps) {
  const { t, isRTL, formatNumber } = useAdminTranslation();

  const getColorClasses = () => {
    switch (color) {
      case 'green':
        return {
          bg: 'bg-green-50 dark:bg-green-900/20',
          icon: 'text-green-600 dark:text-green-400',
          border: 'border-green-200 dark:border-green-800'
        };
      case 'red':
        return {
          bg: 'bg-red-50 dark:bg-red-900/20',
          icon: 'text-red-600 dark:text-red-400',
          border: 'border-red-200 dark:border-red-800'
        };
      case 'yellow':
        return {
          bg: 'bg-yellow-50 dark:bg-yellow-900/20',
          icon: 'text-yellow-600 dark:text-yellow-400',
          border: 'border-yellow-200 dark:border-yellow-800'
        };
      case 'purple':
        return {
          bg: 'bg-purple-50 dark:bg-purple-900/20',
          icon: 'text-purple-600 dark:text-purple-400',
          border: 'border-purple-200 dark:border-purple-800'
        };
      case 'pink':
        return {
          bg: 'bg-pink-50 dark:bg-pink-900/20',
          icon: 'text-pink-600 dark:text-pink-400',
          border: 'border-pink-200 dark:border-pink-800'
        };
      case 'indigo':
        return {
          bg: 'bg-indigo-50 dark:bg-indigo-900/20',
          icon: 'text-indigo-600 dark:text-indigo-400',
          border: 'border-indigo-200 dark:border-indigo-800'
        };
      case 'gray':
        return {
          bg: 'bg-gray-50 dark:bg-gray-900/20',
          icon: 'text-gray-600 dark:text-gray-400',
          border: 'border-gray-200 dark:border-gray-800'
        };
      default: // blue
        return {
          bg: 'bg-blue-50 dark:bg-blue-900/20',
          icon: 'text-blue-600 dark:text-blue-400',
          border: 'border-blue-200 dark:border-blue-800'
        };
    }
  };

  const getTrendIcon = () => {
    if (!trend) return null;
    
    if (trend.value > 0) {
      return <TrendingUp className="w-4 h-4 text-green-500" />;
    } else if (trend.value < 0) {
      return <TrendingDown className="w-4 h-4 text-red-500" />;
    } else {
      return <Minus className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTrendColor = () => {
    if (!trend) return '';
    
    if (trend.value > 0) {
      return 'text-green-600 dark:text-green-400';
    } else if (trend.value < 0) {
      return 'text-red-600 dark:text-red-400';
    } else {
      return 'text-gray-600 dark:text-gray-400';
    }
  };

  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      return formatNumber(val);
    }
    return val;
  };

  const colorClasses = getColorClasses();

  const cardClasses = `
    bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6
    ${clickable ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}
    ${className}
  `;

  const handleCardClick = () => {
    if (clickable && onClick) {
      onClick();
    }
  };

  return (
    <div className={cardClasses} onClick={handleCardClick}>
      {/* Header */}
      <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
          {Icon && (
            <div className={`w-12 h-12 ${colorClasses.bg} rounded-lg flex items-center justify-center ${isRTL ? 'ml-4' : 'mr-4'}`}>
              <Icon className={`w-6 h-6 ${colorClasses.icon}`} />
            </div>
          )}
          <div className={isRTL ? 'text-right' : 'text-left'}>
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</h3>
            {subtitle && (
              <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">{subtitle}</p>
            )}
          </div>
        </div>

        {/* Actions */}
        {actions && actions.length > 0 && (
          <div className="relative group">
            <button className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded">
              <MoreHorizontal className="w-4 h-4" />
            </button>
            
            {/* Dropdown Menu */}
            <div className={`absolute ${isRTL ? 'left-0' : 'right-0'} top-full mt-1 w-48 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10`}>
              {actions.map((action, index) => (
                <button
                  key={index}
                  onClick={(e) => {
                    e.stopPropagation();
                    action.onClick();
                  }}
                  className={`w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center ${isRTL ? 'flex-row-reverse' : ''} ${
                    index === 0 ? 'rounded-t-lg' : ''
                  } ${
                    index === actions.length - 1 ? 'rounded-b-lg' : ''
                  }`}
                >
                  {action.icon && (
                    <action.icon className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  )}
                  {action.label}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Value */}
      <div className={`mb-4 ${isRTL ? 'text-right' : 'text-left'}`}>
        {loading ? (
          <div className="flex items-center">
            <RefreshCw className="w-6 h-6 animate-spin text-gray-400 mr-2" />
            <div className="h-8 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
          </div>
        ) : (
          <p className="text-3xl font-bold text-gray-900 dark:text-white">
            {formatValue(value)}
          </p>
        )}
      </div>

      {/* Trend */}
      {trend && !loading && (
        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
          {getTrendIcon()}
          <span className={`text-sm font-medium ${getTrendColor()} ${isRTL ? 'mr-1' : 'ml-1'}`}>
            {Math.abs(trend.value)}%
          </span>
          {trend.label && (
            <span className={`text-sm text-gray-600 dark:text-gray-400 ${isRTL ? 'mr-2' : 'ml-2'}`}>
              {trend.label}
            </span>
          )}
          {trend.period && (
            <span className={`text-xs text-gray-500 dark:text-gray-500 ${isRTL ? 'mr-1' : 'ml-1'}`}>
              {trend.period}
            </span>
          )}
        </div>
      )}
    </div>
  );
}

// Stats Grid Component
export interface AdminStatsGridProps {
  stats: Array<Omit<AdminStatsCardProps, 'className'>>;
  columns?: 1 | 2 | 3 | 4 | 5 | 6;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function AdminStatsGrid({
  stats,
  columns = 4,
  gap = 'md',
  className = ''
}: AdminStatsGridProps) {
  const getGridClasses = () => {
    const colClasses = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
      5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
    };

    const gapClasses = {
      sm: 'gap-3',
      md: 'gap-4',
      lg: 'gap-6'
    };

    return `grid ${colClasses[columns]} ${gapClasses[gap]}`;
  };

  return (
    <div className={`${getGridClasses()} ${className}`}>
      {stats.map((stat, index) => (
        <AdminStatsCard key={index} {...stat} />
      ))}
    </div>
  );
}

// Compact Stats Card Component
export interface AdminCompactStatsProps {
  title: string;
  value: string | number;
  icon?: React.ComponentType<any>;
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'pink' | 'indigo' | 'gray';
  loading?: boolean;
  className?: string;
}

export function AdminCompactStats({
  title,
  value,
  icon: Icon,
  color = 'blue',
  loading = false,
  className = ''
}: AdminCompactStatsProps) {
  const { formatNumber } = useAdminTranslation();

  const getColorClasses = () => {
    switch (color) {
      case 'green':
        return 'text-green-600 dark:text-green-400';
      case 'red':
        return 'text-red-600 dark:text-red-400';
      case 'yellow':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'purple':
        return 'text-purple-600 dark:text-purple-400';
      case 'pink':
        return 'text-pink-600 dark:text-pink-400';
      case 'indigo':
        return 'text-indigo-600 dark:text-indigo-400';
      case 'gray':
        return 'text-gray-600 dark:text-gray-400';
      default:
        return 'text-blue-600 dark:text-blue-400';
    }
  };

  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      return formatNumber(val);
    }
    return val;
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          {loading ? (
            <div className="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mt-1" />
          ) : (
            <p className="text-xl font-bold text-gray-900 dark:text-white mt-1">
              {formatValue(value)}
            </p>
          )}
        </div>
        {Icon && (
          <Icon className={`w-8 h-8 ${getColorClasses()}`} />
        )}
      </div>
    </div>
  );
}
