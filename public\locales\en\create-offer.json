{"title": "Create <PERSON>er", "subtitle": "Create a buy or sell offer for stablecoins", "comingSoon": "Coming Soon", "description": "We are currently developing the create offer page to provide an optimal and responsive user experience.", "features": {"title": "Upcoming Features", "smartPricing": "Smart Pricing", "smartPricingDesc": "Automatic price suggestions based on market data", "multiPayment": "Multiple Payment Methods", "multiPaymentDesc": "Support for all local and international payment methods", "autoReply": "Auto Reply", "autoReplyDesc": "Welcome messages and automatic replies for buyers", "premiumOffers": "Premium Offers", "premiumOffersDesc": "Highlight your offers at the top for additional fees", "timeLimit": "Flexible Time Limits", "timeLimitDesc": "Set payment waiting time according to your preferences", "locationBased": "Location-Based Offers", "locationBasedDesc": "Target buyers in your geographical area"}, "currentFeatures": {"title": "Currently Available Features", "viewOffers": "View All Offers", "viewOffersDesc": "Browse available offers with advanced filtering", "smartFilters": "Smart Filtering", "smartFiltersDesc": "Interactive filters with real-time statistics", "userAuth": "Authentication System", "userAuthDesc": "Secure and protected login system", "responsive": "Responsive Design", "responsiveDesc": "Works perfectly on all devices"}, "backToOffers": "Back to Offers", "notify": "Notify Me on Launch", "notifyDesc": "Get notified as soon as the create offer page is launched", "email": "Email", "subscribe": "Subscribe", "subscribed": "Successfully subscribed!", "steps": {"basic": "Basic Information", "payment": "Payment Methods & Terms", "review": "Review & Publish"}, "offerType": {"buy": "Buy", "sell": "<PERSON>ll", "buyDescription": "I want to buy stablecoin", "sellDescription": "I want to sell stablecoin"}, "fields": {"offerType": "Offer Type", "stablecoin": "Stablecoin", "localCurrency": "Local Currency", "totalAmount": "Total Amount", "minAmount": "Minimum Amount", "maxAmount": "Maximum Amount", "pricePerUnit": "Price per {{unit}}", "paymentMethods": "Payment Methods", "terms": "Trading Terms", "timeLimit": "Time Limit", "autoReply": "Auto Reply", "premiumOffer": "Premium Offer", "freeOffer": "Free Offer"}, "placeholders": {"amount": "0.00", "price": "0.00", "terms": "Enter trading terms...", "autoReply": "Welcome message for buyers..."}, "validation": {"required": "This field is required", "invalidAmount": "Please enter a valid amount", "invalidPrice": "Please enter a valid price", "minAmountTooHigh": "Minimum amount cannot be higher than total amount", "maxAmountTooHigh": "Maximum amount cannot be higher than total amount", "noPaymentMethods": "Please select at least one payment method", "noTerms": "Please enter trading terms", "priceDeviation": "Price deviates significantly from market price"}, "actions": {"previous": "Previous", "next": "Next", "save": "Save", "publish": "Publish Offer", "creating": "Creating...", "preview": "Preview"}, "preview": {"title": "Offer Preview", "type": "Type", "amount": "Amount", "price": "Price", "total": "Total", "fees": "Fees", "freeOffer": "Free Offer - No Fees"}, "freeOffers": {"title": "Free Offer", "description": "{{remaining}} of {{total}} free offers remaining", "notAvailable": "Free offers are not available at the moment", "limitReached": "You have reached the maximum number of free offers", "verificationRequired": "Identity verification required to create free offers", "cooldownPeriod": "Please wait {{minutes}} minutes before creating another free offer"}, "conversion": {"exchangeRate": "Exchange Rate: 1 {{from}} = {{rate}} {{to}}", "marketPrice": "Current market price: {{price}} {{currency}}", "refreshRate": "Refresh Rate"}, "auth": {"loginRequired": "<PERSON><PERSON> Required", "loginRequiredMessage": "You must log in first to create a new offer. Join IKAROS platform for secure trading.", "login": "<PERSON><PERSON>", "register": "Create New Account", "secureMessage": "Secure platform protected with latest security technologies"}, "messages": {"offerCreated": "Offer created successfully", "offerCreationFailed": "Failed to create offer", "errorOccurred": "An error occurred while creating the offer", "fetchLimitsError": "Error fetching free offer limits", "invalidJsonResponse": "Invalid response from server", "networkError": "Network error, please try again"}, "paymentMethods": {"sell": {"bank_transfer": "Receive Bank Transfer", "quick_transfer": "Receive Quick Transfer", "instant_transfer": "Receive Instant Transfer", "cash_deposit": "Cash Deposit", "mobile_wallet": "Receive via Mobile Wallet", "paypal": "Receive via PayPal", "wise": "Receive via Wise"}, "buy": {"bank_transfer": "Send Bank Transfer", "quick_transfer": "Send Quick Transfer", "instant_transfer": "Send Instant Transfer", "cash_payment": "Cash Payment", "mobile_wallet": "Pay via Mobile Wallet", "credit_card": "Pay with Credit Card", "debit_card": "Pay with Debit Card", "paypal": "Pay via PayPal", "apple_pay": "Pay via Apple Pay", "google_pay": "Pay via Google Pay"}, "labels": {"processingTime": "Processing Time", "fees": "Fees", "popularity": "Popularity", "recommended": "Recommended", "fast": "Fast", "secure": "Secure", "lowFees": "Low Fees", "instant": "Instant", "free": "Free", "lowCost": "Low Cost", "mediumCost": "Medium Cost", "highCost": "High Cost"}}, "timeOptions": {"15min": "15 minutes", "30min": "30 minutes", "1hour": "1 hour", "2hours": "2 hours", "4hours": "4 hours", "8hours": "8 hours", "24hours": "24 hours"}, "summary": {"title": "Offer Summary", "currency": "<PERSON><PERSON><PERSON><PERSON>", "minimum": "Minimum", "maximum": "Maximum", "timeLimit": "Time Limit", "paymentMethodsCount": "Payment Methods", "selectedPaymentMethods": "Selected Payment Methods", "tradingTerms": "Trading Terms", "autoReplyMessage": "Auto Reply", "readyToPublish": "Ready to Publish", "confirmationMessage": "Make sure all information is correct before publishing the offer. You can edit the offer later from the dashboard.", "notSpecified": "Not specified", "minutes": "minutes", "methods": "methods"}, "newForm": {"tryNewForm": "Try New Form (Beta)", "tryNewFormDesc": "Try the new form to create offers with all advanced features", "autoReplyHelp": "Welcome message sent automatically to interested buyers"}, "stablecoins": {"usdt": {"name": "Tether USD", "description": "The most traded stablecoin in the world"}, "usdc": {"name": "USD Coin", "description": "Fully backed stablecoin by US Dollar"}, "busd": {"name": "Binance USD", "description": "Official stablecoin of Binance"}, "dai": {"name": "Dai Stablecoin", "description": "Decentralized stablecoin backed by collateral"}, "fdusd": {"name": "First Digital USD", "description": "New stablecoin backed by US Dollar"}, "tusd": {"name": "TrueUSD", "description": "Trusted stablecoin with full transparency"}, "usdd": {"name": "USDD", "description": "Decentralized stablecoin from TRON"}, "frax": {"name": "Frax", "description": "Fractional-algorithmic stablecoin"}, "usdp": {"name": "Pax Dollar", "description": "Regulated stablecoin by Paxos"}, "lusd": {"name": "Liquity USD", "description": "Decentralized stablecoin backed by ETH"}, "gusd": {"name": "Gemini Dollar", "description": "Regulated stablecoin by Gemini"}, "susd": {"name": "Synthetix USD", "description": "Synthetic stablecoin from Synthetix"}, "ustc": {"name": "TerraClassicUSD", "description": "Stablecoin from Terra Classic"}, "pyusd": {"name": "PayPal USD", "description": "Stablecoin by PayPal"}}, "ui": {"mainForm": "Main Form", "sidebar": "Sidebar - Offer Preview", "offerTypeSection": "Offer Type", "stablecoinSection": "Stablecoin", "localCurrencySection": "Local Currency", "amountSection": "Amount", "priceSection": "Price", "freeOfferSection": "Free Offer Option", "paymentMethodsSection": "Payment Methods", "tradingTermsSection": "Trading Terms", "timeLimitSection": "Time Limit", "autoReplySection": "Auto Reply", "offerSummarySection": "Offer Summary", "selectedPaymentMethodsSection": "Selected Payment Methods", "tradingTermsReviewSection": "Trading Terms", "autoReplyReviewSection": "Auto Reply", "publishConfirmationSection": "Publish Confirmation", "navigationButtons": "Navigation Buttons", "redirectingToOffers": "Redirecting to offers page", "errorTypeCheck": "Error type check", "feeCalculation": "1.5% fees", "loadingFreeOfferLimits": "Loading free offer limits on component mount", "updateMinMaxAmounts": "Update min/max amounts when amount changes", "clearErrorOnEdit": "Clear error on edit"}}