'use client';

import React, { useState } from 'react';
import { Shield, Wallet, Key, User, AlertCircle, CheckCircle, Lock, Eye, EyeOff, Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { notificationService } from '@/services/notificationService';

export default function AdminLoginPage() {
  const router = useRouter();
  const [loginMethod, setLoginMethod] = useState<'wallet' | 'credentials'>('credentials');
  const [isLoading, setIsLoading] = useState(false);
  const [credentials, setCredentials] = useState({
    username: '',
    password: '',
    walletAddress: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  // تسجيل الدخول بالبيانات
  const handleCredentialsLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // التحقق من البيانات
      if (!credentials.username || !credentials.password || !credentials.walletAddress) {
        throw new Error('جميع الحقول مطلوبة');
      }

      // استدعاء API
      const response = await fetch('/api/admin/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: credentials.username.trim(),
          password: credentials.password.trim(),
          walletAddress: credentials.walletAddress.trim()
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // حفظ بيانات الجلسة
        sessionStorage.setItem('admin_token', data.token);
        sessionStorage.setItem('admin_session', JSON.stringify({
          admin: data.admin,
          token: data.token,
          expiresAt: data.expires_at,
          permissions: data.permissions
        }));

        notificationService.success('تم تسجيل الدخول بنجاح');
        router.push('/admin');
      } else {
        throw new Error(data.error || data.message || 'فشل في تسجيل الدخول');
      }
    } catch (error: any) {
      console.error('❌ Admin login error:', error);
      setError(error.message || 'خطأ في تسجيل الدخول');
      notificationService.error(error.message || 'فشل في تسجيل الدخول');
    } finally {
      setIsLoading(false);
    }
  };

  // تسجيل الدخول بالمحفظة
  const handleWalletLogin = async () => {
    setIsLoading(true);
    setError('');

    try {
      // التحقق من وجود محفظة
      if (!window.ethereum) {
        throw new Error('لم يتم العثور على محفظة. يرجى تثبيت MetaMask');
      }

      // طلب الاتصال
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      });

      if (!accounts || accounts.length === 0) {
        throw new Error('لم يتم اختيار أي حساب');
      }

      const walletAddress = accounts[0];

      // التحقق من صلاحيات الإدارة
      const response = await fetch('/api/admin/check-permissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ walletAddress }),
      });

      const data = await response.json();

      if (response.ok && data.success && data.isAdmin) {
        // حفظ بيانات الجلسة
        sessionStorage.setItem('admin_wallet_session', JSON.stringify({
          address: walletAddress,
          adminData: data.adminData,
          loginMethod: 'wallet',
          timestamp: new Date().toISOString()
        }));

        notificationService.success('تم تسجيل الدخول بالمحفظة بنجاح');
        router.push('/admin');
      } else {
        throw new Error('هذه المحفظة لا تملك صلاحيات إدارية');
      }
    } catch (error: any) {
      console.error('❌ Wallet login error:', error);
      setError(error.message || 'فشل في تسجيل الدخول بالمحفظة');
      notificationService.error(error.message || 'فشل في تسجيل الدخول بالمحفظة');
    } finally {
      setIsLoading(false);
    }
  };

  // ملء البيانات الافتراضية
  const fillDefaultData = () => {
    setCredentials({
      username: 'admin',
      password: 'admin123',
      walletAddress: '******************************************'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-4">
              <Shield className="w-8 h-8 text-red-600 dark:text-red-400" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              دخول المدراء
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              لوحة التحكم الإدارية
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-center">
              <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mr-3 flex-shrink-0" />
              <span className="text-red-700 dark:text-red-300 text-sm">{error}</span>
            </div>
          )}

          {/* Login Method Tabs */}
          <div className="flex mb-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setLoginMethod('credentials')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                loginMethod === 'credentials'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
              disabled={isLoading}
            >
              <User className="w-4 h-4 inline mr-2" />
              بيانات الاعتماد
            </button>
            <button
              onClick={() => setLoginMethod('wallet')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                loginMethod === 'wallet'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
              disabled={isLoading}
            >
              <Wallet className="w-4 h-4 inline mr-2" />
              المحفظة
            </button>
          </div>

          {/* Credentials Login */}
          {loginMethod === 'credentials' && (
            <form onSubmit={handleCredentialsLogin} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  اسم المستخدم
                </label>
                <div className="relative">
                  <User className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-5 h-5" />
                  <input
                    type="text"
                    value={credentials.username}
                    onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
                    className="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="admin"
                    required
                    disabled={isLoading}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  كلمة المرور
                </label>
                <div className="relative">
                  <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-5 h-5" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={credentials.password}
                    onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                    className="w-full pr-10 pl-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="••••••••"
                    required
                    disabled={isLoading}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                    disabled={isLoading}
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  عنوان المحفظة
                </label>
                <div className="relative">
                  <Wallet className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-5 h-5" />
                  <input
                    type="text"
                    value={credentials.walletAddress}
                    onChange={(e) => setCredentials(prev => ({ ...prev, walletAddress: e.target.value }))}
                    className="w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-sm"
                    placeholder="0x..."
                    required
                    disabled={isLoading}
                  />
                </div>
                <button
                  type="button"
                  onClick={fillDefaultData}
                  className="mt-2 text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 underline"
                  disabled={isLoading}
                >
                  ملء البيانات الافتراضية للاختبار
                </button>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin mr-2" />
                    جاري تسجيل الدخول...
                  </>
                ) : (
                  <>
                    <Key className="w-5 h-5 mr-2" />
                    تسجيل الدخول
                  </>
                )}
              </button>
            </form>
          )}

          {/* Wallet Login */}
          {loginMethod === 'wallet' && (
            <div className="space-y-6">
              <div className="text-center">
                <Wallet className="w-16 h-16 text-red-600 dark:text-red-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  اربط محفظتك للدخول كمدير
                </p>
              </div>

              <button
                onClick={handleWalletLogin}
                disabled={isLoading}
                className="w-full bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin mr-2" />
                    جاري الاتصال...
                  </>
                ) : (
                  <>
                    <Wallet className="w-5 h-5 mr-2" />
                    ربط المحفظة
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
