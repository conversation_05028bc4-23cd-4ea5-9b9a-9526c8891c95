/**
 * أدوات مساعدة للعناوين
 * Address Utilities
 */

import { ethers } from 'ethers';

/**
 * تحويل العنوان إلى checksum صحيح
 */
export function toChecksumAddress(address: string): string {
  try {
    return ethers.getAddress(address);
  } catch (error) {
    console.error('خطأ في تحويل العنوان إلى checksum:', error);
    throw new Error(`عنوان غير صالح: ${address}`);
  }
}

/**
 * التحقق من صحة العنوان
 */
export function isValidAddress(address: string): boolean {
  try {
    ethers.getAddress(address);
    return true;
  } catch {
    return false;
  }
}

/**
 * تقصير العنوان للعرض
 */
export function shortenAddress(address: string, chars: number = 4): string {
  if (!address) return '';
  
  try {
    const checksumAddress = ethers.getAddress(address);
    return `${checksumAddress.slice(0, chars + 2)}...${checksumAddress.slice(-chars)}`;
  } catch {
    return address;
  }
}

/**
 * مقارنة عنوانين
 */
export function addressesEqual(address1: string, address2: string): boolean {
  try {
    const addr1 = ethers.getAddress(address1);
    const addr2 = ethers.getAddress(address2);
    return addr1.toLowerCase() === addr2.toLowerCase();
  } catch {
    return false;
  }
}

/**
 * التحقق من أن العنوان هو عنوان صفر
 */
export function isZeroAddress(address: string): boolean {
  try {
    const checksumAddress = ethers.getAddress(address);
    return checksumAddress === ethers.ZeroAddress;
  } catch {
    return false;
  }
}

/**
 * تحويل قائمة عناوين إلى checksum
 */
export function toChecksumAddresses(addresses: string[]): string[] {
  return addresses.map(address => {
    try {
      return ethers.getAddress(address);
    } catch {
      console.warn(`تجاهل عنوان غير صالح: ${address}`);
      return address; // إرجاع العنوان الأصلي إذا فشل التحويل
    }
  });
}

/**
 * فلترة العناوين الصالحة فقط
 */
export function filterValidAddresses(addresses: string[]): string[] {
  return addresses.filter(address => isValidAddress(address));
}

/**
 * تنسيق العنوان للعرض مع checksum
 */
export function formatAddressForDisplay(address: string, options?: {
  shorten?: boolean;
  chars?: number;
  showChecksum?: boolean;
}): string {
  const { shorten = false, chars = 4, showChecksum = true } = options || {};
  
  if (!address) return '';
  
  try {
    const checksumAddress = showChecksum ? ethers.getAddress(address) : address;
    
    if (shorten) {
      return shortenAddress(checksumAddress, chars);
    }
    
    return checksumAddress;
  } catch {
    return address;
  }
}

/**
 * استخراج العنوان من النص
 */
export function extractAddressFromText(text: string): string | null {
  const addressRegex = /0x[a-fA-F0-9]{40}/g;
  const matches = text.match(addressRegex);
  
  if (matches && matches.length > 0) {
    const address = matches[0];
    return isValidAddress(address) ? ethers.getAddress(address) : null;
  }
  
  return null;
}

/**
 * تحويل العنوان إلى رابط مستكشف البلوك تشين
 */
export function getExplorerLink(address: string, network: 'mainnet' | 'testnet' = 'testnet'): string {
  try {
    const checksumAddress = ethers.getAddress(address);
    const baseUrl = network === 'mainnet' 
      ? 'https://bscscan.com/address/' 
      : 'https://testnet.bscscan.com/address/';
    
    return `${baseUrl}${checksumAddress}`;
  } catch {
    return '';
  }
}

/**
 * تحويل hash المعاملة إلى رابط مستكشف البلوك تشين
 */
export function getTransactionLink(txHash: string, network: 'mainnet' | 'testnet' = 'testnet'): string {
  const baseUrl = network === 'mainnet' 
    ? 'https://bscscan.com/tx/' 
    : 'https://testnet.bscscan.com/tx/';
  
  return `${baseUrl}${txHash}`;
}

/**
 * تحويل رقم البلوك إلى رابط مستكشف البلوك تشين
 */
export function getBlockLink(blockNumber: number, network: 'mainnet' | 'testnet' = 'testnet'): string {
  const baseUrl = network === 'mainnet' 
    ? 'https://bscscan.com/block/' 
    : 'https://testnet.bscscan.com/block/';
  
  return `${baseUrl}${blockNumber}`;
}

/**
 * التحقق من أن العنوان ينتمي لشبكة BSC
 */
export function isBSCAddress(address: string): boolean {
  // جميع عناوين Ethereum/BSC تبدأ بـ 0x وطولها 42 حرف
  return isValidAddress(address);
}

/**
 * إنشاء عنوان عشوائي للاختبار
 */
export function generateRandomAddress(): string {
  const wallet = ethers.Wallet.createRandom();
  return wallet.address;
}

/**
 * تحويل العنوان إلى bytes32 (للعقود الذكية)
 */
export function addressToBytes32(address: string): string {
  try {
    const checksumAddress = ethers.getAddress(address);
    return ethers.zeroPadValue(checksumAddress, 32);
  } catch {
    throw new Error(`عنوان غير صالح: ${address}`);
  }
}

/**
 * تحويل bytes32 إلى عنوان
 */
export function bytes32ToAddress(bytes32: string): string {
  try {
    const address = ethers.getAddress(ethers.dataSlice(bytes32, 12));
    return address;
  } catch {
    throw new Error(`bytes32 غير صالح: ${bytes32}`);
  }
}

// تصدير العناوين المهمة مع checksum صحيح
export const IMPORTANT_ADDRESSES = {
  ZERO_ADDRESS: ethers.ZeroAddress,
  BURN_ADDRESS: '******************************************',
  // يمكن إضافة عناوين مهمة أخرى هنا
};

// تصدير دوال للاستخدام في وحدة التحكم
if (typeof window !== 'undefined') {
  (window as any).addressUtils = {
    toChecksumAddress,
    isValidAddress,
    shortenAddress,
    addressesEqual,
    isZeroAddress,
    formatAddressForDisplay,
    extractAddressFromText,
    getExplorerLink,
    getTransactionLink,
    getBlockLink,
    generateRandomAddress
  };
}
