/**
 * أنواع البيانات لإدارة النزاعات المتقدمة
 * Advanced Dispute Management Data Types
 */

import { TradeUser, Currency, TradeType } from './adminTrades';

// أنواع البيانات الأساسية
export type DisputeStatus = 'pending' | 'in_progress' | 'resolved' | 'escalated' | 'closed';

export type DisputePriority = 'low' | 'medium' | 'high' | 'urgent';

export type ResolutionType = 'seller_favor' | 'buyer_favor' | 'partial_refund' | 'cancelled';

export type MessageType = 'text' | 'image' | 'file' | 'payment_proof' | 'system';

// واجهة النزاع المتقدمة
export interface AdminDispute {
  id: number;
  trade_id: number;
  
  // معلومات الصفقة الأساسية
  trade_type: TradeType;
  trade_amount: number;
  trade_price: number;
  trade_currency: Currency;
  payment_method: string;
  
  // معلومات النزاع
  dispute_reason: string;
  dispute_description?: string;
  dispute_category?: string;
  dispute_priority: DisputePriority;
  dispute_status: DisputeStatus;
  
  // التواريخ
  trade_created_at: string;
  dispute_created_at: string;
  dispute_updated_at: string;
  dispute_resolved_at?: string;
  last_activity_at: string;
  
  // معلومات الحل
  resolution_type?: ResolutionType;
  resolution_notes?: string;
  resolution_amount?: number;
  resolved_by_admin_id?: number;
  resolved_by_admin_username?: string;
  
  // معلومات المستخدمين
  seller: TradeUser;
  buyer: TradeUser;
  
  // إحصائيات
  message_count: number;
  admin_notes_count: number;
  resolution_count: number;
  dispute_age_hours: number;
  response_time_hours?: number;
  
  // ملفات مرفقة
  attachments_count: number;
  has_payment_proof: boolean;
  
  // معلومات العقد الذكي
  smart_contract_status?: string;
  escrow_amount?: number;
  
  // علامات ومؤشرات
  is_escalated: boolean;
  requires_urgent_attention: boolean;
  has_unread_messages: boolean;
  assigned_admin_id?: number;
  assigned_admin_username?: string;
}

// رسالة في النزاع
export interface DisputeMessage {
  id: number;
  trade_id: number;
  sender_id: number;
  sender_username: string;
  sender_type: 'seller' | 'buyer' | 'admin' | 'system';
  message_type: MessageType;
  content: string;
  
  // ملفات مرفقة
  file_url?: string;
  file_name?: string;
  file_size?: number;
  file_type?: string;
  
  // حالة القراءة
  is_read: boolean;
  read_by_admin: boolean;
  read_at?: string;
  
  // معلومات إضافية
  is_internal: boolean;
  is_system_message: boolean;
  reply_to_message_id?: number;
  
  created_at: string;
  updated_at?: string;
}

// ملاحظة إدارية للنزاع
export interface DisputeNote {
  id: number;
  trade_id: number;
  admin_id: number;
  admin_username: string;
  admin_full_name: string;
  note: string;
  note_type: 'internal' | 'resolution' | 'escalation' | 'investigation';
  is_internal: boolean;
  visibility: 'internal' | 'public' | 'parties_only';
  created_at: string;
  updated_at?: string;
}

// قرار النزاع
export interface DisputeResolution {
  id: number;
  trade_id: number;
  admin_id: number;
  admin_username: string;
  admin_full_name: string;
  
  resolution_type: ResolutionType;
  resolution_notes: string;
  resolution_amount: number;
  
  // تفاصيل التنفيذ
  smart_contract_tx_hash?: string;
  blockchain_confirmation?: boolean;
  execution_status: 'pending' | 'executing' | 'completed' | 'failed';
  
  // أوقات مهمة
  created_at: string;
  executed_at?: string;
  confirmed_at?: string;
  
  // معلومات إضافية
  requires_manual_intervention: boolean;
  follow_up_required: boolean;
  user_satisfaction_rating?: number;
}

// تفاصيل النزاع الكاملة
export interface DisputeDetails {
  dispute: AdminDispute;
  messages: DisputeMessage[];
  admin_notes: DisputeNote[];
  resolutions: DisputeResolution[];
  timeline: DisputeTimelineEvent[];
  related_disputes?: AdminDispute[];
}

// حدث في الجدول الزمني للنزاع
export interface DisputeTimelineEvent {
  id: string;
  type: 'dispute_created' | 'message_sent' | 'note_added' | 'status_changed' | 'resolution_applied' | 'escalated';
  title: string;
  description: string;
  actor_id: number;
  actor_username: string;
  actor_type: 'seller' | 'buyer' | 'admin' | 'system';
  timestamp: string;
  details?: any;
  is_important: boolean;
}

// فلاتر النزاعات
export interface DisputeFilters {
  status?: DisputeStatus | DisputeStatus[];
  priority?: DisputePriority | DisputePriority[];
  resolution_type?: ResolutionType;
  date_from?: string;
  date_to?: string;
  amount_min?: number;
  amount_max?: number;
  currency?: Currency;
  assigned_admin_id?: number;
  dispute_category?: string;
  has_unread_messages?: boolean;
  requires_urgent_attention?: boolean;
  search_term?: string;
  trade_id?: number;
  user_id?: number;
}

// إحصائيات النزاعات
export interface DisputeStatistics {
  // الإحصائيات الأساسية
  total_disputes: number;
  pending_disputes: number;
  in_progress_disputes: number;
  resolved_disputes: number;
  escalated_disputes: number;
  
  // الأوقات
  avg_resolution_time: number; // بالساعات
  avg_response_time: number;
  fastest_resolution: number;
  slowest_resolution: number;
  
  // النسب المئوية
  resolution_rate: number;
  escalation_rate: number;
  user_satisfaction_rate: number;
  
  // إحصائيات الحلول
  resolution_types: Array<{
    type: ResolutionType;
    count: number;
    percentage: number;
    avg_amount: number;
    total_amount: number;
  }>;
  
  // إحصائيات الأولوية
  priority_breakdown: Array<{
    priority: DisputePriority;
    count: number;
    percentage: number;
    avg_resolution_time: number;
  }>;
  
  // إحصائيات الفئات
  category_breakdown: Array<{
    category: string;
    count: number;
    percentage: number;
    avg_resolution_time: number;
  }>;
  
  // أداء المدراء
  admin_performance: Array<{
    admin_id: number;
    admin_username: string;
    resolved_disputes: number;
    avg_resolution_time: number;
    user_satisfaction_rating: number;
    resolution_types: {
      seller_favor: number;
      buyer_favor: number;
      partial_refund: number;
    };
  }>;
  
  // اتجاهات زمنية
  daily_trends?: Array<{
    date: string;
    new_disputes: number;
    resolved_disputes: number;
    avg_resolution_time: number;
  }>;
}

// خيارات حل النزاع
export interface ResolutionOptions {
  resolution_type: ResolutionType;
  resolution_notes: string;
  resolution_amount?: number;
  notify_users: boolean;
  execute_immediately: boolean;
  requires_smart_contract: boolean;
  follow_up_required: boolean;
  user_communication_template?: string;
}

// إجراء تصعيد النزاع
export interface EscalationAction {
  trade_id: number;
  escalation_reason: string;
  escalation_level: 'level_2' | 'level_3' | 'legal';
  assigned_admin_id?: number;
  priority_override?: DisputePriority;
  deadline?: string;
  special_instructions?: string;
}

// تقييم النزاع
export interface DisputeAssessment {
  trade_id: number;
  admin_id: number;
  
  // تقييم الأدلة
  seller_evidence_strength: 1 | 2 | 3 | 4 | 5;
  buyer_evidence_strength: 1 | 2 | 3 | 4 | 5;
  evidence_notes: string;
  
  // تقييم المخاطر
  risk_level: 'low' | 'medium' | 'high';
  fraud_indicators: string[];
  
  // توصيات
  recommended_resolution: ResolutionType;
  recommended_amount?: number;
  confidence_level: 1 | 2 | 3 | 4 | 5;
  
  // ملاحظات
  assessment_notes: string;
  requires_review: boolean;
  
  created_at: string;
  updated_at?: string;
}

// حالة مكون إدارة النزاعات
export interface DisputeManagementState {
  disputes: AdminDispute[];
  selectedDispute?: AdminDispute;
  disputeDetails?: DisputeDetails;
  filters: DisputeFilters;
  statistics?: DisputeStatistics;
  loading: {
    isLoading: boolean;
    isLoadingDetails: boolean;
    isResolving: boolean;
    isEscalating: boolean;
    error?: string;
  };
  view: 'list' | 'details' | 'resolution' | 'statistics';
  sortBy: 'created_at' | 'priority' | 'age' | 'amount';
  sortDirection: 'asc' | 'desc';
  autoRefresh: boolean;
  refreshInterval: number;
}

// أحداث مكون إدارة النزاعات
export interface DisputeManagementEvents {
  onDisputeSelect: (dispute: AdminDispute) => void;
  onResolveDispute: (options: ResolutionOptions) => void;
  onEscalateDispute: (action: EscalationAction) => void;
  onAddNote: (tradeId: number, note: string, type: string) => void;
  onSendMessage: (tradeId: number, message: string, isInternal: boolean) => void;
  onAssignAdmin: (tradeId: number, adminId: number) => void;
  onUpdatePriority: (tradeId: number, priority: DisputePriority) => void;
  onFiltersChange: (filters: DisputeFilters) => void;
  onRefresh: () => void;
  onExport: (filters: DisputeFilters) => void;
}

// خيارات التصدير للنزاعات
export interface DisputeExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  filters?: DisputeFilters;
  include_messages?: boolean;
  include_notes?: boolean;
  include_resolutions?: boolean;
  include_timeline?: boolean;
  date_range?: {
    start: string;
    end: string;
  };
}

// استجابة API للنزاعات
export interface DisputeApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  error_en?: string;
  message?: string;
  message_en?: string;
  timestamp?: string;
}
