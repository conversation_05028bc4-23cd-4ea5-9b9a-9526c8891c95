import { useState, useEffect } from 'react';

interface SubscriptionsTranslations {
  plans: {
    free: string;
    basic: string;
    advanced: string;
    professional: string;
    current: string;
    recommended: string;
    popular: string;
    upgrade: string;
    downgrade: string;
    manage: string;
  };
  features: {
    monthly_offers: string;
    priority_listing: string;
    advanced_analytics: string;
    email_support: string;
    priority_support: string;
    dedicated_support: string;
    api_access: string;
    basic_features: string;
    all_features: string;
    standard_support: string;
  };
  pricing: {
    monthly: string;
    yearly: string;
    save: string;
    per_month: string;
    per_year: string;
    billed_monthly: string;
    billed_yearly: string;
    free_forever: string;
  };
  limits: {
    monthly_offers_used: string;
    monthly_offers_remaining: string;
    monthly_offers_limit: string;
    unlimited: string;
    upgrade_required: string;
    limit_reached: string;
    reset_date: string;
    usage_stats: string;
  };
  status: {
    active: string;
    expired: string;
    cancelled: string;
    pending: string;
    expires_on: string;
    auto_renew: string;
    manual_renew: string;
  };
  actions: {
    subscribe: string;
    upgrade_now: string;
    cancel_subscription: string;
    renew_subscription: string;
    change_plan: string;
    view_billing: string;
    download_invoice: string;
  };
  messages: {
    subscription_success: string;
    subscription_cancelled: string;
    upgrade_success: string;
    payment_required: string;
    limit_exceeded: string;
    upgrade_to_continue: string;
    subscription_expired: string;
    auto_renew_failed: string;
  };
  billing: {
    payment_method: string;
    billing_history: string;
    next_billing: string;
    amount_due: string;
    invoice_date: string;
    payment_status: string;
    paid: string;
    pending: string;
    failed: string;
  };
  comparison: {
    compare_plans: string;
    choose_plan: string;
    best_value: string;
    most_popular: string;
    feature_included: string;
    feature_not_included: string;
    contact_sales: string;
  };
}

export const useSubscriptionsTranslation = () => {
  const [translations, setTranslations] = useState<SubscriptionsTranslations | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadTranslations = async () => {
      try {
        // استخدام اللغة العربية كافتراضي
        const lang = 'ar';
        const response = await fetch(`/locales/${lang}/subscriptions.json`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        setTranslations(data);
      } catch (error) {
        console.error('Error loading subscriptions translations:', error);
        // Fallback to English if Arabic fails
        try {
          const response = await fetch('/locales/en/subscriptions.json');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const data = await response.json();
          setTranslations(data);
        } catch (fallbackError) {
          console.error('Error loading fallback translations:', fallbackError);
          // استخدام ترجمات افتراضية
          setTranslations({
            plans: { free: "الخطة المجانية", basic: "الخطة الأساسية", advanced: "الخطة المتقدمة", professional: "الخطة الاحترافية", current: "الخطة الحالية", recommended: "موصى بها", popular: "الأكثر شعبية", upgrade: "ترقية", downgrade: "تخفيض", manage: "إدارة الاشتراك" },
            features: { monthly_offers: "العروض الشهرية", priority_listing: "أولوية في القوائم", advanced_analytics: "إحصائيات متقدمة", email_support: "دعم عبر البريد الإلكتروني", priority_support: "دعم أولوية", dedicated_support: "دعم مخصص", api_access: "وصول API", basic_features: "الميزات الأساسية", all_features: "جميع الميزات", standard_support: "دعم قياسي" },
            pricing: { monthly: "شهري", yearly: "سنوي", save: "وفر", per_month: "شهرياً", per_year: "سنوياً", billed_monthly: "يُدفع شهرياً", billed_yearly: "يُدفع سنوياً", free_forever: "مجاني للأبد" },
            limits: { monthly_offers_used: "العروض المستخدمة شهرياً", monthly_offers_remaining: "العروض المتبقية", monthly_offers_limit: "الحد الشهري", unlimited: "غير محدود", upgrade_required: "يتطلب ترقية", limit_reached: "تم الوصول للحد الشهري", reset_date: "يتم التجديد في", usage_stats: "إحصائيات الاستخدام" },
            status: { active: "نشط", expired: "منتهي الصلاحية", cancelled: "ملغي", pending: "في الانتظار", expires_on: "ينتهي في", auto_renew: "التجديد التلقائي مفعل", manual_renew: "تجديد يدوي" },
            actions: { subscribe: "اشترك الآن", upgrade_now: "ترقية الآن", cancel_subscription: "إلغاء الاشتراك", renew_subscription: "تجديد الاشتراك", change_plan: "تغيير الخطة", view_billing: "عرض الفواتير", download_invoice: "تحميل الفاتورة" },
            messages: { subscription_success: "تم تفعيل الاشتراك بنجاح!", subscription_cancelled: "تم إلغاء الاشتراك بنجاح", upgrade_success: "تم ترقية الخطة بنجاح!", payment_required: "مطلوب دفع للمتابعة", limit_exceeded: "لقد تجاوزت حد العروض الشهرية", upgrade_to_continue: "قم بترقية خطتك لإنشاء المزيد من العروض", subscription_expired: "انتهت صلاحية اشتراكك", auto_renew_failed: "فشل التجديد التلقائي. يرجى تحديث طريقة الدفع" },
            billing: { payment_method: "طريقة الدفع", billing_history: "تاريخ الفواتير", next_billing: "تاريخ الفاتورة التالية", amount_due: "المبلغ المستحق", invoice_date: "تاريخ الفاتورة", payment_status: "حالة الدفع", paid: "مدفوع", pending: "في الانتظار", failed: "فشل" },
            comparison: { compare_plans: "مقارنة الخطط", choose_plan: "اختر خطتك", best_value: "أفضل قيمة", most_popular: "الأكثر شعبية", feature_included: "متضمن", feature_not_included: "غير متضمن", contact_sales: "تواصل مع المبيعات" }
          });
        }
      } finally {
        setLoading(false);
      }
    };

    loadTranslations();
  }, []);

  const t = (key: string): string => {
    if (!translations || loading) return key;
    
    const keys = key.split('.');
    let value: any = translations;
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return key; // Return key if translation not found
      }
    }
    
    return typeof value === 'string' ? value : key;
  };

  return t;
};
