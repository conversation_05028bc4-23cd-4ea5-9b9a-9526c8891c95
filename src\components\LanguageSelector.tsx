'use client';

import { useState } from 'react';
import { Globe, ChevronDown } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

export default function LanguageSelector() {
  const { language, setLanguage } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const languages = [
    { code: 'ar' as const, name: 'العربية', flag: '🇸🇦' },
    { code: 'en' as const, name: 'English', flag: '🇺🇸' }
  ];

  const currentLanguage = languages.find(lang => lang.code === language);

  const handleLanguageChange = (langCode: 'ar' | 'en') => {
    setLanguage(langCode);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center px-3 py-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200 space-x-2 rtl:space-x-reverse border border-transparent hover:border-red-300 dark:hover:border-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 hover:shadow-sm"
        aria-label={language === 'ar' ? 'تبديل اللغة' : 'Switch Language'}
        title={language === 'ar' ? `اللغة الحالية: ${currentLanguage?.name}` : `Current Language: ${currentLanguage?.name}`}
      >
        <Globe className="w-4 h-4 text-red-600 dark:text-red-400" />
        <span className="text-lg">{currentLanguage?.flag}</span>
        <span className="hidden sm:inline-block text-sm font-medium text-gray-700 dark:text-gray-300">
          {currentLanguage?.name}
        </span>
        <ChevronDown className={`w-3.5 h-3.5 text-red-600 dark:text-red-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <>
          {/* خلفية شفافة لإغلاق القائمة */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />

          {/* القائمة المنسدلة */}
          <div className="absolute top-full ltr:right-0 rtl:left-0 mt-2 w-52 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-red-200 dark:border-red-700 py-2 z-20 backdrop-blur-sm">
            {languages.map((lang) => (
              <button
                key={lang.code}
                onClick={() => handleLanguageChange(lang.code)}
                className={`w-full flex items-center px-4 py-3 text-sm hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors space-x-3 rtl:space-x-reverse ${
                  language === lang.code ? 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 border-r-2 border-red-500' : 'text-gray-700 dark:text-gray-300'
                }`}
              >
                <span className="text-xl">{lang.flag}</span>
                <span className="font-medium flex-1 text-left rtl:text-right">
                  {lang.name}
                </span>
                {language === lang.code && (
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-red-600 dark:bg-red-400 rounded-full animate-pulse"></div>
                  </div>
                )}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
}
