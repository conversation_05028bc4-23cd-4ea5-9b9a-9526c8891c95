'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import {
  ArrowLeft,
  Clock,
  Shield,
  Star,
  AlertTriangle,
  CheckCircle,
  XCircle,
  MessageCircle,
  Phone,
  CreditCard,
  Timer,
  User,
  DollarSign,
  Loader2
} from 'lucide-react';

interface Offer {
  id: string;
  user: {
    id: string;
    username: string;
    rating: number;
    completion_rate: number;
    total_trades: number;
    is_verified: boolean;
    is_online: boolean;
  };
  offer_type: 'buy' | 'sell';
  amount: number;
  price: number;
  currency: string;
  stablecoin: string;
  payment_methods: string[];
  terms: string;
  time_limit: number;
  total_value: number;
}

export default function StartTradePage() {
  const params = useParams();
  const router = useRouter();
  const { t } = useTranslation('trade');
  const { t: commonT } = useTranslation('common');
  
  const [offer, setOffer] = useState<Offer | null>(null);
  const [loading, setLoading] = useState(true);
  const [tradeAmount, setTradeAmount] = useState('');
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);

  useEffect(() => {
    fetchOffer();
  }, [params.id]);

  const fetchOffer = async () => {
    try {
      setLoading(true);
      const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost/ikaros-p2p/api';
      const response = await fetch(`${apiUrl}/offers/details.php?id=${params.id}`);
      const result = await response.json();
      
      if (result.success) {
        setOffer(result.data);
        setTradeAmount(result.data.amount.toString());
      } else {
        throw new Error(result.error || 'Failed to fetch offer');
      }
    } catch (error) {
      console.error('Error fetching offer:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStartTrade = async () => {
    if (!agreedToTerms || !tradeAmount) return;
    
    try {
      // هنا سيتم إرسال طلب بدء التداول للـ API
      console.log('Starting trade with amount:', tradeAmount);
      
      // محاكاة نجاح العملية
      setCurrentStep(2);
      
      // التوجه لصفحة التداول
      setTimeout(() => {
        router.push(`/trade/${params.id}`);
      }, 2000);
      
    } catch (error) {
      console.error('Error starting trade:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600 dark:text-gray-400">{commonT('loading')}</p>
        </div>
      </div>
    );
  }

  if (!offer) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <XCircle className="w-16 h-16 mx-auto mb-4 text-red-500" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {t('messages.offerNotFound')}
          </h2>
          <button
            onClick={() => router.back()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {commonT('goBack')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="container-custom">
        {/* Header */}
        <div className="flex items-center mb-6">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors mr-4"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            {commonT('back')}
          </button>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t('title')}
          </h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* معلومات العرض */}
          <div className="lg:col-span-2 space-y-6">
            {/* تفاصيل العرض */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <DollarSign className="w-5 h-5 mr-2" />
                {t('offerDetails')}
              </h2>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-gray-500 dark:text-gray-400">{t('offer.type')}</span>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {offer.offer_type === 'buy' ? t('offer.buy') : t('offer.sell')} {offer.stablecoin}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-500 dark:text-gray-400">{t('offer.price')}</span>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {offer.price} {offer.currency}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-500 dark:text-gray-400">{t('offer.amount')}</span>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {offer.amount} {offer.stablecoin}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-500 dark:text-gray-400">{t('offer.total')}</span>
                  <p className="font-bold text-lg text-gray-900 dark:text-white">
                    {offer.total_value} {offer.currency}
                  </p>
                </div>
              </div>

              {/* طرق الدفع */}
              <div className="mt-4">
                <span className="text-sm text-gray-500 dark:text-gray-400">{t('offer.paymentMethods')}</span>
                <div className="flex flex-wrap gap-2 mt-1">
                  {offer.payment_methods.map((method, index) => (
                    <span key={index} className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded">
                      {method}
                    </span>
                  ))}
                </div>
              </div>

              {/* الشروط */}
              {offer.terms && (
                <div className="mt-4">
                  <span className="text-sm text-gray-500 dark:text-gray-400">{t('offer.terms')}</span>
                  <p className="text-sm text-gray-700 dark:text-gray-300 mt-1 p-3 bg-gray-50 dark:bg-gray-700 rounded">
                    {offer.terms}
                  </p>
                </div>
              )}
            </div>

            {/* معلومات المتداول */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <User className="w-5 h-5 mr-2" />
                {t('traderInfo')}
              </h2>
              
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 space-x-reverse mb-2">
                    <h3 className="font-medium text-gray-900 dark:text-white">{offer.user.username}</h3>
                    {offer.user.is_verified && (
                      <Shield className="w-4 h-4 text-green-500" title={t('trader.verified')} />
                    )}
                    {offer.user.is_online && (
                      <div className="w-2 h-2 bg-green-500 rounded-full" title={t('trader.online')} />
                    )}
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">{t('trader.rating')}</span>
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-500 fill-current mr-1" />
                        <span className="font-medium">{offer.user.rating}</span>
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">{t('trader.completionRate')}</span>
                      <p className="font-medium">{offer.user.completion_rate}%</p>
                    </div>
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">{t('trader.totalTrades')}</span>
                      <p className="font-medium">{offer.user.total_trades}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* نموذج بدء التداول */}
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {t('actions.startTrade')}
              </h2>

              {/* مبلغ التداول */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('offer.amount')} ({offer.stablecoin})
                </label>
                <input
                  type="number"
                  value={tradeAmount}
                  onChange={(e) => setTradeAmount(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="0.00"
                  min="0"
                  max={offer.amount}
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {t('offer.total')}: {(parseFloat(tradeAmount) * offer.price || 0).toFixed(2)} {offer.currency}
                </p>
              </div>

              {/* الموافقة على الشروط */}
              <div className="mb-6">
                <label className="flex items-start space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    checked={agreedToTerms}
                    onChange={(e) => setAgreedToTerms(e.target.checked)}
                    className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    أوافق على الشروط والأحكام وأتفهم المخاطر المرتبطة بالتداول
                  </span>
                </label>
              </div>

              {/* تحذيرات */}
              <div className="mb-6 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <div className="flex items-start">
                  <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-yellow-800 dark:text-yellow-200">
                    <p className="font-medium mb-1">{t('warnings.timeLimit')}</p>
                    <p>{t('warnings.onlyPayToVerified')}</p>
                  </div>
                </div>
              </div>

              {/* زر بدء التداول */}
              <button
                onClick={handleStartTrade}
                disabled={!agreedToTerms || !tradeAmount || parseFloat(tradeAmount) <= 0}
                className="w-full flex items-center justify-center space-x-2 space-x-reverse px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              >
                {currentStep === 1 ? (
                  <>
                    <CheckCircle className="w-5 h-5" />
                    <span>{t('actions.startTrade')}</span>
                  </>
                ) : (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin" />
                    <span>{t('messages.tradeStarted')}</span>
                  </>
                )}
              </button>

              {/* أزرار إضافية */}
              <div className="grid grid-cols-2 gap-2 mt-3">
                <button
                  onClick={() => router.push(`/chat/${offer.user.id}`)}
                  className="flex items-center justify-center space-x-1 space-x-reverse px-3 py-2 text-sm text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <MessageCircle className="w-4 h-4" />
                  <span>{t('chat.title')}</span>
                </button>
                
                <button
                  onClick={() => router.push(`/offers/${offer.id}`)}
                  className="flex items-center justify-center space-x-1 space-x-reverse px-3 py-2 text-sm text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <span>{t('actions.viewContract')}</span>
                </button>
              </div>
            </div>

            {/* معلومات الأمان */}
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <div className="flex items-start">
                <Shield className="w-5 h-5 text-green-600 dark:text-green-400 mr-2 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-green-800 dark:text-green-200">
                  <p className="font-medium mb-1">حماية العقد الذكي</p>
                  <p>أموالك محمية بواسطة العقد الذكي حتى تأكيد استلام الدفع</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
