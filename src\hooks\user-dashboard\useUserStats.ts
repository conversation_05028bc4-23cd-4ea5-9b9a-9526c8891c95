'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet, handleApiError } from '@/utils/apiClient';

export interface UserStats {
  // إحصائيات أساسية
  totalTrades: number;
  completedTrades: number;
  activeTrades: number;
  cancelledTrades: number;
  disputedTrades: number;
  
  // إحصائيات الحجم
  totalVolume: string;
  monthlyVolume: string;
  weeklyVolume: string;
  averageTradeAmount: string;
  
  // إحصائيات التقييم
  rating: number;
  ratingCount: number;
  positiveReviews: number;
  negativeReviews: number;
  
  // إحصائيات الأداء
  completionRate: number;
  successRate: number;
  disputeRate: number;
  averageTradeTime: number; // بالدقائق
  averageResponseTime: number; // بالدقائق
  
  // إحصائيات زمنية
  joinDate: string;
  lastActivity: string;
  totalDaysActive: number;
  
  // إحصائيات العروض
  totalOffers: number;
  activeOffers: number;
  expiredOffers: number;
  
  // إحصائيات التحقق
  isVerified: boolean;
  verificationLevel: 'none' | 'basic' | 'advanced' | 'premium' | 'full';

  // إحصائيات إضافية
  favoritePartners: string[];
  topCurrencies: Array<{ currency: string; volume: string; count: number }>;
  tradingHours: Array<{ hour: number; count: number }>;
  tradingDays: Array<{ day: string; count: number }>;
}

export interface UserStatsFilters {
  period?: 'week' | 'month' | 'quarter' | 'year' | 'all';
  currency?: string;
  tradeType?: 'buy' | 'sell' | 'all';
  status?: 'completed' | 'active' | 'all';
}

interface UseUserStatsOptions {
  autoRefresh?: boolean;
  refreshInterval?: number; // بالميلي ثانية
  filters?: UserStatsFilters;
}

interface UseUserStatsReturn {
  stats: UserStats | null;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  updateFilters: (filters: UserStatsFilters) => void;
  filters: UserStatsFilters;
}

/**
 * Hook لإدارة إحصائيات المستخدم
 */
export function useUserStats(options: UseUserStatsOptions = {}): UseUserStatsReturn {
  const { user } = useAuth();
  const [stats, setStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<UserStatsFilters>(options.filters || {});

  const {
    autoRefresh = false,
    refreshInterval = 5 * 60 * 1000 // 5 دقائق افتراضياً
  } = options;

  /**
   * جلب إحصائيات المستخدم من API
   */
  const fetchUserStats = useCallback(async () => {
    if (!user?.id) {
      setError('معرف المستخدم غير متاح');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // بناء معاملات الاستعلام
      const queryParams = new URLSearchParams({
        user_id: user.id.toString(),
        ...filters
      });

      // استدعاء API الحقيقي
      const response = await apiGet(`users/stats.php?${queryParams.toString()}`);

      if (!response.success) {
        throw new Error(response.message || 'فشل في جلب الإحصائيات');
      }

      // تحويل البيانات من API إلى تنسيق UserStats
      const apiData = response.data;
      const userStats: UserStats = {
        // إحصائيات أساسية
        totalTrades: parseInt(apiData.total_trades) || 0,
        completedTrades: parseInt(apiData.completed_trades) || 0,
        activeTrades: parseInt(apiData.active_trades) || 0,
        cancelledTrades: parseInt(apiData.cancelled_trades) || 0,
        disputedTrades: parseInt(apiData.disputed_trades) || 0,

        // إحصائيات الحجم
        totalVolume: apiData.total_volume || '0.00',
        monthlyVolume: apiData.monthly_volume || '0.00',
        weeklyVolume: apiData.weekly_volume || '0.00',
        averageTradeAmount: apiData.average_trade_amount || '0.00',

        // إحصائيات التقييم
        rating: parseFloat(apiData.rating) || 0,
        ratingCount: parseInt(apiData.rating_count) || 0,
        positiveReviews: parseInt(apiData.positive_reviews) || 0,
        negativeReviews: parseInt(apiData.negative_reviews) || 0,

        // إحصائيات الأداء
        completionRate: parseFloat(apiData.completion_rate) || 0,
        successRate: parseFloat(apiData.success_rate) || 0,
        disputeRate: parseFloat(apiData.dispute_rate) || 0,
        averageTradeTime: parseInt(apiData.average_trade_time) || 0,
        averageResponseTime: parseInt(apiData.average_response_time) || 0,

        // إحصائيات زمنية
        joinDate: apiData.join_date || '',
        lastActivity: apiData.last_activity || '',
        totalDaysActive: parseInt(apiData.total_days_active) || 0,

        // إحصائيات العروض
        totalOffers: parseInt(apiData.total_offers) || 0,
        activeOffers: parseInt(apiData.active_offers) || 0,
        expiredOffers: parseInt(apiData.expired_offers) || 0,

        // إحصائيات التحقق
        isVerified: Boolean(apiData.is_verified),
        verificationLevel: apiData.verification_level || 'none',

        // إحصائيات إضافية
        favoritePartners: apiData.favorite_partners || [],
        topCurrencies: apiData.top_currencies || [],
        tradingHours: apiData.trading_hours || [],
        tradingDays: apiData.trading_days || []
      };

      setStats(userStats);
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('Error fetching user stats:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.id, filters]);

  /**
   * تحديث المرشحات
   */
  const updateFilters = useCallback((newFilters: UserStatsFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  /**
   * تحديث الإحصائيات يدوياً
   */
  const refresh = useCallback(async () => {
    await fetchUserStats();
  }, [fetchUserStats]);

  // جلب الإحصائيات عند تحميل المكون أو تغيير المرشحات
  useEffect(() => {
    if (user?.id) {
      fetchUserStats();
    }
  }, [fetchUserStats]);

  // التحديث التلقائي
  useEffect(() => {
    if (!autoRefresh || !user?.id) return;

    const interval = setInterval(() => {
      fetchUserStats();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchUserStats, user?.id]);

  return {
    stats,
    loading,
    error,
    refresh,
    updateFilters,
    filters
  };
}

/**
 * Hook مبسط للحصول على إحصائيات أساسية فقط
 */
export function useBasicUserStats() {
  const { stats, loading, error } = useUserStats();

  return {
    totalTrades: stats?.totalTrades || 0,
    completedTrades: stats?.completedTrades || 0,
    totalVolume: stats?.totalVolume || '0.00',
    rating: stats?.rating || 0,
    completionRate: stats?.completionRate || 0,
    loading,
    error
  };
}

export default useUserStats;
