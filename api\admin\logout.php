<?php
/**
 * API endpoint لتسجيل خروج الإدارة
 * Admin Logout API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Method not allowed'
    ]);
    exit();
}

// Include required files
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../middleware/admin_auth.php';
require_once __DIR__ . '/../../includes/admin-auth.php';
require_once __DIR__ . '/../helpers/ResponseHelper.php';

try {
    // Validate request method
    ResponseHelper::validateMethod(['POST']);

    // التحقق من المصادقة الإدارية
    $authResult = checkAdminAuth();

    if (!$authResult['success']) {
        // حتى لو فشلت المصادقة، نقوم بتنظيف الجلسات
        cleanupSessions();

        ResponseHelper::success(
            null,
            'تم تسجيل الخروج',
            'Logged out successfully'
        );
    }
    
    $adminId = $authResult['admin_id'];
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // تسجيل نشاط تسجيل الخروج
    try {
        logAdminActivity('logout', 'admin', $adminId, [
            'logout_time' => date('Y-m-d H:i:s'),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $logError) {
        error_log('Error logging admin logout activity: ' . $logError->getMessage());
    }
    
    // إلغاء جميع الجلسات النشطة للمدير
    $deactivateStmt = $connection->prepare("
        UPDATE admin_sessions 
        SET is_active = 0, last_activity = NOW() 
        WHERE admin_user_id = ? AND is_active = 1
    ");
    $deactivateStmt->execute([$adminId]);
    
    // تنظيف جلسة PHP
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // مسح متغيرات الجلسة الإدارية
    unset($_SESSION['admin_id']);
    unset($_SESSION['admin_logged_in']);
    unset($_SESSION['admin_role']);
    unset($_SESSION['login_time']);
    
    // تدمير الجلسة إذا كانت تحتوي فقط على بيانات إدارية
    $hasNonAdminData = false;
    foreach ($_SESSION as $key => $value) {
        if (!str_starts_with($key, 'admin_')) {
            $hasNonAdminData = true;
            break;
        }
    }
    
    if (!$hasNonAdminData) {
        session_destroy();
    }
    
    ResponseHelper::success(
        ['sessions_terminated' => true],
        'تم تسجيل الخروج بنجاح',
        'Successfully logged out'
    );
    
} catch (Exception $e) {
    error_log('Admin logout error: ' . $e->getMessage());

    // في حالة الخطأ، نقوم بتنظيف الجلسات على أي حال
    cleanupSessions();

    ResponseHelper::success(
        ['note' => 'تم التنظيف رغم وجود خطأ'],
        'تم تسجيل الخروج',
        'Logged out'
    );
} catch (PDOException $e) {
    error_log('Database error in admin logout.php: ' . $e->getMessage());

    // في حالة خطأ قاعدة البيانات، نقوم بتنظيف الجلسات المحلية
    cleanupSessions();

    ResponseHelper::success(
        ['note' => 'خطأ في قاعدة البيانات'],
        'تم تسجيل الخروج محلياً',
        'Logged out locally'
    );
}

/**
 * تنظيف الجلسات المحلية
 * Cleanup local sessions
 */
function cleanupSessions() {
    try {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // مسح متغيرات الجلسة الإدارية
        unset($_SESSION['admin_id']);
        unset($_SESSION['admin_logged_in']);
        unset($_SESSION['admin_role']);
        unset($_SESSION['login_time']);
        
        // إذا لم تكن هناك بيانات أخرى، تدمير الجلسة
        if (empty($_SESSION)) {
            session_destroy();
        }
    } catch (Exception $e) {
        error_log('Error cleaning up sessions: ' . $e->getMessage());
    }
}
?>
