// خدمة الإشعارات الذكية المتقدمة
import { notificationService, NotificationType } from './notificationService';

// أنواع إشعارات العقد الذكي
export type ContractEventType = 'TradeCreated' | 'BuyerJoined' | 'PaymentSent' | 'PaymentConfirmed' | 'TradeCompleted' | 'TradeCancelled' | 'TradeDisputed' | 'DisputeResolved';

// أولويات الإشعارات
export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';

// أنواع الإشعارات الذكية
export type SmartNotificationType = 'trade' | 'security' | 'system' | 'payment' | 'kyc' | 'admin' | 'contract';

// بيانات الإشعار الذكي
export interface SmartNotificationData {
  id: string;
  type: SmartNotificationType;
  priority: NotificationPriority;
  title: string;
  message: string;
  userId?: number;
  tradeId?: number;
  contractEventType?: ContractEventType;
  actionUrl?: string;
  actionLabel?: string;
  timestamp: Date;
  isRead: boolean;
  isPersistent: boolean;
  metadata?: Record<string, any>;
  expiresAt?: Date;
}

// إعدادات الإشعارات للمستخدم
export interface NotificationPreferences {
  userId: number;
  browserNotifications: boolean;
  emailNotifications: boolean;
  tradeNotifications: boolean;
  securityNotifications: boolean;
  systemNotifications: boolean;
  contractNotifications: boolean;
  soundEnabled: boolean;
  quietHours: {
    enabled: boolean;
    startTime: string; // HH:mm
    endTime: string; // HH:mm
  };
  priorities: {
    low: boolean;
    medium: boolean;
    high: boolean;
    urgent: boolean;
  };
  languages: string[]; // ['ar', 'en']
}

// إحصائيات الإشعارات
export interface NotificationStats {
  total: number;
  unread: number;
  byType: Record<SmartNotificationType, number>;
  byPriority: Record<NotificationPriority, number>;
  last24Hours: number;
  lastWeek: number;
}

class SmartNotificationService {
  private notificationQueue: SmartNotificationData[] = [];
  private userPreferences: Map<number, NotificationPreferences> = new Map();
  private isInitialized = false;
  private eventListeners: Map<string, Function[]> = new Map();
  private notificationHistory: SmartNotificationData[] = [];
  private maxHistorySize = 1000;

  /**
   * تهيئة الخدمة
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // طلب إذن الإشعارات من المتصفح
      await this.requestBrowserPermission();
      
      // تحميل إعدادات المستخدم
      await this.loadUserPreferences();
      
      // بدء معالجة طابور الإشعارات
      this.startQueueProcessor();
      
      this.isInitialized = true;
      console.log('✅ تم تهيئة خدمة الإشعارات الذكية');
    } catch (error) {
      console.error('❌ خطأ في تهيئة خدمة الإشعارات الذكية:', error);
    }
  }

  /**
   * طلب إذن الإشعارات من المتصفح
   */
  private async requestBrowserPermission(): Promise<void> {
    if (!('Notification' in window)) {
      console.warn('المتصفح لا يدعم إشعارات المتصفح');
      return;
    }

    if (Notification.permission === 'default') {
      const permission = await Notification.requestPermission();
      console.log('إذن إشعارات المتصفح:', permission);
    }
  }

  /**
   * تحميل إعدادات المستخدم
   */
  private async loadUserPreferences(): Promise<void> {
    try {
      // محاولة تحميل الإعدادات من localStorage أولاً
      const savedPrefs = localStorage.getItem('notificationPreferences');
      if (savedPrefs) {
        const prefs = JSON.parse(savedPrefs);
        this.userPreferences.set(prefs.userId, prefs);
      }

      // TODO: تحميل الإعدادات من API
      // const response = await fetch('/api/notifications/preferences');
      // const data = await response.json();
    } catch (error) {
      console.error('خطأ في تحميل إعدادات الإشعارات:', error);
    }
  }

  /**
   * بدء معالج طابور الإشعارات
   */
  private startQueueProcessor(): void {
    setInterval(() => {
      this.processNotificationQueue();
    }, 1000); // معالجة كل ثانية
  }

  /**
   * معالجة طابور الإشعارات
   */
  private processNotificationQueue(): void {
    if (this.notificationQueue.length === 0) return;

    const now = new Date();
    const toProcess = this.notificationQueue.splice(0, 5); // معالجة 5 إشعارات كحد أقصى

    for (const notification of toProcess) {
      // التحقق من انتهاء صلاحية الإشعار
      if (notification.expiresAt && notification.expiresAt < now) {
        continue;
      }

      // التحقق من إعدادات المستخدم
      if (!this.shouldSendNotification(notification)) {
        continue;
      }

      // إرسال الإشعار
      this.sendNotification(notification);
    }
  }

  /**
   * التحقق من إمكانية إرسال الإشعار
   */
  private shouldSendNotification(notification: SmartNotificationData): boolean {
    if (!notification.userId) return true;

    const prefs = this.userPreferences.get(notification.userId);
    if (!prefs) return true;

    // التحقق من الساعات الهادئة
    if (prefs.quietHours.enabled && this.isQuietHour(prefs.quietHours)) {
      return notification.priority === 'urgent';
    }

    // التحقق من إعدادات النوع
    switch (notification.type) {
      case 'trade':
        return prefs.tradeNotifications;
      case 'security':
        return prefs.securityNotifications;
      case 'system':
        return prefs.systemNotifications;
      case 'contract':
        return prefs.contractNotifications;
      default:
        return true;
    }
  }

  /**
   * التحقق من الساعات الهادئة
   */
  private isQuietHour(quietHours: { startTime: string; endTime: string }): boolean {
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    return currentTime >= quietHours.startTime && currentTime <= quietHours.endTime;
  }

  /**
   * إرسال إشعار
   */
  private async sendNotification(notification: SmartNotificationData): Promise<void> {
    try {
      // إشعار toast
      this.sendToastNotification(notification);

      // إشعار المتصفح
      if (this.shouldSendBrowserNotification(notification)) {
        this.sendBrowserNotification(notification);
      }

      // حفظ في قاعدة البيانات
      await this.saveToDatabase(notification);

      // إضافة للتاريخ
      this.addToHistory(notification);

      // إطلاق الأحداث
      this.emitEvent('notificationSent', notification);

    } catch (error) {
      console.error('خطأ في إرسال الإشعار:', error);
    }
  }

  /**
   * إرسال إشعار toast
   */
  private sendToastNotification(notification: SmartNotificationData): void {
    const options = {
      duration: this.getToastDuration(notification.priority),
      icon: this.getNotificationIcon(notification.type, notification.priority)
    };

    switch (notification.priority) {
      case 'urgent':
        notificationService.error(notification.message, options);
        break;
      case 'high':
        notificationService.warning(notification.message, options);
        break;
      case 'medium':
        notificationService.info(notification.message, options);
        break;
      case 'low':
        notificationService.success(notification.message, options);
        break;
    }
  }

  /**
   * الحصول على مدة عرض الإشعار
   */
  private getToastDuration(priority: NotificationPriority): number {
    switch (priority) {
      case 'urgent': return 10000; // 10 ثوانٍ
      case 'high': return 7000;    // 7 ثوانٍ
      case 'medium': return 5000;  // 5 ثوانٍ
      case 'low': return 3000;     // 3 ثوانٍ
      default: return 4000;
    }
  }

  /**
   * الحصول على أيقونة الإشعار
   */
  private getNotificationIcon(type: SmartNotificationType, priority: NotificationPriority): string {
    const icons = {
      trade: priority === 'urgent' ? '🚨' : '💰',
      security: '🔒',
      system: '⚙️',
      payment: '💳',
      kyc: '👤',
      admin: '👨‍💼',
      contract: '📄'
    };

    return icons[type] || 'ℹ️';
  }

  /**
   * التحقق من إرسال إشعار المتصفح
   */
  private shouldSendBrowserNotification(notification: SmartNotificationData): boolean {
    if (!('Notification' in window) || Notification.permission !== 'granted') {
      return false;
    }

    const prefs = notification.userId ? this.userPreferences.get(notification.userId) : null;
    if (prefs && !prefs.browserNotifications) {
      return false;
    }

    // إرسال إشعارات المتصفح للأولويات العالية فقط
    return notification.priority === 'urgent' || notification.priority === 'high';
  }

  /**
   * إرسال إشعار المتصفح
   */
  private sendBrowserNotification(notification: SmartNotificationData): void {
    try {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: notification.id,
        requireInteraction: notification.priority === 'urgent',
        silent: false
      });

      // إضافة مستمع للنقر
      browserNotification.onclick = () => {
        window.focus();
        if (notification.actionUrl) {
          window.location.href = notification.actionUrl;
        }
        browserNotification.close();
      };

      // إغلاق تلقائي بعد مدة
      setTimeout(() => {
        browserNotification.close();
      }, this.getToastDuration(notification.priority));

    } catch (error) {
      console.error('خطأ في إرسال إشعار المتصفح:', error);
    }
  }

  /**
   * حفظ الإشعار في قاعدة البيانات
   */
  private async saveToDatabase(notification: SmartNotificationData): Promise<void> {
    try {
      const response = await fetch('/api/notifications/index.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: notification.userId,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          data: {
            priority: notification.priority,
            tradeId: notification.tradeId,
            contractEventType: notification.contractEventType,
            metadata: notification.metadata
          },
          action_url: notification.actionUrl
        })
      });

      if (!response.ok) {
        throw new Error('فشل في حفظ الإشعار في قاعدة البيانات');
      }
    } catch (error) {
      console.error('خطأ في حفظ الإشعار:', error);
    }
  }

  /**
   * إضافة الإشعار للتاريخ
   */
  private addToHistory(notification: SmartNotificationData): void {
    this.notificationHistory.unshift(notification);

    // الحفاظ على حجم التاريخ
    if (this.notificationHistory.length > this.maxHistorySize) {
      this.notificationHistory = this.notificationHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * إطلاق حدث
   */
  private emitEvent(eventName: string, data: any): void {
    const listeners = this.eventListeners.get(eventName) || [];
    listeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error(`خطأ في مستمع الحدث ${eventName}:`, error);
      }
    });
  }

  // ===== الواجهة العامة =====

  /**
   * إنشاء إشعار ذكي جديد
   */
  createSmartNotification(data: Omit<SmartNotificationData, 'id' | 'timestamp' | 'isRead'>): string {
    const notification: SmartNotificationData = {
      ...data,
      id: this.generateNotificationId(),
      timestamp: new Date(),
      isRead: false
    };

    // إضافة للطابور
    this.notificationQueue.push(notification);

    return notification.id;
  }

  /**
   * إنشاء إشعار لحدث العقد الذكي
   */
  createContractEventNotification(
    eventType: ContractEventType,
    tradeId: number,
    userId?: number,
    additionalData?: Record<string, any>
  ): string {
    const { title, message, priority } = this.getContractEventDetails(eventType, tradeId, additionalData);

    return this.createSmartNotification({
      type: 'contract',
      priority,
      title,
      message,
      userId,
      tradeId,
      contractEventType: eventType,
      isPersistent: priority === 'urgent',
      metadata: additionalData,
      actionUrl: `/trade/${tradeId}`
    });
  }

  /**
   * الحصول على تفاصيل حدث العقد الذكي
   */
  private getContractEventDetails(
    eventType: ContractEventType,
    tradeId: number,
    data?: Record<string, any>
  ): { title: string; message: string; priority: NotificationPriority } {
    switch (eventType) {
      case 'TradeCreated':
        return {
          title: 'صفقة جديدة',
          message: `تم إنشاء صفقة جديدة #${tradeId}`,
          priority: 'medium'
        };

      case 'BuyerJoined':
        return {
          title: 'انضمام مشتري',
          message: `انضم مشتري للصفقة #${tradeId}`,
          priority: 'high'
        };

      case 'PaymentSent':
        return {
          title: 'تم إرسال الدفع',
          message: `تم إرسال الدفع للصفقة #${tradeId}`,
          priority: 'high'
        };

      case 'PaymentConfirmed':
        return {
          title: 'تأكيد الدفع',
          message: `تم تأكيد الدفع للصفقة #${tradeId}`,
          priority: 'high'
        };

      case 'TradeCompleted':
        return {
          title: 'اكتمال الصفقة',
          message: `تمت الصفقة #${tradeId} بنجاح! 🎉`,
          priority: 'high'
        };

      case 'TradeCancelled':
        return {
          title: 'إلغاء الصفقة',
          message: `تم إلغاء الصفقة #${tradeId}`,
          priority: 'medium'
        };

      case 'TradeDisputed':
        return {
          title: 'نزاع على الصفقة',
          message: `تم فتح نزاع على الصفقة #${tradeId} ⚠️`,
          priority: 'urgent'
        };

      case 'DisputeResolved':
        return {
          title: 'حل النزاع',
          message: `تم حل النزاع للصفقة #${tradeId}`,
          priority: 'high'
        };

      default:
        return {
          title: 'حدث جديد',
          message: `حدث جديد للصفقة #${tradeId}`,
          priority: 'medium'
        };
    }
  }

  /**
   * توليد معرف فريد للإشعار
   */
  private generateNotificationId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * الحصول على إحصائيات الإشعارات
   */
  getNotificationStats(userId?: number): NotificationStats {
    const notifications = userId
      ? this.notificationHistory.filter(n => n.userId === userId)
      : this.notificationHistory;

    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const stats: NotificationStats = {
      total: notifications.length,
      unread: notifications.filter(n => !n.isRead).length,
      byType: {
        trade: 0,
        security: 0,
        system: 0,
        payment: 0,
        kyc: 0,
        admin: 0,
        contract: 0
      },
      byPriority: {
        low: 0,
        medium: 0,
        high: 0,
        urgent: 0
      },
      last24Hours: notifications.filter(n => n.timestamp >= last24Hours).length,
      lastWeek: notifications.filter(n => n.timestamp >= lastWeek).length
    };

    // حساب الإحصائيات حسب النوع والأولوية
    notifications.forEach(notification => {
      stats.byType[notification.type]++;
      stats.byPriority[notification.priority]++;
    });

    return stats;
  }

  /**
   * إضافة مستمع للأحداث
   */
  addEventListener(eventName: string, listener: Function): void {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, []);
    }
    this.eventListeners.get(eventName)!.push(listener);
  }

  /**
   * إزالة مستمع للأحداث
   */
  removeEventListener(eventName: string, listener: Function): void {
    const listeners = this.eventListeners.get(eventName);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * تحديث إعدادات المستخدم
   */
  async updateUserPreferences(userId: number, preferences: Partial<NotificationPreferences>): Promise<void> {
    try {
      const currentPrefs = this.userPreferences.get(userId) || this.getDefaultPreferences(userId);
      const updatedPrefs = { ...currentPrefs, ...preferences };

      this.userPreferences.set(userId, updatedPrefs);

      // حفظ في localStorage
      localStorage.setItem('notificationPreferences', JSON.stringify(updatedPrefs));

      // TODO: حفظ في قاعدة البيانات
      // await this.savePreferencesToDatabase(updatedPrefs);

    } catch (error) {
      console.error('خطأ في تحديث إعدادات الإشعارات:', error);
    }
  }

  /**
   * الحصول على الإعدادات الافتراضية
   */
  private getDefaultPreferences(userId: number): NotificationPreferences {
    return {
      userId,
      browserNotifications: true,
      emailNotifications: true,
      tradeNotifications: true,
      securityNotifications: true,
      systemNotifications: true,
      contractNotifications: true,
      soundEnabled: true,
      quietHours: {
        enabled: false,
        startTime: '22:00',
        endTime: '08:00'
      },
      priorities: {
        low: true,
        medium: true,
        high: true,
        urgent: true
      },
      languages: ['ar', 'en']
    };
  }

  /**
   * تنظيف الإشعارات المنتهية الصلاحية
   */
  cleanupExpiredNotifications(): void {
    const now = new Date();
    this.notificationQueue = this.notificationQueue.filter(
      notification => !notification.expiresAt || notification.expiresAt > now
    );

    this.notificationHistory = this.notificationHistory.filter(
      notification => !notification.expiresAt || notification.expiresAt > now
    );
  }

  /**
   * الحصول على تاريخ الإشعارات
   */
  getNotificationHistory(userId?: number, limit = 50): SmartNotificationData[] {
    const notifications = userId
      ? this.notificationHistory.filter(n => n.userId === userId)
      : this.notificationHistory;

    return notifications.slice(0, limit);
  }

  /**
   * تمييز الإشعار كمقروء
   */
  markAsRead(notificationId: string): void {
    const notification = this.notificationHistory.find(n => n.id === notificationId);
    if (notification) {
      notification.isRead = true;
      this.emitEvent('notificationRead', notification);
    }
  }

  /**
   * تمييز جميع الإشعارات كمقروءة
   */
  markAllAsRead(userId?: number): void {
    const notifications = userId
      ? this.notificationHistory.filter(n => n.userId === userId)
      : this.notificationHistory;

    notifications.forEach(notification => {
      notification.isRead = true;
    });

    this.emitEvent('allNotificationsRead', { userId });
  }
}

// إنشاء مثيل واحد من الخدمة
export const smartNotificationService = new SmartNotificationService();

// تصدير الخدمة كـ default أيضاً
export default smartNotificationService;
