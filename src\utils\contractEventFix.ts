/**
 * إصلاح سريع لمشكلة أحداث العقد الذكي
 * Quick Fix for Smart Contract Events Issue
 */

import { ethers } from 'ethers';

// إعدادات محسنة لتجنب RPC limits
const OPTIMIZED_CONFIG = {
  MAX_BLOCKS_PER_REQUEST: 50, // تقليل عدد البلوكات
  RETRY_ATTEMPTS: 2,
  RETRY_DELAY: 2000, // 2 ثانية
  RATE_LIMIT_DELAY: 1000, // ثانية واحدة بين الطلبات
  DISABLE_EVENT_MONITORING: true, // تعطيل مراقبة الأحداث مؤقتاً
};

/**
 * تعطيل مراقبة الأحداث مؤقتاً
 */
export function disableEventMonitoring(): void {
  console.log('🚫 تم تعطيل مراقبة أحداث العقد الذكي مؤقتاً');
  
  // تعطيل جميع مستمعي الأحداث
  if (typeof window !== 'undefined') {
    window.addEventListener('error', (event) => {
      if (event.error?.message?.includes('limit exceeded')) {
        event.preventDefault();
        console.warn('🚨 تم اكتشاف خطأ RPC limit - تم تجاهله');
      }
    });

    window.addEventListener('unhandledrejection', (event) => {
      if (event.reason?.message?.includes('limit exceeded')) {
        event.preventDefault();
        console.warn('🚨 تم اكتشاف خطأ RPC limit - تم تجاهله');
      }
    });
  }
}

/**
 * إصلاح سريع للعقد الذكي
 */
export class QuickContractFix {
  private static instance: QuickContractFix;
  private isFixed = false;

  static getInstance(): QuickContractFix {
    if (!QuickContractFix.instance) {
      QuickContractFix.instance = new QuickContractFix();
    }
    return QuickContractFix.instance;
  }

  /**
   * تطبيق الإصلاح السريع
   */
  applyQuickFix(): void {
    if (this.isFixed) {
      console.log('✅ الإصلاح السريع مطبق بالفعل');
      return;
    }

    console.log('🔧 تطبيق الإصلاح السريع لمشكلة RPC...');

    // 1. تعطيل مراقبة الأحداث
    disableEventMonitoring();

    // 2. إعداد معالج أخطاء عام
    this.setupGlobalErrorHandler();

    // 3. تحسين إعدادات ethers
    this.optimizeEthersSettings();

    this.isFixed = true;
    console.log('✅ تم تطبيق الإصلاح السريع بنجاح');
  }

  /**
   * إعداد معالج الأخطاء العام
   */
  private setupGlobalErrorHandler(): void {
    const originalConsoleError = console.error;
    console.error = (...args: any[]) => {
      const message = args.join(' ');
      
      // تجاهل أخطاء RPC limit
      if (message.includes('limit exceeded') || 
          message.includes('Internal JSON-RPC error') ||
          message.includes('eth_getLogs')) {
        console.warn('🚨 RPC Error detected and ignored:', message);
        return;
      }
      
      // عرض الأخطاء الأخرى بشكل طبيعي
      originalConsoleError.apply(console, args);
    };
  }

  /**
   * تحسين إعدادات ethers
   */
  private optimizeEthersSettings(): void {
    // تعطيل polling للأحداث
    if (typeof window !== 'undefined' && window.ethereum) {
      try {
        // تقليل تكرار polling
        const provider = new ethers.BrowserProvider(window.ethereum);
        provider.pollingInterval = 30000; // 30 ثانية بدلاً من 4 ثواني
      } catch (error) {
        console.warn('تعذر تحسين إعدادات ethers:', error);
      }
    }
  }

  /**
   * إلغاء الإصلاح
   */
  removeQuickFix(): void {
    console.log('🔄 إلغاء الإصلاح السريع...');
    this.isFixed = false;
    
    // إعادة تعيين console.error
    delete (console as any).error;
    
    console.log('✅ تم إلغاء الإصلاح السريع');
  }
}

/**
 * دالة مساعدة لتطبيق الإصلاح السريع
 */
export function applyContractEventQuickFix(): void {
  const fix = QuickContractFix.getInstance();
  fix.applyQuickFix();
}

/**
 * دالة مساعدة لإلغاء الإصلاح
 */
export function removeContractEventQuickFix(): void {
  const fix = QuickContractFix.getInstance();
  fix.removeQuickFix();
}

/**
 * فحص ما إذا كان الخطأ متعلق بـ RPC limit
 */
export function isRPCLimitError(error: any): boolean {
  const message = error?.message || String(error);
  return message.includes('limit exceeded') || 
         message.includes('Internal JSON-RPC error') ||
         message.includes('eth_getLogs') ||
         error?.code === -32005;
}

/**
 * معالج آمن للطلبات
 */
export async function safeContractCall<T>(
  operation: () => Promise<T>,
  fallbackValue?: T
): Promise<T | undefined> {
  try {
    return await operation();
  } catch (error) {
    if (isRPCLimitError(error)) {
      console.warn('🚨 RPC limit error detected, returning fallback value');
      return fallbackValue;
    }
    
    // إعادة رمي الأخطاء الأخرى
    throw error;
  }
}

/**
 * تهيئة الإصلاح عند تحميل الصفحة
 */
export function initializeContractEventFix(): void {
  if (typeof window !== 'undefined') {
    // تطبيق الإصلاح عند تحميل الصفحة
    window.addEventListener('load', () => {
      applyContractEventQuickFix();
    });

    // تطبيق الإصلاح فوراً إذا كانت الصفحة محملة بالفعل
    if (document.readyState === 'complete') {
      applyContractEventQuickFix();
    }
  }
}

// تصدير الإصلاح كـ default
export default {
  applyQuickFix: applyContractEventQuickFix,
  removeQuickFix: removeContractEventQuickFix,
  isRPCLimitError,
  safeContractCall,
  initialize: initializeContractEventFix
};
