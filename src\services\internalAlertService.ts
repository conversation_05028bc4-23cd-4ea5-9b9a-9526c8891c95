/**
 * خدمة التحذير المبكر الداخلي
 * Internal Early Warning Alert Service
 * 
 * نظام تحذير متقدم للمشرفين عند حدوث مشاكل في النظام
 * Advanced alert system for administrators when system issues occur
 */

interface AlertSettings {
  alertsEnabled: boolean;
  telegramBotToken: string;
  telegramChatId: string;
  emailAlertsEnabled: boolean;
  alertEmail: string;
  disputeThreshold: number;
  pendingTradeThreshold: number; // بالدقائق
  syncDelayThreshold: number; // بالثواني
  highVolumeThreshold: number;
  suspiciousActivityThreshold: number;
}

interface AlertData {
  type: 'dispute' | 'pending_trade' | 'sync_delay' | 'high_volume' | 'suspicious_activity' | 'system_error';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  details?: any;
  timestamp: string;
  resolved?: boolean;
}

class InternalAlertService {
  private settings: AlertSettings;
  private alertHistory: AlertData[] = [];
  private activeAlerts: Map<string, AlertData> = new Map();
  private checkInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.settings = this.getDefaultSettings();
    this.loadSettings();
    this.startMonitoring();
  }

  private getDefaultSettings(): AlertSettings {
    return {
      alertsEnabled: true,
      telegramBotToken: '',
      telegramChatId: '',
      emailAlertsEnabled: true,
      alertEmail: '<EMAIL>',
      disputeThreshold: 5,
      pendingTradeThreshold: 30,
      syncDelayThreshold: 300,
      highVolumeThreshold: 100000,
      suspiciousActivityThreshold: 10
    };
  }

  private async loadSettings(): Promise<void> {
    try {
      // تحميل الإعدادات من قاعدة البيانات أو localStorage
      const savedSettings = localStorage.getItem('internalAlertSettings');
      if (savedSettings) {
        this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
      }
    } catch (error) {
      console.error('Error loading alert settings:', error);
    }
  }

  public updateSettings(newSettings: Partial<AlertSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    localStorage.setItem('internalAlertSettings', JSON.stringify(this.settings));
    
    if (newSettings.alertsEnabled === false) {
      this.stopMonitoring();
    } else if (newSettings.alertsEnabled === true) {
      this.startMonitoring();
    }
  }

  private startMonitoring(): void {
    if (!this.settings.alertsEnabled || this.checkInterval) return;

    // فحص دوري كل دقيقة
    this.checkInterval = setInterval(() => {
      this.performSystemChecks();
    }, 60000);

    console.log('🚨 Internal Alert Service: Monitoring started');
  }

  private stopMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      console.log('🚨 Internal Alert Service: Monitoring stopped');
    }
  }

  private async performSystemChecks(): Promise<void> {
    try {
      await Promise.all([
        this.checkDisputes(),
        this.checkPendingTrades(),
        this.checkSyncStatus(),
        this.checkHighVolume(),
        this.checkSuspiciousActivity()
      ]);
    } catch (error) {
      console.error('Error performing system checks:', error);
      this.sendAlert({
        type: 'system_error',
        severity: 'high',
        title: 'خطأ في نظام المراقبة',
        message: `حدث خطأ أثناء فحص النظام: ${error}`,
        timestamp: new Date().toISOString()
      });
    }
  }

  private async checkDisputes(): Promise<void> {
    try {
      // فحص عدد النزاعات النشطة
      const response = await fetch('/api/admin/disputes-count.php');
      const data = await response.json();
      
      if (data.success && data.activeDisputes >= this.settings.disputeThreshold) {
        const alertKey = 'high_disputes';
        
        if (!this.activeAlerts.has(alertKey)) {
          this.sendAlert({
            type: 'dispute',
            severity: data.activeDisputes >= this.settings.disputeThreshold * 2 ? 'critical' : 'high',
            title: 'عدد نزاعات مرتفع',
            message: `يوجد ${data.activeDisputes} نزاع نشط (الحد الأقصى: ${this.settings.disputeThreshold})`,
            details: { activeDisputes: data.activeDisputes, threshold: this.settings.disputeThreshold },
            timestamp: new Date().toISOString()
          }, alertKey);
        }
      } else {
        this.resolveAlert('high_disputes');
      }
    } catch (error) {
      console.error('Error checking disputes:', error);
    }
  }

  private async checkPendingTrades(): Promise<void> {
    try {
      // فحص الصفقات المعلقة لفترة طويلة
      const response = await fetch('/api/admin/pending-trades.php');
      const data = await response.json();
      
      if (data.success && data.longPendingTrades?.length > 0) {
        const alertKey = 'long_pending_trades';
        
        if (!this.activeAlerts.has(alertKey)) {
          this.sendAlert({
            type: 'pending_trade',
            severity: 'medium',
            title: 'صفقات معلقة لفترة طويلة',
            message: `يوجد ${data.longPendingTrades.length} صفقة معلقة لأكثر من ${this.settings.pendingTradeThreshold} دقيقة`,
            details: { pendingTrades: data.longPendingTrades, threshold: this.settings.pendingTradeThreshold },
            timestamp: new Date().toISOString()
          }, alertKey);
        }
      } else {
        this.resolveAlert('long_pending_trades');
      }
    } catch (error) {
      console.error('Error checking pending trades:', error);
    }
  }

  private async checkSyncStatus(): Promise<void> {
    try {
      // فحص حالة المزامنة مع العقد الذكي
      const response = await fetch('/api/admin/sync-status.php');
      const data = await response.json();
      
      if (data.success && data.lastSyncDelay > this.settings.syncDelayThreshold) {
        const alertKey = 'sync_delay';
        
        if (!this.activeAlerts.has(alertKey)) {
          this.sendAlert({
            type: 'sync_delay',
            severity: data.lastSyncDelay > this.settings.syncDelayThreshold * 2 ? 'critical' : 'high',
            title: 'تأخر في المزامنة',
            message: `تأخرت المزامنة مع العقد الذكي لمدة ${Math.round(data.lastSyncDelay / 60)} دقيقة`,
            details: { delay: data.lastSyncDelay, threshold: this.settings.syncDelayThreshold },
            timestamp: new Date().toISOString()
          }, alertKey);
        }
      } else {
        this.resolveAlert('sync_delay');
      }
    } catch (error) {
      console.error('Error checking sync status:', error);
    }
  }

  private async checkHighVolume(): Promise<void> {
    try {
      // فحص حجم التداول العالي
      const response = await fetch('/api/admin/volume-stats.php');
      const data = await response.json();
      
      if (data.success && data.dailyVolume > this.settings.highVolumeThreshold) {
        const alertKey = 'high_volume';
        
        if (!this.activeAlerts.has(alertKey)) {
          this.sendAlert({
            type: 'high_volume',
            severity: 'medium',
            title: 'حجم تداول عالي',
            message: `حجم التداول اليومي وصل إلى ${data.dailyVolume.toLocaleString()} USDT`,
            details: { volume: data.dailyVolume, threshold: this.settings.highVolumeThreshold },
            timestamp: new Date().toISOString()
          }, alertKey);
        }
      }
    } catch (error) {
      console.error('Error checking volume:', error);
    }
  }

  private async checkSuspiciousActivity(): Promise<void> {
    try {
      // فحص النشاط المشبوه
      const response = await fetch('/api/admin/suspicious-activity.php');
      const data = await response.json();
      
      if (data.success && data.suspiciousEvents?.length >= this.settings.suspiciousActivityThreshold) {
        const alertKey = 'suspicious_activity';
        
        if (!this.activeAlerts.has(alertKey)) {
          this.sendAlert({
            type: 'suspicious_activity',
            severity: 'high',
            title: 'نشاط مشبوه',
            message: `تم رصد ${data.suspiciousEvents.length} حدث مشبوه في آخر ساعة`,
            details: { events: data.suspiciousEvents, threshold: this.settings.suspiciousActivityThreshold },
            timestamp: new Date().toISOString()
          }, alertKey);
        }
      } else {
        this.resolveAlert('suspicious_activity');
      }
    } catch (error) {
      console.error('Error checking suspicious activity:', error);
    }
  }

  private async sendAlert(alert: AlertData, alertKey?: string): Promise<void> {
    if (!this.settings.alertsEnabled) return;

    // إضافة التحذير للسجل
    this.alertHistory.push(alert);
    
    if (alertKey) {
      this.activeAlerts.set(alertKey, alert);
    }

    // إرسال عبر تلغرام
    if (this.settings.telegramBotToken && this.settings.telegramChatId) {
      await this.sendTelegramAlert(alert);
    }

    // إرسال عبر البريد الإلكتروني
    if (this.settings.emailAlertsEnabled && this.settings.alertEmail) {
      await this.sendEmailAlert(alert);
    }

    // إشعار في الواجهة
    this.showUINotification(alert);

    console.log('🚨 Alert sent:', alert);
  }

  private async sendTelegramAlert(alert: AlertData): Promise<void> {
    try {
      const emoji = this.getSeverityEmoji(alert.severity);
      const message = `${emoji} *${alert.title}*\n\n${alert.message}\n\n⏰ ${new Date(alert.timestamp).toLocaleString('ar-SA')}`;
      
      const response = await fetch(`https://api.telegram.org/bot${this.settings.telegramBotToken}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: this.settings.telegramChatId,
          text: message,
          parse_mode: 'Markdown'
        })
      });

      if (!response.ok) {
        throw new Error(`Telegram API error: ${response.status}`);
      }
    } catch (error) {
      console.error('Error sending Telegram alert:', error);
    }
  }

  private async sendEmailAlert(alert: AlertData): Promise<void> {
    try {
      const response = await fetch('/api/admin/send-alert-email.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: this.settings.alertEmail,
          alert: alert
        })
      });

      if (!response.ok) {
        throw new Error(`Email API error: ${response.status}`);
      }
    } catch (error) {
      console.error('Error sending email alert:', error);
    }
  }

  private showUINotification(alert: AlertData): void {
    // إشعار في واجهة المستخدم (للمشرفين المتصلين)
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('internalAlert', { detail: alert }));
    }
  }

  private resolveAlert(alertKey: string): void {
    if (this.activeAlerts.has(alertKey)) {
      const alert = this.activeAlerts.get(alertKey)!;
      alert.resolved = true;
      this.activeAlerts.delete(alertKey);
      
      console.log('✅ Alert resolved:', alertKey);
    }
  }

  private getSeverityEmoji(severity: string): string {
    switch (severity) {
      case 'critical': return '🔴';
      case 'high': return '🟠';
      case 'medium': return '🟡';
      case 'low': return '🟢';
      default: return '⚪';
    }
  }

  // واجهات عامة
  public getSettings(): AlertSettings {
    return { ...this.settings };
  }

  public getAlertHistory(): AlertData[] {
    return [...this.alertHistory];
  }

  public getActiveAlerts(): AlertData[] {
    return Array.from(this.activeAlerts.values());
  }

  public clearAlertHistory(): void {
    this.alertHistory = [];
  }

  public testAlert(): void {
    this.sendAlert({
      type: 'system_error',
      severity: 'low',
      title: 'اختبار نظام التحذير',
      message: 'هذا تحذير تجريبي للتأكد من عمل النظام بشكل صحيح',
      timestamp: new Date().toISOString()
    });
  }
}

// إنشاء مثيل واحد للخدمة
export const internalAlertService = new InternalAlertService();
export default internalAlertService;
