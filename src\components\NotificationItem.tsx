'use client';

import React, { useState } from 'react';
import {
  Bell,
  X,
  Check,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  DollarSign,
  Users,
  Shield,
  Settings,
  Clock,
  Eye,
  Trash2,
  ExternalLink,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { SmartNotificationData, NotificationPriority } from '@/services/smartNotificationService';

interface NotificationItemProps {
  notification: SmartNotificationData;
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
  onAction?: (notification: SmartNotificationData) => void;
  className?: string;
}

export default function NotificationItem({
  notification,
  onMarkAsRead,
  onDelete,
  onAction,
  className = ''
}: NotificationItemProps) {
  const { t } = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);

  /**
   * الحصول على أيقونة الإشعار
   */
  const getNotificationIcon = (type: string, priority: NotificationPriority) => {
    const iconClass = `w-5 h-5 ${
      priority === 'urgent' ? 'text-red-500' :
      priority === 'high' ? 'text-orange-500' :
      priority === 'medium' ? 'text-yellow-500' : 'text-blue-500'
    }`;

    switch (type) {
      case 'trade':
        return <DollarSign className={iconClass} />;
      case 'security':
        return <Shield className={iconClass} />;
      case 'system':
        return <Settings className={iconClass} />;
      case 'payment':
        return <CheckCircle className={iconClass} />;
      case 'kyc':
        return <Users className={iconClass} />;
      case 'admin':
        return <AlertTriangle className={iconClass} />;
      case 'contract':
        return <Bell className={iconClass} />;
      default:
        return <Info className={iconClass} />;
    }
  };

  /**
   * الحصول على لون الأولوية
   */
  const getPriorityColor = (priority: NotificationPriority) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'low':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  /**
   * تنسيق الوقت
   */
  const formatTime = (timestamp: Date | string | undefined | null) => {
    if (!timestamp) return 'الآن';

    const dateObj = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
    if (!dateObj || isNaN(dateObj.getTime())) return 'الآن';

    const now = new Date();
    const diff = now.getTime() - dateObj.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `منذ ${days} ${days === 1 ? 'يوم' : 'أيام'}`;
    } else if (hours > 0) {
      return `منذ ${hours} ${hours === 1 ? 'ساعة' : 'ساعات'}`;
    } else if (minutes > 0) {
      return `منذ ${minutes} ${minutes === 1 ? 'دقيقة' : 'دقائق'}`;
    } else {
      return 'الآن';
    }
  };

  /**
   * معالجة النقر على الإشعار
   */
  const handleNotificationClick = () => {
    if (!notification.isRead) {
      onMarkAsRead(notification.id);
    }

    if (notification.actionUrl && onAction) {
      onAction(notification);
    }
  };

  /**
   * معالجة النقر على الإجراء
   */
  const handleActionClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onAction) {
      onAction(notification);
    }
  };

  return (
    <div
      className={`
        relative bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 
        rounded-lg p-4 transition-all duration-200 hover:shadow-md
        ${!notification.isRead ? 'border-l-4 border-l-blue-500' : ''}
        ${notification.priority === 'urgent' ? 'ring-2 ring-red-200 dark:ring-red-800' : ''}
        ${className}
      `}
    >
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 rtl:space-x-reverse flex-1">
          {/* Icon */}
          <div className="flex-shrink-0 mt-1">
            {getNotificationIcon(notification.type, notification.priority)}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <h4 className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                {notification.title}
              </h4>
              
              {/* Priority Badge */}
              <span className={`
                inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                ${getPriorityColor(notification.priority)}
              `}>
                {notification.priority === 'urgent' && '🚨'}
                {notification.priority === 'high' && '⚠️'}
                {notification.priority === 'medium' && 'ℹ️'}
                {notification.priority === 'low' && '💡'}
                <span className="mr-1 rtl:mr-1 ltr:ml-1">
                  {notification.priority === 'urgent' ? 'حرج' :
                   notification.priority === 'high' ? 'عالي' :
                   notification.priority === 'medium' ? 'متوسط' : 'منخفض'}
                </span>
              </span>
            </div>

            <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
              {notification.message}
            </p>

            {/* Metadata */}
            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Clock className="w-3 h-3" />
                <span>{formatTime(notification.timestamp)}</span>
                
                {notification.tradeId && (
                  <>
                    <span>•</span>
                    <span>صفقة #{notification.tradeId}</span>
                  </>
                )}
                
                {notification.contractEventType && (
                  <>
                    <span>•</span>
                    <span>{notification.contractEventType}</span>
                  </>
                )}
              </div>

              {!notification.isRead && (
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              )}
            </div>

            {/* Expanded Details */}
            {isExpanded && notification.metadata && Object.keys(notification.metadata).length > 0 && (
              <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <h5 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                  تفاصيل إضافية:
                </h5>
                <div className="space-y-1">
                  {Object.entries(notification.metadata).map(([key, value]) => (
                    <div key={key} className="flex justify-between text-xs">
                      <span className="text-gray-600 dark:text-gray-400">{key}:</span>
                      <span className="text-gray-900 dark:text-white font-mono">
                        {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Actions */}
            {(notification.actionUrl || notification.metadata) && (
              <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  {notification.actionUrl && (
                    <button
                      onClick={handleActionClick}
                      className="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors"
                    >
                      <ExternalLink className="w-3 h-3 ml-1 rtl:ml-1 ltr:mr-1" />
                      {notification.actionLabel || 'عرض التفاصيل'}
                    </button>
                  )}
                </div>

                {notification.metadata && Object.keys(notification.metadata).length > 0 && (
                  <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="inline-flex items-center text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    {isExpanded ? (
                      <>
                        <ChevronUp className="w-3 h-3 ml-1 rtl:ml-1 ltr:mr-1" />
                        إخفاء التفاصيل
                      </>
                    ) : (
                      <>
                        <ChevronDown className="w-3 h-3 ml-1 rtl:ml-1 ltr:mr-1" />
                        عرض التفاصيل
                      </>
                    )}
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-1 rtl:space-x-reverse ml-2 rtl:ml-2 ltr:mr-2">
          {!notification.isRead && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onMarkAsRead(notification.id);
              }}
              className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              title="تمييز كمقروء"
            >
              <Check className="w-4 h-4" />
            </button>
          )}

          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete(notification.id);
            }}
            className="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
            title="حذف الإشعار"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Persistent Indicator */}
      {notification.isPersistent && (
        <div className="absolute top-2 left-2 rtl:left-2 ltr:right-2">
          <div className="w-2 h-2 bg-orange-500 rounded-full" title="إشعار مهم"></div>
        </div>
      )}
    </div>
  );
}
