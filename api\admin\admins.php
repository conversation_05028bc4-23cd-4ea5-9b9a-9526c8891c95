<?php
/**
 * API endpoint لإدارة المدراء
 * Admin Management API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();

    // التحقق من وجود الجداول المطلوبة
    $tablesExist = true;
    try {
        $connection->query("SELECT 1 FROM admin_users LIMIT 1");
    } catch (PDOException $e) {
        $tablesExist = false;
        error_log("admin_users table does not exist: " . $e->getMessage());
    }

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // في وضع التطوير، تخطي المصادقة
        $isDevelopment = (
            (isset($_SERVER['SERVER_NAME']) && in_array($_SERVER['SERVER_NAME'], ['localhost', '127.0.0.1'])) ||
            (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE === true)
        );

        if (!$isDevelopment) {
            // التحقق من صلاحيات الإدارة
            require_once __DIR__ . '/../middleware/admin_auth.php';
            $authResult = checkAdminAuth();

            if (!$authResult['success']) {
                http_response_code($authResult['code'] ?? 401);
                echo json_encode($authResult);
                exit();
            }

            // التحقق من صلاحية إدارة المدراء
            $permissionResult = checkAdminPermission('manage_admins', $authResult['admin_id']);
            if (!$permissionResult['success'] || !$permissionResult['has_permission']) {
                http_response_code(403);
                echo json_encode([
                    'success' => false,
                    'error' => 'ليس لديك صلاحية لإدارة المدراء',
                    'error_en' => 'You do not have permission to manage admins'
                ]);
                exit();
            }
        }

        // إذا لم تكن الجداول موجودة، إرجاع بيانات افتراضية
        if (!$tablesExist) {
            echo json_encode([
                'success' => true,
                'data' => [
                    [
                        'admin_id' => 1,
                        'user_id' => 1,
                        'username' => 'admin',
                        'email' => '<EMAIL>',
                        'full_name' => 'مدير النظام',
                        'wallet_address' => '******************************************',
                        'admin_role' => 'super_admin',
                        'permissions' => ['super_admin'],
                        'all_permissions' => ['super_admin', 'manage_users', 'manage_trades', 'resolve_disputes', 'manage_contracts', 'view_analytics', 'manage_settings', 'emergency_actions', 'manage_admins'],
                        'is_active' => true,
                        'is_verified' => true,
                        'created_at' => date('Y-m-d H:i:s'),
                        'last_login' => date('Y-m-d H:i:s')
                    ]
                ],
                'pagination' => [
                    'current_page' => 1,
                    'per_page' => 20,
                    'total' => 1,
                    'total_pages' => 1
                ],
                'summary' => [
                    'total_admins' => 1,
                    'active_admins' => 1,
                    'super_admins' => 1,
                    'locked_admins' => 0
                ]
            ]);
            exit();
        }

        // جلب قائمة المدراء المحسنة
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;

        $search = $_GET['search'] ?? '';
        $status = $_GET['status'] ?? 'all'; // all, active, inactive
        $role = $_GET['role'] ?? 'all'; // all, super_admin, admin, moderator, support

        // بناء الاستعلام المحسن
        $whereConditions = ['au.is_active IS NOT NULL']; // للتأكد من وجود المدير في جدول admin_users
        $params = [];

        if ($status === 'active') {
            $whereConditions[] = 'au.is_active = 1 AND u.is_active = 1';
        } elseif ($status === 'inactive') {
            $whereConditions[] = 'au.is_active = 0 OR u.is_active = 0';
        }

        if ($role !== 'all') {
            $whereConditions[] = 'au.admin_role = ?';
            $params[] = $role;
        }

        if (!empty($search)) {
            $whereConditions[] = '(u.username LIKE ? OR u.full_name LIKE ? OR u.email LIKE ?)';
            $searchTerm = "%$search%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // الحصول على العدد الإجمالي
        $countStmt = $connection->prepare("
            SELECT COUNT(*) as total
            FROM admin_users au
            JOIN users u ON au.user_id = u.id
            WHERE $whereClause
        ");
        $countStmt->execute($params);
        $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

        // جلب المدراء مع معلومات إضافية
        $stmt = $connection->prepare("
            SELECT au.id as admin_id, au.admin_role, au.permissions, au.department,
                   au.can_manage_users, au.can_manage_trades, au.can_resolve_disputes,
                   au.can_manage_contracts, au.can_view_analytics, au.can_manage_settings,
                   au.can_emergency_actions, au.can_manage_admins, au.session_timeout_hours,
                   au.two_factor_enabled, au.last_activity, au.failed_login_attempts,
                   au.locked_until, au.created_at as admin_created_at, au.notes,
                   u.id as user_id, u.username, u.email, u.full_name, u.wallet_address,
                   u.is_verified, u.is_active, u.created_at as user_created_at, u.last_login,
                   u.total_trades, u.completed_trades, u.rating
            FROM admin_users au
            JOIN users u ON au.user_id = u.id
            WHERE $whereClause
            ORDER BY au.created_at DESC
            LIMIT ? OFFSET ?
        ");

        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تحسين بيانات المدراء
        $enhancedAdmins = [];
        foreach ($admins as $admin) {
            $permissions = json_decode($admin['permissions'], true) ?: [];

            // إضافة الصلاحيات البوليانية
            $booleanPermissions = [];
            if ($admin['can_manage_users']) $booleanPermissions[] = 'manage_users';
            if ($admin['can_manage_trades']) $booleanPermissions[] = 'manage_trades';
            if ($admin['can_resolve_disputes']) $booleanPermissions[] = 'resolve_disputes';
            if ($admin['can_manage_contracts']) $booleanPermissions[] = 'manage_contracts';
            if ($admin['can_view_analytics']) $booleanPermissions[] = 'view_analytics';
            if ($admin['can_manage_settings']) $booleanPermissions[] = 'manage_settings';
            if ($admin['can_emergency_actions']) $booleanPermissions[] = 'emergency_actions';
            if ($admin['can_manage_admins']) $booleanPermissions[] = 'manage_admins';

            $enhancedAdmins[] = [
                'admin_id' => $admin['admin_id'],
                'user_id' => $admin['user_id'],
                'username' => $admin['username'],
                'email' => $admin['email'],
                'full_name' => $admin['full_name'],
                'wallet_address' => $admin['wallet_address'],
                'admin_role' => $admin['admin_role'],
                'department' => $admin['department'],
                'permissions' => $permissions,
                'all_permissions' => array_unique(array_merge($permissions, $booleanPermissions)),
                'session_timeout_hours' => $admin['session_timeout_hours'],
                'two_factor_enabled' => (bool)$admin['two_factor_enabled'],
                'is_verified' => (bool)$admin['is_verified'],
                'is_active' => (bool)$admin['is_active'],
                'last_activity' => $admin['last_activity'],
                'last_login' => $admin['last_login'],
                'failed_login_attempts' => $admin['failed_login_attempts'],
                'is_locked' => $admin['locked_until'] && strtotime($admin['locked_until']) > time(),
                'locked_until' => $admin['locked_until'],
                'total_trades' => (int)$admin['total_trades'],
                'completed_trades' => (int)$admin['completed_trades'],
                'rating' => (float)$admin['rating'],
                'admin_created_at' => $admin['admin_created_at'],
                'user_created_at' => $admin['user_created_at'],
                'notes' => $admin['notes']
            ];
        }
        
        echo json_encode([
            'success' => true,
            'data' => $enhancedAdmins,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => intval($totalCount),
                'total_pages' => ceil($totalCount / $limit)
            ],
            'summary' => [
                'total_admins' => intval($totalCount),
                'active_admins' => count(array_filter($enhancedAdmins, fn($a) => $a['is_active'])),
                'super_admins' => count(array_filter($enhancedAdmins, fn($a) => $a['admin_role'] === 'super_admin')),
                'locked_admins' => count(array_filter($enhancedAdmins, fn($a) => $a['is_locked']))
            ]
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // إضافة مدير جديد أو ترقية مستخدم لمدير
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $userId = $input['user_id'] ?? null;
        $action = $input['action'] ?? 'promote'; // promote, demote
        
        if (!$userId) {
            throw new Exception('معرف المستخدم مطلوب');
        }
        
        // التحقق من وجود المستخدم
        $stmt = $connection->prepare("SELECT id, is_admin, username FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            throw new Exception('المستخدم غير موجود');
        }
        
        if ($action === 'promote') {
            // ترقية لمدير
            if ($user['is_admin']) {
                throw new Exception('المستخدم مدير بالفعل');
            }
            
            $stmt = $connection->prepare("
                UPDATE users 
                SET is_admin = 1, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");
            $stmt->execute([$userId]);
            
            $message = "تم ترقية {$user['username']} لمدير بنجاح";
            
        } elseif ($action === 'demote') {
            // إلغاء صلاحيات الإدارة
            if (!$user['is_admin']) {
                throw new Exception('المستخدم ليس مديراً');
            }
            
            $stmt = $connection->prepare("
                UPDATE users 
                SET is_admin = 0, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");
            $stmt->execute([$userId]);
            
            $message = "تم إلغاء صلاحيات الإدارة من {$user['username']} بنجاح";
        } else {
            throw new Exception('إجراء غير صحيح');
        }
        
        echo json_encode([
            'success' => true,
            'message' => $message
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // تحديث حالة المدير (تفعيل/إلغاء تفعيل)
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $userId = $input['user_id'] ?? null;
        $isActive = $input['is_active'] ?? null;
        
        if (!$userId || $isActive === null) {
            throw new Exception('معرف المستخدم وحالة التفعيل مطلوبان');
        }
        
        // التحقق من أن المستخدم مدير
        $stmt = $connection->prepare("SELECT username FROM users WHERE id = ? AND is_admin = 1");
        $stmt->execute([$userId]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$admin) {
            throw new Exception('المدير غير موجود');
        }
        
        // تحديث الحالة
        $stmt = $connection->prepare("
            UPDATE users 
            SET is_active = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        ");
        $stmt->execute([$isActive ? 1 : 0, $userId]);
        
        $status = $isActive ? 'تفعيل' : 'إلغاء تفعيل';
        $message = "تم {$status} المدير {$admin['username']} بنجاح";
        
        echo json_encode([
            'success' => true,
            'message' => $message
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    error_log('Database error in admin/admins.php: ' . $e->getMessage());
}
?>
