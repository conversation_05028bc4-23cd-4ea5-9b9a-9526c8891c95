'use client';

import { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  Users, 
  DollarSign, 
  Clock, 
  Star,
  Activity,
  Shield,
  Zap,
  Award,
  Globe
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';

interface StatMetric {
  id: string;
  icon: React.ElementType;
  label: string;
  value: string;
  growth: string;
  color: string;
  bgColor: string;
  animatedValue: number;
  suffix: string;
  prefix: string;
}

export default function PlatformStatsSection() {
  const { t } = useTranslation();
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });
  const [animatedStats, setAnimatedStats] = useState<Record<string, number>>({});

  const statsMetrics: StatMetric[] = [
    {
      id: 'totalTrades',
      icon: TrendingUp,
      label: t('home.platformStats.metrics.totalTrades.label'),
      value: '50,000+',
      growth: t('home.platformStats.metrics.totalTrades.growth'),
      color: 'text-blue-600',
      bgColor: 'from-blue-500 to-blue-600',
      animatedValue: 50000,
      suffix: '+',
      prefix: ''
    },
    {
      id: 'totalUsers',
      icon: Users,
      label: t('home.platformStats.metrics.totalUsers.label'),
      value: '12,500+',
      growth: t('home.platformStats.metrics.totalUsers.growth'),
      color: 'text-green-600',
      bgColor: 'from-green-500 to-green-600',
      animatedValue: 12500,
      suffix: '+',
      prefix: ''
    },
    {
      id: 'totalVolume',
      icon: DollarSign,
      label: t('home.platformStats.metrics.totalVolume.label'),
      value: '$2.5M+',
      growth: t('home.platformStats.metrics.totalVolume.growth'),
      color: 'text-purple-600',
      bgColor: 'from-purple-500 to-purple-600',
      animatedValue: 2.5,
      suffix: 'M+',
      prefix: '$'
    },
    {
      id: 'successRate',
      icon: Shield,
      label: t('home.platformStats.metrics.successRate.label'),
      value: '99.8%',
      growth: t('home.platformStats.metrics.successRate.growth'),
      color: 'text-emerald-600',
      bgColor: 'from-emerald-500 to-emerald-600',
      animatedValue: 99.8,
      suffix: '%',
      prefix: ''
    },
    {
      id: 'averageTime',
      icon: Clock,
      label: t('home.platformStats.metrics.averageTime.label'),
      value: t('home.platformStats.metrics.averageTime.value'),
      growth: t('home.platformStats.metrics.averageTime.growth'),
      color: 'text-orange-600',
      bgColor: 'from-orange-500 to-orange-600',
      animatedValue: 15,
      suffix: t('home.platformStats.metrics.averageTime.suffix'),
      prefix: ''
    },
    {
      id: 'onlineUsers',
      icon: Activity,
      label: t('home.platformStats.metrics.onlineUsers.label'),
      value: '1,250+',
      growth: t('home.platformStats.metrics.onlineUsers.growth'),
      color: 'text-red-600',
      bgColor: 'from-red-500 to-red-600',
      animatedValue: 1250,
      suffix: '+',
      prefix: ''
    }
  ];

  // Animate numbers when component comes into view
  useEffect(() => {
    if (isInView) {
      statsMetrics.forEach((metric) => {
        let start = 0;
        const end = metric.animatedValue;
        const duration = 2000; // 2 seconds
        const increment = end / (duration / 16); // 60fps

        const timer = setInterval(() => {
          start += increment;
          if (start >= end) {
            start = end;
            clearInterval(timer);
          }
          setAnimatedStats(prev => ({
            ...prev,
            [metric.id]: start
          }));
        }, 16);
      });
    }
  }, [isInView, statsMetrics]);

  // Real-time updates for online users
  useEffect(() => {
    const interval = setInterval(() => {
      setAnimatedStats(prev => ({
        ...prev,
        onlineUsers: (prev.onlineUsers || 1250) + Math.floor(Math.random() * 20) - 10
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const formatNumber = (num: number, metric: StatMetric): string => {
    if (metric.id === 'totalVolume') {
      return `${metric.prefix}${num.toFixed(1)}${metric.suffix}`;
    } else if (metric.id === 'successRate') {
      return `${num.toFixed(1)}${metric.suffix}`;
    } else if (metric.id === 'averageTime') {
      return `${Math.round(num)}${metric.suffix}`;
    } else {
      return `${metric.prefix}${Math.round(num).toLocaleString()}${metric.suffix}`;
    }
  };

  return (
    <section ref={ref} className="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 dark:from-black dark:via-gray-900 dark:to-gray-800 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        {/* Animated Gradient Orbs */}
        <motion.div 
          className="absolute top-20 left-20 w-96 h-96 bg-blue-500/10 rounded-full filter blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div 
          className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500/10 rounded-full filter blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.4, 0.2]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* Grid Pattern */}
        <div className="absolute inset-0 opacity-50" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Header */}
          <motion.div className="text-center mb-16" variants={itemVariants}>
            <div className="inline-flex items-center bg-white/10 backdrop-blur-sm text-white rounded-full px-6 py-3 mb-6 border border-white/20">
              <Activity className="w-5 h-5 ml-2" />
              <span className="text-sm font-medium">{t('home.platformStats.title')}</span>
            </div>
            <h2 className="text-4xl lg:text-6xl font-bold text-white mb-6">
              {t('home.platformStats.title')}
            </h2>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
              {t('home.platformStats.subtitle')}
            </p>
          </motion.div>

          {/* Stats Grid */}
          <motion.div 
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
            variants={itemVariants}
          >
            {statsMetrics.map((metric, index) => {
              const IconComponent = metric.icon;
              const animatedValue = animatedStats[metric.id] || 0;
              
              return (
                <motion.div
                  key={metric.id}
                  className="group relative"
                  variants={itemVariants}
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20 shadow-2xl hover:bg-white/15 transition-all duration-300">
                    {/* Icon and Growth Badge */}
                    <div className="flex items-center justify-between mb-6">
                      <div className={`w-16 h-16 rounded-xl bg-gradient-to-br ${metric.bgColor} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      <div className="text-right">
                        <span className={`text-xs px-3 py-1 rounded-full bg-white/20 text-white border border-white/30`}>
                          {metric.growth}
                        </span>
                      </div>
                    </div>

                    {/* Animated Value */}
                    <div className="mb-3">
                      <motion.div 
                        className="text-3xl lg:text-4xl font-bold text-white"
                        initial={{ scale: 0.5, opacity: 0 }}
                        animate={isInView ? { scale: 1, opacity: 1 } : { scale: 0.5, opacity: 0 }}
                        transition={{ delay: index * 0.1, duration: 0.5 }}
                      >
                        {formatNumber(animatedValue, metric)}
                      </motion.div>
                    </div>

                    {/* Label */}
                    <div className="text-blue-100 font-medium">
                      {metric.label}
                    </div>

                    {/* Live Indicator for Online Users */}
                    {metric.id === 'onlineUsers' && (
                      <motion.div 
                        className="flex items-center mt-2 text-green-400 text-sm"
                        animate={{ opacity: [0.5, 1, 0.5] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse" />
                        {t('home.platformStats.metrics.onlineUsers.growth')}
                      </motion.div>
                    )}

                    {/* Hover Effect */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                </motion.div>
              );
            })}
          </motion.div>

          {/* Trust Indicators */}
          <motion.div 
            className="grid md:grid-cols-4 gap-8 mb-16"
            variants={itemVariants}
          >
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Shield className="w-8 h-8 text-white" />
              </div>
              <h4 className="text-xl font-bold text-white mb-2">{t('home.platformStats.features.smartContracts.title')}</h4>
              <p className="text-blue-200 text-sm">{t('home.platformStats.features.smartContracts.description')}</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <h4 className="text-xl font-bold text-white mb-2">{t('home.platformStats.features.fastTransactions.title')}</h4>
              <p className="text-blue-200 text-sm">{t('home.platformStats.features.fastTransactions.description')}</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Award className="w-8 h-8 text-white" />
              </div>
              <h4 className="text-xl font-bold text-white mb-2">{t('home.platformStats.features.awards.title')}</h4>
              <p className="text-blue-200 text-sm">{t('home.platformStats.features.awards.description')}</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Globe className="w-8 h-8 text-white" />
              </div>
              <h4 className="text-xl font-bold text-white mb-2">{t('home.platformStats.features.globalCoverage.title')}</h4>
              <p className="text-blue-200 text-sm">{t('home.platformStats.features.globalCoverage.description')}</p>
            </div>
          </motion.div>

          {/* Real-time Activity Feed */}
          <motion.div 
            className="bg-white/5 backdrop-blur-lg rounded-2xl p-8 border border-white/20"
            variants={itemVariants}
          >
            <h3 className="text-2xl font-bold text-white mb-6 text-center">
              {t('home.platformStats.liveActivity.title')}
            </h3>
            
            <div className="grid md:grid-cols-3 gap-6">
              <motion.div 
                className="text-center"
                animate={{ scale: [1, 1.05, 1] }}
                transition={{ duration: 2, repeat: Infinity, delay: 0 }}
              >
                <div className="text-2xl font-bold text-green-400 mb-2">
                  +{Math.floor(Math.random() * 5) + 1}
                </div>
                <div className="text-blue-200 text-sm">{t('home.platformStats.liveActivity.newTrades')}</div>
                <div className="text-xs text-blue-300 mt-1">{t('home.platformStats.liveActivity.lastMinute')}</div>
              </motion.div>
              
              <motion.div 
                className="text-center"
                animate={{ scale: [1, 1.05, 1] }}
                transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
              >
                <div className="text-2xl font-bold text-blue-400 mb-2">
                  +{Math.floor(Math.random() * 10) + 5}
                </div>
                <div className="text-blue-200 text-sm">{t('home.platformStats.liveActivity.newUsers')}</div>
                <div className="text-xs text-blue-300 mt-1">{t('home.platformStats.liveActivity.lastHour')}</div>
              </motion.div>
              
              <motion.div 
                className="text-center"
                animate={{ scale: [1, 1.05, 1] }}
                transition={{ duration: 2, repeat: Infinity, delay: 1 }}
              >
                <div className="text-2xl font-bold text-purple-400 mb-2">
                  ${(Math.random() * 50 + 10).toFixed(0)}K
                </div>
                <div className="text-blue-200 text-sm">{t('home.platformStats.liveActivity.tradingVolume')}</div>
                <div className="text-xs text-blue-300 mt-1">{t('home.platformStats.liveActivity.lastHour')}</div>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
