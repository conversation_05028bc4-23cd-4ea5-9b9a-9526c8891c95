/**
 * خدمة API المركزية
 * Centralized API Service
 */

// إعدادات API للبيئات المختلفة
const API_CONFIG = {
  development: {
    baseUrl: '/api', // استخدام rewrites في Next.js
    timeout: 10000,
  },
  production: {
    baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
    timeout: 15000,
  },
  staging: {
    baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
    timeout: 12000,
  }
};

// الحصول على إعدادات API الحالية
const getApiConfig = () => {
  // أولوية للمتغير المخصص للبيئة
  const customEnv = process.env.NEXT_PUBLIC_APP_ENV;
  const nodeEnv = process.env.NODE_ENV || 'development';

  // استخدام البيئة المخصصة أو Node.js environment
  const env = customEnv || nodeEnv;

  return API_CONFIG[env as keyof typeof API_CONFIG] || API_CONFIG.development;
};

// إنشاء URL كامل للAPI
const createApiUrl = (endpoint: string): string => {
  const config = getApiConfig();
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${config.baseUrl}/${cleanEndpoint}`;
};

// إعدادات الطلب الافتراضية
const getDefaultHeaders = (): HeadersInit => {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // إضافة رمز المصادقة إذا كان متوفراً
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('userToken');
    const adminToken = localStorage.getItem('adminToken');

    if (adminToken) {
      headers['Authorization'] = `Bearer ${adminToken}`;
    } else if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
  }

  return headers;
};

// دالة عامة لإرسال الطلبات
const makeRequest = async <T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const config = getApiConfig();
  const url = createApiUrl(endpoint);
  
  const defaultOptions: RequestInit = {
    headers: getDefaultHeaders(),
    credentials: 'include',
    ...options,
  };

  // إضافة timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), config.timeout);

  try {
    const response = await fetch(url, {
      ...defaultOptions,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    // التحقق من حالة الاستجابة
    if (!response.ok) {
      const errorText = await response.text();
      let errorMessage = `خطأ في الخادم: ${response.status} ${response.statusText}`;
      
      try {
        const errorData = JSON.parse(errorText);
        errorMessage = errorData.error || errorData.message || errorMessage;
      } catch {
        // إذا لم تكن الاستجابة JSON صحيحة
        errorMessage = errorText || errorMessage;
      }
      
      throw new Error(errorMessage);
    }

    // محاولة تحليل JSON
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();
      
      // التحقق من نجاح العملية
      if (data.success === false) {
        throw new Error(data.error || data.message || 'فشل في العملية');
      }
      
      return data;
    } else {
      // إذا لم تكن الاستجابة JSON
      const text = await response.text();
      return { success: true, data: text } as T;
    }

  } catch (error: any) {
    clearTimeout(timeoutId);
    
    // معالجة أنواع مختلفة من الأخطاء
    if (error.name === 'AbortError') {
      throw new Error('انتهت مهلة الطلب. تحقق من اتصال الإنترنت.');
    } else if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
      throw new Error('فشل في الاتصال بالخادم. تأكد من تشغيل XAMPP وأن الخادم يعمل بشكل صحيح.');
    } else if (error.name === 'SyntaxError') {
      throw new Error('خطأ في تحليل البيانات من الخادم. تحقق من إعدادات API.');
    } else {
      throw error;
    }
  }
};

// دوال API محددة
export const apiService = {
  // دوال المصادقة
  auth: {
    login: (email: string, password: string) =>
      makeRequest('auth/login.php', {
        method: 'POST',
        body: JSON.stringify({ email, password }),
      }),

    register: (userData: any) =>
      makeRequest('auth/register.php', {
        method: 'POST',
        body: JSON.stringify(userData),
      }),

    logout: (userId?: string, token?: string) =>
      makeRequest('auth/logout.php', {
        method: 'POST',
        body: JSON.stringify({ userId, token }),
      }),

    refreshToken: (refreshToken: string) =>
      makeRequest('auth/refresh.php', {
        method: 'POST',
        body: JSON.stringify({ refreshToken }),
      }),
  },

  // دوال المستخدمين
  users: {
    getProfile: (userId: string) =>
      makeRequest(`users/profile.php?id=${userId}`),

    updateProfile: (userData: any) =>
      makeRequest('users/profile.php', {
        method: 'PUT',
        body: JSON.stringify(userData),
      }),

    getUsers: (params?: any) =>
      makeRequest(`users/list.php${params ? '?' + new URLSearchParams(params) : ''}`),
  },

  // دوال العروض
  offers: {
    getOffers: (params?: any) =>
      makeRequest(`offers/list.php${params ? '?' + new URLSearchParams(params) : ''}`),

    createOffer: (offerData: any) =>
      makeRequest('offers/create.php', {
        method: 'POST',
        body: JSON.stringify(offerData),
      }),

    updateOffer: (offerId: string, offerData: any) =>
      makeRequest(`offers/update.php`, {
        method: 'PUT',
        body: JSON.stringify({ id: offerId, ...offerData }),
      }),

    deleteOffer: (offerId: string) =>
      makeRequest(`offers/delete.php`, {
        method: 'DELETE',
        body: JSON.stringify({ id: offerId }),
      }),
  },

  // دوال الصفقات
  trades: {
    getTrades: (params?: any) =>
      makeRequest(`trades/list.php${params ? '?' + new URLSearchParams(params) : ''}`),

    createTrade: (tradeData: any) =>
      makeRequest('trades/create.php', {
        method: 'POST',
        body: JSON.stringify(tradeData),
      }),

    updateTrade: (tradeId: string, tradeData: any) =>
      makeRequest(`trades/update.php`, {
        method: 'PUT',
        body: JSON.stringify({ id: tradeId, ...tradeData }),
      }),
  },

  // دوال إدارة المدراء
  admin: {
    // تسجيل دخول المدير
    login: (credentials: { username: string; password: string }) =>
      makeRequest('admin/login.php', {
        method: 'POST',
        body: JSON.stringify(credentials),
      }),

    // تسجيل خروج المدير
    logout: () =>
      makeRequest('admin/logout.php', {
        method: 'POST',
        body: JSON.stringify({}),
      }),

    // التحقق من صحة الجلسة
    validateSession: () =>
      makeRequest('admin/validate-session.php', {
        method: 'POST',
        body: JSON.stringify({}),
      }),

    // جلب قائمة المدراء
    getAdmins: (params?: {
      page?: number;
      limit?: number;
      search?: string;
      status?: 'all' | 'active' | 'inactive';
      role?: 'all' | 'super_admin' | 'admin' | 'moderator' | 'support';
    }) => {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.role) queryParams.append('role', params.role);

      const url = queryParams.toString() ? `admin/admins.php?${queryParams}` : 'admin/admins.php';
      return makeRequest(url);
    },

    // التحقق من صلاحيات المحفظة
    checkWalletPermissions: (walletAddress: string) =>
      makeRequest('admin/check-permissions.php', {
        method: 'POST',
        body: JSON.stringify({ walletAddress }),
      }),

    // إنشاء مدير جديد
    createAdmin: (adminData: any) =>
      makeRequest('admin/admins.php', {
        method: 'POST',
        body: JSON.stringify(adminData),
      }),

    // تحديث مدير
    updateAdmin: (adminId: number, adminData: any) =>
      makeRequest(`admin/admins.php?id=${adminId}`, {
        method: 'PUT',
        body: JSON.stringify(adminData),
      }),

    // حذف مدير
    deleteAdmin: (adminId: number) =>
      makeRequest(`admin/admins.php?id=${adminId}`, {
        method: 'DELETE',
      }),

    // تفعيل/إلغاء تفعيل مدير
    toggleAdminStatus: (adminId: number, isActive: boolean) =>
      makeRequest('admin/toggle-status.php', {
        method: 'POST',
        body: JSON.stringify({ admin_id: adminId, is_active: isActive }),
      }),
  },



  // دالة عامة للطلبات المخصصة
  request: makeRequest,
};

export default apiService;
