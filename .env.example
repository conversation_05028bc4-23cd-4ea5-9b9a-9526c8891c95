# إعدادات التطبيق
NEXT_PUBLIC_APP_NAME="IKAROS P2P"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NEXT_PUBLIC_APP_ENV="development"

# إعدادات الشبكة التجريبية (BSC Testnet)
# ملاحظة: هذه العناوين وهمية - يجب نشر العقد الذكي أولاً
NEXT_PUBLIC_TESTNET_ESCROW_CONTRACT=""
NEXT_PUBLIC_TESTNET_USDT_CONTRACT="******************************************"
NEXT_PUBLIC_TESTNET_RPC_URL="https://data-seed-prebsc-1-s1.binance.org:8545/"

# إعدادات الشبكة الحقيقية (BSC Mainnet)
NEXT_PUBLIC_MAINNET_ESCROW_CONTRACT=""
NEXT_PUBLIC_MAINNET_USDT_CONTRACT="0x55d398326f99059fF775485246999027B3197955"
NEXT_PUBLIC_MAINNET_RPC_URL="https://bsc-dataseed1.binance.org/"

# الشبكة الافتراضية (testnet أو mainnet)
# تم تعيينها على "development" لتجنب أخطاء العقد غير المنشور
NEXT_PUBLIC_DEFAULT_NETWORK="development"

# إعدادات قاعدة البيانات (للـ Backend)
DB_HOST="localhost"
DB_PORT="3306"
DB_DATABASE="ikaros_p2p"
DB_USERNAME="root"
DB_PASSWORD=""

# إعدادات الإدارة
ADMIN_EMAIL="<EMAIL>"
ADMIN_WALLET_ADDRESS=""

# مفاتيح API (إذا لزم الأمر)
NEXT_PUBLIC_INFURA_PROJECT_ID=""
NEXT_PUBLIC_ALCHEMY_API_KEY=""

# إعدادات الأمان
JWT_SECRET="your-jwt-secret-key-here"
ENCRYPTION_KEY="your-encryption-key-here"

# إعدادات الملفات
UPLOAD_MAX_SIZE="5242880"
UPLOAD_ALLOWED_TYPES="jpg,jpeg,png,gif,pdf"
UPLOAD_PATH="public/uploads/"

# إعدادات البريد الإلكتروني (للإشعارات)
MAIL_HOST=""
MAIL_PORT="587"
MAIL_USERNAME=""
MAIL_PASSWORD=""
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="IKAROS P2P"

# إعدادات التطوير
NODE_ENV="development"
NEXT_PUBLIC_DEBUG="true"
NEXT_PUBLIC_SHOW_CONSOLE_LOGS="true"

# إعدادات قديمة (للتوافق مع النسخة السابقة)
NEXT_PUBLIC_ESCROW_CONTRACT_ADDRESS="******************************************"
NEXT_PUBLIC_USDT_CONTRACT_ADDRESS="******************************************"
NEXT_PUBLIC_NETWORK_ID="97"
NEXT_PUBLIC_RPC_URL="https://data-seed-prebsc-1-s1.binance.org:8545/"
