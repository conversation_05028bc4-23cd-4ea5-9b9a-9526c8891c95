'use client';

import React from 'react';
import { Loader2 } from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';

export interface AdminButtonProps {
  children: React.ReactNode;
  onClick?: () => void | Promise<void>;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'ghost' | 'outline';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  icon?: React.ComponentType<any>;
  iconPosition?: 'left' | 'right';
  className?: string;
}

export default function AdminButton({
  children,
  onClick,
  type = 'button',
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  fullWidth = false,
  icon: Icon,
  iconPosition = 'left',
  className = ''
}: AdminButtonProps) {
  const { isRTL } = useAdminTranslation();

  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white border-transparent';
      case 'secondary':
        return 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500 text-white border-transparent';
      case 'success':
        return 'bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white border-transparent';
      case 'warning':
        return 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500 text-white border-transparent';
      case 'error':
        return 'bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white border-transparent';
      case 'info':
        return 'bg-cyan-600 hover:bg-cyan-700 focus:ring-cyan-500 text-white border-transparent';
      case 'ghost':
        return 'bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 focus:ring-gray-500 text-gray-700 dark:text-gray-300 border-transparent';
      case 'outline':
        return 'bg-transparent hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-blue-500 text-blue-600 dark:text-blue-400 border-blue-600 dark:border-blue-400';
      default:
        return 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white border-transparent';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'xs':
        return 'px-2 py-1 text-xs';
      case 'sm':
        return 'px-3 py-1.5 text-sm';
      case 'md':
        return 'px-4 py-2 text-sm';
      case 'lg':
        return 'px-6 py-3 text-base';
      case 'xl':
        return 'px-8 py-4 text-lg';
      default:
        return 'px-4 py-2 text-sm';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'xs':
        return 'w-3 h-3';
      case 'sm':
        return 'w-4 h-4';
      case 'md':
        return 'w-4 h-4';
      case 'lg':
        return 'w-5 h-5';
      case 'xl':
        return 'w-6 h-6';
      default:
        return 'w-4 h-4';
    }
  };

  const handleClick = async () => {
    if (onClick && !loading && !disabled) {
      await onClick();
    }
  };

  const baseClasses = `
    inline-flex items-center justify-center
    font-medium rounded-lg border
    transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800
    disabled:opacity-50 disabled:cursor-not-allowed
    ${fullWidth ? 'w-full' : ''}
  `;

  const iconClasses = getIconSize();
  const iconSpacing = isRTL ? 
    (iconPosition === 'left' ? 'ml-2' : 'mr-2') :
    (iconPosition === 'left' ? 'mr-2' : 'ml-2');

  return (
    <button
      type={type}
      onClick={handleClick}
      disabled={disabled || loading}
      className={`${baseClasses} ${getVariantClasses()} ${getSizeClasses()} ${className}`}
    >
      {loading ? (
        <>
          <Loader2 className={`${iconClasses} animate-spin ${isRTL ? 'ml-2' : 'mr-2'}`} />
          {children}
        </>
      ) : (
        <>
          {Icon && iconPosition === 'left' && (
            <Icon className={`${iconClasses} ${iconSpacing}`} />
          )}
          {children}
          {Icon && iconPosition === 'right' && (
            <Icon className={`${iconClasses} ${iconSpacing}`} />
          )}
        </>
      )}
    </button>
  );
}

// Button Group Component
export interface AdminButtonGroupProps {
  children: React.ReactNode;
  orientation?: 'horizontal' | 'vertical';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'ghost' | 'outline';
  className?: string;
}

export function AdminButtonGroup({
  children,
  orientation = 'horizontal',
  size = 'md',
  variant = 'primary',
  className = ''
}: AdminButtonGroupProps) {
  const isVertical = orientation === 'vertical';
  
  const groupClasses = `
    inline-flex
    ${isVertical ? 'flex-col' : 'flex-row'}
    ${isVertical ? 'divide-y' : 'divide-x'}
    divide-gray-300 dark:divide-gray-600
    rounded-lg overflow-hidden
    border border-gray-300 dark:border-gray-600
  `;

  return (
    <div className={`${groupClasses} ${className}`}>
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child as React.ReactElement<AdminButtonProps>, {
            size,
            variant,
            className: `
              ${child.props.className || ''}
              ${isVertical ? 'rounded-none' : 'rounded-none'}
              border-0
              ${isVertical ? 'first:rounded-t-lg last:rounded-b-lg' : 'first:rounded-l-lg last:rounded-r-lg'}
            `
          });
        }
        return child;
      })}
    </div>
  );
}

// Icon Button Component
export interface AdminIconButtonProps {
  icon: React.ComponentType<any>;
  onClick?: () => void | Promise<void>;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'ghost' | 'outline';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  disabled?: boolean;
  tooltip?: string;
  className?: string;
}

export function AdminIconButton({
  icon: Icon,
  onClick,
  type = 'button',
  variant = 'ghost',
  size = 'md',
  loading = false,
  disabled = false,
  tooltip,
  className = ''
}: AdminIconButtonProps) {
  const getSizeClasses = () => {
    switch (size) {
      case 'xs':
        return 'p-1';
      case 'sm':
        return 'p-1.5';
      case 'md':
        return 'p-2';
      case 'lg':
        return 'p-3';
      case 'xl':
        return 'p-4';
      default:
        return 'p-2';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'xs':
        return 'w-3 h-3';
      case 'sm':
        return 'w-4 h-4';
      case 'md':
        return 'w-4 h-4';
      case 'lg':
        return 'w-5 h-5';
      case 'xl':
        return 'w-6 h-6';
      default:
        return 'w-4 h-4';
    }
  };

  return (
    <AdminButton
      onClick={onClick}
      type={type}
      variant={variant}
      loading={loading}
      disabled={disabled}
      className={`${getSizeClasses()} ${className}`}
      title={tooltip}
    >
      {loading ? (
        <Loader2 className={`${getIconSize()} animate-spin`} />
      ) : (
        <Icon className={getIconSize()} />
      )}
    </AdminButton>
  );
}

// Floating Action Button Component
export interface AdminFABProps {
  icon: React.ComponentType<any>;
  onClick?: () => void | Promise<void>;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  loading?: boolean;
  disabled?: boolean;
  tooltip?: string;
  className?: string;
}

export function AdminFAB({
  icon: Icon,
  onClick,
  variant = 'primary',
  size = 'md',
  position = 'bottom-right',
  loading = false,
  disabled = false,
  tooltip,
  className = ''
}: AdminFABProps) {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-12 h-12';
      case 'md':
        return 'w-14 h-14';
      case 'lg':
        return 'w-16 h-16';
      default:
        return 'w-14 h-14';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'w-5 h-5';
      case 'md':
        return 'w-6 h-6';
      case 'lg':
        return 'w-7 h-7';
      default:
        return 'w-6 h-6';
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-right':
        return 'bottom-6 right-6';
      case 'bottom-left':
        return 'bottom-6 left-6';
      case 'top-right':
        return 'top-6 right-6';
      case 'top-left':
        return 'top-6 left-6';
      default:
        return 'bottom-6 right-6';
    }
  };

  return (
    <AdminButton
      onClick={onClick}
      variant={variant}
      loading={loading}
      disabled={disabled}
      className={`
        fixed ${getPositionClasses()} ${getSizeClasses()}
        rounded-full shadow-lg hover:shadow-xl
        z-50 transition-all duration-200
        ${className}
      `}
      title={tooltip}
    >
      {loading ? (
        <Loader2 className={`${getIconSize()} animate-spin`} />
      ) : (
        <Icon className={getIconSize()} />
      )}
    </AdminButton>
  );
}
