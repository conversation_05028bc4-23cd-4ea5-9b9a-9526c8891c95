<?php
/**
 * API لجلب أسعار السوق
 * Market Price API
 */

// تضمين إعدادات CORS
require_once __DIR__ . '/../cors.php';

// التحقق من طريقة الطلب
validateRequestMethod(['GET']);

try {
    // الحصول على المعاملات
    $currency = $_GET['currency'] ?? 'SAR';
    $stablecoin = $_GET['stablecoin'] ?? 'USDT';
    
    // التحقق من صحة المعاملات (مرتبة حسب الأهمية)
    $supportedCurrencies = [
        'SAR', 'AED', 'KWD', 'QAR', 'BHD', 'OMR', 'JOD', 'EGP', 'USD', 'EUR',
        'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY', 'TRY', 'LBP', 'MAD', 'TND',
        'DZD', 'IQD', 'SYP', 'YER', 'PKR', 'INR', 'BDT', 'MYR', 'IDR', 'SGD'
    ];
    $supportedStablecoins = [
        'USDT', 'USDC', 'BUSD', 'DAI', 'FDUSD', 'TUSD', 'USDD', 'FRAX',
        'USDP', 'LUSD', 'GUSD', 'SUSD', 'USTC', 'PYUSD'
    ];
    
    if (!in_array($currency, $supportedCurrencies)) {
        sendErrorResponse('عملة غير مدعومة', 400);
    }
    
    if (!in_array($stablecoin, $supportedStablecoins)) {
        sendErrorResponse('العملة المستقرة غير مدعومة', 400);
    }
    
    // أسعار افتراضية (مرتبة حسب الأهمية - في التطبيق الحقيقي، ستأتي من API خارجي)
    $exchangeRates = [
        // العملات الخليجية والعربية (الأولوية العليا)
        'USD' => 1.0,
        'SAR' => 3.75,
        'AED' => 3.67,
        'KWD' => 0.31,
        'QAR' => 3.64,
        'BHD' => 0.38,
        'OMR' => 0.38,
        'JOD' => 0.71,
        'EGP' => 31.0,
        // العملات العالمية الرئيسية
        'EUR' => 0.92,
        'GBP' => 0.79,
        'JPY' => 150.0,
        'CAD' => 1.35,
        'AUD' => 1.52,
        'CHF' => 0.88,
        'CNY' => 7.25,
        'TRY' => 27.0,
        // العملات العربية الأخرى
        'LBP' => 15000.0,
        'MAD' => 10.2,
        'TND' => 3.1,
        'DZD' => 135.0,
        'IQD' => 1310.0,
        'SYP' => 2512.0,
        'YER' => 250.0,
        // العملات الآسيوية
        'PKR' => 280.0,
        'INR' => 83.0,
        'BDT' => 110.0,
        'MYR' => 4.7,
        'IDR' => 15500.0,
        'SGD' => 1.35
    ];
    
    // أسعار العملات المستقرة (مرتبة حسب الأهمية - عادة قريبة من 1 USD)
    $stablecoinRates = [
        'USDT' => 1.0,
        'USDC' => 1.0,
        'BUSD' => 1.0,
        'DAI' => 1.0,
        'FDUSD' => 1.0,
        'TUSD' => 1.0,
        'USDD' => 1.0,
        'FRAX' => 1.0,
        'USDP' => 1.0,
        'LUSD' => 1.0,
        'GUSD' => 1.0,
        'SUSD' => 1.0,
        'USTC' => 1.0,
        'PYUSD' => 1.0
    ];
    
    // حساب السعر
    $basePrice = $exchangeRates[$currency] ?? 1.0;
    $stablecoinPrice = $stablecoinRates[$stablecoin] ?? 1.0;

    // التأكد من عدم القسمة على صفر
    if ($stablecoinPrice == 0) {
        $stablecoinPrice = 1.0;
    }

    // السعر النهائي (كم من العملة المحلية مقابل 1 من العملة المستقرة)
    $finalPrice = $basePrice / $stablecoinPrice;
    
    // إضافة تقلبات طفيفة لمحاكاة السوق الحقيقي
    $variation = (rand(-200, 200) / 10000); // تقلب ±2%
    $marketPrice = $finalPrice * (1 + $variation);
    
    // تقريب إلى خانتين عشريتين
    $marketPrice = round($marketPrice, 2);
    
    // معلومات إضافية
    $priceInfo = [
        'price' => $marketPrice,
        'currency' => $currency,
        'stablecoin' => $stablecoin,
        'base_rate' => $basePrice,
        'variation' => round($variation * 100, 2), // النسبة المئوية للتقلب
        'timestamp' => time(),
        'last_updated' => date('Y-m-d H:i:s'),
        'source' => 'internal_rates'
    ];
    
    // محاولة جلب سعر حقيقي من API خارجي (اختياري)
    if (function_exists('curl_init')) {
        try {
            $realPrice = fetchRealMarketPrice($currency, $stablecoin);
            if ($realPrice) {
                $priceInfo['price'] = $realPrice;
                $priceInfo['source'] = 'external_api';
            }
        } catch (Exception $e) {
            // تجاهل الأخطاء واستخدم السعر الافتراضي
            logError("Failed to fetch real market price: " . $e->getMessage());
        }
    }
    
    // إرسال الاستجابة مباشرة
    $response = [
        'success' => true,
        'message' => 'تم جلب سعر السوق بنجاح',
        'timestamp' => date('Y-m-d H:i:s'),
        'price' => $priceInfo['price'],
        'currency' => $priceInfo['currency'],
        'stablecoin' => $priceInfo['stablecoin'],
        'base_rate' => $priceInfo['base_rate'],
        'variation' => $priceInfo['variation'],
        'source' => $priceInfo['source'],
        'last_updated' => $priceInfo['last_updated']
    ];

    sendJsonResponse($response);
    
} catch (Exception $e) {
    logError("Market price API error: " . $e->getMessage());
    sendErrorResponse('خطأ في جلب سعر السوق: ' . $e->getMessage(), 500);
}

/**
 * جلب سعر حقيقي من API خارجي
 */
function fetchRealMarketPrice($currency, $stablecoin) {
    try {
        // قائمة العملات المستقرة (مرتبة حسب الأهمية)
        $stablecoins = ['USDT', 'USDC', 'BUSD', 'DAI', 'FDUSD', 'TUSD', 'USDD', 'FRAX', 'USDP', 'LUSD', 'GUSD', 'SUSD', 'USTC', 'PYUSD'];

        // إذا كانت العملة المحلية هي USD والعملة المستقرة من القائمة، فالسعر = 1
        if ($currency === 'USD' && in_array($stablecoin, $stablecoins)) {
            return 1.0;
        }

        // استخدام API مجاني لأسعار الصرف
        $url = "https://api.exchangerate-api.com/v4/latest/USD";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Ikaros P2P Platform');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // للتطوير فقط

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($curlError) {
            error_log("CURL Error: " . $curlError);
            return null;
        }

        if ($httpCode === 200 && $response) {
            $data = json_decode($response, true);

            if ($data && isset($data['rates'][$currency])) {
                $rate = $data['rates'][$currency];

                // العملات المستقرة عادة = 1 USD
                $stablecoinRate = 1.0;

                return round($rate / $stablecoinRate, 2);
            }
        }

        return null;
    } catch (Exception $e) {
        error_log("fetchRealMarketPrice Error: " . $e->getMessage());
        return null;
    }
}

/**
 * جلب أسعار متعددة
 */
function getMultiplePrices($currencies, $stablecoin) {
    $prices = [];
    
    foreach ($currencies as $currency) {
        try {
            // إعادة استخدام نفس المنطق
            $price = fetchRealMarketPrice($currency, $stablecoin);
            if (!$price) {
                // استخدام الأسعار الافتراضية
                $defaultRates = [
                    'SAR' => 3.75,
                    'AED' => 3.67,
                    'USD' => 1.0,
                    'EUR' => 0.92
                ];
                $price = $defaultRates[$currency] ?? 1.0;
            }
            
            $prices[$currency] = $price;
        } catch (Exception $e) {
            $prices[$currency] = null;
        }
    }
    
    return $prices;
}
?>
