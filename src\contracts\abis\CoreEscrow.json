[{"inputs": [{"internalType": "address", "name": "_feeRecipient", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "minAmount", "type": "uint256"}, {"internalType": "uint256", "name": "maxAmount", "type": "uint256"}, {"internalType": "uint256", "name": "feeRate", "type": "uint256"}], "name": "addSupportedToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tradeId", "type": "uint256"}], "name": "cancelTrade", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tradeId", "type": "uint256"}], "name": "confirmPaymentReceived", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tradeId", "type": "uint256"}], "name": "confirmPaymentSent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "pricePerToken", "type": "uint256"}, {"internalType": "string", "name": "currency", "type": "string"}], "name": "createTrade", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "getTokenInfo", "outputs": [{"components": [{"internalType": "bool", "name": "isSupported", "type": "bool"}, {"internalType": "uint256", "name": "minAmount", "type": "uint256"}, {"internalType": "uint256", "name": "maxAmount", "type": "uint256"}, {"internalType": "uint256", "name": "feeRate", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}], "internalType": "struct CoreEscrow.TokenInfo", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tradeId", "type": "uint256"}], "name": "getTrade", "outputs": [{"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "seller", "type": "address"}, {"internalType": "address", "name": "buyer", "type": "address"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "pricePerToken", "type": "uint256"}, {"internalType": "string", "name": "currency", "type": "string"}, {"internalType": "enum CoreEscrow.TradeStatus", "name": "status", "type": "uint8"}, {"internalType": "uint256", "name": "createdAt", "type": "uint256"}, {"internalType": "uint256", "name": "lastActivity", "type": "uint256"}, {"internalType": "bool", "name": "sellerConfirmed", "type": "bool"}, {"internalType": "bool", "name": "buyerConfirmed", "type": "bool"}, {"internalType": "string", "name": "disputeReason", "type": "string"}, {"internalType": "address", "name": "disputeInitiator", "type": "address"}], "internalType": "struct CoreEscrow.Trade", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserTrades", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "isTokenSupported", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tradeId", "type": "uint256"}], "name": "joinTrade", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "removeSupportedToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tradeId", "type": "uint256"}, {"internalType": "string", "name": "reason", "type": "string"}], "name": "requestDispute", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newRecipient", "type": "address"}], "name": "setFeeRecipient", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newRate", "type": "uint256"}], "name": "setPlatformFeeRate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "bool", "name": "status", "type": "bool"}], "name": "setUserBlacklist", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "canceller", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "TradeCancelled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "seller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "TradeCompleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "seller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "pricePerToken", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "currency", "type": "string"}], "name": "TradeCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "initiator", "type": "address"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "TradeDisputed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "TradeJoined", "type": "event"}]