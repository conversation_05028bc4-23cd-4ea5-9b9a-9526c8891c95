'use client';

import React, { useState } from 'react';
import {
  Settings,
  Globe,
  DollarSign,
  Shield,
  Bell,
  Code,
  Network,
  Wrench,
  HardDrive,
  Save,
  RotateCcw,
  Download,
  Upload,
  TestTube,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info,
  Eye,
  EyeOff,
  Copy,
  RefreshCw,
  Clock,
  Calendar,
  Users,
  Lock,
  Unlock,
  Key,
  Database,
  Server,
  Wifi,
  WifiOff,
  Activity,
  BarChart3,
  Zap,
  Timer,
  Gauge,
  Cpu,
  MemoryStick,
  Monitor,
  Cloud,
  CloudOff,
  FileText,
  Folder,
  Archive,
  Trash2,
  Plus,
  Minus,
  Edit,
  X,
  Check
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';

interface SettingItem {
  id: string;
  key: string;
  value: any;
  type: 'text' | 'number' | 'boolean' | 'select' | 'textarea' | 'password' | 'url' | 'email' | 'json';
  category: string;
  label: string;
  description?: string;
  required?: boolean;
  options?: Array<{ value: any; label: string }>;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    custom?: (value: any) => boolean;
  };
  sensitive?: boolean;
  readonly?: boolean;
}

interface AdminSettingsProps {
  className?: string;
}

export default function AdminSettings({ className = '' }: AdminSettingsProps) {
  const { t, language, isRTL, getDirectionClasses, formatNumber, formatCurrency, formatDate } = useAdminTranslation();
  const dirClasses = getDirectionClasses();
  
  const [activeCategory, setActiveCategory] = useState('platform');
  const [settings, setSettings] = useState<Record<string, any>>({});
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [showSensitive, setShowSensitive] = useState<Record<string, boolean>>({});
  const [testResults, setTestResults] = useState<Record<string, 'success' | 'error' | 'pending'>>({});

  // Mock settings data - replace with real data
  const settingsData: SettingItem[] = [
    // Platform Settings
    {
      id: 'site_name',
      key: 'platform.siteName',
      value: 'Ikaros P2P',
      type: 'text',
      category: 'platform',
      label: t('settings.platform.siteName'),
      description: 'The name of your platform',
      required: true
    },
    {
      id: 'site_description',
      key: 'platform.siteDescription',
      value: 'Secure P2P Trading Platform',
      type: 'textarea',
      category: 'platform',
      label: t('settings.platform.siteDescription'),
      description: 'Brief description of your platform'
    },
    {
      id: 'default_language',
      key: 'platform.defaultLanguage',
      value: 'en',
      type: 'select',
      category: 'platform',
      label: t('settings.platform.defaultLanguage'),
      options: [
        { value: 'en', label: 'English' },
        { value: 'ar', label: 'العربية' }
      ]
    },
    {
      id: 'default_currency',
      key: 'platform.defaultCurrency',
      value: 'USD',
      type: 'select',
      category: 'platform',
      label: t('settings.platform.defaultCurrency'),
      options: [
        { value: 'USD', label: 'US Dollar (USD)' },
        { value: 'EUR', label: 'Euro (EUR)' },
        { value: 'BTC', label: 'Bitcoin (BTC)' },
        { value: 'ETH', label: 'Ethereum (ETH)' }
      ]
    },
    {
      id: 'registration_enabled',
      key: 'platform.registrationEnabled',
      value: true,
      type: 'boolean',
      category: 'platform',
      label: t('settings.platform.registrationEnabled'),
      description: 'Allow new user registrations'
    },
    {
      id: 'kyc_required',
      key: 'platform.kycRequired',
      value: true,
      type: 'boolean',
      category: 'platform',
      label: t('settings.platform.kycRequired'),
      description: 'Require KYC verification for trading'
    },
    {
      id: 'max_users_per_day',
      key: 'platform.maxUsersPerDay',
      value: 100,
      type: 'number',
      category: 'platform',
      label: t('settings.platform.maxUsersPerDay'),
      validation: { min: 1, max: 10000 }
    },
    {
      id: 'session_timeout',
      key: 'platform.sessionTimeout',
      value: 3600,
      type: 'number',
      category: 'platform',
      label: t('settings.platform.sessionTimeout'),
      description: 'Session timeout in seconds',
      validation: { min: 300, max: 86400 }
    },

    // Fee Settings
    {
      id: 'trading_fee',
      key: 'fees.tradingFee',
      value: 0.5,
      type: 'number',
      category: 'fees',
      label: t('settings.fees.tradingFee'),
      description: 'Trading fee percentage',
      validation: { min: 0, max: 10 }
    },
    {
      id: 'withdrawal_fee',
      key: 'fees.withdrawalFee',
      value: 0.1,
      type: 'number',
      category: 'fees',
      label: t('settings.fees.withdrawalFee'),
      description: 'Withdrawal fee percentage',
      validation: { min: 0, max: 5 }
    },
    {
      id: 'minimum_trade_amount',
      key: 'fees.minimumTradeAmount',
      value: 10,
      type: 'number',
      category: 'fees',
      label: t('settings.fees.minimumTradeAmount'),
      description: 'Minimum trade amount in USD',
      validation: { min: 1, max: 1000 }
    },
    {
      id: 'maximum_trade_amount',
      key: 'fees.maximumTradeAmount',
      value: 100000,
      type: 'number',
      category: 'fees',
      label: t('settings.fees.maximumTradeAmount'),
      description: 'Maximum trade amount in USD',
      validation: { min: 100, max: ******** }
    },

    // Security Settings
    {
      id: 'two_factor_required',
      key: 'security.twoFactorAuth',
      value: true,
      type: 'boolean',
      category: 'security',
      label: t('settings.security.twoFactorAuth'),
      description: 'Require 2FA for admin accounts'
    },
    {
      id: 'max_login_attempts',
      key: 'security.maxLoginAttempts',
      value: 5,
      type: 'number',
      category: 'security',
      label: t('settings.platform.maxLoginAttempts'),
      validation: { min: 3, max: 20 }
    },
    {
      id: 'api_key',
      key: 'security.apiKey',
      value: 'sk_live_abc123...',
      type: 'password',
      category: 'security',
      label: t('settings.api.apiKey'),
      sensitive: true,
      readonly: true
    },
    {
      id: 'encryption_key',
      key: 'security.encryptionKey',
      value: 'enc_key_xyz789...',
      type: 'password',
      category: 'security',
      label: 'Encryption Key',
      sensitive: true,
      readonly: true
    },

    // API Settings
    {
      id: 'api_rate_limit',
      key: 'api.rateLimits',
      value: 1000,
      type: 'number',
      category: 'api',
      label: t('settings.api.rateLimits'),
      description: 'Requests per hour',
      validation: { min: 100, max: 100000 }
    },
    {
      id: 'webhook_url',
      key: 'api.webhookUrls',
      value: 'https://api.example.com/webhook',
      type: 'url',
      category: 'api',
      label: t('settings.api.webhookUrls'),
      description: 'Webhook endpoint URL'
    },

    // Blockchain Settings
    {
      id: 'eth_rpc_url',
      key: 'blockchain.rpcEndpoints.ethereum',
      value: 'https://mainnet.infura.io/v3/...',
      type: 'url',
      category: 'blockchain',
      label: 'Ethereum RPC URL',
      sensitive: true
    },
    {
      id: 'bsc_rpc_url',
      key: 'blockchain.rpcEndpoints.bsc',
      value: 'https://bsc-dataseed.binance.org/',
      type: 'url',
      category: 'blockchain',
      label: 'BSC RPC URL'
    },
    {
      id: 'gas_limit',
      key: 'blockchain.gasSettings.limit',
      value: 21000,
      type: 'number',
      category: 'blockchain',
      label: 'Gas Limit',
      validation: { min: 21000, max: 8000000 }
    },
    {
      id: 'confirmations_required',
      key: 'blockchain.confirmations',
      value: 12,
      type: 'number',
      category: 'blockchain',
      label: t('settings.blockchain.confirmations'),
      validation: { min: 1, max: 100 }
    }
  ];

  const categories = [
    { id: 'platform', label: t('settings.categories.platformSettings'), icon: Globe },
    { id: 'fees', label: t('settings.categories.feeManagement'), icon: DollarSign },
    { id: 'security', label: t('settings.categories.securitySettings'), icon: Shield },
    { id: 'api', label: t('settings.categories.apiConfiguration'), icon: Code },
    { id: 'blockchain', label: t('settings.categories.blockchainSettings'), icon: Network },
    { id: 'maintenance', label: t('settings.categories.maintenanceMode'), icon: Wrench },
    { id: 'backup', label: t('settings.categories.backupSettings'), icon: HardDrive }
  ];

  const filteredSettings = settingsData.filter(setting => setting.category === activeCategory);

  const handleSettingChange = (settingId: string, value: any) => {
    setSettings(prev => ({ ...prev, [settingId]: value }));
    setUnsavedChanges(true);
  };

  const handleSaveSettings = async () => {
    try {
      // Implement save logic
      console.log('Saving settings:', settings);
      setUnsavedChanges(false);
      // Show success message
    } catch (error) {
      console.error('Error saving settings:', error);
      // Show error message
    }
  };

  const handleTestSetting = async (settingId: string) => {
    setTestResults(prev => ({ ...prev, [settingId]: 'pending' }));
    
    try {
      // Implement test logic based on setting type
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate test
      setTestResults(prev => ({ ...prev, [settingId]: 'success' }));
    } catch (error) {
      setTestResults(prev => ({ ...prev, [settingId]: 'error' }));
    }
  };

  const toggleSensitiveVisibility = (settingId: string) => {
    setShowSensitive(prev => ({ ...prev, [settingId]: !prev[settingId] }));
  };

  const getSettingValue = (setting: SettingItem) => {
    return settings[setting.id] !== undefined ? settings[setting.id] : setting.value;
  };

  const renderSettingInput = (setting: SettingItem) => {
    const value = getSettingValue(setting);
    const isVisible = showSensitive[setting.id];
    const testResult = testResults[setting.id];

    switch (setting.type) {
      case 'boolean':
        return (
          <div className="flex items-center">
            <input
              type="checkbox"
              id={setting.id}
              checked={value}
              onChange={(e) => handleSettingChange(setting.id, e.target.checked)}
              disabled={setting.readonly}
              className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor={setting.id} className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
              {value ? t('settings.status.enabled') : t('settings.status.disabled')}
            </label>
          </div>
        );

      case 'select':
        return (
          <select
            value={value}
            onChange={(e) => handleSettingChange(setting.id, e.target.value)}
            disabled={setting.readonly}
            className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
          >
            {setting.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'textarea':
        return (
          <textarea
            value={value}
            onChange={(e) => handleSettingChange(setting.id, e.target.value)}
            disabled={setting.readonly}
            rows={3}
            className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
            placeholder={setting.description}
          />
        );

      case 'password':
        return (
          <div className="relative">
            <input
              type={isVisible ? 'text' : 'password'}
              value={value}
              onChange={(e) => handleSettingChange(setting.id, e.target.value)}
              disabled={setting.readonly}
              className={`w-full px-3 py-2 ${isRTL ? 'pl-10 pr-3' : 'pr-10 pl-3'} border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
            />
            <button
              type="button"
              onClick={() => toggleSensitiveVisibility(setting.id)}
              className={`absolute top-1/2 transform -translate-y-1/2 ${isRTL ? 'left-3' : 'right-3'} text-gray-400 hover:text-gray-600 dark:hover:text-gray-300`}
            >
              {isVisible ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
        );

      default:
        return (
          <input
            type={setting.type}
            value={value}
            onChange={(e) => handleSettingChange(setting.id, setting.type === 'number' ? Number(e.target.value) : e.target.value)}
            disabled={setting.readonly}
            min={setting.validation?.min}
            max={setting.validation?.max}
            pattern={setting.validation?.pattern}
            className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
            placeholder={setting.description}
          />
        );
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className={`flex flex-col sm:flex-row sm:items-center justify-between gap-4 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
        <div className={isRTL ? 'text-right' : 'text-left'}>
          <h3 className={`text-xl font-bold text-gray-900 dark:text-white flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Settings className={`w-6 h-6 text-blue-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            {t('settings.title')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('settings.subtitle')}</p>
        </div>
        <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {unsavedChanges && (
            <span className="text-sm text-orange-600 dark:text-orange-400 flex items-center">
              <AlertTriangle className={`w-4 h-4 ${isRTL ? 'ml-1' : 'mr-1'}`} />
              Unsaved changes
            </span>
          )}
          <button 
            onClick={() => setSettings({})}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            <RotateCcw className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('settings.actions.reset')}
          </button>
          <button 
            onClick={handleSaveSettings}
            disabled={!unsavedChanges}
            className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
          >
            <Save className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('settings.actions.save')}
          </button>
        </div>
      </div>

      {/* Settings Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Categories Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Categories</h4>
            <nav className="space-y-2">
              {categories.map((category) => {
                const Icon = category.icon;
                return (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`w-full ${isRTL ? 'text-right' : 'text-left'} px-3 py-2 rounded-lg transition-colors ${
                      activeCategory === category.id
                        ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                  >
                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Icon className={`w-4 h-4 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                      <span className="text-sm font-medium">{category.label}</span>
                    </div>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                {categories.find(c => c.id === activeCategory)?.label}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Configure {activeCategory} settings for your platform
              </p>
            </div>

            <div className="p-6">
              <div className="space-y-6">
                {filteredSettings.map((setting) => (
                  <div key={setting.id} className="space-y-2">
                    <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={isRTL ? 'text-right' : 'text-left'}>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          {setting.label}
                          {setting.required && <span className="text-red-500 ml-1">*</span>}
                        </label>
                        {setting.description && (
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {setting.description}
                          </p>
                        )}
                      </div>

                      <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        {setting.sensitive && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
                            <Lock className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                            Sensitive
                          </span>
                        )}

                        {setting.readonly && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
                            <Eye className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                            Read Only
                          </span>
                        )}

                        {(setting.type === 'url' || setting.key.includes('rpc') || setting.key.includes('webhook')) && (
                          <button
                            onClick={() => handleTestSetting(setting.id)}
                            disabled={testResults[setting.id] === 'pending'}
                            className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                            title={t('settings.actions.test')}
                          >
                            {testResults[setting.id] === 'pending' ? (
                              <RefreshCw className="w-4 h-4 animate-spin" />
                            ) : testResults[setting.id] === 'success' ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : testResults[setting.id] === 'error' ? (
                              <XCircle className="w-4 h-4 text-red-500" />
                            ) : (
                              <TestTube className="w-4 h-4" />
                            )}
                          </button>
                        )}

                        {setting.sensitive && setting.readonly && (
                          <button
                            onClick={() => navigator.clipboard.writeText(getSettingValue(setting))}
                            className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                            title="Copy to clipboard"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    </div>

                    <div className="mt-2">
                      {renderSettingInput(setting)}
                    </div>

                    {setting.validation && (
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {setting.validation.min !== undefined && setting.validation.max !== undefined && (
                          <span>Range: {setting.validation.min} - {setting.validation.max}</span>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Category-specific actions */}
              <div className={`mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                {activeCategory === 'backup' && (
                  <>
                    <button className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                      <HardDrive className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      {t('settings.actions.backup')}
                    </button>
                    <button className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                      <Upload className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      {t('settings.actions.restore')}
                    </button>
                  </>
                )}

                {activeCategory === 'api' && (
                  <>
                    <button className="flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                      <Key className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      Generate New API Key
                    </button>
                    <button className="flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors">
                      <FileText className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      View API Docs
                    </button>
                  </>
                )}

                {activeCategory === 'blockchain' && (
                  <>
                    <button className="flex items-center px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors">
                      <Activity className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      Test All Connections
                    </button>
                    <button className="flex items-center px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg transition-colors">
                      <RefreshCw className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      Sync Networks
                    </button>
                  </>
                )}

                {activeCategory === 'maintenance' && (
                  <>
                    <button className="flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors">
                      <Wrench className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      Enable Maintenance Mode
                    </button>
                    <button className="flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                      <Trash2 className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      Clear Cache
                    </button>
                  </>
                )}

                <button className="flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
                  <Download className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('settings.actions.export')}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Settings</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{settingsData.length}</p>
            </div>
            <Settings className="w-8 h-8 text-gray-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Modified Settings</p>
              <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">{Object.keys(settings).length}</p>
            </div>
            <Edit className="w-8 h-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Sensitive Settings</p>
              <p className="text-2xl font-bold text-red-600 dark:text-red-400">{settingsData.filter(s => s.sensitive).length}</p>
            </div>
            <Lock className="w-8 h-8 text-red-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Last Backup</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">2h ago</p>
            </div>
            <HardDrive className="w-8 h-8 text-green-500" />
          </div>
        </div>
      </div>
    </div>
  );
}
