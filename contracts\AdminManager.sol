// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

/**
 * @title AdminManager - Enhanced Administration System
 * @dev Manages administrative functions and system governance
 * <AUTHOR> P2P Team
 * @notice This contract handles platform administration and governance
 */
contract AdminManager is AccessControl, ReentrancyGuard, Pausable {
    using SafeERC20 for IERC20;
    
    // Role definitions
    bytes32 public constant SUPER_ADMIN_ROLE = keccak256("SUPER_ADMIN_ROLE");
    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant MODERATOR_ROLE = keccak256("MODERATOR_ROLE");
    bytes32 public constant DISPUTE_RESOLVER_ROLE = keccak256("DISPUTE_RESOLVER_ROLE");
    bytes32 public constant EMERGENCY_ROLE = keccak256("EMERGENCY_ROLE");

    // Admin levels
    enum AdminLevel {
        None,           // 0: No admin privileges
        Moderator,      // 1: Basic moderation
        Admin,          // 2: Platform administration
        SuperAdmin      // 3: Full system control
    }

    // Admin profile structure
    struct AdminProfile {
        AdminLevel level;
        string name;
        string email;
        uint256 addedAt;
        uint256 lastActivity;
        bool isActive;
        address addedBy;
        uint256 actionsCount;
        string[] permissions;
    }

    // System settings structure
    struct SystemSettings {
        uint256 platformFeeRate;
        uint256 maxTradeAmount;
        uint256 minTradeAmount;
        uint256 disputeTimeLimit;
        uint256 tradeTimeLimit;
        bool emergencyPause;
        bool maintenanceMode;
        string maintenanceMessage;
    }

    // Dispute resolution structure
    struct DisputeResolution {
        uint256 tradeId;
        address resolver;
        address winner;
        string reason;
        uint256 resolvedAt;
        bool isResolved;
        uint256 compensationAmount;
        address compensationToken;
    }

    // State variables
    mapping(address => AdminProfile) public adminProfiles;
    mapping(uint256 => DisputeResolution) public disputeResolutions;
    mapping(address => bool) public blacklistedUsers;
    mapping(address => string) public blacklistReasons;
    
    address[] public adminList;
    uint256[] public resolvedDisputes;
    
    SystemSettings public systemSettings;
    
    // Emergency settings
    address public emergencyContact;
    uint256 public emergencyPauseTime;
    bool public emergencyMode;
    
    // Statistics
    uint256 public totalAdmins;
    uint256 public totalDisputesResolved;
    uint256 public totalBlacklistedUsers;

    // Events
    event AdminAdded(
        address indexed admin,
        AdminLevel level,
        string name,
        address indexed addedBy
    );

    event AdminRemoved(
        address indexed admin,
        address indexed removedBy,
        string reason
    );

    event AdminLevelChanged(
        address indexed admin,
        AdminLevel oldLevel,
        AdminLevel newLevel,
        address indexed changedBy
    );

    event DisputeResolved(
        uint256 indexed tradeId,
        address indexed resolver,
        address indexed winner,
        string reason
    );

    event UserBlacklisted(
        address indexed user,
        address indexed admin,
        string reason
    );

    event UserUnblacklisted(
        address indexed user,
        address indexed admin,
        string reason
    );

    event SystemSettingsUpdated(
        string setting,
        uint256 oldValue,
        uint256 newValue,
        address indexed updatedBy
    );

    event EmergencyActivated(
        address indexed activatedBy,
        string reason,
        uint256 timestamp
    );

    event EmergencyDeactivated(
        address indexed deactivatedBy,
        uint256 timestamp
    );

    event MaintenanceModeToggled(
        bool enabled,
        string message,
        address indexed toggledBy
    );

    // Modifiers
    modifier onlyActiveAdmin() {
        require(adminProfiles[msg.sender].isActive, "Admin not active");
        require(adminProfiles[msg.sender].level != AdminLevel.None, "Not an admin");
        _;
    }

    modifier onlyAdminLevel(AdminLevel minLevel) {
        require(adminProfiles[msg.sender].level >= minLevel, "Insufficient admin level");
        _;
    }

    modifier notInEmergency() {
        require(!emergencyMode, "System in emergency mode");
        _;
    }

    modifier notInMaintenance() {
        require(!systemSettings.maintenanceMode, "System in maintenance mode");
        _;
    }

    /**
     * @dev Constructor
     * @param _emergencyContact Emergency contact address
     */
    constructor(address _emergencyContact) {
        require(_emergencyContact != address(0), "Invalid emergency contact");
        
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _grantRole(SUPER_ADMIN_ROLE, msg.sender);
        _grantRole(EMERGENCY_ROLE, msg.sender);
        
        emergencyContact = _emergencyContact;
        
        // Initialize system settings
        systemSettings = SystemSettings({
            platformFeeRate: 50, // 0.5%
            maxTradeAmount: 1000000 * 10**18, // 1M tokens
            minTradeAmount: 1 * 10**18, // 1 token
            disputeTimeLimit: 7 days,
            tradeTimeLimit: 24 hours,
            emergencyPause: false,
            maintenanceMode: false,
            maintenanceMessage: ""
        });
        
        // Add creator as super admin
        _addAdmin(msg.sender, AdminLevel.SuperAdmin, "System Creator", "<EMAIL>", msg.sender);
    }

    /**
     * @dev Add a new admin
     * @param admin Admin address
     * @param level Admin level
     * @param name Admin name
     * @param email Admin email
     */
    function addAdmin(
        address admin,
        AdminLevel level,
        string memory name,
        string memory email
    ) external onlyRole(SUPER_ADMIN_ROLE) {
        _addAdmin(admin, level, name, email, msg.sender);
    }

    /**
     * @dev Internal function to add admin
     */
    function _addAdmin(
        address admin,
        AdminLevel level,
        string memory name,
        string memory email,
        address addedBy
    ) internal {
        require(admin != address(0), "Invalid admin address");
        require(level != AdminLevel.None, "Invalid admin level");
        require(bytes(name).length > 0, "Name required");
        require(!adminProfiles[admin].isActive, "Admin already exists");

        // Grant appropriate roles
        if (level == AdminLevel.SuperAdmin) {
            _grantRole(SUPER_ADMIN_ROLE, admin);
            _grantRole(ADMIN_ROLE, admin);
            _grantRole(MODERATOR_ROLE, admin);
            _grantRole(DISPUTE_RESOLVER_ROLE, admin);
        } else if (level == AdminLevel.Admin) {
            _grantRole(ADMIN_ROLE, admin);
            _grantRole(MODERATOR_ROLE, admin);
            _grantRole(DISPUTE_RESOLVER_ROLE, admin);
        } else if (level == AdminLevel.Moderator) {
            _grantRole(MODERATOR_ROLE, admin);
        }

        adminProfiles[admin] = AdminProfile({
            level: level,
            name: name,
            email: email,
            addedAt: block.timestamp,
            lastActivity: block.timestamp,
            isActive: true,
            addedBy: addedBy,
            actionsCount: 0,
            permissions: new string[](0)
        });

        adminList.push(admin);
        totalAdmins++;

        emit AdminAdded(admin, level, name, addedBy);
    }

    /**
     * @dev Remove an admin
     * @param admin Admin address to remove
     * @param reason Reason for removal
     */
    function removeAdmin(address admin, string memory reason) 
        external 
        onlyRole(SUPER_ADMIN_ROLE) 
    {
        require(adminProfiles[admin].isActive, "Admin not active");
        require(admin != msg.sender, "Cannot remove yourself");

        AdminProfile storage profile = adminProfiles[admin];
        profile.isActive = false;

        // Revoke all roles
        _revokeRole(SUPER_ADMIN_ROLE, admin);
        _revokeRole(ADMIN_ROLE, admin);
        _revokeRole(MODERATOR_ROLE, admin);
        _revokeRole(DISPUTE_RESOLVER_ROLE, admin);

        // Remove from admin list
        for (uint256 i = 0; i < adminList.length; i++) {
            if (adminList[i] == admin) {
                adminList[i] = adminList[adminList.length - 1];
                adminList.pop();
                break;
            }
        }

        totalAdmins--;

        emit AdminRemoved(admin, msg.sender, reason);
    }

    /**
     * @dev Change admin level
     * @param admin Admin address
     * @param newLevel New admin level
     */
    function changeAdminLevel(address admin, AdminLevel newLevel) 
        external 
        onlyRole(SUPER_ADMIN_ROLE) 
    {
        require(adminProfiles[admin].isActive, "Admin not active");
        require(admin != msg.sender, "Cannot change own level");

        AdminLevel oldLevel = adminProfiles[admin].level;
        adminProfiles[admin].level = newLevel;

        // Update roles based on new level
        _revokeRole(SUPER_ADMIN_ROLE, admin);
        _revokeRole(ADMIN_ROLE, admin);
        _revokeRole(MODERATOR_ROLE, admin);
        _revokeRole(DISPUTE_RESOLVER_ROLE, admin);

        if (newLevel == AdminLevel.SuperAdmin) {
            _grantRole(SUPER_ADMIN_ROLE, admin);
            _grantRole(ADMIN_ROLE, admin);
            _grantRole(MODERATOR_ROLE, admin);
            _grantRole(DISPUTE_RESOLVER_ROLE, admin);
        } else if (newLevel == AdminLevel.Admin) {
            _grantRole(ADMIN_ROLE, admin);
            _grantRole(MODERATOR_ROLE, admin);
            _grantRole(DISPUTE_RESOLVER_ROLE, admin);
        } else if (newLevel == AdminLevel.Moderator) {
            _grantRole(MODERATOR_ROLE, admin);
        }

        emit AdminLevelChanged(admin, oldLevel, newLevel, msg.sender);
    }

    /**
     * @dev Resolve a dispute
     * @param tradeId Trade ID
     * @param winner Address of the dispute winner
     * @param reason Reason for the resolution
     * @param compensationAmount Compensation amount if any
     * @param compensationToken Token for compensation
     */
    function resolveDispute(
        uint256 tradeId,
        address winner,
        string memory reason,
        uint256 compensationAmount,
        address compensationToken
    ) external onlyRole(DISPUTE_RESOLVER_ROLE) {
        require(winner != address(0), "Invalid winner address");
        require(bytes(reason).length > 0, "Reason required");
        require(!disputeResolutions[tradeId].isResolved, "Dispute already resolved");

        disputeResolutions[tradeId] = DisputeResolution({
            tradeId: tradeId,
            resolver: msg.sender,
            winner: winner,
            reason: reason,
            resolvedAt: block.timestamp,
            isResolved: true,
            compensationAmount: compensationAmount,
            compensationToken: compensationToken
        });

        resolvedDisputes.push(tradeId);
        totalDisputesResolved++;

        // Update admin activity
        adminProfiles[msg.sender].lastActivity = block.timestamp;
        adminProfiles[msg.sender].actionsCount++;

        emit DisputeResolved(tradeId, msg.sender, winner, reason);
    }

    /**
     * @dev Blacklist a user
     * @param user User address to blacklist
     * @param reason Reason for blacklisting
     */
    function blacklistUser(address user, string memory reason) 
        external 
        onlyRole(MODERATOR_ROLE) 
    {
        require(user != address(0), "Invalid user address");
        require(!adminProfiles[user].isActive, "Cannot blacklist admin");
        require(!blacklistedUsers[user], "User already blacklisted");
        require(bytes(reason).length > 0, "Reason required");

        blacklistedUsers[user] = true;
        blacklistReasons[user] = reason;
        totalBlacklistedUsers++;

        // Update admin activity
        adminProfiles[msg.sender].lastActivity = block.timestamp;
        adminProfiles[msg.sender].actionsCount++;

        emit UserBlacklisted(user, msg.sender, reason);
    }

    /**
     * @dev Remove user from blacklist
     * @param user User address to unblacklist
     * @param reason Reason for removal
     */
    function unblacklistUser(address user, string memory reason) 
        external 
        onlyRole(MODERATOR_ROLE) 
    {
        require(blacklistedUsers[user], "User not blacklisted");
        require(bytes(reason).length > 0, "Reason required");

        blacklistedUsers[user] = false;
        delete blacklistReasons[user];
        totalBlacklistedUsers--;

        // Update admin activity
        adminProfiles[msg.sender].lastActivity = block.timestamp;
        adminProfiles[msg.sender].actionsCount++;

        emit UserUnblacklisted(user, msg.sender, reason);
    }

    /**
     * @dev Update system settings
     * @param setting Setting name
     * @param value New value
     */
    function updateSystemSetting(string memory setting, uint256 value) 
        external 
        onlyRole(ADMIN_ROLE) 
    {
        uint256 oldValue;
        
        if (keccak256(bytes(setting)) == keccak256(bytes("platformFeeRate"))) {
            require(value <= 1000, "Fee rate too high"); // Max 10%
            oldValue = systemSettings.platformFeeRate;
            systemSettings.platformFeeRate = value;
        } else if (keccak256(bytes(setting)) == keccak256(bytes("maxTradeAmount"))) {
            require(value > systemSettings.minTradeAmount, "Invalid max amount");
            oldValue = systemSettings.maxTradeAmount;
            systemSettings.maxTradeAmount = value;
        } else if (keccak256(bytes(setting)) == keccak256(bytes("minTradeAmount"))) {
            require(value < systemSettings.maxTradeAmount, "Invalid min amount");
            oldValue = systemSettings.minTradeAmount;
            systemSettings.minTradeAmount = value;
        } else if (keccak256(bytes(setting)) == keccak256(bytes("disputeTimeLimit"))) {
            require(value >= 1 hours && value <= 30 days, "Invalid dispute time limit");
            oldValue = systemSettings.disputeTimeLimit;
            systemSettings.disputeTimeLimit = value;
        } else if (keccak256(bytes(setting)) == keccak256(bytes("tradeTimeLimit"))) {
            require(value >= 1 hours && value <= 7 days, "Invalid trade time limit");
            oldValue = systemSettings.tradeTimeLimit;
            systemSettings.tradeTimeLimit = value;
        } else {
            revert("Invalid setting");
        }

        // Update admin activity
        adminProfiles[msg.sender].lastActivity = block.timestamp;
        adminProfiles[msg.sender].actionsCount++;

        emit SystemSettingsUpdated(setting, oldValue, value, msg.sender);
    }

    /**
     * @dev Activate emergency mode
     * @param reason Reason for emergency activation
     */
    function activateEmergency(string memory reason) 
        external 
        onlyRole(EMERGENCY_ROLE) 
    {
        require(!emergencyMode, "Emergency already active");
        require(bytes(reason).length > 0, "Reason required");

        emergencyMode = true;
        emergencyPauseTime = block.timestamp;
        systemSettings.emergencyPause = true;

        emit EmergencyActivated(msg.sender, reason, block.timestamp);
    }

    /**
     * @dev Deactivate emergency mode
     */
    function deactivateEmergency() 
        external 
        onlyRole(EMERGENCY_ROLE) 
    {
        require(emergencyMode, "Emergency not active");

        emergencyMode = false;
        emergencyPauseTime = 0;
        systemSettings.emergencyPause = false;

        emit EmergencyDeactivated(msg.sender, block.timestamp);
    }

    /**
     * @dev Toggle maintenance mode
     * @param enabled Whether to enable maintenance mode
     * @param message Maintenance message
     */
    function toggleMaintenanceMode(bool enabled, string memory message) 
        external 
        onlyRole(ADMIN_ROLE) 
    {
        systemSettings.maintenanceMode = enabled;
        systemSettings.maintenanceMessage = message;

        emit MaintenanceModeToggled(enabled, message, msg.sender);
    }

    /**
     * @dev Check if user is admin
     * @param user User address
     * @return bool True if user is admin
     */
    function isAdmin(address user) external view returns (bool) {
        return adminProfiles[user].isActive && adminProfiles[user].level != AdminLevel.None;
    }

    /**
     * @dev Get admin profile
     * @param admin Admin address
     * @return AdminProfile struct
     */
    function getAdminProfile(address admin) external view returns (AdminProfile memory) {
        return adminProfiles[admin];
    }

    /**
     * @dev Get all admins
     * @return Array of admin addresses
     */
    function getAllAdmins() external view returns (address[] memory) {
        return adminList;
    }

    /**
     * @dev Get system settings
     * @return SystemSettings struct
     */
    function getSystemSettings() external view returns (SystemSettings memory) {
        return systemSettings;
    }

    /**
     * @dev Get dispute resolution
     * @param tradeId Trade ID
     * @return DisputeResolution struct
     */
    function getDisputeResolution(uint256 tradeId) external view returns (DisputeResolution memory) {
        return disputeResolutions[tradeId];
    }

    /**
     * @dev Emergency withdrawal function
     * @param token Token address (address(0) for ETH)
     * @param amount Amount to withdraw
     * @param recipient Recipient address
     */
    function emergencyWithdraw(
        address token,
        uint256 amount,
        address recipient
    ) external onlyRole(SUPER_ADMIN_ROLE) {
        require(recipient != address(0), "Invalid recipient");
        require(emergencyMode, "Emergency mode not active");

        if (token == address(0)) {
            payable(recipient).transfer(amount);
        } else {
            IERC20(token).safeTransfer(recipient, amount);
        }
    }

    // Admin functions
    // Admin functions
    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
    }

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
    }
}
