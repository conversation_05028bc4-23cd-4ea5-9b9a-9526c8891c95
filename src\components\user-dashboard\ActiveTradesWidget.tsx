'use client';

import {
  Activity,
  Clock,
  User,
  MessageSquare,
  Eye,
  AlertCircle,
  CheckCircle,
  ArrowRight,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { useUserTrades } from '@/hooks/user-dashboard/useUserTrades';
import { useAuth } from '@/contexts/AuthContext';

// إزالة interface ActiveTrade لأننا سنستخدم Trade من hook

export default function ActiveTradesWidget() {
  const { t, formatCurrency, formatDate } = useUserDashboardTranslation();
  const { user } = useAuth();

  // استخدام hook الصفقات الحقيقي
  const { activeTrades, loading, error, refresh } = useUserTrades({
    autoRefresh: true,
    refreshInterval: 30 * 1000, // تحديث كل 30 ثانية
    pageSize: 5 // أول 5 صفقات نشطة
  });

  // تحويل الوقت المتبقي من تاريخ انتهاء الصلاحية
  const getTimeRemaining = (expiresAt?: string) => {
    if (!expiresAt) return null;

    const now = new Date();
    const expiry = new Date(expiresAt);
    const diffMs = expiry.getTime() - now.getTime();

    if (diffMs <= 0) return 'منتهية الصلاحية';

    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}س ${minutes}د`;
    }
    return `${minutes}د`;
  };

  // الحصول على لون الحالة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'created':
        return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      case 'joined':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30';
      case 'payment_sent':
        return 'text-orange-600 dark:text-orange-400 bg-orange-100 dark:bg-orange-900/30';
      case 'payment_received':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'disputed':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  // الحصول على نص الحالة
  const getStatusText = (status: string) => {
    switch (status) {
      case 'created':
        return 'تم الإنشاء';
      case 'joined':
        return 'تم الانضمام';
      case 'payment_sent':
        return 'تم إرسال الدفع';
      case 'payment_received':
        return 'تم استلام الدفع';
      case 'disputed':
        return 'متنازع عليها';
      default:
        return 'غير معروف';
    }
  };

  // حساب التقدم بناءً على الحالة
  const getProgress = (status: string) => {
    switch (status) {
      case 'created':
        return 25;
      case 'joined':
        return 50;
      case 'payment_sent':
        return 75;
      case 'payment_received':
        return 90;
      case 'completed':
        return 100;
      default:
        return 0;
    }
  };

  // تحديد نوع الصفقة بناءً على معرف المستخدم
  const getTradeType = (trade: any) => {
    if (!user?.id) return 'unknown';
    return trade.seller_id === user.id ? 'sell' : 'buy';
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            الصفقات النشطة
          </h3>
          <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
        </div>
        <div className="space-y-4">
          {[1, 2].map((i) => (
            <div key={i} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg animate-pulse">
              <div className="h-4 w-3/4 bg-gray-200 dark:bg-gray-600 rounded mb-2" />
              <div className="h-3 w-1/2 bg-gray-200 dark:bg-gray-600 rounded" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            الصفقات النشطة
          </h3>
          <AlertCircle className="w-5 h-5 text-red-500" />
        </div>
        <div className="text-center py-8">
          <AlertCircle className="w-12 h-12 text-red-400 dark:text-red-500 mx-auto mb-3" />
          <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
          <button
            onClick={refresh}
            className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <span>إعادة المحاولة</span>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          الصفقات النشطة
        </h3>
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {activeTrades ? activeTrades.length : 0} صفقة نشطة
          </span>
          <Activity className="w-4 h-4 text-blue-600 dark:text-blue-400" />
        </div>
      </div>

      {activeTrades && activeTrades.length > 0 ? (
        <div className="space-y-4">
          {activeTrades.slice(0, 5).map((trade) => {
            const tradeType = getTradeType(trade);
            const progress = getProgress(trade.status);
            const timeRemaining = getTimeRemaining(trade.expires_at);

            return (
              <div key={trade.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                {/* Header */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <div className={`w-3 h-3 rounded-full ${
                      tradeType === 'buy' ? 'bg-green-500' : 'bg-red-500'
                    }`} />
                    <div className="flex items-center space-x-1 rtl:space-x-reverse">
                      {tradeType === 'buy' ? (
                        <TrendingUp className="w-4 h-4 text-green-600" />
                      ) : (
                        <TrendingDown className="w-4 h-4 text-red-600" />
                      )}
                      <span className="font-medium text-gray-900 dark:text-white">
                        {tradeType === 'buy' ? 'شراء' : 'بيع'} {trade.amount} {trade.stablecoin}
                      </span>
                    </div>
                  </div>

                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(trade.status)}`}>
                    {getStatusText(trade.status)}
                  </span>
                </div>

                {/* Details */}
                <div className="grid grid-cols-2 gap-4 mb-3">
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">الشريك</p>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <User className="w-3 h-3 text-gray-400" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {trade.partner_info?.username || 'غير متاح'}
                      </span>
                      {trade.partner_info?.is_verified && (
                        <CheckCircle className="w-3 h-3 text-green-500" />
                      )}
                    </div>
                  </div>

                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">الإجمالي</p>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatCurrency(parseFloat(trade.total_value), trade.currency)}
                    </p>
                  </div>
                </div>

                {/* Progress */}
                <div className="mb-3">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs text-gray-500 dark:text-gray-400">التقدم</span>
                    <span className="text-xs font-medium text-gray-900 dark:text-white">
                      {progress}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                </div>

                {/* Footer */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    {timeRemaining && (
                      <div className="flex items-center space-x-1 rtl:space-x-reverse text-xs text-gray-500 dark:text-gray-400">
                        <Clock className="w-3 h-3" />
                        <span>{timeRemaining} متبقية</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <a
                      href={`/user-dashboard/trades/${trade.id}`}
                      className="text-xs text-blue-600 dark:text-blue-400 hover:underline flex items-center space-x-1 rtl:space-x-reverse"
                    >
                      <Eye className="w-3 h-3" />
                      <span>عرض</span>
                    </a>
                    <ArrowRight className="w-3 h-3 text-gray-400" />
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-8">
          <Activity className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-3" />
          <p className="text-gray-500 dark:text-gray-400 mb-4">لا توجد صفقات نشطة حالياً</p>
          <a 
            href="/offers" 
            className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <span>تصفح العروض</span>
            <ArrowRight className="w-4 h-4" />
          </a>
        </div>
      )}
    </div>
  );
}
