/**
 * أداة تفعيل المزامنة مع العقد الذكي
 * Enable Smart Contract Synchronization Utility
 */

import { syncService } from '@/services/syncService';
import { contractEventService } from '@/services/contractEventService';
import { notificationService } from '@/services/notificationService';

export interface SyncConfiguration {
  enablePeriodicSync: boolean;
  syncIntervalMs: number;
  enableEventMonitoring: boolean;
  enableNotifications: boolean;
  autoStart: boolean;
}

export const DEFAULT_SYNC_CONFIG: SyncConfiguration = {
  enablePeriodicSync: true,
  syncIntervalMs: 30000, // 30 ثانية
  enableEventMonitoring: true,
  enableNotifications: true,
  autoStart: false // تعطيل التفعيل التلقائي مؤقتاً حتى يتم إصلاح APIs
};

class SyncManager {
  private isInitialized: boolean = false;
  private config: SyncConfiguration = DEFAULT_SYNC_CONFIG;

  /**
   * تهيئة وتفعيل المزامنة
   */
  async initialize(config: Partial<SyncConfiguration> = {}): Promise<void> {
    if (this.isInitialized) {
      console.log('⚠️ المزامنة مفعلة بالفعل');
      return;
    }

    this.config = { ...DEFAULT_SYNC_CONFIG, ...config };

    try {
      console.log('🚀 بدء تفعيل المزامنة مع العقد الذكي...');

      // تفعيل المزامنة الدورية
      if (this.config.enablePeriodicSync) {
        await this.enablePeriodicSync();
      }

      // تفعيل مراقبة الأحداث
      if (this.config.enableEventMonitoring) {
        await this.enableEventMonitoring();
      }

      // تفعيل الإشعارات
      if (this.config.enableNotifications) {
        this.enableNotifications();
      }

      this.isInitialized = true;
      console.log('✅ تم تفعيل المزامنة بنجاح');

      // إشعار للمستخدم
      if (this.config.enableNotifications) {
        notificationService.success('تم تفعيل المزامنة مع العقد الذكي');
      }

    } catch (error) {
      console.error('❌ خطأ في تفعيل المزامنة:', error);
      
      if (this.config.enableNotifications) {
        notificationService.error('فشل في تفعيل المزامنة مع العقد الذكي');
      }
      
      throw error;
    }
  }

  /**
   * تفعيل المزامنة الدورية
   */
  private async enablePeriodicSync(): Promise<void> {
    try {
      console.log('🔄 تفعيل المزامنة الدورية...');
      
      await syncService.startPeriodicSync(this.config.syncIntervalMs);
      
      console.log(`✅ تم تفعيل المزامنة الدورية (كل ${this.config.syncIntervalMs / 1000} ثانية)`);
    } catch (error) {
      console.error('❌ خطأ في تفعيل المزامنة الدورية:', error);
      throw error;
    }
  }

  /**
   * تفعيل مراقبة الأحداث
   */
  private async enableEventMonitoring(): Promise<void> {
    try {
      console.log('👁️ تفعيل مراقبة أحداث العقد الذكي...');
      
      await contractEventService.startMonitoring();
      
      console.log('✅ تم تفعيل مراقبة الأحداث');
    } catch (error) {
      console.error('❌ خطأ في تفعيل مراقبة الأحداث:', error);
      // لا نرمي خطأ هنا لأن مراقبة الأحداث اختيارية
      console.warn('⚠️ سيتم المتابعة بدون مراقبة الأحداث الفورية');
    }
  }

  /**
   * تفعيل الإشعارات
   */
  private enableNotifications(): void {
    console.log('🔔 تفعيل إشعارات المزامنة...');
    
    // إعداد مستمعي أحداث المزامنة
    syncService.on('syncCompleted', (stats) => {
      console.log('📊 اكتملت المزامنة:', stats);
    });

    syncService.on('syncError', (error) => {
      console.error('❌ خطأ في المزامنة:', error);
      notificationService.error(`خطأ في المزامنة: ${error.message}`);
    });

    console.log('✅ تم تفعيل إشعارات المزامنة');
  }

  /**
   * إيقاف المزامنة
   */
  async stop(): Promise<void> {
    if (!this.isInitialized) {
      console.log('⚠️ المزامنة غير مفعلة');
      return;
    }

    try {
      console.log('⏹️ إيقاف المزامنة...');

      // إيقاف المزامنة الدورية
      syncService.stopPeriodicSync();

      // إيقاف مراقبة الأحداث
      await contractEventService.stopMonitoring();

      this.isInitialized = false;
      console.log('✅ تم إيقاف المزامنة');

      if (this.config.enableNotifications) {
        notificationService.info('تم إيقاف المزامنة مع العقد الذكي');
      }

    } catch (error) {
      console.error('❌ خطأ في إيقاف المزامنة:', error);
      throw error;
    }
  }

  /**
   * إعادة تشغيل المزامنة
   */
  async restart(): Promise<void> {
    await this.stop();
    await this.initialize(this.config);
  }

  /**
   * الحصول على حالة المزامنة
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      config: this.config,
      syncStats: syncService.getSyncStats(),
      eventStats: contractEventService.getStats()
    };
  }

  /**
   * تحديث إعدادات المزامنة
   */
  async updateConfig(newConfig: Partial<SyncConfiguration>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    
    if (this.isInitialized) {
      await this.restart();
    }
  }

  /**
   * تشغيل مزامنة فورية
   */
  async forceSyncNow(): Promise<void> {
    try {
      console.log('⚡ تشغيل مزامنة فورية...');
      
      const stats = await syncService.syncAll();
      
      console.log('✅ اكتملت المزامنة الفورية:', stats);
      
      if (this.config.enableNotifications) {
        notificationService.success('تمت المزامنة الفورية بنجاح');
      }
      
      return stats;
    } catch (error) {
      console.error('❌ خطأ في المزامنة الفورية:', error);
      
      if (this.config.enableNotifications) {
        notificationService.error('فشلت المزامنة الفورية');
      }
      
      throw error;
    }
  }
}

// إنشاء مثيل واحد للاستخدام في التطبيق
export const syncManager = new SyncManager();

// تفعيل تلقائي عند تحميل الوحدة (محسن للأداء)
if (typeof window !== 'undefined' &&
    DEFAULT_SYNC_CONFIG.autoStart &&
    process.env.NODE_ENV === 'production' &&
    !process.env.NEXT_PUBLIC_DISABLE_AUTO_SYNC) {
  // تأخير أطول في التطوير لتحسين الأداء
  const delay = process.env.NODE_ENV === 'development' ? 15000 : 5000;
  setTimeout(() => {
    syncManager.initialize().catch(error => {
      console.warn('⚠️ فشل في التفعيل التلقائي للمزامنة:', error);
    });
  }, delay);
}

export default syncManager;
