/**
 * خدمة مزامنة البيانات بين قاعدة البيانات والعقد الذكي
 * تضمن التطابق الكامل بين البيانات المحلية وحالة العقد
 */

import { contractService } from './contractService';
import { databaseService } from './databaseService';
import { notificationService } from './notificationService';
import { EventEmitter } from 'events';

export interface SyncResult {
  success: boolean;
  entityId: string;
  entityType: 'offer' | 'trade' | 'contract_event';
  changes: string[];
  errors: string[];
}

export interface SyncStats {
  totalEntities: number;
  syncedEntities: number;
  failedEntities: number;
  pendingEntities: number;
  lastSyncTime: Date;
  averageSyncTime: number;
}

class SyncService extends EventEmitter {
  private syncInterval: NodeJS.Timeout | null = null;
  private isRunning: boolean = false;
  private syncStats: SyncStats = {
    totalEntities: 0,
    syncedEntities: 0,
    failedEntities: 0,
    pendingEntities: 0,
    lastSyncTime: new Date(),
    averageSyncTime: 0
  };

  constructor() {
    super();
    this.setupEventListeners();
  }

  /**
   * بدء المزامنة الدورية
   */
  async startPeriodicSync(intervalMs: number = 30000): Promise<void> {
    if (this.syncInterval) {
      this.stopPeriodicSync();
    }

    console.log(`🔄 بدء المزامنة الدورية كل ${intervalMs / 1000} ثانية`);
    
    // تشغيل مزامنة فورية
    await this.syncAll();
    
    // جدولة المزامنة الدورية
    this.syncInterval = setInterval(async () => {
      if (!this.isRunning) {
        await this.syncAll();
      }
    }, intervalMs);

    // بدء مراقبة أحداث العقد
    await this.startEventMonitoring();
  }

  /**
   * إيقاف المزامنة الدورية
   */
  stopPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      console.log('⏹️ تم إيقاف المزامنة الدورية');
    }
  }

  /**
   * مزامنة جميع البيانات
   */
  async syncAll(): Promise<SyncStats> {
    if (this.isRunning) {
      console.log('⚠️ المزامنة قيد التشغيل بالفعل');
      return this.syncStats;
    }

    this.isRunning = true;
    const startTime = Date.now();

    try {
      console.log('🔄 بدء المزامنة الشاملة...');
      
      // مزامنة العروض
      const offerResults = await this.syncAllOffers();
      
      // مزامنة الصفقات
      const tradeResults = await this.syncAllTrades();
      
      // معالجة أحداث العقد المعلقة
      const eventResults = await this.processContractEvents();
      
      // تحديث الإحصائيات
      this.updateSyncStats([...offerResults, ...tradeResults, ...eventResults]);
      
      const duration = Date.now() - startTime;
      console.log(`✅ اكتملت المزامنة في ${duration}ms`);
      
      this.emit('syncCompleted', this.syncStats);
      
      return this.syncStats;
      
    } catch (error) {
      console.error('❌ خطأ في المزامنة الشاملة:', error);
      this.emit('syncError', error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * مزامنة جميع العروض
   */
  async syncAllOffers(): Promise<SyncResult[]> {
    try {
      const offers = await databaseService.getOffersWithBlockchainId();
      const results: SyncResult[] = [];

      for (const offer of offers) {
        try {
          const result = await this.syncOfferWithContract(offer.id);
          results.push(result);
        } catch (error) {
          console.error(`خطأ في مزامنة العرض ${offer.id}:`, error);
          results.push({
            success: false,
            entityId: offer.id,
            entityType: 'offer',
            changes: [],
            errors: [error.message]
          });
        }
      }

      return results;
    } catch (error) {
      console.error('خطأ في مزامنة العروض:', error);
      return [];
    }
  }

  /**
   * مزامنة عرض واحد مع العقد
   */
  async syncOfferWithContract(offerId: string): Promise<SyncResult> {
    const result: SyncResult = {
      success: false,
      entityId: offerId,
      entityType: 'offer',
      changes: [],
      errors: []
    };

    try {
      const offer = await databaseService.getOfferById(offerId);
      
      if (!offer) {
        result.errors.push('العرض غير موجود في قاعدة البيانات');
        return result;
      }

      if (!offer.blockchain_trade_id) {
        result.errors.push('العرض غير مرتبط بالعقد الذكي');
        return result;
      }

      // جلب حالة الصفقة من العقد
      const contractTrade = await contractService.getTrade(offer.blockchain_trade_id);
      
      if (!contractTrade) {
        result.errors.push('الصفقة غير موجودة في العقد الذكي');
        return result;
      }

      // مقارنة البيانات وتحديث الاختلافات
      const updates: any = {};
      
      if (contractTrade.status !== offer.contract_status) {
        updates.contract_status = contractTrade.status;
        result.changes.push(`تحديث الحالة من ${offer.contract_status || 'غير محدد'} إلى ${contractTrade.status}`);
      }

      if (contractTrade.buyer && contractTrade.buyer !== '0x0000000000000000000000000000000000000000') {
        if (!offer.buyer_address || offer.buyer_address !== contractTrade.buyer) {
          updates.buyer_address = contractTrade.buyer;
          result.changes.push(`تحديث عنوان المشتري: ${contractTrade.buyer}`);
        }
      }

      // تحديث قاعدة البيانات إذا كانت هناك تغييرات
      if (Object.keys(updates).length > 0) {
        updates.last_sync_at = new Date();
        await databaseService.updateOffer(offerId, updates);
        
        // تسجيل حالة المزامنة
        await this.updateSyncStatus('offer', parseInt(offerId), offer.blockchain_trade_id, 'synced');
        
        // إرسال إشعار بالتغيير
        await this.notifyStatusChange(offer, updates);
      }

      result.success = true;
      return result;

    } catch (error) {
      result.errors.push(error.message);
      
      // تسجيل فشل المزامنة
      await this.updateSyncStatus('offer', parseInt(offerId), null, 'failed', error.message);
      
      return result;
    }
  }

  /**
   * مزامنة جميع الصفقات
   */
  async syncAllTrades(): Promise<SyncResult[]> {
    try {
      const trades = await databaseService.getTradesWithBlockchainId();
      const results: SyncResult[] = [];

      for (const trade of trades) {
        try {
          const result = await this.syncTradeWithContract(trade.id);
          results.push(result);
        } catch (error) {
          console.error(`خطأ في مزامنة الصفقة ${trade.id}:`, error);
          results.push({
            success: false,
            entityId: trade.id,
            entityType: 'trade',
            changes: [],
            errors: [error.message]
          });
        }
      }

      return results;
    } catch (error) {
      console.error('خطأ في مزامنة الصفقات:', error);
      return [];
    }
  }

  /**
   * مزامنة صفقة واحدة مع العقد
   */
  async syncTradeWithContract(tradeId: string): Promise<SyncResult> {
    const result: SyncResult = {
      success: false,
      entityId: tradeId,
      entityType: 'trade',
      changes: [],
      errors: []
    };

    try {
      const trade = await databaseService.getTrade(tradeId);
      
      if (!trade || !trade.blockchain_trade_id) {
        result.errors.push('الصفقة غير مرتبطة بالعقد الذكي');
        return result;
      }

      const contractTrade = await contractService.getTrade(trade.blockchain_trade_id);
      
      if (!contractTrade) {
        result.errors.push('الصفقة غير موجودة في العقد الذكي');
        return result;
      }

      // مقارنة وتحديث البيانات
      const updates: any = {};
      
      if (contractTrade.status !== trade.contract_status) {
        updates.contract_status = contractTrade.status;
        updates.status = this.mapContractStatusToTradeStatus(contractTrade.status);
        result.changes.push(`تحديث حالة الصفقة إلى ${contractTrade.status}`);
      }

      if (Object.keys(updates).length > 0) {
        updates.last_sync_at = new Date();
        await databaseService.updateTrade(tradeId, updates);
        await this.updateSyncStatus('trade', parseInt(tradeId), trade.blockchain_trade_id, 'synced');
      }

      result.success = true;
      return result;

    } catch (error) {
      result.errors.push(error.message);
      await this.updateSyncStatus('trade', parseInt(tradeId), null, 'failed', error.message);
      return result;
    }
  }

  /**
   * إعداد مستمعي الأحداث
   */
  private setupEventListeners(): void {
    this.on('syncCompleted', (stats: SyncStats) => {
      console.log('📊 إحصائيات المزامنة:', stats);
    });

    this.on('syncError', (error: Error) => {
      console.error('❌ خطأ في المزامنة:', error);
      notificationService.error(`خطأ في مزامنة البيانات: ${error.message}`);
    });
  }

  /**
   * تحديث إحصائيات المزامنة
   */
  private updateSyncStats(results: SyncResult[]): void {
    this.syncStats.totalEntities = results.length;
    this.syncStats.syncedEntities = results.filter(r => r.success).length;
    this.syncStats.failedEntities = results.filter(r => !r.success).length;
    this.syncStats.lastSyncTime = new Date();
  }

  /**
   * تحديث حالة المزامنة في قاعدة البيانات
   */
  private async updateSyncStatus(
    entityType: 'offer' | 'trade' | 'contract_event',
    entityId: number,
    blockchainId: number | null,
    status: 'pending' | 'syncing' | 'synced' | 'failed' | 'conflict',
    errorMessage?: string
  ): Promise<void> {
    try {
      await databaseService.updateSyncStatus({
        entity_type: entityType,
        entity_id: entityId,
        blockchain_id: blockchainId,
        sync_status: status,
        error_message: errorMessage,
        last_sync_attempt: new Date()
      });
    } catch (error) {
      console.error('خطأ في تحديث حالة المزامنة:', error);
    }
  }

  /**
   * إرسال إشعار بتغيير الحالة
   */
  private async notifyStatusChange(entity: any, updates: any): Promise<void> {
    try {
      const message = this.getStatusChangeMessage(entity, updates);
      
      await databaseService.createNotification({
        user_id: entity.user_id,
        type: 'status_change',
        title: 'تحديث حالة العرض',
        message: message,
        data: { entity_id: entity.id, updates }
      });
    } catch (error) {
      console.error('خطأ في إرسال الإشعار:', error);
    }
  }

  /**
   * تحويل حالة العقد إلى حالة الصفقة
   */
  private mapContractStatusToTradeStatus(contractStatus: string): string {
    const statusMap: { [key: string]: string } = {
      'Created': 'created',
      'Joined': 'joined',
      'PaymentSent': 'payment_sent',
      'Completed': 'completed',
      'Cancelled': 'cancelled',
      'Disputed': 'disputed'
    };
    
    return statusMap[contractStatus] || 'created';
  }

  /**
   * الحصول على رسالة تغيير الحالة
   */
  private getStatusChangeMessage(entity: any, updates: any): string {
    if (updates.contract_status) {
      return `تم تحديث حالة ${entity.offer_type === 'buy' ? 'طلب الشراء' : 'عرض البيع'} إلى ${updates.contract_status}`;
    }
    return 'تم تحديث بيانات العرض';
  }

  /**
   * بدء مراقبة أحداث العقد الذكي
   */
  private async startEventMonitoring(): Promise<void> {
    try {
      // استيراد خدمة مراقبة الأحداث
      const { contractEventService } = await import('./contractEventService');

      // بدء مراقبة الأحداث
      await contractEventService.startMonitoring();

      console.log('✅ تم بدء مراقبة أحداث العقد الذكي بنجاح');
    } catch (error) {
      console.error('❌ خطأ في بدء مراقبة أحداث العقد الذكي:', error);
      throw error;
    }
  }

  /**
   * معالجة أحداث العقد المعلقة
   */
  private async processContractEvents(): Promise<SyncResult[]> {
    const results: SyncResult[] = [];

    try {
      // جلب الأحداث غير المعالجة
      const response = await fetch('/api/contract-events?processed=false&limit=100');
      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'فشل في جلب الأحداث');
      }

      const unprocessedEvents = data.data || [];

      for (const event of unprocessedEvents) {
        const result: SyncResult = {
          success: false,
          entityType: 'contract_event',
          entityId: event.id.toString(),
          changes: [],
          errors: []
        };

        try {
          // معالجة الحدث
          await this.processContractEvent(event);

          // تحديث حالة الحدث كمعالج
          await this.markEventAsProcessed(event.id);

          result.success = true;
          result.changes.push(`معالجة حدث ${event.event_type} للصفقة #${event.blockchain_trade_id}`);

        } catch (error) {
          result.errors.push(error.message);

          // تحديث حالة الحدث مع رسالة الخطأ
          await this.markEventAsProcessed(event.id, false, error.message);
        }

        results.push(result);
      }

      if (unprocessedEvents.length > 0) {
        console.log(`✅ تم معالجة ${unprocessedEvents.length} حدث من العقد الذكي`);
      }

    } catch (error) {
      console.error('❌ خطأ في معالجة أحداث العقد:', error);

      results.push({
        success: false,
        entityType: 'contract_event',
        entityId: 'batch',
        changes: [],
        errors: [error.message]
      });
    }

    return results;
  }

  /**
   * معالجة حدث واحد من العقد
   */
  private async processContractEvent(event: any): Promise<void> {
    // تحديث حالة الصفقة المرتبطة بالحدث
    const statusMap: { [key: string]: string } = {
      'TradeCreated': 'created',
      'BuyerJoined': 'joined',
      'PaymentSent': 'payment_sent',
      'PaymentConfirmed': 'payment_received',
      'TradeCompleted': 'completed',
      'TradeCancelled': 'cancelled',
      'TradeDisputed': 'disputed',
      'DisputeResolved': 'completed'
    };

    const newStatus = statusMap[event.event_type];
    if (!newStatus) {
      throw new Error(`نوع حدث غير معروف: ${event.event_type}`);
    }

    // تحديث حالة الصفقة باستخدام API المحدث
    const response = await fetch('/api/trades/sync-contract.php', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        blockchain_trade_id: event.blockchain_trade_id,
        status: newStatus,
        contract_status: event.event_type,
        event_type: event.event_type,
        transaction_hash: event.transaction_hash,
        seller_address: event.seller_address,
        buyer_address: event.buyer_address,
        amount: event.amount,
        fee_amount: event.fee_amount
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'فشل في تحديث حالة الصفقة');
    }
  }

  /**
   * تحديث حالة الحدث كمعالج
   */
  private async markEventAsProcessed(eventId: number, success: boolean = true, errorMessage?: string): Promise<void> {
    try {
      const response = await fetch('/api/contract-events', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: eventId,
          processed: success,
          error_message: errorMessage || null
        })
      });

      if (!response.ok) {
        console.error(`فشل في تحديث حالة الحدث ${eventId}`);
      }
    } catch (error) {
      console.error(`خطأ في تحديث حالة الحدث ${eventId}:`, error);
    }
  }

  /**
   * الحصول على إحصائيات المزامنة
   */
  getSyncStats(): SyncStats {
    return { ...this.syncStats };
  }

  /**
   * فرض مزامنة عنصر واحد
   */
  async forceSyncEntity(entityType: 'offer' | 'trade', entityId: string): Promise<SyncResult> {
    if (entityType === 'offer') {
      return await this.syncOfferWithContract(entityId);
    } else {
      return await this.syncTradeWithContract(entityId);
    }
  }

  /**
   * مزامنة صفقة واحدة مع العقد الذكي
   */
  private async syncOfferWithContract(offerId: string): Promise<SyncResult> {
    const result: SyncResult = {
      success: false,
      entityType: 'offer',
      entityId: offerId,
      changes: [],
      errors: []
    };

    try {
      // جلب بيانات العرض
      const response = await fetch(`/api/offers/index.php?id=${offerId}`);
      const data = await response.json();

      if (!data.success || !data.data) {
        throw new Error('العرض غير موجود');
      }

      const offer = data.data;

      // إذا كان العرض مربوط بالعقد، تحقق من حالته
      if (offer.blockchain_trade_id) {
        const contractTrade = await contractService.getTrade(offer.blockchain_trade_id);

        // تحديث حالة العرض إذا لزم الأمر
        const contractStatus = this.mapContractStatusToDbStatus(contractTrade.status);

        if (contractStatus !== offer.status) {
          await this.updateOfferFromContract(offerId, contractStatus, contractTrade);
          result.changes.push(`تم تحديث حالة العرض من ${offer.status} إلى ${contractStatus}`);
        }
      }

      result.success = true;

    } catch (error) {
      result.errors.push(error.message);
    }

    return result;
  }

  /**
   * مزامنة صفقة واحدة مع العقد الذكي
   */
  private async syncTradeWithContract(tradeId: string): Promise<SyncResult> {
    const result: SyncResult = {
      success: false,
      entityType: 'trade',
      entityId: tradeId,
      changes: [],
      errors: []
    };

    try {
      // جلب بيانات الصفقة
      const response = await fetch(`/api/trades/index.php?id=${tradeId}`);
      const data = await response.json();

      if (!data.success || !data.data) {
        throw new Error('الصفقة غير موجودة');
      }

      const trade = data.data;

      // إذا كانت الصفقة مربوطة بالعقد، تحقق من حالتها
      if (trade.blockchain_trade_id) {
        const contractTrade = await contractService.getTrade(trade.blockchain_trade_id);

        // تحديث حالة الصفقة إذا لزم الأمر
        const contractStatus = this.mapContractStatusToDbStatus(contractTrade.status);

        if (contractStatus !== trade.status) {
          await this.updateTradeFromContract(parseInt(tradeId), contractStatus, contractTrade);
          result.changes.push(`تم تحديث حالة الصفقة من ${trade.status} إلى ${contractStatus}`);
        }
      }

      result.success = true;

    } catch (error) {
      result.errors.push(error.message);
    }

    return result;
  }

  /**
   * تحديث العرض من العقد الذكي
   */
  private async updateOfferFromContract(offerId: string, newStatus: string, contractData: any): Promise<void> {
    const response = await fetch('/api/offers/sync-contract.php', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        offer_id: offerId,
        contract_status: newStatus,
        available_amount: contractData.amount,
        last_activity_hash: contractData.lastActivityHash
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'فشل في تحديث العرض من العقد');
    }
  }

  /**
   * تحديث الصفقة من العقد الذكي
   */
  private async updateTradeFromContract(tradeId: number, newStatus: string, contractData: any): Promise<void> {
    const response = await fetch('/api/trades/sync-contract.php', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        trade_id: tradeId,
        status: newStatus,
        contract_status: contractData.status,
        seller_address: contractData.seller,
        buyer_address: contractData.buyer,
        amount: contractData.amount
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'فشل في تحديث الصفقة من العقد');
    }
  }

  /**
   * تحويل حالة العقد الذكي إلى حالة قاعدة البيانات
   */
  private mapContractStatusToDbStatus(contractStatus: number): string {
    const statusMap: { [key: number]: string } = {
      0: 'created',
      1: 'joined',
      2: 'payment_sent',
      3: 'completed',
      4: 'cancelled',
      5: 'disputed'
    };

    return statusMap[contractStatus] || 'unknown';
  }
}

// إنشاء instance واحد للاستخدام في التطبيق
export const syncService = new SyncService();

// تصدير الأنواع
export type { SyncResult, SyncStats };
