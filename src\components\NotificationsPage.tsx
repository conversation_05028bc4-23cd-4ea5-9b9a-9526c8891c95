'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Trash2,
  Filter,
  Search,
  AlertCircle,
  TrendingUp,
  Shield,
  Clock,
  X
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { notificationService } from '@/services/notificationService';

interface Notification {
  id: string;
  type: 'trade' | 'system' | 'security' | 'price';
  title: string;
  message: string;
  data?: any;
  action_url?: string;
  is_read: boolean;
  created_at: string;
}

export default function NotificationsPage() {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [error, setError] = useState<string | null>(null);

  // تحميل الإشعارات
  useEffect(() => {
    loadNotifications();
  }, [user?.id, filterType, filterStatus, currentPage]);

  // تطبيق البحث المحلي
  useEffect(() => {
    let filtered = notifications;

    if (searchTerm) {
      filtered = filtered.filter(notification =>
        notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.message.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredNotifications(filtered);
  }, [notifications, searchTerm]);

  const loadNotifications = async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      setError(null);
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost/ikaros-p2p/api';

      const params = new URLSearchParams({
        user_id: user.id.toString(),
        page: currentPage.toString(),
        limit: '20'
      });

      if (filterType !== 'all') {
        params.append('type', filterType);
      }

      if (filterStatus !== 'all') {
        params.append('status', filterStatus);
      }

      const response = await fetch(`${apiUrl}/notifications/index.php?${params}`);
      const result = await response.json();

      if (result.success) {
        setNotifications(result.data || []);
        setFilteredNotifications(result.data || []);
        setTotalPages(result.pagination?.total_pages || 1);
      } else {
        throw new Error(result.error || 'فشل في تحميل الإشعارات');
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
      setError(error instanceof Error ? error.message : 'خطأ في تحميل الإشعارات');
      notificationService.error('خطأ في تحميل الإشعارات');
    } finally {
      setIsLoading(false);
    }
  };

  // تحديد/إلغاء تحديد إشعار
  const toggleNotificationSelection = (id: string) => {
    setSelectedNotifications(prev =>
      prev.includes(id)
        ? prev.filter(notifId => notifId !== id)
        : [...prev, id]
    );
  };

  // تحديد/إلغاء تحديد جميع الإشعارات
  const toggleSelectAll = () => {
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(filteredNotifications.map(n => n.id));
    }
  };

  // تحديث حالة القراءة
  const markAsRead = async (notificationIds: string[] = [], markAsRead = true) => {
    if (!user?.id) return;

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost/ikaros-p2p/api';
      const response = await fetch(`${apiUrl}/notifications/index.php`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: user.id,
          notification_ids: notificationIds.length > 0 ? notificationIds : undefined,
          mark_as_read: markAsRead
        }),
      });

      const result = await response.json();

      if (result.success) {
        // تحديث الحالة المحلية
        setNotifications(prev =>
          prev.map(notification =>
            notificationIds.length === 0 || notificationIds.includes(notification.id)
              ? { ...notification, is_read: markAsRead }
              : notification
          )
        );
        setSelectedNotifications([]);
        notificationService.success(markAsRead ? 'تم تحديد الإشعارات كمقروءة' : 'تم تحديد الإشعارات كغير مقروءة');
      } else {
        throw new Error(result.error || 'فشل في تحديث الإشعارات');
      }
    } catch (error) {
      console.error('Error updating notifications:', error);
      notificationService.error('خطأ في تحديث الإشعارات');
    }
  };

  // حذف الإشعارات
  const deleteNotifications = async (notificationIds: string[]) => {
    if (!user?.id || notificationIds.length === 0) return;

    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost/ikaros-p2p/api';
      const response = await fetch(`${apiUrl}/notifications/index.php`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: user.id,
          notification_ids: notificationIds
        }),
      });

      const result = await response.json();

      if (result.success) {
        // إزالة الإشعارات من الحالة المحلية
        setNotifications(prev =>
          prev.filter(notification => !notificationIds.includes(notification.id))
        );
        setSelectedNotifications([]);
        notificationService.success('تم حذف الإشعارات بنجاح');
      } else {
        throw new Error(result.error || 'فشل في حذف الإشعارات');
      }
    } catch (error) {
      console.error('Error deleting notifications:', error);
      notificationService.error('خطأ في حذف الإشعارات');
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'trade':
        return <TrendingUp className="w-5 h-5 text-blue-600" />;
      case 'security':
        return <Shield className="w-5 h-5 text-red-600" />;
      case 'system':
        return <AlertCircle className="w-5 h-5 text-yellow-600" />;
      case 'price':
        return <TrendingUp className="w-5 h-5 text-green-600" />;
      default:
        return <Bell className="w-5 h-5 text-gray-600" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'منذ قليل';
    } else if (diffInHours < 24) {
      return `منذ ${diffInHours} ساعة`;
    } else {
      return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  };

  const unreadCount = notifications.filter(n => !n.is_read).length;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="container-custom max-w-4xl">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-300 dark:bg-gray-700 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="container-custom max-w-4xl">
        {/* العنوان */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center">
              <Bell className="w-8 h-8 ml-3 text-primary-600 dark:text-primary-400" />
              الإشعارات
              {unreadCount > 0 && (
                <span className="bg-red-500 text-white text-sm font-medium px-2 py-1 rounded-full mr-2">
                  {unreadCount}
                </span>
              )}
            </h1>
            <p className="text-gray-600 dark:text-gray-300">إدارة جميع إشعاراتك</p>
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            {selectedNotifications.length > 0 && (
              <>
                <button
                  onClick={() => markAsRead(selectedNotifications, true)}
                  className="btn btn-secondary btn-sm"
                >
                  <Check className="w-4 h-4 ml-1" />
                  تحديد كمقروء
                </button>
                <button
                  onClick={() => deleteNotifications(selectedNotifications)}
                  className="btn btn-danger btn-sm"
                >
                  <Trash2 className="w-4 h-4 ml-1" />
                  حذف
                </button>
              </>
            )}
            <button
              onClick={() => markAsRead([], true)}
              className="btn btn-primary btn-sm"
            >
              <CheckCheck className="w-4 h-4 ml-1" />
              تحديد الكل كمقروء
            </button>
          </div>
        </div>

        {/* الفلاتر والبحث */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <div className="grid md:grid-cols-3 gap-4">
            <div>
              <label className="form-label">نوع الإشعار</label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="form-select"
              >
                <option value="all">جميع الأنواع</option>
                <option value="trade">الصفقات</option>
                <option value="security">الأمان</option>
                <option value="system">النظام</option>
                <option value="price">الأسعار</option>
              </select>
            </div>

            <div>
              <label className="form-label">الحالة</label>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="form-select"
              >
                <option value="all">جميع الحالات</option>
                <option value="unread">غير مقروء</option>
                <option value="read">مقروء</option>
              </select>
            </div>

            <div>
              <label className="form-label">البحث</label>
              <div className="input-with-icon">
                <Search className="input-icon-right w-4 h-4" />
                <input
                  type="text"
                  placeholder="ابحث في الإشعارات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="form-input"
                />
              </div>
            </div>
          </div>
        </div>

        {/* قائمة الإشعارات */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          {/* رأس القائمة */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedNotifications.length === filteredNotifications.length && filteredNotifications.length > 0}
                  onChange={toggleSelectAll}
                  className="form-checkbox"
                />
                <span className="mr-3 text-sm text-gray-600 dark:text-gray-400">
                  {selectedNotifications.length > 0
                    ? `تم تحديد ${selectedNotifications.length} إشعار`
                    : `${filteredNotifications.length} إشعار`
                  }
                </span>
              </div>
            </div>
          </div>

          {/* الإشعارات */}
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredNotifications.length === 0 ? (
              <div className="p-8 text-center">
                <BellOff className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <div className="text-gray-500 dark:text-gray-400 text-lg mb-2">لا توجد إشعارات</div>
                <p className="text-gray-400 dark:text-gray-500">ستظهر إشعاراتك هنا عند وصولها</p>
              </div>
            ) : (
              filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                    !notification.is_read ? 'bg-blue-50 dark:bg-blue-900/10' : ''
                  }`}
                >
                  <div className="flex items-start">
                    <input
                      type="checkbox"
                      checked={selectedNotifications.includes(notification.id)}
                      onChange={() => toggleNotificationSelection(notification.id)}
                      className="form-checkbox mt-1"
                    />
                    
                    <div className="mr-3 mt-1">
                      {getNotificationIcon(notification.type)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className={`text-sm font-medium ${
                            !notification.is_read 
                              ? 'text-gray-900 dark:text-white' 
                              : 'text-gray-700 dark:text-gray-300'
                          }`}>
                            {notification.title}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {notification.message}
                          </p>
                          <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                            <Clock className="w-3 h-3 ml-1" />
                            {formatDate(notification.created_at)}
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 rtl:space-x-reverse mr-4">
                          {!notification.is_read && (
                            <button
                              onClick={() => markAsRead([notification.id], true)}
                              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                              title="تحديد كمقروء"
                            >
                              <Check className="w-4 h-4" />
                            </button>
                          )}
                          <button
                            onClick={() => deleteNotifications([notification.id])}
                            className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200"
                            title="حذف"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex justify-center items-center space-x-2 rtl:space-x-reverse">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="btn btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  السابق
                </button>
                
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  صفحة {currentPage} من {totalPages}
                </span>
                
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="btn btn-secondary btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  التالي
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
