'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON>ting<PERSON>,
  Save,
  Bell,
  Shield,
  Globe,
  Moon,
  Sun,
  Monitor,
  Smartphone,
  Mail,
  MessageSquare,
  Lock,
  Eye,
  EyeOff,
  Download,
  Trash2,
  AlertTriangle,
  Check,
  X
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { useAuth } from '@/contexts/AuthContext';

interface UserSettings {
  // إعدادات المظهر
  theme: 'light' | 'dark' | 'system';
  language: 'ar' | 'en';
  currency: string;
  timezone: string;

  // إعدادات الإشعارات
  emailNotifications: {
    trades: boolean;
    offers: boolean;
    payments: boolean;
    security: boolean;
    marketing: boolean;
  };
  pushNotifications: {
    trades: boolean;
    offers: boolean;
    payments: boolean;
    security: boolean;
  };
  smsNotifications: {
    trades: boolean;
    payments: boolean;
    security: boolean;
  };

  // إعدادات الأمان
  twoFactorAuth: boolean;
  loginAlerts: boolean;
  sessionTimeout: number; // بالدقائق
  autoLogout: boolean;

  // إعدادات التداول
  defaultTradeAmount: number;
  autoAcceptTrades: boolean;
  tradingHours: {
    enabled: boolean;
    start: string;
    end: string;
  };

  // إعدادات الخصوصية
  showOnlineStatus: boolean;
  showLastSeen: boolean;
  allowDirectMessages: boolean;
  profileVisibility: 'public' | 'verified' | 'private';
}

export default function SettingsPage() {
  const { t } = useUserDashboardTranslation();
  const { user } = useAuth();
  const [settings, setSettings] = useState<UserSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState<'general' | 'notifications' | 'security' | 'trading' | 'privacy'>('general');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // جلب الإعدادات
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);

        // محاكاة البيانات - يجب استبدالها بـ API حقيقي
        const mockSettings: UserSettings = {
          theme: 'system',
          language: 'ar',
          currency: 'SAR',
          timezone: 'Asia/Riyadh',
          emailNotifications: {
            trades: true,
            offers: true,
            payments: true,
            security: true,
            marketing: false
          },
          pushNotifications: {
            trades: true,
            offers: true,
            payments: true,
            security: true
          },
          smsNotifications: {
            trades: false,
            payments: true,
            security: true
          },
          twoFactorAuth: false,
          loginAlerts: true,
          sessionTimeout: 60,
          autoLogout: true,
          defaultTradeAmount: 1000,
          autoAcceptTrades: false,
          tradingHours: {
            enabled: false,
            start: '09:00',
            end: '17:00'
          },
          showOnlineStatus: true,
          showLastSeen: true,
          allowDirectMessages: true,
          profileVisibility: 'public'
        };

        setSettings(mockSettings);
      } catch (error) {
        console.error('Error fetching settings:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // حفظ الإعدادات
  const handleSave = async () => {
    if (!settings) return;

    try {
      setSaving(true);

      // محاكاة حفظ البيانات - يجب استبدالها بـ API حقيقي
      await new Promise(resolve => setTimeout(resolve, 1000));

      // إشعار بالنجاح
      console.log('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
    } finally {
      setSaving(false);
    }
  };

  // تحديث إعداد
  const updateSetting = (path: string, value: any) => {
    if (!settings) return;

    const keys = path.split('.');
    const newSettings = { ...settings };
    let current: any = newSettings;

    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;

    setSettings(newSettings);
  };

  // حذف الحساب
  const handleDeleteAccount = async () => {
    try {
      // محاكاة حذف الحساب - يجب استبدالها بـ API حقيقي
      await new Promise(resolve => setTimeout(resolve, 1000));

      // إعادة توجيه للصفحة الرئيسية
      window.location.href = '/';
    } catch (error) {
      console.error('Error deleting account:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          خطأ في تحميل الإعدادات
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          حدث خطأ أثناء تحميل إعدادات الحساب
        </p>
      </div>
    );
  }

  const tabs = [
    { id: 'general', label: 'عام', icon: Settings },
    { id: 'notifications', label: 'الإشعارات', icon: Bell },
    { id: 'security', label: 'الأمان', icon: Shield },
    { id: 'trading', label: 'التداول', icon: Globe },
    { id: 'privacy', label: 'الخصوصية', icon: Eye }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            {t('settings.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            إدارة إعدادات حسابك وتفضيلاتك
          </p>
        </div>

        <button
          onClick={handleSave}
          disabled={saving}
          className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
        >
          <Save className="w-4 h-4" />
          <span>{saving ? 'جاري الحفظ...' : 'حفظ الإعدادات'}</span>
        </button>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Sidebar */}
        <div className="lg:w-64">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`w-full flex items-center space-x-3 rtl:space-x-reverse px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <tab.icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            {/* الإعدادات العامة */}
            {activeTab === 'general' && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  الإعدادات العامة
                </h2>

                {/* المظهر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    المظهر
                  </label>
                  <div className="grid grid-cols-3 gap-3">
                    {[
                      { value: 'light', label: 'فاتح', icon: Sun },
                      { value: 'dark', label: 'مظلم', icon: Moon },
                      { value: 'system', label: 'النظام', icon: Monitor }
                    ].map((theme) => (
                      <button
                        key={theme.value}
                        onClick={() => updateSetting('theme', theme.value)}
                        className={`flex flex-col items-center space-y-2 p-3 rounded-lg border-2 transition-colors ${
                          settings.theme === theme.value
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                        }`}
                      >
                        <theme.icon className="w-5 h-5" />
                        <span className="text-sm font-medium">{theme.label}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* اللغة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    اللغة
                  </label>
                  <select
                    value={settings.language}
                    onChange={(e) => updateSetting('language', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="ar">العربية</option>
                    <option value="en">English</option>
                  </select>
                </div>

                {/* العملة الافتراضية */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    العملة الافتراضية
                  </label>
                  <select
                    value={settings.currency}
                    onChange={(e) => updateSetting('currency', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="SAR">ريال سعودي (SAR)</option>
                    <option value="AED">درهم إماراتي (AED)</option>
                    <option value="USD">دولار أمريكي (USD)</option>
                  </select>
                </div>

                {/* المنطقة الزمنية */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    المنطقة الزمنية
                  </label>
                  <select
                    value={settings.timezone}
                    onChange={(e) => updateSetting('timezone', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                    <option value="Asia/Dubai">دبي (GMT+4)</option>
                    <option value="Asia/Kuwait">الكويت (GMT+3)</option>
                  </select>
                </div>
              </div>
            )}

            {/* إعدادات الإشعارات */}
            {activeTab === 'notifications' && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  إعدادات الإشعارات
                </h2>

                {/* إشعارات البريد الإلكتروني */}
                <div>
                  <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                    إشعارات البريد الإلكتروني
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(settings.emailNotifications).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between">
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {key === 'trades' ? 'الصفقات' :
                           key === 'offers' ? 'العروض' :
                           key === 'payments' ? 'المدفوعات' :
                           key === 'security' ? 'الأمان' :
                           key === 'marketing' ? 'التسويق' : key}
                        </span>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={(e) => updateSetting(`emailNotifications.${key}`, e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* الإشعارات المباشرة */}
                <div>
                  <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                    الإشعارات المباشرة
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(settings.pushNotifications).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between">
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {key === 'trades' ? 'الصفقات' :
                           key === 'offers' ? 'العروض' :
                           key === 'payments' ? 'المدفوعات' :
                           key === 'security' ? 'الأمان' : key}
                        </span>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={(e) => updateSetting(`pushNotifications.${key}`, e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* الرسائل النصية */}
                <div>
                  <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                    الرسائل النصية
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(settings.smsNotifications).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between">
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {key === 'trades' ? 'الصفقات' :
                           key === 'payments' ? 'المدفوعات' :
                           key === 'security' ? 'الأمان' : key}
                        </span>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={(e) => updateSetting(`smsNotifications.${key}`, e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* إعدادات الأمان */}
            {activeTab === 'security' && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  إعدادات الأمان
                </h2>

                {/* المصادقة الثنائية */}
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div>
                    <h3 className="text-md font-medium text-gray-900 dark:text-white">
                      المصادقة الثنائية
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      إضافة طبقة حماية إضافية لحسابك
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.twoFactorAuth}
                      onChange={(e) => updateSetting('twoFactorAuth', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                {/* تنبيهات تسجيل الدخول */}
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div>
                    <h3 className="text-md font-medium text-gray-900 dark:text-white">
                      تنبيهات تسجيل الدخول
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      إشعارك عند تسجيل الدخول من جهاز جديد
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.loginAlerts}
                      onChange={(e) => updateSetting('loginAlerts', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                {/* انتهاء مهلة الجلسة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    انتهاء مهلة الجلسة (بالدقائق)
                  </label>
                  <select
                    value={settings.sessionTimeout}
                    onChange={(e) => updateSetting('sessionTimeout', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={15}>15 دقيقة</option>
                    <option value={30}>30 دقيقة</option>
                    <option value={60}>ساعة واحدة</option>
                    <option value={120}>ساعتان</option>
                    <option value={480}>8 ساعات</option>
                  </select>
                </div>

                {/* تسجيل الخروج التلقائي */}
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div>
                    <h3 className="text-md font-medium text-gray-900 dark:text-white">
                      تسجيل الخروج التلقائي
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      تسجيل الخروج تلقائياً عند عدم النشاط
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.autoLogout}
                      onChange={(e) => updateSetting('autoLogout', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                {/* تغيير كلمة المرور */}
                <div className="border-t border-gray-200 dark:border-gray-600 pt-6">
                  <h3 className="text-md font-medium text-gray-900 dark:text-white mb-4">
                    تغيير كلمة المرور
                  </h3>
                  <a
                    href="/user-dashboard/profile/security"
                    className="inline-flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  >
                    <Lock className="w-4 h-4" />
                    <span>تغيير كلمة المرور</span>
                  </a>
                </div>
              </div>
            )}

            {/* إعدادات التداول */}
            {activeTab === 'trading' && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  إعدادات التداول
                </h2>

                {/* المبلغ الافتراضي للتداول */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    المبلغ الافتراضي للتداول (USDT)
                  </label>
                  <input
                    type="number"
                    value={settings.defaultTradeAmount}
                    onChange={(e) => updateSetting('defaultTradeAmount', parseFloat(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* قبول الصفقات تلقائياً */}
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div>
                    <h3 className="text-md font-medium text-gray-900 dark:text-white">
                      قبول الصفقات تلقائياً
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      قبول الصفقات المطابقة لشروطك تلقائياً
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.autoAcceptTrades}
                      onChange={(e) => updateSetting('autoAcceptTrades', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                {/* ساعات التداول */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-md font-medium text-gray-900 dark:text-white">
                      ساعات التداول
                    </h3>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.tradingHours.enabled}
                        onChange={(e) => updateSetting('tradingHours.enabled', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  {settings.tradingHours.enabled && (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          من
                        </label>
                        <input
                          type="time"
                          value={settings.tradingHours.start}
                          onChange={(e) => updateSetting('tradingHours.start', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          إلى
                        </label>
                        <input
                          type="time"
                          value={settings.tradingHours.end}
                          onChange={(e) => updateSetting('tradingHours.end', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* إعدادات الخصوصية */}
            {activeTab === 'privacy' && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  إعدادات الخصوصية
                </h2>

                {/* إظهار حالة الاتصال */}
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div>
                    <h3 className="text-md font-medium text-gray-900 dark:text-white">
                      إظهار حالة الاتصال
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      السماح للآخرين برؤية حالة اتصالك
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.showOnlineStatus}
                      onChange={(e) => updateSetting('showOnlineStatus', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                {/* إظهار آخر ظهور */}
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div>
                    <h3 className="text-md font-medium text-gray-900 dark:text-white">
                      إظهار آخر ظهور
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      السماح للآخرين برؤية آخر وقت كنت متصلاً فيه
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.showLastSeen}
                      onChange={(e) => updateSetting('showLastSeen', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                {/* السماح بالرسائل المباشرة */}
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div>
                    <h3 className="text-md font-medium text-gray-900 dark:text-white">
                      السماح بالرسائل المباشرة
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      السماح للمستخدمين بإرسال رسائل مباشرة إليك
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.allowDirectMessages}
                      onChange={(e) => updateSetting('allowDirectMessages', e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                {/* مستوى رؤية الملف الشخصي */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    مستوى رؤية الملف الشخصي
                  </label>
                  <select
                    value={settings.profileVisibility}
                    onChange={(e) => updateSetting('profileVisibility', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="public">عام - يمكن لأي شخص رؤية ملفك الشخصي</option>
                    <option value="verified">المحققين فقط - المستخدمين المحققين فقط</option>
                    <option value="private">خاص - لا يمكن لأحد رؤية ملفك الشخصي</option>
                  </select>
                </div>

                {/* منطقة الخطر */}
                <div className="border-t border-gray-200 dark:border-gray-600 pt-6">
                  <h3 className="text-md font-medium text-red-600 dark:text-red-400 mb-4">
                    منطقة الخطر
                  </h3>

                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <div className="flex items-start space-x-3 rtl:space-x-reverse">
                      <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5" />
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-red-800 dark:text-red-300 mb-2">
                          حذف الحساب
                        </h4>
                        <p className="text-sm text-red-700 dark:text-red-400 mb-4">
                          حذف حسابك نهائياً. هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع بياناتك.
                        </p>
                        <button
                          onClick={() => setShowDeleteConfirm(true)}
                          className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                          <span>حذف الحساب</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* مودال تأكيد حذف الحساب */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md mx-4">
            <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
              <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                تأكيد حذف الحساب
              </h3>
            </div>

            <p className="text-gray-600 dark:text-gray-400 mb-6">
              هل أنت متأكد من رغبتك في حذف حسابك؟ سيتم حذف جميع بياناتك نهائياً ولا يمكن استرجاعها.
            </p>

            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={handleDeleteAccount}
                className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                حذف الحساب
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}