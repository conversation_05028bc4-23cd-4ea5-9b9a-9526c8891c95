<?php
/**
 * API endpoint للتحقق من صلاحيات الإدارة
 * Admin Permissions Check API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Max-Age: 86400');

// Include response helper
require_once __DIR__ . '/../helpers/ResponseHelper.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Validate request method
ResponseHelper::validateMethod(['POST']);

// Include database configuration
require_once __DIR__ . '/../config/database.php';

try {
    // Get input data
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('بيانات غير صحيحة');
    }

    $walletAddress = $input['walletAddress'] ?? null;

    if (empty($walletAddress)) {
        throw new Exception('عنوان المحفظة مطلوب');
    }

    // Normalize wallet address
    $walletAddress = strtolower(trim($walletAddress));

    // Get database connection
    $db = new Database();
    $connection = $db->getConnection();
    
    // Check if wallet address belongs to an admin user
    $stmt = $connection->prepare("
        SELECT id, username, email, full_name, is_admin, is_active, wallet_address
        FROM users 
        WHERE LOWER(wallet_address) = ? AND is_admin = 1 AND is_active = 1
    ");
    
    $stmt->execute([$walletAddress]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $isAdmin = false;
    $adminData = null;
    
    if ($admin) {
        $isAdmin = true;
        $adminData = [
            'id' => $admin['id'],
            'username' => $admin['username'],
            'email' => $admin['email'],
            'fullName' => $admin['full_name'],
            'walletAddress' => $admin['wallet_address']
        ];
        
        // Log the admin permission check (مع التحقق من وجود الجدول)
        try {
            $checkTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
            $checkTable->execute();

            if ($checkTable->rowCount() > 0) {
                $checkColumns = $connection->prepare("SHOW COLUMNS FROM activity_logs LIKE 'entity_type'");
                $checkColumns->execute();

                if ($checkColumns->rowCount() > 0) {
                    $logStmt = $connection->prepare("
                        INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data)
                        VALUES (?, 'admin_permission_check', 'user', ?, ?, ?, ?)
                    ");

                    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                    $checkData = json_encode([
                        'check_time' => date('Y-m-d H:i:s'),
                        'wallet_address' => $walletAddress,
                        'result' => 'granted'
                    ]);

                    $logStmt->execute([$admin['id'], $admin['id'], $ipAddress, $userAgent, $checkData]);
                }
            }
        } catch (Exception $logError) {
            error_log('Activity log error in check-permissions: ' . $logError->getMessage());
        }
    } else {
        // Log failed permission check (مع التحقق من وجود الجدول)
        try {
            $checkTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
            $checkTable->execute();

            if ($checkTable->rowCount() > 0) {
                $checkColumns = $connection->prepare("SHOW COLUMNS FROM activity_logs LIKE 'entity_type'");
                $checkColumns->execute();

                if ($checkColumns->rowCount() > 0) {
                    $logStmt = $connection->prepare("
                        INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data)
                        VALUES (?, 'admin_permission_check_failed', 'user', ?, ?, ?, ?)
                    ");

                    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                    $checkData = json_encode([
                        'check_time' => date('Y-m-d H:i:s'),
                        'wallet_address' => $walletAddress,
                        'result' => 'denied'
                    ]);

                    $logStmt->execute([null, null, $ipAddress, $userAgent, $checkData]);
                }
            }
        } catch (Exception $logError) {
            error_log('Activity log error in check-permissions failed: ' . $logError->getMessage());
        }
    }
    
    // Success response
    echo json_encode([
        'success' => true,
        'isAdmin' => $isAdmin,
        'adminData' => $adminData,
        'message' => $isAdmin ? 'صلاحيات الإدارة مؤكدة' : 'لا توجد صلاحيات إدارة'
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    // Log database errors (in production, use proper logging)
    error_log('Database error in check-permissions.php: ' . $e->getMessage());
}
?>
