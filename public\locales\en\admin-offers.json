{"title": "Offers Management", "subtitle": "Manage platform offers, limits, and subscription plans", "tabs": {"settings": "Offer <PERSON>s", "plans": "Subscription Plans", "statistics": "Statistics", "users": "User Limits"}, "settings": {"title": "Offer <PERSON>s", "monthly_free_offers": "Monthly Free Offers", "monthly_free_offers_desc": "Default number of free offers per user per month", "max_offer_amount": "Maximum Offer Amount", "max_offer_amount_desc": "Maximum amount allowed for free offers (USDT)", "min_offer_amount": "Minimum Offer Amount", "min_offer_amount_desc": "Minimum amount required for offers (USDT)", "offer_time_limit": "Offer Time Limit", "offer_time_limit_desc": "Default expiration time for offers (hours)", "require_verification": "Require Verification", "require_verification_desc": "Require user verification for creating offers", "subscription_system": "Subscription System", "subscription_system_desc": "Enable subscription plans for users"}, "plans": {"title": "Subscription Plans", "create_plan": "Create New Plan", "edit_plan": "Edit Plan", "plan_name": "Plan Name", "plan_name_ar": "Plan Name (Arabic)", "description": "Description", "description_ar": "Description (Arabic)", "monthly_offers": "Monthly Offers", "price_monthly": "Monthly Price", "price_yearly": "Yearly Price", "features": "Features", "is_active": "Active", "is_default": "Default Plan", "sort_order": "Sort Order", "subscribers": "Subscribers", "revenue": "Revenue"}, "statistics": {"title": "Offer Statistics", "total_offers": "Total Offers", "active_offers": "Active Offers", "free_offers": "Free Offers", "premium_offers": "Premium Offers", "monthly_offers_created": "Monthly Offers Created", "average_offers_per_user": "Avg. Offers per User", "subscription_revenue": "Subscription Revenue", "conversion_rate": "Free to Premium Conversion", "top_currencies": "Top Currencies", "offer_trends": "Offer Trends"}, "users": {"title": "User Limits", "search_user": "Search User", "user_id": "User ID", "username": "Username", "current_plan": "Current Plan", "offers_used": "Offers Used", "offers_limit": "Offers Limit", "subscription_status": "Subscription Status", "last_offer": "Last Offer", "actions": "Actions", "reset_limits": "Reset Limits", "change_plan": "Change Plan", "view_offers": "View Offers"}, "actions": {"save_settings": "Save Settings", "reset_defaults": "Reset to Defaults", "export_data": "Export Data", "import_data": "Import Data", "bulk_actions": "Bulk Actions", "apply": "Apply", "cancel": "Cancel", "confirm": "Confirm"}, "messages": {"settings_saved": "Setting<PERSON> saved successfully", "plan_created": "Plan created successfully", "plan_updated": "Plan updated successfully", "plan_deleted": "Plan deleted successfully", "limits_reset": "User limits reset successfully", "plan_changed": "User plan changed successfully", "export_completed": "Data export completed", "import_completed": "Data import completed"}, "validation": {"required_field": "This field is required", "invalid_number": "Please enter a valid number", "invalid_price": "Please enter a valid price", "plan_name_exists": "Plan name already exists", "min_greater_than_max": "Minimum cannot be greater than maximum"}}