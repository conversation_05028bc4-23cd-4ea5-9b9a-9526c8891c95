/**
 * خدمة محاكاة ظروف الشبكة
 * Network Simulation Service
 * 
 * نظام متقدم لمحاكاة تأخير الشبكة وفقدان الحزم لاختبار قوة النظام
 * Advanced system for simulating network delays and packet loss to test system resilience
 */

interface NetworkCondition {
  name: string;
  latency: {
    min: number;    // أقل تأخير بالميلي ثانية
    max: number;    // أعلى تأخير بالميلي ثانية
    variance: number; // تباين التأخير
  };
  packetLoss: number;     // نسبة فقدان الحزم (0-1)
  jitter: number;         // اهتزاز الشبكة بالميلي ثانية
  bandwidth: number;      // عرض النطاق بالكيلوبايت/ثانية
  enabled: boolean;
}

interface SimulationStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageLatency: number;
  maxLatency: number;
  minLatency: number;
  packetLossCount: number;
  timeoutCount: number;
  retryCount: number;
}

class NetworkSimulationService {
  private conditions: { [key: string]: NetworkCondition } = {
    perfect: {
      name: 'شبكة مثالية',
      latency: { min: 10, max: 50, variance: 5 },
      packetLoss: 0,
      jitter: 0,
      bandwidth: 10000,
      enabled: false
    },
    good: {
      name: 'شبكة جيدة',
      latency: { min: 50, max: 150, variance: 20 },
      packetLoss: 0.01,
      jitter: 10,
      bandwidth: 5000,
      enabled: false
    },
    moderate: {
      name: 'شبكة متوسطة',
      latency: { min: 100, max: 300, variance: 50 },
      packetLoss: 0.03,
      jitter: 25,
      bandwidth: 2000,
      enabled: false
    },
    poor: {
      name: 'شبكة ضعيفة',
      latency: { min: 200, max: 800, variance: 100 },
      packetLoss: 0.08,
      jitter: 50,
      bandwidth: 500,
      enabled: false
    },
    terrible: {
      name: 'شبكة سيئة جداً',
      latency: { min: 500, max: 2000, variance: 200 },
      packetLoss: 0.15,
      jitter: 100,
      bandwidth: 100,
      enabled: false
    },
    unstable: {
      name: 'شبكة غير مستقرة',
      latency: { min: 50, max: 1500, variance: 300 },
      packetLoss: 0.12,
      jitter: 200,
      bandwidth: 1000,
      enabled: false
    }
  };

  private currentCondition: string = 'perfect';
  private stats: SimulationStats;
  private isEnabled: boolean = false;
  private originalFetch: typeof fetch;
  private isClient: boolean = false;

  constructor() {
    // تحقق من وجود window (client-side only)
    this.isClient = typeof window !== 'undefined';

    // تهيئة الإحصائيات
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageLatency: 0,
      maxLatency: 0,
      minLatency: Infinity,
      packetLossCount: 0,
      timeoutCount: 0,
      retryCount: 0
    };

    if (this.isClient) {
      // حفظ fetch الأصلي مع ربط السياق
      this.originalFetch = window.fetch.bind(window);
      this.setupInterceptors();
    } else {
      // في حالة SSR، استخدم fetch العادي
      this.originalFetch = global.fetch || fetch;
    }
  }



  private setupInterceptors(): void {
    // اعتراض fetch requests (client-side only)
    if (this.isClient && typeof window !== 'undefined') {
      const originalFetch = this.originalFetch;
      window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
        if (!this.isEnabled) {
          return originalFetch.call(window, input, init);
        }

        return this.simulateNetworkRequest(input, init);
      };
    }
  }

  private async simulateNetworkRequest(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
    const condition = this.conditions[this.currentCondition];
    const startTime = Date.now();
    
    this.stats.totalRequests++;

    try {
      // محاكاة فقدان الحزم
      if (Math.random() < condition.packetLoss) {
        this.stats.packetLossCount++;
        this.stats.failedRequests++;
        throw new Error('Simulated packet loss');
      }

      // حساب التأخير المحاكي
      const baseLatency = this.generateLatency(condition.latency);
      const jitterDelay = Math.random() * condition.jitter;
      const totalDelay = baseLatency + jitterDelay;

      // محاكاة التأخير
      await this.delay(totalDelay);

      // تنفيذ الطلب الفعلي مع ربط السياق الصحيح
      const response = await this.originalFetch.call(window, input, init);

      // تحديث الإحصائيات
      const actualLatency = Date.now() - startTime;
      this.updateStats(actualLatency, true);

      return response;

    } catch (error) {
      const actualLatency = Date.now() - startTime;
      this.updateStats(actualLatency, false);
      throw error;
    }
  }

  private generateLatency(latencyConfig: NetworkCondition['latency']): number {
    const { min, max, variance } = latencyConfig;
    const baseLatency = min + Math.random() * (max - min);
    const varianceAdjustment = (Math.random() - 0.5) * variance * 2;
    return Math.max(0, baseLatency + varianceAdjustment);
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private updateStats(latency: number, success: boolean): void {
    if (success) {
      this.stats.successfulRequests++;
    } else {
      this.stats.failedRequests++;
    }

    this.stats.averageLatency = (
      (this.stats.averageLatency * (this.stats.totalRequests - 1) + latency) / 
      this.stats.totalRequests
    );

    this.stats.maxLatency = Math.max(this.stats.maxLatency, latency);
    this.stats.minLatency = Math.min(this.stats.minLatency, latency);
  }

  // واجهات عامة
  public enableSimulation(conditionName: string = 'moderate'): void {
    if (!this.isClient) {
      console.warn('🌐 Network Simulation: Cannot enable on server-side');
      return;
    }

    if (this.conditions[conditionName]) {
      this.currentCondition = conditionName;
      this.isEnabled = true;
      this.stats = this.resetStats();

      console.log(`🌐 Network Simulation: Enabled with condition "${conditionName}"`);
      console.log('Condition details:', this.conditions[conditionName]);

      // إشعار في الواجهة
      this.notifyUI('enabled', conditionName);
    } else {
      throw new Error(`Unknown network condition: ${conditionName}`);
    }
  }

  public disableSimulation(): void {
    this.isEnabled = false;
    console.log('🌐 Network Simulation: Disabled');
    this.notifyUI('disabled');
  }

  public setCondition(conditionName: string): void {
    if (this.conditions[conditionName]) {
      this.currentCondition = conditionName;
      console.log(`🌐 Network Simulation: Changed to "${conditionName}"`);
      this.notifyUI('changed', conditionName);
    } else {
      throw new Error(`Unknown network condition: ${conditionName}`);
    }
  }

  public getAvailableConditions(): { [key: string]: NetworkCondition } {
    return { ...this.conditions };
  }

  public getCurrentCondition(): NetworkCondition {
    return { ...this.conditions[this.currentCondition] };
  }

  public getStats(): SimulationStats {
    return { ...this.stats };
  }

  public resetStats(): SimulationStats {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageLatency: 0,
      maxLatency: 0,
      minLatency: Infinity,
      packetLossCount: 0,
      timeoutCount: 0,
      retryCount: 0
    };
    return this.getStats();
  }

  public isSimulationEnabled(): boolean {
    return this.isEnabled;
  }

  public getCurrentConditionName(): string {
    return this.currentCondition;
  }

  // إضافة حالة شبكة مخصصة
  public addCustomCondition(name: string, condition: Omit<NetworkCondition, 'name' | 'enabled'>): void {
    this.conditions[name] = {
      ...condition,
      name,
      enabled: false
    };
  }

  // محاكاة انقطاع الشبكة المؤقت
  public simulateOutage(durationMs: number): void {
    const originalCondition = this.currentCondition;
    
    // تفعيل حالة انقطاع كامل
    this.addCustomCondition('outage', {
      latency: { min: 0, max: 0, variance: 0 },
      packetLoss: 1.0, // فقدان كامل للحزم
      jitter: 0,
      bandwidth: 0
    });
    
    this.setCondition('outage');
    
    setTimeout(() => {
      this.setCondition(originalCondition);
      console.log(`🌐 Network Simulation: Outage ended, restored to "${originalCondition}"`);
    }, durationMs);
    
    console.log(`🌐 Network Simulation: Outage started for ${durationMs}ms`);
  }

  // اختبار تدريجي لظروف الشبكة
  public runProgressiveTest(durationPerCondition: number = 30000): void {
    const conditions = ['perfect', 'good', 'moderate', 'poor', 'terrible'];
    let currentIndex = 0;

    const runNextCondition = () => {
      if (currentIndex < conditions.length) {
        const condition = conditions[currentIndex];
        this.enableSimulation(condition);

        console.log(`🧪 Progressive Test: Testing "${condition}" for ${durationPerCondition}ms`);
        this.notifyUI('testProgress', condition, {
          currentStep: currentIndex + 1,
          totalSteps: conditions.length,
          timeRemaining: (conditions.length - currentIndex) * durationPerCondition
        });

        setTimeout(() => {
          currentIndex++;
          runNextCondition();
        }, durationPerCondition);
      } else {
        this.disableSimulation();
        console.log('🧪 Progressive Test: Completed');
        this.notifyUI('testCompleted');
      }
    };

    runNextCondition();
  }

  // اختبار مزامنة تحت ضغط
  public async testSyncUnderStress(duration: number = 60000): Promise<void> {
    console.log('🔄 Starting sync stress test...');
    this.notifyUI('syncTestStarted');

    const startTime = Date.now();
    const endTime = startTime + duration;
    let requestCount = 0;

    // تفعيل حالة شبكة متوسطة
    this.enableSimulation('moderate');

    const testInterval = setInterval(async () => {
      if (Date.now() >= endTime) {
        clearInterval(testInterval);
        this.disableSimulation();
        console.log(`🔄 Sync stress test completed. Total requests: ${requestCount}`);
        this.notifyUI('syncTestCompleted', undefined, { totalRequests: requestCount });
        return;
      }

      // محاكاة طلبات مزامنة متعددة
      const syncPromises = [];
      for (let i = 0; i < 3; i++) {
        syncPromises.push(
          fetch('/api/admin/sync-status.php')
            .then(response => response.json())
            .catch(error => ({ error: error.message }))
        );
      }

      try {
        await Promise.all(syncPromises);
        requestCount += syncPromises.length;
      } catch (error) {
        console.error('Sync test error:', error);
      }
    }, 2000); // طلب كل ثانيتين
  }

  private notifyUI(action: string, condition?: string, extraData?: any): void {
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('networkSimulation', {
        detail: {
          action,
          condition,
          stats: this.getStats(),
          isEnabled: this.isEnabled,
          ...extraData
        }
      }));
    }
  }

  // تصدير الإحصائيات
  public exportStats(): string {
    const data = {
      condition: this.currentCondition,
      conditionDetails: this.getCurrentCondition(),
      stats: this.getStats(),
      timestamp: new Date().toISOString()
    };
    
    return JSON.stringify(data, null, 2);
  }

  // استعادة fetch الأصلي
  public restore(): void {
    if (this.isClient && typeof window !== 'undefined') {
      window.fetch = this.originalFetch;
    }
    this.isEnabled = false;
    console.log('🌐 Network Simulation: Restored original fetch');
  }
}

// إنشاء مثيل واحد للخدمة
export const networkSimulationService = new NetworkSimulationService();
export default networkSimulationService;
