/**
 * API functions مخصصة لداشبورد المستخدم
 * User Dashboard Specific API Functions
 */

import { apiGet, apiPost, handleApiError } from './apiClient';

// ===== إحصائيات المستخدم =====
export interface UserStats {
  totalTrades: number;
  completedTrades: number;
  activeTrades: number;
  cancelledTrades: number;
  disputedTrades: number;
  totalVolume: string;
  monthlyVolume: string;
  weeklyVolume: string;
  averageTradeAmount: string;
  rating: number;
  ratingCount: number;
  positiveReviews: number;
  negativeReviews: number;
  completionRate: number;
  successRate: number;
  disputeRate: number;
  averageTradeTime: number;
  averageResponseTime: number;
  joinDate: string;
  lastActivity: string;
  totalDaysActive: number;
  totalOffers: number;
  activeOffers: number;
  expiredOffers: number;
  isVerified: boolean;
  verificationLevel: 'none' | 'basic' | 'advanced' | 'premium' | 'full';
}

export interface UserStatsFilters {
  period?: 'week' | 'month' | 'quarter' | 'year' | 'all';
  currency?: string;
  tradeType?: 'buy' | 'sell' | 'all';
  status?: 'completed' | 'active' | 'all';
}

/**
 * جلب إحصائيات المستخدم
 */
export const fetchUserStats = async (userId: string, filters?: UserStatsFilters): Promise<UserStats> => {
  try {
    const queryParams = new URLSearchParams({
      user_id: userId,
      ...filters
    });

    const response = await apiGet(`users/stats.php?${queryParams.toString()}`);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب الإحصائيات');
    }

    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

// ===== المحفظة =====
export interface WalletBalance {
  currency: string;
  symbol: string;
  available: number;
  locked: number;
  total: number;
  usdValue: number;
  change24h: number;
}

export interface WalletTransaction {
  id: string;
  type: 'deposit' | 'withdrawal' | 'trade' | 'fee' | 'refund';
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  date: string;
  description: string;
  txHash?: string;
  fee?: number;
}

/**
 * جلب أرصدة المحفظة
 */
export const fetchWalletBalances = async (userId: string): Promise<WalletBalance[]> => {
  try {
    const response = await apiGet(`wallet/balance.php?user_id=${userId}`);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب أرصدة المحفظة');
    }

    return response.data || [];
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

/**
 * جلب معاملات المحفظة
 */
export const fetchWalletTransactions = async (userId: string, limit = 10): Promise<WalletTransaction[]> => {
  try {
    const response = await apiGet(`wallet/index.php?user_id=${userId}&action=transactions&limit=${limit}`);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب معاملات المحفظة');
    }

    return response.data || [];
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

// ===== الصفقات =====
export interface UserTrade {
  id: string;
  type: 'buy' | 'sell';
  amount: number;
  currency: string;
  price: number;
  status: 'active' | 'completed' | 'cancelled' | 'disputed';
  partner: {
    username: string;
    rating: number;
    isVerified: boolean;
  };
  createdAt: string;
  updatedAt: string;
  escrowAmount?: number;
  paymentMethod: string;
}

export interface TradeFilters {
  status?: string;
  type?: 'buy' | 'sell' | 'all';
  currency?: string;
  dateFrom?: string;
  dateTo?: string;
}

/**
 * جلب صفقات المستخدم
 */
export const fetchUserTrades = async (userId: string, filters?: TradeFilters, page = 1, limit = 20): Promise<{
  trades: UserTrade[];
  total: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
}> => {
  try {
    const queryParams = new URLSearchParams({
      user_id: userId,
      page: page.toString(),
      limit: limit.toString(),
      ...filters
    });

    const response = await apiGet(`trades/index.php?${queryParams.toString()}`);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب الصفقات');
    }

    return {
      trades: response.data || [],
      total: response.pagination?.total || 0,
      currentPage: response.pagination?.currentPage || page,
      totalPages: response.pagination?.totalPages || 1,
      hasNextPage: response.pagination?.hasNextPage || false
    };
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

// ===== العروض =====
export interface UserOffer {
  id: string;
  type: 'buy' | 'sell';
  amount: number;
  minAmount: number;
  maxAmount: number;
  currency: string;
  price: number;
  status: 'active' | 'inactive' | 'expired';
  createdAt: string;
  views: number;
  responses: number;
  paymentMethods: string[];
  terms: string;
}

export interface OfferFilters {
  status?: string;
  type?: 'buy' | 'sell' | 'all';
  currency?: string;
}

/**
 * جلب عروض المستخدم
 */
export const fetchUserOffers = async (userId: string, filters?: OfferFilters, page = 1, limit = 20): Promise<{
  offers: UserOffer[];
  total: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
}> => {
  try {
    const queryParams = new URLSearchParams({
      user_id: userId,
      page: page.toString(),
      limit: limit.toString(),
      ...filters
    });

    const response = await apiGet(`offers/my-offers.php?${queryParams.toString()}`);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب العروض');
    }

    return {
      offers: response.data || [],
      total: response.pagination?.total || 0,
      currentPage: response.pagination?.currentPage || page,
      totalPages: response.pagination?.totalPages || 1,
      hasNextPage: response.pagination?.hasNextPage || false
    };
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

// ===== التقييمات =====
export interface UserReview {
  id: string;
  rating: number;
  comment: string;
  reviewer: {
    username: string;
    isVerified: boolean;
  };
  reviewed: {
    username: string;
    isVerified: boolean;
  };
  tradeId: string;
  date: string;
  type: 'positive' | 'negative' | 'neutral';
}

/**
 * جلب تقييمات المستخدم
 */
export const fetchUserReviews = async (userId: string, type: 'received' | 'given' = 'received', limit = 20): Promise<UserReview[]> => {
  try {
    const response = await apiGet(`reviews/index.php?user_id=${userId}&type=${type}&limit=${limit}`);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب التقييمات');
    }

    return response.data || [];
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

// ===== النشاط الحديث =====
export interface RecentActivity {
  id: string;
  type: 'trade' | 'offer' | 'review' | 'payment' | 'dispute';
  title: string;
  description: string;
  date: string;
  status: 'success' | 'pending' | 'failed' | 'warning';
  relatedId?: string;
}

/**
 * جلب النشاط الحديث للمستخدم
 */
export const fetchRecentActivity = async (userId: string, limit = 8): Promise<RecentActivity[]> => {
  try {
    const response = await apiGet(`users/activity.php?user_id=${userId}&limit=${limit}`);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب النشاط الحديث');
    }

    return response.data || [];
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

// ===== الملف الشخصي =====
export interface UserProfile {
  id: string;
  username: string;
  fullName: string;
  email: string;
  phone?: string;
  country?: string;
  bio?: string;
  profileImage?: string;
  telegramUsername?: string;
  whatsappNumber?: string;
  isVerified: boolean;
  joinDate: string;
  lastActivity: string;
  rating: number;
  totalTrades: number;
}

/**
 * جلب الملف الشخصي للمستخدم
 */
export const fetchUserProfile = async (userId: string): Promise<UserProfile> => {
  try {
    const response = await apiGet(`users/profile.php?user_id=${userId}`);

    if (!response.success) {
      throw new Error(response.message || 'فشل في جلب الملف الشخصي');
    }

    return response.data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};
