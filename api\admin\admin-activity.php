<?php
/**
 * API سجل نشاط المدراء
 * Admin Activity Log API
 * 
 * يوفر وظائف لعرض وإدارة سجل نشاط المدراء
 * Provides functions for viewing and managing admin activity logs
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../../config/database.php';

// التحقق من صحة الجلسة والصلاحيات الإدارية
function validateAdminSession() {
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
        http_response_code(403);
        echo json_encode([
            'success' => false,
            'error' => 'غير مصرح لك بالوصول إلى هذا المورد',
            'error_en' => 'Access denied. Admin privileges required.'
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
    return $_SESSION['user_id'];
}

// الحصول على سجل النشاط مع فلترة
function getActivityLogs($connection, $filters = [], $pagination = []) {
    $where_conditions = [];
    $params = [];
    
    // بناء شروط الفلترة
    if (!empty($filters['admin_id'])) {
        $where_conditions[] = "aal.admin_id = ?";
        $params[] = $filters['admin_id'];
    }
    
    if (!empty($filters['action_type'])) {
        if (is_array($filters['action_type'])) {
            $placeholders = str_repeat('?,', count($filters['action_type']) - 1) . '?';
            $where_conditions[] = "aal.action_type IN ($placeholders)";
            $params = array_merge($params, $filters['action_type']);
        } else {
            $where_conditions[] = "aal.action_type = ?";
            $params[] = $filters['action_type'];
        }
    }
    
    if (!empty($filters['target_type'])) {
        $where_conditions[] = "aal.target_type = ?";
        $params[] = $filters['target_type'];
    }
    
    if (!empty($filters['target_id'])) {
        $where_conditions[] = "aal.target_id = ?";
        $params[] = $filters['target_id'];
    }
    
    if (!empty($filters['date_from'])) {
        $where_conditions[] = "aal.created_at >= ?";
        $params[] = $filters['date_from'];
    }
    
    if (!empty($filters['date_to'])) {
        $where_conditions[] = "aal.created_at <= ?";
        $params[] = $filters['date_to'] . ' 23:59:59';
    }
    
    if (!empty($filters['ip_address'])) {
        $where_conditions[] = "aal.ip_address = ?";
        $params[] = $filters['ip_address'];
    }
    
    // بناء الاستعلام الأساسي
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // حساب العدد الإجمالي
    $count_query = "
        SELECT COUNT(*) as total
        FROM admin_activity_logs aal
        LEFT JOIN users u ON aal.admin_id = u.id
        $where_clause
    ";
    
    $count_stmt = $connection->prepare($count_query);
    $count_stmt->execute($params);
    $total_count = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // إعداد الترقيم
    $page = $pagination['page'] ?? 1;
    $limit = $pagination['limit'] ?? 50;
    $offset = ($page - 1) * $limit;
    
    // الاستعلام الرئيسي
    $query = "
        SELECT 
            aal.*,
            u.username as admin_username,
            u.full_name as admin_full_name
        FROM admin_activity_logs aal
        LEFT JOIN users u ON aal.admin_id = u.id
        $where_clause
        ORDER BY aal.created_at DESC
        LIMIT ? OFFSET ?
    ";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $connection->prepare($query);
    $stmt->execute($params);
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تحويل JSON details إلى مصفوفة
    foreach ($logs as &$log) {
        if ($log['details']) {
            $log['details'] = json_decode($log['details'], true);
        }
    }
    
    return [
        'logs' => $logs,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $limit,
            'total' => $total_count,
            'total_pages' => ceil($total_count / $limit)
        ]
    ];
}

// الحصول على إحصائيات النشاط
function getActivityStatistics($connection, $date_range = null) {
    $where_clause = '';
    $params = [];
    
    if ($date_range) {
        $where_clause = "WHERE created_at BETWEEN ? AND ?";
        $params = [$date_range['start'], $date_range['end']];
    }
    
    // الإحصائيات الأساسية
    $basic_stats_query = "
        SELECT 
            COUNT(*) as total_activities,
            COUNT(DISTINCT admin_id) as active_admins,
            COUNT(DISTINCT ip_address) as unique_ips,
            COUNT(DISTINCT DATE(created_at)) as active_days
        FROM admin_activity_logs 
        $where_clause
    ";
    
    $stmt = $connection->prepare($basic_stats_query);
    $stmt->execute($params);
    $basic_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إحصائيات حسب نوع النشاط
    $activity_type_query = "
        SELECT 
            action_type,
            COUNT(*) as count,
            COUNT(DISTINCT admin_id) as admin_count
        FROM admin_activity_logs 
        $where_clause
        GROUP BY action_type
        ORDER BY count DESC
    ";
    
    $stmt = $connection->prepare($activity_type_query);
    $stmt->execute($params);
    $activity_types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات حسب المدير
    $admin_stats_query = "
        SELECT 
            u.username,
            u.full_name,
            COUNT(aal.id) as activity_count,
            COUNT(DISTINCT aal.action_type) as action_types_count,
            MAX(aal.created_at) as last_activity
        FROM admin_activity_logs aal
        LEFT JOIN users u ON aal.admin_id = u.id
        $where_clause
        GROUP BY aal.admin_id, u.username, u.full_name
        ORDER BY activity_count DESC
    ";
    
    $stmt = $connection->prepare($admin_stats_query);
    $stmt->execute($params);
    $admin_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // النشاط اليومي
    $daily_activity_query = "
        SELECT 
            DATE(created_at) as activity_date,
            COUNT(*) as activity_count,
            COUNT(DISTINCT admin_id) as admin_count
        FROM admin_activity_logs 
        $where_clause
        GROUP BY DATE(created_at)
        ORDER BY activity_date DESC
        LIMIT 30
    ";
    
    $stmt = $connection->prepare($daily_activity_query);
    $stmt->execute($params);
    $daily_activity = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'basic_stats' => $basic_stats,
        'activity_types' => $activity_types,
        'admin_stats' => $admin_stats,
        'daily_activity' => $daily_activity
    ];
}

// الحصول على أنواع النشاط المتاحة
function getAvailableActionTypes($connection) {
    $query = "
        SELECT DISTINCT action_type, COUNT(*) as count
        FROM admin_activity_logs 
        GROUP BY action_type
        ORDER BY action_type
    ";
    
    $stmt = $connection->prepare($query);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// الحصول على قائمة المدراء النشطين
function getActiveAdmins($connection) {
    $query = "
        SELECT DISTINCT 
            u.id,
            u.username,
            u.full_name,
            COUNT(aal.id) as activity_count,
            MAX(aal.created_at) as last_activity
        FROM users u
        JOIN admin_activity_logs aal ON u.id = aal.admin_id
        WHERE u.is_admin = TRUE
        GROUP BY u.id, u.username, u.full_name
        ORDER BY last_activity DESC
    ";
    
    $stmt = $connection->prepare($query);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// تصدير سجل النشاط
function exportActivityLog($connection, $filters = []) {
    $filename = "admin_activity_log_" . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    
    $output = fopen('php://output', 'w');
    
    // إضافة BOM للدعم الصحيح للعربية في Excel
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // رؤوس الأعمدة
    fputcsv($output, [
        'المعرف', 'المدير', 'نوع النشاط', 'نوع الهدف', 'معرف الهدف',
        'التفاصيل', 'عنوان IP', 'المتصفح', 'التاريخ والوقت'
    ]);
    
    // بناء الاستعلام مع الفلاتر
    $where_conditions = [];
    $params = [];
    
    if (!empty($filters['admin_id'])) {
        $where_conditions[] = "aal.admin_id = ?";
        $params[] = $filters['admin_id'];
    }
    
    if (!empty($filters['action_type'])) {
        $where_conditions[] = "aal.action_type = ?";
        $params[] = $filters['action_type'];
    }
    
    if (!empty($filters['date_from'])) {
        $where_conditions[] = "aal.created_at >= ?";
        $params[] = $filters['date_from'];
    }
    
    if (!empty($filters['date_to'])) {
        $where_conditions[] = "aal.created_at <= ?";
        $params[] = $filters['date_to'] . ' 23:59:59';
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    $query = "
        SELECT 
            aal.id,
            u.username as admin_username,
            aal.action_type,
            aal.target_type,
            aal.target_id,
            aal.details,
            aal.ip_address,
            aal.user_agent,
            aal.created_at
        FROM admin_activity_logs aal
        LEFT JOIN users u ON aal.admin_id = u.id
        $where_clause
        ORDER BY aal.created_at DESC
    ";
    
    $stmt = $connection->prepare($query);
    $stmt->execute($params);
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $details = $row['details'] ? json_encode(json_decode($row['details'], true), JSON_UNESCAPED_UNICODE) : '';
        
        fputcsv($output, [
            $row['id'],
            $row['admin_username'],
            $row['action_type'],
            $row['target_type'],
            $row['target_id'],
            $details,
            $row['ip_address'],
            $row['user_agent'],
            $row['created_at']
        ]);
    }
    
    fclose($output);
    exit();
}

// معالجة الطلبات
try {
    session_start();
    $admin_id = validateAdminSession();

    // Get database connection
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();

    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        $action = $_GET['action'] ?? 'logs';
        
        switch ($action) {
            case 'logs':
                $filters = [
                    'admin_id' => $_GET['admin_id'] ?? null,
                    'action_type' => $_GET['action_type'] ?? null,
                    'target_type' => $_GET['target_type'] ?? null,
                    'target_id' => $_GET['target_id'] ?? null,
                    'date_from' => $_GET['date_from'] ?? null,
                    'date_to' => $_GET['date_to'] ?? null,
                    'ip_address' => $_GET['ip_address'] ?? null
                ];
                
                $pagination = [
                    'page' => (int)($_GET['page'] ?? 1),
                    'limit' => (int)($_GET['limit'] ?? 50)
                ];
                
                $result = getActivityLogs($connection, $filters, $pagination);
                
                echo json_encode([
                    'success' => true,
                    'data' => $result['logs'],
                    'pagination' => $result['pagination']
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'statistics':
                $date_range = null;
                if (isset($_GET['date_from']) && isset($_GET['date_to'])) {
                    $date_range = [
                        'start' => $_GET['date_from'],
                        'end' => $_GET['date_to']
                    ];
                }
                
                $stats = getActivityStatistics($connection, $date_range);
                echo json_encode([
                    'success' => true,
                    'data' => $stats
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'action_types':
                $types = getAvailableActionTypes($connection);
                echo json_encode([
                    'success' => true,
                    'data' => $types
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'active_admins':
                $admins = getActiveAdmins($connection);
                echo json_encode([
                    'success' => true,
                    'data' => $admins
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'export':
                $filters = [
                    'admin_id' => $_GET['admin_id'] ?? null,
                    'action_type' => $_GET['action_type'] ?? null,
                    'date_from' => $_GET['date_from'] ?? null,
                    'date_to' => $_GET['date_to'] ?? null
                ];
                exportActivityLog($connection, $filters);
                break;
                
            default:
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'إجراء غير صحيح',
                    'error_en' => 'Invalid action'
                ], JSON_UNESCAPED_UNICODE);
                break;
        }
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'طريقة الطلب غير مدعومة',
            'error_en' => 'Method not allowed'
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في الخادم: ' . $e->getMessage(),
        'error_en' => 'Server error: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
