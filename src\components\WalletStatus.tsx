'use client';

import { useState, useEffect } from 'react';
import { Wallet, WifiOff, CheckCircle, AlertCircle, Copy, ExternalLink, Loader2 } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { notificationService } from '@/services/notificationService';

interface WalletStatusProps {
  variant?: 'header' | 'inline' | 'card';
  showBalance?: boolean;
  showNetwork?: boolean;
  className?: string;
}

export default function WalletStatus({ 
  variant = 'inline', 
  showBalance = false, 
  showNetwork = true,
  className = '' 
}: WalletStatusProps) {
  const { t } = useTranslation();
  const { 
    isWalletConnected, 
    walletAddress, 
    walletBalance, 
    walletNetwork, 
    connectWallet, 
    disconnectWallet,
    isLoading 
  } = useAuth();

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const copyAddress = () => {
    if (walletAddress) {
      navigator.clipboard.writeText(walletAddress);
      notificationService.success(t('wallet.addressCopied'));
    }
  };

  const openInExplorer = () => {
    if (walletAddress) {
      const explorerUrl = walletNetwork.includes('testnet') 
        ? `https://testnet.bscscan.com/address/${walletAddress}`
        : `https://bscscan.com/address/${walletAddress}`;
      window.open(explorerUrl, '_blank');
    }
  };

  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 rtl:space-x-reverse ${className}`}>
        <Loader2 className="w-4 h-4 animate-spin text-primary-500" />
        <span className="text-sm text-gray-500 dark:text-gray-400">{t('wallet.checking')}</span>
      </div>
    );
  }

  // Header variant - compact display
  if (variant === 'header') {
    return (
      <div className={`flex items-center space-x-1 sm:space-x-2 rtl:space-x-reverse ${className}`}>
        {isWalletConnected ? (
          <div className="flex items-center space-x-1 sm:space-x-2 rtl:space-x-reverse bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 px-2 sm:px-3 py-1 sm:py-1.5 rounded-lg border border-green-200 dark:border-green-800 shadow-sm hover:shadow-md transition-all duration-200">
            <div className="flex items-center justify-center w-5 h-5 sm:w-6 sm:h-6 bg-green-100 dark:bg-green-800/50 rounded-full">
              <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-600 dark:text-green-400" />
            </div>
            <div className="hidden sm:flex flex-col">
              <span className="text-xs font-medium text-green-700 dark:text-green-300 leading-tight">
                {t('wallet.connected.title')}
              </span>
              <span className="text-xs text-green-600 dark:text-green-400 font-mono leading-tight">
                {formatAddress(walletAddress!)}
              </span>
            </div>
            <span className="sm:hidden text-xs font-medium text-green-700 dark:text-green-300">
              {formatAddress(walletAddress!)}
            </span>
          </div>
        ) : (
          <button
            onClick={connectWallet}
            className="flex items-center space-x-1 sm:space-x-2 rtl:space-x-reverse bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 hover:from-orange-100 hover:to-amber-100 dark:hover:from-orange-900/30 dark:hover:to-amber-900/30 px-2 sm:px-3 py-1 sm:py-1.5 rounded-lg border border-orange-200 dark:border-orange-800 shadow-sm hover:shadow-md transition-all duration-200 group"
          >
            <div className="flex items-center justify-center w-5 h-5 sm:w-6 sm:h-6 bg-orange-100 dark:bg-orange-800/50 rounded-full group-hover:scale-110 transition-transform duration-200">
              <Wallet className="w-3 h-3 sm:w-4 sm:h-4 text-orange-600 dark:text-orange-400" />
            </div>
            <span className="text-xs sm:text-sm font-medium text-orange-700 dark:text-orange-300 hidden sm:block">
              {t('wallet.connect.button')}
            </span>
            <span className="text-xs font-medium text-orange-700 dark:text-orange-300 sm:hidden">
              {t('wallet.connect')}
            </span>
          </button>
        )}
      </div>
    );
  }

  // Card variant - full featured display
  if (variant === 'card') {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 ${className}`}>
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <Wallet className="w-5 h-5 ml-2 rtl:ml-2 ltr:mr-2" />
            {t('wallet.status')}
          </h3>
          {isWalletConnected && (
            <div className="flex items-center space-x-1 rtl:space-x-reverse">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-green-600 dark:text-green-400">{t('wallet.connected.title')}</span>
            </div>
          )}
        </div>

        {isWalletConnected ? (
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">{t('wallet.address')}</p>
                <p className="font-mono text-sm text-gray-900 dark:text-white">{formatAddress(walletAddress!)}</p>
              </div>
              <div className="flex space-x-2 rtl:space-x-reverse">
                <button
                  onClick={copyAddress}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                  title={t('wallet.copyAddress')}
                >
                  <Copy className="w-4 h-4" />
                </button>
                <button
                  onClick={openInExplorer}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                  title={t('wallet.viewInExplorer')}
                >
                  <ExternalLink className="w-4 h-4" />
                </button>
              </div>
            </div>

            {showNetwork && walletNetwork && (
              <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div>
                  <p className="text-sm text-blue-600 dark:text-blue-400">{t('wallet.network')}</p>
                  <p className="font-medium text-blue-700 dark:text-blue-300">{walletNetwork}</p>
                </div>
              </div>
            )}

            {showBalance && (
              <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div>
                  <p className="text-sm text-green-600 dark:text-green-400">{t('wallet.balance')}</p>
                  <p className="font-medium text-green-700 dark:text-green-300">{walletBalance} BNB</p>
                </div>
              </div>
            )}

            <button
              onClick={disconnectWallet}
              className="w-full btn btn-secondary text-sm"
            >
              {t('wallet.disconnect')}
            </button>
          </div>
        ) : (
          <div className="text-center py-6">
            <WifiOff className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-3" />
            <p className="text-gray-500 dark:text-gray-400 mb-4">{t('wallet.notConnected.description')}</p>
            <button
              onClick={connectWallet}
              className="btn btn-primary"
            >
              <Wallet className="w-4 h-4 ml-2 rtl:ml-2 ltr:mr-2" />
              {t('wallet.connect.button')}
            </button>
          </div>
        )}
      </div>
    );
  }

  // Inline variant - default
  return (
    <div className={`flex items-center space-x-3 rtl:space-x-reverse ${className}`}>
      {isWalletConnected ? (
        <>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {t('wallet.connected.title')}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                {formatAddress(walletAddress!)}
              </p>
            </div>
          </div>
          {showNetwork && walletNetwork && (
            <span className="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded">
              {walletNetwork}
            </span>
          )}
        </>
      ) : (
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <AlertCircle className="w-5 h-5 text-orange-500" />
          <div>
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              {t('wallet.notConnected.title')}
            </p>
            <button
              onClick={connectWallet}
              className="text-xs text-primary-600 dark:text-primary-400 hover:underline"
            >
              {t('wallet.clickToConnect')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
