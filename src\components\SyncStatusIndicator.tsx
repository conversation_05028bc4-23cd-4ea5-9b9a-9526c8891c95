'use client';

import { useState, useEffect } from 'react';
import { 
  Wifi, 
  WifiOff, 
  Refresh<PERSON>w, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Activity,
  Database
} from 'lucide-react';
import { syncManager } from '@/utils/enableSync';
import { useTranslation } from '@/hooks/useTranslation';

interface SyncStatus {
  isInitialized: boolean;
  syncStats: any;
  eventStats: any;
  config: any;
}

export default function SyncStatusIndicator() {
  const { t } = useTranslation();
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // تحديث حالة المزامنة
  const updateSyncStatus = () => {
    try {
      const status = syncManager.getStatus();
      setSyncStatus(status);
    } catch (error) {
      console.error('خطأ في جلب حالة المزامنة:', error);
    }
  };

  // تحديث دوري لحالة المزامنة
  useEffect(() => {
    updateSyncStatus();
    
    const interval = setInterval(updateSyncStatus, 5000); // كل 5 ثوان
    
    return () => clearInterval(interval);
  }, []);

  // تشغيل مزامنة فورية
  const handleForceSync = async () => {
    setIsLoading(true);
    try {
      await syncManager.forceSyncNow();
      updateSyncStatus();
    } catch (error) {
      console.error('خطأ في المزامنة الفورية:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!syncStatus) {
    return (
      <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-500">
        <Clock className="w-4 h-4 animate-pulse" />
        <span className="text-sm">جاري التحقق...</span>
      </div>
    );
  }

  const { isInitialized, syncStats, eventStats } = syncStatus;

  // تحديد لون ونوع المؤشر
  const getStatusColor = () => {
    if (!isInitialized) return 'text-red-500';
    if (syncStats?.failedEntities > 0) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getStatusIcon = () => {
    if (!isInitialized) return <WifiOff className="w-4 h-4" />;
    if (syncStats?.failedEntities > 0) return <AlertCircle className="w-4 h-4" />;
    return <CheckCircle className="w-4 h-4" />;
  };

  const getStatusText = () => {
    if (!isInitialized) return 'غير متصل';
    if (syncStats?.failedEntities > 0) return 'مزامنة جزئية';
    return 'متزامن';
  };

  return (
    <div className="flex items-center space-x-3 rtl:space-x-reverse">
      {/* مؤشر الحالة الرئيسي */}
      <div className={`flex items-center space-x-2 rtl:space-x-reverse ${getStatusColor()}`}>
        {getStatusIcon()}
        <span className="text-sm font-medium">{getStatusText()}</span>
      </div>

      {/* معلومات إضافية */}
      {isInitialized && syncStats && (
        <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-500 text-xs">
          <Database className="w-3 h-3" />
          <span>{syncStats.syncedEntities || 0} متزامن</span>
          
          {syncStats.failedEntities > 0 && (
            <>
              <span>•</span>
              <span className="text-yellow-600">{syncStats.failedEntities} فشل</span>
            </>
          )}
        </div>
      )}

      {/* زر المزامنة الفورية */}
      <button
        onClick={handleForceSync}
        disabled={isLoading}
        className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
        title="مزامنة فورية"
      >
        <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
      </button>

      {/* مؤشر النشاط */}
      {isInitialized && (
        <div className="flex items-center space-x-1 rtl:space-x-reverse">
          <Activity className="w-3 h-3 text-blue-500 animate-pulse" />
          <span className="text-xs text-gray-500">نشط</span>
        </div>
      )}
    </div>
  );
}

// مكون مبسط للاستخدام في الهيدر
export function SimpleSyncIndicator() {
  const [syncStatus, setSyncStatus] = useState<{
    isConnected: boolean;
    lastSync: Date | null;
    hasErrors: boolean;
  }>({
    isConnected: false,
    lastSync: null,
    hasErrors: false
  });

  useEffect(() => {
    const checkConnection = () => {
      try {
        const status = syncManager.getStatus();
        setSyncStatus({
          isConnected: status.isInitialized,
          lastSync: status.syncStats?.lastSyncTime ? new Date(status.syncStats.lastSyncTime) : null,
          hasErrors: (status.syncStats?.failedEntities || 0) > 0
        });
      } catch (error) {
        setSyncStatus({
          isConnected: false,
          lastSync: null,
          hasErrors: true
        });
      }
    };

    checkConnection();
    const interval = setInterval(checkConnection, 15000); // كل 15 ثانية

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = () => {
    if (!syncStatus.isConnected) return 'text-red-500';
    if (syncStatus.hasErrors) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getStatusIcon = () => {
    if (!syncStatus.isConnected) return <WifiOff className="w-4 h-4" />;
    if (syncStatus.hasErrors) return <AlertCircle className="w-4 h-4" />;
    return <CheckCircle className="w-4 h-4" />;
  };

  const getStatusText = () => {
    if (!syncStatus.isConnected) return 'قاعدة البيانات';
    if (syncStatus.hasErrors) return 'مزامنة جزئية';
    return 'قاعدة البيانات';
  };

  const getTooltipText = () => {
    if (!syncStatus.isConnected) return 'غير متصل بقاعدة البيانات';
    if (syncStatus.hasErrors) return 'مزامنة جزئية - يوجد أخطاء';
    const lastSyncText = syncStatus.lastSync
      ? `آخر مزامنة: ${syncStatus.lastSync.toLocaleTimeString('ar-SA')}`
      : 'متزامن';
    return `متصل بقاعدة البيانات - ${lastSyncText}`;
  };

  return (
    <div
      className={`flex items-center space-x-1 rtl:space-x-reverse cursor-help ${getStatusColor()}`}
      title={getTooltipText()}
    >
      {getStatusIcon()}
      <span className="text-xs font-medium">
        {getStatusText()}
      </span>
      {syncStatus.isConnected && !syncStatus.hasErrors && (
        <div className="w-1 h-1 bg-current rounded-full animate-pulse"></div>
      )}
    </div>
  );
}

// مكون تفصيلي لصفحة الإدارة
export function DetailedSyncStatus() {
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const updateStatus = () => {
    try {
      const status = syncManager.getStatus();
      setSyncStatus(status);
    } catch (error) {
      console.error('خطأ في جلب حالة المزامنة:', error);
    }
  };

  useEffect(() => {
    updateStatus();
    const interval = setInterval(updateStatus, 3000);
    return () => clearInterval(interval);
  }, []);

  const handleRestart = async () => {
    setIsLoading(true);
    try {
      await syncManager.restart();
      updateStatus();
    } catch (error) {
      console.error('خطأ في إعادة تشغيل المزامنة:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!syncStatus) {
    return <div className="text-center text-gray-500">جاري التحميل...</div>;
  }

  const { isInitialized, syncStats, eventStats, config } = syncStatus;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          حالة المزامنة مع العقد الذكي
        </h3>
        <button
          onClick={handleRestart}
          disabled={isLoading}
          className="btn btn-secondary btn-sm"
        >
          {isLoading ? (
            <RefreshCw className="w-4 h-4 animate-spin" />
          ) : (
            'إعادة تشغيل'
          )}
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* حالة الاتصال */}
        <div className="text-center">
          <div className={`text-2xl mb-2 ${isInitialized ? 'text-green-500' : 'text-red-500'}`}>
            {isInitialized ? <CheckCircle className="w-8 h-8 mx-auto" /> : <WifiOff className="w-8 h-8 mx-auto" />}
          </div>
          <div className="text-sm font-medium text-gray-900 dark:text-white">
            {isInitialized ? 'متصل' : 'غير متصل'}
          </div>
        </div>

        {/* إحصائيات المزامنة */}
        {syncStats && (
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600 mb-2">
              {syncStats.syncedEntities || 0}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              عنصر متزامن
            </div>
            {syncStats.failedEntities > 0 && (
              <div className="text-xs text-red-500 mt-1">
                {syncStats.failedEntities} فشل
              </div>
            )}
          </div>
        )}

        {/* آخر مزامنة */}
        {syncStats?.lastSyncTime && (
          <div className="text-center">
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              آخر مزامنة
            </div>
            <div className="text-xs text-gray-500">
              {new Date(syncStats.lastSyncTime).toLocaleString('ar-SA')}
            </div>
          </div>
        )}
      </div>

      {/* إعدادات المزامنة */}
      {config && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
            <div>المزامنة الدورية: {config.enablePeriodicSync ? 'مفعلة' : 'معطلة'}</div>
            <div>مراقبة الأحداث: {config.enableEventMonitoring ? 'مفعلة' : 'معطلة'}</div>
            <div>فترة المزامنة: {config.syncIntervalMs / 1000} ثانية</div>
          </div>
        </div>
      )}
    </div>
  );
}
