'use client';

import { useState, useEffect } from 'react';
import {
  BarChart3,
  Users,
  DollarSign,
  AlertTriangle,
  Settings,
  Shield,
  Activity,
  TrendingUp,
  Clock,
  Eye,
  CheckCircle,
  Loader2,
  Bell,
  FileText,
  Database,
  User,
  Globe,
  Coins,
  Monitor,
  TestTube,
  Zap,
  ChevronRight,
  Plus,
  RefreshCw,
  CreditCard
} from 'lucide-react';
import { contractService } from '@/services/contractService';
import { walletService } from '@/services/walletService';
import { notificationService } from '@/services/notificationService';
import { getTradeStatusTranslation } from '@/utils/tradeStatus';


// Import enhanced admin components
import AdvancedUserManagement from '@/components/admin/AdvancedUserManagement';
import AnalyticsCharts from '@/components/admin/AnalyticsCharts';
import EnhancedSmartContractManager from '@/components/admin/EnhancedSmartContractManager';
import AdvancedDisputeManagement from '@/components/admin/AdvancedDisputeManagement';
import AdvancedTradeManagement from '@/components/admin/AdvancedTradeManagement';
import NetworkTokenManagement from '@/components/admin/NetworkTokenManagement';
import NotificationSystem from '@/components/admin/NotificationSystem';
import SystemMonitoring from '@/components/admin/SystemMonitoring';
import AdminSettings from '@/components/admin/AdminSettings';

import AdminSecurity from '@/components/admin/AdminSecurity';
import SubscriptionManagement from '@/components/admin/SubscriptionManagement';

// Use admin-specific translations
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { databaseService } from '@/services/databaseService';

import AdminDashboardHeader from '@/components/AdminDashboardHeader';
// استيراد أنواع البيانات من الملف الرئيسي
import { User as UserType, Trade as TradeType, AnalyticsData, TradeStatus } from '@/types';


// واجهة محلية للصفقات مع خصائص إضافية للعرض
interface AdminTradeView extends Omit<TradeType, 'totalAmount'> {
  seller?: { name?: string };
  buyer?: { name?: string };
  partner?: string;
  totalAmount?: number;
}

export default function AdminDashboard() {
  const { t, language } = useAdminTranslation();
  const [activeTab, setActiveTab] = useState('overview');
  const [users, setUsers] = useState<UserType[]>([]);
  const [trades, setTrades] = useState<AdminTradeView[]>([]);
  const [platformStats, setPlatformStats] = useState<AnalyticsData | null>(null);
  const [adminStats, setAdminStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [adminData, setAdminData] = useState<any>(null);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [isWalletConnected, setIsWalletConnected] = useState(false);

  // RTL/LTR support
  const isRTL = language === 'ar';

  // Load admin dashboard data
  useEffect(() => {
    const loadAdminData = async () => {
      if (!isLoggedIn) return;

      try {
        setIsLoading(true);

        // تحميل البيانات بشكل منفصل مع معالجة أفضل للأخطاء
        let allUsers: any[] = [];
        let allTrades: any[] = [];
        let analytics: any = null;
        let adminStatsData: any = null;

        try {
          allUsers = await databaseService.getUsers();
        } catch (error) {
          console.error('Error loading users:', error);
          allUsers = [];
        }

        try {
          allTrades = await databaseService.getTrades();
        } catch (error) {
          console.error('Error loading trades:', error);
          allTrades = [];
        }

        try {
          analytics = await databaseService.getAnalyticsData();
        } catch (error) {
          console.error('Error loading analytics:', error);
          analytics = {
            totalUsers: 0,
            activeUsers: 0,
            totalTrades: 0,
            totalVolume: 0,
            platformRevenue: 0,
            activeOffers: 0,
            completedTrades: 0,
            averageRating: 0,
            monthlyGrowth: 0,
            dailyStats: []
          };
        }

        try {
          console.log('Loading admin stats...');
          adminStatsData = await databaseService.getAdminStats();
          console.log('Admin stats loaded:', adminStatsData);
        } catch (error) {
          console.error('Error loading admin stats:', error);
          // حساب الإحصائيات من البيانات المحملة
          adminStatsData = {
            totalUsers: allUsers.length,
            activeUsers: allUsers.filter((u: any) => u.isOnline || u.is_active).length,
            totalTrades: allTrades.length,
            activeTrades: allTrades.filter((t: any) => ['CREATED', 'PAYMENT_SENT', 'BUYER_JOINED', 'created', 'payment_sent', 'joined'].includes(t.status)).length,
            completedTrades: allTrades.filter((t: any) => ['COMPLETED', 'completed'].includes(t.status)).length,
            disputedTrades: allTrades.filter((t: any) => ['DISPUTED', 'disputed'].includes(t.status)).length,
            totalVolume: analytics?.totalVolume || 0,
            platformRevenue: analytics?.platformRevenue || 0,
            recentActivity: {
              completedTrades: allTrades.filter((t: any) => ['COMPLETED', 'completed'].includes(t.status)).slice(0, 3),
              newUsers: allUsers.slice(-3),
              disputes: allTrades.filter((t: any) => ['DISPUTED', 'disputed'].includes(t.status)).slice(0, 3)
            }
          };
          console.log('Calculated admin stats from loaded data:', adminStatsData);
        }

        setUsers(allUsers);

        // تحويل الصفقات إلى التنسيق المطلوب للعرض
        const formattedTrades: AdminTradeView[] = allTrades.map(trade => ({
          ...trade,
          seller: { name: `البائع ${trade.sellerId?.slice(-4) || ''}` },
          buyer: { name: `المشتري ${trade.buyerId?.slice(-4) || ''}` },
          partner: `شريك ${trade.id?.slice(-4) || ''}`,
          totalAmount: (trade.amount || 0) * (trade.price || 1),
          status: trade.status || 'CREATED'
        }));

        setTrades(formattedTrades);
        setPlatformStats(analytics);
        setAdminStats(adminStatsData);
      } catch (error) {
        console.error('Error loading admin data:', error);
        // في حالة فشل كامل، عرض بيانات فارغة
        setUsers([]);
        setTrades([]);
        setPlatformStats({
          totalUsers: 0,
          activeUsers: 0,
          totalTrades: 0,
          totalVolume: 0,
          platformRevenue: 0,
          activeOffers: 0,
          completedTrades: 0,
          averageRating: 0,
          monthlyGrowth: 0,
          dailyStats: []
        });
        setAdminStats({
          totalUsers: 0,
          activeUsers: 0,
          totalTrades: 0,
          activeTrades: 0,
          completedTrades: 0,
          disputedTrades: 0,
          totalVolume: 0,
          platformRevenue: 0,
          recentActivity: {
            completedTrades: [],
            newUsers: [],
            disputes: []
          }
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadAdminData();
  }, [isLoggedIn]);

  // التحقق من حالة تسجيل الدخول عند تحميل المكون
  useEffect(() => {
    const checkAuthStatus = () => {
      setIsCheckingAuth(true);

      try {
        // التحقق من حالة المحفظة
        const walletConnected = walletService.isWalletConnected();
        setIsWalletConnected(walletConnected);

        // التحقق من وجود جلسة مدير محفوظة (sessionStorage)
        const adminToken = sessionStorage.getItem('admin_token');
        const adminSession = sessionStorage.getItem('admin_session');
        const walletSession = sessionStorage.getItem('admin_wallet_session');

        if (adminToken && adminSession) {
          // جلسة تسجيل دخول بالبيانات
          const parsedSession = JSON.parse(adminSession);
          if (parsedSession.expiresAt && new Date() < new Date(parsedSession.expiresAt)) {
            setAdminData(parsedSession.admin);
            setIsLoggedIn(true);
          } else {
            // انتهت صلاحية الجلسة
            sessionStorage.removeItem('admin_token');
            sessionStorage.removeItem('admin_session');
          }
        } else if (walletSession) {
          // جلسة تسجيل دخول بالمحفظة
          const parsedWalletSession = JSON.parse(walletSession);
          setAdminData(parsedWalletSession);
          setIsLoggedIn(true);
        }
      } catch (error) {
        console.error('Error checking auth status:', error);
        // مسح جميع الجلسات في حالة الخطأ
        sessionStorage.removeItem('admin_token');
        sessionStorage.removeItem('admin_session');
        sessionStorage.removeItem('admin_wallet_session');
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkAuthStatus();
  }, []);

  // معالج نجاح تسجيل الدخول
  const handleLoginSuccess = (loginData: any) => {
    setAdminData(loginData);
    setIsLoggedIn(true);

    // حفظ الجلسة في localStorage
    localStorage.setItem('adminSession', JSON.stringify(loginData));

    notificationService.success(t('admin.messages.loginSuccess'));
  };



  // ربط المحفظة
  const handleConnectWallet = async () => {
    try {
      const walletInfo = await walletService.connectWallet('metamask');
      setIsWalletConnected(true);
      notificationService.walletConnected(walletInfo.address);
    } catch (error: any) {
      notificationService.error(error.message || t('adminDashboard.messages.walletConnectFailed'));
    }
  };

  // إذا كان يتم التحقق من المصادقة
  if (isCheckingAuth) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-primary-600 dark:text-primary-400 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-300">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  // إذا لم يسجل دخول، إعادة توجيه لصفحة تسجيل الدخول
  if (!isLoggedIn) {
    if (typeof window !== 'undefined') {
      window.location.href = '/admin/login';
    }
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">
            جاري إعادة التوجيه لصفحة تسجيل الدخول...
          </p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
        return 'text-success-600 bg-success-100';
      case 'suspended':
      case 'disputed':
        return 'text-danger-600 bg-danger-100';
      case 'pending':
        return 'text-warning-600 bg-warning-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: string) => {
    // حالات المستخدمين
    switch (status) {
      case 'active':
        return t('adminDashboard.status.active');
      case 'suspended':
        return t('adminDashboard.status.suspended');
      case 'pending':
        return t('adminDashboard.status.pending');
      default:
        // حالات الصفقات - استخدام نظام الترجمة الصحيح
        return getTradeStatusTranslation(status.toUpperCase(), t);
    }
  };



  const handleResolveDispute = async (tradeId: string, favorSeller: boolean) => {
    if (!isWalletConnected) {
      notificationService.error(t('adminDashboard.walletNotConnectedDesc'));
      return;
    }

    // Confirm action
    const party = favorSeller ? t('adminDashboard.messages.seller') : t('adminDashboard.messages.buyer');
    notificationService.confirm(
      t('adminDashboard.messages.confirmDispute', { party }),
      async () => {
        setIsLoading(true);
        const loadingToast = notificationService.loading(t('adminDashboard.messages.resolvingDispute'));

        try {
          // Resolve dispute in smart contract
          const txHash = await contractService.resolveDispute(parseInt(tradeId.replace('T', '')), favorSeller);

          notificationService.dismiss(loadingToast);
          const pendingToast = notificationService.transactionPending(txHash);

          // Simulate waiting for transaction confirmation
          setTimeout(() => {
            notificationService.dismiss(pendingToast);
            notificationService.transactionConfirmed(txHash);
            notificationService.success(t('adminDashboard.messages.disputeResolved', { party }));

            // Update trade status
            setTrades(prev => prev.map(trade =>
              trade.id === tradeId
                ? { ...trade, status: TradeStatus.COMPLETED }
                : trade
            ));
          }, 3000);

        } catch (error: any) {
          notificationService.dismiss(loadingToast);

          if (error.code === 4001) {
            notificationService.userRejectedTransaction();
          } else {
            notificationService.transactionFailed(error.message || t('adminDashboard.messages.disputeResolveError'));
          }
        } finally {
          setIsLoading(false);
        }
      }
    );
  };

  // دالة لتنفيذ إجراءات على الصفقات (محفوظة للاستخدام المستقبلي)
  // const handleTradeAction = (tradeId: string, action: string) => {
  //   setTrades(prev => prev.map(trade =>
  //     trade.id === tradeId
  //       ? { ...trade, status: action }
  //       : trade
  //   ));
  //   const actionText = action === 'completed' ? t('adminDashboard.actions.complete') : t('adminDashboard.actions.cancel');
  //   notificationService.success(t('adminDashboard.messages.tradeActionSuccess', { action: actionText }));
  // };

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Enhanced Admin Dashboard Header */}
      <AdminDashboardHeader
        activeTab={activeTab}
        onTabChange={setActiveTab}
        systemStatus={{
          users: users.length,
          trades: trades.length,
          disputes: 0,
          alerts: 0
        }}
      />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="mb-6">
          <p className={`text-gray-600 dark:text-gray-400 ${isRTL ? 'text-right' : 'text-left'}`}>
            {t('dashboard.subtitle')}
          </p>
        </div>

          {/* تحذير عدم ربط المحفظة */}
          {!isWalletConnected && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-6 mb-8">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <AlertTriangle className="w-6 h-6 text-yellow-600 dark:text-yellow-400 ml-3" />
                  <div>
                    <h3 className="font-semibold text-yellow-800 dark:text-yellow-300">{t('adminDashboard.walletNotConnected')}</h3>
                    <p className="text-yellow-700 dark:text-yellow-400 text-sm">{t('adminDashboard.walletNotConnectedDesc')}</p>
                  </div>
                </div>
                <button
                  onClick={handleConnectWallet}
                  className="btn btn-warning"
                >
                  {t('adminDashboard.connectWallet')}
                </button>
              </div>
            </div>
          )}

          {/* الإحصائيات السريعة */}
          {platformStats && (
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{t('adminDashboard.stats.totalUsers')}</p>
                  <p className="text-xl font-bold text-gray-900 dark:text-white">{(adminStats?.totalUsers || users?.length || 0).toLocaleString()}</p>
                </div>
                <Users className="w-8 h-8 text-primary-600 dark:text-primary-400" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{t('adminDashboard.stats.activeUsers')}</p>
                  <p className="text-xl font-bold text-gray-900 dark:text-white">{(adminStats?.activeUsers || platformStats?.activeUsers || 0).toLocaleString()}</p>
                </div>
                <Activity className="w-8 h-8 text-success-600 dark:text-success-400" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{t('adminDashboard.stats.totalTrades')}</p>
                  <p className="text-xl font-bold text-gray-900 dark:text-white">{(adminStats?.totalTrades || platformStats?.totalTrades || 0).toLocaleString()}</p>
                </div>
                <TrendingUp className="w-8 h-8 text-warning-600 dark:text-warning-400" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{t('adminDashboard.stats.activeTrades')}</p>
                  <p className="text-xl font-bold text-gray-900 dark:text-white">{(adminStats?.activeTrades || trades?.filter(t => t.status === 'CREATED' || t.status === 'PAYMENT_SENT' || t.status === 'BUYER_JOINED')?.length || 0).toLocaleString()}</p>
                </div>
                <Clock className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{t('adminDashboard.stats.pendingDisputes')}</p>
                  <p className="text-xl font-bold text-gray-900 dark:text-white">{(adminStats?.disputedTrades || trades?.filter(t => t.status === 'DISPUTED')?.length || 0).toLocaleString()}</p>
                </div>
                <AlertTriangle className="w-8 h-8 text-danger-600 dark:text-danger-400" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{t('adminDashboard.stats.monthlyFees')}</p>
                  <p className="text-xl font-bold text-gray-900 dark:text-white">${((adminStats?.platformRevenue || platformStats?.platformRevenue || 0) / 1000).toFixed(0)}K</p>
                </div>
                <DollarSign className="w-8 h-8 text-green-600 dark:text-green-400" />
              </div>
            </div>
            </div>
          )}

          {/* Enhanced Navigation Tabs */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
            <div className="border-b border-gray-200 dark:border-gray-700">
              {/* Navigation Header */}
              <div className="px-6 py-4">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{t('dashboard.title')}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('dashboard.subtitle')}</p>
                  </div>
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <span className="text-sm text-gray-500 dark:text-gray-400">{t('common.labels.status')}:</span>
                      <span className="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full text-sm font-medium flex items-center">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                        {t('common.status.active')}
                      </span>
                    </div>
                    <button
                      onClick={() => window.location.reload()}
                      className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                      title={t('common.actions.refresh')}
                    >
                      <RefreshCw className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Enhanced Tab Groups */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">


                  {/* العمود الأول: الإدارة الأساسية */}
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <BarChart3 className="w-4 h-4 text-blue-500" />
                      <h4 className="text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">{t('navigation.groups.core')}</h4>
                    </div>
                    <div className="space-y-1">
                      {[
                        { id: 'overview', label: t('navigation.overview'), icon: BarChart3, color: 'blue' },
                        { id: 'analytics', label: t('navigation.analytics'), icon: TrendingUp, color: 'purple' },
                        { id: 'trades', label: t('navigation.trades'), icon: Activity, color: 'green' },
                        { id: 'disputes', label: t('navigation.disputes'), icon: AlertTriangle, color: 'red' },
                        { id: 'settings', label: t('navigation.settings'), icon: Settings, color: 'gray' }
                      ].map((tab) => (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`w-full flex items-center px-3 py-2.5 rounded-lg text-sm font-medium transition-all group ${
                            activeTab === tab.id
                              ? `bg-${tab.color}-50 dark:bg-${tab.color}-900/20 text-${tab.color}-700 dark:text-${tab.color}-300 shadow-sm border border-${tab.color}-200 dark:border-${tab.color}-800`
                              : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-200'
                          }`}
                        >
                          <tab.icon className="w-4 h-4 mr-3 flex-shrink-0 group-hover:scale-110 transition-transform" />
                          <span className="truncate">{tab.label}</span>
                          {activeTab === tab.id && <ChevronRight className="w-4 h-4 ml-auto text-current" />}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* العمود الثاني: إدارة المستخدمين */}
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Users className="w-4 h-4 text-purple-500" />
                      <h4 className="text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">{t('navigation.groups.users')}</h4>
                    </div>
                    <div className="space-y-1">
                      {[
                        { id: 'users', label: t('navigation.users'), icon: Users, color: 'purple' },

                        { id: 'notifications', label: t('navigation.notifications'), icon: Bell, color: 'yellow' },
                        { id: 'subscriptions', label: 'Subscriptions', icon: CreditCard, color: 'emerald' }
                      ].map((tab) => (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`w-full flex items-center px-3 py-2.5 rounded-lg text-sm font-medium transition-all group ${
                            activeTab === tab.id
                              ? `bg-${tab.color}-50 dark:bg-${tab.color}-900/20 text-${tab.color}-700 dark:text-${tab.color}-300 shadow-sm border border-${tab.color}-200 dark:border-${tab.color}-800`
                              : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-200'
                          }`}
                        >
                          <tab.icon className="w-4 h-4 mr-3 flex-shrink-0 group-hover:scale-110 transition-transform" />
                          <span className="truncate">{tab.label}</span>
                          {activeTab === tab.id && <ChevronRight className="w-4 h-4 ml-auto text-current" />}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* العمود الثالث: الشبكات والتقنيات */}
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Globe className="w-4 h-4 text-cyan-500" />
                      <h4 className="text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">{t('navigation.groups.technical')}</h4>
                    </div>
                    <div className="space-y-1">
                      {[
                        { id: 'networks', label: t('navigation.networks'), icon: Globe, color: 'cyan' },

                        { id: 'contracts', label: t('navigation.contracts'), icon: FileText, color: 'indigo' },

                      ].map((tab) => (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`w-full flex items-center px-3 py-2.5 rounded-lg text-sm font-medium transition-all group ${
                            activeTab === tab.id
                              ? `bg-${tab.color}-50 dark:bg-${tab.color}-900/20 text-${tab.color}-700 dark:text-${tab.color}-300 shadow-sm border border-${tab.color}-200 dark:border-${tab.color}-800`
                              : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-200'
                          }`}
                        >
                          <tab.icon className="w-4 h-4 mr-3 flex-shrink-0 group-hover:scale-110 transition-transform" />
                          <span className="truncate">{tab.label}</span>
                          {activeTab === tab.id && <ChevronRight className="w-4 h-4 ml-auto text-current" />}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* العمود الرابع: المراقبة والأمان */}
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Shield className="w-4 h-4 text-red-500" />
                      <h4 className="text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">{t('navigation.groups.security')}</h4>
                    </div>
                    <div className="space-y-1">
                      {[
                        { id: 'monitoring', label: t('navigation.monitoring'), icon: Activity, color: 'pink' },
                        { id: 'security', label: 'Security', icon: Shield, color: 'red' },

                      ].map((tab) => (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`w-full flex items-center px-3 py-2.5 rounded-lg text-sm font-medium transition-all group ${
                            activeTab === tab.id
                              ? `bg-${tab.color}-50 dark:bg-${tab.color}-900/20 text-${tab.color}-700 dark:text-${tab.color}-300 shadow-sm border border-${tab.color}-200 dark:border-${tab.color}-800`
                              : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-200'
                          }`}
                        >
                          <tab.icon className="w-4 h-4 mr-3 flex-shrink-0 group-hover:scale-110 transition-transform" />
                          <span className="truncate">{tab.label}</span>
                          {activeTab === tab.id && <ChevronRight className="w-4 h-4 ml-auto text-current" />}
                        </button>
                      ))}
                    </div>
                  </div>


                </div>
              </div>
            </div>

            <div className="p-6">
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Quick Actions */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <button className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors group">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <Plus className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      <span className="font-medium text-blue-900 dark:text-blue-300">{t('common.actions.add')} {t('navigation.users')}</span>
                    </div>
                    <ChevronRight className="w-4 h-4 text-blue-600 dark:text-blue-400 group-hover:translate-x-1 transition-transform" />
                  </button>
                  <button className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors group">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <Monitor className="w-5 h-5 text-green-600 dark:text-green-400" />
                      <span className="font-medium text-green-900 dark:text-green-300">{t('monitoring.systemHealth')}</span>
                    </div>
                    <ChevronRight className="w-4 h-4 text-green-600 dark:text-green-400 group-hover:translate-x-1 transition-transform" />
                  </button>
                  <button className="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800 hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors group">
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <FileText className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                      <span className="font-medium text-purple-900 dark:text-purple-300">{t('contracts.deployment')}</span>
                    </div>
                    <ChevronRight className="w-4 h-4 text-purple-600 dark:text-purple-400 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>

                {platformStats ? (
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {/* Trading Volume Card */}
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="font-semibold text-blue-900 dark:text-blue-300">{t('overview.tradingVolume')}</h3>
                        <TrendingUp className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-blue-700 dark:text-blue-400 text-sm">{t('overview.total')}:</span>
                          <span className="font-bold text-blue-900 dark:text-blue-200 text-lg">
                            ${((platformStats?.totalVolume || 0) / 1000000).toFixed(1)}M
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-blue-700 dark:text-blue-400 text-sm">{t('overview.thisMonth')}:</span>
                          <span className="font-bold text-blue-900 dark:text-blue-200">
                            ${(((platformStats?.totalVolume || 0) * 0.3) / 1000000).toFixed(1)}M
                          </span>
                        </div>
                        <div className="mt-3 bg-blue-200 dark:bg-blue-800 rounded-full h-2">
                          <div className="bg-blue-600 dark:bg-blue-400 h-2 rounded-full" style={{ width: '75%' }}></div>
                        </div>
                      </div>
                    </div>

                    {/* Platform Fees Card */}
                    <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl p-6 border border-green-200 dark:border-green-800">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="font-semibold text-green-900 dark:text-green-300">{t('overview.platformFees')}</h3>
                        <DollarSign className="w-5 h-5 text-green-600 dark:text-green-400" />
                      </div>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-green-700 dark:text-green-400 text-sm">{t('overview.total')}:</span>
                          <span className="font-bold text-green-900 dark:text-green-200 text-lg">
                            ${((platformStats?.platformRevenue || 0) / 1000).toFixed(0)}K
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-green-700 dark:text-green-400 text-sm">{t('overview.thisMonth')}:</span>
                          <span className="font-bold text-green-900 dark:text-green-200">
                            ${(((platformStats?.platformRevenue || 0) * 0.4) / 1000).toFixed(0)}K
                          </span>
                        </div>
                        <div className="mt-3 bg-green-200 dark:bg-green-800 rounded-full h-2">
                          <div className="bg-green-600 dark:bg-green-400 h-2 rounded-full" style={{ width: '60%' }}></div>
                        </div>
                      </div>
                    </div>

                    {/* System Health Card */}
                    <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl p-6 border border-purple-200 dark:border-purple-800">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="font-semibold text-purple-900 dark:text-purple-300">{t('monitoring.systemHealth')}</h3>
                        <Activity className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                      </div>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-purple-700 dark:text-purple-400 text-sm">{t('monitoring.metrics.uptime')}:</span>
                          <span className="font-bold text-purple-900 dark:text-purple-200 text-lg">99.9%</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-purple-700 dark:text-purple-400 text-sm">{t('monitoring.metrics.responseTime')}:</span>
                          <span className="font-bold text-purple-900 dark:text-purple-200">120ms</span>
                        </div>
                        <div className="mt-3 bg-purple-200 dark:bg-purple-800 rounded-full h-2">
                          <div className="bg-purple-600 dark:bg-purple-400 h-2 rounded-full" style={{ width: '95%' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="bg-gray-100 dark:bg-gray-700 rounded-xl p-6 animate-pulse">
                        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-4"></div>
                        <div className="space-y-2">
                          <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded"></div>
                          <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-5/6"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Recent Activity */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{t('overview.recentActivity')}</h3>
                    <button className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium">
                      {t('common.actions.view')} {t('common.labels.total')}
                    </button>
                  </div>
                  <div className="space-y-3">
                    {/* عرض النشاط من adminStats إذا كان متوفراً */}
                    {adminStats?.recentActivity ? (
                      <>
                        {/* الصفقات المكتملة */}
                        {adminStats.recentActivity.completedTrades?.slice(0, 1)?.map((trade: any, index: number) => (
                          <div key={`completed-${index}`} className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <CheckCircle className="w-5 h-5 text-success-500 ml-3" />
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">تم إكمال صفقة {trade.id}</p>
                              <p className="text-sm text-gray-600 dark:text-gray-300">منذ {Math.floor(Math.random() * 30 + 5)} دقيقة</p>
                            </div>
                          </div>
                        ))}

                        {/* المستخدمين الجدد */}
                        {adminStats.recentActivity.newUsers?.slice(0, 1)?.map((user: any, index: number) => (
                          <div key={`user-${index}`} className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <Users className="w-5 h-5 text-primary-500 ml-3" />
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">انضم مستخدم جديد: {user.fullName || 'مستخدم جديد'}</p>
                              <p className="text-sm text-gray-600 dark:text-gray-300">منذ {Math.floor(Math.random() * 60 + 10)} دقيقة</p>
                            </div>
                          </div>
                        ))}

                        {/* النزاعات */}
                        {adminStats.recentActivity.disputes?.slice(0, 1)?.map((trade: any, index: number) => (
                          <div key={`dispute-${index}`} className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <AlertTriangle className="w-5 h-5 text-warning-500 ml-3" />
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">تم الإبلاغ عن نزاع في صفقة {trade.id}</p>
                              <p className="text-sm text-gray-600 dark:text-gray-300">منذ {Math.floor(Math.random() * 120 + 30)} دقيقة</p>
                            </div>
                          </div>
                        ))}
                      </>
                    ) : (
                      <>
                        {/* عرض آخر الصفقات المكتملة */}
                        {trades?.filter(t => t.status === 'COMPLETED')?.slice(0, 1)?.map((trade, index) => (
                          <div key={`completed-${index}`} className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <CheckCircle className="w-5 h-5 text-success-500 ml-3" />
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">تم إكمال صفقة {trade.id}</p>
                              <p className="text-sm text-gray-600 dark:text-gray-300">منذ 5 دقائق</p>
                            </div>
                          </div>
                        ))}

                        {/* عرض آخر المستخدمين المنضمين */}
                        {users?.slice(0, 1)?.map((user, index) => (
                          <div key={`user-${index}`} className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <Users className="w-5 h-5 text-primary-500 ml-3" />
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">انضم مستخدم جديد: {user.fullName || 'مستخدم جديد'}</p>
                              <p className="text-sm text-gray-600 dark:text-gray-300">منذ 15 دقيقة</p>
                            </div>
                          </div>
                        ))}

                        {/* عرض النزاعات المعلقة */}
                        {trades?.filter(t => t.status === 'DISPUTED')?.slice(0, 1)?.map((trade, index) => (
                          <div key={`dispute-${index}`} className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <AlertTriangle className="w-5 h-5 text-warning-500 ml-3" />
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">تم الإبلاغ عن نزاع في صفقة {trade.id}</p>
                              <p className="text-sm text-gray-600 dark:text-gray-300">منذ 30 دقيقة</p>
                            </div>
                          </div>
                        ))}
                      </>
                    )}

                    {/* رسالة في حالة عدم وجود نشاط */}
                    {(!adminStats?.recentActivity && (!trades || trades.length === 0) && (!users || users.length === 0)) && (
                      <div className="text-center py-8">
                        <p className="text-gray-500 dark:text-gray-400">لا يوجد نشاط حديث</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Enhanced Users Management Tab */}
            {activeTab === 'users' && (
              <AdvancedUserManagement />
            )}



            {/* Enhanced Smart Contracts Management Tab */}
            {activeTab === 'contracts' && (
              <EnhancedSmartContractManager />
            )}

            {/* Enhanced Trades Management Tab */}
            {activeTab === 'trades' && (
              <AdvancedTradeManagement />
            )}



            {/* Enhanced Analytics Tab */}
            {activeTab === 'analytics' && (
              <AnalyticsCharts />
            )}



            {/* Enhanced System Monitoring Tab */}
            {activeTab === 'monitoring' && (
              <SystemMonitoring />
            )}



            {/* Enhanced Networks Management Tab */}
            {activeTab === 'networks' && (
              <NetworkTokenManagement
                isWalletConnected={isWalletConnected}
                walletAddress={isWalletConnected ? 'Connected' : ''}
                currentNetwork={isWalletConnected ? 'BSC Testnet' : ''}
              />
            )}





            {/* Enhanced Notification System Tab */}
            {activeTab === 'notifications' && (
              <NotificationSystem />
            )}





            {/* تبويب إدارة العقود - مدمج في SmartContractManager */}





            {/* Enhanced Dispute Management Tab */}
            {(activeTab === 'dispute-management' || activeTab === 'disputes') && <AdvancedDisputeManagement />}







            {/* Enhanced Admin Settings Tab */}
            {activeTab === 'settings' && (
              <AdminSettings />
            )}



            {/* Enhanced Security Tab */}
            {activeTab === 'security' && (
              <AdminSecurity />
            )}

            {/* Subscription Management Tab */}
            {activeTab === 'subscriptions' && (
              <SubscriptionManagement />
            )}
          </div>
        </div>
      </main>

      {/* فوتر حديث */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-auto">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center text-sm text-gray-600 dark:text-gray-400">
            <div>
              © 2024 إيكاروس P2P - جميع الحقوق محفوظة
            </div>
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <span>الإصدار: 1.0.0</span>
              <span>•</span>
              <span>آخر تحديث: {new Date().toLocaleDateString('ar-SA')}</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
