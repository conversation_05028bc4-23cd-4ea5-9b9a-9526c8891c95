'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON>et,
  Eye,
  EyeOff,
  TrendingUp,
  TrendingDown,
  ArrowUpRight,
  ArrowDownLeft,
  Plus,
  Minus,
  RefreshCw,
  Lock,
  Unlock,
  ArrowRight,
  AlertCircle
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet, handleApiError } from '@/utils/apiClient';

interface WalletBalance {
  currency: string;
  symbol: string;
  available: number;
  locked: number;
  total: number;
  usdValue: number;
  change24h: number;
  icon?: string;
}

interface RecentTransaction {
  id: string;
  type: 'deposit' | 'withdraw' | 'trade' | 'fee';
  amount: number;
  currency: string;
  status: 'completed' | 'pending' | 'failed';
  timestamp: string;
  description: string;
}

export default function WalletOverviewWidget() {
  const { t, formatCurrency, formatDate } = useUserDashboardTranslation();
  const { user } = useAuth();
  const [balances, setBalances] = useState<WalletBalance[]>([]);
  const [recentTransactions, setRecentTransactions] = useState<RecentTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showBalances, setShowBalances] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // جلب بيانات المحفظة
  useEffect(() => {
    const fetchWalletData = async () => {
      if (!user?.id) {
        setError('معرف المستخدم غير متاح');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // جلب أرصدة المحفظة من API
        const balanceResponse = await apiGet(`wallet/balance.php?user_id=${user.id}`);

        if (!balanceResponse.success) {
          throw new Error(balanceResponse.message || 'فشل في جلب أرصدة المحفظة');
        }

        // تحويل البيانات من API إلى تنسيق WalletBalance
        const apiBalances = balanceResponse.data || [];
        const formattedBalances: WalletBalance[] = apiBalances.map((balance: any) => ({
          currency: balance.currency || 'USDT',
          symbol: balance.symbol || balance.currency,
          available: parseFloat(balance.available) || 0,
          locked: parseFloat(balance.locked) || 0,
          total: parseFloat(balance.total) || 0,
          usdValue: parseFloat(balance.usd_value) || 0,
          change24h: parseFloat(balance.change_24h) || 0
        }));

        setBalances(formattedBalances);

        // جلب المعاملات الحديثة من API
        const transactionsResponse = await apiGet(`transactions/index.php?user_id=${user.id}&limit=5`);

        if (transactionsResponse.success && transactionsResponse.data?.transactions) {
          const apiTransactions = transactionsResponse.data.transactions || [];
          const formattedTransactions: RecentTransaction[] = apiTransactions.map((transaction: any) => ({
            id: transaction.id.toString(),
            type: mapTransactionType(transaction.type),
            amount: parseFloat(transaction.amount) || 0,
            currency: transaction.currency || 'USDT',
            status: mapTransactionStatus(transaction.status),
            timestamp: transaction.timestamp || new Date().toISOString(),
            description: transaction.description || 'معاملة'
          }));

          setRecentTransactions(formattedTransactions);
        } else {
          setRecentTransactions([]);
        }

      } catch (err) {
        const errorMessage = handleApiError(err);
        setError(errorMessage);
        console.error('Error fetching wallet data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchWalletData();
  }, [user?.id]);

  // دالة للحصول على رمز العملة
  const getCurrencySymbol = (currency: string): string => {
    switch (currency?.toUpperCase()) {
      case 'USDT':
      case 'USDC':
        return currency.toUpperCase();
      case 'SAR':
        return 'ر.س';
      case 'AED':
        return 'د.إ';
      case 'USD':
        return '$';
      case 'EUR':
        return '€';
      default:
        return currency || '';
    }
  };

  // دالة لتحويل نوع المعاملة من API
  const mapTransactionType = (apiType: string): RecentTransaction['type'] => {
    switch (apiType?.toLowerCase()) {
      case 'deposit':
        return 'deposit';
      case 'withdraw':
        return 'withdraw';
      case 'trade':
        return 'trade';
      case 'fee':
        return 'fee';
      default:
        return 'trade';
    }
  };

  // دالة لتحويل حالة المعاملة من API
  const mapTransactionStatus = (apiStatus: string): RecentTransaction['status'] => {
    switch (apiStatus?.toLowerCase()) {
      case 'completed':
        return 'completed';
      case 'pending':
        return 'pending';
      case 'failed':
        return 'failed';
      default:
        return 'pending';
    }
  };

  // تحديث البيانات
  const handleRefresh = async () => {
    if (!user?.id) return;

    setRefreshing(true);
    try {
      // إعادة جلب البيانات
      const balanceResponse = await apiGet(`wallet/balance.php?user_id=${user.id}`);
      const transactionsResponse = await apiGet(`transactions/index.php?user_id=${user.id}&limit=5`);

      if (balanceResponse.success) {
        const apiBalances = balanceResponse.data || [];
        const formattedBalances: WalletBalance[] = apiBalances.map((balance: any) => ({
          currency: balance.currency || 'USDT',
          symbol: balance.symbol || balance.currency,
          available: parseFloat(balance.available) || 0,
          locked: parseFloat(balance.locked) || 0,
          total: parseFloat(balance.total) || 0,
          usdValue: parseFloat(balance.usd_value) || 0,
          change24h: parseFloat(balance.change_24h) || 0
        }));
        setBalances(formattedBalances);
      }

      if (transactionsResponse.success) {
        const apiTransactions = transactionsResponse.data || [];
        const formattedTransactions: RecentTransaction[] = apiTransactions.map((transaction: any) => ({
          id: transaction.id.toString(),
          type: mapTransactionType(transaction.transaction_type),
          amount: parseFloat(transaction.amount) || 0,
          currency: transaction.currency || 'USDT',
          status: mapTransactionStatus(transaction.status),
          timestamp: transaction.created_at || new Date().toISOString(),
          description: transaction.description || 'معاملة'
        }));
        setRecentTransactions(formattedTransactions);
      }
    } catch (err) {
      console.error('Error refreshing wallet data:', err);
    } finally {
      setRefreshing(false);
    }
  };

  // حساب إجمالي القيمة بالدولار
  const totalUsdValue = balances.reduce((sum, balance) => sum + balance.usdValue, 0);

  // الحصول على أيقونة نوع المعاملة
  const getTransactionIcon = (type: RecentTransaction['type']) => {
    switch (type) {
      case 'deposit':
        return <ArrowDownLeft className="w-4 h-4 text-green-500" />;
      case 'withdraw':
        return <ArrowUpRight className="w-4 h-4 text-red-500" />;
      case 'trade':
        return <RefreshCw className="w-4 h-4 text-blue-500" />;
      case 'fee':
        return <Minus className="w-4 h-4 text-orange-500" />;
      default:
        return <RefreshCw className="w-4 h-4 text-gray-500" />;
    }
  };

  // الحصول على لون حالة المعاملة
  const getStatusColor = (status: RecentTransaction['status']) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 dark:text-green-400';
      case 'pending':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'failed':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  // عرض رسالة الخطأ
  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3 text-red-600 dark:text-red-400">
          <AlertCircle className="w-5 h-5" />
          <div>
            <h3 className="font-medium">خطأ في تحميل بيانات المحفظة</h3>
            <p className="text-sm mt-1">{error}</p>
          </div>
        </div>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
        >
          إعادة المحاولة
        </button>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="animate-pulse">
          <div className="h-6 w-32 bg-gray-200 dark:bg-gray-700 rounded mb-4" />
          <div className="h-8 w-24 bg-gray-200 dark:bg-gray-700 rounded mb-6" />
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-12 bg-gray-200 dark:bg-gray-700 rounded" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          نظرة عامة على المحفظة
        </h3>
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <button
            onClick={() => setShowBalances(!showBalances)}
            className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
            title={showBalances ? 'إخفاء الأرصدة' : 'إظهار الأرصدة'}
          >
            {showBalances ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </button>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors disabled:opacity-50"
            title="تحديث"
          >
            <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* إجمالي القيمة */}
      <div className="mb-6">
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">إجمالي القيمة</p>
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {showBalances ? formatCurrency(totalUsdValue, 'USD') : '****'}
          </p>
          <div className="flex items-center space-x-1 rtl:space-x-reverse text-sm">
            <TrendingUp className="w-4 h-4 text-green-500" />
            <span className="text-green-600 dark:text-green-400 font-medium">+5.2%</span>
          </div>
        </div>
      </div>

      {/* الأرصدة */}
      <div className="space-y-3 mb-6">
        {balances.map((balance) => (
          <div key={balance.currency} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center">
                <span className="text-sm font-bold text-blue-600 dark:text-blue-400">
                  {balance.currency.charAt(0)}
                </span>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {balance.currency}
                </p>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    متاح: {showBalances ? formatCurrency(balance.available, balance.currency) : '****'}
                  </span>
                  {balance.locked > 0 && (
                    <>
                      <Lock className="w-3 h-3 text-orange-500" />
                      <span className="text-xs text-orange-600 dark:text-orange-400">
                        {showBalances ? formatCurrency(balance.locked, balance.currency) : '****'}
                      </span>
                    </>
                  )}
                </div>
              </div>
            </div>
            
            <div className="text-right">
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {showBalances ? formatCurrency(balance.total, balance.currency) : '****'}
              </p>
              <div className="flex items-center space-x-1 rtl:space-x-reverse">
                {balance.change24h > 0 ? (
                  <TrendingUp className="w-3 h-3 text-green-500" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-red-500" />
                )}
                <span className={`text-xs ${
                  balance.change24h > 0 
                    ? 'text-green-600 dark:text-green-400' 
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {balance.change24h > 0 ? '+' : ''}{balance.change24h}%
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* المعاملات الأخيرة */}
      <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
            المعاملات الأخيرة
          </h4>
          <a 
            href="/user-dashboard/wallet/transactions" 
            className="text-xs text-blue-600 dark:text-blue-400 hover:underline flex items-center space-x-1 rtl:space-x-reverse"
          >
            <span>عرض الكل</span>
            <ArrowRight className="w-3 h-3" />
          </a>
        </div>
        
        <div className="space-y-2">
          {recentTransactions.slice(0, 3).map((transaction) => (
            <div key={transaction.id} className="flex items-center justify-between">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                {getTransactionIcon(transaction.type)}
                <div>
                  <p className="text-xs font-medium text-gray-900 dark:text-white">
                    {transaction.description}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {formatDate(transaction.timestamp, { 
                      hour: '2-digit', 
                      minute: '2-digit',
                      day: 'numeric',
                      month: 'short'
                    })}
                  </p>
                </div>
              </div>
              
              <div className="text-right">
                <p className={`text-xs font-medium ${
                  transaction.type === 'deposit' || transaction.type === 'trade' 
                    ? 'text-green-600 dark:text-green-400' 
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {transaction.type === 'deposit' || transaction.type === 'trade' ? '+' : '-'}
                  {formatCurrency(transaction.amount, transaction.currency)}
                </p>
                <p className={`text-xs ${getStatusColor(transaction.status)}`}>
                  {transaction.status === 'completed' ? 'مكتمل' :
                   transaction.status === 'pending' ? 'قيد الانتظار' : 'فشل'}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* أزرار سريعة */}
      <div className="grid grid-cols-2 gap-3 mt-4">
        <a
          href="/user-dashboard/wallet?action=deposit"
          className="flex items-center justify-center space-x-2 rtl:space-x-reverse bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 py-2 px-3 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span className="text-sm font-medium">إيداع</span>
        </a>
        <a
          href="/user-dashboard/wallet?action=withdraw"
          className="flex items-center justify-center space-x-2 rtl:space-x-reverse bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 py-2 px-3 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors"
        >
          <Minus className="w-4 h-4" />
          <span className="text-sm font-medium">سحب</span>
        </a>
      </div>
    </div>
  );
}
