<?php
/**
 * API endpoint لحذف الملفات
 * File Delete API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";
require_once __DIR__ . "/../middleware/auth.php";

// التحقق من طريقة الطلب
validateRequestMethod(['DELETE', 'POST']);

/**
 * التحقق من صلاحية حذف الملف
 */
function canDeleteFile($fileData, $userId, $isAdmin) {
    // المدراء يمكنهم حذف جميع الملفات
    if ($isAdmin) {
        return true;
    }
    
    // صاحب الملف يمكنه حذفه
    if ($fileData['user_id'] == $userId) {
        return true;
    }
    
    return false;
}

/**
 * حذف الملف من النظام
 */
function deleteFileFromSystem($filePath) {
    if (file_exists($filePath)) {
        return unlink($filePath);
    }
    return true; // الملف غير موجود أصلاً
}

try {
    // التحقق من المصادقة
    $auth = requireAuth();
    $userId = $auth->getCurrentUserId();
    $isAdmin = $auth->isAdmin();
    
    if (!$userId) {
        sendErrorResponse('المصادقة مطلوبة', 401);
    }
    
    // الحصول على معرف الملف
    $fileId = null;
    
    if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        $fileId = $_GET['id'] ?? '';
    } else { // POST
        $input = json_decode(file_get_contents('php://input'), true);
        $fileId = $input['fileId'] ?? $_POST['fileId'] ?? '';
    }
    
    if (empty($fileId) || !is_numeric($fileId)) {
        sendErrorResponse('معرف الملف مطلوب ويجب أن يكون رقماً');
    }
    
    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // البحث عن الملف
    $stmt = $connection->prepare("
        SELECT id, user_id, original_name, safe_filename, file_path, 
               file_size, file_type, mime_type, description, related_id,
               created_at, download_count
        FROM uploaded_files 
        WHERE id = ?
    ");
    
    $stmt->execute([$fileId]);
    $fileData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$fileData) {
        sendErrorResponse('الملف غير موجود', 404);
    }
    
    // التحقق من صلاحية الحذف
    if (!canDeleteFile($fileData, $userId, $isAdmin)) {
        sendErrorResponse('ليس لديك صلاحية لحذف هذا الملف', 403);
    }
    
    // بدء المعاملة
    $connection->beginTransaction();
    
    try {
        // حذف سجل الملف من قاعدة البيانات
        $deleteStmt = $connection->prepare("DELETE FROM uploaded_files WHERE id = ?");
        $deleteStmt->execute([$fileId]);
        
        // حذف الملف من النظام
        $fileDeleted = deleteFileFromSystem($fileData['file_path']);
        
        if (!$fileDeleted) {
            // إذا فشل حذف الملف، نسجل تحذيراً لكن نكمل العملية
            error_log('Warning: Failed to delete physical file: ' . $fileData['file_path']);
        }
        
        // تسجيل النشاط
        try {
            $checkTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
            $checkTable->execute();
            
            if ($checkTable->rowCount() > 0) {
                $checkColumns = $connection->prepare("SHOW COLUMNS FROM activity_logs LIKE 'entity_type'");
                $checkColumns->execute();
                
                if ($checkColumns->rowCount() > 0) {
                    $logStmt = $connection->prepare("
                        INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data)
                        VALUES (?, 'file_deleted', 'file', ?, ?, ?, ?)
                    ");
                    
                    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                    $deleteData = json_encode([
                        'delete_time' => date('Y-m-d H:i:s'),
                        'original_name' => $fileData['original_name'],
                        'file_size' => $fileData['file_size'],
                        'file_type' => $fileData['file_type'],
                        'file_owner_id' => $fileData['user_id'],
                        'deleted_by_admin' => $isAdmin && $fileData['user_id'] != $userId,
                        'physical_file_deleted' => $fileDeleted
                    ]);
                    
                    $logStmt->execute([$userId, $fileId, $ipAddress, $userAgent, $deleteData]);
                }
            }
        } catch (Exception $logError) {
            // تجاهل أخطاء تسجيل النشاط
            error_log('Activity log error in delete: ' . $logError->getMessage());
        }
        
        // تأكيد المعاملة
        $connection->commit();
        
        sendSuccessResponse([
            'message' => 'تم حذف الملف بنجاح',
            'fileId' => $fileId,
            'originalName' => $fileData['original_name'],
            'physicalFileDeleted' => $fileDeleted,
            'timestamp' => date('Y-m-d H:i:s')
        ], 'تم حذف الملف بنجاح');
        
    } catch (Exception $e) {
        // التراجع عن المعاملة
        $connection->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    error_log('Database error in delete.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}
?>
