// خدمة التفاعل مع العقود الذكية المحسنة
import { ethers } from 'ethers';
import {
  ENHANCED_CONTRACT_ADDRESSES,
  ENHANCED_CONTRACT_ABIS,
  ENHANCED_GAS_LIMITS,
  ENHANCED_SUPPORTED_TOKENS,
  getEnhancedContractAddresses,
  getSupportedTokens,
  getTokenBySymbol,
  getContractAddress,
  getContractABI
} from '@/constants';
import { networkService } from './networkService';

// أنواع البيانات
export interface EnhancedTrade {
  id: number;
  seller: string;
  buyer: string;
  token: string;
  amount: string;
  pricePerToken: string;
  currency: string;
  status: number;
  createdAt: number;
  lastActivity: number;
}

export interface TokenInfo {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  isSupported: boolean;
}

export interface NetworkInfo {
  id: number;
  name: string;
  symbol: string;
  chainId: number;
  isTestnet: boolean;
}

export interface ContractAddresses {
  coreEscrow: string;
  reputationManager: string;
  oracleManager: string;
  adminManager: string;
  escrowIntegrator: string;
}

class EnhancedContractService {
  private provider: ethers.BrowserProvider | null = null;
  private signer: ethers.Signer | null = null;
  private coreEscrowContract: ethers.Contract | null = null;
  private reputationManagerContract: ethers.Contract | null = null;
  private oracleManagerContract: ethers.Contract | null = null;
  private adminManagerContract: ethers.Contract | null = null;
  private escrowIntegratorContract: ethers.Contract | null = null;
  private tokenContracts: Map<string, ethers.Contract> = new Map();
  private currentNetwork: NetworkInfo | null = null;
  private isInitialized = false;

  constructor() {
    this.initializeProvider();
  }

  /**
   * تهيئة المزود
   */
  private async initializeProvider() {
    if (typeof window !== 'undefined' && window.ethereum) {
      try {
        this.provider = new ethers.BrowserProvider(window.ethereum);
        await this.setupContracts();
        this.isInitialized = true;
        console.log('✅ تم تهيئة خدمة العقود المحسنة بنجاح');
      } catch (error) {
        console.error('❌ خطأ في تهيئة المزود:', error);
      }
    }
  }

  /**
   * إعداد العقود
   */
  private async setupContracts() {
    if (!this.provider) return;

    try {
      // الحصول على معلومات الشبكة الحالية
      const network = await this.provider.getNetwork();
      const isTestnet = Number(network.chainId) === 97; // BSC Testnet
      
      this.currentNetwork = {
        id: isTestnet ? 1 : 2,
        name: isTestnet ? 'BSC Testnet' : 'BSC Mainnet',
        symbol: isTestnet ? 'tBNB' : 'BNB',
        chainId: Number(network.chainId),
        isTestnet
      };

      // الحصول على عناوين العقود
      const contracts = getEnhancedContractAddresses(isTestnet);

      // إنشاء عقود القراءة فقط
      if (contracts.CORE_ESCROW && ethers.isAddress(contracts.CORE_ESCROW)) {
        this.coreEscrowContract = new ethers.Contract(
          contracts.CORE_ESCROW,
          getContractABI('CORE_ESCROW'),
          this.provider
        );
      }

      if (contracts.REPUTATION_MANAGER && ethers.isAddress(contracts.REPUTATION_MANAGER)) {
        this.reputationManagerContract = new ethers.Contract(
          contracts.REPUTATION_MANAGER,
          getContractABI('REPUTATION_MANAGER'),
          this.provider
        );
      }

      if (contracts.ORACLE_MANAGER && ethers.isAddress(contracts.ORACLE_MANAGER)) {
        this.oracleManagerContract = new ethers.Contract(
          contracts.ORACLE_MANAGER,
          getContractABI('ORACLE_MANAGER'),
          this.provider
        );
      }

      if (contracts.ADMIN_MANAGER && ethers.isAddress(contracts.ADMIN_MANAGER)) {
        this.adminManagerContract = new ethers.Contract(
          contracts.ADMIN_MANAGER,
          getContractABI('ADMIN_MANAGER'),
          this.provider
        );
      }

      if (contracts.ESCROW_INTEGRATOR && ethers.isAddress(contracts.ESCROW_INTEGRATOR)) {
        this.escrowIntegratorContract = new ethers.Contract(
          contracts.ESCROW_INTEGRATOR,
          getContractABI('ESCROW_INTEGRATOR'),
          this.provider
        );
      }

      // إعداد عقود التوكنات
      await this.setupTokenContracts(isTestnet);

      console.log('✅ تم إعداد العقود المحسنة بنجاح');
    } catch (error) {
      console.error('❌ خطأ في إعداد العقود:', error);
    }
  }

  /**
   * إعداد عقود التوكنات
   */
  private async setupTokenContracts(isTestnet: boolean) {
    const tokens = getSupportedTokens(isTestnet);
    
    for (const [symbol, tokenInfo] of Object.entries(tokens)) {
      if (tokenInfo.address && ethers.isAddress(tokenInfo.address)) {
        try {
          const tokenContract = new ethers.Contract(
            tokenInfo.address,
            getContractABI('ERC20'),
            this.provider
          );
          this.tokenContracts.set(symbol, tokenContract);
          this.tokenContracts.set(tokenInfo.address.toLowerCase(), tokenContract);
        } catch (error) {
          console.error(`❌ خطأ في إعداد عقد التوكن ${symbol}:`, error);
        }
      }
    }
  }

  /**
   * الاتصال بالمحفظة
   */
  async connect(): Promise<void> {
    if (typeof window === 'undefined' || !window.ethereum) {
      throw new Error('محفظة MetaMask غير مثبتة');
    }

    try {
      // طلب الاتصال بالمحفظة
      await window.ethereum.request({ method: 'eth_requestAccounts' });

      // تهيئة المزود إذا لم يكن موجوداً
      if (!this.provider) {
        this.provider = new ethers.BrowserProvider(window.ethereum);
      }

      // الحصول على signer للكتابة
      this.signer = await this.provider.getSigner();

      // إعادة إعداد العقود مع signer
      await this.setupContractsWithSigner();

      console.log('✅ تم الاتصال بالعقود المحسنة بنجاح');
    } catch (error) {
      console.error('❌ خطأ في الاتصال بالعقود المحسنة:', error);
      throw error;
    }
  }

  /**
   * إعداد العقود مع signer للكتابة
   */
  private async setupContractsWithSigner() {
    if (!this.provider || !this.signer) return;

    try {
      const network = await this.provider.getNetwork();
      const isTestnet = Number(network.chainId) === 97;
      const contracts = getEnhancedContractAddresses(isTestnet);

      // إعداد العقود مع signer للكتابة
      if (contracts.CORE_ESCROW && this.coreEscrowContract) {
        this.coreEscrowContract = this.coreEscrowContract.connect(this.signer) as any;
      }

      if (contracts.REPUTATION_MANAGER && this.reputationManagerContract) {
        this.reputationManagerContract = this.reputationManagerContract.connect(this.signer) as any;
      }

      if (contracts.ORACLE_MANAGER && this.oracleManagerContract) {
        this.oracleManagerContract = this.oracleManagerContract.connect(this.signer) as any;
      }

      if (contracts.ADMIN_MANAGER && this.adminManagerContract) {
        this.adminManagerContract = this.adminManagerContract.connect(this.signer) as any;
      }

      if (contracts.ESCROW_INTEGRATOR && this.escrowIntegratorContract) {
        this.escrowIntegratorContract = this.escrowIntegratorContract.connect(this.signer) as any;
      }

      // إعداد عقود التوكنات مع signer
      for (const [key, contract] of this.tokenContracts.entries()) {
        this.tokenContracts.set(key, contract.connect(this.signer) as any);
      }

    } catch (error) {
      console.error('❌ خطأ في إعداد العقود مع signer:', error);
    }
  }



  /**
   * التحقق من دعم التوكن
   */
  async isTokenSupported(tokenAddress: string): Promise<boolean> {
    if (!this.coreEscrowContract) {
      throw new Error('العقد الأساسي غير متصل');
    }

    try {
      return await this.coreEscrowContract.isTokenSupported(tokenAddress);
    } catch (error) {
      console.error('خطأ في التحقق من دعم التوكن:', error);
      return false;
    }
  }

  /**
   * إنشاء صفقة جديدة
   */
  async createTrade(
    tokenAddress: string,
    amount: string,
    pricePerToken: string,
    currency: string
  ): Promise<string> {
    if (!this.coreEscrowContract || !this.signer) {
      throw new Error('العقد غير متصل أو المحفظة غير متصلة');
    }

    try {
      // التحقق من دعم التوكن
      const isSupported = await this.isTokenSupported(tokenAddress);
      if (!isSupported) {
        throw new Error('التوكن غير مدعوم');
      }

      // تحويل القيم إلى wei
      const amountWei = ethers.parseUnits(amount, 18);
      const priceWei = ethers.parseUnits(pricePerToken, 18);

      // تنفيذ المعاملة
      const tx = await this.coreEscrowContract.createTrade(
        tokenAddress,
        amountWei,
        priceWei,
        currency,
        {
          gasLimit: ENHANCED_GAS_LIMITS.CREATE_TRADE
        }
      );

      console.log('✅ تم إرسال معاملة إنشاء الصفقة:', tx.hash);
      return tx.hash;

    } catch (error) {
      console.error('❌ خطأ في إنشاء الصفقة:', error);
      throw error;
    }
  }

  /**
   * الانضمام لصفقة
   */
  async joinTrade(tradeId: number): Promise<string> {
    if (!this.coreEscrowContract || !this.signer) {
      throw new Error('العقد غير متصل أو المحفظة غير متصلة');
    }

    try {
      const tx = await this.coreEscrowContract.joinTrade(tradeId, {
        gasLimit: ENHANCED_GAS_LIMITS.JOIN_TRADE
      });

      console.log('✅ تم إرسال معاملة الانضمام للصفقة:', tx.hash);
      return tx.hash;

    } catch (error) {
      console.error('❌ خطأ في الانضمام للصفقة:', error);
      throw error;
    }
  }

  /**
   * تأكيد إرسال الدفع
   */
  async confirmPaymentSent(tradeId: number): Promise<string> {
    if (!this.coreEscrowContract || !this.signer) {
      throw new Error('العقد غير متصل أو المحفظة غير متصلة');
    }

    try {
      const tx = await this.coreEscrowContract.confirmPaymentSent(tradeId, {
        gasLimit: ENHANCED_GAS_LIMITS.CONFIRM_PAYMENT_SENT
      });

      console.log('✅ تم إرسال معاملة تأكيد الدفع:', tx.hash);
      return tx.hash;

    } catch (error) {
      console.error('❌ خطأ في تأكيد إرسال الدفع:', error);
      throw error;
    }
  }

  /**
   * تأكيد استلام الدفع
   */
  async confirmPaymentReceived(tradeId: number): Promise<string> {
    if (!this.coreEscrowContract || !this.signer) {
      throw new Error('العقد غير متصل أو المحفظة غير متصلة');
    }

    try {
      const tx = await this.coreEscrowContract.confirmPaymentReceived(tradeId, {
        gasLimit: ENHANCED_GAS_LIMITS.CONFIRM_PAYMENT_RECEIVED
      });

      console.log('✅ تم إرسال معاملة تأكيد الاستلام:', tx.hash);
      return tx.hash;

    } catch (error) {
      console.error('❌ خطأ في تأكيد استلام الدفع:', error);
      throw error;
    }
  }

  /**
   * طلب نزاع
   */
  async requestDispute(tradeId: number): Promise<string> {
    if (!this.coreEscrowContract || !this.signer) {
      throw new Error('العقد غير متصل أو المحفظة غير متصلة');
    }

    try {
      const tx = await this.coreEscrowContract.requestDispute(tradeId, {
        gasLimit: ENHANCED_GAS_LIMITS.REQUEST_DISPUTE
      });

      console.log('✅ تم إرسال معاملة طلب النزاع:', tx.hash);
      return tx.hash;

    } catch (error) {
      console.error('❌ خطأ في طلب النزاع:', error);
      throw error;
    }
  }

  /**
   * جلب تفاصيل الصفقة
   */
  async getTrade(tradeId: number): Promise<EnhancedTrade | null> {
    if (!this.coreEscrowContract) {
      throw new Error('العقد الأساسي غير متصل');
    }

    try {
      const trade = await this.coreEscrowContract.getTrade(tradeId);
      
      return {
        id: Number(trade.id),
        seller: trade.seller,
        buyer: trade.buyer,
        token: trade.token,
        amount: ethers.formatUnits(trade.amount, 18),
        pricePerToken: ethers.formatUnits(trade.pricePerToken, 18),
        currency: trade.currency,
        status: Number(trade.status),
        createdAt: Number(trade.createdAt),
        lastActivity: Number(trade.lastActivity)
      };

    } catch (error) {
      console.error('❌ خطأ في جلب تفاصيل الصفقة:', error);
      return null;
    }
  }

  /**
   * جلب معلومات التوكن
   */
  async getTokenInfo(tokenAddress: string): Promise<TokenInfo | null> {
    const contract = this.tokenContracts.get(tokenAddress.toLowerCase());
    
    if (!contract) {
      return null;
    }

    try {
      const [symbol, name, decimals] = await Promise.all([
        contract.symbol(),
        contract.name(),
        contract.decimals()
      ]);

      const isSupported = await this.isTokenSupported(tokenAddress);

      return {
        address: tokenAddress,
        symbol,
        name,
        decimals: Number(decimals),
        isSupported
      };

    } catch (error) {
      console.error('❌ خطأ في جلب معلومات التوكن:', error);
      return null;
    }
  }

  /**
   * جلب رصيد التوكن
   */
  async getTokenBalance(tokenAddress: string, userAddress: string): Promise<string> {
    const contract = this.tokenContracts.get(tokenAddress.toLowerCase());
    
    if (!contract) {
      throw new Error('عقد التوكن غير موجود');
    }

    try {
      const balance = await contract.balanceOf(userAddress);
      return ethers.formatUnits(balance, 18);
    } catch (error) {
      console.error('❌ خطأ في جلب رصيد التوكن:', error);
      throw error;
    }
  }

  /**
   * الموافقة على إنفاق التوكن
   */
  async approveToken(tokenAddress: string, amount: string): Promise<string> {
    const contract = this.tokenContracts.get(tokenAddress.toLowerCase());
    
    if (!contract || !this.signer) {
      throw new Error('عقد التوكن غير موجود أو المحفظة غير متصلة');
    }

    try {
      const amountWei = ethers.parseUnits(amount, 18);
      const coreEscrowAddress = getContractAddress('CORE_ESCROW', this.currentNetwork?.isTestnet ?? true);
      
      const tx = await contract.approve(coreEscrowAddress, amountWei, {
        gasLimit: ENHANCED_GAS_LIMITS.APPROVE_TOKEN
      });

      console.log('✅ تم إرسال معاملة الموافقة على التوكن:', tx.hash);
      return tx.hash;

    } catch (error) {
      console.error('❌ خطأ في الموافقة على التوكن:', error);
      throw error;
    }
  }

  /**
   * التحقق من الموافقة على التوكن
   */
  async getTokenAllowance(tokenAddress: string, userAddress: string): Promise<string> {
    const contract = this.tokenContracts.get(tokenAddress.toLowerCase());
    
    if (!contract) {
      throw new Error('عقد التوكن غير موجود');
    }

    try {
      const coreEscrowAddress = getContractAddress('CORE_ESCROW', this.currentNetwork?.isTestnet ?? true);
      const allowance = await contract.allowance(userAddress, coreEscrowAddress);
      return ethers.formatUnits(allowance, 18);
    } catch (error) {
      console.error('❌ خطأ في جلب الموافقة على التوكن:', error);
      throw error;
    }
  }

  /**
   * جلب معلومات الشبكة الحالية
   */
  getCurrentNetwork(): NetworkInfo | null {
    return this.currentNetwork;
  }

  /**
   * التحقق من حالة الاتصال
   */
  isConnected(): boolean {
    return this.isInitialized && this.signer !== null;
  }

  /**
   * قطع الاتصال
   */
  disconnect(): void {
    this.signer = null;
    this.provider = null;
    this.coreEscrowContract = null;
    this.reputationManagerContract = null;
    this.oracleManagerContract = null;
    this.adminManagerContract = null;
    this.escrowIntegratorContract = null;
    this.tokenContracts.clear();
    this.currentNetwork = null;
    this.isInitialized = false;
    console.log('✅ تم قطع الاتصال بالعقود المحسنة');
  }
  // ==================== وظائف Oracle Manager ====================

  /**
   * جلب سعر عملة مستقرة
   */
  async getTokenPrice(tokenAddress: string, currency: string): Promise<string> {
    if (!this.oracleManagerContract) {
      throw new Error('Oracle Manager contract not initialized');
    }

    try {
      const price = await this.oracleManagerContract.getPrice(tokenAddress, currency);
      return ethers.formatUnits(price, 8); // Oracle prices have 8 decimals
    } catch (error) {
      console.error('❌ خطأ في جلب السعر:', error);
      throw error;
    }
  }

  /**
   * جلب أسعار صرف العملات المحلية
   */
  async getExchangeRate(baseCurrency: string, targetCurrency: string): Promise<string> {
    if (!this.oracleManagerContract) {
      throw new Error('Oracle Manager contract not initialized');
    }

    try {
      const rate = await this.oracleManagerContract.getExchangeRate(baseCurrency, targetCurrency);
      return ethers.formatUnits(rate, 8);
    } catch (error) {
      console.error('❌ خطأ في جلب سعر الصرف:', error);
      throw error;
    }
  }

  /**
   * التحقق من صحة السعر
   */
  async validatePrice(tokenAddress: string, currency: string, price: string): Promise<boolean> {
    if (!this.oracleManagerContract) {
      throw new Error('Oracle Manager contract not initialized');
    }

    try {
      const oraclePrice = await this.getTokenPrice(tokenAddress, currency);
      const priceNumber = parseFloat(price);
      const oraclePriceNumber = parseFloat(oraclePrice);

      const deviation = Math.abs(priceNumber - oraclePriceNumber) / oraclePriceNumber;
      const maxDeviation = 0.05; // 5%

      return deviation <= maxDeviation;
    } catch (error) {
      console.error('❌ خطأ في التحقق من السعر:', error);
      return false;
    }
  }

  // ==================== وظائف Reputation Manager ====================

  /**
   * جلب سمعة المستخدم
   */
  async getUserReputation(userAddress: string, networkId: number = 1): Promise<any> {
    if (!this.reputationManagerContract) {
      throw new Error('Reputation Manager contract not initialized');
    }

    try {
      const reputation = await this.reputationManagerContract.getUserReputation(userAddress, networkId);
      return {
        score: Number(reputation.score),
        level: Number(reputation.level),
        totalTrades: Number(reputation.totalTrades),
        successfulTrades: Number(reputation.successfulTrades),
        disputedTrades: Number(reputation.disputedTrades),
        averageRating: Number(reputation.averageRating),
        lastUpdated: Number(reputation.lastUpdated),
        isActive: reputation.isActive
      };
    } catch (error) {
      console.error('❌ خطأ في جلب السمعة:', error);
      throw error;
    }
  }

  /**
   * تقييم مستخدم
   */
  async rateUser(
    ratedAddress: string,
    tradeId: number,
    rating: number,
    comment: string = '',
    networkId: number = 1
  ): Promise<string> {
    if (!this.reputationManagerContract || !this.signer) {
      throw new Error('Reputation Manager contract or signer not initialized');
    }

    try {
      const tx = await this.reputationManagerContract.rateUser(
        ratedAddress,
        tradeId,
        rating,
        comment,
        networkId,
        {
          gasLimit: ENHANCED_GAS_LIMITS.RATE_USER
        }
      );

      return tx.hash;
    } catch (error) {
      console.error('❌ خطأ في تقييم المستخدم:', error);
      throw error;
    }
  }

  /**
   * إنشاء صفقة متعددة العملات
   */
  async createMultiTokenTrade(
    tokenAddress: string,
    amount: string,
    pricePerToken: string,
    currency: string,
    paymentMethod: string = ''
  ): Promise<string> {
    if (!this.coreEscrowContract || !this.signer) {
      throw new Error('Core Escrow contract or signer not initialized');
    }

    try {
      // التحقق من دعم التوكن
      const isSupported = await this.coreEscrowContract.isTokenSupported(tokenAddress);
      if (!isSupported) {
        throw new Error('Token is not supported');
      }

      // التحقق من صحة السعر
      const isPriceValid = await this.validatePrice(tokenAddress, currency, pricePerToken);
      if (!isPriceValid) {
        console.warn('⚠️ تحذير: السعر قد يكون غير دقيق مقارنة بسعر السوق');
      }

      // تحويل القيم
      const tokenContract = this.tokenContracts.get(tokenAddress.toLowerCase());
      if (!tokenContract) {
        throw new Error('Token contract not found');
      }

      const decimals = await tokenContract.decimals();
      const amountWei = ethers.parseUnits(amount, decimals);
      const priceWei = ethers.parseUnits(pricePerToken, 8); // Oracle price decimals

      const tx = await this.coreEscrowContract.createTrade(
        tokenAddress,
        amountWei,
        priceWei,
        currency,
        ethers.encodeBytes32String(paymentMethod),
        {
          gasLimit: ENHANCED_GAS_LIMITS.CREATE_TRADE
        }
      );

      return tx.hash;
    } catch (error) {
      console.error('❌ خطأ في إنشاء الصفقة:', error);
      throw error;
    }
  }

  /**
   * جلب العملات المستقرة المدعومة
   */
  getSupportedTokens(): TokenInfo[] {
    const isTestnet = this.currentNetwork?.isTestnet ?? true;
    const tokens = getSupportedTokens(isTestnet);

    return Object.entries(tokens).map(([symbol, tokenInfo]) => ({
      address: tokenInfo.address,
      symbol,
      name: tokenInfo.name,
      decimals: tokenInfo.decimals,
      isSupported: true
    }));
  }

  /**
   * التحقق من حالة التهيئة
   */
  isReady(): boolean {
    return this.isInitialized && this.provider !== null;
  }

  // ==================== وظائف Admin Manager ====================

  /**
   * التحقق من صلاحيات المشرف
   */
  async isAdmin(userAddress: string): Promise<boolean> {
    if (!this.adminManagerContract) {
      throw new Error('Admin Manager contract not initialized');
    }

    try {
      return await this.adminManagerContract.isAdmin(userAddress);
    } catch (error) {
      console.error('❌ خطأ في التحقق من صلاحيات المشرف:', error);
      return false;
    }
  }

  /**
   * جلب مستوى المشرف
   */
  async getAdminLevel(userAddress: string): Promise<number> {
    if (!this.adminManagerContract) {
      throw new Error('Admin Manager contract not initialized');
    }

    try {
      return await this.adminManagerContract.getAdminLevel(userAddress);
    } catch (error) {
      console.error('❌ خطأ في جلب مستوى المشرف:', error);
      return 0;
    }
  }

  /**
   * حل نزاع
   */
  async resolveDispute(
    tradeId: number,
    winner: string,
    reason: string
  ): Promise<string> {
    if (!this.adminManagerContract || !this.signer) {
      throw new Error('Admin Manager contract or signer not initialized');
    }

    try {
      const tx = await this.adminManagerContract.resolveDispute(
        tradeId,
        winner,
        reason,
        {
          gasLimit: ENHANCED_GAS_LIMITS.RESOLVE_DISPUTE
        }
      );

      return tx.hash;
    } catch (error) {
      console.error('❌ خطأ في حل النزاع:', error);
      throw error;
    }
  }

  // ==================== وظائف Escrow Integrator ====================

  /**
   * إنشاء صفقة متكاملة مع جميع العقود
   */
  async createIntegratedTrade(
    tokenAddress: string,
    amount: string,
    pricePerToken: string,
    currency: string,
    paymentMethod: string = '',
    enableReputationUpdate: boolean = true,
    enablePriceValidation: boolean = true
  ): Promise<string> {
    if (!this.escrowIntegratorContract || !this.signer) {
      throw new Error('Escrow Integrator contract or signer not initialized');
    }

    try {
      // التحقق من صحة السعر إذا كان مفعلاً
      if (enablePriceValidation) {
        const isPriceValid = await this.validatePrice(tokenAddress, currency, pricePerToken);
        if (!isPriceValid) {
          console.warn('⚠️ تحذير: السعر قد يكون غير دقيق مقارنة بسعر السوق');
        }
      }

      // تحويل القيم
      const tokenContract = this.tokenContracts.get(tokenAddress.toLowerCase());
      if (!tokenContract) {
        throw new Error('Token contract not found');
      }

      const decimals = await tokenContract.decimals();
      const amountWei = ethers.parseUnits(amount, decimals);
      const priceWei = ethers.parseUnits(pricePerToken, 8);

      const tx = await this.escrowIntegratorContract.createIntegratedTrade(
        tokenAddress,
        amountWei,
        priceWei,
        currency,
        ethers.encodeBytes32String(paymentMethod),
        enableReputationUpdate,
        enablePriceValidation,
        {
          gasLimit: ENHANCED_GAS_LIMITS.MULTI_TOKEN_OPERATION
        }
      );

      return tx.hash;
    } catch (error) {
      console.error('❌ خطأ في إنشاء الصفقة المتكاملة:', error);
      throw error;
    }
  }

  /**
   * مزامنة الصفقة مع جميع العقود
   */
  async syncTrade(tradeId: number): Promise<string> {
    if (!this.escrowIntegratorContract || !this.signer) {
      throw new Error('Escrow Integrator contract or signer not initialized');
    }

    try {
      const tx = await this.escrowIntegratorContract.syncTrade(tradeId, {
        gasLimit: ENHANCED_GAS_LIMITS.BATCH_OPERATIONS
      });

      return tx.hash;
    } catch (error) {
      console.error('❌ خطأ في مزامنة الصفقة:', error);
      throw error;
    }
  }

  // ==================== وظائف مساعدة ====================

  /**
   * جلب جميع العقود المتصلة
   */
  getConnectedContracts(): {
    coreEscrow: boolean;
    reputationManager: boolean;
    oracleManager: boolean;
    adminManager: boolean;
    escrowIntegrator: boolean;
  } {
    return {
      coreEscrow: this.coreEscrowContract !== null,
      reputationManager: this.reputationManagerContract !== null,
      oracleManager: this.oracleManagerContract !== null,
      adminManager: this.adminManagerContract !== null,
      escrowIntegrator: this.escrowIntegratorContract !== null
    };
  }

  /**
   * إعادة تحميل العقود
   */
  async reloadContracts(): Promise<void> {
    if (!this.provider) {
      throw new Error('Provider not initialized');
    }

    await this.setupContracts();

    if (this.signer) {
      await this.setupContractsWithSigner();
    }
  }


}

// إنشاء مثيل واحد من الخدمة
export const enhancedContractService = new EnhancedContractService();
export default enhancedContractService;
