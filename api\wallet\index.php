<?php
/**
 * API endpoint لإدارة المحفظة
 * Wallet Management API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = new Database();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $userId = $_GET['user_id'] ?? null;
        $action = $_GET['action'] ?? 'balance';
        
        if (!$userId) {
            throw new Exception('معرف المستخدم مطلوب');
        }
        
        // التحقق من وجود المستخدم
        $stmt = $connection->prepare("SELECT wallet_address FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            throw new Exception('المستخدم غير موجود');
        }
        
        if ($action === 'balance') {
            // جلب أرصدة المحفظة
            $stmt = $connection->prepare("
                SELECT
                    currency,
                    SUM(CASE WHEN transaction_type = 'deposit' THEN amount ELSE -amount END) as balance
                FROM wallet_transactions
                WHERE user_id = ?
                GROUP BY currency
                HAVING balance > 0
            ");
            $stmt->execute([$userId]);
            $balances = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // تنسيق البيانات
            $formattedBalances = [];
            foreach ($balances as $balance) {
                $formattedBalances[$balance['currency']] = floatval($balance['balance']);
            }
            
            // إضافة العملات الافتراضية إذا لم تكن موجودة
            $defaultCurrencies = ['USDT', 'BTC', 'ETH', 'BNB'];
            foreach ($defaultCurrencies as $currency) {
                if (!isset($formattedBalances[$currency])) {
                    $formattedBalances[$currency] = 0.0;
                }
            }
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'wallet_address' => $user['wallet_address'],
                    'balances' => $formattedBalances
                ]
            ]);
            
        } elseif ($action === 'transactions') {
            // جلب تاريخ المعاملات
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
            $offset = ($page - 1) * $limit;
            
            $type = $_GET['type'] ?? 'all'; // all, deposit, withdrawal, trade
            $currency = $_GET['currency'] ?? 'all';
            
            // بناء الاستعلام
            $whereConditions = ['user_id = ?'];
            $params = [$userId];
            
            if ($type !== 'all') {
                $whereConditions[] = 'transaction_type = ?';
                $params[] = $type;
            }
            
            if ($currency !== 'all') {
                $whereConditions[] = 'currency = ?';
                $params[] = $currency;
            }
            
            $whereClause = implode(' AND ', $whereConditions);
            
            // الحصول على العدد الإجمالي
            $countStmt = $connection->prepare("
                SELECT COUNT(*) as total
                FROM wallet_transactions
                WHERE $whereClause
            ");
            $countStmt->execute($params);
            $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // جلب المعاملات
            $stmt = $connection->prepare("
                SELECT *
                FROM wallet_transactions
                WHERE $whereClause
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            ");
            
            $params[] = $limit;
            $params[] = $offset;
            $stmt->execute($params);
            $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // تنسيق البيانات
            foreach ($transactions as &$transaction) {
                $transaction['amount'] = floatval($transaction['amount']);
                $transaction['gas_fee'] = floatval($transaction['gas_fee']);
                // إضافة fee كـ alias للتوافق مع الواجهة الأمامية
                $transaction['fee'] = $transaction['gas_fee'];
            }
            
            echo json_encode([
                'success' => true,
                'data' => $transactions,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => intval($totalCount),
                    'total_pages' => ceil($totalCount / $limit)
                ]
            ]);
            
        } else {
            throw new Exception('إجراء غير صحيح');
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // إضافة معاملة جديدة
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $requiredFields = ['user_id', 'type', 'amount', 'currency'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                throw new Exception("الحقل $field مطلوب");
            }
        }
        
        // التحقق من صحة البيانات
        if (!in_array($input['type'], ['deposit', 'withdrawal', 'trade_escrow', 'trade_release'])) {
            throw new Exception('نوع المعاملة غير صحيح');
        }
        
        if ($input['amount'] <= 0) {
            throw new Exception('المبلغ يجب أن يكون أكبر من صفر');
        }
        
        // التحقق من وجود المستخدم
        $stmt = $connection->prepare("SELECT id FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$input['user_id']]);
        
        if (!$stmt->fetch()) {
            throw new Exception('المستخدم غير موجود');
        }
        
        // للسحب، التحقق من الرصيد المتاح
        if ($input['type'] === 'withdrawal') {
            $stmt = $connection->prepare("
                SELECT
                    SUM(CASE WHEN transaction_type = 'deposit' THEN amount ELSE -amount END) as balance
                FROM wallet_transactions
                WHERE user_id = ? AND currency = ?
            ");
            $stmt->execute([$input['user_id'], $input['currency']]);
            $currentBalance = $stmt->fetch(PDO::FETCH_ASSOC)['balance'] ?? 0;
            
            if ($currentBalance < $input['amount']) {
                throw new Exception('الرصيد غير كافي');
            }
        }
        
        // إدراج المعاملة
        $stmt = $connection->prepare("
            INSERT INTO wallet_transactions (
                user_id, transaction_type, amount, currency, gas_fee,
                transaction_hash, status, description, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ");

        $stmt->execute([
            $input['user_id'],
            $input['type'],
            $input['amount'],
            $input['currency'],
            $input['gas_fee'] ?? $input['fee'] ?? 0, // دعم كلا الاسمين للتوافق
            $input['transaction_hash'] ?? null,
            $input['status'] ?? 'pending',
            $input['description'] ?? ''
        ]);
        
        $transactionId = $connection->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة المعاملة بنجاح',
            'data' => ['id' => $transactionId]
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // تحديث عنوان المحفظة
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $userId = $input['user_id'] ?? null;
        $walletAddress = $input['wallet_address'] ?? null;
        
        if (!$userId || !$walletAddress) {
            throw new Exception('معرف المستخدم وعنوان المحفظة مطلوبان');
        }
        
        // التحقق من صحة عنوان المحفظة (تحقق بسيط)
        if (!preg_match('/^0x[a-fA-F0-9]{40}$/', $walletAddress)) {
            throw new Exception('عنوان المحفظة غير صحيح');
        }
        
        // التحقق من عدم استخدام العنوان من قبل مستخدم آخر
        $stmt = $connection->prepare("
            SELECT id FROM users 
            WHERE wallet_address = ? AND id != ? AND is_active = 1
        ");
        $stmt->execute([$walletAddress, $userId]);
        
        if ($stmt->fetch()) {
            throw new Exception('عنوان المحفظة مستخدم من قبل مستخدم آخر');
        }
        
        // تحديث عنوان المحفظة
        $stmt = $connection->prepare("
            UPDATE users 
            SET wallet_address = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        ");
        
        $stmt->execute([$walletAddress, $userId]);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث عنوان المحفظة بنجاح'
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    error_log('Database error in wallet/index.php: ' . $e->getMessage());
}
?>
