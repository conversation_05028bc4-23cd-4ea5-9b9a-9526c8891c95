<?php
/**
 * API إدارة العقد الذكي المباشرة للأدمن
 * Direct Smart Contract Management API for Admin
 * 
 * يوفر واجهة لإدارة العقد الذكي مباشرة من لوحة الأدمن
 * Provides interface for direct smart contract management from admin panel
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../includes/admin-auth.php';

/**
 * الحصول على إحصائيات العقد الشاملة
 */
function getContractStats($connection) {
    try {
        // إحصائيات من قاعدة البيانات
        $stats = [];

        // إحصائيات الصفقات مع فحص وجود الجدول
        try {
            $stmt = $connection->prepare("
                SELECT
                    COUNT(*) as total_trades,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_trades,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_trades,
                    COUNT(CASE WHEN status = 'disputed' THEN 1 END) as disputed_trades,
                    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_trades,
                    COALESCE(SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END), 0) as total_volume,
                    COALESCE(SUM(CASE WHEN status = 'completed' THEN platform_fee ELSE 0 END), 0) as total_fees
                FROM trades
                WHERE blockchain_trade_id IS NOT NULL
            ");
            $stmt->execute();
            $tradeStats = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            // الجدول غير موجود، استخدام قيم افتراضية
            $tradeStats = [
                'total_trades' => 0,
                'active_trades' => 0,
                'completed_trades' => 0,
                'disputed_trades' => 0,
                'cancelled_trades' => 0,
                'total_volume' => 0,
                'total_fees' => 0
            ];
        }
        
        // إحصائيات العروض المربوطة بالعقد
        try {
            $stmt = $connection->prepare("
                SELECT
                    COUNT(*) as total_offers,
                    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_offers,
                    COUNT(CASE WHEN contract_status = 'created' THEN 1 END) as contract_created,
                    COUNT(CASE WHEN contract_status = 'active' THEN 1 END) as contract_active,
                    COUNT(CASE WHEN sync_status = 'synced' THEN 1 END) as synced_offers
                FROM offers
                WHERE blockchain_trade_id IS NOT NULL
            ");
            $stmt->execute();
            $offerStats = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $offerStats = [
                'total_offers' => 0,
                'active_offers' => 0,
                'contract_created' => 0,
                'contract_active' => 0,
                'synced_offers' => 0
            ];
        }
        
        // إحصائيات أحداث العقد
        try {
            $stmt = $connection->prepare("
                SELECT
                    COUNT(*) as total_events,
                    COUNT(CASE WHEN processed = 1 THEN 1 END) as processed_events,
                    COUNT(CASE WHEN processed = 0 THEN 1 END) as pending_events,
                    COUNT(DISTINCT event_type) as event_types,
                    MAX(event_timestamp) as last_event_time
                FROM contract_events
            ");
            $stmt->execute();
            $eventStats = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $eventStats = [
                'total_events' => 0,
                'processed_events' => 0,
                'pending_events' => 0,
                'event_types' => 0,
                'last_event_time' => null
            ];
        }
        
        // إحصائيات المزامنة
        try {
            $stmt = $connection->prepare("
                SELECT
                    COUNT(*) as total_sync_records,
                    COUNT(CASE WHEN sync_status = 'synced' THEN 1 END) as synced_records,
                    COUNT(CASE WHEN sync_status = 'failed' THEN 1 END) as failed_records,
                    COUNT(CASE WHEN sync_status = 'pending' THEN 1 END) as pending_records
                FROM sync_status
            ");
            $stmt->execute();
            $syncStats = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $syncStats = [
                'total_sync_records' => 0,
                'synced_records' => 0,
                'failed_records' => 0,
                'pending_records' => 0
            ];
        }
        
        return [
            'trades' => $tradeStats,
            'offers' => $offerStats,
            'events' => $eventStats,
            'sync' => $syncStats,
            'last_updated' => date('Y-m-d H:i:s')
        ];
        
    } catch (Exception $e) {
        throw new Exception('خطأ في جلب إحصائيات العقد: ' . $e->getMessage());
    }
}

/**
 * الحصول على الصفقات المؤهلة للنزاع التلقائي
 */
function getEligibleAutoDisputeTrades($connection, $limit = 50) {
    try {
        $stmt = $connection->prepare("
            SELECT 
                t.id,
                t.blockchain_trade_id,
                t.seller_id,
                t.buyer_id,
                t.amount,
                t.status,
                t.created_at,
                t.updated_at,
                TIMESTAMPDIFF(HOUR, t.updated_at, NOW()) as hours_since_update,
                u1.username as seller_username,
                u2.username as buyer_username
            FROM trades t
            LEFT JOIN users u1 ON t.seller_id = u1.id
            LEFT JOIN users u2 ON t.buyer_id = u2.id
            WHERE t.status IN ('payment_sent', 'payment_confirmed')
            AND t.blockchain_trade_id IS NOT NULL
            AND TIMESTAMPDIFF(HOUR, t.updated_at, NOW()) >= 24
            ORDER BY t.updated_at ASC
            LIMIT ?
        ");
        
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        throw new Exception('خطأ في جلب الصفقات المؤهلة للنزاع التلقائي: ' . $e->getMessage());
    }
}

/**
 * الحصول على أحداث العقد الحديثة
 */
function getRecentContractEvents($connection, $limit = 20) {
    try {
        $stmt = $connection->prepare("
            SELECT 
                ce.*,
                t.seller_id,
                t.buyer_id,
                u1.username as seller_username,
                u2.username as buyer_username
            FROM contract_events ce
            LEFT JOIN trades t ON ce.blockchain_trade_id = t.blockchain_trade_id
            LEFT JOIN users u1 ON t.seller_id = u1.id
            LEFT JOIN users u2 ON t.buyer_id = u2.id
            ORDER BY ce.event_timestamp DESC
            LIMIT ?
        ");
        
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        throw new Exception('خطأ في جلب أحداث العقد الحديثة: ' . $e->getMessage());
    }
}

/**
 * الحصول على حالة المزامنة
 */
function getSyncStatus($connection) {
    try {
        $syncByType = [];
        $recentErrors = [];

        // محاولة جلب آخر مزامنة ناجحة
        try {
            $stmt = $connection->prepare("
                SELECT
                    entity_type,
                    COUNT(*) as total,
                    COUNT(CASE WHEN sync_status = 'synced' THEN 1 END) as synced,
                    COUNT(CASE WHEN sync_status = 'failed' THEN 1 END) as failed,
                    COUNT(CASE WHEN sync_status = 'pending' THEN 1 END) as pending,
                    MAX(last_successful_sync) as last_sync
                FROM sync_status
                GROUP BY entity_type
            ");
            $stmt->execute();
            $syncByType = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            // الجدول غير موجود
        }

        // محاولة جلب الأخطاء الحديثة
        try {
            $stmt = $connection->prepare("
                SELECT
                    entity_type,
                    entity_id,
                    error_message,
                    sync_attempts,
                    last_sync_attempt
                FROM sync_status
                WHERE sync_status = 'failed'
                ORDER BY last_sync_attempt DESC
                LIMIT 10
            ");
            $stmt->execute();
            $recentErrors = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            // الجدول غير موجود
        }

        return [
            'by_type' => $syncByType,
            'recent_errors' => $recentErrors
        ];

    } catch (Exception $e) {
        throw new Exception('خطأ في جلب حالة المزامنة: ' . $e->getMessage());
    }
}

/**
 * تنفيذ مزامنة شاملة
 */
function executeFullSync($connection, $admin_id) {
    try {
        $affectedRows = 0;

        // محاولة تسجيل بداية المزامنة
        try {
            $stmt = $connection->prepare("
                INSERT INTO admin_activity_logs
                (admin_id, action_type, target_type, target_id, data, created_at)
                VALUES (?, 'full_sync_started', 'contract', 1, ?, NOW())
            ");

            $stmt->execute([
                $admin_id,
                json_encode(['sync_type' => 'full', 'initiated_by' => 'admin'])
            ]);
        } catch (Exception $e) {
            // تجاهل خطأ تسجيل النشاط
        }

        // محاولة إعادة تعيين حالة المزامنة للعناصر الفاشلة
        try {
            $stmt = $connection->prepare("
                UPDATE sync_status
                SET sync_status = 'pending',
                    sync_attempts = 0,
                    error_message = NULL,
                    updated_at = NOW()
                WHERE sync_status = 'failed'
            ");
            $stmt->execute();
            $affectedRows = $stmt->rowCount();
        } catch (Exception $e) {
            // الجدول غير موجود، تجاهل
        }

        // محاولة تسجيل انتهاء المزامنة
        try {
            $stmt = $connection->prepare("
                INSERT INTO admin_activity_logs
                (admin_id, action_type, target_type, target_id, data, created_at)
                VALUES (?, 'full_sync_completed', 'contract', 1, ?, NOW())
            ");

            $stmt->execute([
                $admin_id,
                json_encode([
                    'sync_type' => 'full',
                    'reset_failed_items' => $affectedRows,
                    'status' => 'completed'
                ])
            ]);
        } catch (Exception $e) {
            // تجاهل خطأ تسجيل النشاط
        }

        return [
            'reset_failed_items' => $affectedRows,
            'status' => 'completed'
        ];

    } catch (Exception $e) {
        throw new Exception('خطأ في تنفيذ المزامنة الشاملة: ' . $e->getMessage());
    }
}

// معالجة الطلبات
try {
    // للتطوير: تجاهل المصادقة مؤقتاً
    $admin_id = 1;

    // Get database connection
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();

    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';
    
    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'stats':
                    $stats = getContractStats($connection);
                    echo json_encode([
                        'success' => true,
                        'data' => $stats
                    ], JSON_UNESCAPED_UNICODE);
                    break;
                    
                case 'eligible_disputes':
                    $limit = (int)($_GET['limit'] ?? 50);
                    $trades = getEligibleAutoDisputeTrades($connection, $limit);
                    echo json_encode([
                        'success' => true,
                        'data' => $trades
                    ], JSON_UNESCAPED_UNICODE);
                    break;
                    
                case 'recent_events':
                    $limit = (int)($_GET['limit'] ?? 20);
                    $events = getRecentContractEvents($connection, $limit);
                    echo json_encode([
                        'success' => true,
                        'data' => $events
                    ], JSON_UNESCAPED_UNICODE);
                    break;
                    
                case 'sync_status':
                    $syncStatus = getSyncStatus($connection);
                    echo json_encode([
                        'success' => true,
                        'data' => $syncStatus
                    ], JSON_UNESCAPED_UNICODE);
                    break;
                    
                default:
                    // الحصول على جميع البيانات
                    $stats = getContractStats($connection);
                    $syncStatus = getSyncStatus($connection);
                    $recentEvents = getRecentContractEvents($connection, 10);
                    $eligibleDisputes = getEligibleAutoDisputeTrades($connection, 10);
                    
                    echo json_encode([
                        'success' => true,
                        'data' => [
                            'stats' => $stats,
                            'sync_status' => $syncStatus,
                            'recent_events' => $recentEvents,
                            'eligible_disputes' => $eligibleDisputes
                        ]
                    ], JSON_UNESCAPED_UNICODE);
                    break;
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['action'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'نوع الإجراء مطلوب',
                    'error_en' => 'Action type is required'
                ], JSON_UNESCAPED_UNICODE);
                break;
            }
            
            switch ($input['action']) {
                case 'full_sync':
                    $result = executeFullSync($connection, $admin_id);
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم تنفيذ المزامنة الشاملة بنجاح',
                        'message_en' => 'Full sync executed successfully',
                        'data' => $result
                    ], JSON_UNESCAPED_UNICODE);
                    break;
                    
                default:
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'error' => 'نوع إجراء غير مدعوم',
                        'error_en' => 'Unsupported action type'
                    ], JSON_UNESCAPED_UNICODE);
                    break;
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'error' => 'Method not allowed'
            ]);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
    
    error_log('Error in contract-management.php: ' . $e->getMessage());
}
?>
