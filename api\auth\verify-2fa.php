<?php
/**
 * API endpoint للتحقق من المصادقة الثنائية أثناء تسجيل الدخول
 * 2FA Verification During Login API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";

// التحقق من طريقة الطلب
validateRequestMethod(['POST']);

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/../../.env')) {
    $lines = file(__DIR__ . '/../../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

// مفتاح JWT من متغيرات البيئة
$jwtSecret = $_ENV['JWT_SECRET'] ?? 'default-secret-key';

/**
 * إنشاء JWT token
 */
function createJWT($payload, $secret) {
    $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
    $payload = json_encode($payload);
    
    $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
    $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
    
    $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $secret, true);
    $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
    
    return $base64Header . "." . $base64Payload . "." . $base64Signature;
}

/**
 * التحقق من رمز TOTP
 */
function verifyTOTPCode($secret, $code, $discrepancy = 1) {
    $currentTimeSlice = floor(time() / 30);
    
    for ($i = -$discrepancy; $i <= $discrepancy; $i++) {
        $calculatedCode = generateTOTPCode($secret, $currentTimeSlice + $i);
        if ($calculatedCode === $code) {
            return true;
        }
    }
    
    return false;
}

/**
 * إنشاء رمز TOTP
 */
function generateTOTPCode($secret, $timeSlice = null) {
    if ($timeSlice === null) {
        $timeSlice = floor(time() / 30);
    }
    
    $secretkey = base32_decode($secret);
    $time = pack('N*', 0) . pack('N*', $timeSlice);
    $hm = hash_hmac('SHA1', $time, $secretkey, true);
    $offset = ord(substr($hm, -1)) & 0x0F;
    $hashpart = substr($hm, $offset, 4);
    $value = unpack('N', $hashpart);
    $value = $value[1];
    $value = $value & 0x7FFFFFFF;
    $modulo = pow(10, 6);
    return str_pad($value % $modulo, 6, '0', STR_PAD_LEFT);
}

/**
 * فك تشفير Base32
 */
function base32_decode($input) {
    $alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    $output = '';
    $v = 0;
    $vbits = 0;
    
    for ($i = 0, $j = strlen($input); $i < $j; $i++) {
        $v <<= 5;
        $v += strpos($alphabet, $input[$i]);
        $vbits += 5;
        
        if ($vbits >= 8) {
            $output .= chr($v >> ($vbits - 8));
            $vbits -= 8;
        }
    }
    
    return $output;
}

/**
 * إنشاء رمز SMS
 */
function generateSMSCode() {
    return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
}

/**
 * إرسال رمز SMS
 */
function sendSMSCode($phoneNumber, $code) {
    // TODO: تطبيق إرسال SMS الفعلي
    error_log("SMS Code for $phoneNumber: $code");
    return true;
}

try {
    // الحصول على البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendErrorResponse('بيانات غير صحيحة');
    }
    
    $action = $input['action'] ?? '';
    $userId = $input['user_id'] ?? null;
    $code = $input['code'] ?? '';
    $backupCode = $input['backup_code'] ?? '';
    
    if (!$userId) {
        sendErrorResponse('معرف المستخدم مطلوب');
    }
    
    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // جلب بيانات المستخدم
    $stmt = $connection->prepare("
        SELECT id, wallet_address, username, email, full_name,
               is_verified, is_admin, is_active, rating, total_trades,
               completed_trades, total_volume, created_at, last_login,
               two_factor_enabled, two_factor_type, two_factor_secret, phone
        FROM users
        WHERE id = ? AND is_active = 1
    ");
    
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        sendErrorResponse('المستخدم غير موجود أو غير نشط');
    }
    
    if (!$user['two_factor_enabled']) {
        sendErrorResponse('المصادقة الثنائية غير مفعلة لهذا المستخدم');
    }
    
    $verified = false;
    $verificationMethod = '';
    
    if ($action === 'verify') {
        // التحقق من الرمز
        if (!empty($code)) {
            if ($user['two_factor_type'] === 'totp') {
                // التحقق من رمز TOTP
                if ($user['two_factor_secret'] && verifyTOTPCode($user['two_factor_secret'], $code)) {
                    $verified = true;
                    $verificationMethod = 'totp';
                }
            } elseif ($user['two_factor_type'] === 'sms') {
                // التحقق من رمز SMS
                $stmt = $connection->prepare("
                    SELECT code, expires_at 
                    FROM sms_verification_codes 
                    WHERE user_id = ? AND verified = 0
                    ORDER BY created_at DESC 
                    LIMIT 1
                ");
                $stmt->execute([$userId]);
                $smsData = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($smsData && $smsData['code'] === $code && strtotime($smsData['expires_at']) >= time()) {
                    $verified = true;
                    $verificationMethod = 'sms';
                    
                    // تمييز الرمز كمستخدم
                    $stmt = $connection->prepare("
                        UPDATE sms_verification_codes 
                        SET verified = 1 
                        WHERE user_id = ? AND code = ?
                    ");
                    $stmt->execute([$userId, $code]);
                }
            }
        }
        
        // التحقق من رمز الاسترداد
        if (!$verified && !empty($backupCode)) {
            $stmt = $connection->prepare("
                SELECT id, code 
                FROM two_factor_backup_codes 
                WHERE user_id = ? AND used = 0
            ");
            $stmt->execute([$userId]);
            $backupCodes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($backupCodes as $backup) {
                if (password_verify($backupCode, $backup['code'])) {
                    $verified = true;
                    $verificationMethod = 'backup_code';
                    
                    // تمييز رمز الاسترداد كمستخدم
                    $stmt = $connection->prepare("
                        UPDATE two_factor_backup_codes 
                        SET used = 1, used_at = NOW() 
                        WHERE id = ?
                    ");
                    $stmt->execute([$backup['id']]);
                    break;
                }
            }
        }
        
        if (!$verified) {
            sendErrorResponse('رمز التحقق غير صحيح');
        }
        
        // إنشاء JWT tokens
        $currentTime = time();
        $accessTokenExpiry = $currentTime + (15 * 60); // 15 دقيقة
        $refreshTokenExpiry = $currentTime + (7 * 24 * 60 * 60); // 7 أيام
        
        // رمز الوصول
        $accessTokenPayload = [
            'user_id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'is_admin' => $user['is_admin'],
            'is_verified' => $user['is_verified'],
            'type' => 'access',
            'iat' => $currentTime,
            'exp' => $accessTokenExpiry,
            '2fa_verified' => true,
            '2fa_method' => $verificationMethod
        ];
        
        // رمز التجديد
        $refreshTokenPayload = [
            'user_id' => $user['id'],
            'type' => 'refresh',
            'iat' => $currentTime,
            'exp' => $refreshTokenExpiry
        ];
        
        $accessToken = createJWT($accessTokenPayload, $jwtSecret);
        $refreshToken = createJWT($refreshTokenPayload, $jwtSecret);
        
        // تحديث آخر تسجيل دخول
        $updateStmt = $connection->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $updateStmt->execute([$user['id']]);
        
        // تسجيل النشاط
        try {
            $checkTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
            $checkTable->execute();
            
            if ($checkTable->rowCount() > 0) {
                $checkColumns = $connection->prepare("SHOW COLUMNS FROM activity_logs LIKE 'entity_type'");
                $checkColumns->execute();
                
                if ($checkColumns->rowCount() > 0) {
                    $logStmt = $connection->prepare("
                        INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data)
                        VALUES (?, 'two_factor_verified', 'user', ?, ?, ?, ?)
                    ");
                    
                    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                    $loginData = json_encode([
                        'login_time' => date('Y-m-d H:i:s'),
                        'verification_method' => $verificationMethod,
                        '2fa_type' => $user['two_factor_type']
                    ]);
                    
                    $logStmt->execute([$user['id'], $user['id'], $ipAddress, $userAgent, $loginData]);
                }
            }
        } catch (Exception $logError) {
            error_log('Activity log error in verify-2fa: ' . $logError->getMessage());
        }
        
        // إعداد بيانات المستخدم
        $userData = [
            'id' => $user['id'],
            'walletAddress' => $user['wallet_address'],
            'username' => $user['username'],
            'email' => $user['email'],
            'fullName' => $user['full_name'],
            'isVerified' => (bool)$user['is_verified'],
            'isAdmin' => (bool)$user['is_admin'],
            'rating' => (float)$user['rating'],
            'totalTrades' => (int)$user['total_trades'],
            'completedTrades' => (int)$user['completed_trades'],
            'totalVolume' => (float)$user['total_volume'],
            'joinDate' => $user['created_at'],
            'lastLogin' => date('Y-m-d H:i:s')
        ];
        
        sendSuccessResponse([
            'user' => $userData,
            'accessToken' => $accessToken,
            'refreshToken' => $refreshToken,
            'expiresIn' => 900, // 15 دقيقة بالثواني
            'tokenType' => 'Bearer',
            'verificationMethod' => $verificationMethod
        ], 'تم التحقق من المصادقة الثنائية بنجاح');
        
    } elseif ($action === 'request_sms') {
        // طلب إرسال رمز SMS جديد
        if ($user['two_factor_type'] !== 'sms') {
            sendErrorResponse('المصادقة الثنائية عبر SMS غير مفعلة');
        }
        
        if (empty($user['phone'])) {
            sendErrorResponse('رقم الهاتف غير متوفر');
        }
        
        // إنشاء وإرسال رمز SMS جديد
        $smsCode = generateSMSCode();
        $expiryTime = date('Y-m-d H:i:s', time() + (5 * 60)); // 5 دقائق
        
        // حفظ الرمز
        $stmt = $connection->prepare("
            INSERT INTO sms_verification_codes (user_id, code, expires_at, created_at)
            VALUES (?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE
            code = VALUES(code),
            expires_at = VALUES(expires_at),
            created_at = NOW(),
            verified = 0
        ");
        $stmt->execute([$userId, $smsCode, $expiryTime]);
        
        // إرسال SMS
        $smsSent = sendSMSCode($user['phone'], $smsCode);
        
        sendSuccessResponse([
            'codeSent' => $smsSent,
            'phoneNumber' => substr($user['phone'], 0, 3) . '****' . substr($user['phone'], -2),
            'expiresIn' => 300 // 5 دقائق
        ], 'تم إرسال رمز التحقق بنجاح');
        
    } else {
        sendErrorResponse('إجراء غير صحيح');
    }
    
} catch (Exception $e) {
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    error_log('Database error in verify-2fa.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}
?>
