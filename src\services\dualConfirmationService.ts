/**
 * خدمة التأكيد المزدوج
 * Dual Confirmation Service
 * 
 * نظام تأكيد مزدوج: HTTP Response + Blockchain Event
 */

import { contractService } from './contractService';
import { failureTrackingService } from './failureTrackingService';
import { notificationService } from './notificationService';

interface ConfirmationResult {
  httpConfirmed: boolean;
  blockchainConfirmed: boolean;
  transactionHash?: string;
  blockNumber?: number;
  confirmationTime: number;
  status: 'pending' | 'partial' | 'confirmed' | 'failed';
}

interface PendingConfirmation {
  id: string;
  type: 'create_trade' | 'join_trade' | 'confirm_payment' | 'complete_trade' | 'cancel_trade';
  transactionHash?: string;
  expectedEvent: string;
  startTime: number;
  timeout: number;
  httpResponse?: any;
  blockchainEvent?: any;
  resolve: (result: ConfirmationResult) => void;
  reject: (error: any) => void;
}

class DualConfirmationService {
  private pendingConfirmations: Map<string, PendingConfirmation> = new Map();
  private eventListeners: Map<string, (event: any) => void> = new Map();
  private isListening = false;

  constructor() {
    this.startEventListening();
  }

  // تنفيذ عملية مع تأكيد مزدوج
  public async executeWithDualConfirmation(
    httpOperation: () => Promise<any>,
    expectedEvent: string,
    operationType: PendingConfirmation['type'],
    timeout: number = 30000
  ): Promise<ConfirmationResult> {
    const confirmationId = this.generateId();
    const startTime = Date.now();

    return new Promise<ConfirmationResult>((resolve, reject) => {
      // إنشاء تأكيد معلق
      const pendingConfirmation: PendingConfirmation = {
        id: confirmationId,
        type: operationType,
        expectedEvent,
        startTime,
        timeout,
        resolve,
        reject
      };

      this.pendingConfirmations.set(confirmationId, pendingConfirmation);

      // تنفيذ العملية HTTP
      this.executeHttpOperation(httpOperation, confirmationId);

      // إعداد timeout
      setTimeout(() => {
        this.handleTimeout(confirmationId);
      }, timeout);
    });
  }

  // تنفيذ العملية HTTP
  private async executeHttpOperation(operation: () => Promise<any>, confirmationId: string): Promise<void> {
    try {
      const response = await operation();
      const confirmation = this.pendingConfirmations.get(confirmationId);
      
      if (!confirmation) return;

      confirmation.httpResponse = response;

      // استخراج transaction hash من الاستجابة
      if (response.transactionHash) {
        confirmation.transactionHash = response.transactionHash;
      }

      // التحقق من إمكانية التأكيد الفوري
      this.checkForCompletion(confirmationId);

    } catch (error) {
      this.handleHttpError(confirmationId, error);
    }
  }

  // بدء الاستماع لأحداث البلوك تشين
  private startEventListening(): void {
    if (this.isListening) return;

    try {
      // الاستماع لأحداث العقد الذكي
      const events = [
        'TradeCreated',
        'BuyerJoined', 
        'PaymentSent',
        'PaymentConfirmed',
        'TradeCompleted',
        'TradeCancelled',
        'TradeDisputed'
      ];

      events.forEach(eventName => {
        const listener = (event: any) => this.handleBlockchainEvent(eventName, event);
        this.eventListeners.set(eventName, listener);
        
        // ربط المستمع بالعقد
        contractService.onEvent(eventName, listener);
      });

      this.isListening = true;
      console.log('🔗 Dual Confirmation: Started listening to blockchain events');

    } catch (error) {
      console.error('Failed to start event listening:', error);
    }
  }

  // معالجة أحداث البلوك تشين
  private handleBlockchainEvent(eventName: string, event: any): void {
    // البحث عن التأكيدات المعلقة التي تنتظر هذا الحدث
    for (const [confirmationId, confirmation] of this.pendingConfirmations) {
      if (confirmation.expectedEvent === eventName) {
        // التحقق من تطابق transaction hash إذا كان متاحاً
        if (confirmation.transactionHash && event.transactionHash !== confirmation.transactionHash) {
          continue;
        }

        confirmation.blockchainEvent = event;
        this.checkForCompletion(confirmationId);
      }
    }
  }

  // التحقق من اكتمال التأكيد
  private checkForCompletion(confirmationId: string): void {
    const confirmation = this.pendingConfirmations.get(confirmationId);
    if (!confirmation) return;

    const httpConfirmed = !!confirmation.httpResponse;
    const blockchainConfirmed = !!confirmation.blockchainEvent;
    const confirmationTime = Date.now() - confirmation.startTime;

    let status: ConfirmationResult['status'] = 'pending';

    if (httpConfirmed && blockchainConfirmed) {
      status = 'confirmed';
      this.completeConfirmation(confirmationId, {
        httpConfirmed: true,
        blockchainConfirmed: true,
        transactionHash: confirmation.transactionHash,
        blockNumber: confirmation.blockchainEvent?.blockNumber,
        confirmationTime,
        status
      });
    } else if (httpConfirmed || blockchainConfirmed) {
      status = 'partial';
      // انتظار التأكيد الثاني
      notificationService.info('تم تأكيد العملية جزئياً، انتظار التأكيد النهائي...');
    }
  }

  // إكمال التأكيد
  private completeConfirmation(confirmationId: string, result: ConfirmationResult): void {
    const confirmation = this.pendingConfirmations.get(confirmationId);
    if (!confirmation) return;

    this.pendingConfirmations.delete(confirmationId);
    confirmation.resolve(result);

    // إشعار المستخدم
    if (result.status === 'confirmed') {
      notificationService.success(`تم تأكيد العملية بنجاح على البلوك تشين في ${result.confirmationTime}ms`);
    }

    console.log('✅ Dual Confirmation completed:', result);
  }

  // معالجة انتهاء المهلة
  private handleTimeout(confirmationId: string): void {
    const confirmation = this.pendingConfirmations.get(confirmationId);
    if (!confirmation) return;

    const httpConfirmed = !!confirmation.httpResponse;
    const blockchainConfirmed = !!confirmation.blockchainEvent;
    const confirmationTime = Date.now() - confirmation.startTime;

    // تسجيل حالة الفشل
    failureTrackingService.logFailure({
      networkCondition: 'unknown',
      requestType: confirmation.type,
      stage: 'blockchain',
      errorType: 'network_timeout',
      errorMessage: `Dual confirmation timeout: HTTP=${httpConfirmed}, Blockchain=${blockchainConfirmed}`,
      requestUrl: 'blockchain_event',
      responseTime: confirmationTime,
      retryAttempt: 1,
      userAgent: navigator.userAgent,
      additionalData: {
        httpResponse: confirmation.httpResponse,
        expectedEvent: confirmation.expectedEvent
      }
    });

    if (httpConfirmed && !blockchainConfirmed) {
      // HTTP نجح لكن البلوك تشين لم يؤكد
      notificationService.warning('تم إرسال العملية لكن لم يتم تأكيدها على البلوك تشين بعد. قد تستغرق وقتاً إضافياً.');
      
      this.completeConfirmation(confirmationId, {
        httpConfirmed: true,
        blockchainConfirmed: false,
        confirmationTime,
        status: 'partial'
      });
    } else if (!httpConfirmed && blockchainConfirmed) {
      // البلوك تشين أكد لكن HTTP فشل
      this.completeConfirmation(confirmationId, {
        httpConfirmed: false,
        blockchainConfirmed: true,
        transactionHash: confirmation.transactionHash,
        blockNumber: confirmation.blockchainEvent?.blockNumber,
        confirmationTime,
        status: 'partial'
      });
    } else {
      // فشل كامل
      this.pendingConfirmations.delete(confirmationId);
      confirmation.reject(new Error('Dual confirmation timeout'));
    }
  }

  // معالجة خطأ HTTP
  private handleHttpError(confirmationId: string, error: any): void {
    const confirmation = this.pendingConfirmations.get(confirmationId);
    if (!confirmation) return;

    // تسجيل الخطأ
    failureTrackingService.logFailure({
      networkCondition: 'unknown',
      requestType: confirmation.type,
      stage: 'backend',
      errorType: 'server_error',
      errorMessage: error.message || 'HTTP operation failed',
      requestUrl: 'unknown',
      responseTime: Date.now() - confirmation.startTime,
      retryAttempt: 1,
      userAgent: navigator.userAgent,
      additionalData: { originalError: error }
    });

    // إذا لم يكن هناك تأكيد من البلوك تشين، فشل العملية
    if (!confirmation.blockchainEvent) {
      this.pendingConfirmations.delete(confirmationId);
      confirmation.reject(error);
    }
  }

  // دوال مساعدة للعمليات الشائعة
  public async createTradeWithConfirmation(tradeData: any): Promise<ConfirmationResult> {
    return this.executeWithDualConfirmation(
      () => fetch('/api/trades/create.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(tradeData)
      }).then(res => res.json()),
      'TradeCreated',
      'create_trade'
    );
  }

  public async joinTradeWithConfirmation(tradeId: string): Promise<ConfirmationResult> {
    return this.executeWithDualConfirmation(
      () => fetch(`/api/trades/${tradeId}/join.php`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      }).then(res => res.json()),
      'BuyerJoined',
      'join_trade'
    );
  }

  public async confirmPaymentWithConfirmation(tradeId: string): Promise<ConfirmationResult> {
    return this.executeWithDualConfirmation(
      () => fetch(`/api/trades/${tradeId}/confirm-payment.php`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      }).then(res => res.json()),
      'PaymentConfirmed',
      'confirm_payment'
    );
  }

  // إحصائيات التأكيد
  public getConfirmationStats(): {
    pendingCount: number;
    averageConfirmationTime: number;
    successRate: number;
  } {
    // يمكن تطوير هذا لاحقاً لحفظ الإحصائيات
    return {
      pendingCount: this.pendingConfirmations.size,
      averageConfirmationTime: 0,
      successRate: 0
    };
  }

  // دالة مساعدة لإنشاء ID
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // تنظيف الموارد
  public cleanup(): void {
    this.pendingConfirmations.clear();
    this.eventListeners.clear();
    this.isListening = false;
  }
}

// إنشاء مثيل واحد للخدمة
export const dualConfirmationService = new DualConfirmationService();
export default dualConfirmationService;
