<?php
/**
 * API endpoint لتسجيل الدخول
 * Login API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . '/../../config/database.php';

// التحقق من طريقة الطلب
validateRequestMethod(['POST']);

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/../../.env')) {
    $lines = file(__DIR__ . '/../../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

// مفتاح JWT من متغيرات البيئة
$jwtSecret = $_ENV['JWT_SECRET'] ?? 'default-secret-key';

/**
 * إنشاء JWT token
 */
function createJWT($payload, $secret) {
    $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
    $payload = json_encode($payload);

    $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
    $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

    $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $secret, true);
    $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

    return $base64Header . "." . $base64Payload . "." . $base64Signature;
}

try {
    // Get JSON input
    $input = getJsonInput();

    if (!$input) {
        sendErrorResponse('بيانات غير صحيحة');
    }
    
    $email = trim($input['email'] ?? '');
    $password = $input['password'] ?? '';
    
    // Validate input
    if (empty($email) || empty($password)) {
        sendErrorResponse('البريد الإلكتروني وكلمة المرور مطلوبان');
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        sendErrorResponse('البريد الإلكتروني غير صحيح');
    }
    
    // Get database connection
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // Check if user exists (البحث بالبريد الإلكتروني أو اسم المستخدم)
    $stmt = $connection->prepare("
        SELECT id, wallet_address, username, email, full_name, password_hash,
               is_verified, is_admin, is_active, rating, total_trades,
               completed_trades, total_volume, created_at, last_login
        FROM users
        WHERE (email = ? OR username = ?) AND is_active = 1
    ");

    $stmt->execute([$email, $email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        sendErrorResponse('البريد الإلكتروني أو كلمة المرور غير صحيحة');
    }

    // Verify password
    if (empty($user['password_hash'])) {
        sendErrorResponse('كلمة المرور غير محددة لهذا المستخدم');
    }

    if (!password_verify($password, $user['password_hash'])) {
        // تسجيل محاولة دخول فاشلة
        logError("Failed login attempt", [
            'email' => $user['email'],
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]);
        sendErrorResponse('البريد الإلكتروني أو كلمة المرور غير صحيحة');
    }
    
    // Update last login
    $updateStmt = $connection->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $updateStmt->execute([$user['id']]);
    
    // Log the login activity (تم تعطيله مؤقتاً حتى يتم إصلاح جدول activity_logs)
    try {
        // التحقق من وجود جدول activity_logs أولاً
        $checkTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
        $checkTable->execute();

        if ($checkTable->rowCount() > 0) {
            // التحقق من أعمدة الجدول
            $checkColumns = $connection->prepare("SHOW COLUMNS FROM activity_logs LIKE 'entity_type'");
            $checkColumns->execute();

            if ($checkColumns->rowCount() > 0) {
                $logStmt = $connection->prepare("
                    INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data)
                    VALUES (?, 'user_login', 'user', ?, ?, ?, ?)
                ");

                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                $loginData = json_encode([
                    'login_time' => date('Y-m-d H:i:s'),
                    'method' => 'email_password'
                ]);

                $logStmt->execute([$user['id'], $user['id'], $ipAddress, $userAgent, $loginData]);
            }
        }
    } catch (Exception $logError) {
        // تجاهل أخطاء تسجيل النشاط ومتابعة تسجيل الدخول
        error_log('Activity log error: ' . $logError->getMessage());
    }
    
    // Prepare user data for response (remove sensitive information)
    $userData = [
        'id' => $user['id'],
        'walletAddress' => $user['wallet_address'],
        'username' => $user['username'],
        'email' => $user['email'],
        'fullName' => $user['full_name'],
        'isVerified' => (bool)$user['is_verified'],
        'isAdmin' => (bool)$user['is_admin'],
        'rating' => (float)$user['rating'],
        'totalTrades' => (int)$user['total_trades'],
        'completedTrades' => (int)$user['completed_trades'],
        'totalVolume' => (float)$user['total_volume'],
        'joinDate' => $user['created_at'],
        'lastLogin' => date('Y-m-d H:i:s')
    ];

    // إنشاء JWT tokens
    $currentTime = time();
    $accessTokenExpiry = $currentTime + (15 * 60); // 15 دقيقة
    $refreshTokenExpiry = $currentTime + (7 * 24 * 60 * 60); // 7 أيام

    // رمز الوصول
    $accessTokenPayload = [
        'user_id' => $user['id'],
        'username' => $user['username'],
        'email' => $user['email'],
        'is_admin' => $user['is_admin'],
        'is_verified' => $user['is_verified'],
        'type' => 'access',
        'iat' => $currentTime,
        'exp' => $accessTokenExpiry
    ];

    // رمز التجديد
    $refreshTokenPayload = [
        'user_id' => $user['id'],
        'type' => 'refresh',
        'iat' => $currentTime,
        'exp' => $refreshTokenExpiry
    ];

    $accessToken = createJWT($accessTokenPayload, $jwtSecret);
    $refreshToken = createJWT($refreshTokenPayload, $jwtSecret);

    // إنشاء جلسة آمنة للمستخدم (للتوافق مع النظام القديم)
    require_once __DIR__ . '/../middleware/auth.php';
    $authMiddleware = new AuthMiddleware();

    $sessionType = $user["is_admin"] ? "admin" : "user";
    $expirationHours = $user["is_admin"] ? 8 : 24; // 8 ساعات للمدراء، 24 ساعة للمستخدمين

    $sessionToken = $authMiddleware->createSession($user["id"], $sessionType, $expirationHours);

    if (!$sessionToken) {
        sendErrorResponse('فشل في إنشاء الجلسة', 500);
    }

    // Success response with JWT tokens
    sendSuccessResponse([
        'user' => $userData,
        'token' => $sessionToken, // للتوافق مع النظام القديم
        'accessToken' => $accessToken,
        'refreshToken' => $refreshToken,
        'expiresIn' => 900, // 15 دقيقة بالثواني
        'tokenType' => 'Bearer'
    ], 'تم تسجيل الدخول بنجاح');

} catch (Exception $e) {
    logError("Login error: " . $e->getMessage(), [
        'input' => $input ?? null,
        'file' => __FILE__,
        'line' => __LINE__
    ]);
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    logError("Database error in login.php: " . $e->getMessage(), [
        'input' => $input ?? null,
        'file' => __FILE__,
        'line' => __LINE__
    ]);
    sendErrorResponse('خطأ في قاعدة البيانات', 500);
}
?>
