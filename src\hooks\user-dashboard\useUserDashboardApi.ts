'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  fetchUserStats,
  fetchWalletBalances,
  fetchWalletTransactions,
  fetchUserTrades,
  fetchUserOffers,
  fetchUserReviews,
  fetchRecentActivity,
  fetchUserProfile,
  UserStats,
  WalletBalance,
  WalletTransaction,
  UserTrade,
  UserOffer,
  UserReview,
  RecentActivity,
  UserProfile,
  UserStatsFilters,
  TradeFilters,
  OfferFilters
} from '@/utils/userDashboardApi';

interface UseUserDashboardApiOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseUserDashboardApiReturn {
  // البيانات
  stats: UserStats | null;
  walletBalances: WalletBalance[];
  walletTransactions: WalletTransaction[];
  trades: UserTrade[];
  offers: UserOffer[];
  reviews: UserReview[];
  recentActivity: RecentActivity[];
  profile: UserProfile | null;

  // حالات التحميل
  loading: {
    stats: boolean;
    wallet: boolean;
    trades: boolean;
    offers: boolean;
    reviews: boolean;
    activity: boolean;
    profile: boolean;
  };

  // الأخطاء
  errors: {
    stats: string | null;
    wallet: string | null;
    trades: string | null;
    offers: string | null;
    reviews: string | null;
    activity: string | null;
    profile: string | null;
  };

  // دوال التحديث
  refreshStats: (filters?: UserStatsFilters) => Promise<void>;
  refreshWallet: () => Promise<void>;
  refreshTrades: (filters?: TradeFilters) => Promise<void>;
  refreshOffers: (filters?: OfferFilters) => Promise<void>;
  refreshReviews: (type?: 'received' | 'given') => Promise<void>;
  refreshActivity: () => Promise<void>;
  refreshProfile: () => Promise<void>;
  refreshAll: () => Promise<void>;

  // معلومات الصفحات
  pagination: {
    trades: { currentPage: number; totalPages: number; hasNextPage: boolean };
    offers: { currentPage: number; totalPages: number; hasNextPage: boolean };
  };
}

/**
 * Hook موحد لإدارة جميع APIs في داشبورد المستخدم
 */
export function useUserDashboardApi(options: UseUserDashboardApiOptions = {}): UseUserDashboardApiReturn {
  const { user } = useAuth();
  const { autoRefresh = false, refreshInterval = 5 * 60 * 1000 } = options;

  // البيانات
  const [stats, setStats] = useState<UserStats | null>(null);
  const [walletBalances, setWalletBalances] = useState<WalletBalance[]>([]);
  const [walletTransactions, setWalletTransactions] = useState<WalletTransaction[]>([]);
  const [trades, setTrades] = useState<UserTrade[]>([]);
  const [offers, setOffers] = useState<UserOffer[]>([]);
  const [reviews, setReviews] = useState<UserReview[]>([]);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [profile, setProfile] = useState<UserProfile | null>(null);

  // حالات التحميل
  const [loading, setLoading] = useState({
    stats: false,
    wallet: false,
    trades: false,
    offers: false,
    reviews: false,
    activity: false,
    profile: false
  });

  // الأخطاء
  const [errors, setErrors] = useState({
    stats: null as string | null,
    wallet: null as string | null,
    trades: null as string | null,
    offers: null as string | null,
    reviews: null as string | null,
    activity: null as string | null,
    profile: null as string | null
  });

  // معلومات الصفحات
  const [pagination, setPagination] = useState({
    trades: { currentPage: 1, totalPages: 1, hasNextPage: false },
    offers: { currentPage: 1, totalPages: 1, hasNextPage: false }
  });

  // دالة مساعدة لتحديث حالة التحميل
  const updateLoading = useCallback((key: keyof typeof loading, value: boolean) => {
    setLoading(prev => ({ ...prev, [key]: value }));
  }, []);

  // دالة مساعدة لتحديث الأخطاء
  const updateError = useCallback((key: keyof typeof errors, value: string | null) => {
    setErrors(prev => ({ ...prev, [key]: value }));
  }, []);

  // جلب الإحصائيات
  const refreshStats = useCallback(async (filters?: UserStatsFilters) => {
    if (!user?.id) return;

    updateLoading('stats', true);
    updateError('stats', null);

    try {
      const data = await fetchUserStats(user.id.toString(), filters);
      setStats(data);
    } catch (error) {
      updateError('stats', error instanceof Error ? error.message : 'خطأ في جلب الإحصائيات');
    } finally {
      updateLoading('stats', false);
    }
  }, [user?.id, updateLoading, updateError]);

  // جلب بيانات المحفظة
  const refreshWallet = useCallback(async () => {
    if (!user?.id) return;

    updateLoading('wallet', true);
    updateError('wallet', null);

    try {
      const [balances, transactions] = await Promise.all([
        fetchWalletBalances(user.id.toString()),
        fetchWalletTransactions(user.id.toString(), 10)
      ]);
      setWalletBalances(balances);
      setWalletTransactions(transactions);
    } catch (error) {
      updateError('wallet', error instanceof Error ? error.message : 'خطأ في جلب بيانات المحفظة');
    } finally {
      updateLoading('wallet', false);
    }
  }, [user?.id, updateLoading, updateError]);

  // جلب الصفقات
  const refreshTrades = useCallback(async (filters?: TradeFilters) => {
    if (!user?.id) return;

    updateLoading('trades', true);
    updateError('trades', null);

    try {
      const data = await fetchUserTrades(user.id.toString(), filters);
      setTrades(data.trades);
      setPagination(prev => ({
        ...prev,
        trades: {
          currentPage: data.currentPage,
          totalPages: data.totalPages,
          hasNextPage: data.hasNextPage
        }
      }));
    } catch (error) {
      updateError('trades', error instanceof Error ? error.message : 'خطأ في جلب الصفقات');
    } finally {
      updateLoading('trades', false);
    }
  }, [user?.id, updateLoading, updateError]);

  // جلب العروض
  const refreshOffers = useCallback(async (filters?: OfferFilters) => {
    if (!user?.id) return;

    updateLoading('offers', true);
    updateError('offers', null);

    try {
      const data = await fetchUserOffers(user.id.toString(), filters);
      setOffers(data.offers);
      setPagination(prev => ({
        ...prev,
        offers: {
          currentPage: data.currentPage,
          totalPages: data.totalPages,
          hasNextPage: data.hasNextPage
        }
      }));
    } catch (error) {
      updateError('offers', error instanceof Error ? error.message : 'خطأ في جلب العروض');
    } finally {
      updateLoading('offers', false);
    }
  }, [user?.id, updateLoading, updateError]);

  // جلب التقييمات
  const refreshReviews = useCallback(async (type: 'received' | 'given' = 'received') => {
    if (!user?.id) return;

    updateLoading('reviews', true);
    updateError('reviews', null);

    try {
      const data = await fetchUserReviews(user.id.toString(), type);
      setReviews(data);
    } catch (error) {
      updateError('reviews', error instanceof Error ? error.message : 'خطأ في جلب التقييمات');
    } finally {
      updateLoading('reviews', false);
    }
  }, [user?.id, updateLoading, updateError]);

  // جلب النشاط الحديث
  const refreshActivity = useCallback(async () => {
    if (!user?.id) return;

    updateLoading('activity', true);
    updateError('activity', null);

    try {
      const data = await fetchRecentActivity(user.id.toString());
      setRecentActivity(data);
    } catch (error) {
      updateError('activity', error instanceof Error ? error.message : 'خطأ في جلب النشاط الحديث');
    } finally {
      updateLoading('activity', false);
    }
  }, [user?.id, updateLoading, updateError]);

  // جلب الملف الشخصي
  const refreshProfile = useCallback(async () => {
    if (!user?.id) return;

    updateLoading('profile', true);
    updateError('profile', null);

    try {
      const data = await fetchUserProfile(user.id.toString());
      setProfile(data);
    } catch (error) {
      updateError('profile', error instanceof Error ? error.message : 'خطأ في جلب الملف الشخصي');
    } finally {
      updateLoading('profile', false);
    }
  }, [user?.id, updateLoading, updateError]);

  // تحديث جميع البيانات
  const refreshAll = useCallback(async () => {
    await Promise.all([
      refreshStats(),
      refreshWallet(),
      refreshTrades(),
      refreshOffers(),
      refreshReviews(),
      refreshActivity(),
      refreshProfile()
    ]);
  }, [refreshStats, refreshWallet, refreshTrades, refreshOffers, refreshReviews, refreshActivity, refreshProfile]);

  // التحميل الأولي
  useEffect(() => {
    if (user?.id) {
      refreshAll();
    }
  }, [user?.id, refreshAll]);

  // التحديث التلقائي
  useEffect(() => {
    if (!autoRefresh || !user?.id) return;

    const interval = setInterval(() => {
      refreshAll();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, user?.id, refreshAll]);

  return {
    // البيانات
    stats,
    walletBalances,
    walletTransactions,
    trades,
    offers,
    reviews,
    recentActivity,
    profile,

    // حالات التحميل
    loading,

    // الأخطاء
    errors,

    // دوال التحديث
    refreshStats,
    refreshWallet,
    refreshTrades,
    refreshOffers,
    refreshReviews,
    refreshActivity,
    refreshProfile,
    refreshAll,

    // معلومات الصفحات
    pagination
  };
}

export default useUserDashboardApi;
