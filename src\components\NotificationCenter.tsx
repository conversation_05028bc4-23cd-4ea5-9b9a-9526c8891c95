'use client';

import React, { useState, useEffect } from 'react';
import {
  Bell,
  X,
  AlertTriangle,
  Info,
  CheckCircle,
  DollarSign,
  Users,
  Shield,
  Settings,
  Eye,
  Trash2,
  Search
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { smartNotificationService, SmartNotificationData, NotificationStats } from '@/services/smartNotificationService';

export interface Notification {
  id: string;
  type: 'trade' | 'security' | 'system' | 'payment' | 'kyc' | 'admin';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  message: string;
  timestamp: Date | string;
  isRead: boolean;
  actionUrl?: string;
  actionLabel?: string;
  data?: any;
}

interface NotificationCenterProps {
  isOpen: boolean;
  onClose: () => void;
  notifications: Notification[];
  onMarkAsRead: (id: string) => void;
  onMarkAllAsRead: () => void;
  onDeleteNotification: (id: string) => void;
  onNotificationAction?: (notification: Notification) => void;
}

export default function NotificationCenter({
  isOpen,
  onClose,
  notifications,
  onMarkAsRead,
  onMarkAllAsRead,
  onDeleteNotification,
  onNotificationAction
}: NotificationCenterProps) {
  const { t } = useTranslation();
  const [filter, setFilter] = useState<'all' | 'unread' | 'trade' | 'security' | 'system'>('all');
  const [searchQuery, setSearchQuery] = useState('');

  const getNotificationIcon = (type: string, priority: string) => {
    const baseClass = 'w-8 h-8 p-1.5 rounded-full';
    const iconClass = 'w-full h-full';

    const getColorClasses = () => {
      switch (priority) {
        case 'urgent':
          return 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400';
        case 'high':
          return 'bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400';
        case 'medium':
          return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400';
        default:
          return 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400';
      }
    };

    const colorClasses = getColorClasses();

    switch (type) {
      case 'trade':
        return <div className={`${baseClass} ${colorClasses}`}><DollarSign className={iconClass} /></div>;
      case 'security':
        return <div className={`${baseClass} ${colorClasses}`}><Shield className={iconClass} /></div>;
      case 'system':
        return <div className={`${baseClass} ${colorClasses}`}><Settings className={iconClass} /></div>;
      case 'payment':
        return <div className={`${baseClass} ${colorClasses}`}><CheckCircle className={iconClass} /></div>;
      case 'kyc':
        return <div className={`${baseClass} ${colorClasses}`}><Users className={iconClass} /></div>;
      case 'admin':
        return <div className={`${baseClass} ${colorClasses}`}><AlertTriangle className={iconClass} /></div>;
      default:
        return <div className={`${baseClass} ${colorClasses}`}><Info className={iconClass} /></div>;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-r-4 border-red-500 bg-red-50 dark:bg-red-900/20';
      case 'high':
        return 'border-r-4 border-orange-500 bg-orange-50 dark:bg-orange-900/20';
      case 'medium':
        return 'border-r-4 border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      default:
        return 'border-r-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20';
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    const matchesFilter = filter === 'all' || 
      (filter === 'unread' && !notification.isRead) ||
      notification.type === filter;
    
    const matchesSearch = !searchQuery || 
      notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const formatTimeAgo = (date: Date | string | undefined | null) => {
    if (!date) return t('notifications.justNow');

    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (!dateObj || isNaN(dateObj.getTime())) return t('notifications.justNow');

    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - dateObj.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return t('notifications.justNow');
    if (diffInMinutes < 60) return t('notifications.minutesAgo', { minutes: diffInMinutes });

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return t('notifications.hoursAgo', { hours: diffInHours });

    const diffInDays = Math.floor(diffInHours / 24);
    return t('notifications.daysAgo', { days: diffInDays });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* خلفية شفافة */}
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      {/* لوحة الإشعارات */}
      <div className="absolute top-0 right-0 rtl:left-0 ltr:right-0 h-full w-full max-w-md bg-white dark:bg-gray-800 shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="relative">
              <Bell className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </span>
              )}
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                الإشعارات
              </h2>
              {unreadCount > 0 ? (
                <p className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                  {unreadCount} إشعار غير مقروء
                </p>
              ) : (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  جميع الإشعارات مقروءة
                </p>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-white/50 dark:hover:bg-gray-600 transition-all"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* أدوات التحكم */}
        <div className="p-4 space-y-3 border-b border-gray-200 dark:border-gray-700">
          {/* شريط البحث */}
          <div className="relative">
            <Search className="absolute right-3 rtl:right-3 ltr:left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={t('notifications.searchPlaceholder')}
              className="w-full pl-10 pr-4 rtl:pl-4 rtl:pr-10 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>

          {/* فلاتر */}
          <div className="flex items-center justify-between">
            <div className="flex space-x-2 rtl:space-x-reverse">
              {['all', 'unread', 'trade', 'security', 'system'].map((filterType) => (
                <button
                  key={filterType}
                  onClick={() => setFilter(filterType as any)}
                  className={`px-3 py-1 text-xs rounded-full transition-colors ${
                    filter === filterType
                      ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  {t(`notifications.filters.${filterType}`)}
                </button>
              ))}
            </div>

            {unreadCount > 0 && (
              <button
                onClick={onMarkAllAsRead}
                className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
              >
                {t('notifications.markAllRead')}
              </button>
            )}
          </div>
        </div>

        {/* قائمة الإشعارات */}
        <div className="flex-1 overflow-y-auto">
          {filteredNotifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400">
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                <Bell className="w-8 h-8 opacity-50" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {searchQuery ? 'لا توجد نتائج' : 'لا توجد إشعارات'}
              </h3>
              <p className="text-center text-sm">
                {searchQuery
                  ? 'جرب البحث بكلمات مختلفة'
                  : 'ستظهر الإشعارات الجديدة هنا'
                }
              </p>
            </div>
          ) : (
            <div className="space-y-2 p-3">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`relative p-4 rounded-xl transition-all duration-200 hover:shadow-md cursor-pointer ${
                    !notification.isRead
                      ? 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-r-4 border-blue-500 shadow-sm'
                      : 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-start space-x-3 rtl:space-x-reverse">
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification.type, notification.priority)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <h3 className={`text-sm font-medium ${
                          !notification.isRead ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'
                        }`}>
                          {notification.title}
                        </h3>
                        
                        <div className="flex items-center space-x-1 rtl:space-x-reverse ml-2">
                          {!notification.isRead && (
                            <button
                              onClick={() => onMarkAsRead(notification.id)}
                              className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                              title={t('notifications.markAsRead')}
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                          )}
                          <button
                            onClick={() => onDeleteNotification(notification.id)}
                            className="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                            title={t('notifications.delete')}
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                      
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {notification.message}
                      </p>
                      
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {formatTimeAgo(notification.timestamp)}
                        </span>
                        
                        {notification.actionUrl && notification.actionLabel && (
                          <button
                            onClick={() => onNotificationAction?.(notification)}
                            className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
                          >
                            {notification.actionLabel}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={() => {/* فتح صفحة الإشعارات الكاملة */}}
            className="w-full text-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
          >
            {t('notifications.viewAll')}
          </button>
        </div>
      </div>
    </div>
  );
}
