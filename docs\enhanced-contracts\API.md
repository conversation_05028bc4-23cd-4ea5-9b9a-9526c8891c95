# توثيق APIs للعقود المحسنة

## نظرة عامة

هذا التوثيق يغطي جميع نقاط النهاية (endpoints) للـ APIs الخاصة بالعقود الذكية المحسنة.

## Base URL

```
http://localhost/api/enhanced-contracts/
```

## المصادقة

جميع APIs تتطلب مصادقة المستخدم عبر session أو JWT token.

```http
Authorization: Bearer <token>
```

## نقاط النهاية

### 1. إدارة الشبكات والعملات

#### جلب الشبكات المدعومة

```http
GET /network-management.php?action=list
```

**المعاملات:**
- `testnet` (optional): `1` للشبكات التجريبية، `0` للرئيسية
- `active` (optional): `1` للشبكات النشطة فقط

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "networks": [
      {
        "id": 1,
        "network_name": "BSC Testnet",
        "network_symbol": "tBNB",
        "chain_id": 97,
        "rpc_url": "https://data-seed-prebsc-1-s1.binance.org:8545/",
        "explorer_url": "https://testnet.bscscan.com/",
        "is_testnet": true,
        "is_active": true,
        "gas_price_gwei": 5,
        "block_time_seconds": 3,
        "confirmation_blocks": 3
      }
    ],
    "count": 1
  }
}
```

#### جلب العملات المدعومة

```http
GET /token-management.php?action=list
```

**المعاملات:**
- `network_id` (optional): معرف الشبكة
- `stablecoin` (optional): `1` للعملات المستقرة فقط
- `active` (optional): `1` للعملات النشطة فقط

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "tokens": [
      {
        "id": 1,
        "network_id": 1,
        "token_address": "******************************************",
        "token_symbol": "USDT",
        "token_name": "Tether USD",
        "decimals": 18,
        "is_stablecoin": true,
        "is_active": true,
        "icon_url": "/tokens/usdt.svg",
        "min_trade_amount": 1.0,
        "max_trade_amount": 1000000.0,
        "platform_fee_rate": 0.0050
      }
    ],
    "count": 1
  }
}
```

#### جلب عناوين العقود

```http
GET /contract-verification.php?action=supported-networks
```

**المعاملات:**
- `network_id` (optional): معرف الشبكة
- `contract_type` (optional): نوع العقد
- `active` (optional): `1` للعقود النشطة فقط

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "contracts": [
      {
        "id": 1,
        "network_id": 1,
        "contract_type": "core_escrow",
        "contract_address": "******************************************",
        "is_active": true,
        "deployed_at": "2024-01-15 10:30:00",
        "gas_limit": 500000
      }
    ],
    "count": 1
  }
}
```

### 2. إدارة العروض

#### إنشاء عرض جديد

```http
POST /offers.php
```

**البيانات المرسلة:**
```json
{
  "userId": 1,
  "offerType": "sell",
  "networkId": 1,
  "tokenId": 1,
  "amount": "100",
  "minAmount": "10",
  "maxAmount": "100",
  "price": "3.75",
  "currency": "SAR",
  "paymentMethods": ["bank_transfer", "cash"],
  "terms": "شروط العرض",
  "autoReply": "رد تلقائي",
  "timeLimit": 1800
}
```

**الاستجابة:**
```json
{
  "success": true,
  "message": "تم إنشاء العرض بنجاح",
  "data": {
    "offer_id": 123,
    "blockchain_trade_id": 456,
    "transaction_hash": "0xabcdef1234567890"
  }
}
```

#### جلب العروض

```http
GET /offers.php
```

**المعاملات:**
- `user_id` (optional): معرف المستخدم
- `network_id` (optional): معرف الشبكة
- `token_id` (optional): معرف العملة
- `offer_type` (optional): `buy` أو `sell`
- `is_active` (optional): `1` للعروض النشطة فقط
- `page` (optional): رقم الصفحة (افتراضي: 1)
- `limit` (optional): عدد النتائج (افتراضي: 20)

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "offers": [
      {
        "id": 123,
        "user_id": 1,
        "offer_type": "sell",
        "network_id": 1,
        "token_id": 1,
        "amount": 100.0,
        "price": 3.75,
        "currency": "SAR",
        "is_active": true,
        "blockchain_trade_id": 456,
        "contract_status": "created",
        "sync_status": "synced",
        "created_at": "2024-01-15 10:30:00"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_offers": 100,
      "per_page": 20
    }
  }
}
```

### 3. إدارة الصفقات

#### إنشاء صفقة جديدة

```http
POST /trades.php
```

**البيانات المرسلة:**
```json
{
  "offerId": 123,
  "buyerId": 2,
  "amount": "50",
  "paymentMethod": "bank_transfer"
}
```

**الاستجابة:**
```json
{
  "success": true,
  "message": "تم إنشاء الصفقة بنجاح",
  "data": {
    "trade_id": 789,
    "blockchain_trade_id": 456,
    "transaction_hash": "0xabcdef1234567890"
  }
}
```

#### جلب الصفقات

```http
GET /trades.php
```

**المعاملات:**
- `user_id` (optional): معرف المستخدم
- `trade_id` (optional): معرف الصفقة
- `status` (optional): حالة الصفقة
- `network_id` (optional): معرف الشبكة
- `page` (optional): رقم الصفحة
- `limit` (optional): عدد النتائج

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "trades": [
      {
        "id": 789,
        "blockchain_trade_id": 456,
        "seller_id": 1,
        "buyer_id": 2,
        "network_id": 1,
        "token_id": 1,
        "amount": 50.0,
        "price": 3.75,
        "currency": "SAR",
        "total_value": 187.5,
        "platform_fee": 0.25,
        "status": "joined",
        "contract_status": "Joined",
        "payment_method": "bank_transfer",
        "join_transaction_hash": "0xabcdef1234567890",
        "created_at": "2024-01-15 11:00:00"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 3,
      "total_trades": 50,
      "per_page": 20
    }
  }
}
```

### 4. إدارة الأحداث

#### جلب أحداث العقد الذكي

```http
GET /contract-events.php
```

**المعاملات:**
- `network_id` (optional): معرف الشبكة
- `contract_type` (optional): نوع العقد
- `event_type` (optional): نوع الحدث
- `processed` (optional): `1` للأحداث المعالجة، `0` للمعلقة
- `page` (optional): رقم الصفحة
- `limit` (optional): عدد النتائج

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "events": [
      {
        "id": 1,
        "network_id": 1,
        "contract_type": "core_escrow",
        "contract_address": "******************************************",
        "event_type": "TradeCreated",
        "blockchain_trade_id": 456,
        "transaction_hash": "0xabcdef1234567890",
        "block_number": 12345,
        "processed": true,
        "event_timestamp": "2024-01-15 10:30:00",
        "created_at": "2024-01-15 10:30:05"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 10,
      "total_events": 200,
      "per_page": 20
    }
  }
}
```

#### إنشاء حدث جديد

```http
POST /contract-events.php
```

**البيانات المرسلة:**
```json
{
  "networkId": 1,
  "contractType": "core_escrow",
  "contractAddress": "******************************************",
  "eventType": "TradeCreated",
  "blockchainTradeId": 456,
  "transactionHash": "0xabcdef1234567890",
  "blockNumber": 12345,
  "eventData": {
    "seller": "0x1234...",
    "token": "0x7ef9...",
    "amount": "100000000000000000000"
  }
}
```

### 5. مزامنة البيانات

#### جلب حالة المزامنة

```http
GET /sync.php?action=status
```

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "sync_status": {
      "offers": {
        "total": 100,
        "synced": 95,
        "pending": 3,
        "failed": 2,
        "sync_rate": 95.0
      },
      "trades": {
        "total": 50,
        "synced": 48,
        "pending": 1,
        "failed": 1,
        "sync_rate": 96.0
      },
      "events": {
        "total": 200,
        "processed": 195,
        "pending": 3,
        "failed": 2,
        "processing_rate": 97.5
      }
    },
    "timestamp": "2024-01-15 12:00:00"
  }
}
```

#### مزامنة عرض مع العقد الذكي

```http
POST /sync.php
```

**البيانات المرسلة:**
```json
{
  "action": "sync_offer",
  "offer_id": 123,
  "blockchain_trade_id": 456,
  "transaction_hash": "0xabcdef1234567890",
  "contract_status": "created"
}
```

#### مزامنة صفقة مع العقد الذكي

```http
POST /sync.php
```

**البيانات المرسلة:**
```json
{
  "action": "sync_trade",
  "trade_id": 789,
  "blockchain_trade_id": 456,
  "transaction_hash": "0xabcdef1234567890",
  "contract_status": "Joined",
  "event_type": "join"
}
```

## رموز الأخطاء

| الرمز | الوصف |
|-------|--------|
| 200 | نجح الطلب |
| 400 | طلب غير صحيح |
| 401 | غير مصرح |
| 403 | ممنوع |
| 404 | غير موجود |
| 422 | بيانات غير صحيحة |
| 500 | خطأ في الخادم |

## أمثلة الاستخدام

### JavaScript/TypeScript

```typescript
// جلب الشبكات المدعومة
const networks = await fetch('/api/enhanced-contracts/networks.php?action=networks&active=1')
  .then(res => res.json());

// إنشاء عرض جديد
const offer = await fetch('/api/enhanced-contracts/offers.php', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    userId: 1,
    offerType: 'sell',
    networkId: 1,
    tokenId: 1,
    amount: '100',
    price: '3.75',
    currency: 'SAR',
    paymentMethods: ['bank_transfer']
  })
}).then(res => res.json());
```

### PHP

```php
// جلب العملات المدعومة
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/api/enhanced-contracts/networks.php?action=tokens&network_id=1');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $token
]);
$response = curl_exec($ch);
$tokens = json_decode($response, true);
curl_close($ch);
```

## معدل الطلبات

- **المستخدمون العاديون**: 100 طلب/دقيقة
- **المديرون**: 1000 طلب/دقيقة
- **APIs العامة**: 50 طلب/دقيقة

## الإصدارات

- **v1.0**: الإصدار الأولي
- **v1.1**: دعم متعدد الشبكات
- **v1.2**: نظام السمعة المحسن
- **v2.0**: العقود الذكية المحسنة (الحالي)
