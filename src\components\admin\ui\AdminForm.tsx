'use client';

import React, { useState } from 'react';
import {
  Save,
  X,
  Eye,
  EyeOff,
  Upload,
  Calendar,
  Clock,
  Check,
  AlertCircle,
  Info,
  Plus,
  Minus,
  Copy
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file' | 'date' | 'datetime' | 'time' | 'url' | 'tel' | 'color' | 'range';
  placeholder?: string;
  value?: any;
  defaultValue?: any;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
  options?: Array<{ value: any; label: string; disabled?: boolean }>;
  validation?: {
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    custom?: (value: any) => string | null;
  };
  description?: string;
  prefix?: string;
  suffix?: string;
  multiple?: boolean;
  accept?: string;
  rows?: number;
  cols?: number;
  step?: number;
  className?: string;
}

export interface AdminFormProps {
  fields: FormField[];
  onSubmit: (data: Record<string, any>) => void | Promise<void>;
  onCancel?: () => void;
  title?: string;
  subtitle?: string;
  submitLabel?: string;
  cancelLabel?: string;
  loading?: boolean;
  disabled?: boolean;
  layout?: 'vertical' | 'horizontal' | 'grid';
  gridCols?: number;
  showRequiredIndicator?: boolean;
  validateOnChange?: boolean;
  className?: string;
}

export default function AdminForm({
  fields,
  onSubmit,
  onCancel,
  title,
  subtitle,
  submitLabel,
  cancelLabel,
  loading = false,
  disabled = false,
  layout = 'vertical',
  gridCols = 2,
  showRequiredIndicator = true,
  validateOnChange = true,
  className = ''
}: AdminFormProps) {
  const { t, isRTL } = useAdminTranslation();
  
  const [formData, setFormData] = useState<Record<string, any>>(() => {
    const initialData: Record<string, any> = {};
    fields.forEach(field => {
      initialData[field.name] = field.defaultValue || field.value || '';
    });
    return initialData;
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const validateField = (field: FormField, value: any): string | null => {
    if (field.required && (!value || (typeof value === 'string' && !value.trim()))) {
      return t('validation.required');
    }

    if (field.validation) {
      const { min, max, minLength, maxLength, pattern, custom } = field.validation;

      if (typeof value === 'string') {
        if (minLength && value.length < minLength) {
          return `Minimum length is ${minLength}`;
        }
        if (maxLength && value.length > maxLength) {
          return `Maximum length is ${maxLength}`;
        }
        if (pattern && !new RegExp(pattern).test(value)) {
          return 'Invalid format';
        }
      }

      if (typeof value === 'number') {
        if (min !== undefined && value < min) {
          return `Minimum value is ${min}`;
        }
        if (max !== undefined && value > max) {
          return `Maximum value is ${max}`;
        }
      }

      if (custom) {
        const customError = custom(value);
        if (customError) return customError;
      }
    }

    return null;
  };

  const handleFieldChange = (field: FormField, value: any) => {
    setFormData(prev => ({ ...prev, [field.name]: value }));
    setTouched(prev => ({ ...prev, [field.name]: true }));

    if (validateOnChange) {
      const error = validateField(field, value);
      setErrors(prev => ({ ...prev, [field.name]: error || '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate all fields
    const newErrors: Record<string, string> = {};
    fields.forEach(field => {
      const error = validateField(field, formData[field.name]);
      if (error) {
        newErrors[field.name] = error;
      }
    });

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      await onSubmit(formData);
    }
  };

  const togglePasswordVisibility = (fieldName: string) => {
    setShowPasswords(prev => ({ ...prev, [fieldName]: !prev[fieldName] }));
  };

  const renderField = (field: FormField) => {
    const hasError = errors[field.name] && touched[field.name];
    const fieldValue = formData[field.name] || '';

    const baseInputClasses = `w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-colors ${
      hasError 
        ? 'border-red-500 dark:border-red-400 focus:ring-red-500 focus:border-red-500' 
        : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500'
    } ${field.disabled || disabled ? 'opacity-50 cursor-not-allowed' : ''} ${
      isRTL ? 'text-right' : 'text-left'
    }`;

    switch (field.type) {
      case 'textarea':
        return (
          <textarea
            value={fieldValue}
            onChange={(e) => handleFieldChange(field, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
            disabled={field.disabled || disabled}
            readOnly={field.readonly}
            rows={field.rows || 3}
            className={`${baseInputClasses} resize-vertical`}
          />
        );

      case 'select':
        return (
          <select
            value={fieldValue}
            onChange={(e) => handleFieldChange(field, e.target.value)}
            required={field.required}
            disabled={field.disabled || disabled}
            className={baseInputClasses}
          >
            <option value="">{field.placeholder || 'Select an option'}</option>
            {field.options?.map(option => (
              <option 
                key={option.value} 
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'checkbox':
        return (
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={fieldValue}
              onChange={(e) => handleFieldChange(field, e.target.checked)}
              disabled={field.disabled || disabled}
              className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
            />
            <label className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
              {field.label}
            </label>
          </div>
        );

      case 'radio':
        return (
          <div className="space-y-2">
            {field.options?.map(option => (
              <div key={option.value} className="flex items-center">
                <input
                  type="radio"
                  name={field.name}
                  value={option.value}
                  checked={fieldValue === option.value}
                  onChange={(e) => handleFieldChange(field, e.target.value)}
                  disabled={field.disabled || disabled || option.disabled}
                  className="border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
                />
                <label className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        );

      case 'file':
        return (
          <div className="space-y-2">
            <input
              type="file"
              onChange={(e) => handleFieldChange(field, e.target.files)}
              accept={field.accept}
              multiple={field.multiple}
              disabled={field.disabled || disabled}
              className="hidden"
              id={`file-${field.name}`}
            />
            <label
              htmlFor={`file-${field.name}`}
              className={`flex items-center justify-center px-4 py-2 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:border-blue-500 dark:hover:border-blue-400 transition-colors ${
                field.disabled || disabled ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <Upload className={`w-5 h-5 text-gray-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {field.placeholder || 'Choose file'}
              </span>
            </label>
            {fieldValue && fieldValue.length > 0 && (
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {Array.from(fieldValue).map((file: any, index: number) => (
                  <div key={index}>{file.name}</div>
                ))}
              </div>
            )}
          </div>
        );

      case 'password':
        return (
          <div className="relative">
            <input
              type={showPasswords[field.name] ? 'text' : 'password'}
              value={fieldValue}
              onChange={(e) => handleFieldChange(field, e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
              disabled={field.disabled || disabled}
              readOnly={field.readonly}
              className={`${baseInputClasses} ${isRTL ? 'pl-10 pr-3' : 'pr-10 pl-3'}`}
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility(field.name)}
              className={`absolute top-1/2 transform -translate-y-1/2 ${isRTL ? 'left-3' : 'right-3'} text-gray-400 hover:text-gray-600 dark:hover:text-gray-300`}
            >
              {showPasswords[field.name] ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </button>
          </div>
        );

      case 'range':
        return (
          <div className="space-y-2">
            <input
              type="range"
              value={fieldValue}
              onChange={(e) => handleFieldChange(field, Number(e.target.value))}
              min={field.validation?.min}
              max={field.validation?.max}
              step={field.step}
              disabled={field.disabled || disabled}
              className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
            />
            <div className={`flex justify-between text-sm text-gray-600 dark:text-gray-400 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <span>{field.validation?.min || 0}</span>
              <span className="font-medium">{fieldValue}</span>
              <span>{field.validation?.max || 100}</span>
            </div>
          </div>
        );

      default:
        return (
          <div className="relative">
            {field.prefix && (
              <span className={`absolute top-1/2 transform -translate-y-1/2 ${isRTL ? 'right-3' : 'left-3'} text-gray-500 dark:text-gray-400`}>
                {field.prefix}
              </span>
            )}
            <input
              type={field.type}
              value={fieldValue}
              onChange={(e) => handleFieldChange(field, field.type === 'number' ? Number(e.target.value) : e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
              disabled={field.disabled || disabled}
              readOnly={field.readonly}
              min={field.validation?.min}
              max={field.validation?.max}
              minLength={field.validation?.minLength}
              maxLength={field.validation?.maxLength}
              pattern={field.validation?.pattern}
              step={field.step}
              className={`${baseInputClasses} ${
                field.prefix ? (isRTL ? 'pr-8' : 'pl-8') : ''
              } ${
                field.suffix ? (isRTL ? 'pl-8' : 'pr-8') : ''
              }`}
            />
            {field.suffix && (
              <span className={`absolute top-1/2 transform -translate-y-1/2 ${isRTL ? 'left-3' : 'right-3'} text-gray-500 dark:text-gray-400`}>
                {field.suffix}
              </span>
            )}
          </div>
        );
    }
  };

  const getLayoutClasses = () => {
    switch (layout) {
      case 'horizontal':
        return 'grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4';
      case 'grid':
        return `grid grid-cols-1 md:grid-cols-${gridCols} gap-4`;
      default:
        return 'space-y-4';
    }
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      {(title || subtitle) && (
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          {title && (
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
          )}
          {subtitle && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{subtitle}</p>
          )}
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="p-6">
        <div className={getLayoutClasses()}>
          {fields.map(field => (
            <div key={field.name} className={field.className || ''}>
              {field.type !== 'checkbox' && (
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {field.label}
                  {field.required && showRequiredIndicator && (
                    <span className="text-red-500 ml-1">*</span>
                  )}
                </label>
              )}
              
              {renderField(field)}
              
              {field.description && (
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  {field.description}
                </p>
              )}
              
              {errors[field.name] && touched[field.name] && (
                <p className={`mt-1 text-xs text-red-500 dark:text-red-400 flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <AlertCircle className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                  {errors[field.name]}
                </p>
              )}
            </div>
          ))}
        </div>

        {/* Actions */}
        <div className={`mt-6 pt-6 border-t border-gray-200 dark:border-gray-600 flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button
            type="submit"
            disabled={loading || disabled}
            className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
          >
            {loading ? (
              <div className={`animate-spin rounded-full h-4 w-4 border-b-2 border-white ${isRTL ? 'ml-2' : 'mr-2'}`} />
            ) : (
              <Save className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            )}
            {submitLabel || t('common.actions.save')}
          </button>
          
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              disabled={loading}
              className="flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
            >
              <X className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {cancelLabel || t('common.actions.cancel')}
            </button>
          )}
        </div>
      </form>
    </div>
  );
}
