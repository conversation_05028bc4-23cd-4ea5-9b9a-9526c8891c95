<?php
/**
 * API endpoint لإدارة الإشعارات
 * Notifications Management API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = new Database();
    $connection = $db->connect();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // جلب الإشعارات
        $userId = $_GET['user_id'] ?? null;
        
        if (!$userId) {
            throw new Exception('معرف المستخدم مطلوب');
        }
        
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        
        $type = $_GET['type'] ?? 'all'; // all, trade, system, security
        $status = $_GET['status'] ?? 'all'; // all, read, unread
        
        // بناء الاستعلام
        $whereConditions = ['user_id = ?'];
        $params = [$userId];
        
        if ($type !== 'all') {
            $whereConditions[] = 'type = ?';
            $params[] = $type;
        }
        
        if ($status === 'read') {
            $whereConditions[] = 'is_read = 1';
        } elseif ($status === 'unread') {
            $whereConditions[] = 'is_read = 0';
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // الحصول على العدد الإجمالي
        $countStmt = $connection->prepare("
            SELECT COUNT(*) as total
            FROM notifications
            WHERE $whereClause
        ");
        $countStmt->execute($params);
        $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // جلب الإشعارات
        $stmt = $connection->prepare("
            SELECT *
            FROM notifications
            WHERE $whereClause
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تنسيق البيانات
        foreach ($notifications as &$notification) {
            $notification['is_read'] = (bool)$notification['is_read'];
            $notification['data'] = json_decode($notification['data'], true) ?: [];
        }
        
        echo json_encode([
            'success' => true,
            'data' => $notifications,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => intval($totalCount),
                'total_pages' => ceil($totalCount / $limit)
            ]
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // إنشاء إشعار جديد
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $requiredFields = ['user_id', 'type', 'title', 'message'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                throw new Exception("الحقل $field مطلوب");
            }
        }
        
        // التحقق من وجود المستخدم
        $stmt = $connection->prepare("SELECT id FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$input['user_id']]);
        
        if (!$stmt->fetch()) {
            throw new Exception('المستخدم غير موجود');
        }
        
        // إدراج الإشعار
        $stmt = $connection->prepare("
            INSERT INTO notifications (
                user_id, type, title, message, data, 
                action_url, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ");
        
        $stmt->execute([
            $input['user_id'],
            $input['type'],
            $input['title'],
            $input['message'],
            json_encode($input['data'] ?? []),
            $input['action_url'] ?? null
        ]);
        
        $notificationId = $connection->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إنشاء الإشعار بنجاح',
            'data' => ['id' => $notificationId]
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // تحديث حالة الإشعارات (قراءة/عدم قراءة)
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $userId = $input['user_id'] ?? null;
        $notificationIds = $input['notification_ids'] ?? [];
        $markAsRead = $input['mark_as_read'] ?? true;
        
        if (!$userId) {
            throw new Exception('معرف المستخدم مطلوب');
        }
        
        if (empty($notificationIds)) {
            // تحديث جميع الإشعارات
            $stmt = $connection->prepare("
                UPDATE notifications 
                SET is_read = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE user_id = ?
            ");
            $stmt->execute([$markAsRead ? 1 : 0, $userId]);
            
            $affectedRows = $stmt->rowCount();
        } else {
            // تحديث إشعارات محددة
            $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';
            $stmt = $connection->prepare("
                UPDATE notifications 
                SET is_read = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE user_id = ? AND id IN ($placeholders)
            ");
            
            $params = [$markAsRead ? 1 : 0, $userId];
            $params = array_merge($params, $notificationIds);
            $stmt->execute($params);
            
            $affectedRows = $stmt->rowCount();
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث الإشعارات بنجاح',
            'data' => ['affected_rows' => $affectedRows]
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        // حذف إشعارات
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $userId = $input['user_id'] ?? null;
        $notificationIds = $input['notification_ids'] ?? [];
        
        if (!$userId) {
            throw new Exception('معرف المستخدم مطلوب');
        }
        
        if (empty($notificationIds)) {
            throw new Exception('معرفات الإشعارات مطلوبة');
        }
        
        // حذف الإشعارات
        $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';
        $stmt = $connection->prepare("
            DELETE FROM notifications 
            WHERE user_id = ? AND id IN ($placeholders)
        ");
        
        $params = [$userId];
        $params = array_merge($params, $notificationIds);
        $stmt->execute($params);
        
        $affectedRows = $stmt->rowCount();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف الإشعارات بنجاح',
            'data' => ['affected_rows' => $affectedRows]
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    error_log('Database error in notifications/index.php: ' . $e->getMessage());
}
?>
