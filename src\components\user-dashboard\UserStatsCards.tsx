'use client';

import {
  BarChart3,
  DollarSign,
  Star,
  TrendingUp,
  Activity,
  Clock,
  Shield,
  Target,
  Users,
  Zap,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { UserStats } from '@/hooks/user-dashboard/useUserStats';

interface UserStatsCardsProps {
  stats: UserStats | null;
  loading?: boolean;
}

interface StatCard {
  id: string;
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  change?: {
    value: number;
    type: 'increase' | 'decrease' | 'neutral';
  };
}

export default function UserStatsCards({ stats, loading }: UserStatsCardsProps) {
  const { t, formatNumber, formatCurrency } = useUserDashboardTranslation();

  // إنشاء بطاقات الإحصائيات (6 بطاقات مفيدة)
  const statCards: StatCard[] = [
    {
      id: 'totalTrades',
      title: 'إجمالي الصفقات',
      value: stats?.totalTrades || 0,
      subtitle: `${stats?.completedTrades || 0} مكتملة، ${stats?.activeTrades || 0} نشطة`,
      icon: BarChart3,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30',
      change: stats?.totalTrades && stats.totalTrades > 0 ? {
        value: Math.round(((stats.completedTrades || 0) / stats.totalTrades) * 100),
        type: 'neutral'
      } : undefined
    },
    {
      id: 'totalVolume',
      title: 'حجم التداول الإجمالي',
      value: (() => {
        const volume = parseFloat(stats?.totalVolume || '0') || 0;
        return volume >= 1000
          ? `$${(volume / 1000).toFixed(1)}K`
          : `$${volume.toFixed(0)}`;
      })(),
      subtitle: (() => {
        const monthlyVolume = parseFloat(stats?.monthlyVolume || '0') || 0;
        return monthlyVolume >= 1000
          ? `$${(monthlyVolume / 1000).toFixed(1)}K هذا الشهر`
          : `$${monthlyVolume.toFixed(0)} هذا الشهر`;
      })(),
      icon: DollarSign,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-100 dark:bg-green-900/30',
      change: (() => {
        const totalVol = parseFloat(stats?.totalVolume || '0') || 0;
        const monthlyVol = parseFloat(stats?.monthlyVolume || '0') || 0;
        if (totalVol > 0 && monthlyVol > 0) {
          const percentage = Math.round((monthlyVol / totalVol) * 100);
          return {
            value: percentage,
            type: percentage > 20 ? 'increase' : percentage > 10 ? 'neutral' : 'decrease'
          };
        }
        return undefined;
      })()
    },
    {
      id: 'rating',
      title: 'التقييم',
      value: stats?.rating ? Number(stats.rating).toFixed(1) : '0.0',
      subtitle: `من ${stats?.ratingCount || 0} تقييم (${stats?.positiveReviews || 0} إيجابي)`,
      icon: Star,
      color: 'text-yellow-600 dark:text-yellow-400',
      bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
      change: stats?.rating ? {
        value: Number(stats.rating),
        type: Number(stats.rating) >= 4 ? 'increase' : Number(stats.rating) >= 3 ? 'neutral' : 'decrease'
      } : undefined
    },
    {
      id: 'completionRate',
      title: 'معدل الإنجاز',
      value: `${stats?.completionRate || 0}%`,
      subtitle: `${stats?.completedTrades || 0} من ${stats?.totalTrades || 0} صفقة`,
      icon: TrendingUp,
      color: 'text-purple-600 dark:text-purple-400',
      bgColor: 'bg-purple-100 dark:bg-purple-900/30',
      change: stats?.completionRate ? {
        value: stats.completionRate,
        type: stats.completionRate >= 90 ? 'increase' : stats.completionRate >= 70 ? 'neutral' : 'decrease'
      } : undefined
    },
    {
      id: 'activeOffers',
      title: 'العروض النشطة',
      value: stats?.activeOffers || 0,
      subtitle: `من إجمالي ${stats?.totalOffers || 0} عرض`,
      icon: Activity,
      color: 'text-orange-600 dark:text-orange-400',
      bgColor: 'bg-orange-100 dark:bg-orange-900/30',
      change: (() => {
        const activeOffers = stats?.activeOffers || 0;
        const totalOffers = stats?.totalOffers || 0;
        if (totalOffers > 0) {
          const percentage = Math.round((activeOffers / totalOffers) * 100);
          return {
            value: percentage,
            type: percentage >= 50 ? 'increase' : percentage >= 25 ? 'neutral' : 'decrease'
          };
        } else if (activeOffers > 0) {
          return {
            value: activeOffers,
            type: 'increase'
          };
        }
        return undefined;
      })()
    },
    {
      id: 'averageTradeAmount',
      title: 'متوسط قيمة الصفقة',
      value: (() => {
        const avgAmount = parseFloat(stats?.averageTradeAmount || '0') || 0;
        return avgAmount >= 1000
          ? `$${(avgAmount / 1000).toFixed(1)}K`
          : `$${avgAmount.toFixed(0)}`;
      })(),
      subtitle: stats?.totalTrades && stats.totalTrades > 0 ?
        `من ${stats.totalTrades} صفقة` : 'لا توجد صفقات بعد',
      icon: Target,
      color: 'text-indigo-600 dark:text-indigo-400',
      bgColor: 'bg-indigo-100 dark:bg-indigo-900/30',
      change: (() => {
        const avgAmount = parseFloat(stats?.averageTradeAmount || '0') || 0;
        if (avgAmount > 0) {
          return {
            value: Math.round(avgAmount),
            type: avgAmount >= 1000 ? 'increase' : avgAmount >= 500 ? 'neutral' : 'decrease'
          };
        }
        return undefined;
      })()
    }
  ];

  // مكون بطاقة واحدة
  const StatCard = ({ card }: { card: StatCard }) => (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200 group">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
            {card.title}
          </p>
          <div className="flex items-baseline space-x-2 rtl:space-x-reverse">
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {loading ? (
                <div className="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
              ) : (
                card.value
              )}
            </p>
            {card.change && !loading && (
              <span className={`text-sm font-medium flex items-center space-x-1 rtl:space-x-reverse ${
                card.change.type === 'increase'
                  ? 'text-green-600 dark:text-green-400'
                  : card.change.type === 'decrease'
                  ? 'text-red-600 dark:text-red-400'
                  : 'text-gray-600 dark:text-gray-400'
              }`}>
                {card.change.type === 'increase' && <ArrowUp className="w-3 h-3" />}
                {card.change.type === 'decrease' && <ArrowDown className="w-3 h-3" />}
                {card.change.type === 'neutral' && <Minus className="w-3 h-3" />}
                <span>
                  {card.change.type === 'increase' ? '+' : ''}
                  {card.change.value}
                  {card.id === 'rating' ? '/5' : card.id === 'averageTradeAmount' ? '' : '%'}
                </span>
              </span>
            )}
          </div>
          {card.subtitle && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {loading ? (
                <div className="h-3 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
              ) : (
                card.subtitle
              )}
            </p>
          )}
        </div>
        
        <div className={`w-12 h-12 ${card.bgColor} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}>
          <card.icon className={`w-6 h-6 ${card.color}`} />
        </div>
      </div>
    </div>
  );

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4 sm:gap-6">
      {statCards.slice(0, 6).map((card) => (
        <StatCard key={card.id} card={card} />
      ))}
    </div>
  );
}
