'use client';

import { useState, useEffect } from 'react';
import { Menu, X, ChevronDown, ChevronRight } from 'lucide-react';

interface NavigationItem {
  label: string;
  href: string;
  icon?: React.ReactNode;
  children?: NavigationItem[];
  badge?: string | number;
  active?: boolean;
}

interface ResponsiveNavigationProps {
  items: NavigationItem[];
  logo?: React.ReactNode;
  actions?: React.ReactNode;
  className?: string;
  sticky?: boolean;
  transparent?: boolean;
}

export default function ResponsiveNavigation({
  items,
  logo,
  actions,
  className = '',
  sticky = true,
  transparent = false
}: ResponsiveNavigationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };

    if (sticky) {
      window.addEventListener('scroll', handleScroll);
      return () => window.removeEventListener('scroll', handleScroll);
    }
  }, [sticky]);

  const toggleExpanded = (label: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(label)) {
      newExpanded.delete(label);
    } else {
      newExpanded.add(label);
    }
    setExpandedItems(newExpanded);
  };

  const baseClasses = `
    ${sticky ? 'sticky top-0 z-50' : ''}
    ${transparent && !scrolled ? 'bg-transparent' : 'bg-white/95 dark:bg-gray-900/95 backdrop-blur-md'}
    ${scrolled ? 'shadow-lg border-b border-gray-200 dark:border-gray-700' : ''}
    transition-all duration-300
    ${className}
  `;

  return (
    <nav className={baseClasses}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* الشعار */}
          {logo && (
            <div className="flex-shrink-0">
              {logo}
            </div>
          )}

          {/* التنقل للشاشات الكبيرة */}
          <div className="hidden lg:flex lg:items-center lg:space-x-8 rtl:space-x-reverse">
            {items.map((item) => (
              <div key={item.label} className="relative group">
                {item.children ? (
                  <>
                    <button className="flex items-center space-x-1 rtl:space-x-reverse px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                      {item.icon && <span className="w-4 h-4">{item.icon}</span>}
                      <span>{item.label}</span>
                      <ChevronDown className="w-4 h-4" />
                      {item.badge && (
                        <span className="bg-red-500 text-white text-xs rounded-full px-2 py-0.5 ml-2">
                          {item.badge}
                        </span>
                      )}
                    </button>
                    
                    {/* القائمة المنسدلة */}
                    <div className="absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                      <div className="py-2">
                        {item.children.map((child) => (
                          <a
                            key={child.label}
                            href={child.href}
                            className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                          >
                            {child.icon && <span className="w-4 h-4">{child.icon}</span>}
                            <span>{child.label}</span>
                            {child.badge && (
                              <span className="bg-red-500 text-white text-xs rounded-full px-2 py-0.5 ml-auto">
                                {child.badge}
                              </span>
                            )}
                          </a>
                        ))}
                      </div>
                    </div>
                  </>
                ) : (
                  <a
                    href={item.href}
                    className={`flex items-center space-x-1 rtl:space-x-reverse px-3 py-2 text-sm font-medium transition-colors ${
                      item.active
                        ? 'text-blue-600 dark:text-blue-400'
                        : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400'
                    }`}
                  >
                    {item.icon && <span className="w-4 h-4">{item.icon}</span>}
                    <span>{item.label}</span>
                    {item.badge && (
                      <span className="bg-red-500 text-white text-xs rounded-full px-2 py-0.5 ml-2">
                        {item.badge}
                      </span>
                    )}
                  </a>
                )}
              </div>
            ))}
          </div>

          {/* الإجراءات */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            {actions}
            
            {/* زر القائمة للجوال */}
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="lg:hidden p-2 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
              {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* القائمة المنسدلة للجوال */}
        {isOpen && (
          <div className="lg:hidden border-t border-gray-200 dark:border-gray-700 bg-white/98 dark:bg-gray-900/98 backdrop-blur-md">
            <div className="px-4 py-6 space-y-2">
              {items.map((item) => (
                <div key={item.label}>
                  {item.children ? (
                    <>
                      <button
                        onClick={() => toggleExpanded(item.label)}
                        className="flex items-center justify-between w-full px-3 py-3 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                      >
                        <div className="flex items-center space-x-3 rtl:space-x-reverse">
                          {item.icon && <span className="w-5 h-5">{item.icon}</span>}
                          <span className="font-medium">{item.label}</span>
                          {item.badge && (
                            <span className="bg-red-500 text-white text-xs rounded-full px-2 py-0.5">
                              {item.badge}
                            </span>
                          )}
                        </div>
                        <ChevronRight className={`w-4 h-4 transition-transform ${
                          expandedItems.has(item.label) ? 'rotate-90' : ''
                        }`} />
                      </button>
                      
                      {expandedItems.has(item.label) && (
                        <div className="mt-2 ml-6 space-y-1">
                          {item.children.map((child) => (
                            <a
                              key={child.label}
                              href={child.href}
                              className="flex items-center space-x-3 rtl:space-x-reverse px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                              onClick={() => setIsOpen(false)}
                            >
                              {child.icon && <span className="w-4 h-4">{child.icon}</span>}
                              <span>{child.label}</span>
                              {child.badge && (
                                <span className="bg-red-500 text-white text-xs rounded-full px-2 py-0.5 ml-auto">
                                  {child.badge}
                                </span>
                              )}
                            </a>
                          ))}
                        </div>
                      )}
                    </>
                  ) : (
                    <a
                      href={item.href}
                      className={`flex items-center space-x-3 rtl:space-x-reverse px-3 py-3 rounded-lg transition-colors ${
                        item.active
                          ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      {item.icon && <span className="w-5 h-5">{item.icon}</span>}
                      <span className="font-medium">{item.label}</span>
                      {item.badge && (
                        <span className="bg-red-500 text-white text-xs rounded-full px-2 py-0.5 ml-auto">
                          {item.badge}
                        </span>
                      )}
                    </a>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
