'use client';

import React, { useState, useEffect } from 'react';
import {
  Shield,
  Search,
  Network,
  CheckCircle,
  AlertTriangle,
  Clock,
  Activity,
  Code,
  Database,
  Eye,
  Edit,
  Upload,
  Download,
  Terminal,
  Layers,
  Link,
  ExternalLink,
  RefreshCw,
  Save,
  Zap,
  Settings,
  Users,
  FileText,
  Globe,
  Star,
  TrendingUp,
  BarChart3
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';

interface NetworkInfo {
  chain_id: number;
  name: string;
  explorer_url: string;
  native_currency: string;
  is_testnet?: boolean;
  is_active?: boolean;
}

interface ContractInfo {
  address: string;
  name: string;
  source_code: string;
  abi: string;
  compiler_version: string;
  optimization_used: string;
  verified: boolean;
}

interface ContractType {
  is_token: boolean;
  is_nft: boolean;
  is_escrow: boolean;
  is_defi: boolean;
  is_proxy: boolean;
  contract_type: string;
  functions: string[];
  events: string[];
}

interface SecurityAnalysis {
  is_verified: boolean;
  has_proxy: boolean;
  optimization_used: boolean;
  security_issues: string[];
  security_score: number;
}

interface SupportedToken {
  contract_address: string;
  name: string;
  symbol: string;
  decimals: number;
  is_stablecoin: boolean;
  is_verified: boolean;
}

interface VerificationResult {
  verified: boolean;
  contract_info: ContractInfo;
  contract_type: ContractType;
  security_analysis: SecurityAnalysis;
  supported_tokens: SupportedToken[];
  network_info: NetworkInfo;
  recommendations: string[];
}

interface EnhancedSmartContractManagerProps {
  className?: string;
}

export default function EnhancedSmartContractManager({ className = '' }: EnhancedSmartContractManagerProps) {
  const { t, isRTL } = useAdminTranslation();

  // State management
  const [activeTab, setActiveTab] = useState<'verification' | 'networks' | 'tokens'>('verification');
  const [contractAddress, setContractAddress] = useState('');
  const [selectedNetwork, setSelectedNetwork] = useState(97); // BSC Testnet default
  const [supportedNetworks, setSupportedNetworks] = useState<NetworkInfo[]>([]);
  const [supportedTokens, setSupportedTokens] = useState<any[]>([]);
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Network management state
  const [showNetworkModal, setShowNetworkModal] = useState(false);
  const [editingNetwork, setEditingNetwork] = useState<any>(null);
  const [networkForm, setNetworkForm] = useState({
    network_name: '',
    network_symbol: '',
    chain_id: '',
    rpc_url: '',
    explorer_url: '',
    is_testnet: false,
    is_active: true,
    gas_price_gwei: '5.0',
    block_time_seconds: '12',
    confirmation_blocks: '12'
  });

  // Token management state
  const [showTokenModal, setShowTokenModal] = useState(false);
  const [editingToken, setEditingToken] = useState<any>(null);
  const [tokenForm, setTokenForm] = useState({
    network_id: '',
    token_address: '',
    token_symbol: '',
    token_name: '',
    decimals: '18',
    is_stablecoin: false,
    is_active: true,
    min_trade_amount: '1.0',
    max_trade_amount: '100000.0',
    platform_fee_rate: '0.005'
  });

  // Load supported networks on component mount
  useEffect(() => {
    loadSupportedNetworks();
    if (activeTab === 'networks') {
      loadNetworksList();
    }
    if (activeTab === 'tokens') {
      loadTokensList();
    }
  }, [activeTab]);

  // Load networks list for management
  const loadNetworksList = async () => {
    try {
      const response = await fetch('/api/enhanced-contracts/network-management?action=list');
      const data = await response.json();
      if (data.success) {
        setSupportedNetworks(data.data.networks);
      }
    } catch (error) {
      console.error('Failed to load networks list:', error);
    }
  };

  // Load tokens list for management
  const loadTokensList = async () => {
    try {
      const response = await fetch('/api/enhanced-contracts/token-management?action=list');
      const data = await response.json();
      if (data.success) {
        setSupportedTokens(data.data.tokens);
      }
    } catch (error) {
      console.error('Failed to load tokens list:', error);
    }
  };

  /**
   * تحميل الشبكات المدعومة
   */
  const loadSupportedNetworks = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🔄 Loading supported networks...');
      // استخدام PHP API مباشرة كحل مؤقت
      const apiUrl = window.location.origin.includes('localhost:3000')
        ? '/api/enhanced-contracts/contract-verification?action=supported-networks'
        : '/ikaros-p2p/api/enhanced-contracts/contract-verification.php?action=supported-networks';

      console.log('📡 API URL:', apiUrl);
      const response = await fetch(apiUrl);

      console.log('📡 Response status:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('📥 Response data:', data);

      if (data.success && data.data && data.data.networks) {
        setSupportedNetworks(data.data.networks);
        console.log('✅ Networks loaded successfully:', data.data.networks.length, 'networks');

        // إذا لم تكن الشبكة المختارة موجودة، اختر أول شبكة متاحة
        if (data.data.networks.length > 0) {
          const currentNetworkExists = data.data.networks.some((n: NetworkInfo) => n.chain_id === selectedNetwork);
          if (!currentNetworkExists) {
            setSelectedNetwork(data.data.networks[0].chain_id);
            console.log('🔄 Changed selected network to:', data.data.networks[0].chain_id);
          }
        }
      } else {
        console.error('❌ Invalid response format:', data);
        setError(data.error || t('contracts.messages.networkNotSupported'));
      }
    } catch (error) {
      console.error('❌ Failed to load supported networks:', error);
      setError(t('contracts.messages.networkNotSupported'));

      // استخدام شبكات افتراضية في حالة الفشل
      const fallbackNetworks: NetworkInfo[] = [
        {
          chain_id: 97,
          name: 'BSC Testnet',
          explorer_url: 'https://testnet.bscscan.com',
          native_currency: 'tBNB'
        },
        {
          chain_id: 56,
          name: 'BSC Mainnet',
          explorer_url: 'https://bscscan.com',
          native_currency: 'BNB'
        }
      ];

      setSupportedNetworks(fallbackNetworks);
      console.log('🔄 Using fallback networks:', fallbackNetworks);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * التحقق من العقد الذكي
   */
  const verifyContract = async () => {
    if (!contractAddress.trim()) {
      setError(t('contracts.messages.invalidAddress'));
      return;
    }

    setIsVerifying(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('/api/enhanced-contracts/contract-verification?action=verify-contract', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contract_address: contractAddress.trim(),
          chain_id: selectedNetwork
        }),
      });

      const data = await response.json();

      if (data.success) {
        setVerificationResult(data.data);
        setSuccess(t('contracts.messages.verificationSuccess'));
      } else {
        setError(data.error || t('contracts.messages.verificationFailed'));
      }
    } catch (error) {
      console.error('Contract verification failed:', error);
      setError(t('contracts.messages.verificationFailed'));
    } finally {
      setIsVerifying(false);
    }
  };

  /**
   * تحليل العقد الذكي
   */
  const analyzeContract = async () => {
    if (!contractAddress.trim()) {
      setError(t('contracts.messages.invalidAddress'));
      return;
    }

    setIsAnalyzing(true);
    setError(null);

    try {
      const response = await fetch('/api/enhanced-contracts/contract-verification?action=analyze-contract', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contract_address: contractAddress.trim(),
          chain_id: selectedNetwork
        }),
      });

      const data = await response.json();

      if (data.success) {
        setVerificationResult(data.data);
        setSuccess(t('contracts.messages.analysisSuccess'));
      } else {
        setError(data.error || t('contracts.messages.analysisFailed'));
      }
    } catch (error) {
      console.error('Contract analysis failed:', error);
      setError(t('contracts.messages.analysisFailed'));
    } finally {
      setIsAnalyzing(false);
    }
  };

  /**
   * حفظ البيانات في قاعدة البيانات
   */
  const saveToDatabase = async () => {
    if (!contractAddress.trim()) {
      setError(t('contracts.messages.invalidAddress'));
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      const response = await fetch('/api/enhanced-contracts/contract-verification?action=auto-populate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contract_address: contractAddress.trim(),
          chain_id: selectedNetwork,
          save_to_database: true
        }),
      });

      const data = await response.json();

      if (data.success) {
        setVerificationResult(data.data.analysis);
        setSuccess(t('contracts.messages.saveSuccess'));
      } else {
        setError(data.error || t('contracts.messages.saveFailed'));
      }
    } catch (error) {
      console.error('Save to database failed:', error);
      setError(t('contracts.messages.saveFailed'));
    } finally {
      setIsSaving(false);
    }
  };

  /**
   * مسح النتائج
   */
  const clearResults = () => {
    setVerificationResult(null);
    setError(null);
    setSuccess(null);
  };

  /**
   * إضافة شبكة جديدة
   */
  const handleAddNetwork = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/enhanced-contracts/network-management?action=add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(networkForm)
      });

      const data = await response.json();
      if (data.success) {
        setSuccess(t('networks.addSuccess'));
        setShowNetworkModal(false);
        resetNetworkForm();
        loadNetworksList();
      } else {
        setError(data.error || t('networks.addFailed'));
      }
    } catch (error) {
      setError(t('networks.addFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * تحديث شبكة موجودة
   */
  const handleUpdateNetwork = async () => {
    if (!editingNetwork) return;

    try {
      setIsLoading(true);
      const response = await fetch(`/api/enhanced-contracts/network-management?action=update&id=${editingNetwork.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(networkForm)
      });

      const data = await response.json();
      if (data.success) {
        setSuccess(t('networks.updateSuccess'));
        setShowNetworkModal(false);
        setEditingNetwork(null);
        resetNetworkForm();
        loadNetworksList();
      } else {
        setError(data.error || t('networks.updateFailed'));
      }
    } catch (error) {
      setError(t('networks.updateFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * حذف شبكة
   */
  const handleDeleteNetwork = async (networkId: number) => {
    if (!confirm(t('networks.confirmDelete'))) return;

    try {
      setIsLoading(true);
      const response = await fetch(`/api/enhanced-contracts/network-management?action=remove&id=${networkId}`, {
        method: 'DELETE'
      });

      const data = await response.json();
      if (data.success) {
        setSuccess(t('networks.deleteSuccess'));
        loadNetworksList();
      } else {
        setError(data.error || t('networks.deleteFailed'));
      }
    } catch (error) {
      setError(t('networks.deleteFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * إعادة تعيين نموذج الشبكة
   */
  const resetNetworkForm = () => {
    setNetworkForm({
      network_name: '',
      network_symbol: '',
      chain_id: '',
      rpc_url: '',
      explorer_url: '',
      is_testnet: false,
      is_active: true,
      gas_price_gwei: '5.0',
      block_time_seconds: '12',
      confirmation_blocks: '12'
    });
  };

  /**
   * فتح نموذج تعديل الشبكة
   */
  const openEditNetwork = (network: any) => {
    setEditingNetwork(network);
    setNetworkForm({
      network_name: network.network_name,
      network_symbol: network.network_symbol,
      chain_id: network.chain_id.toString(),
      rpc_url: network.rpc_url,
      explorer_url: network.explorer_url,
      is_testnet: network.is_testnet,
      is_active: network.is_active,
      gas_price_gwei: network.gas_price_gwei.toString(),
      block_time_seconds: network.block_time_seconds.toString(),
      confirmation_blocks: network.confirmation_blocks.toString()
    });
    setShowNetworkModal(true);
  };

  /**
   * إضافة عملة جديدة
   */
  const handleAddToken = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/enhanced-contracts/token-management?action=add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(tokenForm)
      });

      const data = await response.json();
      if (data.success) {
        setSuccess(t('tokens.addSuccess'));
        setShowTokenModal(false);
        resetTokenForm();
        loadTokensList();
      } else {
        setError(data.error || t('tokens.addFailed'));
      }
    } catch (error) {
      setError(t('tokens.addFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * تحديث عملة موجودة
   */
  const handleUpdateToken = async () => {
    if (!editingToken) return;

    try {
      setIsLoading(true);
      const response = await fetch(`/api/enhanced-contracts/token-management?action=update&id=${editingToken.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(tokenForm)
      });

      const data = await response.json();
      if (data.success) {
        setSuccess(t('tokens.updateSuccess'));
        setShowTokenModal(false);
        setEditingToken(null);
        resetTokenForm();
        loadTokensList();
      } else {
        setError(data.error || t('tokens.updateFailed'));
      }
    } catch (error) {
      setError(t('tokens.updateFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * حذف عملة
   */
  const handleDeleteToken = async (tokenId: number) => {
    if (!confirm(t('tokens.confirmDelete'))) return;

    try {
      setIsLoading(true);
      const response = await fetch(`/api/enhanced-contracts/token-management?action=remove&id=${tokenId}`, {
        method: 'DELETE'
      });

      const data = await response.json();
      if (data.success) {
        setSuccess(t('tokens.deleteSuccess'));
        loadTokensList();
      } else {
        setError(data.error || t('tokens.deleteFailed'));
      }
    } catch (error) {
      setError(t('tokens.deleteFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * إعادة تعيين نموذج العملة
   */
  const resetTokenForm = () => {
    setTokenForm({
      network_id: '',
      token_address: '',
      token_symbol: '',
      token_name: '',
      decimals: '18',
      is_stablecoin: false,
      is_active: true,
      min_trade_amount: '1.0',
      max_trade_amount: '100000.0',
      platform_fee_rate: '0.005'
    });
  };

  /**
   * فتح نموذج تعديل العملة
   */
  const openEditToken = (token: any) => {
    setEditingToken(token);
    setTokenForm({
      network_id: token.network_id.toString(),
      token_address: token.token_address,
      token_symbol: token.token_symbol,
      token_name: token.token_name,
      decimals: token.decimals.toString(),
      is_stablecoin: token.is_stablecoin,
      is_active: token.is_active,
      min_trade_amount: token.min_trade_amount.toString(),
      max_trade_amount: token.max_trade_amount.toString(),
      platform_fee_rate: token.platform_fee_rate.toString()
    });
    setShowTokenModal(true);
  };

  /**
   * التحقق من عقد العملة
   */
  const handleValidateTokenContract = async () => {
    if (!tokenForm.token_address || !tokenForm.network_id) {
      setError(t('tokens.addressAndNetworkRequired'));
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetch(`/api/enhanced-contracts/token-management?action=fetch-token-info&contract_address=${tokenForm.token_address}&network_id=${tokenForm.network_id}`);
      const data = await response.json();

      if (data.success) {
        const tokenInfo = data.data.token_info;
        setTokenForm(prev => ({
          ...prev,
          token_symbol: tokenInfo.symbol || prev.token_symbol,
          token_name: tokenInfo.name || prev.token_name,
          decimals: tokenInfo.decimals?.toString() || prev.decimals,
          is_stablecoin: data.data.suggested_settings?.is_stablecoin || prev.is_stablecoin
        }));
        setSuccess(t('tokens.contractValidated'));
      } else {
        setError(data.error || t('tokens.contractValidationFailed'));
      }
    } catch (error) {
      setError(t('tokens.contractValidationFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * الحصول على أيقونة نوع العقد
   */
  const getContractTypeIcon = (contractType: ContractType) => {
    if (contractType.is_token) return Layers;
    if (contractType.is_escrow) return Shield;
    if (contractType.is_defi) return TrendingUp;
    if (contractType.is_nft) return Star;
    return Code;
  };

  /**
   * الحصول على لون نقاط الأمان
   */
  const getSecurityScoreColor = (score: number) => {
    if (score >= 80) return 'green';
    if (score >= 60) return 'yellow';
    return 'red';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 ${isRTL ? 'text-right' : 'text-left'}`}>
        <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
              <Shield className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {t('contracts.title')}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {t('contracts.subtitle')}
              </p>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className={`flex space-x-1 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          <button
            onClick={() => setActiveTab('verification')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'verification'
                ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Search className="w-4 h-4" />
              {t('contracts.verification')}
            </div>
          </button>

          <button
            onClick={() => setActiveTab('networks')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'networks'
                ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Network className="w-4 h-4" />
              {t('networks.management')}
            </div>
          </button>

          <button
            onClick={() => setActiveTab('tokens')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === 'tokens'
                ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Layers className="w-4 h-4" />
              {t('tokens.management')}
            </div>
          </button>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'verification' && (
        <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 ${isRTL ? 'text-right' : 'text-left'}`}>
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('contracts.verification')}
          </h2>

        <div className="space-y-4">
          {/* Network Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('contracts.selectNetwork')}
              {isLoading && (
                <span className={`text-xs text-blue-600 dark:text-blue-400 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                  ({t('contracts.messages.loading')})
                </span>
              )}
            </label>
            <div className="relative">
              <select
                value={selectedNetwork}
                onChange={(e) => setSelectedNetwork(Number(e.target.value))}
                disabled={isLoading}
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed ${isRTL ? 'text-right' : 'text-left'}`}
              >
                {isLoading ? (
                  <option value="">{t('contracts.messages.loading')}</option>
                ) : supportedNetworks.length === 0 ? (
                  <option value="">{t('contracts.messages.networkNotSupported')}</option>
                ) : (
                  supportedNetworks.map((network) => (
                    <option key={network.chain_id} value={network.chain_id}>
                      {network.name} ({network.native_currency}) {network.is_testnet ? '- Testnet' : ''}
                    </option>
                  ))
                )}
              </select>
              {isLoading && (
                <div className={`absolute top-1/2 transform -translate-y-1/2 ${isRTL ? 'left-3' : 'right-3'}`}>
                  <RefreshCw className="w-4 h-4 animate-spin text-blue-600" />
                </div>
              )}
            </div>
            {supportedNetworks.length > 0 && !isLoading && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {supportedNetworks.length} {t('networks.supportedNetworks')} {t('common.labels.available')}
              </p>
            )}
          </div>

          {/* Contract Address Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('contracts.contractAddress')}
            </label>
            <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <input
                type="text"
                value={contractAddress}
                onChange={(e) => setContractAddress(e.target.value)}
                placeholder={t('contracts.contractAddressPlaceholder')}
                className={`flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
              />
              <button
                onClick={clearResults}
                className="px-3 py-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                title={t('common.actions.clear')}
              >
                <RefreshCw className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className={`flex gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              onClick={verifyContract}
              disabled={isVerifying || !contractAddress.trim()}
              className={`flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
            >
              {isVerifying ? (
                <Clock className="w-4 h-4 animate-spin" />
              ) : (
                <CheckCircle className="w-4 h-4" />
              )}
              {isVerifying ? t('contracts.messages.verifying') : t('contracts.verifyContract')}
            </button>

            <button
              onClick={analyzeContract}
              disabled={isAnalyzing || !contractAddress.trim()}
              className={`flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
            >
              {isAnalyzing ? (
                <Clock className="w-4 h-4 animate-spin" />
              ) : (
                <Search className="w-4 h-4" />
              )}
              {isAnalyzing ? t('contracts.messages.analyzing') : t('contracts.analyzeContract')}
            </button>

            <button
              onClick={saveToDatabase}
              disabled={isSaving || !contractAddress.trim()}
              className={`flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
            >
              {isSaving ? (
                <Clock className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              {isSaving ? t('contracts.messages.saving') : t('contracts.saveToDatabase')}
            </button>
          </div>
        </div>
        </div>
      )}

      {/* Networks Management Tab */}
      {activeTab === 'networks' && (
        <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 ${isRTL ? 'text-right' : 'text-left'}`}>
          <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('networks.management')}
            </h2>
            <button
              onClick={() => {
                resetNetworkForm();
                setEditingNetwork(null);
                setShowNetworkModal(true);
              }}
              className={`flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
            >
              <Upload className="w-4 h-4" />
              {t('networks.addNetwork')}
            </button>
          </div>

          {/* Networks List */}
          <div className="space-y-4">
            {supportedNetworks.map((network: any) => (
              <div key={network.id} className={`bg-gray-50 dark:bg-gray-700 rounded-lg p-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${network.is_testnet ? 'bg-yellow-100 dark:bg-yellow-900/30' : 'bg-green-100 dark:bg-green-900/30'}`}>
                      <Network className={`w-5 h-5 ${network.is_testnet ? 'text-yellow-600 dark:text-yellow-400' : 'text-green-600 dark:text-green-400'}`} />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {network.network_name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Chain ID: {network.chain_id} • {network.network_symbol}
                        {network.is_testnet && ' • Testnet'}
                      </p>
                    </div>
                  </div>

                  <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={`w-2 h-2 rounded-full ${network.is_active ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <span className="text-xs text-gray-600 dark:text-gray-400">
                        {network.is_active ? t('common.status.active') : t('common.status.inactive')}
                      </span>
                    </div>

                    <button
                      onClick={() => openEditNetwork(network)}
                      className="p-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                      title={t('common.actions.edit')}
                    >
                      <Edit className="w-4 h-4" />
                    </button>

                    <button
                      onClick={() => handleDeleteNetwork(network.id)}
                      className="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                      title={t('common.actions.delete')}
                    >
                      <AlertTriangle className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">{t('networks.rpcUrl')}: </span>
                    <span className="text-gray-900 dark:text-white font-mono text-xs">{network.rpc_url}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">{t('networks.gasPrice')}: </span>
                    <span className="text-gray-900 dark:text-white">{network.gas_price_gwei} Gwei</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">{t('networks.blockTime')}: </span>
                    <span className="text-gray-900 dark:text-white">{network.block_time_seconds}s</span>
                  </div>
                </div>
              </div>
            ))}

            {supportedNetworks.length === 0 && (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                {t('networks.noNetworks')}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Tokens Management Tab */}
      {activeTab === 'tokens' && (
        <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 ${isRTL ? 'text-right' : 'text-left'}`}>
          <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('tokens.management')}
            </h2>
            <button
              onClick={() => {
                resetTokenForm();
                setEditingToken(null);
                setShowTokenModal(true);
              }}
              className={`flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
            >
              <Upload className="w-4 h-4" />
              {t('tokens.addToken')}
            </button>
          </div>

          {/* Tokens List */}
          <div className="space-y-4">
            {supportedTokens.map((token: any) => (
              <div key={token.id} className={`bg-gray-50 dark:bg-gray-700 rounded-lg p-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${token.is_stablecoin ? 'bg-blue-100 dark:bg-blue-900/30' : 'bg-purple-100 dark:bg-purple-900/30'}`}>
                      <Layers className={`w-5 h-5 ${token.is_stablecoin ? 'text-blue-600 dark:text-blue-400' : 'text-purple-600 dark:text-purple-400'}`} />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {token.token_symbol} - {token.token_name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {token.network_name} • {token.decimals} decimals
                        {token.is_stablecoin && ' • Stablecoin'}
                      </p>
                    </div>
                  </div>

                  <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={`w-2 h-2 rounded-full ${token.is_active ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <span className="text-xs text-gray-600 dark:text-gray-400">
                        {token.is_active ? t('common.status.active') : t('common.status.inactive')}
                      </span>
                    </div>

                    <button
                      onClick={() => openEditToken(token)}
                      className="p-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                      title={t('common.actions.edit')}
                    >
                      <Edit className="w-4 h-4" />
                    </button>

                    <button
                      onClick={() => handleDeleteToken(token.id)}
                      className="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                      title={t('common.actions.delete')}
                    >
                      <AlertTriangle className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">{t('tokens.contractAddress')}: </span>
                    <span className="text-gray-900 dark:text-white font-mono text-xs">{token.token_address}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">{t('tokens.minAmount')}: </span>
                    <span className="text-gray-900 dark:text-white">{token.min_trade_amount}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">{t('tokens.maxAmount')}: </span>
                    <span className="text-gray-900 dark:text-white">{token.max_trade_amount}</span>
                  </div>
                </div>
              </div>
            ))}

            {supportedTokens.length === 0 && (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                {t('tokens.noTokens')}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Error/Success Messages */}
      {error && (
        <div className={`bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 ${isRTL ? 'text-right' : 'text-left'}`}>
          <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />
            <p className="text-red-800 dark:text-red-200">{error}</p>
          </div>
        </div>
      )}

      {success && (
        <div className={`bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 ${isRTL ? 'text-right' : 'text-left'}`}>
          <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
            <p className="text-green-800 dark:text-green-200">{success}</p>
          </div>
        </div>
      )}

      {/* Verification Results */}
      {verificationResult && (
        <div className="space-y-6">
          {/* Contract Information */}
          <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 ${isRTL ? 'text-right' : 'text-left'}`}>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('contracts.contractInfo')}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('contracts.fields.name')}
                </label>
                <p className="text-gray-900 dark:text-white">{verificationResult.contract_info?.name || 'Unknown'}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('contracts.fields.address')}
                </label>
                <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <p className="text-gray-900 dark:text-white font-mono text-sm">
                    {verificationResult.contract_info?.address || 'Unknown'}
                  </p>
                  {verificationResult.contract_info?.address && verificationResult.network_info?.explorer_url && (
                    <a
                      href={`${verificationResult.network_info.explorer_url}/address/${verificationResult.contract_info.address}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </a>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('contracts.fields.network')}
                </label>
                <p className="text-gray-900 dark:text-white">{verificationResult.network_info?.name || 'Unknown'}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('contracts.fields.verified')}
                </label>
                <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  {verificationResult.security_analysis?.is_verified ? (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-green-600">{t('contracts.security.verified')}</span>
                    </>
                  ) : (
                    <>
                      <AlertTriangle className="w-4 h-4 text-yellow-600" />
                      <span className="text-yellow-600">{t('contracts.security.unverified')}</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Contract Type */}
          <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 ${isRTL ? 'text-right' : 'text-left'}`}>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('contracts.contractType')}
            </h3>

            {verificationResult.contract_type && (
              <div className={`flex items-center gap-3 mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                {(() => {
                  const Icon = getContractTypeIcon(verificationResult.contract_type);
                  return (
                    <>
                      <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                        <Icon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {verificationResult.contract_type?.contract_type ?
                            t(`contracts.types.${verificationResult.contract_type.contract_type}`) :
                            'Unknown Contract Type'
                          }
                        </p>
                        <div className={`flex gap-2 mt-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          {verificationResult.contract_type?.is_token && (
                            <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-xs rounded">
                              Token
                            </span>
                          )}
                          {verificationResult.contract_type?.is_escrow && (
                            <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded">
                              Escrow
                            </span>
                          )}
                          {verificationResult.contract_type?.is_defi && (
                            <span className="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 text-xs rounded">
                              DeFi
                            </span>
                          )}
                          {verificationResult.contract_type?.is_nft && (
                            <span className="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 text-xs rounded">
                              NFT
                            </span>
                          )}
                        </div>
                      </div>
                    </>
                  );
                })()}
              </div>
            )}
          </div>

          {/* Security Analysis */}
          <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 ${isRTL ? 'text-right' : 'text-left'}`}>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('contracts.securityAnalysis')}
            </h3>

            <div className="space-y-4">
              {/* Security Score */}
              {verificationResult.security_analysis?.security_score !== undefined && (
                <div>
                  <div className={`flex items-center justify-between mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {t('contracts.security.securityScore')}
                    </span>
                    <span className={`text-sm font-bold text-${getSecurityScoreColor(verificationResult.security_analysis.security_score)}-600`}>
                      {verificationResult.security_analysis.security_score}/100
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full bg-${getSecurityScoreColor(verificationResult.security_analysis.security_score)}-600`}
                      style={{ width: `${verificationResult.security_analysis.security_score}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Security Issues */}
              {verificationResult.security_analysis?.security_issues?.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('contracts.security.securityIssues')}
                  </h4>
                  <div className="space-y-2">
                    {verificationResult.security_analysis.security_issues.map((issue, index) => (
                      <div key={index} className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <AlertTriangle className="w-4 h-4 text-red-600" />
                        <span className="text-sm text-red-800 dark:text-red-200">{issue}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Supported Tokens */}
          {verificationResult.supported_tokens?.length > 0 && (
            <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 ${isRTL ? 'text-right' : 'text-left'}`}>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {t('contracts.supportedTokens')}
              </h3>

              <div className="space-y-3">
                {verificationResult.supported_tokens.map((token, index) => (
                  <div key={index} className={`flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                        <Layers className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">{token.symbol}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{token.name}</p>
                      </div>
                    </div>
                    <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      {token.is_stablecoin && (
                        <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-xs rounded">
                          Stablecoin
                        </span>
                      )}
                      {token.is_verified && (
                        <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded">
                          Verified
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Recommendations */}
          {verificationResult.recommendations?.length > 0 && (
            <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 ${isRTL ? 'text-right' : 'text-left'}`}>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {t('contracts.recommendations')}
              </h3>

              <div className="space-y-2">
                {verificationResult.recommendations.map((recommendation, index) => (
                  <div key={index} className={`flex items-start gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">{recommendation}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Network Modal */}
      {showNetworkModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto ${isRTL ? 'text-right' : 'text-left'}`}>
            <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {editingNetwork ? t('networks.editNetwork') : t('networks.addNetwork')}
              </h3>
              <button
                onClick={() => {
                  setShowNetworkModal(false);
                  setEditingNetwork(null);
                  resetNetworkForm();
                }}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <AlertTriangle className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('networks.networkName')}
                  </label>
                  <input
                    type="text"
                    value={networkForm.network_name}
                    onChange={(e) => setNetworkForm({...networkForm, network_name: e.target.value})}
                    className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                    placeholder="Ethereum Mainnet"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('networks.networkSymbol')}
                  </label>
                  <input
                    type="text"
                    value={networkForm.network_symbol}
                    onChange={(e) => setNetworkForm({...networkForm, network_symbol: e.target.value})}
                    className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                    placeholder="ETH"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('networks.chainId')}
                  </label>
                  <input
                    type="number"
                    value={networkForm.chain_id}
                    onChange={(e) => setNetworkForm({...networkForm, chain_id: e.target.value})}
                    className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                    placeholder="1"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('networks.gasPrice')} (Gwei)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={networkForm.gas_price_gwei}
                    onChange={(e) => setNetworkForm({...networkForm, gas_price_gwei: e.target.value})}
                    className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                    placeholder="20.0"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('networks.rpcUrl')}
                </label>
                <input
                  type="url"
                  value={networkForm.rpc_url}
                  onChange={(e) => setNetworkForm({...networkForm, rpc_url: e.target.value})}
                  className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                  placeholder="https://mainnet.infura.io/v3/YOUR_PROJECT_ID"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('networks.explorerUrl')}
                </label>
                <input
                  type="url"
                  value={networkForm.explorer_url}
                  onChange={(e) => setNetworkForm({...networkForm, explorer_url: e.target.value})}
                  className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                  placeholder="https://etherscan.io"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('networks.blockTime')} (seconds)
                  </label>
                  <input
                    type="number"
                    value={networkForm.block_time_seconds}
                    onChange={(e) => setNetworkForm({...networkForm, block_time_seconds: e.target.value})}
                    className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                    placeholder="12"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('networks.confirmationBlocks')}
                  </label>
                  <input
                    type="number"
                    value={networkForm.confirmation_blocks}
                    onChange={(e) => setNetworkForm({...networkForm, confirmation_blocks: e.target.value})}
                    className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                    placeholder="12"
                  />
                </div>
              </div>

              <div className="flex gap-4">
                <label className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <input
                    type="checkbox"
                    checked={networkForm.is_testnet}
                    onChange={(e) => setNetworkForm({...networkForm, is_testnet: e.target.checked})}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">{t('networks.isTestnet')}</span>
                </label>

                <label className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <input
                    type="checkbox"
                    checked={networkForm.is_active}
                    onChange={(e) => setNetworkForm({...networkForm, is_active: e.target.checked})}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">{t('networks.isActive')}</span>
                </label>
              </div>
            </div>

            <div className={`flex gap-3 mt-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <button
                onClick={editingNetwork ? handleUpdateNetwork : handleAddNetwork}
                disabled={isLoading}
                className={`flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
              >
                {isLoading ? (
                  <Clock className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                {isLoading ? t('common.actions.saving') : (editingNetwork ? t('common.actions.update') : t('common.actions.add'))}
              </button>

              <button
                onClick={() => {
                  setShowNetworkModal(false);
                  setEditingNetwork(null);
                  resetNetworkForm();
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg font-medium transition-colors"
              >
                {t('common.actions.cancel')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Token Modal */}
      {showTokenModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto ${isRTL ? 'text-right' : 'text-left'}`}>
            <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {editingToken ? t('tokens.editToken') : t('tokens.addToken')}
              </h3>
              <button
                onClick={() => {
                  setShowTokenModal(false);
                  setEditingToken(null);
                  resetTokenForm();
                }}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <AlertTriangle className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('tokens.selectNetwork')}
                </label>
                <select
                  value={tokenForm.network_id}
                  onChange={(e) => setTokenForm({...tokenForm, network_id: e.target.value})}
                  className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                >
                  <option value="">{t('tokens.selectNetwork')}</option>
                  {supportedNetworks.map((network: any) => (
                    <option key={network.id} value={network.id}>
                      {network.network_name} ({network.network_symbol})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('tokens.contractAddress')}
                </label>
                <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <input
                    type="text"
                    value={tokenForm.token_address}
                    onChange={(e) => setTokenForm({...tokenForm, token_address: e.target.value})}
                    className={`flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                    placeholder="0x..."
                  />
                  <button
                    onClick={handleValidateTokenContract}
                    disabled={isLoading || !tokenForm.token_address || !tokenForm.network_id}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors"
                  >
                    {isLoading ? <Clock className="w-4 h-4 animate-spin" /> : <Search className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('tokens.tokenSymbol')}
                  </label>
                  <input
                    type="text"
                    value={tokenForm.token_symbol}
                    onChange={(e) => setTokenForm({...tokenForm, token_symbol: e.target.value})}
                    className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                    placeholder="USDT"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('tokens.decimals')}
                  </label>
                  <input
                    type="number"
                    value={tokenForm.decimals}
                    onChange={(e) => setTokenForm({...tokenForm, decimals: e.target.value})}
                    className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                    placeholder="18"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('tokens.tokenName')}
                </label>
                <input
                  type="text"
                  value={tokenForm.token_name}
                  onChange={(e) => setTokenForm({...tokenForm, token_name: e.target.value})}
                  className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                  placeholder="Tether USD"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('tokens.minAmount')}
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={tokenForm.min_trade_amount}
                    onChange={(e) => setTokenForm({...tokenForm, min_trade_amount: e.target.value})}
                    className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                    placeholder="1.0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('tokens.maxAmount')}
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={tokenForm.max_trade_amount}
                    onChange={(e) => setTokenForm({...tokenForm, max_trade_amount: e.target.value})}
                    className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                    placeholder="100000.0"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('tokens.platformFee')} (%)
                </label>
                <input
                  type="number"
                  step="0.001"
                  value={tokenForm.platform_fee_rate}
                  onChange={(e) => setTokenForm({...tokenForm, platform_fee_rate: e.target.value})}
                  className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'text-right' : 'text-left'}`}
                  placeholder="0.005"
                />
              </div>

              <div className="flex gap-4">
                <label className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <input
                    type="checkbox"
                    checked={tokenForm.is_stablecoin}
                    onChange={(e) => setTokenForm({...tokenForm, is_stablecoin: e.target.checked})}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">{t('tokens.isStablecoin')}</span>
                </label>

                <label className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <input
                    type="checkbox"
                    checked={tokenForm.is_active}
                    onChange={(e) => setTokenForm({...tokenForm, is_active: e.target.checked})}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">{t('tokens.isActive')}</span>
                </label>
              </div>
            </div>

            <div className={`flex gap-3 mt-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <button
                onClick={editingToken ? handleUpdateToken : handleAddToken}
                disabled={isLoading}
                className={`flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
              >
                {isLoading ? (
                  <Clock className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                {isLoading ? t('common.actions.saving') : (editingToken ? t('common.actions.update') : t('common.actions.add'))}
              </button>

              <button
                onClick={() => {
                  setShowTokenModal(false);
                  setEditingToken(null);
                  resetTokenForm();
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg font-medium transition-colors"
              >
                {t('common.actions.cancel')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
