# دليل العقود الذكية المحسنة - iKAROS P2P

## نظرة عامة

هذا الدليل يوضح كيفية استخدام وتطوير العقود الذكية المحسنة لمنصة iKAROS P2P للتداول اللامركزي للعملات المستقرة.

## المحتويات

- [البنية العامة](#البنية-العامة)
- [العقود الذكية](#العقود-الذكية)
- [قاعدة البيانات](#قاعدة-البيانات)
- [خدمات Frontend](#خدمات-frontend)
- [واجهات المستخدم](#واجهات-المستخدم)
- [نظام الاختبارات](#نظام-الاختبارات)
- [التوثيق](#التوثيق)

## البنية العامة

### الهيكل التنظيمي

```
iKAROS_P2P_CONTRUCT/
├── contracts/enhanced/          # العقود الذكية المحسنة
├── database/                    # قاعدة البيانات المحسنة
├── api/enhanced-contracts/      # APIs للعقود المحسنة
├── src/services/               # خدمات Frontend
├── src/components/enhanced/    # مكونات UI المحسنة
├── tests/enhanced-contracts/   # اختبارات العقود المحسنة
└── docs/enhanced-contracts/    # التوثيق
```

### المميزات الجديدة

1. **دعم متعدد الشبكات**: BSC Mainnet و Testnet
2. **دعم متعدد العملات**: USDT, USDC, BUSD, DAI
3. **نظام سمعة محسن**: تتبع دقيق لأداء المستخدمين
4. **مزامنة ذكية**: ربط تلقائي بين قاعدة البيانات والعقود الذكية
5. **إدارة متقدمة**: أدوات مراقبة وإدارة شاملة

## العقود الذكية

### العقود المنشورة

#### BSC Testnet
- **Core Escrow**: `0xAb25F1fFDB7fC4EA75abDcc662CeD61E4b20F7f2`
- **Reputation Manager**: `0x56A6914523413b0e7344f57466A6239fCC97b913`
- **Oracle Manager**: `0xB70715392F62628Ccd1258AAF691384bE8C023b6`
- **Admin Manager**: `0x5A9FD8082ADA38678721D59AAB4d4F76883c5575`
- **Escrow Integrator**: `0xc0Ec65ffA95aca485e4C2e9b35B20C85FA36F0e0`

### وظائف العقود

#### Core Escrow Contract
```solidity
// إنشاء صفقة جديدة
function createTrade(
    address token,
    uint256 amount,
    uint256 pricePerToken,
    string memory currency
) external returns (uint256 tradeId)

// الانضمام لصفقة
function joinTrade(uint256 tradeId) external

// تأكيد إرسال الدفع
function confirmPaymentSent(uint256 tradeId) external

// تأكيد استلام الدفع
function confirmPaymentReceived(uint256 tradeId) external

// طلب نزاع
function requestDispute(uint256 tradeId) external
```

#### Reputation Manager Contract
```solidity
// تحديث سمعة المستخدم
function updateReputation(
    address user,
    uint256 networkId,
    int256 change,
    string memory reason
) external

// جلب سمعة المستخدم
function getUserReputation(
    address user,
    uint256 networkId
) external view returns (ReputationData memory)
```

## قاعدة البيانات

### الجداول الجديدة

#### supported_networks
```sql
CREATE TABLE supported_networks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    network_name VARCHAR(100) NOT NULL,
    network_symbol VARCHAR(10) NOT NULL,
    chain_id INT UNIQUE NOT NULL,
    rpc_url VARCHAR(255) NOT NULL,
    explorer_url VARCHAR(255) NOT NULL,
    is_testnet BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE
);
```

#### supported_tokens
```sql
CREATE TABLE supported_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    network_id INT NOT NULL,
    token_address VARCHAR(42) NOT NULL,
    token_symbol VARCHAR(20) NOT NULL,
    token_name VARCHAR(100) NOT NULL,
    decimals INT NOT NULL DEFAULT 18,
    is_stablecoin BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    platform_fee_rate DECIMAL(5, 4) DEFAULT 0.0050
);
```

#### enhanced_user_reputation
```sql
CREATE TABLE enhanced_user_reputation (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    network_id INT NOT NULL,
    reputation_score INT DEFAULT 100,
    reputation_level ENUM('beginner', 'intermediate', 'advanced', 'expert', 'master') DEFAULT 'beginner',
    total_trades INT DEFAULT 0,
    successful_trades INT DEFAULT 0,
    average_rating DECIMAL(3, 2) DEFAULT 0.00
);
```

### Views المحسنة

#### enhanced_platform_stats
```sql
CREATE VIEW enhanced_platform_stats AS
SELECT 
    (SELECT COUNT(*) FROM users WHERE is_active = TRUE) as total_users,
    (SELECT COUNT(*) FROM offers WHERE is_active = TRUE) as active_offers,
    (SELECT COUNT(*) FROM trades WHERE status = 'completed') as completed_trades,
    (SELECT SUM(total_value) FROM trades WHERE status = 'completed') as total_trade_volume,
    (SELECT COUNT(*) FROM enhanced_contract_events WHERE processed = TRUE) as processed_events;
```

## خدمات Frontend

### EnhancedContractService

خدمة التفاعل المباشر مع العقود الذكية:

```typescript
import { enhancedContractService } from '@/services/enhancedContractService';

// الاتصال بالمحفظة
await enhancedContractService.connect();

// إنشاء صفقة
const txHash = await enhancedContractService.createTrade(
    tokenAddress,
    amount,
    pricePerToken,
    currency
);

// جلب تفاصيل الصفقة
const trade = await enhancedContractService.getTrade(tradeId);
```

### EnhancedContractApiService

خدمة API المتكاملة مع قاعدة البيانات:

```typescript
import { enhancedContractApiService } from '@/services/enhancedContractApiService';

// إنشاء عرض مع ربط العقد الذكي
const result = await enhancedContractApiService.createEnhancedOfferWithContract({
    userId: 1,
    offerType: 'sell',
    networkId: 1,
    tokenId: 1,
    amount: '100',
    price: '3.75',
    currency: 'SAR',
    paymentMethods: ['bank_transfer']
});
```

## واجهات المستخدم

### NetworkTokenSelector

مكون لاختيار الشبكة والعملة:

```tsx
import NetworkTokenSelector from '@/components/enhanced/NetworkTokenSelector';

<NetworkTokenSelector
    selectedNetworkId={networkId}
    selectedTokenId={tokenId}
    onNetworkChange={setNetworkId}
    onTokenChange={handleTokenChange}
    showOnlyStablecoins={true}
/>
```

### EnhancedCreateOfferForm

نموذج إنشاء عرض محسن:

```tsx
import EnhancedCreateOfferForm from '@/components/enhanced/EnhancedCreateOfferForm';

<EnhancedCreateOfferForm
    userId={userId}
    onSuccess={handleOfferCreated}
    onCancel={handleCancel}
/>
```

### UserDashboard vs AdminDashboard

#### لوحة تحكم المستخدم العادي
- عرض الإحصائيات الشخصية
- إدارة العروض والصفقات
- اتصال المحفظة
- إعدادات الملف الشخصي

#### لوحة تحكم المدير
- مراقبة النظام الشامل
- إدارة المستخدمين
- إدارة الشبكات والعملات
- مراقبة المزامنة
- اتصال محفظة منفصل للأمان

## نظام الاختبارات

### تشغيل الاختبارات

#### Windows (PowerShell)
```powershell
# تشغيل جميع الاختبارات
.\scripts\test-enhanced-contracts.ps1

# تشغيل اختبارات محددة
.\scripts\test-enhanced-contracts.ps1 -Unit
.\scripts\test-enhanced-contracts.ps1 -Component
.\scripts\test-enhanced-contracts.ps1 -Coverage
```

#### Linux/Mac (Bash)
```bash
# تشغيل جميع الاختبارات
./scripts/test-enhanced-contracts.sh

# تشغيل اختبارات محددة
./scripts/test-enhanced-contracts.sh --unit
./scripts/test-enhanced-contracts.sh --component
./scripts/test-enhanced-contracts.sh --coverage
```

### أنواع الاختبارات

1. **اختبارات الوحدة**: اختبار الخدمات والدوال المنفردة
2. **اختبارات المكونات**: اختبار مكونات React
3. **اختبارات التكامل**: اختبار التفاعل بين الأنظمة
4. **اختبارات الأداء**: قياس أداء النظام
5. **اختبارات الأمان**: فحص الثغرات الأمنية

## التوثيق

### ملفات التوثيق

- `README.md`: الدليل الرئيسي
- `API.md`: توثيق APIs
- `SMART_CONTRACTS.md`: توثيق العقود الذكية
- `DATABASE.md`: توثيق قاعدة البيانات
- `FRONTEND.md`: توثيق Frontend
- `TESTING.md`: دليل الاختبارات
- `DEPLOYMENT.md`: دليل النشر

### أمثلة الاستخدام

راجع مجلد `examples/` للحصول على أمثلة عملية لاستخدام النظام.

## المساهمة

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. كتابة الاختبارات
4. تنفيذ الميزة
5. تشغيل جميع الاختبارات
6. إرسال Pull Request

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- مراجعة التوثيق
- فحص الاختبارات

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.
