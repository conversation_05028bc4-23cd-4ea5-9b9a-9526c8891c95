'use client';

import { useState, useEffect } from 'react';
import {
  Activity,
  CheckCircle,
  AlertCircle,
  Clock,
  DollarSign,
  User,
  Star,
  ShoppingBag,
  MessageSquare,
  Settings,
  ArrowRight,
  Calendar
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet, handleApiError } from '@/utils/apiClient';

interface ActivityItem {
  id: string;
  type: 'trade_completed' | 'trade_created' | 'offer_created' | 'offer_updated' | 'payment_received' | 'review_received' | 'profile_updated' | 'message_received';
  title: string;
  description: string;
  amount?: number;
  currency?: string;
  timestamp: string;
  status: 'success' | 'pending' | 'failed' | 'info';
  relatedUser?: {
    username: string;
    isVerified: boolean;
  };
  metadata?: Record<string, any>;
}

export default function RecentActivityWidget() {
  const { t, formatCurrency, formatDate } = useUserDashboardTranslation();
  const { user } = useAuth();
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // جلب النشاط الأخير
  useEffect(() => {
    const fetchRecentActivity = async () => {
      if (!user?.id) {
        setError('معرف المستخدم غير متاح');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // استدعاء API الحقيقي لجلب النشاط الحديث
        const response = await apiGet(`users/activity.php?user_id=${user.id}&limit=8`);

        if (!response.success) {
          throw new Error(response.message || 'فشل في جلب النشاط الحديث');
        }

        // تحويل البيانات من API إلى تنسيق ActivityItem
        const apiActivities = response.data || [];
        const formattedActivities: ActivityItem[] = apiActivities.map((activity: any) => ({
          id: activity.id.toString(),
          type: mapActivityType(activity.type),
          title: activity.title || 'نشاط',
          description: activity.description || '',
          amount: activity.amount ? parseFloat(activity.amount) : undefined,
          currency: activity.currency || undefined,
          timestamp: activity.timestamp || new Date().toISOString(),
          status: mapActivityStatus(activity.status),
          relatedUser: activity.related_user ? {
            username: activity.related_user.username || 'مستخدم غير معروف',
            isVerified: Boolean(activity.related_user.is_verified)
          } : undefined,
          metadata: activity.metadata || {}
        }));

        setActivities(formattedActivities);
      } catch (err) {
        const errorMessage = handleApiError(err);
        setError(errorMessage);
        console.error('Error fetching recent activity:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchRecentActivity();
  }, [user?.id]);

  // دالة لتحويل نوع النشاط من API
  const mapActivityType = (apiType: string): ActivityItem['type'] => {
    switch (apiType?.toLowerCase()) {
      case 'trade_completed':
        return 'trade_completed';
      case 'trade_created':
        return 'trade_created';
      case 'offer_created':
        return 'offer_created';
      case 'offer_updated':
        return 'offer_updated';
      case 'payment_received':
        return 'payment_received';
      case 'review_received':
        return 'review_received';
      case 'profile_updated':
        return 'profile_updated';
      case 'message_received':
        return 'message_received';
      default:
        return 'trade_created';
    }
  };

  // دالة لتحويل حالة النشاط من API
  const mapActivityStatus = (apiStatus: string): ActivityItem['status'] => {
    switch (apiStatus?.toLowerCase()) {
      case 'success':
        return 'success';
      case 'pending':
        return 'pending';
      case 'failed':
        return 'failed';
      case 'info':
        return 'info';
      default:
        return 'info';
    }
  };

  // الحصول على أيقونة النشاط
  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'trade_completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'trade_created':
        return <Activity className="w-4 h-4 text-blue-500" />;
      case 'offer_created':
      case 'offer_updated':
        return <ShoppingBag className="w-4 h-4 text-purple-500" />;
      case 'payment_received':
        return <DollarSign className="w-4 h-4 text-green-500" />;
      case 'review_received':
        return <Star className="w-4 h-4 text-yellow-500" />;
      case 'message_received':
        return <MessageSquare className="w-4 h-4 text-blue-500" />;
      case 'profile_updated':
        return <Settings className="w-4 h-4 text-gray-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  // الحصول على لون الحالة
  const getStatusColor = (status: ActivityItem['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-600 dark:text-green-400';
      case 'pending':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'failed':
        return 'text-red-600 dark:text-red-400';
      case 'info':
        return 'text-blue-600 dark:text-blue-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  // تنسيق الوقت النسبي
  const getRelativeTime = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'الآن';
    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `منذ ${diffInDays} يوم`;
  };

  // عرض رسالة الخطأ
  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3 text-red-600 dark:text-red-400">
          <AlertCircle className="w-5 h-5" />
          <div>
            <h3 className="font-medium">خطأ في تحميل النشاط الحديث</h3>
            <p className="text-sm mt-1">{error}</p>
          </div>
        </div>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
        >
          إعادة المحاولة
        </button>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            النشاط الأخير
          </h3>
          <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
        </div>
        <div className="space-y-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="flex items-center space-x-3 rtl:space-x-reverse animate-pulse">
              <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full" />
              <div className="flex-1">
                <div className="h-4 w-3/4 bg-gray-200 dark:bg-gray-700 rounded mb-1" />
                <div className="h-3 w-1/2 bg-gray-200 dark:bg-gray-700 rounded" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          النشاط الأخير
        </h3>
        <a 
          href="/user-dashboard/activity" 
          className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center space-x-1 rtl:space-x-reverse"
        >
          <span>عرض الكل</span>
          <ArrowRight className="w-3 h-3" />
        </a>
      </div>

      {activities.length > 0 ? (
        <div className="space-y-4">
          {activities.slice(0, 5).map((activity) => (
            <div key={activity.id} className="flex items-start space-x-3 rtl:space-x-reverse group hover:bg-gray-50 dark:hover:bg-gray-700 p-2 rounded-lg transition-colors">
              {/* Icon */}
              <div className="flex-shrink-0 mt-1">
                {getActivityIcon(activity.type)}
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {activity.title}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                      {activity.description}
                    </p>
                    
                    {/* Additional Info */}
                    <div className="flex items-center space-x-4 rtl:space-x-reverse mt-2">
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {getRelativeTime(activity.timestamp)}
                      </span>
                      
                      {activity.amount && (
                        <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                          {formatCurrency(activity.amount, activity.currency || 'USDT')}
                        </span>
                      )}
                      
                      {activity.relatedUser && (
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          <User className="w-3 h-3 text-gray-400" />
                          <span className="text-xs text-gray-600 dark:text-gray-400">
                            {activity.relatedUser.username}
                          </span>
                          {activity.relatedUser.isVerified && (
                            <CheckCircle className="w-3 h-3 text-green-500" />
                          )}
                        </div>
                      )}
                      
                      {activity.metadata?.rating && (
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          <Star className="w-3 h-3 text-yellow-500 fill-current" />
                          <span className="text-xs text-gray-600 dark:text-gray-400">
                            {activity.metadata.rating}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Status Indicator */}
                  <div className={`flex-shrink-0 w-2 h-2 rounded-full mt-2 ${
                    activity.status === 'success' ? 'bg-green-500' :
                    activity.status === 'pending' ? 'bg-yellow-500' :
                    activity.status === 'failed' ? 'bg-red-500' :
                    'bg-blue-500'
                  }`} />
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <Calendar className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-3" />
          <p className="text-gray-500 dark:text-gray-400 mb-4">لا يوجد نشاط حديث</p>
          <a 
            href="/offers" 
            className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <span>ابدأ التداول</span>
            <ArrowRight className="w-4 h-4" />
          </a>
        </div>
      )}
    </div>
  );
}
