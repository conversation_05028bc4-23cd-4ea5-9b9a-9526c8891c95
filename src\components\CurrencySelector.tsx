'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Search, Check, TrendingUp, RefreshCw } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import {
  getActiveCurrencies,
  getCurrencyDisplay,
  searchCurrencies,
  Currency
} from '@/utils/currencies';
import { currencyConversionService, ConversionResult } from '@/services/currencyConversionService';

interface CurrencySelectorProps {
  selectedCurrency?: string;
  onCurrencyChange: (currency: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  showFlag?: boolean;
  showFullName?: boolean;
  // خصائص جديدة للتحويل
  amount?: number;
  stablecoin?: string;
  showConversion?: boolean;
  onConversionUpdate?: (convertedAmount: number, rate: number) => void;
}

export default function CurrencySelector({
  selectedCurrency = 'SAR',
  onCurrencyChange,
  placeholder,
  disabled = false,
  className = '',
  showFlag = true,
  showFullName = false,
  amount = 0,
  stablecoin = 'USDT',
  showConversion = false,
  onConversionUpdate
}: CurrencySelectorProps) {
  const { t, language } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCurrencies, setFilteredCurrencies] = useState<Currency[]>([]);
  const [conversionResult, setConversionResult] = useState<ConversionResult | null>(null);
  const [isConverting, setIsConverting] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const activeCurrencies = getActiveCurrencies();
  const selectedCurrencyData = getCurrencyDisplay(selectedCurrency, language as 'ar' | 'en');

  useEffect(() => {
    if (searchQuery) {
      setFilteredCurrencies(searchCurrencies(searchQuery, language as 'ar' | 'en'));
    } else {
      setFilteredCurrencies(activeCurrencies);
    }
  }, [searchQuery, language]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchQuery('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  // تحديث التحويل عند تغيير المبلغ أو العملة
  useEffect(() => {
    if (showConversion && amount > 0 && selectedCurrency !== stablecoin) {
      convertCurrency();
    }
  }, [amount, selectedCurrency, stablecoin, showConversion]);

  const convertCurrency = async () => {
    if (!amount || amount <= 0) return;

    setIsConverting(true);
    try {
      const result = await currencyConversionService.convertAmount(
        amount,
        stablecoin,
        selectedCurrency
      );
      setConversionResult(result);

      // إشعار المكون الأب بالتحويل الجديد
      if (onConversionUpdate) {
        onConversionUpdate(result.convertedAmount, result.rate);
      }
    } catch (error) {
      console.error('خطأ في تحويل العملة:', error);
      setConversionResult(null);
    } finally {
      setIsConverting(false);
    }
  };

  const refreshConversion = () => {
    if (showConversion && amount > 0) {
      convertCurrency();
    }
  };

  const handleCurrencySelect = (currencyCode: string) => {
    onCurrencyChange(currencyCode);
    setIsOpen(false);
    setSearchQuery('');
  };

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  return (
    <div className={`relative currency-selector ${className}`} ref={dropdownRef}>
      {/* المحدد الرئيسي */}
      <button
        type="button"
        onClick={toggleDropdown}
        disabled={disabled}
        className={`currency-selector-button w-full flex items-center px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:border-gray-400 dark:hover:border-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors relative ${
          disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
        }`}
      >
        <div className="flex items-center space-x-3 rtl:space-x-reverse flex-1">
          {showFlag && (
            <span className="text-lg">{selectedCurrencyData.flag}</span>
          )}
          <div className="text-right">
            <div className="font-medium text-gray-900 dark:text-white">
              {selectedCurrencyData.code}
            </div>
            {showFullName && (
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {selectedCurrencyData.name}
              </div>
            )}
          </div>
          <div className="text-lg font-bold text-gray-600 dark:text-gray-300">
            {selectedCurrencyData.symbol}
          </div>
        </div>

        {/* السهم في موضع منفصل */}
        <div className="currency-dropdown-arrow">
          <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>
      </button>

      {/* معلومات التحويل */}
      {showConversion && conversionResult && (
        <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <TrendingUp className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              <span className="text-sm text-blue-800 dark:text-blue-300">
                {amount.toLocaleString('en-US')} {stablecoin} = {conversionResult.convertedAmount.toLocaleString('en-US')} {selectedCurrency}
              </span>
            </div>
            <button
              onClick={refreshConversion}
              disabled={isConverting}
              className="p-1 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isConverting ? 'animate-spin' : ''}`} />
            </button>
          </div>
          <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
            سعر الصرف: 1 {stablecoin} = {conversionResult.rate.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 4 })} {selectedCurrency}
          </div>
        </div>
      )}

      {/* القائمة المنسدلة */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-80 overflow-hidden">
          {/* شريط البحث */}
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <div className="relative">
              <Search className="absolute ltr:left-3 rtl:right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={placeholder || t('common.search')}
                className="w-full ltr:pl-10 ltr:pr-4 rtl:pr-10 rtl:pl-4 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white"
              />
            </div>
          </div>

          {/* قائمة العملات */}
          <div className="max-h-60 overflow-y-auto">
            {filteredCurrencies.length === 0 ? (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                {t('currencies.noResults')}
              </div>
            ) : (
              filteredCurrencies.map((currency) => {
                const currencyDisplay = getCurrencyDisplay(currency.code, language as 'ar' | 'en');
                const isSelected = currency.code === selectedCurrency;
                
                return (
                  <button
                    key={currency.code}
                    onClick={() => handleCurrencySelect(currency.code)}
                    className={`w-full flex items-center justify-between px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                      isSelected ? 'bg-blue-50 dark:bg-blue-900/20 ltr:border-l-2 rtl:border-r-2 border-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      {showFlag && (
                        <span className="text-lg">{currencyDisplay.flag}</span>
                      )}
                      <div className="ltr:text-left rtl:text-right">
                        <div className={`font-medium ${isSelected ? 'text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-white'}`}>
                          {currencyDisplay.code}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {currencyDisplay.name}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <span className={`text-lg font-bold ${isSelected ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300'}`}>
                        {currencyDisplay.symbol}
                      </span>
                      {isSelected && (
                        <Check className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      )}
                    </div>
                  </button>
                );
              })
            )}
          </div>

          {/* معلومات إضافية */}
          <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
            <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
              {t('currencies.supportedCount', { count: activeCurrencies.length })}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

// مكون مبسط للعرض فقط
export function CurrencyDisplay({ 
  currencyCode, 
  amount, 
  showFlag = true,
  className = '' 
}: {
  currencyCode: string;
  amount?: number;
  showFlag?: boolean;
  className?: string;
}) {
  const { language } = useTranslation();
  const currencyData = getCurrencyDisplay(currencyCode, language as 'ar' | 'en');

  return (
    <div className={`flex items-center space-x-2 rtl:space-x-reverse ${className}`}>
      {showFlag && (
        <span className="text-sm">{currencyData.flag}</span>
      )}
      <span className="font-medium text-gray-900 dark:text-white">
        {currencyData.code}
      </span>
      <span className="text-gray-600 dark:text-gray-300">
        {currencyData.symbol}
      </span>
      {amount !== undefined && (
        <span className="font-bold text-gray-900 dark:text-white">
          {amount.toLocaleString()}
        </span>
      )}
    </div>
  );
}
