<?php
/**
 * API endpoint لسجلات الأمان للمستخدمين
 * User Security Logs API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // جلب سجلات الأمان للمستخدم
        $userId = $_GET['user_id'] ?? null;
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        
        $logType = $_GET['type'] ?? 'all'; // all, login, logout, password_change, security_alert
        $dateFrom = $_GET['date_from'] ?? null;
        $dateTo = $_GET['date_to'] ?? null;
        
        if (!$userId) {
            throw new Exception('معرف المستخدم مطلوب');
        }
        
        // بناء الاستعلام
        $whereConditions = ['user_id = ?'];
        $params = [$userId];
        
        if ($logType !== 'all') {
            $whereConditions[] = 'log_type = ?';
            $params[] = $logType;
        }
        
        if ($dateFrom) {
            $whereConditions[] = 'created_at >= ?';
            $params[] = $dateFrom . ' 00:00:00';
        }
        
        if ($dateTo) {
            $whereConditions[] = 'created_at <= ?';
            $params[] = $dateTo . ' 23:59:59';
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // الحصول على العدد الإجمالي
        $countStmt = $connection->prepare("
            SELECT COUNT(*) as total
            FROM security_logs
            WHERE $whereClause
        ");
        $countStmt->execute($params);
        $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // جلب السجلات
        $stmt = $connection->prepare("
            SELECT 
                id,
                log_type,
                action_description,
                ip_address,
                user_agent,
                location_info,
                risk_level,
                additional_data,
                created_at
            FROM security_logs
            WHERE $whereClause
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تنسيق البيانات
        foreach ($logs as &$log) {
            $log['additional_data'] = $log['additional_data'] ? json_decode($log['additional_data'], true) : null;
            $log['location_info'] = $log['location_info'] ? json_decode($log['location_info'], true) : null;
            $log['risk_level'] = intval($log['risk_level']);
        }
        
        echo json_encode([
            'success' => true,
            'data' => $logs,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => intval($totalCount),
                'total_pages' => ceil($totalCount / $limit)
            ]
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // إضافة سجل أمان جديد
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $requiredFields = ['user_id', 'log_type', 'action_description'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                throw new Exception("الحقل $field مطلوب");
            }
        }
        
        // التحقق من صحة نوع السجل
        $validLogTypes = ['login', 'logout', 'password_change', 'email_change', 'phone_change', 
                         'two_factor_enabled', 'two_factor_disabled', 'suspicious_activity', 
                         'account_locked', 'account_unlocked', 'security_alert'];
        
        if (!in_array($input['log_type'], $validLogTypes)) {
            throw new Exception('نوع السجل غير صحيح');
        }
        
        // الحصول على معلومات إضافية
        $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        // تحديد مستوى المخاطر
        $riskLevel = calculateRiskLevel($input['log_type'], $input['additional_data'] ?? []);
        
        // إدراج السجل
        $stmt = $connection->prepare("
            INSERT INTO security_logs (
                user_id, log_type, action_description, ip_address, 
                user_agent, location_info, risk_level, additional_data, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ");
        
        $stmt->execute([
            $input['user_id'],
            $input['log_type'],
            $input['action_description'],
            $ipAddress,
            $userAgent,
            isset($input['location_info']) ? json_encode($input['location_info']) : null,
            $riskLevel,
            isset($input['additional_data']) ? json_encode($input['additional_data']) : null
        ]);
        
        $logId = $connection->lastInsertId();
        
        // إذا كان مستوى المخاطر عالي، أرسل تنبيه
        if ($riskLevel >= 7) {
            sendSecurityAlert($connection, $input['user_id'], $input['log_type'], $input['action_description']);
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة سجل الأمان بنجاح',
            'data' => ['id' => $logId, 'risk_level' => $riskLevel]
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    error_log('Database error in users/security-logs.php: ' . $e->getMessage());
}

/**
 * حساب مستوى المخاطر للنشاط
 */
function calculateRiskLevel($logType, $additionalData) {
    $riskLevels = [
        'login' => 2,
        'logout' => 1,
        'password_change' => 5,
        'email_change' => 6,
        'phone_change' => 5,
        'two_factor_enabled' => 3,
        'two_factor_disabled' => 7,
        'suspicious_activity' => 9,
        'account_locked' => 8,
        'account_unlocked' => 6,
        'security_alert' => 8
    ];
    
    $baseRisk = $riskLevels[$logType] ?? 5;
    
    // تعديل المخاطر بناءً على البيانات الإضافية
    if (isset($additionalData['failed_attempts']) && $additionalData['failed_attempts'] > 3) {
        $baseRisk += 2;
    }
    
    if (isset($additionalData['new_location']) && $additionalData['new_location']) {
        $baseRisk += 1;
    }
    
    if (isset($additionalData['unusual_time']) && $additionalData['unusual_time']) {
        $baseRisk += 1;
    }
    
    return min(10, max(1, $baseRisk));
}

/**
 * إرسال تنبيه أمني
 */
function sendSecurityAlert($connection, $userId, $logType, $description) {
    try {
        // الحصول على بيانات المستخدم
        $stmt = $connection->prepare("
            SELECT email, full_name, security_settings 
            FROM users 
            WHERE id = ?
        ");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            $securitySettings = json_decode($user['security_settings'], true);
            
            // إذا كانت التنبيهات الأمنية مفعلة
            if ($securitySettings['suspicious_activity_alerts'] ?? true) {
                // إدراج إشعار في قاعدة البيانات
                $notificationStmt = $connection->prepare("
                    INSERT INTO notifications (
                        user_id, type, title, message, is_read, created_at
                    ) VALUES (?, ?, ?, ?, 0, CURRENT_TIMESTAMP)
                ");
                
                $notificationStmt->execute([
                    $userId,
                    'security_alert',
                    'تنبيه أمني',
                    "تم اكتشاف نشاط مشبوه: $description"
                ]);
                
                // يمكن إضافة إرسال بريد إلكتروني هنا
            }
        }
    } catch (Exception $e) {
        error_log('Error sending security alert: ' . $e->getMessage());
    }
}
?>
