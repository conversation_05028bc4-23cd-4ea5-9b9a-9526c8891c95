import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';

export const useTradeTranslation = () => {
  const { t, i18n } = useTranslation('trade');
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const loadTranslations = async () => {
      try {
        if (!i18n.hasResourceBundle(i18n.language, 'trade')) {
          // تحميل ترجمات التداول
          const currentLang = i18n.language;
          const tradeTranslations = await import(`../../public/locales/${currentLang}/trade.json`);
          
          i18n.addResourceBundle(currentLang, 'trade', tradeTranslations.default, true, true);
        }
        setIsLoaded(true);
      } catch (error) {
        console.error('Error loading trade translations:', error);
        // fallback للإنجليزية
        try {
          const fallbackTranslations = await import(`../../public/locales/en/trade.json`);
          i18n.addResourceBundle('en', 'trade', fallbackTranslations.default, true, true);
          setIsLoaded(true);
        } catch (fallbackError) {
          console.error('Error loading fallback trade translations:', fallbackError);
          setIsLoaded(true); // تحميل حتى لو فشل
        }
      }
    };

    loadTranslations();
  }, [i18n]);

  return { t, isLoaded };
};
