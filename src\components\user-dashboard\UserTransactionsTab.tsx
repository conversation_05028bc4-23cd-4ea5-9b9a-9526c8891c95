'use client';

import { useState, useEffect } from 'react';
import {
  CreditCard,
  ArrowUpRight,
  ArrowDownLeft,
  Filter,
  Search,
  Download,
  Eye,
  Copy,
  ExternalLink,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet, handleApiError } from '@/utils/apiClient';

// أنواع البيانات
type TransactionType = 'deposit' | 'withdrawal' | 'trade' | 'fee' | 'refund';
type TransactionStatus = 'pending' | 'completed' | 'failed' | 'cancelled';

interface Transaction {
  id: string;
  type: 'deposit' | 'withdrawal' | 'trade' | 'fee' | 'refund';
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  date: string;
  description: string;
  hash?: string;
  fromAddress?: string;
  toAddress?: string;
  gasUsed?: number;
  gasFee?: number;
}

// دالة تحويل نوع المعاملة
const mapTransactionType = (type: string): TransactionType => {
  switch (type?.toLowerCase()) {
    case 'deposit':
    case 'إيداع':
      return 'deposit';
    case 'withdrawal':
    case 'سحب':
      return 'withdrawal';
    case 'trade':
    case 'تداول':
      return 'trade';
    case 'fee':
    case 'رسوم':
      return 'fee';
    case 'refund':
    case 'استرداد':
      return 'refund';
    default:
      return 'trade';
  }
};

export default function UserTransactionsTab() {
  const { formatCurrency, formatDate } = useUserDashboardTranslation();
  const { user } = useAuth();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<TransactionType | 'all'>('all');
  const [filterStatus, setFilterStatus] = useState<TransactionStatus | 'all'>('all');
  const [showFilters, setShowFilters] = useState(false);

  // جلب المعاملات من API
  useEffect(() => {
    const fetchTransactions = async () => {
      if (!user?.id) {
        setError('معرف المستخدم غير متاح');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // استدعاء API الحقيقي لجلب معاملات المحفظة
        const response = await apiGet(`transactions/index.php?user_id=${user.id}&limit=50`);

        if (!response.success) {
          throw new Error(response.message || 'فشل في جلب المعاملات');
        }

        // تحويل البيانات من API إلى تنسيق Transaction
        const apiTransactions = response.data?.transactions || response.data || [];
        const formattedTransactions: Transaction[] = Array.isArray(apiTransactions) ? apiTransactions.map((transaction: any) => ({
          id: transaction.id.toString(),
          type: mapTransactionType(transaction.type || transaction.transaction_type),
          amount: parseFloat(transaction.amount) || 0,
          currency: transaction.currency || 'USDT',
          status: transaction.status as TransactionStatus || 'completed',
          date: transaction.timestamp || transaction.created_at || new Date().toISOString(),
          description: transaction.description || `${transaction.type || transaction.transaction_type} ${transaction.currency}`,
          hash: transaction.tx_hash || undefined,
          fromAddress: transaction.from_address || undefined,
          toAddress: transaction.to_address || undefined,
          gasUsed: transaction.gas_used ? parseInt(transaction.gas_used) : undefined,
          gasFee: transaction.gas_fee ? parseFloat(transaction.gas_fee) : undefined
        })) : [];

        setTransactions(formattedTransactions);
      } catch (error) {
        console.error('Error fetching transactions:', error);
        const errorMessage = handleApiError(error);
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchTransactions();
  }, [user?.id]);

  // البيانات الوهمية للعرض في حالة عدم وجود بيانات حقيقية
  const mockTransactions: Transaction[] = [
    {
      id: '1',
      type: 'deposit',
      amount: 1000,
      currency: 'USDT',
      status: 'completed',
      date: '2024-01-15T10:30:00Z',
      description: 'إيداع USDT',
      hash: '0x1234567890abcdef1234567890abcdef12345678',
      toAddress: '0x742d35Cc6634C0532925a3b8D4C0532925a3b8D4',
      gasUsed: 21000,
      gasFee: 0.002
    },
    {
      id: '2',
      type: 'withdrawal',
      amount: 500,
      currency: 'USDT',
      status: 'pending',
      date: '2024-01-14T15:45:00Z',
      description: 'سحب USDT',
      hash: '0xabcdef1234567890abcdef1234567890abcdef12',
      fromAddress: '0x742d35Cc6634C0532925a3b8D4C0532925a3b8D4',
      gasUsed: 21000,
      gasFee: 0.003
    },
    {
      id: '3',
      type: 'trade',
      amount: 250,
      currency: 'USDT',
      status: 'completed',
      date: '2024-01-13T09:20:00Z',
      description: 'رسوم صفقة #12345',
      hash: '0x567890abcdef1234567890abcdef1234567890ab',
      gasUsed: 45000,
      gasFee: 0.005
    },
    {
      id: '4',
      type: 'fee',
      amount: 15,
      currency: 'USDT',
      status: 'completed',
      date: '2024-01-12T14:10:00Z',
      description: 'رسوم منصة',
      hash: '0x890abcdef1234567890abcdef1234567890abcdef',
      gasUsed: 21000,
      gasFee: 0.001
    }
  ];

  // استخدام البيانات الحقيقية إذا كانت متوفرة، وإلا استخدام البيانات الوهمية
  const displayTransactions = transactions.length > 0 ? transactions : mockTransactions;

  const filteredTransactions = displayTransactions.filter(transaction => {
    const matchesType = filterType === 'all' || transaction.type === filterType;
    const matchesStatus = filterStatus === 'all' || transaction.status === filterStatus;
    const matchesSearch = searchTerm === '' ||
      transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.hash?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesType && matchesStatus && matchesSearch;
  });

  const getTypeIcon = (type: Transaction['type']) => {
    switch (type) {
      case 'deposit':
        return <ArrowDownLeft className="w-5 h-5 text-green-600 dark:text-green-400" />;
      case 'withdrawal':
        return <ArrowUpRight className="w-5 h-5 text-red-600 dark:text-red-400" />;
      case 'trade':
        return <CreditCard className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
      case 'fee':
        return <CreditCard className="w-5 h-5 text-orange-600 dark:text-orange-400" />;
      case 'refund':
        return <ArrowDownLeft className="w-5 h-5 text-purple-600 dark:text-purple-400" />;
      default:
        return <CreditCard className="w-5 h-5 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getStatusIcon = (status: Transaction['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600 dark:text-red-400" />;
      case 'cancelled':
        return <AlertCircle className="w-4 h-4 text-gray-600 dark:text-gray-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getTypeText = (type: Transaction['type']) => {
    switch (type) {
      case 'deposit': return 'إيداع';
      case 'withdrawal': return 'سحب';
      case 'trade': return 'تداول';
      case 'fee': return 'رسوم';
      case 'refund': return 'استرداد';
      default: return type;
    }
  };

  const getStatusText = (status: Transaction['status']) => {
    switch (status) {
      case 'completed': return 'مكتملة';
      case 'pending': return 'معلقة';
      case 'failed': return 'فاشلة';
      case 'cancelled': return 'ملغية';
      default: return status;
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="space-y-6">
      {/* رأس القسم */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <CreditCard className="w-6 h-6 ml-3 text-blue-600" />
            المعاملات
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            تتبع جميع معاملاتك المالية
          </p>
        </div>
        <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
          <Download className="w-4 h-4" />
          <span>تصدير</span>
        </button>
      </div>

      {/* حالة التحميل */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="mr-3 text-gray-600 dark:text-gray-400">جاري تحميل المعاملات...</span>
        </div>
      )}

      {/* حالة الخطأ */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 ml-3" />
            <span className="text-red-800 dark:text-red-200">{error}</span>
          </div>
        </div>
      )}

      {/* الفلاتر والبحث */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              البحث
            </label>
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث في المعاملات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              نوع المعاملة
            </label>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as typeof filterType)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">جميع الأنواع</option>
              <option value="deposit">إيداع</option>
              <option value="withdrawal">سحب</option>
              <option value="trade">تداول</option>
              <option value="fee">رسوم</option>
              <option value="refund">استرداد</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              الحالة
            </label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as typeof filterStatus)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">جميع الحالات</option>
              <option value="completed">مكتملة</option>
              <option value="pending">معلقة</option>
              <option value="failed">فاشلة</option>
              <option value="cancelled">ملغية</option>
            </select>
          </div>

          <div className="flex items-end">
            <button className="w-full flex items-center justify-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
              <Filter className="w-4 h-4" />
              <span>تطبيق الفلاتر</span>
            </button>
          </div>
        </div>
      </div>

      {/* قائمة المعاملات */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  المعاملة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  المبلغ
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  التاريخ
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredTransactions.map((transaction) => (
                <tr key={transaction.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 ml-3">
                        {getTypeIcon(transaction.type)}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {getTypeText(transaction.type)}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {transaction.description}
                        </div>
                        {transaction.hash && (
                          <div className="text-xs text-gray-400 dark:text-gray-500 font-mono">
                            {transaction.hash.slice(0, 10)}...{transaction.hash.slice(-8)}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`text-sm font-medium ${
                      transaction.type === 'deposit' || transaction.type === 'refund'
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {transaction.type === 'deposit' || transaction.type === 'refund' ? '+' : '-'}
                      {formatCurrency(transaction.amount, transaction.currency)}
                    </div>
                    {transaction.gasFee && (
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        رسوم الغاز: {transaction.gasFee} ETH
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getStatusIcon(transaction.status)}
                      <span className={`mr-2 text-sm ${
                        transaction.status === 'completed'
                          ? 'text-green-600 dark:text-green-400'
                          : transaction.status === 'pending'
                          ? 'text-yellow-600 dark:text-yellow-400'
                          : transaction.status === 'failed'
                          ? 'text-red-600 dark:text-red-400'
                          : 'text-gray-600 dark:text-gray-400'
                      }`}>
                        {getStatusText(transaction.status)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {formatDate(new Date(transaction.date), {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <button
                        onClick={() => copyToClipboard(transaction.hash || '')}
                        className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                        title="نسخ الهاش"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                      {transaction.hash && (
                        <a
                          href={`https://testnet.bscscan.com/tx/${transaction.hash}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                          title="عرض في المستكشف"
                        >
                          <ExternalLink className="w-4 h-4" />
                        </a>
                      )}
                      <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200">
                        <Eye className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredTransactions.length === 0 && (
          <div className="text-center py-12">
            <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              لا توجد معاملات
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {searchTerm || filterType !== 'all' || filterStatus !== 'all'
                ? 'لا توجد معاملات تطابق الفلاتر المحددة'
                : 'لم تقم بأي معاملات بعد'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
