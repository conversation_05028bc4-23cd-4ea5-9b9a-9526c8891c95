import Header from '@/components/Header';
import TradePage from '@/components/TradePage';
import Footer from '@/components/Footer';

interface TradePageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function Trade({ params }: TradePageProps) {
  const { id } = await params;

  return (
    <div className="min-h-screen">
      <Header />
      <TradePage tradeId={id} />
      <Footer />
    </div>
  );
}
