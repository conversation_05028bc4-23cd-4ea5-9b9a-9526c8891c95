import { Language } from '@/contexts/LanguageContext';
import arTranslations from '@/locales/ar.json';
import enTranslations from '@/locales/en.json';

const translations = {
  ar: arTranslations,
  en: enTranslations
};

export function getTranslation(key: string, language: Language): string {
  const keys = key.split('.');
  let value: any = translations[language];
  
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      // Fallback to Arabic if key not found
      value = translations.ar;
      for (const fallbackKey of keys) {
        if (value && typeof value === 'object' && fallbackKey in value) {
          value = value[fallbackKey];
        } else {
          return key; // Return key if translation not found
        }
      }
      break;
    }
  }
  
  return typeof value === 'string' ? value : key;
}

export function createTranslationFunction(language: Language) {
  return (key: string, params?: Record<string, string | number>) => {
    let translation = getTranslation(key, language);
    
    // Replace parameters in translation
    if (params) {
      Object.entries(params).forEach(([param, value]) => {
        translation = translation.replace(new RegExp(`{{${param}}}`, 'g'), String(value));
      });
    }
    
    return translation;
  };
}
