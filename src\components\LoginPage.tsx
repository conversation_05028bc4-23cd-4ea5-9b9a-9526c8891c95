'use client';

import { useState } from 'react';
import {
  Mail,
  Lock,
  Eye,
  EyeOff,
  Shield,
  ArrowRight,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { notificationService } from '@/services/notificationService';

export default function LoginPage() {
  const { t } = useTranslation();
  const { login } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // إزالة الخطأ عند التعديل
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    // التحقق من البريد الإلكتروني
    if (!formData.email.trim()) {
      newErrors.email = t('auth.validation.emailRequired');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('auth.validation.emailInvalid');
    }

    // التحقق من كلمة المرور
    if (!formData.password) {
      newErrors.password = t('auth.validation.passwordRequired');
    } else if (formData.password.length < 8) {
      newErrors.password = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }

    // التحقق من قوة كلمة المرور للأمان
    if (formData.password && formData.password.length >= 8) {
      const hasUpperCase = /[A-Z]/.test(formData.password);
      const hasLowerCase = /[a-z]/.test(formData.password);
      const hasNumbers = /\d/.test(formData.password);
      const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(formData.password);

      if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
        newErrors.password = t('validation.passwordRequirements');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // تنظيف البيانات قبل الإرسال
      const cleanEmail = formData.email.trim().toLowerCase();
      const cleanPassword = formData.password.trim();

      await login(cleanEmail, cleanPassword);

      // حفظ تفضيل "تذكرني" إذا تم اختياره
      if (formData.rememberMe) {
        localStorage.setItem('rememberLogin', 'true');
        localStorage.setItem('lastLoginEmail', cleanEmail);
      } else {
        localStorage.removeItem('rememberLogin');
        localStorage.removeItem('lastLoginEmail');
      }

      notificationService.success(t('auth.messages.loginSuccess'));
    } catch (error: any) {
      // تسجيل الخطأ للمراقبة
      console.error('Login error:', error);
      notificationService.error(error.message || t('auth.messages.error'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* خلفية زخرفية */}
      <div className="absolute inset-0 opacity-5 dark:opacity-10">
        <div className="absolute top-0 left-0 w-40 h-40 bg-blue-500 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-60 h-60 bg-purple-500 rounded-full filter blur-3xl"></div>
      </div>

      <div className="max-w-md w-full space-y-8 relative z-10">
        {/* الهيدر */}
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg transform hover:rotate-6 transition-transform duration-300">
              <span className="text-white font-bold text-3xl">إ</span>
            </div>
          </div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
            {t('auth.login.title')}
          </h2>
          <p className="mt-2 text-gray-600 dark:text-gray-300">
            {t('auth.login.subtitle')}
          </p>
        </div>

        {/* النموذج */}
        <form className="mt-8 space-y-6 bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700" onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* البريد الإلكتروني */}
            <div>
              <label className="form-label">{t('auth.login.email')}</label>
              <div className="input-container input-with-icon">
                <Mail className="input-icon-right w-5 h-5" />
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`form-input form-input-with-icon ${errors.email ? 'border-danger-500' : ''}`}
                  placeholder={t('auth.login.emailPlaceholder')}
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-danger-600 flex items-center">
                  <AlertCircle className="w-4 h-4 ml-1" />
                  {errors.email}
                </p>
              )}
            </div>

            {/* كلمة المرور */}
            <div>
              <label className="form-label">{t('auth.login.password')}</label>
              <div className="input-container input-with-icon has-left-icon">
                <Lock className="input-icon-right w-5 h-5" />
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className={`form-input form-input-with-icon ${errors.password ? 'border-danger-500' : ''}`}
                  placeholder={t('auth.login.passwordPlaceholder')}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="input-icon-left text-gray-400 hover:text-gray-600 cursor-pointer"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-danger-600 flex items-center">
                  <AlertCircle className="w-4 h-4 ml-1" />
                  {errors.password}
                </p>
              )}
            </div>
          </div>

          {/* تذكرني ونسيت كلمة المرور */}
          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                type="checkbox"
                name="rememberMe"
                checked={formData.rememberMe}
                onChange={handleInputChange}
                className="ml-2"
              />
              <span className="text-sm text-gray-600 dark:text-gray-300">{t('auth.login.rememberMe')}</span>
            </label>
            <a href="#" className="text-sm text-primary-600 hover:text-primary-700 font-medium">
              {t('auth.login.forgotPassword')}
            </a>
          </div>

          {/* زر الإرسال */}
          <button
            type="submit"
            disabled={isLoading}
            className={`w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold py-4 px-6 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl text-lg ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
                {t('auth.messages.processing')}
              </div>
            ) : (
              <div className="flex items-center justify-center">
                {t('auth.login.loginButton')}
                <ArrowRight className="w-5 h-5 mr-2" />
              </div>
            )}
          </button>

          {/* رابط التسجيل */}
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-300">
              {t('auth.login.noAccount')}
              {' '}
              <a
                href="/register"
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                {t('auth.login.createAccount')}
              </a>
            </p>
          </div>
        </form>

        {/* ميزات الأمان */}
        <div className="mt-8 bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <Shield className="w-5 h-5 text-primary-600 dark:text-primary-400 ml-2" />
            <span className="font-medium text-gray-900 dark:text-white">{t('auth.security.title')}</span>
          </div>
          <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
            <li className="flex items-center">
              <CheckCircle className="w-4 h-4 text-success-500 ml-2" />
              {t('auth.security.ssl')}
            </li>
            <li className="flex items-center">
              <CheckCircle className="w-4 h-4 text-success-500 ml-2" />
              {t('auth.security.twoFactor')}
            </li>
            <li className="flex items-center">
              <CheckCircle className="w-4 h-4 text-success-500 ml-2" />
              {t('auth.security.fraudProtection')}
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}