'use client';

import { ReactNode } from 'react';
import { useTranslation } from '@/hooks/useTranslation';

interface TranslatedContentProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export default function TranslatedContent({ children, fallback }: TranslatedContentProps) {
  const { isInitialized } = useTranslation();

  // أثناء SSR أو قبل التهيئة، اعرض fallback أو محتوى فارغ
  if (!isInitialized) {
    return <>{fallback || <div className="opacity-0">{children}</div>}</>;
  }

  return <>{children}</>;
}
