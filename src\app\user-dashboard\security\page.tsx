'use client';

import { useState } from 'react';
import { 
  Shield, 
  Smartphone, 
  Key, 
  Eye, 
  EyeOff, 
  Clock, 
  MapPin, 
  Monitor, 
  AlertTriangle,
  CheckCircle,
  X,
  RefreshCw,
  Download
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';

interface SecuritySession {
  id: string;
  device: string;
  location: string;
  ip: string;
  lastActivity: string;
  isCurrent: boolean;
  userAgent: string;
}

interface LoginAttempt {
  id: string;
  ip: string;
  location: string;
  userAgent: string;
  timestamp: string;
  success: boolean;
  suspicious: boolean;
}

export default function SecurityPage() {
  const { t, formatDate } = useUserDashboardTranslation();
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [showBackupCodes, setShowBackupCodes] = useState(false);
  const [sessions, setSessions] = useState<SecuritySession[]>([
    {
      id: '1',
      device: 'Chrome على Windows',
      location: 'الرياض، السعودية',
      ip: '*************',
      lastActivity: new Date().toISOString(),
      isCurrent: true,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    },
    {
      id: '2',
      device: 'Safari على iPhone',
      location: 'جدة، السعودية',
      ip: '*************',
      lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      isCurrent: false,
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)'
    }
  ]);

  const [loginHistory, setLoginHistory] = useState<LoginAttempt[]>([
    {
      id: '1',
      ip: '*************',
      location: 'الرياض، السعودية',
      userAgent: 'Chrome على Windows',
      timestamp: new Date().toISOString(),
      success: true,
      suspicious: false
    },
    {
      id: '2',
      ip: '*********',
      location: 'دبي، الإمارات',
      userAgent: 'Firefox على Linux',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      success: false,
      suspicious: true
    }
  ]);

  const handleToggleTwoFactor = () => {
    setTwoFactorEnabled(!twoFactorEnabled);
  };

  const handleTerminateSession = (sessionId: string) => {
    setSessions(sessions.filter(session => session.id !== sessionId));
  };

  const handleTerminateAllSessions = () => {
    setSessions(sessions.filter(session => session.isCurrent));
  };

  const backupCodes = [
    '1234-5678-9012',
    '3456-7890-1234',
    '5678-9012-3456',
    '7890-1234-5678',
    '9012-3456-7890'
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <Shield className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('security.title')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {t('security.subtitle')}
            </p>
          </div>
        </div>
      </div>

      {/* Two-Factor Authentication */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <Smartphone className="w-6 h-6 text-green-600 dark:text-green-400" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t('security.twoFactor.title')}
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {t('security.twoFactor.description')}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              twoFactorEnabled 
                ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                : 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300'
            }`}>
              {twoFactorEnabled ? t('security.twoFactor.enabled') : t('security.twoFactor.disabled')}
            </span>
            <button
              onClick={handleToggleTwoFactor}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                twoFactorEnabled
                  ? 'bg-red-600 hover:bg-red-700 text-white'
                  : 'bg-green-600 hover:bg-green-700 text-white'
              }`}
            >
              {twoFactorEnabled ? t('security.twoFactor.disable') : t('security.twoFactor.enable')}
            </button>
          </div>
        </div>

        {twoFactorEnabled && (
          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium text-gray-900 dark:text-white">
                {t('security.twoFactor.backupCodes')}
              </h3>
              <button
                onClick={() => setShowBackupCodes(!showBackupCodes)}
                className="text-blue-600 dark:text-blue-400 hover:underline flex items-center space-x-1 rtl:space-x-reverse"
              >
                {showBackupCodes ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                <span>{showBackupCodes ? 'إخفاء' : 'عرض'}</span>
              </button>
            </div>
            
            {showBackupCodes && (
              <div className="space-y-2">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {backupCodes.map((code, index) => (
                    <div key={index} className="bg-white dark:bg-gray-800 p-2 rounded border font-mono text-sm">
                      {code}
                    </div>
                  ))}
                </div>
                <button className="mt-3 flex items-center space-x-2 rtl:space-x-reverse text-blue-600 dark:text-blue-400 hover:underline">
                  <Download className="w-4 h-4" />
                  <span>تحميل الرموز</span>
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Active Sessions */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <Monitor className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t('security.sessions.title')}
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {t('security.sessions.description')}
              </p>
            </div>
          </div>
          <button
            onClick={handleTerminateAllSessions}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors"
          >
            {t('security.sessions.terminateAll')}
          </button>
        </div>

        <div className="space-y-3">
          {sessions.map((session) => (
            <div key={session.id} className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse mb-2">
                    <Monitor className="w-5 h-5 text-gray-400" />
                    <span className="font-medium text-gray-900 dark:text-white">
                      {session.device}
                    </span>
                    {session.isCurrent && (
                      <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs rounded-full">
                        {t('security.sessions.currentSession')}
                      </span>
                    )}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <MapPin className="w-4 h-4" />
                      <span>{session.location}</span>
                    </div>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Clock className="w-4 h-4" />
                      <span>{formatDate(session.lastActivity)}</span>
                    </div>
                    <div className="font-mono text-xs">
                      {session.ip}
                    </div>
                  </div>
                </div>
                {!session.isCurrent && (
                  <button
                    onClick={() => handleTerminateSession(session.id)}
                    className="ml-4 p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Login History */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
          <Clock className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('security.loginHistory.title')}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t('security.loginHistory.description')}
            </p>
          </div>
        </div>

        <div className="space-y-3">
          {loginHistory.map((attempt) => (
            <div key={attempt.id} className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse mb-2">
                    {attempt.success ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : (
                      <AlertTriangle className="w-5 h-5 text-red-500" />
                    )}
                    <span className="font-medium text-gray-900 dark:text-white">
                      {attempt.success ? t('security.loginHistory.successful') : t('security.loginHistory.failed')}
                    </span>
                    {attempt.suspicious && (
                      <span className="px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 text-xs rounded-full">
                        {t('security.loginHistory.suspicious')}
                      </span>
                    )}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <MapPin className="w-4 h-4" />
                      <span>{attempt.location}</span>
                    </div>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <Clock className="w-4 h-4" />
                      <span>{formatDate(attempt.timestamp)}</span>
                    </div>
                    <div className="font-mono text-xs">
                      {attempt.ip}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Password Security */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <Key className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t('security.passwordSecurity.title')}
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                آخر تغيير: منذ 30 يوماً
              </p>
            </div>
          </div>
          <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
            {t('security.passwordSecurity.changePassword')}
          </button>
        </div>

        <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {t('security.passwordSecurity.strength')}
            </span>
            <span className="text-sm text-green-600 dark:text-green-400 font-medium">
              {t('security.passwordSecurity.strong')}
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
            <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }} />
          </div>
        </div>
      </div>
    </div>
  );
}
