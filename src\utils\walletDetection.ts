// أدوات كشف المحافظ الرقمية
export interface WalletDetectionResult {
  isInstalled: boolean;
  isAvailable: boolean;
  provider?: any;
  error?: string;
}

// كشف MetaMask
export function detectMetaMask(): WalletDetectionResult {
  if (typeof window === 'undefined') {
    return {
      isInstalled: false,
      isAvailable: false,
      error: 'Window object not available (SSR)'
    };
  }

  try {
    const ethereum = (window as any).ethereum;
    
    if (!ethereum) {
      return {
        isInstalled: false,
        isAvailable: false,
        error: 'No Ethereum provider found'
      };
    }

    // التحقق من MetaMask تحديداً
    if (ethereum.isMetaMask) {
      return {
        isInstalled: true,
        isAvailable: true,
        provider: ethereum
      };
    }

    // التحقق من وجود providers متعددة
    if (ethereum.providers) {
      const metamaskProvider = ethereum.providers.find((p: any) => p.isMetaMask);
      if (metamaskProvider) {
        return {
          isInstalled: true,
          isAvailable: true,
          provider: metamaskProvider
        };
      }
    }

    return {
      isInstalled: false,
      isAvailable: false,
      error: 'MetaMask not detected among available providers'
    };
  } catch (error) {
    return {
      isInstalled: false,
      isAvailable: false,
      error: `Detection error: ${error}`
    };
  }
}

// كشف Trust Wallet
export function detectTrustWallet(): WalletDetectionResult {
  if (typeof window === 'undefined') {
    return {
      isInstalled: false,
      isAvailable: false,
      error: 'Window object not available (SSR)'
    };
  }

  try {
    const ethereum = (window as any).ethereum;
    
    if (!ethereum) {
      return {
        isInstalled: false,
        isAvailable: false,
        error: 'No Ethereum provider found'
      };
    }

    // Trust Wallet يستخدم نفس واجهة MetaMask
    if (ethereum.isTrust || ethereum.isTrustWallet) {
      return {
        isInstalled: true,
        isAvailable: true,
        provider: ethereum
      };
    }

    // إذا كان MetaMask متاح، يمكن استخدامه مع Trust Wallet
    if (ethereum.isMetaMask) {
      return {
        isInstalled: true,
        isAvailable: true,
        provider: ethereum
      };
    }

    return {
      isInstalled: false,
      isAvailable: false,
      error: 'Trust Wallet not detected'
    };
  } catch (error) {
    return {
      isInstalled: false,
      isAvailable: false,
      error: `Detection error: ${error}`
    };
  }
}

// كشف أي محفظة متاحة
export function detectAnyWallet(): WalletDetectionResult {
  if (typeof window === 'undefined') {
    return {
      isInstalled: false,
      isAvailable: false,
      error: 'Window object not available (SSR)'
    };
  }

  try {
    const ethereum = (window as any).ethereum;
    
    if (!ethereum) {
      return {
        isInstalled: false,
        isAvailable: false,
        error: 'No Ethereum provider found'
      };
    }

    return {
      isInstalled: true,
      isAvailable: true,
      provider: ethereum
    };
  } catch (error) {
    return {
      isInstalled: false,
      isAvailable: false,
      error: `Detection error: ${error}`
    };
  }
}

// دالة للحصول على رسالة خطأ مناسبة (تحتاج لدالة الترجمة)
export function getWalletErrorMessage(walletType: string, detection: WalletDetectionResult, t?: (key: string) => string): string {
  // إذا لم تتوفر دالة الترجمة، استخدم النصوص الإنجليزية
  if (!t) {
    if (!detection.isInstalled) {
      switch (walletType) {
        case 'metamask':
          return 'MetaMask is not installed. Please install MetaMask from https://metamask.io';
        case 'trustwallet':
          return 'Trust Wallet is not installed. Please install Trust Wallet or use MetaMask';
        default:
          return 'No digital wallet installed. Please install MetaMask or Trust Wallet';
      }
    }

    if (!detection.isAvailable) {
      return 'Wallet is not available. Please reload the page and try again';
    }

    return detection.error || 'Unknown wallet error';
  }

  // استخدام نظام الترجمة
  if (!detection.isInstalled) {
    switch (walletType) {
      case 'metamask':
        return t('wallet.errors.metamaskNotInstalled');
      case 'trustwallet':
        return t('wallet.errors.trustwalletNotInstalled');
      default:
        return t('wallet.errors.noWalletInstalled');
    }
  }

  if (!detection.isAvailable) {
    return t('wallet.errors.walletNotAvailable');
  }

  return detection.error || t('wallet.errors.unknownError');
}

// دالة للتحقق من دعم المتصفح للمحافظ
export function isBrowserSupported(): boolean {
  if (typeof window === 'undefined') return false;
  
  // التحقق من دعم Web3
  return !!(window as any).ethereum || !!(window as any).web3;
}

// دالة للحصول على معلومات المتصفح
export function getBrowserInfo(): { name: string; version: string; isSupported: boolean } {
  if (typeof window === 'undefined') {
    return { name: 'Unknown', version: 'Unknown', isSupported: false };
  }

  const userAgent = navigator.userAgent;
  let browserName = 'Unknown';
  let browserVersion = 'Unknown';

  if (userAgent.includes('Chrome')) {
    browserName = 'Chrome';
    const match = userAgent.match(/Chrome\/(\d+)/);
    browserVersion = match ? match[1] : 'Unknown';
  } else if (userAgent.includes('Firefox')) {
    browserName = 'Firefox';
    const match = userAgent.match(/Firefox\/(\d+)/);
    browserVersion = match ? match[1] : 'Unknown';
  } else if (userAgent.includes('Safari')) {
    browserName = 'Safari';
    const match = userAgent.match(/Version\/(\d+)/);
    browserVersion = match ? match[1] : 'Unknown';
  } else if (userAgent.includes('Edge')) {
    browserName = 'Edge';
    const match = userAgent.match(/Edge\/(\d+)/);
    browserVersion = match ? match[1] : 'Unknown';
  }

  return {
    name: browserName,
    version: browserVersion,
    isSupported: isBrowserSupported()
  };
}
