# 🌟 IKAROS P2P - منصة التداول اللامركزي

منصة تداول آمنة وموثوقة للعملات المستقرة بين الأفراد مباشرة

## 📋 نظرة عامة

IKAROS P2P هي منصة تداول لامركزية تتيح للمستخدمين تداول العملات المستقرة (خاصة USDT) بشكل آمن ومباشر. تستخدم المنصة العقود الذكية لضمان الأمان والشفافية في جميع المعاملات.

## 🚀 الميزات الحديثة

### ✅ المكتملة
- **نظام التداول P2P**: تداول آمن للعملات المستقرة
- **العقود الذكية**: وساطة تلقائية مع الحماية الكاملة
- **صفحة إنشاء العروض**: نموذج تفاعلي متقدم مع:
  - اختيار العملة المحلية مع تحويل تلقائي للأسعار
  - نظام العروض المجانية مع حدود قابلة للتحكم
  - واجهة متعددة الخطوات سهلة الاستخدام
  - دعم طرق دفع متعددة
- **نظام المراجعات**: تقييمات شفافة للمتداولين
- **المحفظة الرقمية**: إدارة العملات المشفرة
- **نظام الإشعارات**: تحديثات فورية للصفقات
- **لوحة الإدارة**: مراقبة وإدارة شاملة مع إعدادات العروض المجانية
- **تعدد اللغات**: دعم العربية والإنجليزية مع ترجمات شاملة
- **الوضع المظلم**: تجربة مستخدم محسنة

### 🔄 قيد التطوير
- **نظام KYC**: التحقق من الهوية
- **التحليلات المتقدمة**: إحصائيات مفصلة
- **التطبيق المحمول**: نسخة الهاتف المحمول

## 🛠️ التقنيات المستخدمة

### Frontend
- **Next.js 15**: إطار عمل React متقدم
- **TypeScript**: لغة برمجة قوية ومرنة
- **Tailwind CSS**: تصميم سريع ومرن
- **Lucide React**: أيقونات حديثة

### Backend
- **PHP**: خادم API قوي
- **MySQL**: قاعدة بيانات موثوقة
- **Blockchain**: شبكة BSC Testnet

### العقود الذكية
- **Solidity**: لغة العقود الذكية
- **OpenZeppelin**: مكتبات أمان معتمدة

## 📁 هيكل المشروع

```
ikaros-p2p/
├── src/                    # كود المصدر
│   ├── app/               # صفحات Next.js
│   ├── components/        # مكونات React
│   ├── hooks/            # React Hooks مخصصة
│   ├── services/         # خدمات API
│   ├── utils/            # أدوات مساعدة
│   └── constants/        # ثوابت المشروع
├── api/                   # خادم PHP API
├── contracts/            # العقود الذكية
├── database/             # ملفات قاعدة البيانات
├── public/               # ملفات عامة
│   └── locales/          # ملفات الترجمة
└── docs/                 # الوثائق
```

## 🚀 التشغيل السريع

### المتطلبات
- Node.js 18+
- PHP 8.0+
- MySQL 8.0+
- XAMPP أو WAMP

### خطوات التشغيل

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-repo/ikaros-p2p.git
cd ikaros-p2p
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **إعداد قاعدة البيانات**
```bash
# استيراد ملف قاعدة البيانات
mysql -u root -p < database/schema.sql
```

4. **إعداد متغيرات البيئة**
```bash
cp .env.example .env.local
# تحرير الملف وإضافة القيم المطلوبة
```

5. **تشغيل المشروع**
```bash
npm run dev
```

## 🔧 الإعدادات

### إعدادات العروض المجانية
يمكن التحكم في العروض المجانية من لوحة الإدارة:
- عدد العروض المجانية لكل مستخدم
- الحد الأقصى للمبلغ
- مدة صلاحية العرض
- فترة الانتظار بين العروض

### إعدادات العملات
- دعم العملات المحلية المتعددة
- تحويل تلقائي للأسعار
- تحديث أسعار الصرف

## 📚 الوثائق

- [دليل المطور](docs/DEVELOPER_GUIDE.md)
- [تصميم صفحة إنشاء العرض](docs/CREATE_OFFER_PAGE_DESIGN.md)
- [API Documentation](docs/API.md)

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## 📄 الترخيص

هذا المشروع مرخص تحت [MIT License](LICENSE).

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 50 123 4567
- **الموقع**: https://ikaros-p2p.com

---

**تم تطوير المشروع بواسطة فريق IKAROS** 🚀
