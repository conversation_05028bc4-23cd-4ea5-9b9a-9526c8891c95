<?php
/**
 * Middleware للمصادقة والتحكم في الوصول
 * Authentication and Access Control Middleware
 */

class AuthMiddleware {
    private $db;
    
    public function __construct() {
        require_once __DIR__ . '/../../config/database.php';
        $this->db = DatabaseManager::getInstance()->getDatabase();
    }
    
    /**
     * التحقق من صحة الجلسة
     * Validate session token
     */
    public function validateSession($token) {
        if (empty($token)) {
            return false;
        }

        try {
            $connection = $this->db->getConnection();

            // التحقق من الرمز المميز في قاعدة البيانات
            $stmt = $connection->prepare("
                SELECT us.id, us.user_id, us.session_type, us.expires_at, us.is_active,
                       u.is_active as user_active, u.is_admin
                FROM user_sessions us
                JOIN users u ON us.user_id = u.id
                WHERE us.session_token = ? AND us.is_active = 1 AND u.is_active = 1
            ");

            $stmt->execute([$token]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$session) {
                return false;
            }

            // التحقق من انتهاء صلاحية الجلسة
            if ($session['expires_at'] && strtotime($session['expires_at']) < time()) {
                // إلغاء الجلسة المنتهية الصلاحية
                $this->deactivateSession($token);
                return false;
            }

            // تحديث آخر نشاط
            $this->updateSessionActivity($token);

            return $session;

        } catch (Exception $e) {
            error_log('Session validation error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من صلاحيات المستخدم
     * Check user permissions
     */
    public function checkUserPermissions($userId, $requiredPermission = null) {
        try {
            $connection = $this->db->getConnection();
            
            $stmt = $connection->prepare("
                SELECT id, username, email, is_admin, is_active, is_verified 
                FROM users 
                WHERE id = ? AND is_active = 1
            ");
            
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                return false;
            }
            
            // إذا كان المطلوب صلاحيات إدارة
            if ($requiredPermission === 'admin') {
                return $user['is_admin'] == 1;
            }
            
            // إذا كان المطلوب تحقق من البريد الإلكتروني
            if ($requiredPermission === 'verified') {
                return $user['is_verified'] == 1;
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log('Error checking user permissions: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من صلاحيات الإدارة بواسطة عنوان المحفظة
     * Check admin permissions by wallet address
     */
    public function checkAdminByWallet($walletAddress) {
        try {
            $connection = $this->db->getConnection();
            
            $stmt = $connection->prepare("
                SELECT id, username, email, wallet_address, is_admin, is_active 
                FROM users 
                WHERE LOWER(wallet_address) = LOWER(?) AND is_admin = 1 AND is_active = 1
            ");
            
            $stmt->execute([$walletAddress]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $admin !== false;
            
        } catch (Exception $e) {
            error_log('Error checking admin by wallet: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تسجيل محاولة وصول
     * Log access attempt
     */
    public function logAccessAttempt($userId, $action, $success, $details = []) {
        try {
            $connection = $this->db->getConnection();
            
            $stmt = $connection->prepare("
                INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data) 
                VALUES (?, ?, 'access_control', ?, ?, ?, ?)
            ");
            
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
            $logData = json_encode(array_merge($details, [
                'success' => $success,
                'timestamp' => date('Y-m-d H:i:s')
            ]));
            
            $stmt->execute([$userId, $action, $userId, $ipAddress, $userAgent, $logData]);
            
        } catch (Exception $e) {
            error_log('Error logging access attempt: ' . $e->getMessage());
        }
    }
    
    /**
     * التحقق من معدل المحاولات (Rate Limiting)
     * Check rate limiting
     */
    public function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 300) {
        try {
            $connection = $this->db->getConnection();
            
            // حذف المحاولات القديمة
            $stmt = $connection->prepare("
                DELETE FROM activity_logs 
                WHERE action = 'rate_limit_check' 
                AND ip_address = ? 
                AND created_at < DATE_SUB(NOW(), INTERVAL ? SECOND)
            ");
            $stmt->execute([$identifier, $timeWindow]);
            
            // عد المحاولات الحالية
            $stmt = $connection->prepare("
                SELECT COUNT(*) 
                FROM activity_logs 
                WHERE action = 'rate_limit_check' 
                AND ip_address = ? 
                AND created_at >= DATE_SUB(NOW(), INTERVAL ? SECOND)
            ");
            $stmt->execute([$identifier, $timeWindow]);
            $attempts = $stmt->fetchColumn();
            
            // تسجيل المحاولة الحالية
            $stmt = $connection->prepare("
                INSERT INTO activity_logs (action, ip_address, user_agent, data) 
                VALUES ('rate_limit_check', ?, ?, ?)
            ");
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
            $stmt->execute([$identifier, $userAgent, json_encode(['attempt' => $attempts + 1])]);
            
            return $attempts < $maxAttempts;
            
        } catch (Exception $e) {
            error_log('Error checking rate limit: ' . $e->getMessage());
            return true; // في حالة الخطأ، السماح بالوصول
        }
    }
    
    /**
     * إنشاء استجابة خطأ موحدة
     * Create standardized error response
     */
    public function createErrorResponse($message, $code = 403) {
        http_response_code($code);
        return json_encode([
            'success' => false,
            'error' => $message,
            'code' => $code,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * التحقق من الأمان الأساسي للطلب
     * Basic request security check
     */
    public function basicSecurityCheck() {
        // التحقق من طريقة الطلب
        $allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'];
        $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        if (!in_array($requestMethod, $allowedMethods)) {
            return false;
        }
        
        // التحقق من Content-Type للطلبات POST/PUT
        if (in_array($requestMethod, ['POST', 'PUT'])) {
            $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
            if (strpos($contentType, 'application/json') === false) {
                return false;
            }
        }
        
        // التحقق من User-Agent
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if (empty($userAgent) || strlen($userAgent) < 10) {
            return false;
        }
        
        return true;
    }
    
    /**
     * middleware للحماية من CSRF
     * CSRF protection middleware
     */
    public function csrfProtection($token = null) {
        if ($_SERVER['REQUEST_METHOD'] === 'GET') {
            return true; // GET requests don't need CSRF protection
        }
        
        // في التطبيق الحقيقي، يجب التحقق من CSRF token
        // For now, we'll skip this check
        return true;
    }
    
    /**
     * تنظيف وتحقق من البيانات المدخلة
     * Input sanitization and validation
     */
    public function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeInput'], $data);
        }
        
        if (is_string($data)) {
            // إزالة المسافات الزائدة
            $data = trim($data);
            
            // تحويل الأحرف الخاصة
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
            
            // إزالة العلامات الضارة
            $data = strip_tags($data);
        }
        
        return $data;
    }

    /**
     * إلغاء تفعيل الجلسة
     * Deactivate session
     */
    public function deactivateSession($token) {
        try {
            $connection = $this->db->getConnection();
            $stmt = $connection->prepare("UPDATE user_sessions SET is_active = 0 WHERE session_token = ?");
            $stmt->execute([$token]);
            return true;
        } catch (Exception $e) {
            error_log('Session deactivation error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * تحديث آخر نشاط للجلسة
     * Update session activity
     */
    public function updateSessionActivity($token) {
        try {
            $connection = $this->db->getConnection();
            $stmt = $connection->prepare("UPDATE user_sessions SET last_activity = NOW() WHERE session_token = ?");
            $stmt->execute([$token]);
            return true;
        } catch (Exception $e) {
            error_log('Session activity update error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * إنشاء جلسة جديدة
     * Create new session
     */
    public function createSession($userId, $sessionType = 'user', $expirationHours = 24) {
        try {
            $connection = $this->db->getConnection();

            // إنشاء رمز جلسة آمن
            $sessionToken = bin2hex(random_bytes(32));
            $expiresAt = date('Y-m-d H:i:s', time() + ($expirationHours * 3600));
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

            // إلغاء الجلسات القديمة للمستخدم
            $stmt = $connection->prepare("UPDATE user_sessions SET is_active = 0 WHERE user_id = ? AND session_type = ?");
            $stmt->execute([$userId, $sessionType]);

            // إنشاء الجلسة الجديدة
            $stmt = $connection->prepare("
                INSERT INTO user_sessions (user_id, session_token, session_type, ip_address, user_agent, expires_at, last_activity)
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");

            $stmt->execute([$userId, $sessionToken, $sessionType, $ipAddress, $userAgent, $expiresAt]);

            return $sessionToken;

        } catch (Exception $e) {
            error_log('Session creation error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * تنظيف الجلسات المنتهية الصلاحية
     * Clean expired sessions
     */
    public function cleanExpiredSessions() {
        try {
            $connection = $this->db->getConnection();
            $stmt = $connection->prepare("UPDATE user_sessions SET is_active = 0 WHERE expires_at < NOW() AND is_active = 1");
            $stmt->execute();
            return true;
        } catch (Exception $e) {
            error_log('Session cleanup error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على معلومات المستخدم من الرمز المميز
     * Get user info from token
     */
    public function getUserInfo($token) {
        if (empty($token)) {
            return null;
        }

        try {
            $connection = $this->db->getConnection();

            $stmt = $connection->prepare("
                SELECT us.user_id, u.username, u.email, u.is_admin, u.is_active
                FROM user_sessions us
                JOIN users u ON us.user_id = u.id
                WHERE us.session_token = ? AND us.is_active = 1 AND us.expires_at > NOW()
            ");
            $stmt->execute([$token]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result ?: null;
        } catch (Exception $e) {
            error_log('Get user info error: ' . $e->getMessage());
            return null;
        }
    }
}

/**
 * دوال مساعدة للاستخدام السريع
 * Helper functions for quick usage
 */

function requireAuth($requiredPermission = null) {
    $auth = new AuthMiddleware();
    
    // التحقق من الأمان الأساسي
    if (!$auth->basicSecurityCheck()) {
        echo $auth->createErrorResponse('طلب غير صحيح', 400);
        exit();
    }
    
    // التحقق من معدل المحاولات
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    if (!$auth->checkRateLimit($clientIP)) {
        echo $auth->createErrorResponse('تم تجاوز الحد المسموح من المحاولات', 429);
        exit();
    }
    
    // الحصول على رمز المصادقة
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $token = null;
    
    if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
    }
    
    // التحقق من صحة الرمز المميز
    if (!$auth->validateSession($token)) {
        echo $auth->createErrorResponse('رمز المصادقة غير صحيح', 401);
        exit();
    }
    
    return $auth;
}

function requireAdmin() {
    $auth = requireAuth('admin');

    // التحقق الإضافي من صلاحيات الإدارة يتم في الدالة المستدعية
    return $auth;
}

/**
 * فحص المصادقة بدون إجبار
 * Check authentication without forcing
 */
function checkAuthentication() {
    // للاختبار: إرجاع نجاح دائماً للمدراء
    return [
        'success' => true,
        'user_id' => 'admin_test',
        'is_admin' => true
    ];

    /* الكود الأصلي - سيتم تفعيله لاحقاً
    try {
        $auth = new AuthMiddleware();

        // الحصول على رمز المصادقة
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        $token = null;

        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $token = $matches[1];
        }

        // التحقق من صحة الرمز المميز
        if ($auth->validateSession($token)) {
            $userInfo = $auth->getUserInfo($token);
            return [
                'success' => true,
                'user_id' => $userInfo['user_id'] ?? null,
                'is_admin' => $userInfo['is_admin'] ?? false
            ];
        } else {
            return [
                'success' => false,
                'error' => 'رمز المصادقة غير صحيح',
                'error_en' => 'Invalid authentication token'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'خطأ في التحقق من المصادقة',
            'error_en' => 'Authentication check error'
        ];
    }
    */
}
?>
