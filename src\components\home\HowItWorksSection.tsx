'use client';

import { useState } from 'react';
import { 
  Search, 
  CreditCard, 
  CheckCircle, 
  ArrowRight,
  Shield,
  Clock,
  Users,
  MessageCircle,
  Wallet,
  TrendingUp
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { motion, AnimatePresence } from 'framer-motion';

interface Step {
  id: number;
  icon: React.ElementType;
  title: string;
  description: string;
  details: string;
  color: string;
  bgColor: string;
  features: string[];
}

export default function HowItWorksSection() {
  const { t } = useTranslation();
  const [activeStep, setActiveStep] = useState<number>(1);

  const steps: Step[] = [
    {
      id: 1,
      icon: Search,
      title: t('home.howItWorks.steps.step1.title'),
      description: t('home.howItWorks.steps.step1.description'),
      details: t('home.howItWorks.steps.step1.details'),
      color: 'text-blue-600',
      bgColor: 'from-blue-500 to-blue-600',
      features: [t('home.howItWorks.step1.feature1'), t('home.howItWorks.step1.feature2'), t('home.howItWorks.step1.feature3'), t('home.howItWorks.step1.feature4')]
    },
    {
      id: 2,
      icon: CreditCard,
      title: t('home.howItWorks.steps.step2.title'),
      description: t('home.howItWorks.steps.step2.description'),
      details: t('home.howItWorks.steps.step2.details'),
      color: 'text-green-600',
      bgColor: 'from-green-500 to-green-600',
      features: [t('home.howItWorks.step2.feature1'), t('home.howItWorks.step2.feature2'), t('home.howItWorks.step2.feature3'), t('home.howItWorks.step2.feature4')]
    },
    {
      id: 3,
      icon: CheckCircle,
      title: t('home.howItWorks.steps.step3.title'),
      description: t('home.howItWorks.steps.step3.description'),
      details: t('home.howItWorks.steps.step3.details'),
      color: 'text-purple-600',
      bgColor: 'from-purple-500 to-purple-600',
      features: [t('home.howItWorks.step3.feature1'), t('home.howItWorks.step3.feature2'), t('home.howItWorks.step3.feature3'), t('home.howItWorks.step3.feature4')]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const stepVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: { 
      scale: 1, 
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    },
    exit: { 
      scale: 0.8, 
      opacity: 0,
      transition: {
        duration: 0.2
      }
    }
  };

  return (
    <section className="py-20 bg-white dark:bg-gray-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-64 h-64 bg-blue-500/5 rounded-full filter blur-3xl" />
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500/5 rounded-full filter blur-3xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-green-500/5 rounded-full filter blur-3xl" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {/* Section Header */}
          <motion.div className="text-center mb-16" variants={itemVariants}>
            <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-800 dark:text-blue-300 rounded-full px-6 py-3 mb-6">
              <TrendingUp className="w-5 h-5 ml-2" />
              <span className="text-sm font-medium">{t('home.howItWorks.subtitle')}</span>
            </div>
            <h2 className="text-4xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              {t('home.howItWorks.title')}
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
              {t('home.howItWorks.subtitle')}
            </p>
          </motion.div>

          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              {/* Steps Navigation */}
              <motion.div className="space-y-6" variants={itemVariants}>
                {steps.map((step, index) => {
                  const IconComponent = step.icon;
                  const isActive = activeStep === step.id;
                  
                  return (
                    <motion.div
                      key={step.id}
                      className={`relative cursor-pointer transition-all duration-300 ${
                        isActive ? 'scale-105' : 'hover:scale-102'
                      }`}
                      onClick={() => setActiveStep(step.id)}
                      whileHover={{ x: 10 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <div className={`p-6 rounded-2xl border-2 transition-all duration-300 ${
                        isActive 
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg' 
                          : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-gray-300 dark:hover:border-gray-600'
                      }`}>
                        <div className="flex items-start gap-4">
                          {/* Step Number & Icon */}
                          <div className="flex-shrink-0">
                            <div className={`w-16 h-16 rounded-xl bg-gradient-to-br ${step.bgColor} flex items-center justify-center shadow-lg`}>
                              <IconComponent className="w-8 h-8 text-white" />
                            </div>
                            <div className="mt-2 text-center">
                              <span className={`text-sm font-bold ${step.color}`}>
                                {t('common.step')} {step.id}
                              </span>
                            </div>
                          </div>

                          {/* Content */}
                          <div className="flex-1">
                            <h3 className={`text-xl font-bold mb-2 transition-colors ${
                              isActive ? step.color : 'text-gray-900 dark:text-white'
                            }`}>
                              {step.title}
                            </h3>
                            <p className="text-gray-600 dark:text-gray-300 mb-3 leading-relaxed">
                              {step.description}
                            </p>
                            
                            {/* Features */}
                            <div className="flex flex-wrap gap-2">
                              {step.features.map((feature, featureIndex) => (
                                <span
                                  key={featureIndex}
                                  className={`text-xs px-3 py-1 rounded-full transition-all ${
                                    isActive
                                      ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                                      : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                                  }`}
                                >
                                  {feature}
                                </span>
                              ))}
                            </div>
                          </div>

                          {/* Arrow */}
                          <div className={`flex-shrink-0 transition-all duration-300 ${
                            isActive ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-2'
                          }`}>
                            <ArrowRight className={`w-6 h-6 ${step.color}`} />
                          </div>
                        </div>
                      </div>

                      {/* Connection Line */}
                      {index < steps.length - 1 && (
                        <div className="absolute left-8 top-full w-0.5 h-6 bg-gradient-to-b from-gray-300 to-transparent dark:from-gray-600" />
                      )}
                    </motion.div>
                  );
                })}
              </motion.div>

              {/* Interactive Demo */}
              <motion.div className="relative" variants={itemVariants}>
                <div className="bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-3xl p-8 shadow-2xl border border-gray-200 dark:border-gray-600">
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={activeStep}
                      variants={stepVariants}
                      initial="hidden"
                      animate="visible"
                      exit="exit"
                      className="text-center"
                    >
                      {/* Demo Content Based on Active Step */}
                      {activeStep === 1 && (
                        <div>
                          <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                            <Search className="w-10 h-10 text-white" />
                          </div>
                          <h4 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                            {t('home.howItWorks.demo.step1.title')}
                          </h4>
                          <p className="text-gray-600 dark:text-gray-300 mb-6">
                            {t('home.howItWorks.demo.step1.description')}
                          </p>
                          
                          {/* Mock Interface */}
                          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-inner">
                            <div className="flex items-center gap-3 mb-4">
                              <div className="flex-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-3 text-right">
                                <span className="text-gray-500 dark:text-gray-400 text-sm">{t('home.howItWorks.demo.step1.searchPlaceholder')}</span>
                              </div>
                              <button className="bg-blue-500 text-white p-3 rounded-lg">
                                <Search className="w-4 h-4" />
                              </button>
                            </div>
                            <div className="space-y-2">
                              {[1, 2, 3].map((i) => (
                                <div key={i} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 flex justify-between items-center">
                                  <div className="text-right">
                                    <div className="font-medium text-gray-900 dark:text-white">{t('home.howItWorks.demo.step1.trustedSeller')}</div>
                                    <div className="text-sm text-gray-500 dark:text-gray-400">{t('home.howItWorks.demo.step1.rating')}</div>
                                  </div>
                                  <div className="text-left">
                                    <div className="font-bold text-green-600">3.67 ريال</div>
                                    <div className="text-sm text-gray-500 dark:text-gray-400">{t('home.howItWorks.demo.step1.perUSDT')}</div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}

                      {activeStep === 2 && (
                        <div>
                          <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                            <MessageCircle className="w-10 h-10 text-white" />
                          </div>
                          <h4 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                            {t('home.howItWorks.demo.step2.title')}
                          </h4>
                          <p className="text-gray-600 dark:text-gray-300 mb-6">
                            {t('home.howItWorks.demo.step2.description')}
                          </p>
                          
                          {/* Mock Chat Interface */}
                          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-inner">
                            <div className="space-y-3 mb-4 max-h-40 overflow-y-auto">
                              <div className="flex justify-end">
                                <div className="bg-blue-500 text-white rounded-lg p-3 max-w-xs">
                                  <p className="text-sm">{t('home.howItWorks.demo.step2.message1')}</p>
                                </div>
                              </div>
                              <div className="flex justify-start">
                                <div className="bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg p-3 max-w-xs">
                                  <p className="text-sm">{t('home.howItWorks.demo.step2.message2')}</p>
                                </div>
                              </div>
                              <div className="flex justify-start">
                                <div className="bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg p-3 max-w-xs">
                                  <p className="text-sm">{t('home.howItWorks.demo.step2.message3')}</p>
                                </div>
                              </div>
                            </div>
                            <div className="flex gap-2">
                              <input 
                                type="text" 
                                placeholder={t('home.howItWorks.demo.step2.placeholder')}
                                className="flex-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-3 text-sm"
                                disabled
                              />
                              <button className="bg-green-500 text-white p-3 rounded-lg">
                                <ArrowRight className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      )}

                      {activeStep === 3 && (
                        <div>
                          <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                            <Wallet className="w-10 h-10 text-white" />
                          </div>
                          <h4 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                            {t('home.howItWorks.demo.step3.title')}
                          </h4>
                          <p className="text-gray-600 dark:text-gray-300 mb-6">
                            {t('home.howItWorks.demo.step3.description')}
                          </p>
                          
                          {/* Success Animation */}
                          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-inner">
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ 
                                type: "spring", 
                                stiffness: 300,
                                delay: 0.2 
                              }}
                              className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4"
                            >
                              <CheckCircle className="w-8 h-8 text-green-500" />
                            </motion.div>
                            <h5 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                              {t('home.howItWorks.demo.step3.successTitle')}
                            </h5>
                            <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                              {t('home.howItWorks.demo.step3.successMessage')}
                            </p>
                            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                              <div className="flex justify-between items-center text-sm">
                                <span className="text-gray-500 dark:text-gray-400">{t('home.howItWorks.demo.step3.balance')}:</span>
                                <span className="font-bold text-gray-900 dark:text-white">1,000.00</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </motion.div>
                  </AnimatePresence>
                </div>

                {/* Floating Elements */}
                <motion.div
                  className="absolute -top-4 -right-4 w-8 h-8 bg-yellow-400 rounded-full"
                  animate={{
                    y: [-5, 5, -5],
                    rotate: [0, 180, 360]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
                <motion.div
                  className="absolute -bottom-4 -left-4 w-6 h-6 bg-blue-400 rounded-full"
                  animate={{
                    y: [5, -5, 5],
                    scale: [1, 1.2, 1]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              </motion.div>
            </div>
          </div>

          {/* Bottom CTA */}
          <motion.div className="text-center mt-16" variants={itemVariants}>
            <motion.button
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {t('home.howItWorks.startFirstTrade')}
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
