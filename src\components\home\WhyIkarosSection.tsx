'use client';

import { useState } from 'react';
import {
  Shield,
  Zap,
  DollarSign,
  Clock,
  Users,
  CheckCircle,
  X,
  Star,
  Award,
  TrendingUp,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { motion, AnimatePresence } from 'framer-motion';

interface ComparisonFeature {
  icon: React.ElementType;
  title: string;
  ikarosValue: string;
  othersValue: string;
  ikarosColor: string;
  advantage: boolean;
}

export default function WhyIkarosSection() {
  const { t } = useTranslation();
  const [activeFeature, setActiveFeature] = useState<number>(0);
  const [expandedFeature, setExpandedFeature] = useState<number | null>(null);

  const comparisonFeatures: ComparisonFeature[] = [
    {
      icon: Shield,
      title: t('home.whyIkaros.comparison.features.smartContracts.title'),
      ikarosValue: t('home.whyIkaros.comparison.features.smartContracts.ikaros'),
      othersValue: t('home.whyIkaros.comparison.features.smartContracts.others'),
      ikarosColor: 'text-green-500',
      advantage: true
    },
    {
      icon: DollarSign,
      title: t('home.whyIkaros.comparison.features.fees.title'),
      ikarosValue: t('home.whyIkaros.comparison.features.fees.ikaros'),
      othersValue: t('home.whyIkaros.comparison.features.fees.others'),
      ikarosColor: 'text-blue-500',
      advantage: true
    },
    {
      icon: Zap,
      title: t('home.whyIkaros.comparison.features.speed.title'),
      ikarosValue: t('home.whyIkaros.comparison.features.speed.ikaros'),
      othersValue: t('home.whyIkaros.comparison.features.speed.others'),
      ikarosColor: 'text-yellow-500',
      advantage: true
    },
    {
      icon: Clock,
      title: t('home.whyIkaros.comparison.features.security.title'),
      ikarosValue: t('home.whyIkaros.comparison.features.security.ikaros'),
      othersValue: t('home.whyIkaros.comparison.features.security.others'),
      ikarosColor: 'text-purple-500',
      advantage: true
    },
    {
      icon: Users,
      title: t('home.whyIkaros.comparison.features.support.title'),
      ikarosValue: t('home.whyIkaros.comparison.features.support.ikaros'),
      othersValue: t('home.whyIkaros.comparison.features.support.others'),
      ikarosColor: 'text-indigo-500',
      advantage: true
    },
    {
      icon: Award,
      title: t('home.whyIkaros.comparison.features.verification.title'),
      ikarosValue: t('home.whyIkaros.comparison.features.verification.ikaros'),
      othersValue: t('home.whyIkaros.comparison.features.verification.others'),
      ikarosColor: 'text-orange-500',
      advantage: true
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <section className="py-12 lg:py-20 bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5 dark:opacity-10">
        <div className="absolute inset-0 opacity-20" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23000000' fill-opacity='0.4' fill-rule='evenodd'/%3E%3C/svg%3E")`
        }} />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {/* Section Header */}
          <motion.div className="text-center mb-12 lg:mb-16" variants={itemVariants}>
            <div className="inline-flex items-center bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full px-4 lg:px-6 py-2 lg:py-3 mb-4 lg:mb-6">
              <TrendingUp className="w-4 h-4 lg:w-5 lg:h-5 ml-2" />
              <span className="text-xs lg:text-sm font-medium">{t('home.whyIkaros.description')}</span>
            </div>
            <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-6xl font-bold text-gray-900 dark:text-white mb-4 lg:mb-6 px-4">
              {t('home.whyIkaros.title')}
            </h2>
            <p className="text-base lg:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed px-4">
              {t('home.whyIkaros.subtitle')}
            </p>
          </motion.div>

          {/* Comparison Table - Desktop Version */}
          <motion.div
            className="max-w-6xl mx-auto hidden lg:block"
            variants={itemVariants}
          >
            <div className="bg-white dark:bg-gray-800 rounded-3xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-700">
              {/* Table Header */}
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8">
                <div className="grid grid-cols-3 gap-8 items-center">
                  <div className="text-center">
                    <h3 className="text-xl font-bold">{t('home.whyIkaros.comparison.feature')}</h3>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-3">
                      <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                        <Star className="w-6 h-6 text-yellow-400" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold">{t('home.whyIkaros.comparison.ikaros')}</h3>
                        <p className="text-blue-100 text-sm">{t('home.whyIkaros.comparison.ikarosDesc')}</p>
                      </div>
                    </div>
                  </div>
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-gray-300">{t('home.whyIkaros.comparison.others')}</h3>
                    <p className="text-blue-200 text-sm">{t('home.whyIkaros.comparison.othersDesc')}</p>
                  </div>
                </div>
              </div>

              {/* Comparison Rows - Desktop */}
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {comparisonFeatures.map((feature, index) => {
                  const IconComponent = feature.icon;
                  return (
                    <motion.div
                      key={index}
                      className={`grid grid-cols-3 gap-8 p-6 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-300 cursor-pointer ${
                        activeFeature === index ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                      }`}
                      onClick={() => setActiveFeature(index)}
                      whileHover={{ scale: 1.01 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      {/* Feature Name */}
                      <div className="flex items-center gap-4">
                        <div className={`w-12 h-12 rounded-xl bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 flex items-center justify-center ${feature.ikarosColor}`}>
                          <IconComponent className="w-6 h-6" />
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-900 dark:text-white">
                            {feature.title}
                          </h4>
                        </div>
                      </div>

                      {/* IKAROS Value */}
                      <div className="flex items-center justify-center">
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-2 mb-2">
                            <CheckCircle className="w-6 h-6 text-green-500" />
                            <span className="text-green-600 dark:text-green-400 font-bold text-sm">
                              {t('common.superior')}
                            </span>
                          </div>
                          <p className="text-gray-900 dark:text-white font-medium">
                            {feature.ikarosValue}
                          </p>
                        </div>
                      </div>

                      {/* Others Value */}
                      <div className="flex items-center justify-center">
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-2 mb-2">
                            <X className="w-6 h-6 text-red-500" />
                            <span className="text-red-600 dark:text-red-400 font-bold text-sm">
                              {t('common.limited')}
                            </span>
                          </div>
                          <p className="text-gray-600 dark:text-gray-400">
                            {feature.othersValue}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>

              {/* Bottom CTA */}
              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 p-8 text-center">
                <h4 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  {t('home.whyIkaros.tryDifference')}
                </h4>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  {t('home.whyIkaros.joinThousands')}
                </p>
                <motion.button
                  className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {t('home.whyIkaros.startTradingFree')}
                </motion.button>
              </div>
            </div>
          </motion.div>

          {/* Mobile Version - Card Layout */}
          <motion.div
            className="max-w-4xl mx-auto lg:hidden"
            variants={itemVariants}
          >
            {/* Mobile Header */}
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-t-3xl p-6 text-center">
              <div className="flex items-center justify-center gap-3 mb-4">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                  <Star className="w-6 h-6 text-yellow-400" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">{t('home.whyIkaros.comparison.ikaros')}</h3>
                  <p className="text-blue-100 text-sm">{t('home.whyIkaros.comparison.ikarosDesc')}</p>
                </div>
              </div>
              <p className="text-blue-100 text-sm">
                {t('home.whyIkaros.comparison.title')}
              </p>
            </div>

            {/* Mobile Comparison Cards */}
            <div className="bg-white dark:bg-gray-800 rounded-b-3xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {comparisonFeatures.map((feature, index) => {
                  const IconComponent = feature.icon;
                  const isExpanded = expandedFeature === index;

                  return (
                    <motion.div
                      key={index}
                      className="p-4"
                    >
                      {/* Feature Header - Always Visible */}
                      <div
                        className="flex items-center justify-between cursor-pointer"
                        onClick={() => setExpandedFeature(isExpanded ? null : index)}
                      >
                        <div className="flex items-center gap-3">
                          <div className={`w-10 h-10 rounded-xl bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 flex items-center justify-center ${feature.ikarosColor}`}>
                            <IconComponent className="w-5 h-5" />
                          </div>
                          <h4 className="font-bold text-gray-900 dark:text-white text-sm">
                            {feature.title}
                          </h4>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-5 h-5 text-green-500" />
                          {isExpanded ? (
                            <ChevronUp className="w-5 h-5 text-gray-400" />
                          ) : (
                            <ChevronDown className="w-5 h-5 text-gray-400" />
                          )}
                        </div>
                      </div>

                      {/* Expandable Content */}
                      <AnimatePresence>
                        {isExpanded && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3 }}
                            className="overflow-hidden"
                          >
                            <div className="pt-4 space-y-4">
                              {/* IKAROS Advantage */}
                              <div className="bg-green-50 dark:bg-green-900/20 rounded-xl p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <CheckCircle className="w-5 h-5 text-green-500" />
                                  <span className="text-green-600 dark:text-green-400 font-bold text-sm">
                                    {t('home.whyIkaros.comparison.ikaros')}
                                  </span>
                                </div>
                                <p className="text-gray-900 dark:text-white font-medium text-sm">
                                  {feature.ikarosValue}
                                </p>
                              </div>

                              {/* Others Limitation */}
                              <div className="bg-red-50 dark:bg-red-900/20 rounded-xl p-4">
                                <div className="flex items-center gap-2 mb-2">
                                  <X className="w-5 h-5 text-red-500" />
                                  <span className="text-red-600 dark:text-red-400 font-bold text-sm">
                                    {t('home.whyIkaros.comparison.others')}
                                  </span>
                                </div>
                                <p className="text-gray-600 dark:text-gray-400 text-sm">
                                  {feature.othersValue}
                                </p>
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  );
                })}
              </div>

              {/* Mobile CTA */}
              <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 p-6 text-center">
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                  {t('home.whyIkaros.tryDifference')}
                </h4>
                <p className="text-gray-600 dark:text-gray-300 mb-4 text-sm">
                  {t('home.whyIkaros.joinThousands')}
                </p>
                <motion.button
                  className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl font-bold text-sm shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {t('home.whyIkaros.startTradingFree')}
                </motion.button>
              </div>
            </div>
          </motion.div>

          {/* Additional Stats */}
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 mt-12 lg:mt-16"
            variants={itemVariants}
          >
            <div className="text-center bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="w-12 h-12 lg:w-16 lg:h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-6 h-6 lg:w-8 lg:h-8 text-white" />
              </div>
              <h4 className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white mb-2">99.8%</h4>
              <p className="text-gray-600 dark:text-gray-300 text-sm lg:text-base">{t('home.whyIkaros.stats.successRate')}</p>
            </div>

            <div className="text-center bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="w-12 h-12 lg:w-16 lg:h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Zap className="w-6 h-6 lg:w-8 lg:h-8 text-white" />
              </div>
              <h4 className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white mb-2">{t('home.whyIkaros.stats.averageTime')}</h4>
              <p className="text-gray-600 dark:text-gray-300 text-sm lg:text-base">{t('home.whyIkaros.stats.averageTimeLabel')}</p>
            </div>

            <div className="text-center bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 sm:col-span-2 lg:col-span-1">
              <div className="w-12 h-12 lg:w-16 lg:h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Star className="w-6 h-6 lg:w-8 lg:h-8 text-white" />
              </div>
              <h4 className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white mb-2">4.9/5</h4>
              <p className="text-gray-600 dark:text-gray-300 text-sm lg:text-base">{t('home.whyIkaros.stats.userRating')}</p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
