<?php
/**
 * API الصفقات المحسنة للعقد متعدد العملات
 * Enhanced Trades API for Multi-Token Contract
 */

require_once __DIR__ . '/../cors.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/response.php';

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetTrades();
            break;
        case 'POST':
            handleCreateTrade();
            break;
        case 'PUT':
            handleUpdateTrade();
            break;
        case 'DELETE':
            handleDeleteTrade();
            break;
        default:
            sendErrorResponse('طريقة طلب غير مدعومة', 405);
    }
    
} catch (Exception $e) {
    logError("Enhanced Trades API error: " . $e->getMessage());
    sendErrorResponse('خطأ في الخادم: ' . $e->getMessage(), 500);
}

/**
 * جلب الصفقات المحسنة
 */
function handleGetTrades() {
    $db = new Database();
    $connection = $db->getConnection();
    
    try {
        // معاملات البحث والتصفية
        $tradeId = $_GET['trade_id'] ?? null;
        $userId = $_GET['user_id'] ?? null;
        $sellerId = $_GET['seller_id'] ?? null;
        $buyerId = $_GET['buyer_id'] ?? null;
        $networkId = $_GET['network_id'] ?? null;
        $tokenId = $_GET['token_id'] ?? null;
        $status = $_GET['status'] ?? null;
        $contractStatus = $_GET['contract_status'] ?? null;
        
        // معاملات الترقيم
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(100, max(1, intval($_GET['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        
        // معاملات الترتيب
        $sortBy = $_GET['sort_by'] ?? 'created_at';
        $sortOrder = strtoupper($_GET['sort_order'] ?? 'DESC') === 'ASC' ? 'ASC' : 'DESC';
        
        // بناء شروط البحث
        $whereConditions = [];
        $params = [];
        
        if ($tradeId) {
            $whereConditions[] = "t.id = ?";
            $params[] = $tradeId;
        }
        
        if ($userId) {
            $whereConditions[] = "(t.seller_id = ? OR t.buyer_id = ?)";
            $params[] = $userId;
            $params[] = $userId;
        }
        
        if ($sellerId) {
            $whereConditions[] = "t.seller_id = ?";
            $params[] = $sellerId;
        }
        
        if ($buyerId) {
            $whereConditions[] = "t.buyer_id = ?";
            $params[] = $buyerId;
        }
        
        if ($networkId) {
            $whereConditions[] = "t.network_id = ?";
            $params[] = $networkId;
        }
        
        if ($tokenId) {
            $whereConditions[] = "t.token_id = ?";
            $params[] = $tokenId;
        }
        
        if ($status) {
            $whereConditions[] = "t.status = ?";
            $params[] = $status;
        }
        
        if ($contractStatus) {
            $whereConditions[] = "t.contract_status = ?";
            $params[] = $contractStatus;
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // استعلام العد الإجمالي
        $countQuery = "
            SELECT COUNT(*) as total 
            FROM trades t
            $whereClause
        ";
        
        $countStmt = $connection->prepare($countQuery);
        $countStmt->execute($params);
        $totalTrades = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // استعلام البيانات الرئيسي
        $dataQuery = "
            SELECT 
                t.id,
                t.blockchain_trade_id,
                t.offer_id,
                t.offer_blockchain_id,
                t.seller_id,
                t.buyer_id,
                t.network_id,
                t.token_id,
                t.amount,
                t.price,
                t.currency,
                t.total_value,
                t.platform_fee,
                t.net_amount,
                t.status,
                t.contract_status,
                t.payment_method,
                t.payment_details,
                t.selected_payment_methods,
                t.seller_confirmed,
                t.buyer_confirmed,
                t.dispute_reason,
                t.dispute_resolved_by,
                t.dispute_resolution,
                t.dispute_created_at,
                t.dispute_resolved_at,
                
                -- معلومات العقد الذكي المحسن
                t.create_transaction_hash,
                t.join_transaction_hash,
                t.payment_sent_transaction_hash,
                t.payment_confirmed_transaction_hash,
                t.complete_transaction_hash,
                t.cancel_transaction_hash,
                t.dispute_transaction_hash,
                t.resolve_transaction_hash,
                t.core_escrow_address,
                t.reputation_manager_address,
                t.oracle_manager_address,
                t.admin_manager_address,
                
                -- معلومات السمعة والأوراكل
                t.seller_reputation_before,
                t.buyer_reputation_before,
                t.seller_reputation_after,
                t.buyer_reputation_after,
                t.oracle_price_at_creation,
                t.oracle_price_at_completion,
                
                -- timestamps العقد الذكي
                t.contract_created_at,
                t.contract_joined_at,
                t.contract_payment_sent_at,
                t.contract_payment_confirmed_at,
                t.contract_completed_at,
                t.contract_disputed_at,
                t.contract_resolved_at,
                
                -- مزامنة البيانات
                t.last_sync_at,
                t.sync_status,
                t.sync_attempts,
                t.last_sync_error,
                
                -- timestamps عامة
                t.created_at,
                t.updated_at,
                t.expires_at,
                t.completed_at,
                
                -- معلومات الشبكة
                n.network_name,
                n.network_symbol,
                n.chain_id,
                n.is_testnet as network_is_testnet,
                
                -- معلومات العملة
                tk.token_address,
                tk.token_symbol,
                tk.token_name,
                tk.decimals,
                tk.is_stablecoin,
                tk.icon_url as token_icon,
                
                -- معلومات البائع
                seller.username as seller_username,
                seller.full_name as seller_name,
                seller.wallet_address as seller_wallet,
                seller.rating as seller_rating,
                seller.total_trades as seller_total_trades,
                seller.is_verified as seller_verified,
                seller.profile_image as seller_image,
                
                -- معلومات المشتري
                buyer.username as buyer_username,
                buyer.full_name as buyer_name,
                buyer.wallet_address as buyer_wallet,
                buyer.rating as buyer_rating,
                buyer.total_trades as buyer_total_trades,
                buyer.is_verified as buyer_verified,
                buyer.profile_image as buyer_image,
                
                -- معلومات العرض
                o.offer_type,
                o.terms as offer_terms,
                o.auto_reply as offer_auto_reply,
                
                -- السمعة المحسنة للبائع
                seller_rep.reputation_score as seller_reputation_score,
                seller_rep.reputation_level as seller_reputation_level,
                seller_rep.average_rating as seller_average_rating,
                
                -- السمعة المحسنة للمشتري
                buyer_rep.reputation_score as buyer_reputation_score,
                buyer_rep.reputation_level as buyer_reputation_level,
                buyer_rep.average_rating as buyer_average_rating
                
            FROM trades t
            LEFT JOIN supported_networks n ON t.network_id = n.id
            LEFT JOIN supported_tokens tk ON t.token_id = tk.id
            LEFT JOIN users seller ON t.seller_id = seller.id
            LEFT JOIN users buyer ON t.buyer_id = buyer.id
            LEFT JOIN offers o ON t.offer_id = o.id
            LEFT JOIN enhanced_user_reputation seller_rep ON (t.seller_id = seller_rep.user_id AND t.network_id = seller_rep.network_id)
            LEFT JOIN enhanced_user_reputation buyer_rep ON (t.buyer_id = buyer_rep.user_id AND t.network_id = buyer_rep.network_id)
            $whereClause
            ORDER BY t.$sortBy $sortOrder
            LIMIT ? OFFSET ?
        ";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $dataStmt = $connection->prepare($dataQuery);
        $dataStmt->execute($params);
        $trades = $dataStmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تحويل البيانات وتنسيقها
        foreach ($trades as &$trade) {
            // تحويل القيم المنطقية
            $trade['seller_confirmed'] = (bool)$trade['seller_confirmed'];
            $trade['buyer_confirmed'] = (bool)$trade['buyer_confirmed'];
            $trade['is_stablecoin'] = (bool)$trade['is_stablecoin'];
            $trade['network_is_testnet'] = (bool)$trade['network_is_testnet'];
            $trade['seller_verified'] = (bool)$trade['seller_verified'];
            $trade['buyer_verified'] = (bool)$trade['buyer_verified'];
            
            // تحويل القيم الرقمية
            $trade['amount'] = (float)$trade['amount'];
            $trade['price'] = (float)$trade['price'];
            $trade['total_value'] = (float)$trade['total_value'];
            $trade['platform_fee'] = (float)$trade['platform_fee'];
            $trade['net_amount'] = (float)$trade['net_amount'];
            $trade['oracle_price_at_creation'] = $trade['oracle_price_at_creation'] ? (float)$trade['oracle_price_at_creation'] : null;
            $trade['oracle_price_at_completion'] = $trade['oracle_price_at_completion'] ? (float)$trade['oracle_price_at_completion'] : null;
            $trade['seller_rating'] = $trade['seller_rating'] ? (float)$trade['seller_rating'] : 0;
            $trade['buyer_rating'] = $trade['buyer_rating'] ? (float)$trade['buyer_rating'] : 0;
            $trade['seller_reputation_score'] = $trade['seller_reputation_score'] ? (int)$trade['seller_reputation_score'] : 100;
            $trade['buyer_reputation_score'] = $trade['buyer_reputation_score'] ? (int)$trade['buyer_reputation_score'] : 100;
            $trade['seller_average_rating'] = $trade['seller_average_rating'] ? (float)$trade['seller_average_rating'] : 0;
            $trade['buyer_average_rating'] = $trade['buyer_average_rating'] ? (float)$trade['buyer_average_rating'] : 0;
            
            // تحويل JSON
            $trade['payment_details'] = $trade['payment_details'] ? json_decode($trade['payment_details'], true) : null;
            $trade['selected_payment_methods'] = $trade['selected_payment_methods'] ? json_decode($trade['selected_payment_methods'], true) : [];
            
            // إضافة معلومات إضافية
            $trade['can_cancel'] = in_array($trade['status'], ['created', 'joined']);
            $trade['can_dispute'] = in_array($trade['status'], ['payment_sent', 'payment_confirmed']);
            $trade['is_disputed'] = $trade['status'] === 'disputed';
            $trade['is_completed'] = $trade['status'] === 'completed';
            
            // حساب الوقت المتبقي
            if ($trade['expires_at']) {
                $expiresAt = new DateTime($trade['expires_at']);
                $now = new DateTime();
                $trade['time_remaining'] = max(0, $expiresAt->getTimestamp() - $now->getTimestamp());
            } else {
                $trade['time_remaining'] = null;
            }
        }
        
        // حساب معلومات الترقيم
        $totalPages = ceil($totalTrades / $limit);
        $hasNextPage = $page < $totalPages;
        $hasPrevPage = $page > 1;
        
        sendSuccessResponse([
            'trades' => $trades,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_trades' => (int)$totalTrades,
                'per_page' => $limit,
                'has_next_page' => $hasNextPage,
                'has_prev_page' => $hasPrevPage
            ],
            'filters' => [
                'user_id' => $userId,
                'network_id' => $networkId,
                'token_id' => $tokenId,
                'status' => $status,
                'contract_status' => $contractStatus,
                'sort_by' => $sortBy,
                'sort_order' => $sortOrder
            ]
        ]);
        
    } catch (Exception $e) {
        sendErrorResponse('خطأ في جلب الصفقات: ' . $e->getMessage(), 500);
    }
}

/**
 * إنشاء صفقة جديدة محسنة
 */
function handleCreateTrade() {
    $db = new Database();
    $connection = $db->getConnection();
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            sendErrorResponse('بيانات غير صحيحة', 400);
            return;
        }
        
        // التحقق من الحقول المطلوبة
        $requiredFields = ['offer_id', 'buyer_id', 'amount', 'payment_method'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || $input[$field] === '') {
                sendErrorResponse("الحقل $field مطلوب", 400);
                return;
            }
        }
        
        // جلب معلومات العرض
        $offerStmt = $connection->prepare("
            SELECT o.*, t.token_symbol, t.platform_fee_rate, n.network_name
            FROM offers o
            LEFT JOIN supported_tokens t ON o.token_id = t.id
            LEFT JOIN supported_networks n ON o.network_id = n.id
            WHERE o.id = ? AND o.is_active = 1
        ");
        $offerStmt->execute([$input['offer_id']]);
        $offer = $offerStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$offer) {
            sendErrorResponse('العرض غير موجود أو غير نشط', 404);
            return;
        }
        
        // التحقق من أن المشتري ليس البائع
        if ($offer['user_id'] == $input['buyer_id']) {
            sendErrorResponse('لا يمكن للبائع شراء عرضه الخاص', 400);
            return;
        }
        
        // التحقق من المبلغ
        if ($input['amount'] < $offer['min_amount'] || $input['amount'] > $offer['max_amount']) {
            sendErrorResponse('المبلغ خارج النطاق المسموح للعرض', 400);
            return;
        }
        
        // حساب القيم
        $totalValue = $input['amount'] * $offer['price'];
        $platformFee = $input['amount'] * $offer['platform_fee_rate'];
        $netAmount = $input['amount'] - $platformFee;
        
        // إنشاء الصفقة
        $stmt = $connection->prepare("
            INSERT INTO trades (
                offer_id, offer_blockchain_id, seller_id, buyer_id, network_id, token_id,
                amount, price, currency, total_value, platform_fee, net_amount,
                status, contract_status, payment_method, selected_payment_methods,
                created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ");
        
        $selectedPaymentMethods = json_encode([$input['payment_method']]);
        
        $stmt->execute([
            $input['offer_id'],
            $offer['blockchain_trade_id'],
            $offer['user_id'], // seller_id
            $input['buyer_id'],
            $offer['network_id'],
            $offer['token_id'],
            $input['amount'],
            $offer['price'],
            $offer['currency'],
            $totalValue,
            $platformFee,
            $netAmount,
            'created',
            'Created',
            $input['payment_method'],
            $selectedPaymentMethods
        ]);
        
        $tradeId = $connection->lastInsertId();
        
        sendSuccessResponse([
            'trade_id' => (int)$tradeId,
            'message' => 'تم إنشاء الصفقة بنجاح',
            'total_value' => $totalValue,
            'platform_fee' => $platformFee,
            'net_amount' => $netAmount,
            'next_step' => 'يمكنك الآن ربط الصفقة بالعقد الذكي'
        ]);
        
    } catch (Exception $e) {
        sendErrorResponse('خطأ في إنشاء الصفقة: ' . $e->getMessage(), 500);
    }
}

/**
 * تحديث صفقة موجودة
 */
function handleUpdateTrade() {
    // سيتم تنفيذها لاحقاً
    sendErrorResponse('غير مدعوم حالياً', 501);
}

/**
 * حذف صفقة
 */
function handleDeleteTrade() {
    // سيتم تنفيذها لاحقاً
    sendErrorResponse('غير مدعوم حالياً', 501);
}

?>
