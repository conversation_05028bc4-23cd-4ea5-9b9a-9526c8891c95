/**
 * خدمة BSCScan API للتفاعل مع شبكة BSC
 * BSCScan API Service for BSC Network Integration
 */

interface BSCScanResponse<T = any> {
  status: string;
  message: string;
  result: T;
}

interface TokenBalance {
  account: string;
  balance: string;
  contractAddress: string;
  decimals: string;
  name: string;
  symbol: string;
}

interface Transaction {
  blockNumber: string;
  timeStamp: string;
  hash: string;
  from: string;
  to: string;
  value: string;
  gas: string;
  gasPrice: string;
  gasUsed: string;
  isError: string;
  txreceipt_status: string;
}

interface ContractInfo {
  SourceCode: string;
  ABI: string;
  ContractName: string;
  CompilerVersion: string;
  OptimizationUsed: string;
  Runs: string;
  ConstructorArguments: string;
  EVMVersion: string;
  Library: string;
  LicenseType: string;
  Proxy: string;
  Implementation: string;
  SwarmSource: string;
}

class BSCScanService {
  private readonly baseURL: string;
  private readonly apiKey: string;
  private readonly isTestnet: boolean;

  constructor(isTestnet: boolean = true) {
    this.isTestnet = isTestnet;
    this.baseURL = isTestnet 
      ? 'https://api-testnet.bscscan.com/api'
      : 'https://api.bscscan.com/api';
    
    // يمكن إضافة API Key من متغيرات البيئة
    this.apiKey = process.env.NEXT_PUBLIC_BSCSCAN_API_KEY || 'YourApiKeyToken';
  }

  /**
   * طلب عام لـ BSCScan API
   */
  private async makeRequest<T>(params: Record<string, string>): Promise<T> {
    const url = new URL(this.baseURL);
    
    // إضافة المعاملات
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.append(key, value);
    });
    
    // إضافة API Key
    url.searchParams.append('apikey', this.apiKey);

    try {
      const response = await fetch(url.toString());
      const data: BSCScanResponse<T> = await response.json();
      
      if (data.status === '1') {
        return data.result;
      } else {
        throw new Error(`BSCScan API Error: ${data.message}`);
      }
    } catch (error) {
      console.error('BSCScan API request failed:', error);
      throw error;
    }
  }

  /**
   * الحصول على رصيد BNB لعنوان محدد
   */
  async getBNBBalance(address: string): Promise<string> {
    const result = await this.makeRequest<string>({
      module: 'account',
      action: 'balance',
      address: address,
      tag: 'latest'
    });
    
    // تحويل من Wei إلى BNB
    return (parseFloat(result) / Math.pow(10, 18)).toString();
  }

  /**
   * الحصول على رصيد عملة معينة (ERC-20/BEP-20)
   */
  async getTokenBalance(contractAddress: string, address: string): Promise<TokenBalance> {
    const result = await this.makeRequest<string>({
      module: 'account',
      action: 'tokenbalance',
      contractaddress: contractAddress,
      address: address,
      tag: 'latest'
    });

    // الحصول على معلومات العملة
    const tokenInfo = await this.getTokenInfo(contractAddress);
    
    return {
      account: address,
      balance: result,
      contractAddress: contractAddress,
      decimals: tokenInfo.decimals || '18',
      name: tokenInfo.name || 'Unknown',
      symbol: tokenInfo.symbol || 'UNKNOWN'
    };
  }

  /**
   * الحصول على معلومات عملة من عنوان العقد
   */
  async getTokenInfo(contractAddress: string): Promise<{name?: string, symbol?: string, decimals?: string}> {
    try {
      // محاولة الحصول على معلومات العملة من العقد
      const nameResult = await this.makeRequest<string>({
        module: 'proxy',
        action: 'eth_call',
        to: contractAddress,
        data: '0x06fdde03', // name() function signature
        tag: 'latest'
      });

      const symbolResult = await this.makeRequest<string>({
        module: 'proxy',
        action: 'eth_call',
        to: contractAddress,
        data: '0x95d89b41', // symbol() function signature
        tag: 'latest'
      });

      const decimalsResult = await this.makeRequest<string>({
        module: 'proxy',
        action: 'eth_call',
        to: contractAddress,
        data: '0x313ce567', // decimals() function signature
        tag: 'latest'
      });

      return {
        name: this.hexToString(nameResult),
        symbol: this.hexToString(symbolResult),
        decimals: parseInt(decimalsResult, 16).toString()
      };
    } catch (error) {
      console.warn('Failed to get token info:', error);
      return {};
    }
  }

  /**
   * الحصول على قائمة المعاملات لعنوان محدد
   */
  async getTransactions(address: string, startBlock: number = 0, endBlock: number = ********): Promise<Transaction[]> {
    return await this.makeRequest<Transaction[]>({
      module: 'account',
      action: 'txlist',
      address: address,
      startblock: startBlock.toString(),
      endblock: endBlock.toString(),
      page: '1',
      offset: '100',
      sort: 'desc'
    });
  }

  /**
   * الحصول على معاملات العملات (ERC-20/BEP-20)
   */
  async getTokenTransactions(contractAddress: string, address: string): Promise<Transaction[]> {
    return await this.makeRequest<Transaction[]>({
      module: 'account',
      action: 'tokentx',
      contractaddress: contractAddress,
      address: address,
      page: '1',
      offset: '100',
      sort: 'desc'
    });
  }

  /**
   * الحصول على معلومات العقد الذكي
   */
  async getContractInfo(contractAddress: string): Promise<ContractInfo> {
    const result = await this.makeRequest<ContractInfo[]>({
      module: 'contract',
      action: 'getsourcecode',
      address: contractAddress
    });

    return result[0];
  }

  /**
   * التحقق من حالة المعاملة
   */
  async getTransactionStatus(txHash: string): Promise<{status: string, isError: string}> {
    return await this.makeRequest<{status: string, isError: string}>({
      module: 'transaction',
      action: 'gettxreceiptstatus',
      txhash: txHash
    });
  }

  /**
   * الحصول على سعر الغاز الحالي
   */
  async getGasPrice(): Promise<string> {
    const result = await this.makeRequest<string>({
      module: 'proxy',
      action: 'eth_gasPrice'
    });
    
    // تحويل من Wei إلى Gwei
    return (parseInt(result, 16) / Math.pow(10, 9)).toString();
  }

  /**
   * تحويل Hex إلى نص
   */
  private hexToString(hex: string): string {
    try {
      // إزالة 0x إذا كانت موجودة
      hex = hex.replace(/^0x/, '');
      
      // تحويل إلى نص
      let str = '';
      for (let i = 0; i < hex.length; i += 2) {
        const charCode = parseInt(hex.substr(i, 2), 16);
        if (charCode !== 0) {
          str += String.fromCharCode(charCode);
        }
      }
      
      return str.trim();
    } catch (error) {
      return '';
    }
  }

  /**
   * فحص صحة عنوان المحفظة
   */
  isValidAddress(address: string): boolean {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  }

  /**
   * فحص صحة hash المعاملة
   */
  isValidTxHash(txHash: string): boolean {
    return /^0x[a-fA-F0-9]{64}$/.test(txHash);
  }
}

// إنشاء مثيل للشبكة التجريبية والرئيسية
export const bscscanTestnet = new BSCScanService(true);
export const bscscanMainnet = new BSCScanService(false);

// تصدير الكلاس للاستخدام المخصص
export { BSCScanService };

// تصدير الأنواع
export type { TokenBalance, Transaction, ContractInfo };
