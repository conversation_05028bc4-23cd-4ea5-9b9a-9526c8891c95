<?php
/**
 * صفحة API الرئيسية
 * Main API Page
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: http://localhost:3000');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

echo json_encode([
    'success' => true,
    'message' => 'مرحباً بك في API منصة إيكاروس P2P',
    'version' => '1.0.0',
    'timestamp' => date('Y-m-d H:i:s'),
    'endpoints' => [
        'GET /api/users/stats.php' => 'إحصائيات المستخدم',
        'GET /api/trades/index.php' => 'إدارة الصفقات',
        'GET /api/offers/index.php' => 'إدارة العروض',
        'GET /api/wallet/balance.php' => 'رصيد المحفظة',
        'GET /api/users/activity.php' => 'نشاط المستخدم',
        'GET /api/notifications/index.php' => 'الإشعارات',
        'GET /api/reviews/index.php' => 'التقييمات'
    ]
]);
?>
