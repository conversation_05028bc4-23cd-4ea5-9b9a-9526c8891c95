import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { describe, it, expect, jest, beforeEach } from '@jest/globals';

// Mock the translation hook
jest.mock('@/hooks/useAdminTranslation', () => ({
  useAdminTranslation: () => ({
    t: (key: string) => key,
    language: 'en',
    isRTL: false,
    getDirectionClasses: () => ({ textAlign: 'left', direction: 'ltr' }),
    formatNumber: (num: number) => num.toLocaleString(),
    formatCurrency: (amount: number) => `$${amount.toFixed(2)}`,
    formatDate: (date: string) => new Date(date).toLocaleDateString(),
    formatRelativeTime: (date: string) => 'just now'
  })
}));

// Import components after mocking
import AdvancedUserManagement from '@/components/admin/AdvancedUserManagement';
import AdvancedDisputeManagement from '@/components/admin/AdvancedDisputeManagement';
import AdvancedTradeManagement from '@/components/admin/AdvancedTradeManagement';
import NetworkTokenManagement from '@/components/admin/NetworkTokenManagement';
import NotificationSystem from '@/components/admin/NotificationSystem';
import SystemMonitoring from '@/components/admin/SystemMonitoring';
import AdminSettings from '@/components/admin/AdminSettings';

import AdminSecurity from '@/components/admin/AdminSecurity';

// UI Components
import { AdminDataTable, AdminChart, AdminForm, AdminModal, AdminButton, AdminStatsCard } from '@/components/admin/ui';

describe('Admin Components', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('AdvancedUserManagement', () => {
    it('renders without crashing', () => {
      render(<AdvancedUserManagement />);
      expect(screen.getByText('users.title')).toBeInTheDocument();
    });

    it('displays user statistics', () => {
      render(<AdvancedUserManagement />);
      expect(screen.getByText('users.stats.totalUsers')).toBeInTheDocument();
      expect(screen.getByText('users.stats.activeUsers')).toBeInTheDocument();
      expect(screen.getByText('users.stats.pendingKyc')).toBeInTheDocument();
    });

    it('handles tab switching', () => {
      render(<AdvancedUserManagement />);
      const kycTab = screen.getByText('users.tabs.kyc');
      fireEvent.click(kycTab);
      expect(kycTab).toHaveClass('border-blue-500');
    });

    it('opens user details modal', async () => {
      render(<AdvancedUserManagement />);
      const viewButton = screen.getAllByTitle('common.actions.view')[0];
      fireEvent.click(viewButton);
      await waitFor(() => {
        expect(screen.getByText('users.userDetails')).toBeInTheDocument();
      });
    });
  });

  describe('AdvancedDisputeManagement', () => {
    it('renders dispute management interface', () => {
      render(<AdvancedDisputeManagement />);
      expect(screen.getByText('disputes.title')).toBeInTheDocument();
    });

    it('displays dispute statistics', () => {
      render(<AdvancedDisputeManagement />);
      expect(screen.getByText('disputes.stats.totalDisputes')).toBeInTheDocument();
      expect(screen.getByText('disputes.stats.activeDisputes')).toBeInTheDocument();
    });

    it('filters disputes by status', () => {
      render(<AdvancedDisputeManagement />);
      const filterButton = screen.getByText('common.actions.filter');
      fireEvent.click(filterButton);
      expect(screen.getByText('disputes.status.open')).toBeInTheDocument();
    });
  });

  describe('AdvancedTradeManagement', () => {
    it('renders trade management interface', () => {
      render(<AdvancedTradeManagement />);
      expect(screen.getByText('trades.title')).toBeInTheDocument();
    });

    it('displays trade statistics', () => {
      render(<AdvancedTradeManagement />);
      expect(screen.getByText('trades.stats.totalTrades')).toBeInTheDocument();
      expect(screen.getByText('trades.stats.activeTrades')).toBeInTheDocument();
    });

    it('handles trade status updates', async () => {
      render(<AdvancedTradeManagement />);
      const statusButton = screen.getAllByText('trades.actions.updateStatus')[0];
      fireEvent.click(statusButton);
      await waitFor(() => {
        expect(screen.getByText('trades.status.completed')).toBeInTheDocument();
      });
    });
  });

  describe('NetworkTokenManagement', () => {
    it('renders network and token management', () => {
      render(<NetworkTokenManagement />);
      expect(screen.getByText('networks.title')).toBeInTheDocument();
    });

    it('displays network statistics', () => {
      render(<NetworkTokenManagement />);
      expect(screen.getByText('networks.stats.totalNetworks')).toBeInTheDocument();
      expect(screen.getByText('networks.stats.activeTokens')).toBeInTheDocument();
    });

    it('handles network addition', () => {
      render(<NetworkTokenManagement />);
      const addButton = screen.getByText('networks.actions.addNetwork');
      fireEvent.click(addButton);
      expect(screen.getByText('networks.addNetwork')).toBeInTheDocument();
    });
  });

  describe('NotificationSystem', () => {
    it('renders notification system', () => {
      render(<NotificationSystem />);
      expect(screen.getByText('notifications.title')).toBeInTheDocument();
    });

    it('displays notification statistics', () => {
      render(<NotificationSystem />);
      expect(screen.getByText('notifications.stats.totalNotifications')).toBeInTheDocument();
      expect(screen.getByText('notifications.stats.unreadNotifications')).toBeInTheDocument();
    });

    it('handles notification creation', () => {
      render(<NotificationSystem />);
      const createButton = screen.getByText('notifications.actions.create');
      fireEvent.click(createButton);
      expect(screen.getByText('notifications.createNotification')).toBeInTheDocument();
    });
  });

  describe('SystemMonitoring', () => {
    it('renders system monitoring dashboard', () => {
      render(<SystemMonitoring />);
      expect(screen.getByText('monitoring.title')).toBeInTheDocument();
    });

    it('displays system metrics', () => {
      render(<SystemMonitoring />);
      expect(screen.getByText('monitoring.metrics.cpuUsage')).toBeInTheDocument();
      expect(screen.getByText('monitoring.metrics.memoryUsage')).toBeInTheDocument();
    });

    it('handles metric refresh', () => {
      render(<SystemMonitoring />);
      const refreshButton = screen.getByText('common.actions.refresh');
      fireEvent.click(refreshButton);
      expect(refreshButton).toBeInTheDocument();
    });
  });

  describe('AdminSettings', () => {
    it('renders admin settings interface', () => {
      render(<AdminSettings />);
      expect(screen.getByText('settings.title')).toBeInTheDocument();
    });

    it('displays settings categories', () => {
      render(<AdminSettings />);
      expect(screen.getByText('settings.categories.platformSettings')).toBeInTheDocument();
      expect(screen.getByText('settings.categories.securitySettings')).toBeInTheDocument();
    });

    it('handles settings save', () => {
      render(<AdminSettings />);
      const saveButton = screen.getByText('settings.actions.save');
      fireEvent.click(saveButton);
      expect(saveButton).toBeInTheDocument();
    });
  });



  describe('AdminSecurity', () => {
    it('renders security management interface', () => {
      render(<AdminSecurity />);
      expect(screen.getByText('auth.title')).toBeInTheDocument();
    });

    it('displays security tabs', () => {
      render(<AdminSecurity />);
      expect(screen.getByText('common.overview')).toBeInTheDocument();
      expect(screen.getByText('auth.sessions.title')).toBeInTheDocument();
      expect(screen.getByText('auth.roles.title')).toBeInTheDocument();
    });

    it('handles tab switching', () => {
      render(<AdminSecurity />);
      const sessionsTab = screen.getByText('auth.sessions.title');
      fireEvent.click(sessionsTab);
      expect(sessionsTab).toHaveClass('border-blue-500');
    });
  });
});

describe('UI Components', () => {
  describe('AdminDataTable', () => {
    const mockData = [
      { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active' },
      { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive' }
    ];

    const mockColumns = [
      { key: 'name', label: 'Name', sortable: true },
      { key: 'email', label: 'Email', sortable: true },
      { key: 'status', label: 'Status', filterable: true }
    ];

    it('renders data table with data', () => {
      render(<AdminDataTable data={mockData} columns={mockColumns} />);
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    it('handles sorting', () => {
      render(<AdminDataTable data={mockData} columns={mockColumns} />);
      const nameHeader = screen.getByText('Name');
      fireEvent.click(nameHeader);
      expect(nameHeader).toBeInTheDocument();
    });

    it('handles search', () => {
      render(<AdminDataTable data={mockData} columns={mockColumns} searchable />);
      const searchInput = screen.getByPlaceholderText('common.actions.search');
      fireEvent.change(searchInput, { target: { value: 'John' } });
      expect(searchInput).toHaveValue('John');
    });
  });

  describe('AdminChart', () => {
    const mockChartData = {
      labels: ['Jan', 'Feb', 'Mar'],
      datasets: [{
        label: 'Sales',
        data: [100, 200, 150],
        backgroundColor: '#3B82F6'
      }]
    };

    it('renders chart component', () => {
      render(<AdminChart type="bar" data={mockChartData} title="Sales Chart" />);
      expect(screen.getByText('Sales Chart')).toBeInTheDocument();
    });

    it('displays chart data info', () => {
      render(<AdminChart type="line" data={mockChartData} />);
      expect(screen.getByText('1 dataset(s) with 3 data points')).toBeInTheDocument();
    });
  });

  describe('AdminForm', () => {
    const mockFields = [
      { name: 'name', label: 'Name', type: 'text' as const, required: true },
      { name: 'email', label: 'Email', type: 'email' as const, required: true },
      { name: 'active', label: 'Active', type: 'checkbox' as const }
    ];

    const mockOnSubmit = jest.fn();

    it('renders form with fields', () => {
      render(<AdminForm fields={mockFields} onSubmit={mockOnSubmit} />);
      expect(screen.getByLabelText('Name *')).toBeInTheDocument();
      expect(screen.getByLabelText('Email *')).toBeInTheDocument();
    });

    it('handles form submission', async () => {
      render(<AdminForm fields={mockFields} onSubmit={mockOnSubmit} />);
      
      const nameInput = screen.getByLabelText('Name *');
      const emailInput = screen.getByLabelText('Email *');
      const submitButton = screen.getByText('common.actions.save');

      fireEvent.change(nameInput, { target: { value: 'John Doe' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          name: 'John Doe',
          email: '<EMAIL>',
          active: ''
        });
      });
    });
  });

  describe('AdminButton', () => {
    it('renders button with text', () => {
      render(<AdminButton>Click me</AdminButton>);
      expect(screen.getByText('Click me')).toBeInTheDocument();
    });

    it('handles click events', () => {
      const mockClick = jest.fn();
      render(<AdminButton onClick={mockClick}>Click me</AdminButton>);
      
      const button = screen.getByText('Click me');
      fireEvent.click(button);
      expect(mockClick).toHaveBeenCalled();
    });

    it('shows loading state', () => {
      render(<AdminButton loading>Loading</AdminButton>);
      expect(screen.getByText('Loading')).toBeInTheDocument();
    });

    it('is disabled when specified', () => {
      render(<AdminButton disabled>Disabled</AdminButton>);
      const button = screen.getByText('Disabled');
      expect(button).toBeDisabled();
    });
  });

  describe('AdminStatsCard', () => {
    it('renders stats card with data', () => {
      render(
        <AdminStatsCard
          title="Total Users"
          value={1234}
          trend={{ value: 12, label: 'vs last month' }}
        />
      );
      expect(screen.getByText('Total Users')).toBeInTheDocument();
      expect(screen.getByText('1,234')).toBeInTheDocument();
    });

    it('displays trend information', () => {
      render(
        <AdminStatsCard
          title="Revenue"
          value="$50,000"
          trend={{ value: -5, label: 'vs last month' }}
        />
      );
      expect(screen.getByText('5%')).toBeInTheDocument();
      expect(screen.getByText('vs last month')).toBeInTheDocument();
    });
  });
});

describe('Integration Tests', () => {
  it('components work together in admin dashboard', () => {
    // This would test the full admin dashboard integration
    // For now, we'll just ensure components can be rendered together
    const TestWrapper = () => (
      <div>
        <AdminStatsCard title="Test" value={100} />
        <AdminButton>Test Button</AdminButton>
      </div>
    );

    render(<TestWrapper />);
    expect(screen.getByText('Test')).toBeInTheDocument();
    expect(screen.getByText('Test Button')).toBeInTheDocument();
  });
});

describe('Accessibility Tests', () => {
  it('components have proper ARIA labels', () => {
    render(<AdminButton>Accessible Button</AdminButton>);
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });

  it('forms have proper labels', () => {
    const fields = [
      { name: 'test', label: 'Test Field', type: 'text' as const, required: true }
    ];
    
    render(<AdminForm fields={fields} onSubmit={() => {}} />);
    expect(screen.getByLabelText('Test Field *')).toBeInTheDocument();
  });
});

describe('Performance Tests', () => {
  it('large data tables render efficiently', () => {
    const largeData = Array.from({ length: 1000 }, (_, i) => ({
      id: i,
      name: `User ${i}`,
      email: `user${i}@example.com`
    }));

    const columns = [
      { key: 'name', label: 'Name' },
      { key: 'email', label: 'Email' }
    ];

    const startTime = performance.now();
    render(<AdminDataTable data={largeData} columns={columns} pageSize={50} />);
    const endTime = performance.now();

    // Should render within reasonable time (less than 100ms)
    expect(endTime - startTime).toBeLessThan(100);
  });
});

describe('Error Handling', () => {
  it('handles missing translation keys gracefully', () => {
    // Mock a missing translation
    jest.mocked(require('@/hooks/useAdminTranslation').useAdminTranslation).mockReturnValue({
      t: (key: string) => `Missing: ${key}`,
      language: 'en',
      isRTL: false,
      getDirectionClasses: () => ({ textAlign: 'left', direction: 'ltr' }),
      formatNumber: (num: number) => num.toLocaleString(),
      formatCurrency: (amount: number) => `$${amount.toFixed(2)}`,
      formatDate: (date: string) => new Date(date).toLocaleDateString(),
      formatRelativeTime: (date: string) => 'just now'
    });

    render(<AdminButton>Test</AdminButton>);
    expect(screen.getByText('Test')).toBeInTheDocument();
  });

  it('handles empty data gracefully', () => {
    render(<AdminDataTable data={[]} columns={[]} />);
    expect(screen.getByText('common.noData')).toBeInTheDocument();
  });
});
