[{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "uint256", "name": "networkId", "type": "uint256"}], "name": "addNetwork", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "score", "type": "uint256"}], "name": "getReputationLevel", "outputs": [{"internalType": "enum ReputationManager.ReputationLevel", "name": "", "type": "uint8"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "networkId", "type": "uint256"}], "name": "getNetworkStats", "outputs": [{"internalType": "uint256", "name": "totalUsers", "type": "uint256"}, {"internalType": "uint256", "name": "averageScore", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getSupportedNetworks", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "networkId", "type": "uint256"}], "name": "getUserReputation", "outputs": [{"components": [{"internalType": "uint256", "name": "score", "type": "uint256"}, {"internalType": "enum ReputationManager.ReputationLevel", "name": "level", "type": "uint8"}, {"internalType": "uint256", "name": "totalTrades", "type": "uint256"}, {"internalType": "uint256", "name": "successfulTrades", "type": "uint256"}, {"internalType": "uint256", "name": "disputedTrades", "type": "uint256"}, {"internalType": "uint256", "name": "resolvedDisputes", "type": "uint256"}, {"internalType": "uint256", "name": "averageRating", "type": "uint256"}, {"internalType": "uint256", "name": "lastUpdated", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}], "internalType": "struct ReputationManager.ReputationData", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "networkId", "type": "uint256"}], "name": "initializeUserReputation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "rated", "type": "address"}, {"internalType": "uint256", "name": "tradeId", "type": "uint256"}, {"internalType": "uint256", "name": "networkId", "type": "uint256"}, {"internalType": "uint256", "name": "rating", "type": "uint256"}, {"internalType": "string", "name": "comment", "type": "string"}], "name": "rateUser", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "networkId", "type": "uint256"}], "name": "recordDisputedTrade", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "networkId", "type": "uint256"}, {"internalType": "bool", "name": "inFavor", "type": "bool"}], "name": "recordResolvedDispute", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "networkId", "type": "uint256"}], "name": "recordSuccessfulTrade", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "networkId", "type": "uint256"}], "name": "removeNetwork", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "networkId", "type": "uint256"}, {"internalType": "string", "name": "reason", "type": "string"}], "name": "resetUserReputation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "networkId", "type": "uint256"}, {"internalType": "int256", "name": "change", "type": "int256"}, {"internalType": "string", "name": "reason", "type": "string"}], "name": "updateReputation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "networkId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}], "name": "NetworkAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "networkId", "type": "uint256"}], "name": "NetworkRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "networkId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}], "name": "ReputationReset", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "networkId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "oldScore", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newScore", "type": "uint256"}, {"indexed": false, "internalType": "enum ReputationManager.ReputationLevel", "name": "newLevel", "type": "uint8"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}], "name": "ReputationUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "rater", "type": "address"}, {"indexed": true, "internalType": "address", "name": "rated", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "networkId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "rating", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "comment", "type": "string"}], "name": "UserRated", "type": "event"}]