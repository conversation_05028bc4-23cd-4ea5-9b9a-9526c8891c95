'use client';

import { useState, useEffect } from 'react';
import {
  Star,
  ThumbsUp,
  ThumbsDown,
  Filter,
  Search,
  Calendar,
  User,
  TrendingUp,
  Award,
  Shield,
  Clock
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { databaseService } from '@/services/databaseService';
import { apiGet, handleApiError } from '@/utils/apiClient';
import { notificationService } from '@/services/notificationService';
interface Review {
  id: string;
  reviewer: {
    id: string;
    username: string;
    isVerified: boolean;
    totalTrades: number;
  };
  rating: number;
  comment: string;
  tradeId: string;
  tradeType: 'buy' | 'sell';
  amount: number;
  currency: string;
  timestamp: string;
  isPositive: boolean;
  tags: string[];
}

interface User {
  id: string;
  username: string;
  isVerified: boolean;
  totalTrades: number;
  rating: number;
}

export default function ReviewsPage() {
  const { t } = useTranslation();
  const { isDark } = useTheme();
  const { user } = useAuth();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [filteredReviews, setFilteredReviews] = useState<Review[]>([]);
  const [userData, setUserData] = useState<User | null>(null);
  const [filterRating, setFilterRating] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [error, setError] = useState<string | null>(null);

  // Load reviews and user data
  useEffect(() => {
    const loadReviewsData = async () => {
      if (!user?.id) return;

      try {
        setIsLoading(true);
        setError(null);

        // جلب بيانات المستخدم
        const userResult = await apiGet(`users/profile.php?user_id=${user.id}`);

        if (userResult.success) {
          // تحويل بيانات المستخدم إلى تنسيق المكون
          const formattedUser = {
            id: userResult.data.id,
            name: userResult.data.full_name || userResult.data.username,
            rating: parseFloat(userResult.data.rating) || 0,
            trades: parseInt(userResult.data.total_trades) || 0,
            completionRate: userResult.data.completed_trades > 0
              ? Math.round((userResult.data.completed_trades / userResult.data.total_trades) * 100)
              : 0,
            avgResponseTime: '5 دقائق', // سيتم حسابه لاحقاً
            isVerified: userResult.data.is_verified,
            joinedAt: userResult.data.created_at
          };
          setUserData(formattedUser);
        } else {
          throw new Error(userResult.error || 'فشل في تحميل بيانات المستخدم');
        }

        // جلب التقييمات
        const params = new URLSearchParams({
          user_id: user.id.toString(),
          type: filterType !== 'all' ? filterType : 'received',
          page: currentPage.toString(),
          limit: '20'
        });

        const reviewsResult = await apiGet(`reviews/index.php?${params}`);

        if (reviewsResult.success) {
          // تحويل بيانات التقييمات إلى تنسيق المكون
          const formattedReviews = reviewsResult.data.map((review: any) => ({
            id: review.id,
            reviewer: review.reviewer.full_name || review.reviewer.username,
            rating: parseInt(review.rating),
            comment: review.comment,
            tradeDate: review.created_at,
            tradeAmount: review.trade?.amount || 0,
            type: review.trade?.currency === 'buy' ? 'buy' : 'sell'
          }));

          setReviews(formattedReviews);
          setFilteredReviews(formattedReviews);
          setTotalPages(reviewsResult.pagination?.total_pages || 1);
        } else {
          console.warn('فشل في تحميل التقييمات:', reviewsResult.error);
          setReviews([]);
          setFilteredReviews([]);
        }

      } catch (error) {
        console.error('Error loading reviews data:', error);
        const errorMessage = handleApiError(error);
        setError(errorMessage);
        notificationService.error(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    loadReviewsData();
  }, [user?.id, filterType, currentPage]);

  // تطبيق الفلاتر
  const applyFilters = () => {
    let filtered = reviews;

    // فلتر التقييم
    if (filterRating !== 'all') {
      const rating = parseInt(filterRating);
      filtered = filtered.filter(review => review.rating === rating);
    }

    // فلتر نوع الصفقة
    if (filterType !== 'all') {
      filtered = filtered.filter(review => review.type === filterType);
    }

    // فلتر البحث
    if (searchTerm) {
      filtered = filtered.filter(review => 
        review.reviewer.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.comment.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredReviews(filtered);
  };

  // تطبيق الفلاتر عند تغيير القيم
  useEffect(() => {
    applyFilters();
  }, [filterRating, filterType, searchTerm, reviews]);

  const getRatingDistribution = () => {
    const distribution = [0, 0, 0, 0, 0];
    reviews.forEach(review => {
      distribution[review.rating - 1]++;
    });
    return distribution.reverse(); // ترتيب من 5 إلى 1
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="container-custom">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-300 dark:bg-gray-700 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!userData) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="container-custom">
          <div className="text-center">
            <p className="text-gray-600 dark:text-gray-300">{t('reviews.errorLoading')}</p>
          </div>
        </div>
      </div>
    );
  }

  const renderStars = (rating: number, size: string = 'w-4 h-4') => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${size} ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="container-custom max-w-6xl">
        {/* معلومات المستخدم */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between">
            <div className="flex items-center mb-4 lg:mb-0">
              <div className="w-20 h-20 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center ml-4">
                <User className="w-10 h-10 text-primary-600 dark:text-primary-400" />
              </div>
              <div>
                <div className="flex items-center mb-2">
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white ml-2">{userData.name}</h1>
                  {userData.isVerified && (
                    <Shield className="w-6 h-6 text-success-500" />
                  )}
                </div>
                <div className="flex items-center mb-2">
                  {renderStars(Math.round(userData.rating), 'w-5 h-5')}
                  <span className="text-lg font-semibold text-gray-900 dark:text-white mr-2">
                    {userData.rating}
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">({userData.trades} {t('reviews.userProfile.totalTrades')})</span>
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {t('reviews.userProfile.memberSince')} {formatDate(userData.joinedAt)}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <div className="text-2xl font-bold text-success-600">{userData.completionRate}%</div>
                <div className="text-xs text-gray-600 dark:text-gray-400">{t('reviews.userProfile.completionRate')}</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <div className="text-2xl font-bold text-primary-600">{userData.avgResponseTime}</div>
                <div className="text-xs text-gray-600 dark:text-gray-400">{t('reviews.userProfile.avgResponseTime')}</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <div className="text-2xl font-bold text-warning-600">{reviews.filter(r => r.rating >= 4).length}</div>
                <div className="text-xs text-gray-600 dark:text-gray-400">{t('reviews.userProfile.positiveReviews')}</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <div className="text-2xl font-bold text-gray-600 dark:text-gray-300">{userData.trades}</div>
                <div className="text-xs text-gray-600 dark:text-gray-400">{t('reviews.userProfile.totalTrades')}</div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* الشريط الجانبي */}
          <div className="space-y-6">
            {/* توزيع التقييمات */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-4">{t('reviews.distribution.title')}</h3>

              <div className="space-y-3">
                {getRatingDistribution().map((count, index) => {
                  const rating = 5 - index;
                  const percentage = reviews.length > 0 ? (count / reviews.length) * 100 : 0;

                  return (
                    <div key={rating} className="flex items-center">
                      <div className="flex items-center w-12">
                        <span className="text-sm text-gray-600 dark:text-gray-400 ml-1">{rating}</span>
                        <Star className="w-3 h-3 text-yellow-400 fill-current" />
                      </div>
                      <div className="flex-1 mx-3">
                        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                          <div
                            className="bg-yellow-400 h-2 rounded-full"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                      <span className="text-sm text-gray-600 dark:text-gray-400 w-8">{count}</span>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* الفلاتر */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <Filter className="w-4 h-4 ml-2" />
                {t('reviews.filters.title')}
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="form-label">{t('reviews.filters.rating')}</label>
                  <select
                    value={filterRating}
                    onChange={(e) => setFilterRating(e.target.value)}
                    className="form-select"
                  >
                    <option value="all">{t('reviews.filters.allRatings')}</option>
                    <option value="5">5 {t('reviews.filters.stars')}</option>
                    <option value="4">4 {t('reviews.filters.stars')}</option>
                    <option value="3">3 {t('reviews.filters.stars')}</option>
                    <option value="2">2 {t('reviews.filters.stars')}</option>
                    <option value="1">1 {t('reviews.filters.star')}</option>
                  </select>
                </div>

                <div>
                  <label className="form-label">{t('reviews.filters.tradeType')}</label>
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    className="form-select"
                  >
                    <option value="all">{t('reviews.filters.allTrades')}</option>
                    <option value="buy">{t('reviews.filters.buy')}</option>
                    <option value="sell">{t('reviews.filters.sell')}</option>
                  </select>
                </div>

                <button
                  onClick={applyFilters}
                  className="btn btn-primary w-full"
                >
                  {t('reviews.filters.apply')}
                </button>
              </div>
            </div>

            {/* الإنجازات */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <Award className="w-4 h-4 ml-2" />
                {t('reviews.achievements.title')}
              </h3>

              <div className="space-y-3">
                <div className="flex items-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-800 rounded-full flex items-center justify-center ml-3">
                    <Star className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">{t('reviews.achievements.trustedTrader')}</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">{t('reviews.achievements.trustedTraderDesc')}</div>
                  </div>
                </div>

                <div className="flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center ml-3">
                    <Clock className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">{t('reviews.achievements.quickResponse')}</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">{t('reviews.achievements.quickResponseDesc')}</div>
                  </div>
                </div>

                <div className="flex items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="w-8 h-8 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center ml-3">
                    <TrendingUp className="w-4 h-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">{t('reviews.achievements.highCompletion')}</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">{t('reviews.achievements.highCompletionDesc')}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* التقييمات */}
          <div className="lg:col-span-3">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 md:mb-0">
                    {t('reviews.list.title')} ({filteredReviews.length})
                  </h2>

                  <div className="input-with-icon w-64">
                    <Search className="input-icon-right w-4 h-4" />
                    <input
                      type="text"
                      placeholder={t('reviews.list.searchPlaceholder')}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="form-input text-base-ar"
                    />
                  </div>
                </div>
              </div>

              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredReviews.length === 0 ? (
                  <div className="p-8 text-center">
                    <div className="text-gray-500 dark:text-gray-400 text-lg mb-2">{t('reviews.list.noReviews')}</div>
                    <p className="text-gray-400 dark:text-gray-500">{t('reviews.list.noReviewsDesc')}</p>
                  </div>
                ) : (
                  filteredReviews.map((review) => (
                    <div key={review.id} className="p-6">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center ml-3">
                            <User className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">{review.reviewer}</div>
                            <div className="flex items-center">
                              {renderStars(review.rating)}
                              <span className="text-sm text-gray-600 dark:text-gray-400 mr-2">
                                {formatDate(review.tradeDate)}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="text-right">
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {review.type === 'buy' ? t('reviews.filters.buy') : t('reviews.filters.sell')} {review.tradeAmount} USDT
                          </div>
                        </div>
                      </div>

                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                        {review.comment}
                      </p>

                      <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                        <button className="flex items-center hover:text-success-600 transition-colors">
                          <ThumbsUp className="w-4 h-4 ml-1" />
                          {t('reviews.list.helpful')}
                        </button>
                        <button className="flex items-center hover:text-danger-600 transition-colors">
                          <ThumbsDown className="w-4 h-4 ml-1" />
                          {t('reviews.list.notHelpful')}
                        </button>
                      </div>
                    </div>
                  ))
                )}
              </div>

              {filteredReviews.length > 0 && (
                <div className="p-6 border-t border-gray-200 dark:border-gray-700 text-center">
                  <button className="btn btn-secondary">
                    {t('reviews.list.loadMore')}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
