<?php
/**
 * إعدادات وضع التطوير
 * Development Mode Configuration
 */

// تفعيل وضع التطوير
define('DEVELOPMENT_MODE', true);

// إعدادات التطوير
define('DEV_ADMIN_ID', 1);
define('DEV_ADMIN_USERNAME', 'admin');
define('DEV_ADMIN_EMAIL', '<EMAIL>');

// تعطيل وضع الصيانة في التطوير
define('MAINTENANCE_MODE_DISABLED', true);

// إعدادات قاعدة البيانات للتطوير
define('DB_HOST', 'localhost');
define('DB_NAME', 'ikaros_p2p');
define('DB_USER', 'root');
define('DB_PASS', '');

// إعدادات العقد الذكي للتطوير (Binance Smart Chain Testnet)
define('CONTRACT_ADDRESS', '0x...'); // عنوان العقد الذكي
define('RPC_URL', 'https://data-seed-prebsc-1-s1.binance.org:8545/');
define('CHAIN_ID', 97);

// تفعيل عرض الأخطاء في التطوير
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// دالة للتحقق من وضع التطوير
function isDevMode() {
    return defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE === true;
}

// دالة للحصول على معلومات المدير الافتراضي
function getDevAdminInfo() {
    return [
        'id' => DEV_ADMIN_ID,
        'username' => DEV_ADMIN_USERNAME,
        'email' => DEV_ADMIN_EMAIL,
        'admin_role' => 'super_admin',
        'wallet_address' => null,
        'is_development' => true
    ];
}
?>
