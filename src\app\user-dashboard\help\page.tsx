'use client';

import { useState } from 'react';
import { 
  HelpCircle,
  Search,
  ChevronDown,
  ChevronRight,
  MessageSquare,
  Phone,
  Mail,
  ExternalLink,
  Book,
  Video,
  FileText,
  Users,
  Shield,
  Wallet,
  Activity,
  Settings
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
}

interface HelpCategory {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  description: string;
  articles: number;
}

export default function HelpPage() {
  const { t } = useUserDashboardTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);

  // الفئات
  const categories: HelpCategory[] = [
    {
      id: 'getting-started',
      name: 'البدء',
      icon: Book,
      description: 'كيفية البدء في استخدام المنصة',
      articles: 8
    },
    {
      id: 'trading',
      name: 'التداول',
      icon: Activity,
      description: 'دليل التداول والصفقات',
      articles: 12
    },
    {
      id: 'wallet',
      name: 'المحفظة',
      icon: Wallet,
      description: 'إدارة المحفظة والمعاملات',
      articles: 6
    },
    {
      id: 'security',
      name: 'الأمان',
      icon: Shield,
      description: 'حماية حسابك وأمان المعاملات',
      articles: 10
    },
    {
      id: 'account',
      name: 'الحساب',
      icon: Users,
      description: 'إعدادات الحساب والملف الشخصي',
      articles: 7
    },
    {
      id: 'technical',
      name: 'المشاكل التقنية',
      icon: Settings,
      description: 'حل المشاكل التقنية الشائعة',
      articles: 9
    }
  ];

  // الأسئلة الشائعة
  const faqs: FAQItem[] = [
    {
      id: '1',
      question: 'كيف أبدأ أول صفقة تداول؟',
      answer: 'لبدء أول صفقة، اذهب إلى صفحة العروض، اختر العرض المناسب، وانقر على "بدء التداول". تأكد من قراءة شروط البائع قبل المتابعة.',
      category: 'trading',
      tags: ['تداول', 'صفقة', 'بداية']
    },
    {
      id: '2',
      question: 'كيف أربط محفظتي الرقمية؟',
      answer: 'اذهب إلى إعدادات المحفظة، انقر على "ربط محفظة"، واختر نوع المحفظة (MetaMask، Trust Wallet، إلخ). اتبع التعليمات لإكمال الربط.',
      category: 'wallet',
      tags: ['محفظة', 'ربط', 'MetaMask']
    },
    {
      id: '3',
      question: 'ما هي رسوم التداول؟',
      answer: 'رسوم التداول هي 1% من قيمة الصفقة للبائع و 0.5% للمشتري. هذه الرسوم تُخصم تلقائياً عند إتمام الصفقة.',
      category: 'trading',
      tags: ['رسوم', 'تكلفة', 'تداول']
    },
    {
      id: '4',
      question: 'كيف أحمي حسابي من الاختراق؟',
      answer: 'فعّل المصادقة الثنائية، استخدم كلمة مرور قوية، لا تشارك معلومات حسابك مع أحد، وتأكد من تسجيل الخروج من الأجهزة العامة.',
      category: 'security',
      tags: ['أمان', 'حماية', 'مصادقة ثنائية']
    },
    {
      id: '5',
      question: 'كم يستغرق تأكيد المعاملة؟',
      answer: 'معاملات USDT تستغرق عادة 5-15 دقيقة للتأكيد. المعاملات البنكية قد تستغرق 1-3 أيام عمل حسب البنك.',
      category: 'wallet',
      tags: ['معاملة', 'تأكيد', 'وقت']
    },
    {
      id: '6',
      question: 'ماذا أفعل إذا واجهت مشكلة في صفقة؟',
      answer: 'إذا واجهت مشكلة، استخدم نظام الدردشة في الصفقة أولاً. إذا لم تحل المشكلة، يمكنك فتح نزاع وسيتدخل فريق الدعم.',
      category: 'trading',
      tags: ['مشكلة', 'نزاع', 'دعم']
    }
  ];

  // تصفية الأسئلة الشائعة
  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.tags.some(tag => tag.includes(searchTerm));
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-2">
          مركز المساعدة
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          نحن هنا لمساعدتك في كل خطوة
        </p>
      </div>

      {/* البحث */}
      <div className="max-w-2xl mx-auto">
        <div className="relative">
          <Search className="absolute left-3 rtl:right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="ابحث في المساعدة..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 rtl:pr-10 pr-4 rtl:pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* الفئات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`p-6 rounded-xl border-2 transition-all text-left rtl:text-right ${
              selectedCategory === category.id
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            <div className="flex items-center space-x-3 rtl:space-x-reverse mb-3">
              <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                selectedCategory === category.id
                  ? 'bg-blue-100 dark:bg-blue-800'
                  : 'bg-gray-100 dark:bg-gray-700'
              }`}>
                <category.icon className={`w-5 h-5 ${
                  selectedCategory === category.id
                    ? 'text-blue-600 dark:text-blue-400'
                    : 'text-gray-600 dark:text-gray-400'
                }`} />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">
                  {category.name}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {category.articles} مقال
                </p>
              </div>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {category.description}
            </p>
          </button>
        ))}
      </div>

      {/* زر عرض الكل */}
      {selectedCategory !== 'all' && (
        <div className="text-center">
          <button
            onClick={() => setSelectedCategory('all')}
            className="text-blue-600 dark:text-blue-400 hover:underline"
          >
            عرض جميع الفئات
          </button>
        </div>
      )}

      {/* الأسئلة الشائعة */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            الأسئلة الشائعة
          </h2>
        </div>
        
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {filteredFAQs.map((faq) => (
            <div key={faq.id} className="p-6">
              <button
                onClick={() => setExpandedFAQ(expandedFAQ === faq.id ? null : faq.id)}
                className="w-full flex items-center justify-between text-left rtl:text-right"
              >
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {faq.question}
                </h3>
                {expandedFAQ === faq.id ? (
                  <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                ) : (
                  <ChevronRight className="w-5 h-5 text-gray-500 flex-shrink-0" />
                )}
              </button>
              
              {expandedFAQ === faq.id && (
                <div className="mt-4 text-gray-600 dark:text-gray-400">
                  <p>{faq.answer}</p>
                  <div className="flex flex-wrap gap-2 mt-3">
                    {faq.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* لم تجد ما تبحث عنه؟ */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            لم تجد ما تبحث عنه؟
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            تواصل مع فريق الدعم للحصول على مساعدة شخصية
          </p>
          
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <button className="flex items-center justify-center space-x-2 rtl:space-x-reverse p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <MessageSquare className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              <span className="text-gray-900 dark:text-white">دردشة مباشرة</span>
            </button>
            
            <button className="flex items-center justify-center space-x-2 rtl:space-x-reverse p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <Mail className="w-5 h-5 text-green-600 dark:text-green-400" />
              <span className="text-gray-900 dark:text-white">إرسال إيميل</span>
            </button>
            
            <button className="flex items-center justify-center space-x-2 rtl:space-x-reverse p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <Phone className="w-5 h-5 text-orange-600 dark:text-orange-400" />
              <span className="text-gray-900 dark:text-white">اتصال هاتفي</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
