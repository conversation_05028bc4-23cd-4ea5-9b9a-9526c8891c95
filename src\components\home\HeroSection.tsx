'use client';

import { useState, useEffect } from 'react';
import { 
  Shield, 
  TrendingUp, 
  Users, 
  Zap, 
  ArrowRight, 
  Play,
  CheckCircle,
  Star,
  Globe
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { motion } from 'framer-motion';
import TranslatedContent from '@/components/TranslatedContent';

interface PlatformStats {
  totalTrades: number;
  totalUsers: number;
  totalVolume: string;
  averageRating: number;
  activeOffers: number;
  onlineUsers: number;
}

export default function HeroSection() {
  const { t } = useTranslation();
  const [stats, setStats] = useState<PlatformStats>({
    totalTrades: 50000,
    totalUsers: 12500,
    totalVolume: '2.5M',
    averageRating: 4.9,
    activeOffers: 1250,
    onlineUsers: 850
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const floatingAnimation = {
    y: [-10, 10, -10],
    transition: {
      duration: 3,
      repeat: Infinity,
      ease: "easeInOut" as const
    }
  };

  // Simulate real-time stats updates
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(prev => ({
        ...prev,
        onlineUsers: prev.onlineUsers + Math.floor(Math.random() * 10) - 5,
        activeOffers: prev.activeOffers + Math.floor(Math.random() * 6) - 3
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 dark:from-gray-900 dark:via-gray-800 dark:to-black overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        {/* Gradient Orbs */}
        <motion.div 
          className="absolute top-20 left-20 w-72 h-72 bg-blue-500/20 rounded-full filter blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div 
          className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/20 rounded-full filter blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.4, 0.2]
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* Floating Geometric Shapes */}
        <motion.div
          className="absolute top-32 right-32 w-16 h-16 border-2 border-white/20 rotate-45"
          animate={floatingAnimation}
        />
        <motion.div
          className="absolute bottom-40 left-40 w-12 h-12 bg-yellow-400/30 rounded-full"
          animate={{
            y: [5, -5, 5],
            scale: [1, 1.2, 1],
            transition: {
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut" as const
            }
          }}
        />
        <motion.div
          className="absolute top-1/2 left-20 w-8 h-8 bg-green-400/40 transform rotate-45"
          animate={{
            y: [-5, 5, -5],
            rotate: [0, 180, 360],
            transition: {
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut" as const
            }
          }}
        />
      </div>

      {/* Grid Pattern Overlay */}
      <div className="absolute inset-0 opacity-30" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }} />

      <div className="container mx-auto px-4 py-20 relative z-10">
        <motion.div 
          className="grid lg:grid-cols-2 gap-16 items-center min-h-screen"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Left Content */}
          <motion.div className="text-white" variants={itemVariants}>
            {/* Badge */}
            <motion.div
              className="inline-flex items-center bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 mb-8 border border-white/20"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Shield className="w-5 h-5 ml-2 text-green-400" />
              <TranslatedContent fallback={<span className="text-sm font-medium">Secure & Licensed Web3 Platform</span>}>
                <span className="text-sm font-medium">{t('home.hero.badge')}</span>
              </TranslatedContent>
            </motion.div>

            {/* Main Title */}
            <motion.h1
              className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-6 leading-tight"
              variants={itemVariants}
            >
              <TranslatedContent fallback={
                <>
                  <span className="block">Stablecoin Exchange</span>
                  <span className="block bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 bg-clip-text text-transparent">
                    with Complete Safety & Trust
                  </span>
                </>
              }>
                <span className="block">{t('home.hero.title')}</span>
                <span className="block bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 bg-clip-text text-transparent">
                  {t('home.hero.titleHighlight')}
                </span>
              </TranslatedContent>
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              className="text-lg sm:text-xl lg:text-2xl text-blue-100 mb-6 leading-relaxed"
              variants={itemVariants}
            >
              {t('home.hero.subtitle')}
            </motion.p>

            {/* Description */}
            <motion.p
              className="text-base sm:text-lg text-blue-200 mb-10 leading-relaxed max-w-2xl"
              variants={itemVariants}
            >
              {t('home.hero.description')}
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4 mb-12"
              variants={itemVariants}
            >
              <motion.a
                href="/offers"
                className="group relative overflow-hidden bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-bold text-base sm:text-lg shadow-2xl"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="relative z-10 flex items-center justify-center">
                  <ArrowRight className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 group-hover:translate-x-1 transition-transform" />
                  {t('home.hero.ctaPrimary')}
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-red-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </motion.a>

              <motion.button
                className="group flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 border-2 border-white/30 text-white rounded-xl font-bold text-base sm:text-lg backdrop-blur-sm hover:bg-white/10 transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Play className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 group-hover:scale-110 transition-transform" />
                {t('home.hero.ctaSecondary')}
              </motion.button>
            </motion.div>

            {/* Trust Indicators */}
            <motion.div
              className="flex flex-col sm:flex-row items-center gap-4 sm:gap-8 text-sm text-blue-200"
              variants={itemVariants}
            >
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-400" />
                <span className="text-xs sm:text-sm">{t('home.hero.features.smartContracts')}</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400" />
                <span className="text-xs sm:text-sm">{t('home.hero.features.rating')}</span>
              </div>
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4 sm:w-5 sm:h-5 text-blue-400" />
                <span className="text-xs sm:text-sm">{t('home.hero.features.global')}</span>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Content - Stats Dashboard */}
          <motion.div 
            className="relative"
            variants={itemVariants}
          >
            <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl">
              <motion.h3 
                className="text-2xl font-bold text-white mb-8 text-center"
                variants={itemVariants}
              >
                {t('home.hero.stats.title')}
              </motion.h3>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                {/* Total Trades */}
                <motion.div 
                  className="bg-white/5 rounded-2xl p-6 border border-white/10"
                  whileHover={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="flex items-center justify-between mb-3">
                    <TrendingUp className="w-8 h-8 text-green-400" />
                    <span className="text-xs text-green-400 bg-green-400/20 px-2 py-1 rounded-full">
                      +25%
                    </span>
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">
                    {stats.totalTrades.toLocaleString()}+
                  </div>
                  <div className="text-sm text-blue-200">
                    {t('home.hero.stats.totalTrades')}
                  </div>
                </motion.div>

                {/* Total Users */}
                <motion.div 
                  className="bg-white/5 rounded-2xl p-6 border border-white/10"
                  whileHover={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="flex items-center justify-between mb-3">
                    <Users className="w-8 h-8 text-blue-400" />
                    <span className="text-xs text-blue-400 bg-blue-400/20 px-2 py-1 rounded-full">
                      +18%
                    </span>
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">
                    {stats.totalUsers.toLocaleString()}+
                  </div>
                  <div className="text-sm text-blue-200">
                    {t('home.hero.stats.totalUsers')}
                  </div>
                </motion.div>

                {/* Trading Volume */}
                <motion.div 
                  className="bg-white/5 rounded-2xl p-6 border border-white/10"
                  whileHover={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="flex items-center justify-between mb-3">
                    <Zap className="w-8 h-8 text-yellow-400" />
                    <span className="text-xs text-yellow-400 bg-yellow-400/20 px-2 py-1 rounded-full">
                      +32%
                    </span>
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">
                    ${stats.totalVolume}
                  </div>
                  <div className="text-sm text-blue-200">
                    {t('home.hero.stats.totalVolume')}
                  </div>
                </motion.div>

                {/* Online Users */}
                <motion.div 
                  className="bg-white/5 rounded-2xl p-6 border border-white/10"
                  whileHover={{ scale: 1.02 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="relative">
                      <Users className="w-8 h-8 text-green-400" />
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse" />
                    </div>
                    <span className="text-xs text-green-400 bg-green-400/20 px-2 py-1 rounded-full">
                      {t('home.platformStats.metrics.onlineUsers.growth')}
                    </span>
                  </div>
                  <div className="text-2xl font-bold text-white mb-1">
                    {stats.onlineUsers.toLocaleString()}
                  </div>
                  <div className="text-sm text-blue-200">
                    {t('home.hero.stats.onlineUsers')}
                  </div>
                </motion.div>
              </div>

              {/* Live indicator */}
              <motion.div 
                className="flex items-center justify-center mt-6 text-sm text-green-400"
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse" />
                {t('home.hero.liveUpdate')}
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
