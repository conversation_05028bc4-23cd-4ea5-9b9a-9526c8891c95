'use client';

import { useState } from 'react';
import { 
  Shield, 
  DollarSign, 
  Zap, 
  Globe, 
  MessageCircle, 
  Clock,
  CheckCircle,
  Star,
  Award,
  Lock,
  TrendingUp,
  Users
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { motion, AnimatePresence } from 'framer-motion';

interface Feature {
  id: string;
  icon: React.ElementType;
  title: string;
  description: string;
  benefits: string[];
  color: string;
  bgColor: string;
  hoverColor: string;
}

export default function FeaturesSection() {
  const { t } = useTranslation();
  const [activeFeature, setActiveFeature] = useState<string>('smartContracts');
  const [hoveredFeature, setHoveredFeature] = useState<string | null>(null);

  const features: Feature[] = [
    {
      id: 'smartContracts',
      icon: Shield,
      title: t('home.features.list.smartContracts.title'),
      description: t('home.features.list.smartContracts.description'),
      benefits: t('home.features.list.smartContracts.benefits'),
      color: 'text-green-600',
      bgColor: 'from-green-500 to-emerald-600',
      hoverColor: 'hover:border-green-500'
    },
    {
      id: 'lowFees',
      icon: DollarSign,
      title: t('home.features.list.lowFees.title'),
      description: t('home.features.list.lowFees.description'),
      benefits: t('home.features.list.lowFees.benefits'),
      color: 'text-blue-600',
      bgColor: 'from-blue-500 to-blue-600',
      hoverColor: 'hover:border-blue-500'
    },
    {
      id: 'fastTransactions',
      icon: Zap,
      title: t('home.features.list.fastTransactions.title'),
      description: t('home.features.list.fastTransactions.description'),
      benefits: t('home.features.list.fastTransactions.benefits'),
      color: 'text-yellow-600',
      bgColor: 'from-yellow-500 to-orange-600',
      hoverColor: 'hover:border-yellow-500'
    },
    {
      id: 'multiCurrency',
      icon: Globe,
      title: t('home.features.list.multiCurrency.title'),
      description: t('home.features.list.multiCurrency.description'),
      benefits: t('home.features.list.multiCurrency.benefits'),
      color: 'text-purple-600',
      bgColor: 'from-purple-500 to-purple-600',
      hoverColor: 'hover:border-purple-500'
    },
    {
      id: 'secureChat',
      icon: MessageCircle,
      title: t('home.features.list.secureChat.title'),
      description: t('home.features.list.secureChat.description'),
      benefits: t('home.features.list.secureChat.benefits'),
      color: 'text-indigo-600',
      bgColor: 'from-indigo-500 to-indigo-600',
      hoverColor: 'hover:border-indigo-500'
    },
    {
      id: 'support247',
      icon: Clock,
      title: t('home.features.list.support247.title'),
      description: t('home.features.list.support247.description'),
      benefits: t('home.features.list.support247.benefits'),
      color: 'text-red-600',
      bgColor: 'from-red-500 to-red-600',
      hoverColor: 'hover:border-red-500'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const activeFeatureData = features.find(f => f.id === activeFeature) || features[0];

  return (
    <section className="py-20 bg-white dark:bg-gray-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5 dark:opacity-10">
        <div className="absolute inset-0 opacity-20" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.4'%3E%3Cpath d='M0 0h80v80H0V0zm20 20v40h40V20H20zm20 35a15 15 0 1 1 0-30 15 15 0 0 1 0 30z' fill-rule='nonzero'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {/* Section Header */}
          <motion.div className="text-center mb-16" variants={itemVariants}>
            <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-800 dark:text-blue-300 rounded-full px-6 py-3 mb-6">
              <Star className="w-5 h-5 ml-2" />
              <span className="text-sm font-medium">{t('home.features.subtitle')}</span>
            </div>
            <h2 className="text-4xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              {t('home.features.title')}
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
              {t('home.features.subtitle')}
            </p>
          </motion.div>

          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-start">
              {/* Features Grid */}
              <motion.div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6" variants={itemVariants}>
                {features.map((feature, index) => {
                  const IconComponent = feature.icon;
                  const isActive = activeFeature === feature.id;
                  const isHovered = hoveredFeature === feature.id;
                  
                  return (
                    <motion.div
                      key={feature.id}
                      className={`group cursor-pointer transition-all duration-300 ${
                        isActive ? 'scale-105' : 'hover:scale-102'
                      }`}
                      onClick={() => setActiveFeature(feature.id)}
                      onMouseEnter={() => setHoveredFeature(feature.id)}
                      onMouseLeave={() => setHoveredFeature(null)}
                      whileHover={{ y: -5 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <div className={`relative p-4 sm:p-6 rounded-2xl border-2 transition-all duration-300 ${
                        isActive
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-xl'
                          : `border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 ${feature.hoverColor} hover:shadow-lg`
                      }`}>
                        {/* Icon */}
                        <div className={`w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-xl bg-gradient-to-br ${feature.bgColor} flex items-center justify-center mb-3 sm:mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                          <IconComponent className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" />
                        </div>

                        {/* Title */}
                        <h3 className={`text-lg sm:text-xl font-bold mb-2 sm:mb-3 transition-colors leading-tight ${
                          isActive ? feature.color : 'text-gray-900 dark:text-white group-hover:' + feature.color
                        }`}>
                          {feature.title}
                        </h3>

                        {/* Description */}
                        <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed mb-3 sm:mb-4">
                          {feature.description}
                        </p>

                        {/* Benefits Preview */}
                        <div className="space-y-2">
                          {Array.isArray(feature.benefits) && feature.benefits.slice(0, 2).map((benefit, benefitIndex) => (
                            <div key={benefitIndex} className="flex items-center gap-2">
                              <CheckCircle className={`w-4 h-4 ${feature.color}`} />
                              <span className="text-xs text-gray-600 dark:text-gray-400">
                                {benefit}
                              </span>
                            </div>
                          ))}
                        </div>

                        {/* Active Indicator */}
                        {isActive && (
                          <motion.div
                            className="absolute top-4 right-4"
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ type: "spring", stiffness: 300 }}
                          >
                            <div className={`w-3 h-3 rounded-full bg-gradient-to-br ${feature.bgColor}`} />
                          </motion.div>
                        )}

                        {/* Hover Effect */}
                        <div className={`absolute inset-0 bg-gradient-to-br ${feature.bgColor} rounded-2xl opacity-0 group-hover:opacity-5 transition-opacity duration-300`} />
                      </div>
                    </motion.div>
                  );
                })}
              </motion.div>

              {/* Active Feature Details */}
              <motion.div className="lg:sticky lg:top-8" variants={itemVariants}>
                <AnimatePresence mode="wait">
                  <motion.div
                    key={activeFeature}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                    className="bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-3xl p-4 sm:p-6 lg:p-8 shadow-2xl border border-gray-200 dark:border-gray-600"
                  >
                    {/* Feature Icon and Title */}
                    <div className="flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
                      <div className={`w-16 h-16 sm:w-18 sm:h-18 lg:w-20 lg:h-20 rounded-2xl bg-gradient-to-br ${activeFeatureData.bgColor} flex items-center justify-center shadow-lg`}>
                        <activeFeatureData.icon className="w-8 h-8 sm:w-9 sm:h-9 lg:w-10 lg:h-10 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-2 leading-tight">
                          {activeFeatureData.title}
                        </h3>
                        <div className={`inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-medium bg-white/50 dark:bg-gray-700/50 ${activeFeatureData.color}`}>
                          <Star className="w-3 h-3 mr-1" />
                          {t('home.features.exclusiveFeature')}
                        </div>
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-gray-600 dark:text-gray-300 text-base sm:text-lg leading-relaxed mb-6 sm:mb-8">
                      {activeFeatureData.description}
                    </p>

                    {/* All Benefits */}
                    <div className="space-y-3 sm:space-y-4 mb-6 sm:mb-8">
                      <h4 className="text-base sm:text-lg font-bold text-gray-900 dark:text-white">
                        {t('home.features.keyFeatures')}:
                      </h4>
                      {Array.isArray(activeFeatureData.benefits) && activeFeatureData.benefits.map((benefit, index) => (
                        <motion.div
                          key={index}
                          className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 bg-white/50 dark:bg-gray-700/50 rounded-xl"
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                        >
                          <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-lg bg-gradient-to-br ${activeFeatureData.bgColor} flex items-center justify-center flex-shrink-0`}>
                            <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                          </div>
                          <span className="text-gray-700 dark:text-gray-300 font-medium text-sm sm:text-base">
                            {benefit}
                          </span>
                        </motion.div>
                      ))}
                    </div>

                    {/* CTA Button */}
                    <motion.button
                      className={`w-full bg-gradient-to-r ${activeFeatureData.bgColor} text-white py-3 sm:py-4 rounded-xl font-bold text-base sm:text-lg shadow-lg hover:shadow-xl transition-all duration-300`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      {t('home.features.tryFeature')}
                    </motion.button>
                  </motion.div>
                </AnimatePresence>
              </motion.div>
            </div>
          </div>

          {/* Bottom Stats */}
          <motion.div
            className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 mt-12 sm:mt-16 lg:mt-20 pt-8 sm:pt-12 lg:pt-16 border-t border-gray-200 dark:border-gray-700"
            variants={itemVariants}
          >
            <div className="text-center px-2">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-2 sm:mb-3">
                <Shield className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-1">100%</div>
              <div className="text-gray-600 dark:text-gray-400 text-xs sm:text-sm leading-relaxed">{t('home.features.quickStats.fundProtection')}</div>
            </div>

            <div className="text-center px-2">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-2 sm:mb-3">
                <Zap className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-1">0.5%</div>
              <div className="text-gray-600 dark:text-gray-400 text-xs sm:text-sm leading-relaxed">{t('home.features.quickStats.lowFees')}</div>
            </div>

            <div className="text-center px-2">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-2 sm:mb-3">
                <Clock className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-1">24/7</div>
              <div className="text-gray-600 dark:text-gray-400 text-xs sm:text-sm leading-relaxed">{t('home.features.quickStats.continuousSupport')}</div>
            </div>

            <div className="text-center px-2">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-2 sm:mb-3">
                <Users className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-1">50K+</div>
              <div className="text-gray-600 dark:text-gray-400 text-xs sm:text-sm leading-relaxed">{t('home.features.quickStats.satisfiedUsers')}</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
