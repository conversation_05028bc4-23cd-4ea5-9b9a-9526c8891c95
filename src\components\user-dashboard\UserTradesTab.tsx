'use client';

import { useState, useEffect } from 'react';
import {
  Activity,
  Search,
  Filter,
  Eye,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  DollarSign,
  MessageSquare,
  ExternalLink
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet, handleApiError } from '@/utils/apiClient';

interface Trade {
  id: string;
  type: 'buy' | 'sell';
  amount: number;
  currency: string;
  price: number;
  status: 'active' | 'completed' | 'cancelled' | 'disputed';
  partner: {
    username: string;
    rating: number;
    isVerified: boolean;
  };
  createdAt: string;
  updatedAt: string;
  escrowAmount?: number;
  paymentMethod: string;
}

export default function UserTradesTab() {
  const { formatCurrency, formatDate } = useUserDashboardTranslation();
  const { user } = useAuth();
  const [trades, setTrades] = useState<Trade[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'active' | 'completed' | 'cancelled'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const fetchTrades = async () => {
      if (!user?.id) {
        setError('معرف المستخدم غير متاح');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // استدعاء API الحقيقي
        const response = await apiGet(`trades/index.php?user_id=${user.id}`);

        if (!response.success) {
          throw new Error(response.message || 'فشل في جلب الصفقات');
        }

        // تحويل البيانات من API إلى تنسيق Trade
        const apiTrades = response.data || [];
        const formattedTrades: Trade[] = apiTrades.map((trade: any) => ({
          id: trade.id.toString(),
          type: trade.trade_type || 'buy',
          amount: parseFloat(trade.amount) || 0,
          currency: trade.currency || 'USDT',
          price: parseFloat(trade.price) || 0,
          status: mapTradeStatus(trade.status),
          partner: {
            username: trade.partner_username || 'مستخدم غير معروف',
            rating: parseFloat(trade.partner_rating) || 0,
            isVerified: Boolean(trade.partner_verified)
          },
          createdAt: trade.created_at || new Date().toISOString(),
          updatedAt: trade.updated_at || new Date().toISOString(),
          escrowAmount: parseFloat(trade.escrow_amount) || 0,
          paymentMethod: trade.payment_method || 'غير محدد'
        }));

        setTrades(formattedTrades);
      } catch (err) {
        const errorMessage = handleApiError(err);
        setError(errorMessage);
        console.error('Error fetching trades:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchTrades();
  }, [user?.id]);

  // دالة لتحويل حالة الصفقة من API إلى التنسيق المطلوب
  const mapTradeStatus = (apiStatus: string): Trade['status'] => {
    switch (apiStatus?.toLowerCase()) {
      case 'created':
      case 'buyer_joined':
      case 'payment_sent':
        return 'active';
      case 'completed':
        return 'completed';
      case 'cancelled':
        return 'cancelled';
      case 'disputed':
        return 'disputed';
      default:
        return 'active';
    }
  };

  const filteredTrades = trades.filter(trade => {
    const matchesFilter = filter === 'all' || trade.status === filter;
    const matchesSearch = trade.partner.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         trade.id.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const getStatusColor = (status: Trade['status']) => {
    switch (status) {
      case 'active':
        return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      case 'completed':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'cancelled':
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
      case 'disputed':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getStatusText = (status: Trade['status']) => {
    switch (status) {
      case 'active':
        return 'نشطة';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      case 'disputed':
        return 'متنازع عليها';
      default:
        return 'غير معروف';
    }
  };

  const getStatusIcon = (status: Trade['status']) => {
    switch (status) {
      case 'active':
        return <Clock className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'cancelled':
        return <AlertCircle className="w-4 h-4" />;
      case 'disputed':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  // عرض رسالة الخطأ
  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6">
          <div className="flex items-center gap-3">
            <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
            <div>
              <h3 className="text-red-800 dark:text-red-200 font-medium">خطأ في تحميل الصفقات</h3>
              <p className="text-red-600 dark:text-red-400 text-sm mt-1">{error}</p>
            </div>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">إدارة الصفقات</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            متابعة وإدارة جميع صفقاتك
          </p>
        </div>
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
          بدء صفقة جديدة
        </button>
      </div>

      {/* المرشحات والبحث */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* البحث */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 rtl:right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="البحث في الصفقات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 rtl:pr-10 pr-3 rtl:pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* المرشحات */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Filter className="w-4 h-4 text-gray-500" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as typeof filter)}
                className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الصفقات</option>
                <option value="active">النشطة</option>
                <option value="completed">المكتملة</option>
                <option value="cancelled">الملغية</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* قائمة الصفقات */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        {loading ? (
          <div className="p-6">
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded-lg" />
                </div>
              ))}
            </div>
          </div>
        ) : filteredTrades.length > 0 ? (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredTrades.map((trade) => (
              <div key={trade.id} className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                      trade.type === 'buy' ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30'
                    }`}>
                      {trade.type === 'buy' ? (
                        <TrendingUp className="w-6 h-6 text-green-600 dark:text-green-400" />
                      ) : (
                        <DollarSign className="w-6 h-6 text-red-600 dark:text-red-400" />
                      )}
                    </div>

                    <div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {trade.type === 'buy' ? 'شراء' : 'بيع'} {trade.amount} {trade.currency}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 rtl:space-x-reverse ${getStatusColor(trade.status)}`}>
                          {getStatusIcon(trade.status)}
                          <span>{getStatusText(trade.status)}</span>
                        </span>
                      </div>
                      <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-600 dark:text-gray-400 mt-1">
                        <span>مع: {trade.partner.username}</span>
                        <span>•</span>
                        <span>السعر: {formatCurrency(trade.price, 'SAR')}</span>
                        <span>•</span>
                        <span>{formatDate(trade.createdAt, { day: 'numeric', month: 'short' })}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <button className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                      <MessageSquare className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                      <Eye className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                      <ExternalLink className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-12 text-center">
            <Activity className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              لا توجد صفقات
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {filter === 'all' 
                ? 'لم تقم بأي صفقات بعد'
                : `لا توجد صفقات ${getStatusText(filter as Trade['status'])}`
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
