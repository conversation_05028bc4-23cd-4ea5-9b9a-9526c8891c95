'use client';

import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  Users,
  DollarSign,
  TrendingUp,
  Package,
  Shield,
  CheckCircle,
  XCircle,
  Clock,
  Star,
  Crown,
  Zap,
  Building,
  Gift,
  RefreshCw,
  Download,
  Upload,
  Filter,
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Settings,
  BarChart3,
  PieChart,
  LineChart,
  Calendar,
  Mail,
  Phone,
  Smartphone,
  Wallet,
  Key,
  AlertTriangle,
  Info,
  Target,
  Award,
  Percent,
  Activity,
  Globe,
  Lock,
  Unlock,
  PlayCircle,
  PauseCircle,
  StopCircle,
  RotateCcw,
  ArrowUpCircle,
  ArrowDownCircle,
  Copy,
  ExternalLink,
  FileText,
  Calculator,
  Banknote,
  Receipt
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';

interface SubscriptionPlan {
  id: string;
  name: string;
  type: 'free' | 'basic' | 'pro' | 'enterprise';
  price: number;
  currency: string;
  billingCycle: 'monthly' | 'yearly';
  features: {
    offersLimit: number;
    commission: number;
    support: string;
    analytics: boolean;
    priority: number;
    apiAccess: boolean;
    escrowFree: boolean;
    whiteLabel?: boolean;
    dedicatedManager?: boolean;
  };
  isActive: boolean;
  userCount: number;
  revenue: number;
  createdAt: string;
  updatedAt: string;
}

interface UserSubscription {
  id: string;
  userId: string;
  username: string;
  email: string;
  planId: string;
  planName: string;
  status: 'active' | 'expired' | 'cancelled' | 'pending';
  startDate: string;
  endDate: string;
  autoRenew: boolean;
  paymentMethod: string;
  totalPaid: number;
  offersUsed: number;
  offersLimit: number;
  isVerified: boolean;
  verificationLevel: number;
  lastActivity: string;
}

interface PaymentTransaction {
  id: string;
  userId: string;
  username: string;
  planId: string;
  planName: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  paymentMethod: string;
  transactionId: string;
  createdAt: string;
  processedAt?: string;
  failureReason?: string;
}

interface SubscriptionStats {
  totalRevenue: number;
  monthlyRevenue: number;
  activeSubscriptions: number;
  freeUsers: number;
  paidUsers: number;
  conversionRate: number;
  churnRate: number;
  averageRevenue: number;
  lifetimeValue: number;
  monthlyGrowth: number;
}

interface SubscriptionManagementProps {
  className?: string;
}

export default function SubscriptionManagement({ className = '' }: SubscriptionManagementProps) {
  const { t, language, isRTL, getDirectionClasses, formatNumber, formatCurrency, formatDate, formatRelativeTime } = useAdminTranslation();
  const dirClasses = getDirectionClasses();
  
  const [activeTab, setActiveTab] = useState('overview');
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [subscriptions, setSubscriptions] = useState<UserSubscription[]>([]);
  const [transactions, setTransactions] = useState<PaymentTransaction[]>([]);
  const [stats, setStats] = useState<SubscriptionStats | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [selectedSubscription, setSelectedSubscription] = useState<UserSubscription | null>(null);
  const [showPlanModal, setShowPlanModal] = useState(false);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPlan, setFilterPlan] = useState('all');

  // Mock data - replace with real API calls
  useEffect(() => {
    // Mock subscription plans
    setPlans([
      {
        id: '1',
        name: 'Free Plan',
        type: 'free',
        price: 0,
        currency: 'USD',
        billingCycle: 'monthly',
        features: {
          offersLimit: 3,
          commission: 2.0,
          support: 'Basic',
          analytics: false,
          priority: 1,
          apiAccess: false,
          escrowFree: false
        },
        isActive: true,
        userCount: 1250,
        revenue: 0,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '2',
        name: 'Basic Plan',
        type: 'basic',
        price: 9.99,
        currency: 'USD',
        billingCycle: 'monthly',
        features: {
          offersLimit: 15,
          commission: 1.5,
          support: 'Advanced',
          analytics: true,
          priority: 2,
          apiAccess: false,
          escrowFree: false
        },
        isActive: true,
        userCount: 320,
        revenue: 3196.8,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-15T10:30:00Z'
      },
      {
        id: '3',
        name: 'Pro Plan',
        type: 'pro',
        price: 24.99,
        currency: 'USD',
        billingCycle: 'monthly',
        features: {
          offersLimit: 50,
          commission: 1.0,
          support: 'Dedicated',
          analytics: true,
          priority: 3,
          apiAccess: true,
          escrowFree: true
        },
        isActive: true,
        userCount: 180,
        revenue: 4498.2,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-20T14:15:00Z'
      },
      {
        id: '4',
        name: 'Enterprise Plan',
        type: 'enterprise',
        price: 99.99,
        currency: 'USD',
        billingCycle: 'monthly',
        features: {
          offersLimit: -1, // Unlimited
          commission: 0.5,
          support: '24/7',
          analytics: true,
          priority: 4,
          apiAccess: true,
          escrowFree: true,
          whiteLabel: true,
          dedicatedManager: true
        },
        isActive: true,
        userCount: 45,
        revenue: 4499.55,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-18T09:45:00Z'
      }
    ]);

    // Mock user subscriptions
    setSubscriptions([
      {
        id: '1',
        userId: '101',
        username: 'john_trader',
        email: '<EMAIL>',
        planId: '2',
        planName: 'Basic Plan',
        status: 'active',
        startDate: '2024-01-15T00:00:00Z',
        endDate: '2024-02-15T00:00:00Z',
        autoRenew: true,
        paymentMethod: 'Credit Card',
        totalPaid: 9.99,
        offersUsed: 8,
        offersLimit: 15,
        isVerified: true,
        verificationLevel: 3,
        lastActivity: '2024-01-20T10:30:00Z'
      },
      {
        id: '2',
        userId: '102',
        username: 'crypto_pro',
        email: '<EMAIL>',
        planId: '3',
        planName: 'Pro Plan',
        status: 'active',
        startDate: '2024-01-10T00:00:00Z',
        endDate: '2024-02-10T00:00:00Z',
        autoRenew: true,
        paymentMethod: 'Cryptocurrency',
        totalPaid: 24.99,
        offersUsed: 32,
        offersLimit: 50,
        isVerified: true,
        verificationLevel: 4,
        lastActivity: '2024-01-20T15:45:00Z'
      },
      {
        id: '3',
        userId: '103',
        username: 'enterprise_corp',
        email: '<EMAIL>',
        planId: '4',
        planName: 'Enterprise Plan',
        status: 'active',
        startDate: '2024-01-01T00:00:00Z',
        endDate: '2024-02-01T00:00:00Z',
        autoRenew: true,
        paymentMethod: 'Bank Transfer',
        totalPaid: 99.99,
        offersUsed: 125,
        offersLimit: -1,
        isVerified: true,
        verificationLevel: 5,
        lastActivity: '2024-01-20T16:20:00Z'
      }
    ]);

    // Mock payment transactions
    setTransactions([
      {
        id: '1',
        userId: '101',
        username: 'john_trader',
        planId: '2',
        planName: 'Basic Plan',
        amount: 9.99,
        currency: 'USD',
        status: 'completed',
        paymentMethod: 'Credit Card',
        transactionId: 'txn_1234567890',
        createdAt: '2024-01-15T00:00:00Z',
        processedAt: '2024-01-15T00:05:00Z'
      },
      {
        id: '2',
        userId: '102',
        username: 'crypto_pro',
        planId: '3',
        planName: 'Pro Plan',
        amount: 24.99,
        currency: 'USD',
        status: 'completed',
        paymentMethod: 'Cryptocurrency',
        transactionId: 'txn_0987654321',
        createdAt: '2024-01-10T00:00:00Z',
        processedAt: '2024-01-10T00:15:00Z'
      },
      {
        id: '3',
        userId: '104',
        username: 'failed_user',
        planId: '2',
        planName: 'Basic Plan',
        amount: 9.99,
        currency: 'USD',
        status: 'failed',
        paymentMethod: 'Credit Card',
        transactionId: 'txn_1111111111',
        createdAt: '2024-01-18T00:00:00Z',
        failureReason: 'Insufficient funds'
      }
    ]);

    // Mock statistics
    setStats({
      totalRevenue: 12194.57,
      monthlyRevenue: 8547.32,
      activeSubscriptions: 545,
      freeUsers: 1250,
      paidUsers: 545,
      conversionRate: 30.4,
      churnRate: 5.2,
      averageRevenue: 22.38,
      lifetimeValue: 134.28,
      monthlyGrowth: 15.7
    });
  }, []);

  const tabs = [
    { id: 'overview', label: t('common.overview'), icon: BarChart3 },
    { id: 'plans', label: t('subscriptions.plans.title'), icon: Package },
    { id: 'subscriptions', label: t('subscriptions.management.title'), icon: Users },
    { id: 'payments', label: t('subscriptions.payments.title'), icon: CreditCard },
    { id: 'verification', label: t('subscriptions.verification.title'), icon: Shield },
    { id: 'analytics', label: t('subscriptions.analytics.title'), icon: PieChart },
    { id: 'settings', label: t('subscriptions.settings.title'), icon: Settings }
  ];

  const getPlanIcon = (type: string) => {
    switch (type) {
      case 'free': return Gift;
      case 'basic': return Star;
      case 'pro': return Crown;
      case 'enterprise': return Building;
      default: return Package;
    }
  };

  const getPlanColor = (type: string) => {
    switch (type) {
      case 'free': return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700';
      case 'basic': return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      case 'pro': return 'text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/30';
      case 'enterprise': return 'text-gold-600 dark:text-gold-400 bg-gold-100 dark:bg-gold-900/30';
      default: return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'expired': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      case 'cancelled': return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700';
      case 'pending': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30';
      default: return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'pending': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30';
      case 'failed': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      case 'refunded': return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      case 'cancelled': return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700';
      default: return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700';
    }
  };

  const filteredSubscriptions = subscriptions.filter(sub => {
    const matchesSearch = sub.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sub.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sub.planName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || sub.status === filterStatus;
    const matchesPlan = filterPlan === 'all' || sub.planId === filterPlan;
    return matchesSearch && matchesStatus && matchesPlan;
  });

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className={`flex flex-col sm:flex-row sm:items-center justify-between gap-4 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
        <div className={isRTL ? 'text-right' : 'text-left'}>
          <h3 className={`text-xl font-bold text-gray-900 dark:text-white flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <CreditCard className={`w-6 h-6 text-blue-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            {t('subscriptions.title')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('subscriptions.subtitle')}</p>
        </div>
        <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
            <Download className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.export')}
          </button>
          <button className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
            <Plus className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('subscriptions.management.createPlan')}
          </button>
        </div>
      </div>

      {/* Subscription Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className={`flex space-x-8 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <Icon className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {tab.label}
                </div>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && stats && (
          <div className="space-y-6">
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={isRTL ? 'text-right' : 'text-left'}>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('subscriptions.stats.totalRevenue')}</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatCurrency(stats.totalRevenue)}</p>
                    <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                      +{formatNumber(stats.monthlyGrowth)}% {t('common.thisMonth')}
                    </p>
                  </div>
                  <DollarSign className="w-8 h-8 text-green-500" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={isRTL ? 'text-right' : 'text-left'}>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('subscriptions.stats.activeSubscriptions')}</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatNumber(stats.activeSubscriptions)}</p>
                    <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                      {formatNumber(stats.conversionRate)}% {t('subscriptions.stats.conversionRate')}
                    </p>
                  </div>
                  <Users className="w-8 h-8 text-blue-500" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={isRTL ? 'text-right' : 'text-left'}>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('subscriptions.stats.averageRevenue')}</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatCurrency(stats.averageRevenue)}</p>
                    <p className="text-xs text-purple-600 dark:text-purple-400 mt-1">
                      {formatCurrency(stats.lifetimeValue)} LTV
                    </p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-purple-500" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={isRTL ? 'text-right' : 'text-left'}>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('subscriptions.stats.churnRate')}</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatNumber(stats.churnRate)}%</p>
                    <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                      {formatNumber(stats.freeUsers)} {t('subscriptions.stats.freeUsers')}
                    </p>
                  </div>
                  <Activity className="w-8 h-8 text-red-500" />
                </div>
              </div>
            </div>

            {/* Plans Overview */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('subscriptions.plans.title')}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Overview of all subscription plans</p>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {plans.map((plan) => {
                    const PlanIcon = getPlanIcon(plan.type);
                    return (
                      <div key={plan.id} className={`p-4 border-2 rounded-lg ${plan.isActive ? 'border-green-200 dark:border-green-700' : 'border-gray-200 dark:border-gray-600'}`}>
                        <div className={`flex items-center justify-between mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getPlanColor(plan.type)}`}>
                            <PlanIcon className="w-5 h-5" />
                          </div>
                          {plan.isActive ? (
                            <CheckCircle className="w-5 h-5 text-green-500" />
                          ) : (
                            <XCircle className="w-5 h-5 text-red-500" />
                          )}
                        </div>
                        <h5 className="font-medium text-gray-900 dark:text-white mb-1">{plan.name}</h5>
                        <p className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                          {plan.price === 0 ? t('common.free') : formatCurrency(plan.price)}
                          {plan.price > 0 && <span className="text-sm font-normal text-gray-500">/{t('common.month')}</span>}
                        </p>
                        <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                          <p>{formatNumber(plan.userCount)} {t('common.users')}</p>
                          <p>{formatCurrency(plan.revenue)} {t('common.revenue')}</p>
                          <p>{plan.features.offersLimit === -1 ? t('common.unlimited') : formatNumber(plan.features.offersLimit)} {t('common.offers')}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Recent Subscriptions */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('subscriptions.management.activeSubscriptions')}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Recent subscription activities</p>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {subscriptions.slice(0, 5).map((subscription) => (
                    <div key={subscription.id} className={`flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className={`w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center ${isRTL ? 'ml-4' : 'mr-4'}`}>
                          <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div className={isRTL ? 'text-right' : 'text-left'}>
                          <p className="font-medium text-gray-900 dark:text-white">{subscription.username}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{subscription.planName} • {formatDate(subscription.startDate)}</p>
                        </div>
                      </div>
                      <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(subscription.status)}`}>
                          {t(`subscriptions.payments.status.${subscription.status}`)}
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {formatCurrency(subscription.totalPaid)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Plans Tab */}
        {activeTab === 'plans' && (
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className={`p-6 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={isRTL ? 'text-right' : 'text-left'}>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('subscriptions.plans.title')}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('subscriptions.plans.subtitle')}</p>
                </div>
                <button
                  onClick={() => setShowPlanModal(true)}
                  className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
                >
                  <Plus className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('subscriptions.management.createPlan')}
                </button>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
                  {plans.map((plan) => {
                    const PlanIcon = getPlanIcon(plan.type);
                    return (
                      <div key={plan.id} className={`relative p-6 border-2 rounded-xl ${plan.type === 'pro' ? 'border-purple-300 dark:border-purple-700 bg-purple-50 dark:bg-purple-900/20' : 'border-gray-200 dark:border-gray-600'}`}>
                        {plan.type === 'pro' && (
                          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                            <span className="bg-purple-600 text-white px-3 py-1 rounded-full text-xs font-medium">
                              {t('common.popular')}
                            </span>
                          </div>
                        )}

                        <div className="text-center mb-6">
                          <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4 ${getPlanColor(plan.type)}`}>
                            <PlanIcon className="w-8 h-8" />
                          </div>
                          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{plan.name}</h3>
                          <div className="mb-4">
                            <span className="text-3xl font-bold text-gray-900 dark:text-white">
                              {plan.price === 0 ? t('common.free') : `$${plan.price}`}
                            </span>
                            {plan.price > 0 && (
                              <span className="text-gray-500 dark:text-gray-400">/{t('common.month')}</span>
                            )}
                          </div>
                        </div>

                        <div className="space-y-3 mb-6">
                          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              {plan.features.offersLimit === -1 ? t('common.unlimited') : formatNumber(plan.features.offersLimit)} {t('subscriptions.plans.free.features.offers')}
                            </span>
                          </div>
                          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              {formatNumber(plan.features.commission)}% {t('subscriptions.plans.free.features.commission')}
                            </span>
                          </div>
                          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              {plan.features.support} {t('subscriptions.plans.free.features.support')}
                            </span>
                          </div>
                          {plan.features.analytics && (
                            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                              <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                              <span className="text-sm text-gray-600 dark:text-gray-400">
                                {t('subscriptions.plans.basic.features.analytics')}
                              </span>
                            </div>
                          )}
                          {plan.features.apiAccess && (
                            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                              <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                              <span className="text-sm text-gray-600 dark:text-gray-400">
                                {t('subscriptions.plans.pro.features.api')}
                              </span>
                            </div>
                          )}
                          {plan.features.whiteLabel && (
                            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                              <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                              <span className="text-sm text-gray-600 dark:text-gray-400">
                                {t('subscriptions.plans.enterprise.features.whitelabel')}
                              </span>
                            </div>
                          )}
                        </div>

                        <div className="space-y-2 mb-6 text-sm text-gray-600 dark:text-gray-400">
                          <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <span>{t('common.users')}:</span>
                            <span className="font-medium">{formatNumber(plan.userCount)}</span>
                          </div>
                          <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <span>{t('common.revenue')}:</span>
                            <span className="font-medium">{formatCurrency(plan.revenue)}</span>
                          </div>
                          <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <span>{t('common.status')}:</span>
                            <span className={`font-medium ${plan.isActive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                              {plan.isActive ? t('common.active') : t('common.inactive')}
                            </span>
                          </div>
                        </div>

                        <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <button
                            onClick={() => {
                              setSelectedPlan(plan);
                              setShowPlanModal(true);
                            }}
                            className="flex-1 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors"
                          >
                            {t('subscriptions.management.editPlan')}
                          </button>
                          <button
                            onClick={() => console.log('Toggle plan status')}
                            className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                              plan.isActive
                                ? 'bg-red-600 hover:bg-red-700 text-white'
                                : 'bg-green-600 hover:bg-green-700 text-white'
                            }`}
                          >
                            {plan.isActive ? <Lock className="w-4 h-4" /> : <Unlock className="w-4 h-4" />}
                          </button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Subscriptions Tab */}
        {activeTab === 'subscriptions' && (
          <div className="space-y-6">
            {/* Filters */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <div className={`flex flex-col sm:flex-row gap-4 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
                <div className="flex-1">
                  <div className="relative">
                    <Search className={`absolute top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 ${isRTL ? 'right-3' : 'left-3'}`} />
                    <input
                      type="text"
                      placeholder={t('common.actions.search')}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className={`w-full bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg py-2 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'}`}
                    />
                  </div>
                </div>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">{t('common.all')} {t('common.status')}</option>
                  <option value="active">{t('subscriptions.payments.status.active')}</option>
                  <option value="expired">{t('subscriptions.payments.status.expired')}</option>
                  <option value="cancelled">{t('subscriptions.payments.status.cancelled')}</option>
                  <option value="pending">{t('subscriptions.payments.status.pending')}</option>
                </select>
                <select
                  value={filterPlan}
                  onChange={(e) => setFilterPlan(e.target.value)}
                  className="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">{t('common.all')} {t('subscriptions.plans.title')}</option>
                  {plans.map((plan) => (
                    <option key={plan.id} value={plan.id}>{plan.name}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Subscriptions List */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('subscriptions.management.title')}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {formatNumber(filteredSubscriptions.length)} {t('common.results')}
                </p>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                        {t('common.user')}
                      </th>
                      <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                        {t('subscriptions.plans.title')}
                      </th>
                      <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                        {t('common.status')}
                      </th>
                      <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                        {t('common.usage')}
                      </th>
                      <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                        {t('common.payment')}
                      </th>
                      <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                        {t('common.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredSubscriptions.map((subscription) => (
                      <tr key={subscription.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <div className={`w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center ${isRTL ? 'ml-3' : 'mr-3'}`}>
                              <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div className={isRTL ? 'text-right' : 'text-left'}>
                              <div className="text-sm font-medium text-gray-900 dark:text-white">{subscription.username}</div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">{subscription.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={isRTL ? 'text-right' : 'text-left'}>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">{subscription.planName}</div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {formatDate(subscription.startDate)} - {formatDate(subscription.endDate)}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(subscription.status)}`}>
                            {t(`subscriptions.payments.status.${subscription.status}`)}
                          </span>
                          {subscription.isVerified && (
                            <CheckCircle className="w-4 h-4 text-green-500 mt-1" />
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={isRTL ? 'text-right' : 'text-left'}>
                            <div className="text-sm text-gray-900 dark:text-white">
                              {formatNumber(subscription.offersUsed)} / {subscription.offersLimit === -1 ? '∞' : formatNumber(subscription.offersLimit)}
                            </div>
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{
                                  width: subscription.offersLimit === -1 ? '100%' : `${Math.min((subscription.offersUsed / subscription.offersLimit) * 100, 100)}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={isRTL ? 'text-right' : 'text-left'}>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">{formatCurrency(subscription.totalPaid)}</div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">{subscription.paymentMethod}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <button
                              onClick={() => {
                                setSelectedSubscription(subscription);
                                setShowSubscriptionModal(true);
                              }}
                              className="p-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                              title={t('common.actions.view')}
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => console.log('Edit subscription')}
                              className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300"
                              title={t('common.actions.edit')}
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => console.log('Upgrade subscription')}
                              className="p-1 text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
                              title={t('subscriptions.actions.upgrade')}
                            >
                              <ArrowUpCircle className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Verification Tab */}
        {activeTab === 'verification' && (
          <div className="space-y-6">
            {/* Verification Requirements */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('subscriptions.verification.requirements.title')}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('subscriptions.verification.subtitle')}</p>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                    <div className={`flex items-center mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Wallet className={`w-6 h-6 text-blue-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                      <h5 className="font-medium text-gray-900 dark:text-white">{t('subscriptions.verification.walletVerification')}</h5>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      {t('subscriptions.verification.requirements.wallet')}
                    </p>
                    <div className="space-y-2">
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        <span className="text-xs text-gray-600 dark:text-gray-400">Valid wallet connection</span>
                      </div>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        <span className="text-xs text-gray-600 dark:text-gray-400">Transaction history</span>
                      </div>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        <span className="text-xs text-gray-600 dark:text-gray-400">Balance verification</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                    <div className={`flex items-center mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Mail className={`w-6 h-6 text-green-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                      <h5 className="font-medium text-gray-900 dark:text-white">{t('subscriptions.verification.emailVerification')}</h5>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      {t('subscriptions.verification.requirements.email')}
                    </p>
                    <div className="space-y-2">
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        <span className="text-xs text-gray-600 dark:text-gray-400">Email confirmation</span>
                      </div>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        <span className="text-xs text-gray-600 dark:text-gray-400">Domain validation</span>
                      </div>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        <span className="text-xs text-gray-600 dark:text-gray-400">Anti-spam check</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                    <div className={`flex items-center mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Key className={`w-6 h-6 text-purple-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                      <h5 className="font-medium text-gray-900 dark:text-white">{t('subscriptions.verification.passwordSecurity')}</h5>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      {t('subscriptions.verification.requirements.password')}
                    </p>
                    <div className="space-y-2">
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        <span className="text-xs text-gray-600 dark:text-gray-400">Minimum 8 characters</span>
                      </div>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        <span className="text-xs text-gray-600 dark:text-gray-400">Mixed case letters</span>
                      </div>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        <span className="text-xs text-gray-600 dark:text-gray-400">Numbers & symbols</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Anti-Spam Measures */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('subscriptions.verification.antiSpam.title')}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Advanced measures to prevent fake accounts</p>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                  <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Globe className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                    <h6 className="font-medium text-gray-900 dark:text-white text-sm">{t('subscriptions.verification.antiSpam.ipTracking')}</h6>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">Monitor IP patterns</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Smartphone className="w-8 h-8 text-green-500 mx-auto mb-2" />
                    <h6 className="font-medium text-gray-900 dark:text-white text-sm">{t('subscriptions.verification.antiSpam.deviceFingerprint')}</h6>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">Unique device ID</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Activity className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                    <h6 className="font-medium text-gray-900 dark:text-white text-sm">{t('subscriptions.verification.antiSpam.behaviorAnalysis')}</h6>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">User behavior patterns</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Copy className="w-8 h-8 text-orange-500 mx-auto mb-2" />
                    <h6 className="font-medium text-gray-900 dark:text-white text-sm">{t('subscriptions.verification.antiSpam.duplicateDetection')}</h6>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">Detect duplicates</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Target className="w-8 h-8 text-red-500 mx-auto mb-2" />
                    <h6 className="font-medium text-gray-900 dark:text-white text-sm">{t('subscriptions.verification.antiSpam.riskScoring')}</h6>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">Risk assessment</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Payments Tab */}
        {activeTab === 'payments' && (
          <div className="space-y-6">
            {/* Payment Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={isRTL ? 'text-right' : 'text-left'}>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('subscriptions.payments.transactions')}</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatNumber(transactions.length)}</p>
                  </div>
                  <CreditCard className="w-8 h-8 text-blue-500" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={isRTL ? 'text-right' : 'text-left'}>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Successful Payments</p>
                    <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {formatNumber(transactions.filter(t => t.status === 'completed').length)}
                    </p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-500" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={isRTL ? 'text-right' : 'text-left'}>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Failed Payments</p>
                    <p className="text-2xl font-bold text-red-600 dark:text-red-400">
                      {formatNumber(transactions.filter(t => t.status === 'failed').length)}
                    </p>
                  </div>
                  <XCircle className="w-8 h-8 text-red-500" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={isRTL ? 'text-right' : 'text-left'}>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Payments</p>
                    <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                      {formatNumber(transactions.filter(t => t.status === 'pending').length)}
                    </p>
                  </div>
                  <Clock className="w-8 h-8 text-yellow-500" />
                </div>
              </div>
            </div>

            {/* Payment Transactions */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('subscriptions.payments.transactions')}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Recent payment transactions</p>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                        Transaction ID
                      </th>
                      <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                        {t('common.user')}
                      </th>
                      <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                        {t('subscriptions.plans.title')}
                      </th>
                      <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                        Amount
                      </th>
                      <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                        {t('common.status')}
                      </th>
                      <th className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isRTL ? 'text-right' : 'text-left'}`}>
                        Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {transactions.map((transaction) => (
                      <tr key={transaction.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-mono text-gray-900 dark:text-white">{transaction.transactionId}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">{transaction.username}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">{transaction.planName}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {formatCurrency(transaction.amount)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPaymentStatusColor(transaction.status)}`}>
                            {t(`subscriptions.payments.status.${transaction.status}`)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900 dark:text-white">{formatDate(transaction.createdAt)}</div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Payment Methods */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('subscriptions.payments.methods')}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Supported payment gateways</p>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg text-center">
                    <CreditCard className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                    <h6 className="font-medium text-gray-900 dark:text-white">{t('subscriptions.payments.gateways.stripe')}</h6>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">Credit & Debit Cards</p>
                    <div className="mt-2">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                        Active
                      </span>
                    </div>
                  </div>

                  <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg text-center">
                    <Wallet className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                    <h6 className="font-medium text-gray-900 dark:text-white">{t('subscriptions.payments.gateways.paypal')}</h6>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">Digital Wallet</p>
                    <div className="mt-2">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                        Active
                      </span>
                    </div>
                  </div>

                  <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg text-center">
                    <Zap className="w-8 h-8 text-orange-500 mx-auto mb-2" />
                    <h6 className="font-medium text-gray-900 dark:text-white">{t('subscriptions.payments.gateways.crypto')}</h6>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">Cryptocurrency</p>
                    <div className="mt-2">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                        Active
                      </span>
                    </div>
                  </div>

                  <div className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg text-center">
                    <Building className="w-8 h-8 text-gray-500 mx-auto mb-2" />
                    <h6 className="font-medium text-gray-900 dark:text-white">{t('subscriptions.payments.gateways.bank')}</h6>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">Bank Transfer</p>
                    <div className="mt-2">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                        Manual
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && stats && (
          <div className="space-y-6">
            {/* Revenue Analytics */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('subscriptions.analytics.revenue.title')}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Revenue performance and trends</p>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                      {formatCurrency(stats.monthlyRevenue)}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{t('subscriptions.analytics.revenue.monthly')}</p>
                    <div className="mt-2">
                      <span className="text-green-600 dark:text-green-400 text-sm font-medium">
                        +{formatNumber(stats.monthlyGrowth)}%
                      </span>
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                      {formatCurrency(stats.totalRevenue)}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{t('subscriptions.analytics.revenue.yearly')}</p>
                    <div className="mt-2">
                      <span className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                        All Time
                      </span>
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                      {formatCurrency(stats.averageRevenue)}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{t('subscriptions.analytics.revenue.byPlan')}</p>
                    <div className="mt-2">
                      <span className="text-purple-600 dark:text-purple-400 text-sm font-medium">
                        Per User
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* User Analytics */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('subscriptions.analytics.users.title')}</h4>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    <div className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-sm text-gray-600 dark:text-gray-400">{t('subscriptions.stats.freeUsers')}</span>
                      <span className="font-medium text-gray-900 dark:text-white">{formatNumber(stats.freeUsers)}</span>
                    </div>
                    <div className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-sm text-gray-600 dark:text-gray-400">{t('subscriptions.stats.paidUsers')}</span>
                      <span className="font-medium text-gray-900 dark:text-white">{formatNumber(stats.paidUsers)}</span>
                    </div>
                    <div className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-sm text-gray-600 dark:text-gray-400">{t('subscriptions.analytics.users.conversion')}</span>
                      <span className="font-medium text-green-600 dark:text-green-400">{formatNumber(stats.conversionRate)}%</span>
                    </div>
                    <div className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-sm text-gray-600 dark:text-gray-400">{t('subscriptions.analytics.users.churn')}</span>
                      <span className="font-medium text-red-600 dark:text-red-400">{formatNumber(stats.churnRate)}%</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('subscriptions.analytics.performance.title')}</h4>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {plans.map((plan) => (
                      <div key={plan.id} className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <div className={`w-3 h-3 rounded-full ${getPlanColor(plan.type)} ${isRTL ? 'ml-2' : 'mr-2'}`}></div>
                          <span className="text-sm text-gray-600 dark:text-gray-400">{plan.name}</span>
                        </div>
                        <div className={`text-right ${isRTL ? 'text-left' : ''}`}>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">{formatNumber(plan.userCount)}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">{formatCurrency(plan.revenue)}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Settings Tab */}
        {activeTab === 'settings' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Pricing Settings */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('subscriptions.settings.pricing')}</h4>
                </div>
                <div className="p-6 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Default Currency
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                      <option value="USD">USD - US Dollar</option>
                      <option value="EUR">EUR - Euro</option>
                      <option value="GBP">GBP - British Pound</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Tax Rate (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      step="0.01"
                      defaultValue="0"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded" defaultChecked />
                      <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                        Enable promotional pricing
                      </span>
                    </label>
                  </div>

                  <div>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded" defaultChecked />
                      <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                        Allow plan downgrades
                      </span>
                    </label>
                  </div>
                </div>
              </div>

              {/* Feature Settings */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('subscriptions.settings.features')}</h4>
                </div>
                <div className="p-6 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Free Plan Offer Limit
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="10"
                      defaultValue="3"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Trial Period (days)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="30"
                      defaultValue="7"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>

                  <div>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded" defaultChecked />
                      <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                        Require email verification
                      </span>
                    </label>
                  </div>

                  <div>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded" defaultChecked />
                      <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                        Require wallet verification
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Notification Settings */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('subscriptions.settings.notifications')}</h4>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h5 className="font-medium text-gray-900 dark:text-white">Email Notifications</h5>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input type="checkbox" className="rounded" defaultChecked />
                        <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                          Payment confirmations
                        </span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="rounded" defaultChecked />
                        <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                          Subscription renewals
                        </span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="rounded" />
                        <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                          Plan upgrade suggestions
                        </span>
                      </label>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h5 className="font-medium text-gray-900 dark:text-white">Admin Notifications</h5>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input type="checkbox" className="rounded" defaultChecked />
                        <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                          New subscriptions
                        </span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="rounded" defaultChecked />
                        <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                          Payment failures
                        </span>
                      </label>
                      <label className="flex items-center">
                        <input type="checkbox" className="rounded" />
                        <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                          Subscription cancellations
                        </span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Save Settings */}
            <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <button className="flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
                <Settings className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                {t('settings.actions.save')}
              </button>
              <button className="flex items-center px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors">
                <RotateCcw className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                {t('settings.actions.reset')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
