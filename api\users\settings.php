<?php
/**
 * API endpoint لإدارة إعدادات المستخدم
 * User Settings API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // جلب إعدادات المستخدم
        $userId = $_GET['user_id'] ?? null;
        
        if (!$userId) {
            throw new Exception('معرف المستخدم مطلوب');
        }
        
        // جلب بيانات المستخدم الأساسية
        $stmt = $connection->prepare("
            SELECT 
                id, username, email, full_name, phone, bio,
                telegram_username, whatsapp_number, country_code,
                is_verified, profile_image, language_preference,
                theme_preference, notification_preferences,
                privacy_settings, security_settings
            FROM users 
            WHERE id = ? AND is_active = 1
        ");
        
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            throw new Exception('المستخدم غير موجود');
        }
        
        // تنسيق البيانات
        $settings = [
            'profile' => [
                'username' => $user['username'],
                'email' => $user['email'],
                'full_name' => $user['full_name'],
                'phone' => $user['phone'],
                'bio' => $user['bio'],
                'telegram_username' => $user['telegram_username'],
                'whatsapp_number' => $user['whatsapp_number'],
                'country_code' => $user['country_code'],
                'profile_image' => $user['profile_image']
            ],
            'preferences' => [
                'language' => $user['language_preference'] ?: 'ar',
                'theme' => $user['theme_preference'] ?: 'light',
                'notifications' => json_decode($user['notification_preferences'], true) ?: [
                    'email_notifications' => true,
                    'sms_notifications' => false,
                    'push_notifications' => true,
                    'trade_updates' => true,
                    'price_alerts' => true,
                    'marketing_emails' => false
                ]
            ],
            'privacy' => json_decode($user['privacy_settings'], true) ?: [
                'show_online_status' => true,
                'show_trade_history' => false,
                'allow_direct_messages' => true,
                'show_profile_to_public' => true
            ],
            'security' => json_decode($user['security_settings'], true) ?: [
                'two_factor_enabled' => false,
                'login_notifications' => true,
                'session_timeout' => 30,
                'require_password_for_trades' => false
            ],
            'verification' => [
                'is_verified' => (bool)$user['is_verified'],
                'verification_level' => $user['is_verified'] ? 'verified' : 'unverified'
            ]
        ];
        
        echo json_encode([
            'success' => true,
            'data' => $settings
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // تحديث إعدادات المستخدم
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $userId = $input['user_id'] ?? null;
        
        if (!$userId) {
            throw new Exception('معرف المستخدم مطلوب');
        }
        
        // التحقق من وجود المستخدم
        $stmt = $connection->prepare("SELECT id FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$userId]);
        
        if (!$stmt->fetch()) {
            throw new Exception('المستخدم غير موجود');
        }
        
        $updateFields = [];
        $updateValues = [];
        
        // تحديث بيانات الملف الشخصي
        if (isset($input['profile'])) {
            $profile = $input['profile'];
            $profileFields = ['full_name', 'phone', 'bio', 'telegram_username', 'whatsapp_number', 'country_code'];
            
            foreach ($profileFields as $field) {
                if (isset($profile[$field])) {
                    $updateFields[] = "$field = ?";
                    $updateValues[] = $profile[$field];
                }
            }
        }
        
        // تحديث التفضيلات
        if (isset($input['preferences'])) {
            $preferences = $input['preferences'];
            
            if (isset($preferences['language'])) {
                $updateFields[] = "language_preference = ?";
                $updateValues[] = $preferences['language'];
            }
            
            if (isset($preferences['theme'])) {
                $updateFields[] = "theme_preference = ?";
                $updateValues[] = $preferences['theme'];
            }
            
            if (isset($preferences['notifications'])) {
                $updateFields[] = "notification_preferences = ?";
                $updateValues[] = json_encode($preferences['notifications']);
            }
        }
        
        // تحديث إعدادات الخصوصية
        if (isset($input['privacy'])) {
            $updateFields[] = "privacy_settings = ?";
            $updateValues[] = json_encode($input['privacy']);
        }
        
        // تحديث إعدادات الأمان
        if (isset($input['security'])) {
            $updateFields[] = "security_settings = ?";
            $updateValues[] = json_encode($input['security']);
        }
        
        if (empty($updateFields)) {
            throw new Exception('لا توجد بيانات للتحديث');
        }
        
        $updateValues[] = $userId;
        
        $stmt = $connection->prepare("
            UPDATE users 
            SET " . implode(', ', $updateFields) . ", updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        ");
        
        $stmt->execute($updateValues);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث الإعدادات بنجاح'
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    error_log('Database error in users/settings.php: ' . $e->getMessage());
}
?>
