<?php
/**
 * API endpoint لإرسال الإشعارات الذكية
 * Smart Notifications Sending API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        // التحقق من البيانات المطلوبة
        $requiredFields = ['type', 'title', 'message'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                throw new Exception("الحقل $field مطلوب");
            }
        }
        
        // التحقق من صحة النوع
        $validTypes = ['trade', 'security', 'system', 'payment', 'kyc', 'admin', 'contract'];
        if (!in_array($input['type'], $validTypes)) {
            throw new Exception('نوع الإشعار غير صحيح');
        }
        
        // التحقق من صحة الأولوية
        $validPriorities = ['low', 'medium', 'high', 'urgent'];
        $priority = $input['priority'] ?? 'medium';
        if (!in_array($priority, $validPriorities)) {
            $priority = 'medium';
        }
        
        // إعداد البيانات
        $userId = $input['user_id'] ?? null;
        $tradeId = $input['trade_id'] ?? null;
        $contractEventType = $input['contract_event_type'] ?? null;
        $actionUrl = $input['action_url'] ?? null;
        $actionLabel = $input['action_label'] ?? null;
        $metadata = $input['metadata'] ?? [];
        
        // إعداد البيانات الإضافية
        $data = [
            'priority' => $priority,
            'timestamp' => date('Y-m-d H:i:s'),
            'tradeId' => $tradeId,
            'contractEventType' => $contractEventType,
            'actionLabel' => $actionLabel,
            'metadata' => $metadata
        ];
        
        // إذا كان الإشعار لمستخدم محدد
        if ($userId) {
            // التحقق من وجود المستخدم
            $stmt = $connection->prepare("SELECT id FROM users WHERE id = ? AND is_active = 1");
            $stmt->execute([$userId]);
            
            if (!$stmt->fetch()) {
                throw new Exception('المستخدم غير موجود');
            }
            
            // جلب إعدادات المستخدم
            $prefsStmt = $connection->prepare("
                SELECT setting_value 
                FROM system_settings 
                WHERE setting_key = 'notification_preferences' AND updated_by = ?
            ");
            $prefsStmt->execute([$userId]);
            $prefsResult = $prefsStmt->fetch(PDO::FETCH_ASSOC);
            
            $userPrefs = null;
            if ($prefsResult) {
                $userPrefs = json_decode($prefsResult['setting_value'], true);
            }
            
            // التحقق من إعدادات المستخدم
            if ($userPrefs && !shouldSendNotification($input['type'], $priority, $userPrefs)) {
                echo json_encode([
                    'success' => true,
                    'message' => 'تم تجاهل الإشعار بناءً على إعدادات المستخدم',
                    'skipped' => true
                ]);
                exit();
            }
            
            // إدراج الإشعار
            $stmt = $connection->prepare("
                INSERT INTO notifications (
                    user_id, type, title, message, data, 
                    action_url, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ");
            
            $stmt->execute([
                $userId,
                $input['type'],
                $input['title'],
                $input['message'],
                json_encode($data),
                $actionUrl
            ]);
            
            $notificationId = $connection->lastInsertId();
            
            // إرسال إشعار البريد الإلكتروني إذا كان مفعلاً
            if ($userPrefs && $userPrefs['emailNotifications'] && shouldSendEmailNotification($priority)) {
                sendEmailNotification($userId, $input, $connection);
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'تم إرسال الإشعار بنجاح',
                'data' => ['id' => $notificationId]
            ]);
            
        } else {
            // إشعار لجميع المستخدمين (إشعار النظام)
            if ($input['type'] !== 'system' && $input['type'] !== 'admin') {
                throw new Exception('الإشعارات العامة مسموحة فقط لأنواع النظام والإدارة');
            }
            
            // جلب جميع المستخدمين النشطين
            $usersStmt = $connection->prepare("
                SELECT id, email 
                FROM users 
                WHERE is_active = 1
            ");
            $usersStmt->execute();
            $users = $usersStmt->fetchAll(PDO::FETCH_ASSOC);
            
            $sentCount = 0;
            $skippedCount = 0;
            
            foreach ($users as $user) {
                // جلب إعدادات المستخدم
                $prefsStmt = $connection->prepare("
                    SELECT setting_value 
                    FROM system_settings 
                    WHERE setting_key = 'notification_preferences' AND updated_by = ?
                ");
                $prefsStmt->execute([$user['id']]);
                $prefsResult = $prefsStmt->fetch(PDO::FETCH_ASSOC);
                
                $userPrefs = null;
                if ($prefsResult) {
                    $userPrefs = json_decode($prefsResult['setting_value'], true);
                }
                
                // التحقق من إعدادات المستخدم
                if ($userPrefs && !shouldSendNotification($input['type'], $priority, $userPrefs)) {
                    $skippedCount++;
                    continue;
                }
                
                // إدراج الإشعار
                $stmt = $connection->prepare("
                    INSERT INTO notifications (
                        user_id, type, title, message, data, 
                        action_url, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ");
                
                $stmt->execute([
                    $user['id'],
                    $input['type'],
                    $input['title'],
                    $input['message'],
                    json_encode($data),
                    $actionUrl
                ]);
                
                $sentCount++;
                
                // إرسال إشعار البريد الإلكتروني إذا كان مفعلاً
                if ($userPrefs && $userPrefs['emailNotifications'] && shouldSendEmailNotification($priority)) {
                    sendEmailNotification($user['id'], $input, $connection);
                }
            }
            
            echo json_encode([
                'success' => true,
                'message' => "تم إرسال الإشعار إلى {$sentCount} مستخدم، تم تجاهل {$skippedCount} مستخدم",
                'data' => [
                    'sent_count' => $sentCount,
                    'skipped_count' => $skippedCount,
                    'total_users' => count($users)
                ]
            ]);
        }
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
}

/**
 * التحقق من إمكانية إرسال الإشعار بناءً على إعدادات المستخدم
 */
function shouldSendNotification($type, $priority, $userPrefs) {
    // التحقق من الساعات الهادئة
    if ($userPrefs['quietHours']['enabled'] && isQuietHour($userPrefs['quietHours'])) {
        // السماح بالإشعارات الحرجة فقط خلال الساعات الهادئة
        if ($priority !== 'urgent') {
            return false;
        }
    }
    
    // التحقق من إعدادات النوع
    switch ($type) {
        case 'trade':
            return $userPrefs['tradeNotifications'] ?? true;
        case 'security':
            return $userPrefs['securityNotifications'] ?? true;
        case 'system':
            return $userPrefs['systemNotifications'] ?? true;
        case 'contract':
            return $userPrefs['contractNotifications'] ?? true;
        default:
            return true;
    }
}

/**
 * التحقق من الساعات الهادئة
 */
function isQuietHour($quietHours) {
    $now = new DateTime();
    $currentTime = $now->format('H:i');
    
    return $currentTime >= $quietHours['startTime'] && $currentTime <= $quietHours['endTime'];
}

/**
 * التحقق من إرسال إشعار البريد الإلكتروني
 */
function shouldSendEmailNotification($priority) {
    // إرسال البريد الإلكتروني للأولويات العالية والحرجة فقط
    return in_array($priority, ['high', 'urgent']);
}

/**
 * إرسال إشعار البريد الإلكتروني
 */
function sendEmailNotification($userId, $notificationData, $connection) {
    try {
        // جلب بيانات المستخدم
        $stmt = $connection->prepare("SELECT email, full_name FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user || !$user['email']) {
            return false;
        }
        
        // TODO: تنفيذ إرسال البريد الإلكتروني الفعلي
        // يمكن استخدام PHPMailer أو خدمة بريد إلكتروني خارجية
        
        // حفظ سجل محاولة الإرسال
        $logStmt = $connection->prepare("
            INSERT INTO activity_logs (user_id, action, entity_type, data, created_at)
            VALUES (?, 'email_notification_sent', 'notification', ?, CURRENT_TIMESTAMP)
        ");
        
        $logStmt->execute([
            $userId,
            json_encode([
                'email' => $user['email'],
                'title' => $notificationData['title'],
                'type' => $notificationData['type'],
                'priority' => $notificationData['priority'] ?? 'medium'
            ])
        ]);
        
        return true;
        
    } catch (Exception $e) {
        error_log("خطأ في إرسال البريد الإلكتروني: " . $e->getMessage());
        return false;
    }
}
?>
