import { useTranslation } from './useTranslation';
import { useState, useEffect } from 'react';

interface CreateOfferTranslations {
  [key: string]: any;
}

export function useCreateOfferTranslation() {
  const { language } = useTranslation();
  const [createOfferTranslations, setCreateOfferTranslations] = useState<CreateOfferTranslations>({});
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const loadTranslations = async () => {
      try {
        const response = await fetch(`/locales/${language}/create-offer.json`);
        if (response.ok) {
          const translations = await response.json();
          setCreateOfferTranslations(translations);
        }
      } catch (error) {
        console.warn('Failed to load create-offer translations:', error);
      } finally {
        setIsLoaded(true);
      }
    };

    if (language) {
      loadTranslations();
    }
  }, [language]);

  const t = (key: string, params?: Record<string, any>): string => {
    try {
      // تقسيم المفتاح للوصول للقيم المتداخلة
      const keys = key.split('.');
      let value: any = createOfferTranslations;

      for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
          value = value[k];
        } else {
          // إذا لم توجد الترجمة، أرجع المفتاح
          return key;
        }
      }

      // إذا كانت القيمة نص، قم بتطبيق المعاملات
      if (typeof value === 'string') {
        if (params) {
          return value.replace(/\{\{(\w+)\}\}/g, (match: string, paramKey: string) => {
            return params[paramKey] || match;
          });
        }
        return value;
      }

      return key;

    } catch (error) {
      // في حالة الخطأ، أرجع المفتاح
      return key;
    }
  };

  return {
    t,
    language,
    isLoaded
  };
}
