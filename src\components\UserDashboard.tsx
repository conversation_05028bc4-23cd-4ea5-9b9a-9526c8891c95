'use client';

import { useState, useEffect } from 'react';
import {
  BarChart3,
  DollarSign,
  Settings,
  Activity,
  TrendingUp,
  Filter,
  Bell,
  Wallet,
  Star,
  HelpCircle,
  User,
  CreditCard,
  ShoppingCart,
  RefreshCw,
  Target
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { useUserStats } from '@/hooks/user-dashboard/useUserStats';
import { walletService } from '@/services/walletService';
import { notificationService } from '@/services/notificationService';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import LoadingSpinner from '@/components/LoadingSpinner';
import UnifiedWalletManager from '@/components/UnifiedWalletManager';
import dynamic from 'next/dynamic';
import ActiveTradesWidget from '@/components/user-dashboard/ActiveTradesWidget';
import WalletOverviewWidget from '@/components/user-dashboard/WalletOverviewWidget';
import QuickActions from '@/components/user-dashboard/QuickActions';


// استيراد ديناميكي للمكونات لتجنب مشاكل التحميل
const UserTradesTab = dynamic(() => import('@/components/user-dashboard/UserTradesTab'), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center p-8"><div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div></div>
});

const UserOffersTab = dynamic(() => import('@/components/user-dashboard/UserOffersTab'), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center p-8"><div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div></div>
});

const UserWalletTab = dynamic(() => import('@/components/user-dashboard/UserWalletTab'), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center p-8"><div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div></div>
});

const UserProfileTab = dynamic(() => import('@/components/user-dashboard/UserProfileTab'), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center p-8"><div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div></div>
});

const UserNotificationsTab = dynamic(() => import('@/components/user-dashboard/UserNotificationsTab'), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center p-8"><div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div></div>
});

const UserSettingsTab = dynamic(() => import('@/components/user-dashboard/UserSettingsTab'), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center p-8"><div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div></div>
});

const UserHelpTab = dynamic(() => import('@/components/user-dashboard/UserHelpTab'), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center p-8"><div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div></div>
});

const UserReviewsTab = dynamic(() => import('@/components/user-dashboard/UserReviewsTab'), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center p-8"><div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div></div>
});

const UserTransactionsTab = dynamic(() => import('@/components/user-dashboard/UserTransactionsTab'), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center p-8"><div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div></div>
});

export default function UserDashboard() {
  const { t, formatDate } = useUserDashboardTranslation();
  const { user, isAuthenticated } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [isWalletConnected, setIsWalletConnected] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [timeFilter, setTimeFilter] = useState<'week' | 'month' | 'quarter' | 'year'>('month');

  const {
    stats,
    loading,
    refresh,
    updateFilters
  } = useUserStats({
    autoRefresh: true,
    refreshInterval: 5 * 60 * 1000, // 5 دقائق
    filters: { period: timeFilter }
  });

  // التحقق من حالة المحفظة عند التحميل
  useEffect(() => {
    const checkWalletStatus = async () => {
      try {
        const walletConnected = await walletService.isWalletConnected();
        setIsWalletConnected(walletConnected);
      } catch (error) {
        console.error('Error checking wallet status:', error);
      }
    };

    if (isAuthenticated) {
      checkWalletStatus();
    }
  }, [isAuthenticated]);

  // تحديث البيانات
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refresh();
    } finally {
      setRefreshing(false);
    }
  };

  // تغيير فلتر الوقت
  const handleTimeFilterChange = (period: typeof timeFilter) => {
    setTimeFilter(period);
    updateFilters({ period });
  };

  // معالجة تغيير حالة الاتصال بالمحفظة
  const handleWalletConnectionChange = (isConnected: boolean, address?: string) => {
    setIsWalletConnected(isConnected);
    if (isConnected && address) {
      notificationService.walletConnected(address);
      // تحديث البيانات عند الاتصال
      refresh();
    } else {
      notificationService.info('تم قطع الاتصال بالمحفظة');
    }
  };

  // معالجة أخطاء المحفظة
  const handleWalletError = (error: string) => {
    notificationService.error(error);
  };

  // إذا لم يسجل دخول
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen">
        <Header />
        <div className="bg-gray-50 dark:bg-gray-900 py-8">
          <div className="container mx-auto px-4">
            <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                تسجيل الدخول مطلوب
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                يجب تسجيل الدخول للوصول إلى لوحة التحكم
              </p>
              <div className="space-y-3">
                <a 
                  href="/login" 
                  className="block w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                >
                  تسجيل الدخول
                </a>
                <a 
                  href="/register" 
                  className="block w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-900 dark:text-white font-medium py-3 px-4 rounded-lg transition-colors"
                >
                  إنشاء حساب جديد
                </a>
                <a 
                  href="/" 
                  className="block w-full text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white font-medium py-2 transition-colors"
                >
                  العودة للرئيسية
                </a>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (loading && !stats) {
    return (
      <div className="min-h-screen">
        <Header />
        <div className="bg-gray-50 dark:bg-gray-900 flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Header />
      <div className="bg-gray-50 dark:bg-gray-900 py-8">
        <div className="container-custom">
          {/* العنوان */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center">
                  <User className="w-8 h-8 ml-3 text-blue-600" />
                  {t('dashboard.welcome')}، {user?.fullName || user?.username || 'مستخدم'}
                  <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs font-medium px-2.5 py-0.5 rounded-full mr-3">
                    لوحة التحكم
                  </span>
                </h1>
                <p className="text-gray-600 dark:text-gray-300">{t('dashboard.subtitle')}</p>
              </div>
              <div className="text-left">
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  آخر تحديث
                </div>
                <div className="font-medium text-gray-900 dark:text-white">
                  {formatDate(new Date(), { 
                    hour: '2-digit', 
                    minute: '2-digit',
                    day: 'numeric',
                    month: 'short'
                  })}
                </div>
                {user?.walletAddress && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                    {user.walletAddress?.slice(0, 6) || ''}...{user.walletAddress?.slice(-4) || ''}
                  </div>
                )}
              </div>
            </div>
          </div>



          {/* إدارة المحفظة */}
          <div className="mb-8">
            <UnifiedWalletManager
              variant="card"
              showBalance={true}
              showNetwork={true}
              showActions={true}
              showConnectionOptions={true}
              onConnectionChange={handleWalletConnectionChange}
              onError={handleWalletError}
              className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20"
            />
          </div>

          {/* الإحصائيات السريعة */}
          {stats && (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الصفقات</p>
                    <p className="text-xl font-bold text-gray-900 dark:text-white">{(stats?.totalTrades || 0).toLocaleString()}</p>
                  </div>
                  <BarChart3 className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">حجم التداول</p>
                    <p className="text-xl font-bold text-gray-900 dark:text-white">
                      {(() => {
                        const volume = parseFloat(stats?.totalVolume || '0') || 0;
                        return volume >= 1000
                          ? `$${(volume / 1000).toFixed(1)}K`
                          : `$${volume.toFixed(0)}`;
                      })()}
                    </p>
                  </div>
                  <DollarSign className="w-8 h-8 text-green-600 dark:text-green-400" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">التقييم</p>
                    <p className="text-xl font-bold text-gray-900 dark:text-white">
                      {(Number(stats?.rating) || 0).toFixed(1)}
                      <span className="text-sm text-gray-500 mr-1">⭐</span>
                    </p>
                  </div>
                  <Star className="w-8 h-8 text-yellow-600 dark:text-yellow-400" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">معدل الإنجاز</p>
                    <p className="text-xl font-bold text-gray-900 dark:text-white">{(stats?.completionRate || 0)}%</p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-purple-600 dark:text-purple-400" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">العروض النشطة</p>
                    <p className="text-xl font-bold text-gray-900 dark:text-white">{(stats?.activeOffers || 0).toLocaleString()}</p>
                  </div>
                  <Activity className="w-8 h-8 text-orange-600 dark:text-orange-400" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">متوسط الصفقة</p>
                    <p className="text-xl font-bold text-gray-900 dark:text-white">
                      {(() => {
                        const avgAmount = parseFloat(stats?.averageTradeAmount || '0') || 0;
                        return avgAmount >= 1000
                          ? `$${(avgAmount / 1000).toFixed(1)}K`
                          : `$${avgAmount.toFixed(0)}`;
                      })()}
                    </p>
                  </div>
                  <Target className="w-8 h-8 text-indigo-600 dark:text-indigo-400" />
                </div>
              </div>
            </div>
          )}

          {/* التبويبات المحسنة */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
            <div className="border-b border-gray-200 dark:border-gray-700">
              {/* التبويبات الرئيسية */}
              <div className="px-6 py-2">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">لوحة التحكم</h3>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <span className="text-sm text-gray-500 dark:text-gray-400">التبويب النشط:</span>
                    <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-md text-sm font-medium">
                      {[
                        { id: 'overview', label: 'نظرة عامة' },
                        { id: 'trades', label: 'الصفقات' },
                        { id: 'offers', label: 'العروض' },
                        { id: 'wallet', label: 'المحفظة' },
                        { id: 'transactions', label: 'المعاملات' },
                        { id: 'profile', label: 'الملف الشخصي' },
                        { id: 'reviews', label: 'التقييمات' },
                        { id: 'notifications', label: 'الإشعارات' },
                        { id: 'settings', label: 'الإعدادات' },
                        { id: 'help', label: 'المساعدة' }
                      ].find(tab => tab.id === activeTab)?.label}
                    </span>
                  </div>
                </div>

                {/* مجموعات التبويبات */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {/* مجموعة التداول */}
                  <div className="space-y-2">
                    <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">التداول</h4>
                    <div className="space-y-1">
                      {[
                        { id: 'overview', label: 'نظرة عامة', icon: BarChart3 },
                        { id: 'trades', label: 'الصفقات', icon: Activity },
                        { id: 'offers', label: 'العروض', icon: ShoppingCart },
                        { id: 'wallet', label: 'المحفظة', icon: Wallet },
                        { id: 'transactions', label: 'المعاملات', icon: CreditCard }
                      ].map((tab) => (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`w-full flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                            activeTab === tab.id
                              ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 shadow-sm'
                              : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-200'
                          }`}
                        >
                          <tab.icon className="w-4 h-4 ml-2 flex-shrink-0" />
                          <span className="truncate">{tab.label}</span>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* مجموعة الحساب */}
                  <div className="space-y-2">
                    <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الحساب</h4>
                    <div className="space-y-1">
                      {[
                        { id: 'profile', label: 'الملف الشخصي', icon: User },
                        { id: 'reviews', label: 'التقييمات', icon: Star },
                        { id: 'notifications', label: 'الإشعارات', icon: Bell }
                      ].map((tab) => (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`w-full flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                            activeTab === tab.id
                              ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 shadow-sm'
                              : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-200'
                          }`}
                        >
                          <tab.icon className="w-4 h-4 ml-2 flex-shrink-0" />
                          <span className="truncate">{tab.label}</span>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* مجموعة الدعم */}
                  <div className="space-y-2">
                    <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الدعم</h4>
                    <div className="space-y-1">
                      {[
                        { id: 'settings', label: 'الإعدادات', icon: Settings },
                        { id: 'help', label: 'المساعدة', icon: HelpCircle }
                      ].map((tab) => (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`w-full flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                            activeTab === tab.id
                              ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 shadow-sm'
                              : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-200'
                          }`}
                        >
                          <tab.icon className="w-4 h-4 ml-2 flex-shrink-0" />
                          <span className="truncate">{tab.label}</span>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="p-6">
              {/* تبويب النظرة العامة */}
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900 dark:text-white">نظرة عامة</h2>
                      <p className="text-gray-600 dark:text-gray-400 mt-1">ملخص نشاطك على المنصة</p>
                    </div>

                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      {/* فلتر الوقت */}
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Filter className="w-4 h-4 text-gray-500" />
                        <select
                          value={timeFilter}
                          onChange={(e) => handleTimeFilterChange(e.target.value as typeof timeFilter)}
                          className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="week">هذا الأسبوع</option>
                          <option value="month">هذا الشهر</option>
                          <option value="quarter">هذا الربع</option>
                          <option value="year">هذا العام</option>
                        </select>
                      </div>

                      {/* زر التحديث */}
                      <button
                        onClick={handleRefresh}
                        disabled={refreshing}
                        className="flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors disabled:opacity-50"
                      >
                        <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                        <span className="text-sm">تحديث</span>
                      </button>
                    </div>
                  </div>

                  {/* الشبكة الرئيسية */}
                  <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 md:gap-6">
                    {/* الصفقات النشطة */}
                    <div className="xl:col-span-2 order-2 xl:order-1">
                      <ActiveTradesWidget />
                    </div>

                    {/* الإجراءات السريعة */}
                    <div className="xl:col-span-1 order-1 xl:order-2">
                      <QuickActions />
                    </div>
                  </div>

                  {/* نظرة عامة على المحفظة */}
                  <WalletOverviewWidget />
                </div>
              )}

              {/* تبويب الصفقات */}
              {activeTab === 'trades' && <UserTradesTab />}

              {/* تبويب العروض */}
              {activeTab === 'offers' && <UserOffersTab />}

              {/* تبويب المحفظة */}
              {activeTab === 'wallet' && <UserWalletTab />}

              {/* تبويب المعاملات */}
              {activeTab === 'transactions' && <UserTransactionsTab />}

              {/* تبويب الملف الشخصي */}
              {activeTab === 'profile' && <UserProfileTab />}

              {/* تبويب التقييمات */}
              {activeTab === 'reviews' && <UserReviewsTab />}

              {/* تبويب الإشعارات */}
              {activeTab === 'notifications' && <UserNotificationsTab />}

              {/* تبويب الإعدادات */}
              {activeTab === 'settings' && <UserSettingsTab />}

              {/* تبويب المساعدة */}
              {activeTab === 'help' && <UserHelpTab />}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
