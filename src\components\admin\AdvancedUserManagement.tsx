'use client';

import React, { useState } from 'react';
import {
  Users,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Ban,
  CheckCircle,
  Shield,
  Mail,
  Phone,
  Calendar,
  TrendingUp,
  AlertTriangle,
  MoreVertical,
  UserCheck,
  UserX,
  MessageSquare,
  FileText,
  Clock,
  MapPin,
  Star,
  Award,
  Activity,
  Settings,
  RefreshCw,
  Upload,
  X,
  Check
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';

interface User {
  id: string;
  fullName: string;
  email: string;
  phone?: string;
  isVerified: boolean;
  isOnline: boolean;
  joinedAt: string;
  totalTrades: number;
  rating: number;
  lastActivity: string;
  status: 'active' | 'suspended' | 'pending' | 'blocked' | 'banned';
  kycStatus: 'unverified' | 'pending' | 'approved' | 'rejected';
  kycLevel: 0 | 1 | 2 | 3;
  isVip: boolean;
  isPremium: boolean;
  avatar?: string;
  country?: string;
  totalVolume: number;
  successRate: number;
  riskScore: number;
}

interface AdvancedUserManagementProps {
  className?: string;
}

export default function AdvancedUserManagement({ className = '' }: AdvancedUserManagementProps) {
  const { t, language, isRTL, getDirectionClasses, formatNumber, formatCurrency, formatDate } = useAdminTranslation();
  const dirClasses = getDirectionClasses();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showUserDetails, setShowUserDetails] = useState(false);

  // Mock data - replace with real data
  const users: User[] = [
    {
      id: '1',
      fullName: 'أحمد محمد علي',
      email: '<EMAIL>',
      phone: '+966501234567',
      isVerified: true,
      isOnline: true,
      joinedAt: '2024-01-15',
      totalTrades: 45,
      rating: 4.8,
      lastActivity: '2024-01-20T10:30:00Z',
      status: 'active',
      kycStatus: 'approved',
      kycLevel: 2,
      isVip: true,
      isPremium: false,
      country: 'SA',
      totalVolume: 125000,
      successRate: 98.5,
      riskScore: 15
    },
    {
      id: '2',
      fullName: 'فاطمة أحمد',
      email: '<EMAIL>',
      phone: '+966507654321',
      isVerified: false,
      isOnline: false,
      joinedAt: '2024-01-18',
      totalTrades: 12,
      rating: 4.2,
      lastActivity: '2024-01-19T15:45:00Z',
      status: 'pending',
      kycStatus: 'pending',
      kycLevel: 1,
      isVip: false,
      isPremium: false,
      country: 'AE',
      totalVolume: 25000,
      successRate: 95.0,
      riskScore: 25
    },
    {
      id: '3',
      fullName: 'محمد عبدالله',
      email: '<EMAIL>',
      isVerified: true,
      isOnline: false,
      joinedAt: '2024-01-10',
      totalTrades: 78,
      rating: 4.9,
      lastActivity: '2024-01-20T08:15:00Z',
      status: 'active',
      kycStatus: 'approved',
      kycLevel: 3,
      isVip: false,
      isPremium: true,
      country: 'EG',
      totalVolume: 350000,
      successRate: 99.2,
      riskScore: 8
    }
  ];

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = selectedFilter === 'all' ||
                         (selectedFilter === 'verified' && user.isVerified) ||
                         (selectedFilter === 'unverified' && !user.isVerified) ||
                         (selectedFilter === 'active' && user.status === 'active') ||
                         (selectedFilter === 'suspended' && user.status === 'suspended') ||
                         (selectedFilter === 'pending' && user.status === 'pending') ||
                         (selectedFilter === 'vipUsers' && user.isVip) ||
                         (selectedFilter === 'kycPending' && user.kycStatus === 'pending') ||
                         (selectedFilter === 'kycApproved' && user.kycStatus === 'approved') ||
                         (selectedFilter === 'highVolume' && user.totalVolume > 100000);
    
    return matchesSearch && matchesFilter;
  });

  const stats = {
    total: users.length,
    verified: users.filter(u => u.isVerified).length,
    active: users.filter(u => u.status === 'active').length,
    vip: users.filter(u => u.isVip).length,
    kycPending: users.filter(u => u.kycStatus === 'pending').length,
    online: users.filter(u => u.isOnline).length,
    newToday: users.filter(u => {
      const today = new Date().toDateString();
      return new Date(u.joinedAt).toDateString() === today;
    }).length,
    highRisk: users.filter(u => u.riskScore > 50).length
  };

  const handleUserAction = (userId: string, action: string) => {
    console.log(`Action ${action} for user ${userId}`);
    // Implement user actions
  };

  const handleBulkAction = (action: string) => {
    console.log(`Bulk action ${action} for users:`, selectedUsers);
    setShowBulkActions(false);
    setSelectedUsers([]);
  };

  const StatusBadge = ({ status, kycStatus, isVip, isPremium }: { 
    status: string; 
    kycStatus: string; 
    isVip: boolean; 
    isPremium: boolean; 
  }) => {
    if (isVip) {
      return (
        <div className="flex items-center gap-1">
          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
            <Star className="w-3 h-3 mr-1" />
            VIP
          </span>
        </div>
      );
    }
    
    if (isPremium) {
      return (
        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400">
          <Award className="w-3 h-3 mr-1" />
          {t('users.status.premium')}
        </span>
      );
    }

    if (kycStatus === 'approved' && status === 'active') {
      return (
        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
          <Shield className="w-3 h-3 mr-1" />
          {t('users.status.verified')}
        </span>
      );
    }
    
    if (status === 'suspended') {
      return (
        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">
          <Ban className="w-3 h-3 mr-1" />
          {t('users.status.suspended')}
        </span>
      );
    }
    
    return (
      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
        <Clock className="w-3 h-3 mr-1" />
        {t('users.status.pending')}
      </span>
    );
  };

  const KycLevelBadge = ({ level }: { level: number }) => {
    const colors = ['gray', 'blue', 'green', 'purple'];
    const color = colors[level] || 'gray';
    
    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-${color}-100 text-${color}-800 dark:bg-${color}-900/30 dark:text-${color}-400`}>
        {t(`users.kyc.levels.level${level}`)}
      </span>
    );
  };

  const RiskScoreBadge = ({ score }: { score: number }) => {
    let color = 'green';
    let icon = CheckCircle;
    
    if (score > 50) {
      color = 'red';
      icon = AlertTriangle;
    } else if (score > 25) {
      color = 'yellow';
      icon = AlertTriangle;
    }
    
    const Icon = icon;
    
    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-${color}-100 text-${color}-800 dark:bg-${color}-900/30 dark:text-${color}-400`}>
        <Icon className="w-3 h-3 mr-1" />
        {score}%
      </span>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className={`flex flex-col sm:flex-row sm:items-center justify-between gap-4 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
        <div className={isRTL ? 'text-right' : 'text-left'}>
          <h3 className={`text-xl font-bold text-gray-900 dark:text-white flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Users className={`w-6 h-6 text-blue-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            {t('users.title')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('users.subtitle')}</p>
        </div>
        <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button 
            onClick={() => handleBulkAction('export')}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            <Download className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.export')}
          </button>
          <button className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
            <Plus className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.add')} {t('navigation.users')}
          </button>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-8 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('users.totalUsers')}</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatNumber(stats.total)}</p>
            </div>
            <Users className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('users.verifiedUsers')}</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">{formatNumber(stats.verified)}</p>
            </div>
            <Shield className="w-8 h-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('users.onlineUsers')}</p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{formatNumber(stats.online)}</p>
            </div>
            <Activity className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">VIP</p>
              <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{formatNumber(stats.vip)}</p>
            </div>
            <Star className="w-8 h-8 text-yellow-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">KYC {t('users.status.pending')}</p>
              <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">{formatNumber(stats.kycPending)}</p>
            </div>
            <FileText className="w-8 h-8 text-orange-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('users.newUsersToday')}</p>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">{formatNumber(stats.newToday)}</p>
            </div>
            <TrendingUp className="w-8 h-8 text-purple-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('common.labels.active')}</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">{formatNumber(stats.active)}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">High Risk</p>
              <p className="text-2xl font-bold text-red-600 dark:text-red-400">{formatNumber(stats.highRisk)}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-500" />
          </div>
        </div>
      </div>

      {/* Advanced Search and Filters */}
      <div className={`flex flex-col lg:flex-row gap-4 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
        <div className="relative flex-1">
          <Search className={`absolute top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 ${isRTL ? 'right-3' : 'left-3'}`} />
          <input
            type="text"
            placeholder={t('users.searchPlaceholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full ${isRTL ? 'pr-10 pl-4 text-right' : 'pl-10 pr-4 text-left'} py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
          />
        </div>
        
        <div className="flex items-center gap-3">
          <select
            value={selectedFilter}
            onChange={(e) => setSelectedFilter(e.target.value)}
            className={`px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
          >
            <option value="all">{t('users.filters.all')}</option>
            <option value="verified">{t('users.filters.verified')}</option>
            <option value="unverified">{t('users.filters.unverified')}</option>
            <option value="active">{t('users.filters.active')}</option>
            <option value="suspended">{t('users.filters.suspended')}</option>
            <option value="vipUsers">{t('users.filters.vipUsers')}</option>
            <option value="kycPending">{t('users.filters.kycPending')}</option>
            <option value="kycApproved">{t('users.filters.kycApproved')}</option>
            <option value="highVolume">{t('users.filters.highVolume')}</option>
          </select>
          
          <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
            <Filter className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.filter')}
          </button>
          
          <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
            <RefreshCw className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.refresh')}
          </button>
        </div>
      </div>

      {/* Advanced Users Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  <input
                    type="checkbox"
                    className="rounded"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedUsers(filteredUsers.map(u => u.id));
                        setShowBulkActions(true);
                      } else {
                        setSelectedUsers([]);
                        setShowBulkActions(false);
                      }
                    }}
                  />
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('users.profile.personalInfo')}
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('users.profile.tradingInfo')}
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  KYC {t('common.labels.status')}
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('common.labels.status')}
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  Risk Score
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('users.activity.lastActivity')}
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('users.actions.view')}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <td className="py-4 px-4">
                    <input
                      type="checkbox"
                      className="rounded"
                      checked={selectedUsers.includes(user.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedUsers([...selectedUsers, user.id]);
                          setShowBulkActions(true);
                        } else {
                          setSelectedUsers(selectedUsers.filter(id => id !== user.id));
                          if (selectedUsers.length === 1) setShowBulkActions(false);
                        }
                      }}
                    />
                  </td>
                  <td className="py-4 px-4">
                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={`w-12 h-12 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center ${isRTL ? 'ml-3' : 'mr-3'} relative`}>
                        <span className="text-blue-700 dark:text-blue-400 font-semibold text-sm">
                          {user.fullName.charAt(0).toUpperCase()}
                        </span>
                        {user.isOnline && (
                          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
                        )}
                      </div>
                      <div className={isRTL ? 'text-right' : 'text-left'}>
                        <div className={`font-medium text-gray-900 dark:text-white flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                          {user.fullName}
                          {user.isVip && <Star className={`w-4 h-4 text-yellow-500 ${isRTL ? 'mr-2' : 'ml-2'}`} />}
                          {user.isPremium && <Award className={`w-4 h-4 text-purple-500 ${isRTL ? 'mr-2' : 'ml-2'}`} />}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">{user.email}</div>
                        {user.country && (
                          <div className={`text-xs text-gray-500 dark:text-gray-400 flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <MapPin className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                            {user.country}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className={isRTL ? 'text-right' : 'text-left'}>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {formatNumber(user.totalTrades)} {t('stats.totalTrades')}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        {formatCurrency(user.totalVolume)} {t('overview.total')}
                      </div>
                      <div className={`text-xs text-gray-500 dark:text-gray-400 flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        ⭐ {user.rating}/5 • {user.successRate}% {t('stats.successRate')}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className="space-y-1">
                      <KycLevelBadge level={user.kycLevel} />
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {t(`users.kyc.status`)}: {t(`users.status.${user.kycStatus}`)}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <StatusBadge
                      status={user.status}
                      kycStatus={user.kycStatus}
                      isVip={user.isVip}
                      isPremium={user.isPremium}
                    />
                  </td>
                  <td className="py-4 px-4">
                    <RiskScoreBadge score={user.riskScore} />
                  </td>
                  <td className="py-4 px-4 text-sm text-gray-600 dark:text-gray-300">
                    <div>{formatDate(user.lastActivity)}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {t('users.profile.personalInfo')}: {formatDate(user.joinedAt)}
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <button
                        onClick={() => {
                          setSelectedUser(user);
                          setShowUserDetails(true);
                        }}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 p-1 rounded transition-colors"
                        title={t('users.actions.viewProfile')}
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleUserAction(user.id, 'edit')}
                        className="text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 p-1 rounded transition-colors"
                        title={t('users.actions.editProfile')}
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleUserAction(user.id, 'message')}
                        className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 p-1 rounded transition-colors"
                        title={t('users.actions.sendMessage')}
                      >
                        <MessageSquare className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleUserAction(user.id, user.status === 'active' ? 'suspend' : 'activate')}
                        className={`p-1 rounded transition-colors ${
                          user.status === 'active'
                            ? 'text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300'
                            : 'text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300'
                        }`}
                        title={user.status === 'active' ? t('users.actions.suspend') : t('users.actions.activate')}
                      >
                        {user.status === 'active' ? <Ban className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
                      </button>
                      <button
                        className="text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 p-1 rounded transition-colors"
                        title={t('common.actions.settings')}
                      >
                        <MoreVertical className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Empty State */}
        {filteredUsers.length === 0 && (
          <div className="text-center py-12">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{t('common.messages.noData')}</h3>
            <p className="text-gray-600 dark:text-gray-400">{t('users.searchPlaceholder')}</p>
          </div>
        )}
      </div>

      {/* Bulk Actions Panel */}
      {showBulkActions && selectedUsers.length > 0 && (
        <div className={`fixed bottom-6 ${isRTL ? 'left-6' : 'right-6'} bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 z-50`}>
          <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {t('users.bulkActions.selectedCount', { count: selectedUsers.length })}
            </span>
            <button
              onClick={() => handleBulkAction('verify')}
              className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors"
            >
              {t('users.bulkActions.verifySelected')}
            </button>
            <button
              onClick={() => handleBulkAction('suspend')}
              className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors"
            >
              {t('users.bulkActions.suspendSelected')}
            </button>
            <button
              onClick={() => handleBulkAction('message')}
              className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors"
            >
              {t('users.bulkActions.sendMessageToSelected')}
            </button>
            <button
              onClick={() => {
                setSelectedUsers([]);
                setShowBulkActions(false);
              }}
              className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition-colors"
            >
              {t('common.actions.cancel')}
            </button>
          </div>
        </div>
      )}

      {/* User Details Modal */}
      {showUserDetails && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className={`flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t('users.profile.title')}: {selectedUser.fullName}
              </h3>
              <button
                onClick={() => setShowUserDetails(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Personal Information */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                    {t('users.profile.personalInfo')}
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.name')}:</span>
                      <span className="text-gray-900 dark:text-white">{selectedUser.fullName}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.email')}:</span>
                      <span className="text-gray-900 dark:text-white">{selectedUser.email}</span>
                    </div>
                    {selectedUser.phone && (
                      <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className="text-gray-600 dark:text-gray-400">{t('common.labels.phone')}:</span>
                        <span className="text-gray-900 dark:text-white">{selectedUser.phone}</span>
                      </div>
                    )}
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.created')}:</span>
                      <span className="text-gray-900 dark:text-white">{formatDate(selectedUser.joinedAt)}</span>
                    </div>
                  </div>
                </div>

                {/* Trading Information */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                    {t('users.profile.tradingInfo')}
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('stats.totalTrades')}:</span>
                      <span className="text-gray-900 dark:text-white">{formatNumber(selectedUser.totalTrades)}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('overview.total')} {t('overview.tradingVolume')}:</span>
                      <span className="text-gray-900 dark:text-white">{formatCurrency(selectedUser.totalVolume)}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.rating')}:</span>
                      <span className="text-gray-900 dark:text-white">⭐ {selectedUser.rating}/5</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('stats.successRate')}:</span>
                      <span className="text-gray-900 dark:text-white">{selectedUser.successRate}%</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className={`flex items-center gap-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <button
                  onClick={() => handleUserAction(selectedUser.id, 'edit')}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  {t('users.actions.editProfile')}
                </button>
                <button
                  onClick={() => handleUserAction(selectedUser.id, 'message')}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                >
                  {t('users.actions.sendMessage')}
                </button>
                <button
                  onClick={() => handleUserAction(selectedUser.id, 'viewTrades')}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  {t('users.actions.viewTrades')}
                </button>
                {selectedUser.kycStatus === 'pending' && (
                  <>
                    <button
                      onClick={() => handleUserAction(selectedUser.id, 'approveKyc')}
                      className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                    >
                      {t('users.kyc.actions.approve')}
                    </button>
                    <button
                      onClick={() => handleUserAction(selectedUser.id, 'rejectKyc')}
                      className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                    >
                      {t('users.kyc.actions.reject')}
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
