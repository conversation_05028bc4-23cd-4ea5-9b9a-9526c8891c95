'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet, apiPost, apiPut, apiDelete, handleApiError } from '@/utils/apiClient';

export interface Offer {
  id: number;
  user_id: number;
  offer_type: 'buy' | 'sell';
  amount: string;
  min_amount?: string;
  max_amount?: string;
  price: string;
  currency: string;
  stablecoin: string;
  payment_methods?: string[];
  terms?: string;
  auto_reply?: string;
  is_active: boolean;
  is_premium: boolean;
  is_free: boolean;
  views_count: number;
  time_limit: number;
  blockchain_trade_id?: number;
  transaction_hash?: string;
  contract_status?: string;
  created_at: string;
  updated_at: string;
  expires_at?: string;
  
  // إحصائيات العرض
  trades_count?: number;
  successful_trades?: number;
  response_rate?: number;
  average_completion_time?: number;
}

export interface OfferFilters {
  status?: 'active' | 'inactive' | 'expired' | 'all';
  type?: 'buy' | 'sell' | 'all';
  currency?: string;
  stablecoin?: string;
  is_premium?: boolean;
  is_free?: boolean;
  period?: 'week' | 'month' | 'quarter' | 'year' | 'all';
}

interface UseUserOffersOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  filters?: OfferFilters;
  pageSize?: number;
}

interface UseUserOffersReturn {
  offers: Offer[];
  activeOffers: Offer[];
  inactiveOffers: Offer[];
  expiredOffers: Offer[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  totalPages: number;
  refresh: () => Promise<void>;
  updateFilters: (filters: OfferFilters) => void;
  loadMore: () => Promise<void>;
  toggleOfferStatus: (offerId: number) => Promise<void>;
  deleteOffer: (offerId: number) => Promise<void>;
  duplicateOffer: (offerId: number) => Promise<void>;
  filters: OfferFilters;
  hasMore: boolean;
}

/**
 * Hook لإدارة عروض المستخدم
 */
export function useUserOffers(options: UseUserOffersOptions = {}): UseUserOffersReturn {
  const { user } = useAuth();
  const [offers, setOffers] = useState<Offer[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<OfferFilters>(options.filters || {});
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  const {
    autoRefresh = false,
    refreshInterval = 2 * 60 * 1000, // دقيقتان للعروض
    pageSize = 20
  } = options;

  /**
   * جلب عروض المستخدم من API
   */
  const fetchOffers = useCallback(async (page = 1, append = false) => {
    if (!user?.id) {
      setError('معرف المستخدم غير متاح');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // بناء معاملات الاستعلام
      const queryParams = new URLSearchParams({
        user_id: user.id.toString(),
        page: page.toString(),
        limit: pageSize.toString(),
        ...filters
      });

      const result = await apiGet(`offers/my-offers.php?${queryParams.toString()}`);

      if (!result.success) {
        throw new Error(result.error || 'فشل في جلب العروض');
      }

      const { offers: newOffers, total, current_page, total_pages } = result.data;

      if (append) {
        setOffers(prev => [...prev, ...newOffers]);
      } else {
        setOffers(newOffers);
      }

      setTotalCount(total);
      setCurrentPage(current_page);
      setHasMore(current_page < total_pages);

    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('Error fetching offers:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.id, filters, pageSize]);

  /**
   * تبديل حالة العرض (تفعيل/إلغاء تفعيل)
   */
  const toggleOfferStatus = useCallback(async (offerId: number) => {
    try {
      const result = await apiPut(`offers/index.php`, {
        offer_id: offerId,
        action: 'toggle_status'
      });

      if (!result.success) {
        throw new Error(result.error || 'فشل في تحديث حالة العرض');
      }

      // تحديث العرض في القائمة المحلية
      setOffers(prev => prev.map(offer => 
        offer.id === offerId 
          ? { ...offer, is_active: !offer.is_active }
          : offer
      ));

    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('Error toggling offer status:', err);
    }
  }, []);

  /**
   * حذف عرض
   */
  const deleteOffer = useCallback(async (offerId: number) => {
    try {
      const result = await apiDelete(`offers/index.php?offer_id=${offerId}`);

      if (!result.success) {
        throw new Error(result.error || 'فشل في حذف العرض');
      }

      // إزالة العرض من القائمة المحلية
      setOffers(prev => prev.filter(offer => offer.id !== offerId));
      setTotalCount(prev => prev - 1);

    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('Error deleting offer:', err);
    }
  }, []);

  /**
   * نسخ عرض
   */
  const duplicateOffer = useCallback(async (offerId: number) => {
    try {
      const result = await apiPost(`offers/index.php`, {
        action: 'duplicate',
        offer_id: offerId
      });

      if (!result.success) {
        throw new Error(result.error || 'فشل في نسخ العرض');
      }

      // إعادة تحميل العروض لإظهار العرض الجديد
      await fetchOffers(1, false);

    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('Error duplicating offer:', err);
    }
  }, [fetchOffers]);

  /**
   * تحديث المرشحات
   */
  const updateFilters = useCallback((newFilters: OfferFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1);
  }, []);

  /**
   * تحميل المزيد من العروض
   */
  const loadMore = useCallback(async () => {
    if (hasMore && !loading) {
      await fetchOffers(currentPage + 1, true);
    }
  }, [hasMore, loading, currentPage, fetchOffers]);

  /**
   * تحديث العروض يدوياً
   */
  const refresh = useCallback(async () => {
    setCurrentPage(1);
    await fetchOffers(1, false);
  }, [fetchOffers]);

  // جلب العروض عند تحميل المكون أو تغيير المرشحات
  useEffect(() => {
    if (user?.id) {
      fetchOffers(1, false);
    }
  }, [fetchOffers]);

  // التحديث التلقائي
  useEffect(() => {
    if (!autoRefresh || !user?.id) return;

    const interval = setInterval(() => {
      fetchOffers(1, false);
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchOffers, user?.id]);

  // تصفية العروض حسب الحالة
  const activeOffers = offers.filter(offer => offer.is_active);
  const inactiveOffers = offers.filter(offer => !offer.is_active && !isOfferExpired(offer));
  const expiredOffers = offers.filter(offer => isOfferExpired(offer));

  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    offers,
    activeOffers,
    inactiveOffers,
    expiredOffers,
    loading,
    error,
    totalCount,
    currentPage,
    totalPages,
    refresh,
    updateFilters,
    loadMore,
    toggleOfferStatus,
    deleteOffer,
    duplicateOffer,
    filters,
    hasMore
  };
}

/**
 * Hook للحصول على عرض واحد بالتفصيل
 */
export function useOfferDetails(offerId: number) {
  const [offer, setOffer] = useState<Offer | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchOfferDetails = useCallback(async () => {
    if (!offerId) return;

    try {
      setLoading(true);
      setError(null);

      const result = await apiGet(`offers/index.php?offer_id=${offerId}`);

      if (!result.success) {
        throw new Error(result.error || 'فشل في جلب تفاصيل العرض');
      }

      setOffer(result.data);
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('Error fetching offer details:', err);
    } finally {
      setLoading(false);
    }
  }, [offerId]);

  useEffect(() => {
    fetchOfferDetails();
  }, [fetchOfferDetails]);

  return {
    offer,
    loading,
    error,
    refresh: fetchOfferDetails
  };
}

/**
 * دالة مساعدة للتحقق من انتهاء صلاحية العرض
 */
function isOfferExpired(offer: Offer): boolean {
  if (!offer.expires_at) return false;
  return new Date(offer.expires_at) < new Date();
}

export default useUserOffers;
