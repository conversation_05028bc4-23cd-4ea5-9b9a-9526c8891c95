<?php
/**
 * إعدادات CORS المركزية لمنصة إيكاروس P2P
 * Centralized CORS Configuration for Ikaros P2P Platform
 */

/**
 * تطبيق إعدادات CORS
 */
function applyCorsHeaders() {
    // قراءة المصادر المسموحة من متغيرات البيئة
    $envOrigins = $_ENV['CORS_ALLOWED_ORIGINS'] ?? '';
    $envOriginsArray = !empty($envOrigins) ? explode(',', $envOrigins) : [];

    // المصادر الافتراضية للتطوير
    $defaultOrigins = [
        'http://localhost:3000',
        'http://localhost:3001',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:3001',
        'http://localhost',
        'http://127.0.0.1'
    ];

    // دمج المصادر من متغيرات البيئة مع الافتراضية
    $allowedOrigins = array_merge($defaultOrigins, $envOriginsArray);

    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';

    // التحقق من المصدر المسموح
    if (in_array($origin, $allowedOrigins) || strpos($origin, 'localhost') !== false) {
        header("Access-Control-Allow-Origin: $origin");
    } else {
        header("Access-Control-Allow-Origin: *");
    }

    // Headers أساسية
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept, Origin');
    header('Access-Control-Allow-Credentials: true');
    header('Access-Control-Max-Age: 86400'); // 24 ساعة

    // Content Type
    header('Content-Type: application/json; charset=utf-8');

    // إضافة headers إضافية لحل مشاكل CORS
    header('Access-Control-Expose-Headers: Content-Length, X-JSON');
    header('Vary: Accept-Encoding, Origin');

    // معالجة طلبات OPTIONS (Preflight)
    if (($_SERVER['REQUEST_METHOD'] ?? '') === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
}

/**
 * إرسال استجابة JSON مع معالجة الأخطاء
 */
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit();
}

/**
 * إرسال استجابة خطأ
 */
function sendErrorResponse($message, $statusCode = 400, $details = null) {
    $response = [
        'success' => false,
        'error' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    if ($details) {
        $response['details'] = $details;
    }
    
    sendJsonResponse($response, $statusCode);
}

/**
 * إرسال استجابة نجاح
 */
function sendSuccessResponse($data, $message = null) {
    $response = [
        'success' => true,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    if ($message) {
        $response['message'] = $message;
    }
    
    if (is_array($data)) {
        $response = array_merge($response, $data);
    } else {
        $response['data'] = $data;
    }
    
    sendJsonResponse($response);
}

/**
 * التحقق من طريقة الطلب
 */
function validateRequestMethod($allowedMethods) {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if (!in_array($method, $allowedMethods)) {
        sendErrorResponse(
            'طريقة الطلب غير مدعومة',
            405,
            ['allowed_methods' => $allowedMethods, 'received_method' => $method]
        );
    }
}

/**
 * قراءة بيانات JSON من الطلب
 */
function getJsonInput() {
    $input = file_get_contents("php://input");
    
    if (empty($input)) {
        return null;
    }
    
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        sendErrorResponse(
            'بيانات JSON غير صحيحة',
            400,
            ['json_error' => json_last_error_msg()]
        );
    }
    
    return $data;
}

/**
 * تسجيل الأخطاء
 */
function logError($message, $context = []) {
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'context' => $context,
        'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
        'request_method' => $_SERVER['REQUEST_METHOD'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? ''
    ];
    
    error_log(json_encode($logEntry, JSON_UNESCAPED_UNICODE));
}

// تطبيق إعدادات CORS تلقائياً عند تضمين هذا الملف
applyCorsHeaders();
?>
