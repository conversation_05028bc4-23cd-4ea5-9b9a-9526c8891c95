'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>heck,
  Trash2,
  Filter,
  Search,
  Activity,
  ShoppingBag,
  Wallet,
  Shield,
  MessageSquare,
  Star,
  AlertCircle,
  Info,
  Settings,
  Eye,
  EyeOff
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet, apiPost, handleApiError } from '@/utils/apiClient';

interface Notification {
  id: string;
  type: 'trade' | 'offer' | 'payment' | 'security' | 'system' | 'review' | 'message';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  actionUrl?: string;
  metadata?: Record<string, any>;
}

type NotificationFilter = 'all' | 'unread' | 'read' | 'trade' | 'offer' | 'payment' | 'security' | 'system';

export default function NotificationsPage() {
  const { t, formatDate } = useUserDashboardTranslation();
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<NotificationFilter>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);

  // جلب الإشعارات
  useEffect(() => {
    const fetchNotifications = async () => {
      if (!user?.id) {
        setError('معرف المستخدم غير متاح');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // بناء معاملات الاستعلام
        const queryParams = new URLSearchParams({
          user_id: user.id.toString(),
          filter: filter,
          limit: '20'
        });

        // استدعاء API الحقيقي لجلب الإشعارات
        const response = await apiGet(`/api/notifications/index.php?${queryParams.toString()}`);

        if (!response.success) {
          throw new Error(response.message || 'فشل في جلب الإشعارات');
        }

        // تحويل البيانات من API إلى تنسيق Notification
        const apiNotifications = response.data || [];
        const formattedNotifications: Notification[] = apiNotifications.map((notification: any) => ({
          id: notification.id.toString(),
          type: notification.type || 'system',
          title: notification.title || 'إشعار',
          message: notification.message || '',
          timestamp: notification.timestamp || new Date().toISOString(),
          isRead: Boolean(notification.is_read),
          priority: notification.priority || 'medium',
          actionUrl: notification.action_url || undefined,
          metadata: notification.metadata || {}
        }));

        setNotifications(formattedNotifications);
      } catch (err) {
        const errorMessage = handleApiError(err);
        setError(errorMessage);
        console.error('Error fetching notifications:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchNotifications();
  }, [user?.id, filter]);

  // تصفية الإشعارات
  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread' && notification.isRead) return false;
    if (filter === 'read' && !notification.isRead) return false;
    if (filter !== 'all' && filter !== 'unread' && filter !== 'read' && notification.type !== filter) return false;
    if (searchTerm && !notification.title.toLowerCase().includes(searchTerm.toLowerCase()) && 
        !notification.message.toLowerCase().includes(searchTerm.toLowerCase())) return false;
    return true;
  });

  // الحصول على أيقونة نوع الإشعار
  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'trade':
        return <Activity className="w-5 h-5 text-blue-500" />;
      case 'offer':
        return <ShoppingBag className="w-5 h-5 text-purple-500" />;
      case 'payment':
        return <Wallet className="w-5 h-5 text-green-500" />;
      case 'security':
        return <Shield className="w-5 h-5 text-red-500" />;
      case 'review':
        return <Star className="w-5 h-5 text-yellow-500" />;
      case 'message':
        return <MessageSquare className="w-5 h-5 text-indigo-500" />;
      case 'system':
        return <Info className="w-5 h-5 text-gray-500" />;
      default:
        return <Bell className="w-5 h-5 text-gray-500" />;
    }
  };

  // الحصول على لون الأولوية
  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-red-500';
      case 'high':
        return 'border-l-orange-500';
      case 'medium':
        return 'border-l-blue-500';
      case 'low':
        return 'border-l-gray-300';
      default:
        return 'border-l-gray-300';
    }
  };

  // تحديد/إلغاء تحديد إشعار
  const toggleNotificationSelection = (id: string) => {
    setSelectedNotifications(prev => 
      prev.includes(id) 
        ? prev.filter(notifId => notifId !== id)
        : [...prev, id]
    );
  };

  // تحديد/إلغاء تحديد جميع الإشعارات
  const toggleAllNotifications = () => {
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(filteredNotifications.map(n => n.id));
    }
  };

  // تحديد كمقروء
  const markAsRead = async (ids: string[]) => {
    if (!user?.id) return;

    try {
      const response = await apiPost('/api/notifications/index.php', {
        user_id: user.id,
        notification_ids: ids,
        action: 'mark_read'
      });

      if (response.success) {
        setNotifications(prev =>
          prev.map(notification =>
            ids.includes(notification.id)
              ? { ...notification, isRead: true }
              : notification
          )
        );
        setSelectedNotifications([]);
      }
    } catch (err) {
      console.error('Error marking notifications as read:', err);
    }
  };

  // تحديد كغير مقروء
  const markAsUnread = async (ids: string[]) => {
    if (!user?.id) return;

    try {
      const response = await apiPost('/api/notifications/index.php', {
        user_id: user.id,
        notification_ids: ids,
        action: 'mark_unread'
      });

      if (response.success) {
        setNotifications(prev =>
          prev.map(notification =>
            ids.includes(notification.id)
              ? { ...notification, isRead: false }
              : notification
          )
        );
        setSelectedNotifications([]);
      }
    } catch (err) {
      console.error('Error marking notifications as unread:', err);
    }
  };

  // حذف الإشعارات
  const deleteNotifications = async (ids: string[]) => {
    if (!user?.id) return;

    try {
      const response = await apiPost('/api/notifications/index.php', {
        user_id: user.id,
        notification_ids: ids,
        action: 'delete'
      });

      if (response.success) {
        setNotifications(prev => prev.filter(notification => !ids.includes(notification.id)));
        setSelectedNotifications([]);
      }
    } catch (err) {
      console.error('Error deleting notifications:', err);
    }
  };

  // إحصائيات سريعة
  const stats = {
    total: notifications.length,
    unread: notifications.filter(n => !n.isRead).length,
    high: notifications.filter(n => n.priority === 'high' || n.priority === 'urgent').length
  };

  // عرض رسالة الخطأ
  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6">
          <div className="flex items-center gap-3">
            <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
            <div>
              <h3 className="text-red-800 dark:text-red-200 font-medium">خطأ في تحميل الإشعارات</h3>
              <p className="text-red-600 dark:text-red-400 text-sm mt-1">{error}</p>
            </div>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            {t('notifications.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            إدارة إشعاراتك وتنبيهاتك
          </p>
        </div>

        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <a
            href="/user-dashboard/settings#notifications"
            className="flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
          >
            <Settings className="w-4 h-4" />
            <span className="text-sm">إعدادات الإشعارات</span>
          </a>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <Bell className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <span className="text-sm text-gray-600 dark:text-gray-400">الإجمالي</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{stats.total}</p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <EyeOff className="w-5 h-5 text-orange-600 dark:text-orange-400" />
            <span className="text-sm text-gray-600 dark:text-gray-400">غير مقروءة</span>
          </div>
          <p className="text-2xl font-bold text-orange-600 dark:text-orange-400 mt-1">{stats.unread}</p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
            <span className="text-sm text-gray-600 dark:text-gray-400">مهمة</span>
          </div>
          <p className="text-2xl font-bold text-red-600 dark:text-red-400 mt-1">{stats.high}</p>
        </div>
      </div>

      {/* المرشحات والبحث */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* البحث */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 rtl:right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="البحث في الإشعارات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 rtl:pr-10 pr-3 rtl:pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* المرشحات */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Filter className="w-4 h-4 text-gray-500" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as NotificationFilter)}
                className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الإشعارات</option>
                <option value="unread">غير المقروءة</option>
                <option value="read">المقروءة</option>
                <option value="trade">الصفقات</option>
                <option value="offer">العروض</option>
                <option value="payment">المدفوعات</option>
                <option value="security">الأمان</option>
                <option value="system">النظام</option>
              </select>
            </div>
          </div>
        </div>

        {/* إجراءات مجمعة */}
        {selectedNotifications.length > 0 && (
          <div className="flex items-center space-x-3 rtl:space-x-reverse mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {selectedNotifications.length} محدد
            </span>
            
            <button
              onClick={() => markAsRead(selectedNotifications)}
              className="flex items-center space-x-1 rtl:space-x-reverse px-3 py-1 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
            >
              <Check className="w-4 h-4" />
              <span className="text-sm">تحديد كمقروء</span>
            </button>
            
            <button
              onClick={() => markAsUnread(selectedNotifications)}
              className="flex items-center space-x-1 rtl:space-x-reverse px-3 py-1 bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors"
            >
              <EyeOff className="w-4 h-4" />
              <span className="text-sm">تحديد كغير مقروء</span>
            </button>
            
            <button
              onClick={() => deleteNotifications(selectedNotifications)}
              className="flex items-center space-x-1 rtl:space-x-reverse px-3 py-1 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors"
            >
              <Trash2 className="w-4 h-4" />
              <span className="text-sm">حذف</span>
            </button>
          </div>
        )}
      </div>

      {/* قائمة الإشعارات */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        {loading ? (
          <div className="p-6">
            <div className="space-y-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg" />
                </div>
              ))}
            </div>
          </div>
        ) : filteredNotifications.length > 0 ? (
          <div>
            {/* Header للتحديد الجماعي */}
            <div className="flex items-center space-x-3 rtl:space-x-reverse p-4 border-b border-gray-200 dark:border-gray-700">
              <input
                type="checkbox"
                checked={selectedNotifications.length === filteredNotifications.length}
                onChange={toggleAllNotifications}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                تحديد الكل ({filteredNotifications.length})
              </span>
            </div>

            {/* الإشعارات */}
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`flex items-start space-x-4 rtl:space-x-reverse p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border-l-4 ${getPriorityColor(notification.priority)} ${
                    !notification.isRead ? 'bg-blue-50/30 dark:bg-blue-900/10' : ''
                  }`}
                >
                  {/* Checkbox */}
                  <input
                    type="checkbox"
                    checked={selectedNotifications.includes(notification.id)}
                    onChange={() => toggleNotificationSelection(notification.id)}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 mt-1"
                  />

                  {/* Icon */}
                  <div className="flex-shrink-0 mt-1">
                    {getNotificationIcon(notification.type)}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className={`text-sm font-medium ${
                          !notification.isRead 
                            ? 'text-gray-900 dark:text-white' 
                            : 'text-gray-700 dark:text-gray-300'
                        }`}>
                          {notification.title}
                        </h4>
                        <p className={`text-sm mt-1 ${
                          !notification.isRead 
                            ? 'text-gray-700 dark:text-gray-300' 
                            : 'text-gray-600 dark:text-gray-400'
                        }`}>
                          {notification.message}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                          {formatDate(notification.timestamp, { 
                            day: 'numeric', 
                            month: 'short',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center space-x-2 rtl:space-x-reverse ml-4 rtl:mr-4">
                        {!notification.isRead ? (
                          <button
                            onClick={() => markAsRead([notification.id])}
                            className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                            title="تحديد كمقروء"
                          >
                            <Check className="w-4 h-4" />
                          </button>
                        ) : (
                          <button
                            onClick={() => markAsUnread([notification.id])}
                            className="p-1 text-gray-400 hover:text-orange-600 dark:hover:text-orange-400 transition-colors"
                            title="تحديد كغير مقروء"
                          >
                            <EyeOff className="w-4 h-4" />
                          </button>
                        )}
                        
                        <button
                          onClick={() => deleteNotifications([notification.id])}
                          className="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
                          title="حذف"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    {/* Action Button */}
                    {notification.actionUrl && (
                      <a
                        href={notification.actionUrl}
                        className="inline-flex items-center space-x-1 rtl:space-x-reverse text-xs text-blue-600 dark:text-blue-400 hover:underline mt-2"
                      >
                        <span>عرض التفاصيل</span>
                        <Eye className="w-3 h-3" />
                      </a>
                    )}
                  </div>

                  {/* Unread Indicator */}
                  {!notification.isRead && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-2" />
                  )}
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="p-12 text-center">
            <Bell className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              لا توجد إشعارات
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {filter === 'all' 
                ? 'لا توجد إشعارات حالياً'
                : `لا توجد إشعارات ${filter === 'unread' ? 'غير مقروءة' : filter === 'read' ? 'مقروءة' : 'من هذا النوع'}`
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
