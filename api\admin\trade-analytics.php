<?php
/**
 * API التحليلات والتقارير المتقدمة للتداولات
 * Advanced Trade Analytics and Reporting API
 * 
 * يوفر تحليلات شاملة وتقارير مفصلة للتداولات والنزاعات
 * Provides comprehensive analytics and detailed reports for trades and disputes
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../../config/database.php';

// التحقق من صحة الجلسة والصلاحيات الإدارية
function validateAdminSession() {
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
        http_response_code(403);
        echo json_encode([
            'success' => false,
            'error' => 'غير مصرح لك بالوصول إلى هذا المورد',
            'error_en' => 'Access denied. Admin privileges required.'
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
    return $_SESSION['user_id'];
}

// الحصول على إحصائيات شاملة للتداولات
function getComprehensiveTradeStats($connection, $date_range = null) {
    $where_clause = '';
    $params = [];
    
    if ($date_range) {
        $where_clause = "WHERE created_at BETWEEN ? AND ?";
        $params = [$date_range['start'], $date_range['end']];
    }
    
    // الإحصائيات الأساسية
    $basic_stats_query = "
        SELECT 
            COUNT(*) as total_trades,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_trades,
            COUNT(CASE WHEN status = 'disputed' THEN 1 END) as disputed_trades,
            COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_trades,
            COUNT(CASE WHEN status IN ('created', 'joined', 'payment_sent', 'payment_received') THEN 1 END) as active_trades,
            SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_volume,
            AVG(CASE WHEN status = 'completed' THEN amount END) as avg_trade_size,
            AVG(CASE WHEN status = 'completed' AND completed_at IS NOT NULL 
                THEN TIMESTAMPDIFF(MINUTE, created_at, completed_at) END) as avg_completion_time,
            (COUNT(CASE WHEN status = 'disputed' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)) as dispute_rate,
            (COUNT(CASE WHEN status = 'completed' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)) as completion_rate
        FROM trades 
        $where_clause
    ";
    
    $stmt = $connection->prepare($basic_stats_query);
    $stmt->execute($params);
    $basic_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إحصائيات حسب العملة
    $currency_stats_query = "
        SELECT 
            currency,
            COUNT(*) as trade_count,
            SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_volume,
            AVG(CASE WHEN status = 'completed' THEN amount END) as avg_amount
        FROM trades 
        $where_clause
        GROUP BY currency
        ORDER BY total_volume DESC
    ";
    
    $stmt = $connection->prepare($currency_stats_query);
    $stmt->execute($params);
    $currency_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إحصائيات حسب الحالة
    $status_stats_query = "
        SELECT 
            status,
            COUNT(*) as count,
            (COUNT(*) * 100.0 / (SELECT COUNT(*) FROM trades " . ($where_clause ? $where_clause : "") . ")) as percentage
        FROM trades 
        $where_clause
        GROUP BY status
        ORDER BY count DESC
    ";
    
    $stmt = $connection->prepare($status_stats_query);
    $stmt->execute($params);
    $status_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'basic_stats' => $basic_stats,
        'currency_breakdown' => $currency_stats,
        'status_breakdown' => $status_stats
    ];
}

// الحصول على بيانات الحجم اليومي
function getDailyVolumeData($connection, $days = 30) {
    $query = "
        SELECT 
            DATE(created_at) as trade_date,
            COUNT(*) as trade_count,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
            SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as daily_volume,
            COUNT(CASE WHEN status = 'disputed' THEN 1 END) as dispute_count
        FROM trades 
        WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
        GROUP BY DATE(created_at)
        ORDER BY trade_date DESC
    ";
    
    $stmt = $connection->prepare($query);
    $stmt->execute([$days]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// الحصول على أداء المستخدمين الأعلى
function getTopPerformers($connection, $limit = 10, $date_range = null) {
    $where_clause = '';
    $params = [];
    
    if ($date_range) {
        $where_clause = "WHERE t.created_at BETWEEN ? AND ?";
        $params = [$date_range['start'], $date_range['end']];
    }
    
    $query = "
        SELECT 
            u.id,
            u.username,
            u.wallet_address,
            COUNT(t.id) as total_trades,
            COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_trades,
            SUM(CASE WHEN t.status = 'completed' THEN t.amount ELSE 0 END) as total_volume,
            AVG(CASE WHEN t.status = 'completed' AND t.completed_at IS NOT NULL 
                THEN TIMESTAMPDIFF(MINUTE, t.created_at, t.completed_at) END) as avg_completion_time,
            (COUNT(CASE WHEN t.status = 'completed' THEN 1 END) * 100.0 / NULLIF(COUNT(t.id), 0)) as completion_rate,
            COUNT(CASE WHEN t.status = 'disputed' THEN 1 END) as dispute_count
        FROM users u
        JOIN trades t ON (u.id = t.seller_id OR u.id = t.buyer_id)
        $where_clause
        GROUP BY u.id, u.username, u.wallet_address
        HAVING total_trades >= 5
        ORDER BY total_volume DESC, completion_rate DESC
        LIMIT ?
    ";
    
    $params[] = $limit;
    $stmt = $connection->prepare($query);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// الحصول على إحصائيات النزاعات المفصلة
function getDetailedDisputeStats($connection, $date_range = null) {
    $where_clause = '';
    $params = [];
    
    if ($date_range) {
        $where_clause = "WHERE dispute_created_at BETWEEN ? AND ?";
        $params = [$date_range['start'], $date_range['end']];
    }
    
    // الإحصائيات الأساسية للنزاعات
    $basic_query = "
        SELECT 
            COUNT(*) as total_disputes,
            COUNT(CASE WHEN dispute_resolved_by IS NULL THEN 1 END) as pending_disputes,
            COUNT(CASE WHEN dispute_resolved_by IS NOT NULL THEN 1 END) as resolved_disputes,
            AVG(CASE WHEN dispute_resolved_by IS NOT NULL 
                THEN TIMESTAMPDIFF(HOUR, dispute_created_at, dispute_resolved_at) END) as avg_resolution_time,
            (COUNT(CASE WHEN dispute_resolved_by IS NOT NULL THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)) as resolution_rate
        FROM trades 
        WHERE status = 'disputed' OR dispute_resolved_by IS NOT NULL
        " . ($date_range ? "AND " . str_replace('WHERE ', '', $where_clause) : "");
    
    $stmt = $connection->prepare($basic_query);
    $stmt->execute($params);
    $basic_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إحصائيات أنواع القرارات
    $resolution_query = "
        SELECT 
            dr.resolution_type,
            COUNT(*) as count,
            AVG(dr.resolution_amount) as avg_amount,
            SUM(dr.resolution_amount) as total_amount
        FROM dispute_resolutions dr
        JOIN trades t ON dr.trade_id = t.id
        " . ($date_range ? "WHERE dr.created_at BETWEEN ? AND ?" : "") . "
        GROUP BY dr.resolution_type
        ORDER BY count DESC
    ";
    
    $stmt = $connection->prepare($resolution_query);
    $stmt->execute($date_range ? [$date_range['start'], $date_range['end']] : []);
    $resolution_types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // أداء المدراء في حل النزاعات
    $admin_performance_query = "
        SELECT 
            u.username as admin_username,
            COUNT(dr.id) as resolved_disputes,
            AVG(TIMESTAMPDIFF(HOUR, t.dispute_created_at, dr.created_at)) as avg_resolution_time,
            COUNT(CASE WHEN dr.resolution_type = 'seller_favor' THEN 1 END) as seller_favor_count,
            COUNT(CASE WHEN dr.resolution_type = 'buyer_favor' THEN 1 END) as buyer_favor_count,
            COUNT(CASE WHEN dr.resolution_type = 'partial_refund' THEN 1 END) as partial_refund_count
        FROM dispute_resolutions dr
        JOIN trades t ON dr.trade_id = t.id
        JOIN users u ON dr.admin_id = u.id
        " . ($date_range ? "WHERE dr.created_at BETWEEN ? AND ?" : "") . "
        GROUP BY u.id, u.username
        ORDER BY resolved_disputes DESC
    ";
    
    $stmt = $connection->prepare($admin_performance_query);
    $stmt->execute($date_range ? [$date_range['start'], $date_range['end']] : []);
    $admin_performance = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'basic_stats' => $basic_stats,
        'resolution_types' => $resolution_types,
        'admin_performance' => $admin_performance
    ];
}

// الحصول على تقرير شامل
function getComprehensiveReport($connection, $date_range = null) {
    return [
        'trade_statistics' => getComprehensiveTradeStats($connection, $date_range),
        'daily_volume' => getDailyVolumeData($connection, 30),
        'top_performers' => getTopPerformers($connection, 10, $date_range),
        'dispute_statistics' => getDetailedDisputeStats($connection, $date_range),
        'generated_at' => date('Y-m-d H:i:s'),
        'date_range' => $date_range
    ];
}

// تصدير البيانات بصيغة CSV
function exportToCSV($connection, $type, $date_range = null) {
    $filename = "ikaros_p2p_{$type}_" . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    
    $output = fopen('php://output', 'w');
    
    // إضافة BOM للدعم الصحيح للعربية في Excel
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    switch ($type) {
        case 'trades':
            fputcsv($output, [
                'معرف الصفقة', 'نوع الصفقة', 'المبلغ', 'السعر', 'العملة', 'الحالة',
                'البائع', 'المشتري', 'طريقة الدفع', 'تاريخ الإنشاء', 'تاريخ الإكمال'
            ]);
            
            $where_clause = '';
            $params = [];
            if ($date_range) {
                $where_clause = "WHERE t.created_at BETWEEN ? AND ?";
                $params = [$date_range['start'], $date_range['end']];
            }
            
            $query = "
                SELECT 
                    t.id, t.type, t.amount, t.price, t.currency, t.status,
                    seller.username as seller_username,
                    buyer.username as buyer_username,
                    t.payment_method, t.created_at, t.completed_at
                FROM trades t
                LEFT JOIN users seller ON t.seller_id = seller.id
                LEFT JOIN users buyer ON t.buyer_id = buyer.id
                $where_clause
                ORDER BY t.created_at DESC
            ";
            
            $stmt = $connection->prepare($query);
            $stmt->execute($params);
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                fputcsv($output, [
                    $row['id'],
                    $row['type'] === 'buy' ? 'شراء' : 'بيع',
                    $row['amount'],
                    $row['price'],
                    $row['currency'],
                    $row['status'],
                    $row['seller_username'],
                    $row['buyer_username'],
                    $row['payment_method'],
                    $row['created_at'],
                    $row['completed_at']
                ]);
            }
            break;
            
        case 'disputes':
            fputcsv($output, [
                'معرف الصفقة', 'سبب النزاع', 'تاريخ النزاع', 'حالة النزاع',
                'المحلول بواسطة', 'قرار النزاع', 'تاريخ الحل'
            ]);
            
            $where_clause = "WHERE t.status = 'disputed' OR t.dispute_resolved_by IS NOT NULL";
            $params = [];
            if ($date_range) {
                $where_clause .= " AND t.dispute_created_at BETWEEN ? AND ?";
                $params = [$date_range['start'], $date_range['end']];
            }
            
            $query = "
                SELECT 
                    t.id, t.dispute_reason, t.dispute_created_at,
                    CASE WHEN t.dispute_resolved_by IS NULL THEN 'معلق' ELSE 'محلول' END as dispute_status,
                    resolver.username as resolver_username,
                    t.dispute_resolution, t.dispute_resolved_at
                FROM trades t
                LEFT JOIN users resolver ON t.dispute_resolved_by = resolver.id
                $where_clause
                ORDER BY t.dispute_created_at DESC
            ";
            
            $stmt = $connection->prepare($query);
            $stmt->execute($params);
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                fputcsv($output, [
                    $row['id'],
                    $row['dispute_reason'],
                    $row['dispute_created_at'],
                    $row['dispute_status'],
                    $row['resolver_username'],
                    $row['dispute_resolution'],
                    $row['dispute_resolved_at']
                ]);
            }
            break;
    }
    
    fclose($output);
    exit();
}

// معالجة الطلبات
try {
    session_start();
    $admin_id = validateAdminSession();

    // Get database connection
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();

    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        $action = $_GET['action'] ?? 'comprehensive';
        
        // إعداد نطاق التاريخ
        $date_range = null;
        if (isset($_GET['date_from']) && isset($_GET['date_to'])) {
            $date_range = [
                'start' => $_GET['date_from'],
                'end' => $_GET['date_to']
            ];
        }
        
        switch ($action) {
            case 'comprehensive':
                $report = getComprehensiveReport($connection, $date_range);
                echo json_encode([
                    'success' => true,
                    'data' => $report
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'trade_stats':
                $stats = getComprehensiveTradeStats($connection, $date_range);
                echo json_encode([
                    'success' => true,
                    'data' => $stats
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'daily_volume':
                $days = (int)($_GET['days'] ?? 30);
                $data = getDailyVolumeData($connection, $days);
                echo json_encode([
                    'success' => true,
                    'data' => $data
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'top_performers':
                $limit = (int)($_GET['limit'] ?? 10);
                $data = getTopPerformers($connection, $limit, $date_range);
                echo json_encode([
                    'success' => true,
                    'data' => $data
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'dispute_stats':
                $stats = getDetailedDisputeStats($connection, $date_range);
                echo json_encode([
                    'success' => true,
                    'data' => $stats
                ], JSON_UNESCAPED_UNICODE);
                break;
                
            case 'export':
                $type = $_GET['type'] ?? 'trades';
                if (!in_array($type, ['trades', 'disputes'])) {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'error' => 'نوع التصدير غير صحيح',
                        'error_en' => 'Invalid export type'
                    ], JSON_UNESCAPED_UNICODE);
                    break;
                }
                exportToCSV($connection, $type, $date_range);
                break;
                
            default:
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'إجراء غير صحيح',
                    'error_en' => 'Invalid action'
                ], JSON_UNESCAPED_UNICODE);
                break;
        }
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'طريقة الطلب غير مدعومة',
            'error_en' => 'Method not allowed'
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في الخادم: ' . $e->getMessage(),
        'error_en' => 'Server error: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
