import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';

export const useChatTranslation = () => {
  const { t, i18n } = useTranslation('chat');
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const loadTranslations = async () => {
      try {
        if (!i18n.hasResourceBundle(i18n.language, 'chat')) {
          // تحميل ترجمات الدردشة
          const currentLang = i18n.language;
          const chatTranslations = await import(`../../public/locales/${currentLang}/chat.json`);
          
          i18n.addResourceBundle(currentLang, 'chat', chatTranslations.default, true, true);
        }
        setIsLoaded(true);
      } catch (error) {
        console.error('Error loading chat translations:', error);
        // fallback للإنجليزية
        try {
          const fallbackTranslations = await import(`../../public/locales/en/chat.json`);
          i18n.addResourceBundle('en', 'chat', fallbackTranslations.default, true, true);
          setIsLoaded(true);
        } catch (fallbackError) {
          console.error('Error loading fallback chat translations:', fallbackError);
          setIsLoaded(true); // تحميل حتى لو فشل
        }
      }
    };

    loadTranslations();
  }, [i18n]);

  return { t, isLoaded };
};
