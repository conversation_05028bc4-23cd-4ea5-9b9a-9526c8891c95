<?php
/**
 * API endpoint لإدارة المستخدمين
 * Users Management API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    // Get database connection
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($method) {
        case 'GET':
            // جلب قائمة المستخدمين
            $page = $_GET['page'] ?? 1;
            $limit = $_GET['limit'] ?? 20;
            $search = $_GET['search'] ?? '';
            $status = $_GET['status'] ?? 'all';
            
            $offset = ($page - 1) * $limit;
            
            // بناء الاستعلام
            $whereConditions = [];
            $params = [];
            
            if (!empty($search)) {
                $whereConditions[] = "(username LIKE ? OR email LIKE ? OR full_name LIKE ?)";
                $searchTerm = "%$search%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            if ($status !== 'all') {
                switch ($status) {
                    case 'active':
                        $whereConditions[] = "is_active = 1";
                        break;
                    case 'inactive':
                        $whereConditions[] = "is_active = 0";
                        break;
                    case 'verified':
                        $whereConditions[] = "is_verified = 1";
                        break;
                    case 'admin':
                        $whereConditions[] = "is_admin = 1";
                        break;
                }
            }
            
            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
            
            // جلب المستخدمين
            $stmt = $connection->prepare("
                SELECT id, username, email, full_name, wallet_address, is_verified, is_admin, is_active, 
                       rating, total_trades, completed_trades, total_volume, created_at, last_login
                FROM users 
                $whereClause
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            ");
            
            $params[] = $limit;
            $params[] = $offset;
            $stmt->execute($params);
            $users = $stmt->fetchAll();
            
            // عدد المستخدمين الإجمالي
            $countStmt = $connection->prepare("SELECT COUNT(*) as total FROM users $whereClause");
            $countParams = array_slice($params, 0, -2); // إزالة limit و offset
            $countStmt->execute($countParams);
            $total = $countStmt->fetch()['total'];
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'users' => $users,
                    'pagination' => [
                        'page' => (int)$page,
                        'limit' => (int)$limit,
                        'total' => (int)$total,
                        'pages' => ceil($total / $limit)
                    ]
                ]
            ]);
            break;
            
        case 'PUT':
            // تحديث مستخدم
            if (!$input || !isset($input['id'])) {
                throw new Exception('معرف المستخدم مطلوب');
            }
            
            $userId = $input['id'];
            $updates = [];
            $params = [];
            
            // الحقول القابلة للتحديث
            $allowedFields = ['is_active', 'is_verified', 'is_admin', 'full_name', 'email'];
            
            foreach ($allowedFields as $field) {
                if (isset($input[$field])) {
                    $updates[] = "$field = ?";
                    $params[] = $input[$field];
                }
            }
            
            if (empty($updates)) {
                throw new Exception('لا توجد حقول للتحديث');
            }
            
            $params[] = $userId;
            
            $stmt = $connection->prepare("
                UPDATE users 
                SET " . implode(', ', $updates) . " 
                WHERE id = ?
            ");
            
            $stmt->execute($params);
            
            if ($stmt->rowCount() === 0) {
                throw new Exception('المستخدم غير موجود');
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث المستخدم بنجاح'
            ]);
            break;
            
        case 'DELETE':
            // حذف مستخدم (تعطيل فقط)
            if (!$input || !isset($input['id'])) {
                throw new Exception('معرف المستخدم مطلوب');
            }
            
            $userId = $input['id'];
            
            $stmt = $connection->prepare("UPDATE users SET is_active = 0 WHERE id = ?");
            $stmt->execute([$userId]);
            
            if ($stmt->rowCount() === 0) {
                throw new Exception('المستخدم غير موجود');
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تعطيل المستخدم بنجاح'
            ]);
            break;
            
        default:
            throw new Exception('طريقة غير مدعومة');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    // Log database errors (in production, use proper logging)
    error_log('Database error in users-management.php: ' . $e->getMessage());
}
?>
