<?php
/**
 * API إدارة خطط الاشتراك - لوحة الإدارة
 */

require_once __DIR__ . '/../cors.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../utils/response.php';

try {
    // التحقق من صلاحيات الإدارة
    $authResult = authenticateAdmin();
    if (!$authResult['success']) {
        sendErrorResponse($authResult['message'], 401);
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    if ($method === 'GET') {
        $action = $_GET['action'] ?? 'list';
        
        if ($action === 'list') {
            getSubscriptionPlans($connection);
        } elseif ($action === 'statistics') {
            getSubscriptionStatistics($connection);
        } else {
            sendErrorResponse('إجراء غير صحيح', 400);
        }
        
    } elseif ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
        
        if ($action === 'create') {
            createSubscriptionPlan($connection, $input);
        } elseif ($action === 'update') {
            updateSubscriptionPlan($connection, $input);
        } elseif ($action === 'delete') {
            deleteSubscriptionPlan($connection, $input);
        } else {
            sendErrorResponse('إجراء غير صحيح', 400);
        }
        
    } else {
        sendErrorResponse('طريقة طلب غير مدعومة', 405);
    }
    
} catch (Exception $e) {
    logError("Admin Subscription Plans API error: " . $e->getMessage());
    sendErrorResponse('خطأ في الخادم: ' . $e->getMessage(), 500);
}

/**
 * جلب جميع خطط الاشتراك
 */
function getSubscriptionPlans($connection) {
    try {
        $stmt = $connection->prepare("
            SELECT sp.*, 
                   COUNT(us.id) as subscribers_count,
                   SUM(CASE WHEN us.status = 'active' THEN us.payment_amount ELSE 0 END) as total_revenue
            FROM subscription_plans sp
            LEFT JOIN user_subscriptions us ON sp.id = us.plan_id
            GROUP BY sp.id
            ORDER BY sp.sort_order ASC, sp.created_at ASC
        ");
        $stmt->execute();
        $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($plans as &$plan) {
            $plan['features'] = json_decode($plan['features'], true) ?: [];
            $plan['price_monthly'] = floatval($plan['price_monthly']);
            $plan['price_yearly'] = floatval($plan['price_yearly']);
            $plan['monthly_free_offers'] = intval($plan['monthly_free_offers']);
            $plan['subscribers_count'] = intval($plan['subscribers_count']);
            $plan['total_revenue'] = floatval($plan['total_revenue']);
            $plan['is_active'] = boolval($plan['is_active']);
            $plan['is_default'] = boolval($plan['is_default']);
        }
        
        sendSuccessResponse([
            'plans' => $plans,
            'total' => count($plans)
        ], 'تم جلب خطط الاشتراك بنجاح');
        
    } catch (Exception $e) {
        throw new Exception('خطأ في جلب خطط الاشتراك: ' . $e->getMessage());
    }
}

/**
 * إنشاء خطة اشتراك جديدة
 */
function createSubscriptionPlan($connection, $input) {
    try {
        // التحقق من البيانات المطلوبة
        $required = ['name', 'name_ar', 'monthly_free_offers', 'price_monthly'];
        foreach ($required as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                sendErrorResponse("الحقل {$field} مطلوب", 400);
            }
        }
        
        // التحقق من عدم تكرار اسم الخطة
        $stmt = $connection->prepare("SELECT id FROM subscription_plans WHERE name = ? OR name_ar = ?");
        $stmt->execute([$input['name'], $input['name_ar']]);
        if ($stmt->fetch()) {
            sendErrorResponse('اسم الخطة موجود بالفعل', 400);
        }
        
        $stmt = $connection->prepare("
            INSERT INTO subscription_plans 
            (name, name_ar, description, description_ar, monthly_free_offers, 
             price_monthly, price_yearly, features, is_active, is_default, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $features = isset($input['features']) ? json_encode($input['features']) : '[]';
        $isActive = isset($input['is_active']) ? $input['is_active'] : true;
        $isDefault = isset($input['is_default']) ? $input['is_default'] : false;
        $sortOrder = isset($input['sort_order']) ? $input['sort_order'] : 0;
        
        // إذا كانت هذه الخطة افتراضية، قم بإلغاء الافتراضية من الخطط الأخرى
        if ($isDefault) {
            $connection->prepare("UPDATE subscription_plans SET is_default = 0")->execute();
        }
        
        $stmt->execute([
            $input['name'],
            $input['name_ar'],
            $input['description'] ?? '',
            $input['description_ar'] ?? '',
            $input['monthly_free_offers'],
            $input['price_monthly'],
            $input['price_yearly'] ?? null,
            $features,
            $isActive,
            $isDefault,
            $sortOrder
        ]);
        
        $planId = $connection->lastInsertId();
        
        sendSuccessResponse([
            'plan_id' => $planId,
            'message' => 'تم إنشاء خطة الاشتراك بنجاح'
        ], 'تم إنشاء خطة الاشتراك بنجاح');
        
    } catch (Exception $e) {
        throw new Exception('خطأ في إنشاء خطة الاشتراك: ' . $e->getMessage());
    }
}

/**
 * تحديث خطة اشتراك
 */
function updateSubscriptionPlan($connection, $input) {
    try {
        if (!isset($input['plan_id'])) {
            sendErrorResponse('معرف الخطة مطلوب', 400);
        }
        
        $planId = $input['plan_id'];
        
        // التحقق من وجود الخطة
        $stmt = $connection->prepare("SELECT id FROM subscription_plans WHERE id = ?");
        $stmt->execute([$planId]);
        if (!$stmt->fetch()) {
            sendErrorResponse('خطة الاشتراك غير موجودة', 404);
        }
        
        $updateFields = [];
        $updateValues = [];
        
        $allowedFields = [
            'name', 'name_ar', 'description', 'description_ar', 
            'monthly_free_offers', 'price_monthly', 'price_yearly', 
            'is_active', 'is_default', 'sort_order'
        ];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updateFields[] = "{$field} = ?";
                $updateValues[] = $input[$field];
            }
        }
        
        if (isset($input['features'])) {
            $updateFields[] = "features = ?";
            $updateValues[] = json_encode($input['features']);
        }
        
        if (empty($updateFields)) {
            sendErrorResponse('لا توجد حقول للتحديث', 400);
        }
        
        // إذا كانت هذه الخطة ستصبح افتراضية، قم بإلغاء الافتراضية من الخطط الأخرى
        if (isset($input['is_default']) && $input['is_default']) {
            $connection->prepare("UPDATE subscription_plans SET is_default = 0 WHERE id != ?")->execute([$planId]);
        }
        
        $updateFields[] = "updated_at = NOW()";
        $updateValues[] = $planId;
        
        $sql = "UPDATE subscription_plans SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $connection->prepare($sql);
        $stmt->execute($updateValues);
        
        sendSuccessResponse([
            'plan_id' => $planId,
            'message' => 'تم تحديث خطة الاشتراك بنجاح'
        ], 'تم تحديث خطة الاشتراك بنجاح');
        
    } catch (Exception $e) {
        throw new Exception('خطأ في تحديث خطة الاشتراك: ' . $e->getMessage());
    }
}

/**
 * حذف خطة اشتراك
 */
function deleteSubscriptionPlan($connection, $input) {
    try {
        if (!isset($input['plan_id'])) {
            sendErrorResponse('معرف الخطة مطلوب', 400);
        }
        
        $planId = $input['plan_id'];
        
        // التحقق من عدم وجود مشتركين نشطين
        $stmt = $connection->prepare("
            SELECT COUNT(*) as active_subscribers 
            FROM user_subscriptions 
            WHERE plan_id = ? AND status = 'active'
        ");
        $stmt->execute([$planId]);
        $activeSubscribers = $stmt->fetch(PDO::FETCH_ASSOC)['active_subscribers'];
        
        if ($activeSubscribers > 0) {
            sendErrorResponse('لا يمكن حذف خطة تحتوي على مشتركين نشطين', 400);
        }
        
        // حذف الخطة
        $stmt = $connection->prepare("DELETE FROM subscription_plans WHERE id = ?");
        $stmt->execute([$planId]);
        
        if ($stmt->rowCount() === 0) {
            sendErrorResponse('خطة الاشتراك غير موجودة', 404);
        }
        
        sendSuccessResponse([
            'plan_id' => $planId,
            'message' => 'تم حذف خطة الاشتراك بنجاح'
        ], 'تم حذف خطة الاشتراك بنجاح');
        
    } catch (Exception $e) {
        throw new Exception('خطأ في حذف خطة الاشتراك: ' . $e->getMessage());
    }
}

/**
 * جلب إحصائيات الاشتراكات
 */
function getSubscriptionStatistics($connection) {
    try {
        // إحصائيات عامة
        $stats = [];
        
        // إجمالي الخطط
        $stmt = $connection->prepare("SELECT COUNT(*) as total FROM subscription_plans WHERE is_active = 1");
        $stmt->execute();
        $stats['total_plans'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // إجمالي المشتركين النشطين
        $stmt = $connection->prepare("SELECT COUNT(*) as total FROM user_subscriptions WHERE status = 'active'");
        $stmt->execute();
        $stats['active_subscribers'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // إجمالي الإيرادات الشهرية
        $stmt = $connection->prepare("
            SELECT SUM(payment_amount) as total 
            FROM user_subscriptions 
            WHERE status = 'active' AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ");
        $stmt->execute();
        $stats['monthly_revenue'] = floatval($stmt->fetch(PDO::FETCH_ASSOC)['total']);
        
        sendSuccessResponse([
            'statistics' => $stats
        ], 'تم جلب الإحصائيات بنجاح');
        
    } catch (Exception $e) {
        throw new Exception('خطأ في جلب الإحصائيات: ' . $e->getMessage());
    }
}

/**
 * تسجيل الأخطاء
 */
function logError($message) {
    error_log("[" . date('Y-m-d H:i:s') . "] Admin Subscription Plans API: " . $message);
}

?>
