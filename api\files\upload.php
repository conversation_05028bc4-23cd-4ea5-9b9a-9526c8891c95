<?php
/**
 * API endpoint لرفع الملفات
 * File Upload API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";
require_once __DIR__ . "/../middleware/auth.php";

// التحقق من طريقة الطلب
validateRequestMethod(['POST']);

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/../../.env')) {
    $lines = file(__DIR__ . '/../../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

// إعدادات الملفات من متغيرات البيئة
$maxFileSize = (int)($_ENV['UPLOAD_MAX_SIZE'] ?? 5242880); // 5MB افتراضي
$allowedTypes = explode(',', $_ENV['UPLOAD_ALLOWED_TYPES'] ?? 'jpg,jpeg,png,gif,pdf,doc,docx');
$uploadPath = $_ENV['UPLOAD_PATH'] ?? 'public/uploads/';

// التأكد من وجود مجلد الرفع
if (!file_exists($uploadPath)) {
    mkdir($uploadPath, 0755, true);
}

/**
 * التحقق من نوع الملف
 */
function isAllowedFileType($filename, $allowedTypes) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, $allowedTypes);
}

/**
 * إنشاء اسم ملف آمن وفريد
 */
function generateSafeFilename($originalName) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $basename = pathinfo($originalName, PATHINFO_FILENAME);
    
    // تنظيف اسم الملف
    $basename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
    $basename = substr($basename, 0, 50); // تحديد طول الاسم
    
    // إضافة timestamp وhash للتفرد
    $uniqueId = uniqid() . '_' . time();
    
    return $basename . '_' . $uniqueId . '.' . $extension;
}

/**
 * التحقق من نوع MIME
 */
function validateMimeType($filePath, $allowedTypes) {
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $filePath);
    finfo_close($finfo);
    
    $allowedMimes = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'pdf' => 'application/pdf',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    foreach ($allowedTypes as $type) {
        if (isset($allowedMimes[$type]) && $allowedMimes[$type] === $mimeType) {
            return true;
        }
    }
    
    return false;
}

try {
    // التحقق من المصادقة
    $auth = requireAuth();
    $userId = $auth->getCurrentUserId();
    
    if (!$userId) {
        sendErrorResponse('المصادقة مطلوبة', 401);
    }
    
    // التحقق من وجود ملف مرفوع
    if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        $errorMessages = [
            UPLOAD_ERR_INI_SIZE => 'الملف كبير جداً (تجاوز حد الخادم)',
            UPLOAD_ERR_FORM_SIZE => 'الملف كبير جداً (تجاوز حد النموذج)',
            UPLOAD_ERR_PARTIAL => 'تم رفع الملف جزئياً فقط',
            UPLOAD_ERR_NO_FILE => 'لم يتم رفع أي ملف',
            UPLOAD_ERR_NO_TMP_DIR => 'مجلد مؤقت مفقود',
            UPLOAD_ERR_CANT_WRITE => 'فشل في كتابة الملف',
            UPLOAD_ERR_EXTENSION => 'امتداد PHP أوقف رفع الملف'
        ];
        
        $error = $_FILES['file']['error'] ?? UPLOAD_ERR_NO_FILE;
        $message = $errorMessages[$error] ?? 'خطأ غير معروف في رفع الملف';
        
        sendErrorResponse($message);
    }
    
    $file = $_FILES['file'];
    $originalName = $file['name'];
    $tempPath = $file['tmp_name'];
    $fileSize = $file['size'];
    
    // التحقق من حجم الملف
    if ($fileSize > $maxFileSize) {
        sendErrorResponse('حجم الملف كبير جداً. الحد الأقصى: ' . ($maxFileSize / 1024 / 1024) . 'MB');
    }
    
    // التحقق من نوع الملف
    if (!isAllowedFileType($originalName, $allowedTypes)) {
        sendErrorResponse('نوع الملف غير مسموح. الأنواع المسموحة: ' . implode(', ', $allowedTypes));
    }
    
    // التحقق من نوع MIME
    if (!validateMimeType($tempPath, $allowedTypes)) {
        sendErrorResponse('نوع الملف غير صحيح أو تم تعديله');
    }
    
    // إنشاء اسم ملف آمن
    $safeFilename = generateSafeFilename($originalName);
    $finalPath = $uploadPath . $safeFilename;
    
    // نقل الملف إلى المجلد النهائي
    if (!move_uploaded_file($tempPath, $finalPath)) {
        sendErrorResponse('فشل في حفظ الملف');
    }
    
    // حفظ معلومات الملف في قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // الحصول على معلومات إضافية
    $fileType = $_POST['fileType'] ?? 'general'; // نوع الملف (profile, document, trade, etc.)
    $description = $_POST['description'] ?? '';
    $relatedId = $_POST['relatedId'] ?? null; // معرف مرتبط (trade_id, user_id, etc.)
    
    $insertStmt = $connection->prepare("
        INSERT INTO uploaded_files (
            user_id, original_name, safe_filename, file_path, file_size, 
            file_type, mime_type, description, related_id, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $mimeType = mime_content_type($finalPath);
    
    $insertStmt->execute([
        $userId,
        $originalName,
        $safeFilename,
        $finalPath,
        $fileSize,
        $fileType,
        $mimeType,
        $description,
        $relatedId
    ]);
    
    $fileId = $connection->lastInsertId();
    
    // تسجيل النشاط
    try {
        $checkTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
        $checkTable->execute();
        
        if ($checkTable->rowCount() > 0) {
            $checkColumns = $connection->prepare("SHOW COLUMNS FROM activity_logs LIKE 'entity_type'");
            $checkColumns->execute();
            
            if ($checkColumns->rowCount() > 0) {
                $logStmt = $connection->prepare("
                    INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data)
                    VALUES (?, 'file_uploaded', 'file', ?, ?, ?, ?)
                ");
                
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                $uploadData = json_encode([
                    'upload_time' => date('Y-m-d H:i:s'),
                    'original_name' => $originalName,
                    'file_size' => $fileSize,
                    'file_type' => $fileType,
                    'mime_type' => $mimeType
                ]);
                
                $logStmt->execute([$userId, $fileId, $ipAddress, $userAgent, $uploadData]);
            }
        }
    } catch (Exception $logError) {
        // تجاهل أخطاء تسجيل النشاط
        error_log('Activity log error in upload: ' . $logError->getMessage());
    }
    
    // إرجاع معلومات الملف المرفوع
    sendSuccessResponse([
        'fileId' => $fileId,
        'originalName' => $originalName,
        'safeFilename' => $safeFilename,
        'fileSize' => $fileSize,
        'fileType' => $fileType,
        'mimeType' => $mimeType,
        'uploadTime' => date('Y-m-d H:i:s'),
        'downloadUrl' => '/api/files/download.php?id=' . $fileId
    ], 'تم رفع الملف بنجاح');
    
} catch (Exception $e) {
    // حذف الملف إذا تم رفعه ولكن فشل في حفظ البيانات
    if (isset($finalPath) && file_exists($finalPath)) {
        unlink($finalPath);
    }
    
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    // حذف الملف إذا تم رفعه ولكن فشل في حفظ البيانات
    if (isset($finalPath) && file_exists($finalPath)) {
        unlink($finalPath);
    }
    
    error_log('Database error in upload.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}
?>
