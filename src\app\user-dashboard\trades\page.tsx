'use client';

import { useState, useEffect } from 'react';
import { 
  Activity,
  Filter,
  Search,
  Calendar,
  Download,
  Eye,
  MessageSquare,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  TrendingUp,
  TrendingDown,
  User,
  ArrowUpDown
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';

interface Trade {
  id: string;
  type: 'buy' | 'sell';
  amount: number;
  currency: string;
  price: number;
  total: number;
  partner: {
    username: string;
    rating: number;
    isVerified: boolean;
  };
  status: 'active' | 'completed' | 'cancelled' | 'disputed';
  progress: number;
  createdAt: string;
  updatedAt: string;
  timeRemaining?: number;
  hasUnreadMessages?: boolean;
}

type TradeFilter = 'all' | 'active' | 'completed' | 'cancelled' | 'disputed';
type SortField = 'createdAt' | 'amount' | 'status' | 'partner';
type SortOrder = 'asc' | 'desc';

export default function TradesPage() {
  const { t, formatCurrency, formatDate } = useUserDashboardTranslation();
  const [trades, setTrades] = useState<Trade[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<TradeFilter>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<SortField>('createdAt');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: '',
    end: ''
  });

  // جلب الصفقات
  useEffect(() => {
    const fetchTrades = async () => {
      try {
        setLoading(true);
        
        // محاكاة البيانات - يجب استبدالها بـ API حقيقي
        const mockTrades: Trade[] = [
          {
            id: '1',
            type: 'sell',
            amount: 1000,
            currency: 'USDT',
            price: 3.75,
            total: 3750,
            partner: {
              username: 'أحمد_التاجر',
              rating: 4.8,
              isVerified: true
            },
            status: 'active',
            progress: 75,
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
            timeRemaining: 1800,
            hasUnreadMessages: true
          },
          {
            id: '2',
            type: 'buy',
            amount: 500,
            currency: 'USDT',
            price: 3.76,
            total: 1880,
            partner: {
              username: 'فاطمة_المتداولة',
              rating: 4.9,
              isVerified: true
            },
            status: 'completed',
            progress: 100,
            createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 20 * 60 * 60 * 1000).toISOString()
          },
          {
            id: '3',
            type: 'sell',
            amount: 750,
            currency: 'USDT',
            price: 3.74,
            total: 2805,
            partner: {
              username: 'محمد_الخبير',
              rating: 4.6,
              isVerified: false
            },
            status: 'cancelled',
            progress: 25,
            createdAt: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 46 * 60 * 60 * 1000).toISOString()
          }
        ];

        setTrades(mockTrades);
      } catch (error) {
        console.error('Error fetching trades:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTrades();
  }, [filter, dateRange]);

  // تصفية وترتيب الصفقات
  const filteredTrades = trades
    .filter(trade => {
      if (filter !== 'all' && trade.status !== filter) return false;
      if (searchTerm && !trade.partner.username.toLowerCase().includes(searchTerm.toLowerCase())) return false;
      return true;
    })
    .sort((a, b) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];
      
      if (sortField === 'partner') {
        aValue = a.partner.username;
        bValue = b.partner.username;
      }
      
      if (typeof aValue === 'string') {
        return sortOrder === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    });

  // الحصول على أيقونة الحالة
  const getStatusIcon = (status: Trade['status']) => {
    switch (status) {
      case 'active':
        return <Activity className="w-4 h-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-gray-500" />;
      case 'disputed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  // الحصول على لون الحالة
  const getStatusColor = (status: Trade['status']) => {
    switch (status) {
      case 'active':
        return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      case 'completed':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'cancelled':
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
      case 'disputed':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  // الحصول على نص الحالة
  const getStatusText = (status: Trade['status']) => {
    switch (status) {
      case 'active':
        return 'نشطة';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغية';
      case 'disputed':
        return 'متنازع عليها';
      default:
        return 'غير معروف';
    }
  };

  // تنسيق الوقت المتبقي
  const formatTimeRemaining = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}س ${minutes}د`;
    }
    return `${minutes}د`;
  };

  // إحصائيات سريعة
  const stats = {
    total: trades.length,
    active: trades.filter(t => t.status === 'active').length,
    completed: trades.filter(t => t.status === 'completed').length,
    disputed: trades.filter(t => t.status === 'disputed').length
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            {t('trades.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            إدارة ومتابعة جميع صفقاتك
          </p>
        </div>

        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors">
            <Download className="w-4 h-4" />
            <span>تصدير</span>
          </button>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <Activity className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <span className="text-sm text-gray-600 dark:text-gray-400">الإجمالي</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{stats.total}</p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <Activity className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <span className="text-sm text-gray-600 dark:text-gray-400">نشطة</span>
          </div>
          <p className="text-2xl font-bold text-blue-600 dark:text-blue-400 mt-1">{stats.active}</p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
            <span className="text-sm text-gray-600 dark:text-gray-400">مكتملة</span>
          </div>
          <p className="text-2xl font-bold text-green-600 dark:text-green-400 mt-1">{stats.completed}</p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
            <span className="text-sm text-gray-600 dark:text-gray-400">نزاعات</span>
          </div>
          <p className="text-2xl font-bold text-red-600 dark:text-red-400 mt-1">{stats.disputed}</p>
        </div>
      </div>

      {/* المرشحات والبحث */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* البحث */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 rtl:right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="البحث بالشريك..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 rtl:pr-10 pr-3 rtl:pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* المرشحات */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Filter className="w-4 h-4 text-gray-500" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as TradeFilter)}
                className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الصفقات</option>
                <option value="active">النشطة</option>
                <option value="completed">المكتملة</option>
                <option value="cancelled">الملغية</option>
                <option value="disputed">المتنازع عليها</option>
              </select>
            </div>

            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <ArrowUpDown className="w-4 h-4 text-gray-500" />
              <select
                value={`${sortField}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-');
                  setSortField(field as SortField);
                  setSortOrder(order as SortOrder);
                }}
                className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="createdAt-desc">الأحدث أولاً</option>
                <option value="createdAt-asc">الأقدم أولاً</option>
                <option value="amount-desc">المبلغ (الأعلى)</option>
                <option value="amount-asc">المبلغ (الأقل)</option>
                <option value="partner-asc">الشريك (أ-ي)</option>
                <option value="partner-desc">الشريك (ي-أ)</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* قائمة الصفقات */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        {loading ? (
          <div className="p-6">
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded-lg" />
                </div>
              ))}
            </div>
          </div>
        ) : filteredTrades.length > 0 ? (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredTrades.map((trade) => (
              <div key={trade.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    {/* نوع الصفقة */}
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                      trade.type === 'buy' ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30'
                    }`}>
                      {trade.type === 'buy' ? (
                        <TrendingUp className="w-6 h-6 text-green-600 dark:text-green-400" />
                      ) : (
                        <TrendingDown className="w-6 h-6 text-red-600 dark:text-red-400" />
                      )}
                    </div>

                    {/* تفاصيل الصفقة */}
                    <div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {trade.type === 'buy' ? 'شراء' : 'بيع'} {trade.amount} {trade.currency}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(trade.status)}`}>
                          {getStatusText(trade.status)}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          <User className="w-4 h-4" />
                          <span>{trade.partner.username}</span>
                          {trade.partner.isVerified && (
                            <CheckCircle className="w-3 h-3 text-green-500" />
                          )}
                        </div>
                        
                        <span>•</span>
                        
                        <span>{formatCurrency(trade.total, 'SAR')}</span>
                        
                        <span>•</span>
                        
                        <span>{formatDate(trade.createdAt, { 
                          day: 'numeric', 
                          month: 'short',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}</span>
                        
                        {trade.timeRemaining && trade.status === 'active' && (
                          <>
                            <span>•</span>
                            <div className="flex items-center space-x-1 rtl:space-x-reverse text-orange-600 dark:text-orange-400">
                              <Clock className="w-3 h-3" />
                              <span>{formatTimeRemaining(trade.timeRemaining)} متبقية</span>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* الإجراءات */}
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    {trade.hasUnreadMessages && (
                      <div className="flex items-center space-x-1 rtl:space-x-reverse text-blue-600 dark:text-blue-400">
                        <MessageSquare className="w-4 h-4" />
                        <span className="text-sm">رسائل جديدة</span>
                      </div>
                    )}
                    
                    <button className="flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors">
                      <Eye className="w-4 h-4" />
                      <span>عرض</span>
                    </button>
                  </div>
                </div>

                {/* شريط التقدم للصفقات النشطة */}
                {trade.status === 'active' && (
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs text-gray-500 dark:text-gray-400">التقدم</span>
                      <span className="text-xs font-medium text-gray-900 dark:text-white">
                        {trade.progress}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${trade.progress}%` }}
                      />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="p-12 text-center">
            <Activity className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              لا توجد صفقات
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {filter === 'all' 
                ? 'لم تقم بأي صفقات بعد'
                : `لا توجد صفقات ${getStatusText(filter as Trade['status'])}`
              }
            </p>
            <a
              href="/offers"
              className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <span>تصفح العروض</span>
            </a>
          </div>
        )}
      </div>
    </div>
  );
}
