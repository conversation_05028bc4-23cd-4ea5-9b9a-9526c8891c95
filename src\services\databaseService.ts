// خدمة قاعدة البيانات - نسخة نظيفة بدون محاكيات
import { 
  User, 
  Trade, 
  Offer, 
  Message, 
  Review, 
  Notification, 
  AnalyticsData,
  TradeStatus 
} from '@/types';

// واجهة الاستجابة من API
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// خدمة قاعدة البيانات
class DatabaseService {
  constructor() {
    console.log('DatabaseService initialized - using real database only');
  }

  // دالة مساعدة لإجراء طلبات HTTP
  private async makeRequest<T>(endpoint: string, options?: RequestInit): Promise<ApiResponse<T>> {
    // في وضع التطوير، استخدم البيانات المحلية مباشرة
    const isDevelopment = process.env.NODE_ENV === 'development' ||
                         typeof window !== 'undefined' && window.location.hostname === 'localhost';

    if (isDevelopment) {
      console.log(`Development mode: Using local data for ${endpoint}`);
      return this.getLocalData<T>(endpoint);
    }

    try {
      // تحديد URL الصحيح للـ API
      const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost/ikaros-p2p/api';
      const fullUrl = `${apiUrl}${endpoint}`;

      console.log(`Making API request to: ${fullUrl}`);

      // استخدام fetch مع إعدادات CORS محسنة
      const fetchOptions: RequestInit = {
        method: options?.method || 'GET',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          ...options?.headers,
        },
        ...options,
      };

      const response = await fetch(fullUrl, fetchOptions);

      // التحقق من نوع المحتوى
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const textResponse = await response.text();
        console.error(`Non-JSON response from ${endpoint}:`, textResponse);
        // في حالة عدم وجود JSON، استخدم البيانات المحلية
        return this.getLocalData<T>(endpoint);
      }

      const data = await response.json();

      if (!response.ok) {
        console.error(`HTTP error ${response.status} for ${endpoint}:`, data);
        // في حالة خطأ HTTP، استخدم البيانات المحلية
        return this.getLocalData<T>(endpoint);
      }

      console.log(`API response for ${endpoint}:`, data);

      // التأكد من أن الاستجابة تحتوي على البيانات المطلوبة
      if (data.success === false) {
        console.warn(`API returned success: false for ${endpoint}, using local data`);
        return this.getLocalData<T>(endpoint);
      }

      return data;
    } catch (error) {
      console.error(`Database request failed for ${endpoint}:`, error);
      console.log(`Falling back to local data for ${endpoint}`);
      return this.getLocalData<T>(endpoint);
    }
  }

  // دالة للحصول على البيانات المحلية حسب النوع
  private getLocalData<T>(endpoint: string): ApiResponse<T> {
    console.log(`Getting local data for endpoint: ${endpoint}`);

    if (endpoint.includes('/users/')) {
      return {
        success: true,
        data: this.getDefaultUsers() as T
      };
    }

    if (endpoint.includes('/trades/')) {
      return {
        success: true,
        data: this.getDefaultTrades() as T
      };
    }

    if (endpoint.includes('/admin/admins')) {
      return {
        success: true,
        data: this.getDefaultAdmins() as T
      };
    }

    if (endpoint.includes('/admin/disputes')) {
      return {
        success: true,
        data: this.getDefaultDisputes() as T
      };
    }

    if (endpoint.includes('/admin/analytics')) {
      return {
        success: true,
        data: this.getDefaultAnalytics() as T
      };
    }

    // بيانات افتراضية عامة
    return {
      success: true,
      data: [] as T
    };
  }



  // المستخدمون
  async getUsers(): Promise<User[]> {
    console.log('Getting users - using local data in development mode');
    return this.getDefaultUsers();
  }

  // بيانات افتراضية للمستخدمين
  private getDefaultUsers(): User[] {
    return [
      {
        id: '1',
        fullName: 'مستخدم تجريبي 1',
        email: '<EMAIL>',
        phone: '+966501234567',
        rating: 4.5,
        totalTrades: 5,
        completionRate: 80,
        isVerified: true,
        isOnline: true,
        country: 'السعودية',
        joinedAt: new Date('2024-01-01'),
        lastSeen: new Date()
      },
      {
        id: '2',
        fullName: 'مدير النظام',
        email: '<EMAIL>',
        phone: '+966501234568',
        rating: 5.0,
        totalTrades: 0,
        completionRate: 100,
        isVerified: true,
        isOnline: true,
        country: 'السعودية',
        joinedAt: new Date('2024-01-01'),
        lastSeen: new Date()
      }
    ];
  }

  async getUserById(userId: string): Promise<User | null> {
    const response = await this.makeRequest<User>(`/users/profile.php?user_id=${userId}`);
    return response.data || null;
  }

  async getCurrentUser(): Promise<User | null> {
    const response = await this.makeRequest<User>('/users/profile.php?current=true');
    return response.data || null;
  }

  // المدراء
  async getAdmins(): Promise<any[]> {
    try {
      console.log('Fetching admins from API...');
      const response = await this.makeRequest<any[]>('/admin/admins.php');

      if (response.success && response.data) {
        console.log(`Successfully loaded ${response.data.length} admins`);
        return response.data;
      } else {
        console.error('Failed to load admins:', response.error);
        // في حالة فشل API، إرجاع بيانات افتراضية للتطوير
        return this.getDefaultAdmins();
      }
    } catch (error) {
      console.error('Error in getAdmins:', error);
      return this.getDefaultAdmins();
    }
  }

  // بيانات افتراضية للمدراء في حالة فشل API
  private getDefaultAdmins(): any[] {
    return [
      {
        id: 1,
        admin_id: 1,
        user_id: 1,
        username: 'admin',
        email: '<EMAIL>',
        full_name: 'مدير النظام',
        wallet_address: '******************************************',
        admin_role: 'super_admin',
        permissions: ['super_admin', 'manage_users', 'manage_trades', 'resolve_disputes', 'manage_contracts', 'view_analytics', 'manage_settings', 'emergency_actions', 'manage_admins'],
        is_active: true,
        is_verified: true,
        created_at: new Date().toISOString(),
        last_login: new Date().toISOString()
      }
    ];
  }

  // إنشاء مدير جديد
  async createAdmin(adminData: any): Promise<any> {
    try {
      console.log('Creating new admin:', adminData);
      const response = await this.makeRequest('/admin/create-admin.php', {
        method: 'POST',
        body: JSON.stringify(adminData)
      });

      if (response.success) {
        console.log('Admin created successfully:', response.data);
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to create admin');
      }
    } catch (error) {
      console.error('Error creating admin:', error);
      throw error;
    }
  }

  // حذف مدير
  async deleteAdmin(adminId: string): Promise<boolean> {
    try {
      console.log('Deleting admin:', adminId);
      const response = await this.makeRequest(`/admin/delete-admin.php`, {
        method: 'DELETE',
        body: JSON.stringify({ admin_id: adminId })
      });

      if (response.success) {
        console.log('Admin deleted successfully');
        return true;
      } else {
        throw new Error(response.error || 'Failed to delete admin');
      }
    } catch (error) {
      console.error('Error deleting admin:', error);
      throw error;
    }
  }

  // النزاعات
  async getDisputes(): Promise<any[]> {
    try {
      console.log('Fetching disputes - using local data in development mode');
      // في وضع التطوير، استخدم البيانات المحلية مباشرة
      return this.getDefaultDisputes();
    } catch (error) {
      console.error('Error in getDisputes:', error);
      return this.getDefaultDisputes();
    }
  }

  // بيانات افتراضية للنزاعات
  private getDefaultDisputes(): any[] {
    return [
      {
        id: 1,
        tradeId: 1,
        reason: 'عدم استلام الدفع',
        status: 'pending',
        amount: 1000,
        currency: 'SAR',
        stablecoin: 'USDT',
        sellerUsername: 'user1',
        buyerUsername: 'user2',
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        resolvedAt: null,
        adminNotes: null
      },
      {
        id: 2,
        tradeId: 2,
        reason: 'مشكلة في التحويل',
        status: 'resolved',
        amount: 500,
        currency: 'SAR',
        stablecoin: 'USDT',
        sellerUsername: 'user3',
        buyerUsername: 'user4',
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        resolvedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        adminNotes: 'تم حل النزاع لصالح البائع'
      }
    ];
  }

  // بيانات افتراضية للتحليلات
  private getDefaultAnalytics(): AnalyticsData {
    return {
      totalUsers: 156,
      activeUsers: 23,
      totalTrades: 45,
      totalVolume: 125000,
      platformRevenue: 1875,
      activeOffers: 12,
      completedTrades: 38,
      averageRating: 4.2,
      monthlyGrowth: 12.5,
      dailyStats: Array.from({ length: 7 }, (_, i) => ({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        trades: Math.floor(Math.random() * 10) + 5,
        volume: Math.floor(Math.random() * 10000) + 5000,
        users: Math.floor(Math.random() * 20) + 10
      })).reverse()
    };
  }

  // العروض
  async getOffers(filters?: {
    type?: 'buy' | 'sell';
    currency?: string;
    country?: string;
    paymentMethod?: string;
    minAmount?: number;
    maxAmount?: number;
  }): Promise<Offer[]> {
    const queryParams = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/offers/index.php${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await this.makeRequest<Offer[]>(endpoint);
    return response.data || [];
  }

  async getOfferById(offerId: string): Promise<Offer | null> {
    const response = await this.makeRequest<Offer>(`/offers/index.php?id=${offerId}`);
    return response.data || null;
  }

  async createOffer(offerData: Partial<Offer>): Promise<Offer | null> {
    const response = await this.makeRequest<Offer>('/offers/index.php', {
      method: 'POST',
      body: JSON.stringify(offerData),
    });
    return response.data || null;
  }

  // الصفقات
  async getTrades(_userId?: string): Promise<Trade[]> {
    console.log('Getting trades - using local data in development mode');
    return this.getDefaultTrades();
  }

  // بيانات افتراضية للصفقات
  private getDefaultTrades(): Trade[] {
    return [
      {
        id: '1',
        offerId: '1',
        sellerId: '1',
        buyerId: '2',
        amount: 1000,
        price: 3.75,
        totalAmount: 3750,
        currency: 'SAR',
        stablecoin: 'USDT',
        paymentMethod: 'تحويل بنكي',
        status: TradeStatus.COMPLETED,
        createdAt: new Date('2024-06-01'),
        updatedAt: new Date('2024-06-01'),
        completedAt: new Date('2024-06-01'),
        messages: [],
        blockchain_trade_id: null,
        offer_blockchain_id: null,
        contract_status: 'Completed',
        platform_fee: 18.75,
        net_amount: 981.25
      },
      {
        id: '2',
        offerId: '2',
        sellerId: '2',
        buyerId: '1',
        amount: 500,
        price: 3.75,
        totalAmount: 1875,
        currency: 'SAR',
        stablecoin: 'USDT',
        paymentMethod: 'تحويل بنكي',
        status: TradeStatus.PAYMENT_SENT,
        createdAt: new Date('2024-06-28'),
        updatedAt: new Date('2024-06-28'),
        messages: [],
        blockchain_trade_id: null,
        offer_blockchain_id: null,
        contract_status: 'PaymentSent',
        platform_fee: 9.375,
        net_amount: 490.625
      }
    ];
  }

  async getTradeById(tradeId: string): Promise<Trade | null> {
    const response = await this.makeRequest<Trade>(`/trades/index.php?id=${tradeId}`);
    return response.data || null;
  }

  async createTrade(tradeData: Partial<Trade>): Promise<Trade | null> {
    const response = await this.makeRequest<Trade>('/trades/index.php', {
      method: 'POST',
      body: JSON.stringify(tradeData),
    });
    return response.data || null;
  }

  async updateTradeStatus(tradeId: string, updates: any): Promise<boolean> {
    const response = await this.makeRequest<any>(`/trades/update-status.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ trade_id: tradeId, ...updates })
    });
    return response.success;
  }

  // الرسائل
  async getMessages(tradeId: string): Promise<Message[]> {
    const response = await this.makeRequest<Message[]>(`/trades/messages.php?trade_id=${tradeId}`);
    return response.data || [];
  }

  async sendMessage(tradeId: string, messageData: Partial<Message>): Promise<Message | null> {
    const response = await this.makeRequest<Message>(`/trades/messages.php`, {
      method: 'POST',
      body: JSON.stringify({ trade_id: tradeId, ...messageData }),
    });
    return response.data || null;
  }

  // التقييمات
  async getReviews(userId?: string): Promise<Review[]> {
    const endpoint = userId ? `/reviews/index.php?user_id=${userId}` : '/reviews/index.php';
    const response = await this.makeRequest<Review[]>(endpoint);
    return response.data || [];
  }

  // الإشعارات
  async getNotifications(userId?: string): Promise<Notification[]> {
    if (!userId) {
      console.warn('getNotifications: لم يتم توفير معرف المستخدم');
      return [];
    }

    const endpoint = `/notifications/index.php?user_id=${userId}`;
    const response = await this.makeRequest<Notification[]>(endpoint);
    return response.data || [];
  }

  // التحليلات
  async getAnalyticsData(): Promise<AnalyticsData> {
    console.log('Getting analytics - using local data in development mode');
    return this.getDefaultAnalytics();
  }

  // إحصائيات محسنة للإدارة
  async getAdminStats(): Promise<any> {
    try {
      const users = await this.getUsers();
      const trades = await this.getTrades();
      const analytics = await this.getAnalyticsData();
      
      return {
        totalUsers: users.length,
        activeUsers: users.filter(u => u.isOnline).length,
        totalTrades: trades.length,
        activeTrades: trades.filter(t => [TradeStatus.CREATED, TradeStatus.PAYMENT_SENT, TradeStatus.BUYER_JOINED].includes(t.status)).length,
        completedTrades: trades.filter(t => t.status === TradeStatus.COMPLETED).length,
        disputedTrades: trades.filter(t => t.status === TradeStatus.DISPUTED).length,
        totalVolume: analytics.totalVolume,
        platformRevenue: analytics.platformRevenue,
        recentActivity: {
          completedTrades: trades.filter(t => t.status === TradeStatus.COMPLETED).slice(0, 3),
          newUsers: users.slice(-3),
          disputes: trades.filter(t => t.status === TradeStatus.DISPUTED).slice(0, 3)
        }
      };
    } catch (error) {
      console.error('Error getting admin stats:', error);
      return {
        totalUsers: 0,
        activeUsers: 0,
        totalTrades: 0,
        activeTrades: 0,
        completedTrades: 0,
        disputedTrades: 0,
        totalVolume: 0,
        platformRevenue: 0,
        recentActivity: {
          completedTrades: [],
          newUsers: [],
          disputes: []
        }
      };
    }
  }

  // فحص حالة الاتصال بقاعدة البيانات
  async checkConnection(): Promise<boolean> {
    try {
      const response = await this.makeRequest('/test-connection.php');
      return response.success;
    } catch {
      return false;
    }
  }
}

export const databaseService = new DatabaseService();
export default databaseService;
