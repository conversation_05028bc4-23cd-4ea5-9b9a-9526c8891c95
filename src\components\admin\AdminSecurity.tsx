'use client';

import React, { useState, useEffect } from 'react';
import {
  Shield,
  Lock,
  Key,
  Eye,
  EyeOff,
  Smartphone,
  Users,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Globe,
  Monitor,
  FileText,
  Download,
  Upload,
  RefreshCw,
  RotateCcw,
  Settings,
  UserCheck,
  UserX,
  LogIn,
  LogOut,
  Fingerprint,
  Scan,
  QrCode,
  Copy,
  Save,
  Trash2,
  Plus,
  Edit,
  Search,
  Filter,
  Calendar,
  MapPin,
  Wifi,
  WifiOff,
  Database,
  Server,
  HardDrive,
  Zap,
  Bell,
  Mail,
  MessageSquare,
  Phone,
  AlertCircle,
  Info,
  CheckSquare,
  Square,
  MoreHorizontal,
  ExternalLink,
  History,
  Timer,
  Target,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  LineChart
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';

interface SecurityEvent {
  id: string;
  type: 'login' | 'logout' | 'failed_login' | 'password_change' | 'role_change' | 'suspicious_activity' | 'security_breach';
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'new' | 'investigating' | 'resolved' | 'false_positive';
  user: string;
  action: string;
  details: string;
  ipAddress: string;
  userAgent: string;
  location?: string;
  timestamp: string;
  resolved_by?: string;
  resolved_at?: string;
}

interface AdminSession {
  id: string;
  userId: string;
  username: string;
  email: string;
  role: string;
  ipAddress: string;
  userAgent: string;
  location?: string;
  loginTime: string;
  lastActivity: string;
  duration: number;
  isCurrentSession: boolean;
  isSuspicious: boolean;
}

interface AdminRole {
  id: string;
  name: string;
  displayName: string;
  description: string;
  permissions: string[];
  userCount: number;
  isSystem: boolean;
  createdAt: string;
  updatedAt: string;
}

interface AdminSecurityProps {
  className?: string;
}

export default function AdminSecurity({ className = '' }: AdminSecurityProps) {
  const { t, language, isRTL, getDirectionClasses, formatNumber, formatDate, formatRelativeTime } = useAdminTranslation();
  const dirClasses = getDirectionClasses();
  
  const [activeTab, setActiveTab] = useState('overview');
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [activeSessions, setActiveSessions] = useState<AdminSession[]>([]);
  const [roles, setRoles] = useState<AdminRole[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<SecurityEvent | null>(null);
  const [showEventDetails, setShowEventDetails] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  const [backupCodes, setBackupCodes] = useState<string[]>([]);

  // Mock data - replace with real API calls
  useEffect(() => {
    // Mock security events
    setSecurityEvents([
      {
        id: '1',
        type: 'failed_login',
        severity: 'medium',
        status: 'new',
        user: '<EMAIL>',
        action: 'Failed login attempt',
        details: 'Multiple failed login attempts from suspicious IP',
        ipAddress: '************0',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        location: 'New York, USA',
        timestamp: '2024-01-20T10:30:00Z'
      },
      {
        id: '2',
        type: 'suspicious_activity',
        severity: 'high',
        status: 'investigating',
        user: '<EMAIL>',
        action: 'Unusual access pattern detected',
        details: 'Access from new device and location',
        ipAddress: '************',
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
        location: 'London, UK',
        timestamp: '2024-01-20T09:15:00Z'
      },
      {
        id: '3',
        type: 'role_change',
        severity: 'high',
        status: 'resolved',
        user: '<EMAIL>',
        action: 'Role permissions modified',
        details: 'Added financial reports permission to moderator role',
        ipAddress: '********',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
        timestamp: '2024-01-20T08:45:00Z',
        resolved_by: '<EMAIL>',
        resolved_at: '2024-01-20T09:00:00Z'
      }
    ]);

    // Mock active sessions
    setActiveSessions([
      {
        id: '1',
        userId: '1',
        username: 'admin',
        email: '<EMAIL>',
        role: 'Super Admin',
        ipAddress: '************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        location: 'New York, USA',
        loginTime: '2024-01-20T08:00:00Z',
        lastActivity: '2024-01-20T10:30:00Z',
        duration: 9000, // seconds
        isCurrentSession: true,
        isSuspicious: false
      },
      {
        id: '2',
        userId: '2',
        username: 'moderator',
        email: '<EMAIL>',
        role: 'Moderator',
        ipAddress: '************',
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
        location: 'London, UK',
        loginTime: '2024-01-20T09:00:00Z',
        lastActivity: '2024-01-20T10:25:00Z',
        duration: 5100,
        isCurrentSession: false,
        isSuspicious: true
      }
    ]);

    // Mock roles
    setRoles([
      {
        id: '1',
        name: 'super_admin',
        displayName: 'Super Admin',
        description: 'Full system access with all permissions',
        permissions: ['*'],
        userCount: 2,
        isSystem: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '2',
        name: 'admin',
        displayName: 'Admin',
        description: 'Administrative access with most permissions',
        permissions: ['user_management', 'trade_management', 'dispute_resolution', 'system_settings'],
        userCount: 5,
        isSystem: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-15T10:30:00Z'
      },
      {
        id: '3',
        name: 'moderator',
        displayName: 'Moderator',
        description: 'Content moderation and user support',
        permissions: ['user_management', 'dispute_resolution', 'support'],
        userCount: 8,
        isSystem: false,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-20T08:45:00Z'
      }
    ]);
  }, []);

  const tabs = [
    { id: 'overview', label: t('common.overview'), icon: Shield },
    { id: 'sessions', label: t('auth.sessions.title'), icon: Monitor },
    { id: 'roles', label: t('auth.roles.title'), icon: Users },
    { id: 'logs', label: t('auth.logs.title'), icon: FileText },
    { id: 'alerts', label: t('auth.alerts.title'), icon: AlertTriangle },
    { id: 'settings', label: t('auth.security.title'), icon: Settings },
    { id: 'two-factor', label: t('auth.twoFactor.title'), icon: Smartphone }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      case 'high': return 'text-orange-600 dark:text-orange-400 bg-orange-100 dark:bg-orange-900/30';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30';
      case 'low': return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      default: return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'resolved': return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'investigating': return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      case 'false_positive': return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
      case 'new': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      default: return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'login': return LogIn;
      case 'logout': return LogOut;
      case 'failed_login': return UserX;
      case 'password_change': return Key;
      case 'role_change': return UserCheck;
      case 'suspicious_activity': return AlertTriangle;
      case 'security_breach': return Shield;
      default: return Activity;
    }
  };

  const terminateSession = (sessionId: string) => {
    setActiveSessions(prev => prev.filter(session => session.id !== sessionId));
    // In real app, make API call to terminate session
  };

  const terminateAllSessions = () => {
    setActiveSessions(prev => prev.filter(session => session.isCurrentSession));
    // In real app, make API call to terminate all sessions except current
  };

  const generateBackupCodes = () => {
    const codes = Array.from({ length: 10 }, () => 
      Math.random().toString(36).substring(2, 8).toUpperCase()
    );
    setBackupCodes(codes);
  };

  const downloadBackupCodes = () => {
    const content = backupCodes.join('\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'backup-codes.txt';
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className={`flex flex-col sm:flex-row sm:items-center justify-between gap-4 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
        <div className={isRTL ? 'text-right' : 'text-left'}>
          <h3 className={`text-xl font-bold text-gray-900 dark:text-white flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Shield className={`w-6 h-6 text-blue-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            {t('auth.title')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('auth.subtitle')}</p>
        </div>
        <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
            <Download className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('auth.logs.exportSecurityReport')}
          </button>
          <button className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
            <RefreshCw className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.refresh')}
          </button>
        </div>
      </div>

      {/* Security Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className={`flex space-x-8 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <Icon className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {tab.label}
                </div>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Security Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={isRTL ? 'text-right' : 'text-left'}>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('auth.sessions.activeSessions')}</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatNumber(activeSessions.length)}</p>
                  </div>
                  <Monitor className="w-8 h-8 text-blue-500" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={isRTL ? 'text-right' : 'text-left'}>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('auth.alerts.title')}</p>
                    <p className="text-2xl font-bold text-red-600 dark:text-red-400">{formatNumber(securityEvents.filter(e => e.status === 'new').length)}</p>
                  </div>
                  <AlertTriangle className="w-8 h-8 text-red-500" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={isRTL ? 'text-right' : 'text-left'}>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('auth.logs.failedLogins')}</p>
                    <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{formatNumber(securityEvents.filter(e => e.type === 'failed_login').length)}</p>
                  </div>
                  <UserX className="w-8 h-8 text-yellow-500" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={isRTL ? 'text-right' : 'text-left'}>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('auth.roles.title')}</p>
                    <p className="text-2xl font-bold text-green-600 dark:text-green-400">{formatNumber(roles.length)}</p>
                  </div>
                  <Users className="w-8 h-8 text-green-500" />
                </div>
              </div>
            </div>

            {/* Recent Security Events */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('auth.logs.title')}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Recent security events and activities</p>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {securityEvents.slice(0, 5).map((event) => {
                    const EventIcon = getEventIcon(event.type);
                    return (
                      <div key={event.id} className={`flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <div className={`w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center ${isRTL ? 'ml-4' : 'mr-4'}`}>
                            <EventIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                          </div>
                          <div className={isRTL ? 'text-right' : 'text-left'}>
                            <p className="font-medium text-gray-900 dark:text-white">{event.action}</p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">{event.user} • {formatRelativeTime(event.timestamp)}</p>
                          </div>
                        </div>
                        <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(event.severity)}`}>
                            {t(`auth.alerts.severity.${event.severity}`)}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(event.status)}`}>
                            {t(`auth.alerts.status.${event.status}`)}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Sessions Tab */}
        {activeTab === 'sessions' && (
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className={`p-6 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={isRTL ? 'text-right' : 'text-left'}>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('auth.sessions.activeSessions')}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{activeSessions.length} active sessions</p>
                </div>
                <button
                  onClick={terminateAllSessions}
                  className="flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors"
                >
                  <LogOut className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('auth.sessions.terminateAllSessions')}
                </button>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {activeSessions.map((session) => (
                    <div key={session.id} className={`p-4 border border-gray-200 dark:border-gray-600 rounded-lg ${session.isSuspicious ? 'border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/20' : ''}`}>
                      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <div className={`w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center ${isRTL ? 'ml-4' : 'mr-4'}`}>
                            <Monitor className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div className={isRTL ? 'text-right' : 'text-left'}>
                            <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                              <p className="font-medium text-gray-900 dark:text-white">{session.username}</p>
                              <span className="text-sm text-gray-500 dark:text-gray-400">({session.role})</span>
                              {session.isCurrentSession && (
                                <span className="px-2 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full text-xs font-medium">
                                  Current
                                </span>
                              )}
                              {session.isSuspicious && (
                                <span className="px-2 py-0.5 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 rounded-full text-xs font-medium">
                                  Suspicious
                                </span>
                              )}
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                              <p>{session.ipAddress} • {session.location}</p>
                              <p>Login: {formatDate(session.loginTime)} • Last: {formatRelativeTime(session.lastActivity)}</p>
                            </div>
                          </div>
                        </div>
                        <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <button
                            onClick={() => console.log('View session details')}
                            className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          {!session.isCurrentSession && (
                            <button
                              onClick={() => terminateSession(session.id)}
                              className="p-2 text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30"
                            >
                              <LogOut className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Roles Tab */}
        {activeTab === 'roles' && (
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className={`p-6 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={isRTL ? 'text-right' : 'text-left'}>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('auth.roles.title')}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Manage user roles and permissions</p>
                </div>
                <button className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
                  <Plus className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('auth.roles.createRole')}
                </button>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {roles.map((role) => (
                    <div key={role.id} className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
                      <div className={`flex items-center justify-between mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className={isRTL ? 'text-right' : 'text-left'}>
                          <h5 className="font-medium text-gray-900 dark:text-white">{role.displayName}</h5>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{role.userCount} users</p>
                        </div>
                        {role.isSystem && (
                          <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 rounded-full text-xs font-medium">
                            System
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{role.description}</p>
                      <div className="flex flex-wrap gap-1 mb-3">
                        {role.permissions.slice(0, 3).map((permission) => (
                          <span key={permission} className="px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs">
                            {permission === '*' ? 'All' : permission.replace('_', ' ')}
                          </span>
                        ))}
                        {role.permissions.length > 3 && (
                          <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded text-xs">
                            +{role.permissions.length - 3} more
                          </span>
                        )}
                      </div>
                      <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <button className="flex-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors">
                          {t('auth.roles.editRole')}
                        </button>
                        {!role.isSystem && (
                          <button className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors">
                            <Trash2 className="w-3 h-3" />
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Two-Factor Tab */}
        {activeTab === 'two-factor' && (
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('auth.twoFactor.title')}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('auth.twoFactor.subtitle')}</p>
              </div>
              <div className="p-6">
                <div className="space-y-6">
                  {/* 2FA Status */}
                  <div className={`flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Smartphone className={`w-8 h-8 text-blue-500 ${isRTL ? 'ml-4' : 'mr-4'}`} />
                      <div className={isRTL ? 'text-right' : 'text-left'}>
                        <p className="font-medium text-gray-900 dark:text-white">{t('auth.twoFactor.title')}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {twoFactorEnabled ? 'Enabled and active' : 'Not configured'}
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={() => setTwoFactorEnabled(!twoFactorEnabled)}
                      className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                        twoFactorEnabled
                          ? 'bg-red-600 hover:bg-red-700 text-white'
                          : 'bg-green-600 hover:bg-green-700 text-white'
                      }`}
                    >
                      {twoFactorEnabled ? t('auth.twoFactor.disable') : t('auth.twoFactor.enable')}
                    </button>
                  </div>

                  {/* Setup 2FA */}
                  {!twoFactorEnabled && (
                    <div className="space-y-4">
                      <div className="text-center">
                        <button
                          onClick={() => setShowQRCode(!showQRCode)}
                          className="flex items-center justify-center mx-auto px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
                        >
                          <QrCode className={`w-5 h-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                          {t('auth.twoFactor.setup')}
                        </button>
                      </div>

                      {showQRCode && (
                        <div className="text-center space-y-4">
                          <div className="w-48 h-48 bg-gray-200 dark:bg-gray-700 rounded-lg mx-auto flex items-center justify-center">
                            <QrCode className="w-16 h-16 text-gray-400" />
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{t('auth.twoFactor.appInstructions')}</p>
                          <div className="flex items-center justify-center gap-2">
                            <input
                              type="text"
                              value="JBSWY3DPEHPK3PXP"
                              readOnly
                              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm font-mono"
                            />
                            <button className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                              <Copy className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Backup Codes */}
                  {twoFactorEnabled && (
                    <div className="space-y-4">
                      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <h5 className="font-medium text-gray-900 dark:text-white">{t('auth.twoFactor.backupCodes')}</h5>
                        <button
                          onClick={generateBackupCodes}
                          className="flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors"
                        >
                          <RefreshCw className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                          {t('auth.twoFactor.generateBackupCodes')}
                        </button>
                      </div>

                      {backupCodes.length > 0 && (
                        <div className="space-y-3">
                          <div className="grid grid-cols-2 gap-2">
                            {backupCodes.map((code, index) => (
                              <div key={index} className="px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded font-mono text-sm text-center">
                                {code}
                              </div>
                            ))}
                          </div>
                          <button
                            onClick={downloadBackupCodes}
                            className="w-full flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                          >
                            <Download className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                            {t('auth.twoFactor.downloadBackupCodes')}
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Security Logs Tab */}
        {activeTab === 'logs' && (
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className={`p-6 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={isRTL ? 'text-right' : 'text-left'}>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('auth.logs.title')}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Monitor and analyze security activities</p>
                </div>
                <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                    <Filter className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    Filter
                  </button>
                  <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                    <Download className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {t('auth.logs.exportSecurityReport')}
                  </button>
                </div>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {securityEvents.map((event) => {
                    const EventIcon = getEventIcon(event.type);
                    return (
                      <div key={event.id} className={`p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer`}
                           onClick={() => {
                             setSelectedEvent(event);
                             setShowEventDetails(true);
                           }}>
                        <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <div className={`w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center ${isRTL ? 'ml-4' : 'mr-4'}`}>
                              <EventIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                            </div>
                            <div className={isRTL ? 'text-right' : 'text-left'}>
                              <p className="font-medium text-gray-900 dark:text-white">{event.action}</p>
                              <p className="text-sm text-gray-600 dark:text-gray-400">{event.user} • {event.ipAddress}</p>
                              <p className="text-xs text-gray-500 dark:text-gray-500">{formatDate(event.timestamp)}</p>
                            </div>
                          </div>
                          <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(event.severity)}`}>
                              {t(`auth.alerts.severity.${event.severity}`)}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(event.status)}`}>
                              {t(`auth.alerts.status.${event.status}`)}
                            </span>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Security Alerts Tab */}
        {activeTab === 'alerts' && (
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className={`p-6 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={isRTL ? 'text-right' : 'text-left'}>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('auth.alerts.title')}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('auth.alerts.subtitle')}</p>
                </div>
                <button className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
                  <Plus className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  Create Alert Rule
                </button>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {securityEvents.filter(e => e.status === 'new').map((event) => (
                    <div key={event.id} className={`p-4 border-2 rounded-lg ${
                      event.severity === 'critical' ? 'border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/20' :
                      event.severity === 'high' ? 'border-orange-300 dark:border-orange-700 bg-orange-50 dark:bg-orange-900/20' :
                      event.severity === 'medium' ? 'border-yellow-300 dark:border-yellow-700 bg-yellow-50 dark:bg-yellow-900/20' :
                      'border-blue-300 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/20'
                    }`}>
                      <div className={`flex items-center justify-between mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(event.severity)}`}>
                          {t(`auth.alerts.severity.${event.severity}`)}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">{formatRelativeTime(event.timestamp)}</span>
                      </div>
                      <h5 className="font-medium text-gray-900 dark:text-white mb-2">{event.action}</h5>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{event.details}</p>
                      <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <button className="flex-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors">
                          {t('auth.alerts.actions.investigate')}
                        </button>
                        <button className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors">
                          {t('auth.alerts.actions.resolve')}
                        </button>
                        <button className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition-colors">
                          {t('auth.alerts.actions.dismiss')}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Security Settings Tab */}
        {activeTab === 'settings' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Password Policy */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('auth.security.passwordPolicy.title')}</h4>
                </div>
                <div className="p-6 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {t('auth.security.passwordPolicy.minLength')}
                    </label>
                    <input
                      type="number"
                      min="6"
                      max="32"
                      defaultValue="8"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded" defaultChecked />
                      <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                        {t('auth.security.passwordPolicy.requireUppercase')}
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded" defaultChecked />
                      <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                        {t('auth.security.passwordPolicy.requireLowercase')}
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded" defaultChecked />
                      <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                        {t('auth.security.passwordPolicy.requireNumbers')}
                      </span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded" />
                      <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                        {t('auth.security.passwordPolicy.requireSymbols')}
                      </span>
                    </label>
                  </div>
                </div>
              </div>

              {/* Login Security */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{t('auth.security.loginSecurity.title')}</h4>
                </div>
                <div className="p-6 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {t('auth.security.loginSecurity.maxAttempts')}
                    </label>
                    <input
                      type="number"
                      min="3"
                      max="20"
                      defaultValue="5"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {t('auth.security.loginSecurity.lockoutDuration')} (minutes)
                    </label>
                    <input
                      type="number"
                      min="5"
                      max="1440"
                      defaultValue="30"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {t('auth.security.loginSecurity.sessionTimeout')} (minutes)
                    </label>
                    <input
                      type="number"
                      min="15"
                      max="1440"
                      defaultValue="60"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className={`text-sm text-gray-700 dark:text-gray-300 ${isRTL ? 'mr-2' : 'ml-2'}`}>
                      {t('auth.security.loginSecurity.requireTwoFactor')}
                    </span>
                  </label>
                </div>
              </div>
            </div>

            {/* Save Settings */}
            <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <button className="flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
                <Save className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                {t('settings.actions.save')}
              </button>
              <button className="flex items-center px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors">
                <RotateCcw className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                {t('settings.actions.reset')}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Event Details Modal */}
      {showEventDetails && selectedEvent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className={`flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{t('auth.logs.logDetails')}</h3>
              <button
                onClick={() => setShowEventDetails(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('auth.logs.action')}</label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">{selectedEvent.action}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('auth.logs.timestamp')}</label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">{formatDate(selectedEvent.timestamp)}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">User</label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">{selectedEvent.user}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">IP Address</label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">{selectedEvent.ipAddress}</p>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">{t('auth.logs.details')}</label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">{selectedEvent.details}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">User Agent</label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white break-all">{selectedEvent.userAgent}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
