'use client';

import React, { useState, useEffect } from 'react';
import { Bell } from 'lucide-react';
import NotificationCenter, { Notification } from './NotificationCenter';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuth } from '@/contexts/AuthContext';

import { databaseService } from '@/services/databaseService';

interface NotificationButtonProps {
  className?: string;
}

export default function NotificationButton({ className = '' }: NotificationButtonProps) {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // تحميل الإشعارات من قاعدة البيانات
  useEffect(() => {
    loadNotifications();
  }, []);

  useEffect(() => {
    const count = notifications.filter(n => !n.isRead).length;
    setUnreadCount(count);
  }, [notifications]);

  const loadNotifications = async () => {
    try {
      const dbNotifications = await databaseService.getNotifications();
      // تحويل البيانات من قاعدة البيانات إلى تنسيق المكون
      const formattedNotifications: Notification[] = dbNotifications.map(notification => ({
        id: notification.id,
        type: notification.type as any,
        priority: 'medium' as any, // قيمة افتراضية
        title: notification.title,
        message: notification.message,
        timestamp: notification.createdAt || notification.timestamp || new Date().toISOString(),
        isRead: notification.isRead,
        actionUrl: notification.actionUrl,
        actionLabel: notification.actionLabel
      }));

      setNotifications(formattedNotifications);
    } catch (error) {
      console.error('Error loading notifications:', error);
      // في حالة الخطأ، استخدم البيانات الافتراضية
      setNotifications([]);
    }
  };



  const handleMarkAsRead = async (id: string) => {
    try {
      // تحديث الحالة محلياً أولاً
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === id
            ? { ...notification, isRead: true }
            : notification
        )
      );

      // محاولة تحديث قاعدة البيانات
      const response = await fetch(`/api/notifications/index.php`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: id,
          is_read: true
        })
      });

      if (!response.ok) {
        console.warn('Failed to update notification in database');
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      // تحديث الحالة محلياً أولاً
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, isRead: true }))
      );

      // محاولة تحديث قاعدة البيانات
      const response = await fetch(`/api/notifications/index.php`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mark_all_read: true,
          user_id: user?.id
        })
      });

      if (!response.ok) {
        console.warn('Failed to mark all notifications as read in database');
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const handleDeleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  const handleNotificationAction = (notification: Notification) => {
    if (notification.actionUrl) {
      // التنقل إلى الرابط
      window.location.href = notification.actionUrl;
    }
    
    // تحديد الإشعار كمقروء
    handleMarkAsRead(notification.id);
    
    // إغلاق مركز الإشعارات
    setIsOpen(false);
  };

  return (
    <>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`relative p-3 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900/20 group ${className}`}
        title="الإشعارات"
      >
        <Bell className={`w-5 h-5 transition-transform duration-200 ${isOpen ? 'scale-110' : 'group-hover:scale-105'}`} />

        {/* عداد الإشعارات غير المقروءة */}
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 rtl:-right-1 ltr:-left-1 min-w-[20px] h-[20px] bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold rounded-full flex items-center justify-center shadow-lg animate-pulse">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}

        {/* نقطة الإشعار الجديد */}
        {unreadCount > 0 && (
          <span className="absolute top-1 right-1 rtl:right-1 ltr:left-1 w-2 h-2 bg-red-400 rounded-full animate-ping"></span>
        )}

        {/* تأثير الخلفية */}
        {unreadCount > 0 && (
          <span className="absolute inset-0 bg-blue-100 dark:bg-blue-900/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
        )}
      </button>

      {/* مركز الإشعارات */}
      <NotificationCenter
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        notifications={notifications}
        onMarkAsRead={handleMarkAsRead}
        onMarkAllAsRead={handleMarkAllAsRead}
        onDeleteNotification={handleDeleteNotification}
        onNotificationAction={handleNotificationAction}
      />
    </>
  );
}

// Hook لاستخدام الإشعارات في أي مكان
export function useNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date()
    };

    setNotifications(prev => [newNotification, ...prev]);
    return newNotification.id;
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, isRead: true } : n)
    );
  };

  const clearAll = () => {
    setNotifications([]);
  };

  const getUnreadCount = () => {
    return notifications.filter(n => !n.isRead).length;
  };

  return {
    notifications,
    addNotification,
    removeNotification,
    markAsRead,
    clearAll,
    unreadCount: getUnreadCount()
  };
}

// مكون إشعار سريع للاستخدام في أي مكان
export function QuickNotification({ 
  type, 
  title, 
  message, 
  onClose 
}: {
  type: Notification['type'];
  title: string;
  message: string;
  onClose: () => void;
}) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onClose, 300); // انتظار انتهاء الأنيميشن
    }, 5000);

    return () => clearTimeout(timer);
  }, [onClose]);

  if (!isVisible) return null;

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'trade':
        return 'bg-green-500';
      case 'security':
        return 'bg-red-500';
      case 'payment':
        return 'bg-blue-500';
      case 'kyc':
        return 'bg-purple-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className={`fixed top-4 right-4 rtl:right-4 ltr:left-4 max-w-sm bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 z-50 transform transition-all duration-300 ${
      isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
    }`}>
      <div className="flex items-start space-x-3 rtl:space-x-reverse">
        <div className={`w-2 h-2 rounded-full ${getTypeColor(type)} mt-2`}></div>
        <div className="flex-1">
          <h4 className="font-medium text-gray-900 dark:text-white text-sm">
            {title}
          </h4>
          <p className="text-gray-600 dark:text-gray-300 text-sm mt-1">
            {message}
          </p>
        </div>
        <button
          onClick={() => {
            setIsVisible(false);
            setTimeout(onClose, 300);
          }}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          ×
        </button>
      </div>
    </div>
  );
}
