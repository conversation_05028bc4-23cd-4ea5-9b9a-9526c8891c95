<?php
/**
 * API endpoint لتحديث حالة الصفقات من العقد الذكي
 * Trade Status Update API Endpoint for Smart Contract Integration
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        // التحقق من البيانات المطلوبة
        if (!isset($input['blockchain_trade_id']) || !isset($input['status'])) {
            throw new Exception('معرف الصفقة والحالة مطلوبان');
        }
        
        $blockchainTradeId = $input['blockchain_trade_id'];
        $status = $input['status'];
        $contractStatus = $input['contract_status'] ?? null;
        $transactionHash = $input['transaction_hash'] ?? null;
        $lastSyncAt = $input['last_sync_at'] ?? date('Y-m-d H:i:s');
        
        // التحقق من صحة الحالة
        $validStatuses = ['created', 'joined', 'payment_sent', 'payment_received', 'completed', 'cancelled', 'disputed'];
        if (!in_array($status, $validStatuses)) {
            throw new Exception('حالة الصفقة غير صحيحة');
        }
        
        // البحث عن الصفقة
        $stmt = $connection->prepare("
            SELECT id, status, contract_status 
            FROM trades 
            WHERE blockchain_trade_id = ?
        ");
        $stmt->execute([$blockchainTradeId]);
        $trade = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$trade) {
            throw new Exception('الصفقة غير موجودة');
        }
        
        // تحضير حقول التحديث
        $updateFields = ['status = ?', 'last_sync_at = ?'];
        $params = [$status, $lastSyncAt];
        
        if ($contractStatus) {
            $updateFields[] = 'contract_status = ?';
            $params[] = $contractStatus;
        }
        
        // تحديث معلومات المعاملة حسب نوع الحدث
        switch ($status) {
            case 'joined':
                if ($transactionHash) {
                    $updateFields[] = 'join_transaction_hash = ?';
                    $params[] = $transactionHash;
                    $updateFields[] = 'contract_joined_at = ?';
                    $params[] = $lastSyncAt;
                }
                break;
                
            case 'payment_sent':
                if ($transactionHash) {
                    $updateFields[] = 'payment_sent_transaction_hash = ?';
                    $params[] = $transactionHash;
                    $updateFields[] = 'contract_payment_sent_at = ?';
                    $params[] = $lastSyncAt;
                }
                break;
                
            case 'completed':
                if ($transactionHash) {
                    $updateFields[] = 'complete_transaction_hash = ?';
                    $params[] = $transactionHash;
                    $updateFields[] = 'contract_completed_at = ?';
                    $params[] = $lastSyncAt;
                    $updateFields[] = 'completed_at = ?';
                    $params[] = $lastSyncAt;
                }
                break;
                
            case 'cancelled':
                if ($transactionHash) {
                    $updateFields[] = 'cancel_transaction_hash = ?';
                    $params[] = $transactionHash;
                }
                break;
        }
        
        // تحديث حالة المزامنة
        $updateFields[] = 'sync_status = ?';
        $params[] = 'synced';
        
        $params[] = $trade['id'];
        
        // تنفيذ التحديث
        $stmt = $connection->prepare("
            UPDATE trades 
            SET " . implode(', ', $updateFields) . "
            WHERE id = ?
        ");
        
        $stmt->execute($params);
        
        // تحديث إحصائيات المستخدمين إذا تمت الصفقة
        if ($status === 'completed' && $trade['status'] !== 'completed') {
            $this->updateUserStats($connection, $trade['id']);
        }
        
        // إنشاء سجل نشاط
        $this->logActivity($connection, $trade['id'], $status, $transactionHash);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث حالة الصفقة بنجاح',
            'data' => [
                'trade_id' => $trade['id'],
                'blockchain_trade_id' => $blockchainTradeId,
                'old_status' => $trade['status'],
                'new_status' => $status,
                'transaction_hash' => $transactionHash
            ]
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    error_log('Database error in trades/update-status.php: ' . $e->getMessage());
}

/**
 * تحديث إحصائيات المستخدمين
 */
function updateUserStats($connection, $tradeId) {
    try {
        // جلب معلومات الصفقة
        $stmt = $connection->prepare("
            SELECT seller_id, buyer_id, total_value 
            FROM trades 
            WHERE id = ?
        ");
        $stmt->execute([$tradeId]);
        $trade = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($trade) {
            // تحديث إحصائيات البائع
            $stmt = $connection->prepare("
                UPDATE users 
                SET completed_trades = completed_trades + 1,
                    total_volume = total_volume + ?
                WHERE id = ?
            ");
            $stmt->execute([$trade['total_value'], $trade['seller_id']]);
            
            // تحديث إحصائيات المشتري
            $stmt = $connection->prepare("
                UPDATE users 
                SET completed_trades = completed_trades + 1,
                    total_volume = total_volume + ?
                WHERE id = ?
            ");
            $stmt->execute([$trade['total_value'], $trade['buyer_id']]);
        }
    } catch (Exception $e) {
        error_log('Error updating user stats: ' . $e->getMessage());
    }
}

/**
 * تسجيل النشاط
 */
function logActivity($connection, $tradeId, $status, $transactionHash) {
    try {
        $stmt = $connection->prepare("
            INSERT INTO activity_logs (
                action, entity_type, entity_id, data, created_at
            ) VALUES (?, 'trade', ?, ?, CURRENT_TIMESTAMP)
        ");
        
        $data = json_encode([
            'status' => $status,
            'transaction_hash' => $transactionHash,
            'source' => 'smart_contract'
        ]);
        
        $stmt->execute([
            "trade_status_updated_to_$status",
            $tradeId,
            $data
        ]);
    } catch (Exception $e) {
        error_log('Error logging activity: ' . $e->getMessage());
    }
}
?>
