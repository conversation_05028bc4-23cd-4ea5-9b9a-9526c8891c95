'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import {
  ArrowLeft,
  Clock,
  Shield,
  Star,
  Eye,
  MessageCircle,
  Share2,
  Flag,
  Heart,
  HeartOff,
  Play,
  User,
  CreditCard,
  MapPin,
  Calendar,
  TrendingUp,
  Award,
  Loader2,
  ExternalLink,
  Calculator
} from 'lucide-react';

interface Offer {
  id: string;
  user: {
    id: string;
    username: string;
    full_name: string;
    rating: number;
    completion_rate: number;
    total_trades: number;
    total_volume: number;
    is_verified: boolean;
    is_online: boolean;
    country_code: string;
    created_at: string;
  };
  offer_type: 'buy' | 'sell';
  amount: number;
  min_amount: number;
  max_amount: number;
  price: number;
  currency: string;
  stablecoin: string;
  payment_methods: string[];
  terms: string;
  auto_reply: string;
  time_limit: number;
  views_count: number;
  is_premium: boolean;
  created_at: string;
  total_value: number;
}

export default function OfferDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { t } = useTranslation('offer-details');
  const { t: commonT } = useTranslation('common');
  
  const [offer, setOffer] = useState<Offer | null>(null);
  const [loading, setLoading] = useState(true);
  const [saved, setSaved] = useState(false);
  const [calculatorAmount, setCalculatorAmount] = useState('');
  const [showCalculator, setShowCalculator] = useState(false);

  useEffect(() => {
    fetchOffer();
  }, [params.id]);

  const fetchOffer = async () => {
    try {
      setLoading(true);
      const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost/ikaros-p2p/api';
      const response = await fetch(`${apiUrl}/offers/details.php?id=${params.id}`);
      const result = await response.json();
      
      if (result.success) {
        setOffer(result.data);
      } else {
        throw new Error(result.error || 'Failed to fetch offer');
      }
    } catch (error) {
      console.error('Error fetching offer:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveOffer = () => {
    setSaved(!saved);
    // هنا سيتم حفظ العرض في قاعدة البيانات
  };

  const handleShareOffer = () => {
    const url = `${window.location.origin}/offers/${offer?.id}`;
    if (navigator.share) {
      navigator.share({
        title: `${offer?.offer_type === 'buy' ? t('offer.buy') : t('offer.sell')} ${offer?.stablecoin}`,
        text: `${t('offer.price')}: ${offer?.price} ${offer?.currency}`,
        url: url
      });
    } else {
      navigator.clipboard.writeText(url);
    }
  };

  const calculateTotal = (amount: string) => {
    if (!offer || !amount) return 0;
    return parseFloat(amount) * offer.price;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600 dark:text-gray-400">{commonT('loading')}</p>
        </div>
      </div>
    );
  }

  if (!offer) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {t('messages.offerNotFound')}
          </h2>
          <button
            onClick={() => router.back()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {commonT('goBack')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="container-custom">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <button
              onClick={() => router.back()}
              className="flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors mr-4"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              {t('backToOffers')}
            </button>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('title')}
            </h1>
          </div>
          
          <div className="flex items-center space-x-2 space-x-reverse">
            <button
              onClick={handleSaveOffer}
              className="p-2 text-gray-400 hover:text-red-500 transition-colors"
              title={saved ? t('messages.offerSaved') : t('actions.saveOffer')}
            >
              {saved ? (
                <Heart className="w-5 h-5 fill-current text-red-500" />
              ) : (
                <HeartOff className="w-5 h-5" />
              )}
            </button>
            
            <button
              onClick={handleShareOffer}
              className="p-2 text-gray-400 hover:text-blue-500 transition-colors"
              title={t('actions.shareOffer')}
            >
              <Share2 className="w-5 h-5" />
            </button>
            
            <button
              className="p-2 text-gray-400 hover:text-red-500 transition-colors"
              title={t('actions.reportOffer')}
            >
              <Flag className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* المحتوى الرئيسي */}
          <div className="lg:col-span-2 space-y-6">
            {/* معلومات العرض */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-3 ${offer.offer_type === 'buy' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  {offer.offer_type === 'buy' ? t('offer.buy') : t('offer.sell')} {offer.stablecoin}
                  {offer.is_premium && (
                    <Award className="w-5 h-5 text-yellow-500 ml-2" title={t('offer.premium')} />
                  )}
                </h2>
                
                <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                  <Eye className="w-4 h-4" />
                  <span>{offer.views_count}</span>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <p className="text-sm text-gray-500 dark:text-gray-400">{t('offer.price')}</p>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {offer.price} {offer.currency}
                  </p>
                </div>
                
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <p className="text-sm text-gray-500 dark:text-gray-400">{t('offer.amount')}</p>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {offer.amount} {offer.stablecoin}
                  </p>
                </div>
                
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <p className="text-sm text-gray-500 dark:text-gray-400">{t('offer.minAmount')}</p>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {offer.min_amount}
                  </p>
                </div>
                
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <p className="text-sm text-gray-500 dark:text-gray-400">{t('offer.maxAmount')}</p>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {offer.max_amount}
                  </p>
                </div>
              </div>

              {/* طرق الدفع */}
              <div className="mb-6">
                <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                  {t('paymentMethods')}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {offer.payment_methods.map((method, index) => (
                    <span key={index} className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded-full flex items-center">
                      <CreditCard className="w-3 h-3 mr-1" />
                      {method}
                    </span>
                  ))}
                </div>
              </div>

              {/* الشروط والأحكام */}
              {offer.terms && (
                <div className="mb-6">
                  <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                    {t('termsAndConditions')}
                  </h3>
                  <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                      {offer.terms}
                    </p>
                  </div>
                </div>
              )}

              {/* الرد التلقائي */}
              {offer.auto_reply && (
                <div>
                  <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                    رسالة ترحيبية
                  </h3>
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <p className="text-blue-800 dark:text-blue-200 text-sm">
                      {offer.auto_reply}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* معلومات المتداول */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <User className="w-5 h-5 mr-2" />
                {t('traderInfo')}
              </h2>
              
              <div className="flex items-start space-x-4 space-x-reverse">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 space-x-reverse mb-3">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {offer.user.username}
                    </h3>
                    {offer.user.is_verified && (
                      <Shield className="w-5 h-5 text-green-500" title={t('trader.verified')} />
                    )}
                    <div className={`w-2 h-2 rounded-full ${offer.user.is_online ? 'bg-green-500' : 'bg-gray-400'}`} 
                         title={offer.user.is_online ? t('trader.online') : t('trader.offline')} />
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">{t('trader.rating')}</span>
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-500 fill-current mr-1" />
                        <span className="font-medium">{offer.user.rating}</span>
                      </div>
                    </div>
                    
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">{t('trader.completionRate')}</span>
                      <p className="font-medium">{offer.user.completion_rate}%</p>
                    </div>
                    
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">{t('trader.totalTrades')}</span>
                      <p className="font-medium">{offer.user.total_trades}</p>
                    </div>
                    
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">{t('trader.totalVolume')}</span>
                      <p className="font-medium">{offer.user.total_volume?.toLocaleString()}</p>
                    </div>
                  </div>
                  
                  <div className="mt-3 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {t('trader.memberSince')}: {new Date(offer.user.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* الشريط الجانبي */}
          <div className="space-y-6">
            {/* أزرار الإجراءات */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="space-y-3">
                <button
                  onClick={() => router.push(`/trade/start/${offer.id}`)}
                  className="w-full flex items-center justify-center space-x-2 space-x-reverse px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  <Play className="w-5 h-5" />
                  <span>{t('actions.startTrade')}</span>
                </button>
                
                <button
                  onClick={() => router.push(`/chat/${offer.user.id}`)}
                  className="w-full flex items-center justify-center space-x-2 space-x-reverse px-4 py-2 text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <MessageCircle className="w-4 h-4" />
                  <span>{t('actions.contactTrader')}</span>
                </button>
                
                <button
                  onClick={() => setShowCalculator(!showCalculator)}
                  className="w-full flex items-center justify-center space-x-2 space-x-reverse px-4 py-2 text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <Calculator className="w-4 h-4" />
                  <span>{t('actions.calculateAmount')}</span>
                </button>
              </div>
            </div>

            {/* حاسبة المبلغ */}
            {showCalculator && (
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {t('calculator.title')}
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {t('calculator.enterAmount')} ({offer.stablecoin})
                    </label>
                    <input
                      type="number"
                      value={calculatorAmount}
                      onChange={(e) => setCalculatorAmount(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                      placeholder="0.00"
                      min={offer.min_amount}
                      max={offer.max_amount}
                    />
                  </div>
                  
                  {calculatorAmount && (
                    <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500 dark:text-gray-400">
                          {offer.offer_type === 'buy' ? t('calculator.youReceive') : t('calculator.youPay')}:
                        </span>
                        <span className="font-medium text-gray-900 dark:text-white">
                          {calculateTotal(calculatorAmount).toFixed(2)} {offer.currency}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* معلومات إضافية */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {t('statistics')}
              </h3>
              
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">{t('stats.views')}</span>
                  <span className="font-medium text-gray-900 dark:text-white">{offer.views_count}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">{t('offer.timeLimit')}</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {Math.round(offer.time_limit / 60)} {t('offer.minutes')}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">{t('offer.createdAt')}</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {new Date(offer.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>

            {/* تحذيرات الأمان */}
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                نصائح الأمان
              </h4>
              <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                <li>• {t('warnings.verifyTrader')}</li>
                <li>• {t('warnings.useSecurePayment')}</li>
                <li>• {t('warnings.keepRecords')}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
