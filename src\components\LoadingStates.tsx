'use client';

import React from 'react';
import { Loader2 } from 'lucide-react';

// Loading Spinner أساسي
export const LoadingSpinner: React.FC<{ size?: 'sm' | 'md' | 'lg'; className?: string }> = ({ 
  size = 'md', 
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <Loader2 className={`animate-spin ${sizeClasses[size]} ${className}`} />
  );
};

// Loading للصفحة كاملة
export const PageLoading: React.FC<{ message?: string }> = ({ message = 'جاري التحميل...' }) => (
  <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
    <div className="text-center">
      <LoadingSpinner size="lg" className="text-primary-600 dark:text-primary-400 mx-auto mb-4" />
      <p className="text-gray-600 dark:text-gray-300">{message}</p>
    </div>
  </div>
);

// Loading للمحتوى
export const ContentLoading: React.FC<{ message?: string; className?: string }> = ({ 
  message = 'جاري التحميل...', 
  className = '' 
}) => (
  <div className={`flex items-center justify-center py-12 ${className}`}>
    <div className="text-center">
      <LoadingSpinner size="md" className="text-primary-600 dark:text-primary-400 mx-auto mb-3" />
      <p className="text-gray-600 dark:text-gray-300 text-sm">{message}</p>
    </div>
  </div>
);

// Loading للبطاقات
export const CardLoading: React.FC<{ count?: number }> = ({ count = 3 }) => (
  <div className="space-y-4">
    {Array.from({ length: count }).map((_, index) => (
      <div key={index} className="animate-pulse">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
              <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
            </div>
            <div className="w-20 h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
          </div>
        </div>
      </div>
    ))}
  </div>
);

// Loading للجدول
export const TableLoading: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => (
  <div className="animate-pulse">
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      {/* Header */}
      <div className="bg-gray-50 dark:bg-gray-700 p-4 border-b border-gray-200 dark:border-gray-600">
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, index) => (
            <div key={index} className="h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
          ))}
        </div>
      </div>
      
      {/* Rows */}
      <div className="divide-y divide-gray-200 dark:divide-gray-600">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="p-4">
            <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <div key={colIndex} className="h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

// Loading للإحصائيات
export const StatsLoading: React.FC<{ count?: number }> = ({ count = 4 }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    {Array.from({ length: count }).map((_, index) => (
      <div key={index} className="animate-pulse">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
            <div className="w-12 h-6 bg-gray-300 dark:bg-gray-600 rounded"></div>
          </div>
          <div className="space-y-2">
            <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    ))}
  </div>
);

// Loading للنموذج
export const FormLoading: React.FC = () => (
  <div className="animate-pulse space-y-6">
    <div className="space-y-2">
      <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/4"></div>
      <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded"></div>
    </div>
    <div className="space-y-2">
      <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/3"></div>
      <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded"></div>
    </div>
    <div className="space-y-2">
      <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/5"></div>
      <div className="h-24 bg-gray-300 dark:bg-gray-600 rounded"></div>
    </div>
    <div className="flex space-x-4 rtl:space-x-reverse">
      <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-24"></div>
      <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-24"></div>
    </div>
  </div>
);

// Loading للقائمة
export const ListLoading: React.FC<{ count?: number }> = ({ count = 5 }) => (
  <div className="space-y-3">
    {Array.from({ length: count }).map((_, index) => (
      <div key={index} className="animate-pulse">
        <div className="flex items-center space-x-3 rtl:space-x-reverse p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
            <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
          </div>
          <div className="w-16 h-6 bg-gray-300 dark:bg-gray-600 rounded"></div>
        </div>
      </div>
    ))}
  </div>
);

// Loading مع رسالة مخصصة
export const CustomLoading: React.FC<{
  type?: 'spinner' | 'dots' | 'pulse';
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}> = ({ 
  type = 'spinner', 
  message, 
  size = 'md', 
  className = '' 
}) => {
  const renderLoader = () => {
    switch (type) {
      case 'dots':
        return (
          <div className="flex space-x-1 rtl:space-x-reverse">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={`bg-primary-600 dark:bg-primary-400 rounded-full animate-pulse ${
                  size === 'sm' ? 'w-2 h-2' : size === 'lg' ? 'w-4 h-4' : 'w-3 h-3'
                }`}
                style={{ animationDelay: `${i * 0.2}s` }}
              />
            ))}
          </div>
        );
      case 'pulse':
        return (
          <div className={`bg-primary-600 dark:bg-primary-400 rounded-full animate-pulse ${
            size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-8 h-8' : 'w-6 h-6'
          }`} />
        );
      default:
        return <LoadingSpinner size={size} className="text-primary-600 dark:text-primary-400" />;
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      {renderLoader()}
      {message && (
        <p className={`text-gray-600 dark:text-gray-300 mt-3 ${
          size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-lg' : 'text-base'
        }`}>
          {message}
        </p>
      )}
    </div>
  );
};

export default {
  LoadingSpinner,
  PageLoading,
  ContentLoading,
  CardLoading,
  TableLoading,
  StatsLoading,
  FormLoading,
  ListLoading,
  CustomLoading,
};
