/**
 * خدمة إدارة النزاعات المتقدمة للمدراء
 * Advanced Dispute Management Service for Admins
 * 
 * يوفر وظائف شاملة للتعامل مع APIs إدارة النزاعات
 * Provides comprehensive functions for interacting with dispute management APIs
 */

import { API_BASE_URL } from '@/constants';

// أنواع البيانات
export interface DisputeFilters {
  status?: 'all' | 'pending' | 'resolved';
  date_from?: string;
  date_to?: string;
}

export interface DisputedTrade {
  id: number;
  type: 'buy' | 'sell';
  amount: number;
  price: number;
  currency: string;
  status: string;
  seller_id: number;
  buyer_id: number;
  payment_method: string;
  created_at: string;
  dispute_created_at: string;
  dispute_resolved_at?: string;
  dispute_reason: string;
  dispute_resolution?: string;
  dispute_resolved_by?: number;
  
  // معلومات المستخدمين
  seller_username: string;
  seller_wallet: string;
  seller_email: string;
  seller_rating: number;
  buyer_username: string;
  buyer_wallet: string;
  buyer_email: string;
  buyer_rating: number;
  resolver_username?: string;
  
  // إحصائيات إضافية
  message_count: number;
  notes_count: number;
  resolution_count: number;
  dispute_age_hours: number;
}

export interface DisputeMessage {
  id: number;
  trade_id: number;
  sender_id: number;
  message_type: 'text' | 'image' | 'file' | 'payment_proof';
  content: string;
  file_url?: string;
  file_name?: string;
  file_size?: number;
  is_read: boolean;
  created_at: string;
  sender_username: string;
}

export interface DisputeNote {
  id: number;
  trade_id: number;
  admin_id: number;
  note: string;
  is_internal: boolean;
  visibility: 'internal' | 'public' | 'parties_only';
  created_at: string;
  admin_username: string;
}

export interface DisputeResolution {
  id: number;
  trade_id: number;
  admin_id: number;
  resolution_type: 'seller_favor' | 'buyer_favor' | 'partial_refund';
  admin_notes: string;
  resolution_amount: number;
  smart_contract_tx_hash?: string;
  created_at: string;
  admin_username: string;
}

export interface DisputeDetails {
  trade: DisputedTrade;
  messages: DisputeMessage[];
  admin_notes: DisputeNote[];
  resolutions: DisputeResolution[];
}

export interface DisputeStatistics {
  total_disputes: number;
  pending_disputes: number;
  in_progress_disputes: number;
  resolved_disputes: number;
  escalated_disputes: number;
  avg_resolution_time: number;
  avg_response_time: number;
  fastest_resolution: number;
  slowest_resolution: number;
  resolution_rate: number;
  escalation_rate: number;
  user_satisfaction_rate: number;
  resolution_types: Array<{
    type: 'seller_favor' | 'buyer_favor' | 'partial_refund' | 'cancelled';
    count: number;
    percentage: number;
    avg_amount: number;
    total_amount: number;
  }>;
  priority_breakdown: Array<{
    priority: 'low' | 'medium' | 'high' | 'urgent';
    count: number;
    percentage: number;
    avg_resolution_time: number;
  }>;
  category_breakdown: Array<{
    category: string;
    count: number;
    percentage: number;
    avg_resolution_time: number;
  }>;
  admin_performance: Array<{
    admin_id: number;
    admin_username: string;
    resolved_disputes: number;
    avg_resolution_time: number;
    user_satisfaction_rating: number;
    resolution_types: {
      seller_favor: number;
      buyer_favor: number;
      partial_refund: number;
    };
  }>;
}

export interface AdminPerformance {
  admin_username: string;
  resolved_disputes: number;
  avg_resolution_time: number;
  seller_favor_count: number;
  buyer_favor_count: number;
  partial_refund_count: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  error_en?: string;
  message?: string;
  message_en?: string;
}

class DisputesAdminService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/admin`;
  }

  /**
   * الحصول على قائمة النزاعات
   * Get disputes list
   */
  async fetchDisputes(status: 'all' | 'pending' | 'resolved' = 'all'): Promise<DisputedTrade[]> {
    try {
      const params = new URLSearchParams();
      if (status !== 'all') {
        params.append('status', status);
      }
      
      const response = await fetch(`${this.baseUrl}/disputes-management.php?${params}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const result: ApiResponse<DisputedTrade[]> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في جلب النزاعات');
      }
      
      return result.data || [];
    } catch (error) {
      console.error('Error fetching disputes:', error);
      throw error;
    }
  }

  /**
   * الحصول على تفاصيل نزاع محدد
   * Get specific dispute details
   */
  async getDisputeDetails(tradeId: number): Promise<DisputeDetails> {
    try {
      const params = new URLSearchParams();
      params.append('action', 'details');
      params.append('trade_id', tradeId.toString());
      
      const response = await fetch(`${this.baseUrl}/disputes-management.php?${params}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const result: ApiResponse<DisputeDetails> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في جلب تفاصيل النزاع');
      }
      
      return result.data || {
        trade: {} as DisputedTrade,
        messages: [],
        admin_notes: [],
        resolutions: []
      };
    } catch (error) {
      console.error('Error fetching dispute details:', error);
      throw error;
    }
  }

  /**
   * حل النزاع
   * Resolve dispute
   */
  async resolveDispute(
    tradeId: number,
    resolutionType: 'seller_favor' | 'buyer_favor' | 'partial_refund',
    adminNotes: string,
    resolutionAmount: number = 0
  ): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/disputes-management.php`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'resolve',
          trade_id: tradeId,
          resolution_type: resolutionType,
          admin_notes: adminNotes,
          resolution_amount: resolutionAmount
        }),
      });
      
      const result: ApiResponse<any> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في حل النزاع');
      }
    } catch (error) {
      console.error('Error resolving dispute:', error);
      throw error;
    }
  }

  /**
   * تصعيد النزاع
   * Escalate dispute
   */
  async escalateDispute(
    tradeId: number,
    escalationReason: string,
    assignedAdminId?: number
  ): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/disputes-management.php`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'escalate',
          trade_id: tradeId,
          escalation_reason: escalationReason,
          assigned_admin_id: assignedAdminId
        }),
      });
      
      const result: ApiResponse<any> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في تصعيد النزاع');
      }
    } catch (error) {
      console.error('Error escalating dispute:', error);
      throw error;
    }
  }

  /**
   * الحصول على إحصائيات النزاعات
   * Get dispute statistics
   */
  async getDisputeStatistics(dateRange?: { start: string; end: string }): Promise<DisputeStatistics> {
    try {
      const params = new URLSearchParams();
      params.append('action', 'statistics');
      
      if (dateRange) {
        params.append('date_from', dateRange.start);
        params.append('date_to', dateRange.end);
      }
      
      const response = await fetch(`${this.baseUrl}/disputes-management.php?${params}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const result: ApiResponse<DisputeStatistics> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في جلب إحصائيات النزاعات');
      }
      
      return result.data || {
        total_disputes: 0,
        pending_disputes: 0,
        in_progress_disputes: 0,
        resolved_disputes: 0,
        escalated_disputes: 0,
        avg_resolution_time: 0,
        avg_response_time: 0,
        fastest_resolution: 0,
        slowest_resolution: 0,
        resolution_rate: 0,
        escalation_rate: 0,
        user_satisfaction_rate: 0,
        resolution_types: [],
        priority_breakdown: [],
        category_breakdown: [],
        admin_performance: []
      };
    } catch (error) {
      console.error('Error fetching dispute statistics:', error);
      throw error;
    }
  }

  /**
   * الحصول على أنواع القرارات المتاحة
   * Get available resolution types
   */
  getResolutionTypes(): Array<{ 
    value: 'seller_favor' | 'buyer_favor' | 'partial_refund'; 
    label_ar: string; 
    label_en: string;
    description_ar: string;
    description_en: string;
  }> {
    return [
      {
        value: 'seller_favor',
        label_ar: 'لصالح البائع',
        label_en: 'Seller Favor',
        description_ar: 'إتمام الصفقة لصالح البائع',
        description_en: 'Complete trade in favor of seller'
      },
      {
        value: 'buyer_favor',
        label_ar: 'لصالح المشتري',
        label_en: 'Buyer Favor',
        description_ar: 'إلغاء الصفقة وإرجاع الأموال للمشتري',
        description_en: 'Cancel trade and refund buyer'
      },
      {
        value: 'partial_refund',
        label_ar: 'استرداد جزئي',
        label_en: 'Partial Refund',
        description_ar: 'استرداد جزء من المبلغ للمشتري',
        description_en: 'Partial refund to buyer'
      }
    ];
  }

  /**
   * الحصول على أولويات النزاعات
   * Get dispute priorities
   */
  getDisputePriorities(): Array<{ 
    value: 'low' | 'medium' | 'high' | 'urgent'; 
    label_ar: string; 
    label_en: string;
    color: string;
  }> {
    return [
      {
        value: 'low',
        label_ar: 'منخفضة',
        label_en: 'Low',
        color: 'text-green-600 dark:text-green-400'
      },
      {
        value: 'medium',
        label_ar: 'متوسطة',
        label_en: 'Medium',
        color: 'text-yellow-600 dark:text-yellow-400'
      },
      {
        value: 'high',
        label_ar: 'عالية',
        label_en: 'High',
        color: 'text-orange-600 dark:text-orange-400'
      },
      {
        value: 'urgent',
        label_ar: 'عاجلة',
        label_en: 'Urgent',
        color: 'text-red-600 dark:text-red-400'
      }
    ];
  }

  /**
   * حساب عمر النزاع بالساعات
   * Calculate dispute age in hours
   */
  calculateDisputeAge(disputeCreatedAt: string): number {
    const createdDate = new Date(disputeCreatedAt);
    const now = new Date();
    const diffInMs = now.getTime() - createdDate.getTime();
    return Math.floor(diffInMs / (1000 * 60 * 60)); // تحويل إلى ساعات
  }

  /**
   * تحديد أولوية النزاع بناءً على العمر والمبلغ
   * Determine dispute priority based on age and amount
   */
  determineDisputePriority(disputeAgeHours: number, amount: number): 'low' | 'medium' | 'high' | 'urgent' {
    if (disputeAgeHours > 72 || amount > 10000) {
      return 'urgent';
    } else if (disputeAgeHours > 48 || amount > 5000) {
      return 'high';
    } else if (disputeAgeHours > 24 || amount > 1000) {
      return 'medium';
    } else {
      return 'low';
    }
  }
}

export const disputesAdminService = new DisputesAdminService();
export default disputesAdminService;
