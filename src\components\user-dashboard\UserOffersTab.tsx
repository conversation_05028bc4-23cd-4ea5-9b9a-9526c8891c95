'use client';

import { useState, useEffect } from 'react';
import {
  ShoppingCart,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  DollarSign
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet, handleApiError } from '@/utils/apiClient';

interface Offer {
  id: string;
  type: 'buy' | 'sell';
  amount: number;
  currency: string;
  price: number;
  minAmount: number;
  maxAmount: number;
  status: 'active' | 'paused' | 'completed' | 'expired';
  paymentMethods: string[];
  description: string;
  createdAt: string;
  updatedAt: string;
  views: number;
  responses: number;
  completionRate: number;
}

export default function UserOffersTab() {
  const { t, formatCurrency, formatDate } = useUserDashboardTranslation();
  const { user } = useAuth();
  const [offers, setOffers] = useState<Offer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'active' | 'paused' | 'completed'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const fetchOffers = async () => {
      if (!user?.id) {
        setError('معرف المستخدم غير متاح');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // استدعاء API الحقيقي لجلب عروض المستخدم
        const response = await apiGet(`offers/my-offers.php?user_id=${user.id}`);

        if (!response.success) {
          throw new Error(response.message || 'فشل في جلب العروض');
        }

        // تحويل البيانات من API إلى تنسيق Offer
        const apiOffers = response.data || [];
        const formattedOffers: Offer[] = apiOffers.map((offer: any) => ({
          id: offer.id.toString(),
          type: offer.offer_type === 'buy' ? 'buy' : 'sell',
          amount: parseFloat(offer.amount) || 0,
          currency: offer.currency || 'USDT',
          price: parseFloat(offer.price) || 0,
          minAmount: parseFloat(offer.min_amount) || 0,
          maxAmount: parseFloat(offer.max_amount) || 0,
          status: mapOfferStatus(offer.status),
          paymentMethods: (() => {
            try {
              if (typeof offer.payment_methods === 'string') {
                // إذا كان JSON string
                if (offer.payment_methods.startsWith('[')) {
                  return JSON.parse(offer.payment_methods);
                }
                // إذا كان comma-separated string
                return offer.payment_methods.split(',').map((method: string) => method.trim());
              } else if (Array.isArray(offer.payment_methods)) {
                return offer.payment_methods;
              }
              return [];
            } catch (error) {
              console.warn('Error parsing payment methods:', error);
              return [];
            }
          })(),
          description: offer.description || '',
          createdAt: offer.created_at || new Date().toISOString(),
          updatedAt: offer.updated_at || new Date().toISOString(),
          views: parseInt(offer.views) || 0,
          responses: parseInt(offer.responses) || 0,
          completionRate: parseFloat(offer.completion_rate) || 0
        }));

        setOffers(formattedOffers);
      } catch (err) {
        const errorMessage = handleApiError(err);
        setError(errorMessage);
        console.error('Error fetching offers:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchOffers();
  }, [user?.id]);

  // دالة لتحويل حالة العرض من API إلى التنسيق المطلوب
  const mapOfferStatus = (apiStatus: string): Offer['status'] => {
    switch (apiStatus?.toLowerCase()) {
      case 'active':
        return 'active';
      case 'paused':
      case 'inactive':
        return 'paused';
      case 'completed':
      case 'closed':
        return 'completed';
      case 'expired':
        return 'expired';
      default:
        return 'active';
    }
  };

  const filteredOffers = offers.filter(offer => {
    const matchesFilter = filter === 'all' || offer.status === filter;
    const matchesSearch = offer.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         offer.currency.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const getStatusColor = (status: Offer['status']) => {
    switch (status) {
      case 'active':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'paused':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30';
      case 'completed':
        return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30';
      case 'expired':
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getStatusText = (status: Offer['status']) => {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'paused':
        return 'متوقف';
      case 'completed':
        return 'مكتمل';
      case 'expired':
        return 'منتهي';
      default:
        return 'غير معروف';
    }
  };

  const getStatusIcon = (status: Offer['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4" />;
      case 'paused':
        return <Clock className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'expired':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  // عرض رسالة الخطأ
  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6">
          <div className="flex items-center gap-3">
            <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
            <div>
              <h3 className="text-red-800 dark:text-red-200 font-medium">خطأ في تحميل العروض</h3>
              <p className="text-red-600 dark:text-red-400 text-sm mt-1">{error}</p>
            </div>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">إدارة العروض</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            إنشاء وإدارة عروض البيع والشراء
          </p>
        </div>
        <button className="flex items-center space-x-2 rtl:space-x-reverse bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
          <Plus className="w-4 h-4" />
          <span>إنشاء عرض جديد</span>
        </button>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">العروض النشطة</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {offers.filter(o => o.status === 'active').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
              <Eye className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي المشاهدات</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {offers.reduce((sum, offer) => sum + offer.views, 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
              <ShoppingCart className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">الردود</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {offers.reduce((sum, offer) => sum + offer.responses, 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">معدل الإنجاز</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {offers.length > 0 ? Math.round(offers.reduce((sum, offer) => sum + offer.completionRate, 0) / offers.length) : 0}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* المرشحات والبحث */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* البحث */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 rtl:right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="البحث في العروض..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 rtl:pr-10 pr-3 rtl:pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* المرشحات */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Filter className="w-4 h-4 text-gray-500" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as typeof filter)}
                className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع العروض</option>
                <option value="active">النشطة</option>
                <option value="paused">المتوقفة</option>
                <option value="completed">المكتملة</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* قائمة العروض */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        {loading ? (
          <div className="p-6">
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded-lg" />
                </div>
              ))}
            </div>
          </div>
        ) : filteredOffers.length > 0 ? (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredOffers.map((offer) => (
              <div key={offer.id} className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 rtl:space-x-reverse">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                      offer.type === 'buy' ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30'
                    }`}>
                      {offer.type === 'buy' ? (
                        <TrendingUp className="w-6 h-6 text-green-600 dark:text-green-400" />
                      ) : (
                        <TrendingDown className="w-6 h-6 text-red-600 dark:text-red-400" />
                      )}
                    </div>

                    <div>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {offer.type === 'buy' ? 'شراء' : 'بيع'} {offer.currency}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 rtl:space-x-reverse ${getStatusColor(offer.status)}`}>
                          {getStatusIcon(offer.status)}
                          <span>{getStatusText(offer.status)}</span>
                        </span>
                      </div>
                      <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-600 dark:text-gray-400 mt-1">
                        <span>السعر: {formatCurrency(offer.price, 'SAR')}</span>
                        <span>•</span>
                        <span>الكمية: {offer.minAmount} - {offer.maxAmount}</span>
                        <span>•</span>
                        <span>{offer.views} مشاهدة</span>
                        <span>•</span>
                        <span>{offer.responses} رد</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <button className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                      <Eye className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-red-500 hover:text-red-700 dark:hover:text-red-300 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-12 text-center">
            <ShoppingCart className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              لا توجد عروض
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {filter === 'all' 
                ? 'لم تقم بإنشاء أي عروض بعد'
                : `لا توجد عروض ${getStatusText(filter as Offer['status'])}`
              }
            </p>
            <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
              إنشاء أول عرض
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
