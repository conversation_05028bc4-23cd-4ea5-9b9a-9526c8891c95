/**
 * خدمة جلب العملات المدعومة من البلوك تشين مباشرة
 * Blockchain Token Service - Fetch supported tokens from smart contracts
 */

import { ethers } from 'ethers';
import { NETWORKS, getSupportedTokens, getContractConfig } from '@/contracts/config';
import { ENHANCED_SUPPORTED_TOKENS } from '@/constants';

// أنواع البيانات
export interface BlockchainToken {
  id: string;
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  networkId: string;
  chainId: number;
  isStablecoin: boolean;
  isActive: boolean;
  totalSupply?: string;
  marketCap?: number;
  price?: number;
  volume24h?: number;
  holders?: number;
  transfers24h?: number;
  contractVerified?: boolean;
  lastUpdated: string;
}

export interface BlockchainNetwork {
  id: string;
  name: string;
  chainId: number;
  symbol: string;
  rpcUrl: string;
  explorerUrl: string;
  isTestnet: boolean;
  isEnabled: boolean;
  blockTime: number;
  gasPrice: number;
  latency?: number;
  uptime?: number;
  blockHeight?: number;
  nodeCount?: number;
  totalTransactions?: number;
  dailyTransactions?: number;
  lastSync: string;
  supportedTokensCount: number;
}

export class BlockchainTokenService {
  private providers: Map<number, ethers.JsonRpcProvider> = new Map();
  private tokenContracts: Map<string, ethers.Contract> = new Map();

  constructor() {
    this.initializeProviders();
  }

  /**
   * تهيئة موفري RPC للشبكات المختلفة
   */
  private initializeProviders() {
    Object.values(NETWORKS).forEach(network => {
      try {
        const provider = new ethers.JsonRpcProvider(network.rpcUrl);
        this.providers.set(network.chainId, provider);
        console.log(`✅ تم تهيئة موفر RPC للشبكة: ${network.name}`);
      } catch (error) {
        console.error(`❌ فشل في تهيئة موفر RPC للشبكة ${network.name}:`, error);
      }
    });
  }

  /**
   * جلب الشبكات المدعومة من التكوين
   */
  async getSupportedNetworks(): Promise<BlockchainNetwork[]> {
    const networks: BlockchainNetwork[] = [];

    for (const [chainId, networkConfig] of Object.entries(NETWORKS)) {
      try {
        const provider = this.providers.get(parseInt(chainId));
        if (!provider) continue;

        // جلب معلومات الشبكة من البلوك تشين
        const blockNumber = await provider.getBlockNumber().catch(() => 0);
        const gasPrice = await provider.getFeeData().then(fee => 
          fee.gasPrice ? Number(ethers.formatUnits(fee.gasPrice, 'gwei')) : 5
        ).catch(() => 5);

        // حساب عدد العملات المدعومة
        const supportedTokens = getSupportedTokens(parseInt(chainId));
        
        const network: BlockchainNetwork = {
          id: chainId,
          name: networkConfig.name,
          chainId: parseInt(chainId),
          symbol: networkConfig.symbol,
          rpcUrl: networkConfig.rpcUrl,
          explorerUrl: networkConfig.explorerUrl,
          isTestnet: networkConfig.isTestnet,
          isEnabled: true, // يمكن تحديثها من قاعدة البيانات
          blockTime: 3, // افتراضي لـ BSC
          gasPrice: gasPrice,
          latency: 100, // سيتم حسابها لاحقاً
          uptime: 99.5, // سيتم حسابها لاحقاً
          blockHeight: blockNumber,
          nodeCount: 1,
          totalTransactions: 0, // سيتم جلبها من API المستكشف
          dailyTransactions: 0,
          lastSync: new Date().toISOString(),
          supportedTokensCount: supportedTokens.length
        };

        networks.push(network);
      } catch (error) {
        console.error(`❌ خطأ في جلب معلومات الشبكة ${networkConfig.name}:`, error);
      }
    }

    return networks;
  }

  /**
   * جلب العملات المدعومة من العقد الذكي
   */
  async getSupportedTokensFromContract(chainId: number): Promise<BlockchainToken[]> {
    try {
      const provider = this.providers.get(chainId);
      if (!provider) {
        throw new Error(`موفر RPC غير متوفر للشبكة ${chainId}`);
      }

      // جلب العملات من التكوين (المدعومة في العقد)
      const configTokens = getSupportedTokens(chainId);
      const tokens: BlockchainToken[] = [];

      for (const tokenConfig of configTokens) {
        try {
          // إنشاء عقد ERC20 للعملة
          const tokenContract = new ethers.Contract(
            tokenConfig.address,
            [
              'function name() view returns (string)',
              'function symbol() view returns (string)',
              'function decimals() view returns (uint8)',
              'function totalSupply() view returns (uint256)',
              'function balanceOf(address) view returns (uint256)'
            ],
            provider
          );

          // جلب معلومات العملة من البلوك تشين
          const [name, symbol, decimals, totalSupply] = await Promise.all([
            tokenContract.name().catch(() => tokenConfig.name),
            tokenContract.symbol().catch(() => tokenConfig.symbol),
            tokenContract.decimals().catch(() => tokenConfig.decimals),
            tokenContract.totalSupply().catch(() => '0')
          ]);

          const token: BlockchainToken = {
            id: `${chainId}-${tokenConfig.address}`,
            address: tokenConfig.address,
            symbol: symbol,
            name: name,
            decimals: Number(decimals),
            networkId: chainId.toString(),
            chainId: chainId,
            isStablecoin: tokenConfig.isStablecoin || false,
            isActive: true,
            totalSupply: ethers.formatUnits(totalSupply, decimals),
            marketCap: 0, // سيتم جلبها من API الأسعار
            price: tokenConfig.isStablecoin ? 1.0 : 0,
            volume24h: 0,
            holders: 0,
            transfers24h: 0,
            contractVerified: true,
            lastUpdated: new Date().toISOString()
          };

          tokens.push(token);
          console.log(`✅ تم جلب معلومات العملة: ${symbol} على الشبكة ${chainId}`);
        } catch (error) {
          console.error(`❌ خطأ في جلب معلومات العملة ${tokenConfig.symbol}:`, error);
          
          // إضافة العملة بالمعلومات الافتراضية في حالة الخطأ
          const fallbackToken: BlockchainToken = {
            id: `${chainId}-${tokenConfig.address}`,
            address: tokenConfig.address,
            symbol: tokenConfig.symbol,
            name: tokenConfig.name,
            decimals: tokenConfig.decimals,
            networkId: chainId.toString(),
            chainId: chainId,
            isStablecoin: tokenConfig.isStablecoin || false,
            isActive: true,
            totalSupply: '0',
            marketCap: 0,
            price: tokenConfig.isStablecoin ? 1.0 : 0,
            volume24h: 0,
            holders: 0,
            transfers24h: 0,
            contractVerified: false,
            lastUpdated: new Date().toISOString()
          };
          
          tokens.push(fallbackToken);
        }
      }

      return tokens;
    } catch (error) {
      console.error(`❌ خطأ في جلب العملات المدعومة للشبكة ${chainId}:`, error);
      throw error;
    }
  }

  /**
   * جلب جميع العملات المدعومة من جميع الشبكات
   */
  async getAllSupportedTokens(): Promise<BlockchainToken[]> {
    const allTokens: BlockchainToken[] = [];

    for (const chainId of Object.keys(NETWORKS)) {
      try {
        const tokens = await this.getSupportedTokensFromContract(parseInt(chainId));
        allTokens.push(...tokens);
      } catch (error) {
        console.error(`❌ خطأ في جلب العملات للشبكة ${chainId}:`, error);
      }
    }

    return allTokens;
  }

  /**
   * التحقق من صحة عنوان العملة
   */
  async validateTokenAddress(chainId: number, address: string): Promise<boolean> {
    try {
      const provider = this.providers.get(chainId);
      if (!provider) return false;

      const code = await provider.getCode(address);
      return code !== '0x';
    } catch (error) {
      console.error(`❌ خطأ في التحقق من عنوان العملة:`, error);
      return false;
    }
  }

  /**
   * جلب معلومات عملة محددة
   */
  async getTokenInfo(chainId: number, address: string): Promise<BlockchainToken | null> {
    try {
      const provider = this.providers.get(chainId);
      if (!provider) return null;

      const tokenContract = new ethers.Contract(
        address,
        [
          'function name() view returns (string)',
          'function symbol() view returns (string)',
          'function decimals() view returns (uint8)',
          'function totalSupply() view returns (uint256)'
        ],
        provider
      );

      const [name, symbol, decimals, totalSupply] = await Promise.all([
        tokenContract.name(),
        tokenContract.symbol(),
        tokenContract.decimals(),
        tokenContract.totalSupply()
      ]);

      return {
        id: `${chainId}-${address}`,
        address: address,
        symbol: symbol,
        name: name,
        decimals: Number(decimals),
        networkId: chainId.toString(),
        chainId: chainId,
        isStablecoin: false,
        isActive: true,
        totalSupply: ethers.formatUnits(totalSupply, decimals),
        contractVerified: true,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error(`❌ خطأ في جلب معلومات العملة:`, error);
      return null;
    }
  }
}

// إنشاء instance مشترك
export const blockchainTokenService = new BlockchainTokenService();
