/**
 * خدمة إدارة العقد الذكي للأدمن
 * Smart Contract Admin Management Service
 * 
 * يوفر وظائف إدارية شاملة للتحكم في العقد الذكي
 * Provides comprehensive admin functions for smart contract management
 */

import { contractService } from './contractService';
import { notificationService } from './notificationService';
import { API_BASE_URL } from '@/constants/index';

// أنواع البيانات للإدارة
export interface AdminContractStats {
  totalTrades: number;
  activeTrades: number;
  completedTrades: number;
  disputedTrades: number;
  totalVolume: string;
  totalFees: string;
  contractBalance: string;
  isPaused: boolean;
  owner: string;
  feeRate: number;
  feeWallet: string;
  autoTimeoutDuration: number;
  cancelWaitDuration: number;
  autoDisputeDuration: number;
}

export interface DisputeResolutionData {
  tradeId: number;
  favorBuyer: boolean;
  reason: string;
  adminId: number;
  adminNotes?: string;
}

export interface ContractSettingsUpdate {
  feeRate?: number;
  feeWallet?: string;
  autoTimeoutDuration?: number;
  cancelWaitDuration?: number;
  autoDisputeDuration?: number;
}

export interface EmergencyAction {
  action: 'pause' | 'unpause' | 'emergency_withdraw';
  reason: string;
  adminId: number;
  details?: any;
}

export interface TradeForceAction {
  tradeId: number;
  action: 'force_complete' | 'force_cancel' | 'force_dispute';
  reason: string;
  adminId: number;
  favorBuyer?: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  error_en?: string;
  message?: string;
  message_en?: string;
}

class ContractAdminService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/admin`;
  }

  /**
   * معالجة استجابة API مع التحقق من الأخطاء
   * Handle API response with error checking
   */
  private async handleApiResponse<T>(response: Response): Promise<ApiResponse<T>> {
    try {
      // التحقق من حالة HTTP
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`);
      }

      // التحقق من نوع المحتوى
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const textResponse = await response.text();
        console.error('Non-JSON response received:', textResponse);

        // محاولة استخراج رسالة الخطأ من HTML
        const errorMatch = textResponse.match(/<b>([^<]+)<\/b>/);
        const errorMessage = errorMatch ? errorMatch[1] : 'الخادم أرجع محتوى غير صحيح';

        throw new Error(`${errorMessage}. نوع المحتوى: ${contentType}`);
      }

      // محاولة تحليل JSON
      const result: ApiResponse<T> = await response.json();
      return result;

    } catch (error: any) {
      // إذا كان الخطأ في تحليل JSON
      if (error.message.includes('Unexpected token')) {
        const textResponse = await response.text().catch(() => 'Unable to read response');
        console.error('JSON parsing error. Response text:', textResponse);
        throw new Error('الخادم أرجع استجابة غير صحيحة. تحقق من إعدادات الخادم.');
      }
      throw error;
    }
  }

  /**
   * الحصول على إحصائيات العقد الشاملة للأدمن
   * Get comprehensive contract statistics for admin
   */
  async getAdminContractStats(): Promise<AdminContractStats> {
    try {
      // التحقق من الاتصال بالعقد
      await contractService.ensureConnection();

      const [
        contractStats,
        isPaused,
        owner,
        feeRate,
        feeWallet,
        autoTimeoutDuration,
        cancelWaitDuration,
        autoDisputeDuration,
        contractBalance
      ] = await Promise.all([
        contractService.getContractStats(),
        contractService.isPaused(),
        contractService.getOwner(),
        contractService.getFeeRate(),
        contractService.getFeeWallet(),
        contractService.getAutoTimeoutDuration(),
        contractService.getCancelWaitDuration(),
        contractService.getAutoDisputeDuration(),
        contractService.getContractUSDTBalance()
      ]);

      return {
        totalTrades: contractStats.totalTrades,
        activeTrades: contractStats.activeTrades,
        completedTrades: contractStats.completedTrades,
        disputedTrades: contractStats.disputedTrades,
        totalVolume: contractStats.totalVolumeUSDT.toString(),
        totalFees: '0', // سيتم حسابها من قاعدة البيانات
        contractBalance,
        isPaused,
        owner,
        feeRate,
        feeWallet,
        autoTimeoutDuration,
        cancelWaitDuration,
        autoDisputeDuration
      };
    } catch (error) {
      console.error('Error fetching admin contract stats:', error);
      throw error;
    }
  }

  /**
   * حل النزاع من خلال العقد الذكي
   * Resolve dispute through smart contract
   */
  async resolveDisputeOnContract(data: DisputeResolutionData): Promise<string> {
    try {
      // التحقق من الاتصال والصلاحيات
      await contractService.ensureConnection();
      
      // تنفيذ حل النزاع في العقد الذكي
      const txHash = await contractService.resolveDispute(data.tradeId, data.favorBuyer);
      
      // تسجيل الإجراء في قاعدة البيانات
      await this.logDisputeResolution({
        tradeId: data.tradeId,
        favorBuyer: data.favorBuyer,
        reason: data.reason,
        adminId: data.adminId,
        transactionHash: txHash,
        adminNotes: data.adminNotes
      });

      return txHash;
    } catch (error) {
      console.error('Error resolving dispute on contract:', error);
      throw error;
    }
  }

  /**
   * تحديث إعدادات العقد
   * Update contract settings
   */
  async updateContractSettings(settings: ContractSettingsUpdate, adminId: number): Promise<string[]> {
    try {
      await contractService.ensureConnection();
      
      const transactions: string[] = [];
      const updates: string[] = [];

      // تحديث معدل الرسوم
      if (settings.feeRate !== undefined) {
        const txHash = await contractService.setFeeRate(settings.feeRate);
        transactions.push(txHash);
        updates.push(`Fee rate updated to ${settings.feeRate / 100}%`);
      }

      // تحديث محفظة الرسوم
      if (settings.feeWallet) {
        const txHash = await contractService.setFeeWallet(settings.feeWallet);
        transactions.push(txHash);
        updates.push(`Fee wallet updated to ${settings.feeWallet}`);
      }

      // تحديث مهلة الإنهاء التلقائي
      if (settings.autoTimeoutDuration !== undefined) {
        const txHash = await contractService.setAutoTimeoutDuration(settings.autoTimeoutDuration);
        transactions.push(txHash);
        updates.push(`Auto timeout duration updated to ${settings.autoTimeoutDuration} seconds`);
      }

      // تحديث مهلة انتظار الإلغاء
      if (settings.cancelWaitDuration !== undefined) {
        const txHash = await contractService.setCancelWaitDuration(settings.cancelWaitDuration);
        transactions.push(txHash);
        updates.push(`Cancel wait duration updated to ${settings.cancelWaitDuration} seconds`);
      }

      // تحديث مهلة النزاع التلقائي
      if (settings.autoDisputeDuration !== undefined) {
        const txHash = await contractService.setAutoDisputeDuration(settings.autoDisputeDuration);
        transactions.push(txHash);
        updates.push(`Auto dispute duration updated to ${settings.autoDisputeDuration} seconds`);
      }

      // تسجيل التحديثات في قاعدة البيانات
      await this.logContractSettingsUpdate({
        adminId,
        updates,
        transactions,
        settings
      });

      return transactions;
    } catch (error) {
      console.error('Error updating contract settings:', error);
      throw error;
    }
  }

  /**
   * تنفيذ إجراء طوارئ
   * Execute emergency action
   */
  async executeEmergencyAction(action: EmergencyAction): Promise<string> {
    try {
      await contractService.ensureConnection();
      
      let txHash: string;

      switch (action.action) {
        case 'pause':
          txHash = await contractService.pauseContract();
          break;
        case 'unpause':
          txHash = await contractService.unpauseContract();
          break;
        case 'emergency_withdraw':
          if (!action.details?.token || !action.details?.amount || !action.details?.to) {
            throw new Error('Emergency withdraw requires token, amount, and recipient address');
          }
          txHash = await contractService.emergencyWithdraw(
            action.details.token,
            action.details.amount,
            action.details.to
          );
          break;
        default:
          throw new Error(`Unknown emergency action: ${action.action}`);
      }

      // تسجيل الإجراء في قاعدة البيانات
      await this.logEmergencyAction({
        ...action,
        transactionHash: txHash
      });

      return txHash;
    } catch (error) {
      console.error('Error executing emergency action:', error);
      throw error;
    }
  }

  /**
   * إجراء قسري على صفقة
   * Force action on trade
   */
  async executeTradeForceAction(action: TradeForceAction): Promise<string> {
    try {
      await contractService.ensureConnection();
      
      let txHash: string;

      switch (action.action) {
        case 'force_complete':
          // إكمال الصفقة قسرياً (للبائع)
          txHash = await contractService.resolveDispute(action.tradeId, false);
          break;
        case 'force_cancel':
          // إلغاء الصفقة قسرياً
          txHash = await contractService.autoCancel(action.tradeId);
          break;
        case 'force_dispute':
          // إنشاء نزاع قسرياً ثم حله
          if (action.favorBuyer === undefined) {
            throw new Error('favorBuyer must be specified for force dispute');
          }
          txHash = await contractService.resolveDispute(action.tradeId, action.favorBuyer);
          break;
        default:
          throw new Error(`Unknown force action: ${action.action}`);
      }

      // تسجيل الإجراء في قاعدة البيانات
      await this.logTradeForceAction({
        ...action,
        transactionHash: txHash
      });

      return txHash;
    } catch (error) {
      console.error('Error executing trade force action:', error);
      throw error;
    }
  }

  /**
   * تسجيل حل النزاع في قاعدة البيانات
   * Log dispute resolution in database
   */
  private async logDisputeResolution(data: any): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/contract-management.php`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'log_dispute_resolution',
          ...data
        }),
      });

      const result: ApiResponse<any> = await response.json();
      
      if (!result.success) {
        console.error('Failed to log dispute resolution:', result.error);
      }
    } catch (error) {
      console.error('Error logging dispute resolution:', error);
    }
  }

  /**
   * تسجيل تحديث إعدادات العقد
   * Log contract settings update
   */
  private async logContractSettingsUpdate(data: any): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/contract-management.php`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'log_settings_update',
          ...data
        }),
      });

      const result: ApiResponse<any> = await response.json();
      
      if (!result.success) {
        console.error('Failed to log settings update:', result.error);
      }
    } catch (error) {
      console.error('Error logging settings update:', error);
    }
  }

  /**
   * تسجيل إجراء الطوارئ
   * Log emergency action
   */
  private async logEmergencyAction(data: any): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/contract-management.php`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'log_emergency_action',
          ...data
        }),
      });

      const result: ApiResponse<any> = await response.json();
      
      if (!result.success) {
        console.error('Failed to log emergency action:', result.error);
      }
    } catch (error) {
      console.error('Error logging emergency action:', error);
    }
  }

  /**
   * تسجيل الإجراء القسري على الصفقة
   * Log trade force action
   */
  private async logTradeForceAction(data: any): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/contract-management.php`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'log_trade_force_action',
          ...data
        }),
      });

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        console.error('Failed to log trade force action:', result.error);
      }
    } catch (error) {
      console.error('Error logging trade force action:', error);
    }
  }

  /**
   * الحصول على الصفقات المؤهلة للنزاع التلقائي
   * Get trades eligible for auto dispute
   */
  async getTradesEligibleForAutoDispute(limit: number = 50): Promise<number[]> {
    try {
      await contractService.ensureConnection();
      return await contractService.getTradesEligibleForAutoDispute(limit);
    } catch (error) {
      console.error('Error getting trades eligible for auto dispute:', error);
      throw error;
    }
  }

  /**
   * تنفيذ النزاع التلقائي
   * Execute auto dispute
   */
  async executeAutoDispute(tradeId: number, adminId: number): Promise<string> {
    try {
      await contractService.ensureConnection();

      const txHash = await contractService.autoDispute(tradeId);

      // تسجيل الإجراء
      await this.logAutoDispute({
        tradeId,
        adminId,
        transactionHash: txHash
      });

      return txHash;
    } catch (error) {
      console.error('Error executing auto dispute:', error);
      throw error;
    }
  }

  /**
   * مراقبة أحداث العقد الذكي
   * Monitor smart contract events
   */
  async monitorContractEvents(): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/contract-events.php`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await this.handleApiResponse<any[]>(response);

      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في جلب أحداث العقد');
      }

      return result.data || [];
    } catch (error) {
      console.error('Error monitoring contract events:', error);
      throw error;
    }
  }

  /**
   * مزامنة حالة العقد مع قاعدة البيانات
   * Sync contract state with database
   */
  async syncContractState(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/sync-contract.php`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'full_sync'
        }),
      });

      const result = await this.handleApiResponse<any>(response);

      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في مزامنة العقد');
      }
    } catch (error) {
      console.error('Error syncing contract state:', error);
      throw error;
    }
  }

  /**
   * التحقق من صحة العقد والاتصال
   * Validate contract and connection
   */
  async validateContractHealth(): Promise<{
    isConnected: boolean;
    isOwner: boolean;
    contractAddress: string;
    networkId: number;
    blockNumber: number;
    gasPrice: string;
    issues: string[];
  }> {
    try {
      const issues: string[] = [];

      // التحقق من الاتصال
      const isConnected = await contractService.ensureConnection();
      if (!isConnected) {
        issues.push('Contract not connected');
      }

      // التحقق من الملكية
      const [owner, currentAccount] = await Promise.all([
        contractService.getOwner().catch(() => ''),
        contractService.getCurrentAccount().catch(() => '')
      ]);

      const isOwner = Boolean(currentAccount && owner &&
        currentAccount.toLowerCase() === owner.toLowerCase());

      if (!isOwner) {
        issues.push('Current account is not contract owner');
      }

      // معلومات الشبكة - استخدام provider مباشرة من contractService
      let networkId = 97; // BSC Testnet افتراضي
      let blockNumber = 0;
      let gasPrice = '0';
      let contractAddress = '';

      try {
        // محاولة الحصول على معلومات الشبكة
        if ((contractService as any).provider) {
          const provider = (contractService as any).provider;
          const [network, currentBlock, feeData] = await Promise.all([
            provider.getNetwork().catch(() => ({ chainId: 97 })),
            provider.getBlockNumber().catch(() => 0),
            provider.getFeeData().catch(() => ({ gasPrice: BigInt(0) }))
          ]);

          networkId = Number(network.chainId);
          blockNumber = currentBlock;
          gasPrice = feeData.gasPrice?.toString() || '0';
        }

        // محاولة الحصول على عنوان العقد
        if ((contractService as any).escrowContract?.target) {
          contractAddress = (contractService as any).escrowContract.target;
        }
      } catch (error) {
        console.warn('تعذر الحصول على معلومات الشبكة:', error);
        issues.push('Unable to fetch network information');
      }

      return {
        isConnected,
        isOwner,
        contractAddress,
        networkId,
        blockNumber,
        gasPrice,
        issues
      };
    } catch (error) {
      console.error('Error validating contract health:', error);
      throw error;
    }
  }

  /**
   * تسجيل النزاع التلقائي
   * Log auto dispute
   */
  private async logAutoDispute(data: any): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/contract-management.php`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'log_auto_dispute',
          ...data
        }),
      });

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        console.error('Failed to log auto dispute:', result.error);
      }
    } catch (error) {
      console.error('Error logging auto dispute:', error);
    }
  }

  /**
   * حل النزاع مباشرة من العقد الذكي مع تسجيل في قاعدة البيانات
   * Resolve dispute directly from smart contract with database logging
   */
  async resolveDisputeWithLogging(data: DisputeResolutionData & { transactionHash: string }): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/contract-disputes.php`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'resolve_dispute',
          trade_id: data.tradeId,
          favor_buyer: data.favorBuyer,
          reason: data.reason,
          admin_notes: data.adminNotes,
          transaction_hash: data.transactionHash
        }),
      });

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في حل النزاع');
      }
    } catch (error) {
      console.error('Error resolving dispute with logging:', error);
      throw error;
    }
  }

  /**
   * تنفيذ النزاع التلقائي مع تسجيل في قاعدة البيانات
   * Execute auto dispute with database logging
   */
  async executeAutoDisputeWithLogging(tradeId: number, transactionHash: string, adminId: number): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/contract-disputes.php`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'auto_dispute',
          trade_id: tradeId,
          transaction_hash: transactionHash,
          admin_id: adminId
        }),
      });

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في تنفيذ النزاع التلقائي');
      }
    } catch (error) {
      console.error('Error executing auto dispute with logging:', error);
      throw error;
    }
  }

  /**
   * الحصول على الصفقات المؤهلة للنزاع التلقائي من قاعدة البيانات
   * Get trades eligible for auto dispute from database
   */
  async getEligibleAutoDisputeTradesFromDB(limit: number = 50): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/contract-disputes.php?action=eligible_trades&limit=${limit}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result: ApiResponse<any[]> = await response.json();

      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في جلب الصفقات المؤهلة');
      }

      return result.data || [];
    } catch (error) {
      console.error('Error getting eligible auto dispute trades from DB:', error);
      throw error;
    }
  }

  /**
   * الحصول على تقرير شامل عن حالة العقد
   * Get comprehensive contract status report
   */
  async getContractStatusReport(): Promise<{
    contractStats: AdminContractStats;
    eligibleDisputes: any[];
    recentEvents: any[];
    syncStatus: any;
    contractHealth: any;
  }> {
    try {
      const [
        contractStats,
        eligibleDisputes,
        recentEvents,
        syncStatus,
        contractHealth
      ] = await Promise.all([
        this.getAdminContractStats(),
        this.getEligibleAutoDisputeTradesFromDB(20),
        this.monitorContractEvents(),
        this.getSyncStatusFromAPI(),
        this.validateContractHealth()
      ]);

      return {
        contractStats,
        eligibleDisputes,
        recentEvents,
        syncStatus,
        contractHealth
      };
    } catch (error) {
      console.error('Error getting contract status report:', error);
      throw error;
    }
  }

  /**
   * الحصول على حالة المزامنة من API
   * Get sync status from API
   */
  private async getSyncStatusFromAPI(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/contract-management.php?action=sync_status`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في جلب حالة المزامنة');
      }

      return result.data;
    } catch (error) {
      console.error('Error getting sync status from API:', error);
      throw error;
    }
  }
}

// إنشاء مثيل واحد للخدمة
export const contractAdminService = new ContractAdminService();
