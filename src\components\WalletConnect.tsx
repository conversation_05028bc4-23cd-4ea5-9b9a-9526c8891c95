'use client';

import { useState, useEffect } from 'react';
import {
  Wallet,
  AlertCircle,
  CheckCircle,
  ExternalLink,
  Copy,
  LogOut
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { walletService } from '@/services/walletService';
import { notificationService } from '@/services/notificationService';
import { detectMetaMask, detectTrustWallet, getWalletErrorMessage } from '@/utils/walletDetection';

interface WalletInfo {
  address: string;
  balance: string;
  network: string;
  isConnected: boolean;
}

export default function WalletConnect() {
  const { t } = useTranslation();
  const [wallet, setWallet] = useState<WalletInfo | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string>('');
  const [showWalletOptions, setShowWalletOptions] = useState(false);

  // التحقق من وجود المحافظ
  const getWalletStatus = (walletType: 'metamask' | 'trustwallet') => {
    switch (walletType) {
      case 'metamask':
        return detectMetaMask();
      case 'trustwallet':
        return detectTrustWallet();
      default:
        return { isInstalled: false, isAvailable: false };
    }
  };

  // الاتصال الفعلي بالمحفظة
  const connectWallet = async (walletType: 'metamask' | 'walletconnect' | 'trustwallet') => {
    setIsConnecting(true);
    setError('');

    try {
      // التحقق من توفر المحفظة قبل المحاولة
      const walletStatus = getWalletStatus(walletType);

      if (!walletStatus.isInstalled || !walletStatus.isAvailable) {
        const errorMessage = getWalletErrorMessage(walletType, walletStatus, t);
        throw new Error(errorMessage);
      }

      // استخدام walletService للاتصال الفعلي
      const walletInfo = await walletService.connectWallet(walletType);

      setWallet(walletInfo);
      setShowWalletOptions(false);

      // إشعار نجاح الاتصال
      notificationService.success(t('wallet.connected.title'));

    } catch (err: any) {
      const errorMessage = err.message || t('wallet.errors.connectionFailed');
      setError(errorMessage);
      notificationService.error(errorMessage);
    } finally {
      setIsConnecting(false);
    }
  };

  // قطع الاتصال
  const disconnectWallet = () => {
    walletService.disconnectWallet();
    setWallet(null);
    notificationService.success(t('wallet.connected.disconnect'));
  };

  // نسخ العنوان
  const copyAddress = async () => {
    if (wallet?.address) {
      try {
        await navigator.clipboard.writeText(wallet.address);
        notificationService.success(t('common.copied'));
      } catch (error) {
        console.error('Failed to copy address:', error);
      }
    }
  };

  // التحقق من الاتصال المحفوظ عند التحميل
  useEffect(() => {
    const checkSavedConnection = async () => {
      try {
        const savedWalletInfo = await walletService.checkSavedConnection();
        if (savedWalletInfo) {
          setWallet(savedWalletInfo);
        }
      } catch (error) {
        console.error('Error checking saved connection:', error);
      }
    };

    checkSavedConnection();
  }, []);

  // عرض المحفظة المتصلة
  if (wallet?.isConnected) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-success-100 dark:bg-success-900/30 rounded-full flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-success-600 dark:text-success-400" />
            </div>
            <div className="mr-3">
              <h3 className="font-semibold text-gray-900 dark:text-white">{t('wallet.connected.title')}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">{wallet.network}</p>
            </div>
          </div>
          <button
            onClick={disconnectWallet}
            className="text-gray-400 dark:text-gray-500 hover:text-danger-600 dark:hover:text-danger-400 transition-colors"
            title={t('wallet.connected.disconnect')}
          >
            <LogOut className="w-5 h-5" />
          </button>
        </div>

        <div className="space-y-3">
          <div>
            <label className="text-sm text-gray-600 dark:text-gray-400">{t('wallet.connected.address')}</label>
            <div className="flex items-center justify-between bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mt-1">
              <span className="text-sm font-mono text-gray-800 dark:text-gray-200">
                {wallet.address && wallet.address.length > 10
                  ? `${wallet.address.slice(0, 6)}...${wallet.address.slice(-4)}`
                  : wallet.address || ''
                }
              </span>
              <button
                onClick={copyAddress}
                className="text-gray-400 dark:text-gray-500 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                title={t('wallet.connected.copyAddress')}
              >
                <Copy className="w-4 h-4" />
              </button>
            </div>
          </div>

          <div>
            <label className="text-sm text-gray-600 dark:text-gray-400">{t('wallet.connected.balance')}</label>
            <div className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
              {wallet.balance} USDT
            </div>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button className="btn btn-primary w-full">
            {t('wallet.connected.viewOnExplorer')}
            <ExternalLink className="w-4 h-4 mr-2" />
          </button>
        </div>
      </div>
    );
  }

  // عرض خيارات الاتصال
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
          <Wallet className="w-8 h-8 text-primary-600 dark:text-primary-400" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 heading-arabic">
          {t('wallet.connect.title')}
        </h3>
        <p className="text-gray-600 dark:text-gray-300 body-arabic">
          {t('wallet.connect.description')}
        </p>
      </div>

      {error && (
        <div className="bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg p-4 mb-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-danger-600 dark:text-danger-400 ml-2" />
            <span className="text-danger-800 dark:text-danger-300 text-sm">{error}</span>
          </div>
        </div>
      )}

      {!showWalletOptions ? (
        <button
          onClick={() => setShowWalletOptions(true)}
          className="btn btn-primary w-full text-lg py-4 body-arabic"
        >
          {t('wallet.connect.chooseWallet')}
        </button>
      ) : (
        <div className="space-y-3">
          {/* MetaMask */}
          <button
            onClick={() => connectWallet('metamask')}
            disabled={isConnecting}
            className="w-full flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-primary-300 dark:hover:border-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-colors disabled:opacity-50"
          >
            <div className="flex items-center">
              <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                <span className="text-orange-600 dark:text-orange-400 font-bold">M</span>
              </div>
              <div className="mr-3 text-right">
                <div className="font-semibold text-gray-900 dark:text-white body-arabic">{t('wallet.connect.metamask')}</div>
                <div className="text-sm text-gray-600 dark:text-gray-300 body-arabic">
                  {(() => {
                    const status = getWalletStatus('metamask');
                    return status.isInstalled ? t('wallet.connect.available') : t('wallet.connect.notInstalled');
                  })()}
                </div>
              </div>
            </div>
            {isConnecting ? (
              <div className="loading-spinner"></div>
            ) : (
              <ExternalLink className="w-5 h-5 text-gray-400 dark:text-gray-500" />
            )}
          </button>

          {/* WalletConnect */}
          <button
            onClick={() => connectWallet('walletconnect')}
            disabled={isConnecting}
            className="w-full flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-primary-300 dark:hover:border-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-colors disabled:opacity-50"
          >
            <div className="flex items-center">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 dark:text-blue-400 font-bold">W</span>
              </div>
              <div className="mr-3 text-right">
                <div className="font-semibold text-gray-900 dark:text-white">{t('wallet.connect.walletConnect')}</div>
                <div className="text-sm text-gray-600 dark:text-gray-300">{t('wallet.connect.connectAnyWallet')}</div>
              </div>
            </div>
            {isConnecting ? (
              <div className="loading-spinner"></div>
            ) : (
              <ExternalLink className="w-5 h-5 text-gray-400 dark:text-gray-500" />
            )}
          </button>

          {/* Trust Wallet */}
          <button
            onClick={() => connectWallet('trustwallet')}
            disabled={isConnecting}
            className="w-full flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-primary-300 dark:hover:border-primary-600 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-colors disabled:opacity-50"
          >
            <div className="flex items-center">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 dark:text-blue-400 font-bold">T</span>
              </div>
              <div className="mr-3 text-right">
                <div className="font-semibold text-gray-900 dark:text-white">{t('wallet.connect.trustWallet')}</div>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  {(() => {
                    const status = getWalletStatus('trustwallet');
                    return status.isInstalled ? t('wallet.connect.available') : t('wallet.connect.notInstalled');
                  })()}
                </div>
              </div>
            </div>
            {isConnecting ? (
              <div className="loading-spinner"></div>
            ) : (
              <ExternalLink className="w-5 h-5 text-gray-400 dark:text-gray-500" />
            )}
          </button>

          <button
            onClick={() => setShowWalletOptions(false)}
            className="btn btn-secondary w-full mt-4"
          >
            {t('common.cancel')}
          </button>
        </div>
      )}

      {/* معلومات إضافية */}
      <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="text-center">
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
            {t('wallet.connect.noWallet')}
          </p>
          <a
            href="https://metamask.io"
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-sm font-medium"
          >
            {t('wallet.connect.learnHow')}
            <ExternalLink className="w-3 h-3 mr-1 inline" />
          </a>
        </div>
      </div>
    </div>
  );
}