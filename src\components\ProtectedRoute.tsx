'use client';

import { ReactNode, useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';

interface ProtectedRouteProps {
  children: ReactNode;
  requireAuth?: boolean;
  requireAdmin?: boolean;
  fallback?: ReactNode;
  redirectTo?: string;
}

/**
 * مكون حماية المسارات - يتحكم في الوصول للصفحات حسب حالة المصادقة وصلاحيات المستخدم
 * Protected Route Component - Controls access to pages based on authentication and user permissions
 */
export default function ProtectedRoute({
  children,
  requireAuth = false,
  requireAdmin = false,
  fallback = null,
  redirectTo = '/'
}: ProtectedRouteProps) {
  const { isAuthenticated, user, isLoading } = useAuth();
  const { t } = useTranslation();
  const [isChecking, setIsChecking] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);

  useEffect(() => {
    const checkAccess = async () => {
      try {
        // انتظار انتهاء تحميل حالة المصادقة
        if (isLoading) {
          return;
        }

        // إذا لم تكن هناك متطلبات خاصة، السماح بالوصول
        if (!requireAuth && !requireAdmin) {
          setHasAccess(true);
          setIsChecking(false);
          return;
        }

        // التحقق من متطلبات المصادقة
        if (requireAuth && !isAuthenticated) {
          setHasAccess(false);
          setIsChecking(false);
          
          // إعادة توجيه للصفحة الرئيسية أو صفحة تسجيل الدخول
          if (typeof window !== 'undefined') {
            setTimeout(() => {
              window.location.href = redirectTo;
            }, 1000);
          }
          return;
        }

        // التحقق من متطلبات الإدارة
        if (requireAdmin) {
          if (!isAuthenticated || !user) {
            setHasAccess(false);
            setIsChecking(false);
            
            if (typeof window !== 'undefined') {
              setTimeout(() => {
                window.location.href = '/admin';
              }, 1000);
            }
            return;
          }

          // التحقق من صلاحيات الإدارة
          const isAdmin = user.isAdmin || false;
          
          if (!isAdmin) {
            setHasAccess(false);
            setIsChecking(false);
            
            if (typeof window !== 'undefined') {
              setTimeout(() => {
                window.location.href = '/';
              }, 1000);
            }
            return;
          }
        }

        // إذا وصلنا هنا، فالمستخدم لديه الصلاحيات المطلوبة
        setHasAccess(true);
        setIsChecking(false);

      } catch (error) {
        console.error('Error checking route access:', error);
        setHasAccess(false);
        setIsChecking(false);
      }
    };

    checkAccess();
  }, [isAuthenticated, user, isLoading, requireAuth, requireAdmin, redirectTo]);

  // عرض مؤشر التحميل أثناء التحقق
  if (isChecking || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">
            {t('common.loading')}
          </p>
        </div>
      </div>
    );
  }

  // عرض رسالة عدم وجود صلاحيات
  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="mb-6">
            <svg className="mx-auto h-16 w-16 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            {requireAdmin ? t('errors.noAdminAccess') : t('errors.noAccess')}
          </h2>
          
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {requireAdmin 
              ? t('errors.adminAccessRequired') 
              : t('errors.loginRequired')
            }
          </p>
          
          <button
            onClick={() => window.location.href = requireAdmin ? '/admin' : '/'}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
          >
            {requireAdmin ? t('common.goToAdminLogin') : t('common.goToHome')}
          </button>
        </div>
      </div>
    );
  }

  // عرض المحتوى إذا كان لدى المستخدم الصلاحيات المطلوبة
  return <>{children}</>;
}
