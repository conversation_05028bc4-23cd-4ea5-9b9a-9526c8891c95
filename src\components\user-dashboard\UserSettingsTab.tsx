'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Shield,
  Bell,
  Globe,
  Moon,
  Sun,
  Eye,
  EyeOff,
  Save,
  Smartphone,
  Mail,
  Lock
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';

export default function UserSettingsTab() {
  const [settings, setSettings] = useState({
    language: 'ar',
    theme: 'light',
    notifications: {
      email: true,
      push: true,
      sms: false,
      trades: true,
      offers: true,
      security: true
    },
    privacy: {
      showProfile: true,
      showTrades: false,
      showRating: true
    },
    security: {
      twoFactor: false,
      loginNotifications: true,
      sessionTimeout: 30
    }
  });

  const handleSave = () => {
    // حفظ الإعدادات
    console.log('Saving settings:', settings);
  };

  return (
    <div className="space-y-6">
      {/* رأس القسم */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <Settings className="w-6 h-6 ml-3 text-blue-600" />
            الإعدادات
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            إدارة تفضيلاتك وإعدادات الحساب
          </p>
        </div>
        <button
          onClick={handleSave}
          className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Save className="w-4 h-4" />
          <span>حفظ التغييرات</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* الإعدادات العامة */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <Globe className="w-5 h-5 ml-2 text-blue-600" />
            الإعدادات العامة
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                اللغة
              </label>
              <select
                value={settings.language}
                onChange={(e) => setSettings({...settings, language: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="ar">العربية</option>
                <option value="en">English</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                المظهر
              </label>
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <button
                  onClick={() => setSettings({...settings, theme: 'light'})}
                  className={`flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-lg transition-colors ${
                    settings.theme === 'light'
                      ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  <Sun className="w-4 h-4" />
                  <span>فاتح</span>
                </button>
                <button
                  onClick={() => setSettings({...settings, theme: 'dark'})}
                  className={`flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 rounded-lg transition-colors ${
                    settings.theme === 'dark'
                      ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  <Moon className="w-4 h-4" />
                  <span>مظلم</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* إعدادات الإشعارات */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <Bell className="w-5 h-5 ml-2 text-blue-600" />
            الإشعارات
          </h3>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Mail className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-700 dark:text-gray-300">إشعارات البريد الإلكتروني</span>
              </div>
              <input
                type="checkbox"
                checked={settings.notifications.email}
                onChange={(e) => setSettings({
                  ...settings,
                  notifications: {...settings.notifications, email: e.target.checked}
                })}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Smartphone className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-700 dark:text-gray-300">الإشعارات المباشرة</span>
              </div>
              <input
                type="checkbox"
                checked={settings.notifications.push}
                onChange={(e) => setSettings({
                  ...settings,
                  notifications: {...settings.notifications, push: e.target.checked}
                })}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700 dark:text-gray-300">إشعارات الصفقات</span>
              <input
                type="checkbox"
                checked={settings.notifications.trades}
                onChange={(e) => setSettings({
                  ...settings,
                  notifications: {...settings.notifications, trades: e.target.checked}
                })}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700 dark:text-gray-300">إشعارات العروض</span>
              <input
                type="checkbox"
                checked={settings.notifications.offers}
                onChange={(e) => setSettings({
                  ...settings,
                  notifications: {...settings.notifications, offers: e.target.checked}
                })}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* إعدادات الخصوصية */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <Eye className="w-5 h-5 ml-2 text-blue-600" />
            الخصوصية
          </h3>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700 dark:text-gray-300">إظهار الملف الشخصي</span>
              <input
                type="checkbox"
                checked={settings.privacy.showProfile}
                onChange={(e) => setSettings({
                  ...settings,
                  privacy: {...settings.privacy, showProfile: e.target.checked}
                })}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700 dark:text-gray-300">إظهار سجل الصفقات</span>
              <input
                type="checkbox"
                checked={settings.privacy.showTrades}
                onChange={(e) => setSettings({
                  ...settings,
                  privacy: {...settings.privacy, showTrades: e.target.checked}
                })}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700 dark:text-gray-300">إظهار التقييم</span>
              <input
                type="checkbox"
                checked={settings.privacy.showRating}
                onChange={(e) => setSettings({
                  ...settings,
                  privacy: {...settings.privacy, showRating: e.target.checked}
                })}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* إعدادات الأمان */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <Shield className="w-5 h-5 ml-2 text-blue-600" />
            الأمان
          </h3>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <Lock className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-700 dark:text-gray-300">المصادقة الثنائية</span>
              </div>
              <input
                type="checkbox"
                checked={settings.security.twoFactor}
                onChange={(e) => setSettings({
                  ...settings,
                  security: {...settings.security, twoFactor: e.target.checked}
                })}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700 dark:text-gray-300">إشعارات تسجيل الدخول</span>
              <input
                type="checkbox"
                checked={settings.security.loginNotifications}
                onChange={(e) => setSettings({
                  ...settings,
                  security: {...settings.security, loginNotifications: e.target.checked}
                })}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                انتهاء الجلسة (دقيقة)
              </label>
              <select
                value={settings.security.sessionTimeout}
                onChange={(e) => setSettings({
                  ...settings,
                  security: {...settings.security, sessionTimeout: parseInt(e.target.value)}
                })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value={15}>15 دقيقة</option>
                <option value={30}>30 دقيقة</option>
                <option value={60}>ساعة واحدة</option>
                <option value={120}>ساعتان</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
