'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Alert<PERSON>riangle, RefreshCw, Home, Bug, Copy, ExternalLink } from 'lucide-react';
import { errorHandler } from '@/utils/errorHandler';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  enableDemoMode?: boolean;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
  showDetails: boolean;
}

class ErrorBoundary extends Component<Props, State> {
  private errorHandler: ErrorHandler;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      showDetails: false
    };
    this.errorHandler = ErrorHandler.getInstance();
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);

    this.setState({
      error,
      errorInfo
    });

    // معالجة الخطأ باستخدام ErrorHandler
    this.errorHandler.handleError(error, 'ErrorBoundary', {
      showNotification: false, // لا نريد إشعار لأننا سنعرض UI مخصص
      logError: true,
      reportToService: true
    });

    // استدعاء callback المخصص إذا كان موجوداً
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      errorId: undefined,
      showDetails: false
    });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleToggleDetails = () => {
    this.setState(prev => ({ showDetails: !prev.showDetails }));
  };

  handleCopyError = async () => {
    if (!this.state.error) return;

    const errorText = `
خطأ في التطبيق:
ID: ${this.state.errorId}
الوقت: ${new Date().toLocaleString('ar-SA')}
الرسالة: ${this.state.error.message}
المكدس: ${this.state.error.stack}
معلومات إضافية: ${JSON.stringify(this.state.errorInfo, null, 2)}
    `.trim();

    try {
      await navigator.clipboard.writeText(errorText);
      alert('تم نسخ تفاصيل الخطأ');
    } catch (err) {
      console.error('فشل في نسخ النص:', err);
    }
  };

  handleEnableDemoMode = () => {
    // تفعيل الوضع التوضيحي للمحفظة والعقود
    localStorage.setItem('wallet_demo_mode', 'true');
    localStorage.setItem('contract_demo_mode', 'true');

    // إعادة تحميل الصفحة
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-8 text-center">
            <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
              <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
            </div>
            
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              حدث خطأ غير متوقع
            </h1>
            
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              نعتذر، حدث خطأ أثناء تحميل هذه الصفحة. يرجى المحاولة مرة أخرى.
            </p>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="text-left mb-6 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
                <summary className="cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  تفاصيل الخطأ (وضع التطوير)
                </summary>
                <pre className="text-xs text-red-600 dark:text-red-400 overflow-auto">
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </details>
            )}

            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={this.handleRetry}
                className="flex-1 btn btn-primary"
              >
                <RefreshCw className="w-4 h-4 ml-2" />
                إعادة المحاولة
              </button>
              
              <button
                onClick={this.handleGoHome}
                className="flex-1 btn btn-secondary"
              >
                <Home className="w-4 h-4 ml-2" />
                العودة للرئيسية
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
