<?php
/**
 * API endpoint لإعادة تعيين كلمة المرور
 * Reset Password API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";

// التحقق من طريقة الطلب
validateRequestMethod(['POST']);

try {
    // الحصول على البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendErrorResponse('بيانات غير صحيحة');
    }
    
    $token = trim($input['token'] ?? '');
    $newPassword = trim($input['newPassword'] ?? '');
    $confirmPassword = trim($input['confirmPassword'] ?? '');
    
    // التحقق من صحة البيانات
    if (empty($token)) {
        sendErrorResponse('رمز إعادة التعيين مطلوب');
    }
    
    if (empty($newPassword)) {
        sendErrorResponse('كلمة المرور الجديدة مطلوبة');
    }
    
    if (strlen($newPassword) < 8) {
        sendErrorResponse('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
    }
    
    if ($newPassword !== $confirmPassword) {
        sendErrorResponse('كلمة المرور وتأكيدها غير متطابقتين');
    }
    
    // التحقق من قوة كلمة المرور
    if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/', $newPassword)) {
        sendErrorResponse('كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص');
    }
    
    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // البحث عن رمز إعادة التعيين
    $stmt = $connection->prepare("
        SELECT pr.id, pr.user_id, pr.email, pr.expires_at, pr.used,
               u.username, u.is_active
        FROM password_resets pr
        JOIN users u ON pr.user_id = u.id
        WHERE pr.token = ? AND pr.used = 0 AND u.is_active = 1
    ");
    
    $stmt->execute([$token]);
    $resetRecord = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$resetRecord) {
        sendErrorResponse('رمز إعادة التعيين غير صحيح أو مستخدم بالفعل');
    }
    
    // التحقق من انتهاء صلاحية الرمز
    if (strtotime($resetRecord['expires_at']) < time()) {
        sendErrorResponse('رمز إعادة التعيين منتهي الصلاحية');
    }
    
    // تشفير كلمة المرور الجديدة
    $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // بدء المعاملة
    $connection->beginTransaction();
    
    try {
        // تحديث كلمة المرور
        $updateStmt = $connection->prepare("
            UPDATE users 
            SET password_hash = ?, updated_at = NOW() 
            WHERE id = ?
        ");
        
        $updateStmt->execute([$passwordHash, $resetRecord['user_id']]);
        
        // تمييز رمز إعادة التعيين كمستخدم
        $markUsedStmt = $connection->prepare("
            UPDATE password_resets 
            SET used = 1, used_at = NOW() 
            WHERE id = ?
        ");
        
        $markUsedStmt->execute([$resetRecord['id']]);
        
        // تسجيل النشاط
        try {
            $checkTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
            $checkTable->execute();
            
            if ($checkTable->rowCount() > 0) {
                $checkColumns = $connection->prepare("SHOW COLUMNS FROM activity_logs LIKE 'entity_type'");
                $checkColumns->execute();
                
                if ($checkColumns->rowCount() > 0) {
                    $logStmt = $connection->prepare("
                        INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data)
                        VALUES (?, 'password_reset_completed', 'user', ?, ?, ?, ?)
                    ");
                    
                    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                    $resetData = json_encode([
                        'reset_time' => date('Y-m-d H:i:s'),
                        'email' => $resetRecord['email'],
                        'username' => $resetRecord['username']
                    ]);
                    
                    $logStmt->execute([$resetRecord['user_id'], $resetRecord['user_id'], $ipAddress, $userAgent, $resetData]);
                }
            }
        } catch (Exception $logError) {
            // تجاهل أخطاء تسجيل النشاط
            error_log('Activity log error in reset-password: ' . $logError->getMessage());
        }
        
        // تأكيد المعاملة
        $connection->commit();
        
        sendSuccessResponse([
            'message' => 'تم تغيير كلمة المرور بنجاح',
            'timestamp' => date('Y-m-d H:i:s')
        ], 'تم تغيير كلمة المرور بنجاح');
        
    } catch (Exception $e) {
        // التراجع عن المعاملة
        $connection->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    error_log('Database error in reset-password.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}
?>
