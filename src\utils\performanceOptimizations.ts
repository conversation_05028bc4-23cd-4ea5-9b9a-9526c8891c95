/**
 * تحسينات الأداء لمنصة إيكاروس P2P
 * Performance Optimizations for Ikaros P2P Platform
 */

import { useCallback, useMemo, useRef, useEffect, useState } from 'react';
import { debounce, throttle } from 'lodash';

// ===== إدارة الذاكرة والكاش =====

export class MemoryManager {
  private static cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private static readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 دقائق

  /**
   * حفظ البيانات في الكاش
   */
  static set(key: string, data: any, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });

    // تنظيف الكاش إذا أصبح كبيراً جداً
    if (this.cache.size > 1000) {
      this.cleanup();
    }
  }

  /**
   * استرجاع البيانات من الكاش
   */
  static get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // التحقق من انتهاء صلاحية البيانات
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  /**
   * حذف عنصر من الكاش
   */
  static delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * تنظيف الكاش من البيانات المنتهية الصلاحية
   */
  static cleanup(): void {
    const now = Date.now();
    
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * مسح الكاش بالكامل
   */
  static clear(): void {
    this.cache.clear();
  }

  /**
   * الحصول على إحصائيات الكاش
   */
  static getStats(): { size: number; hitRate: number } {
    return {
      size: this.cache.size,
      hitRate: 0 // يمكن تحسينه لاحقاً لتتبع معدل الإصابة
    };
  }
}

// ===== تحسين الشبكة والطلبات =====

export class NetworkOptimizer {
  private static requestQueue = new Map<string, Promise<any>>();
  private static readonly MAX_CONCURRENT_REQUESTS = 6;
  private static activeRequests = 0;

  /**
   * طلب HTTP محسن مع إعادة المحاولة والكاش
   */
  static async optimizedFetch(
    url: string, 
    options: RequestInit = {},
    cacheKey?: string,
    retries: number = 3
  ): Promise<any> {
    // التحقق من الكاش أولاً
    if (cacheKey) {
      const cached = MemoryManager.get(cacheKey);
      if (cached) {
        return cached;
      }
    }

    // تجنب الطلبات المكررة
    if (this.requestQueue.has(url)) {
      return this.requestQueue.get(url);
    }

    // انتظار إذا كان هناك طلبات كثيرة
    await this.waitForSlot();

    const requestPromise = this.executeRequest(url, options, retries);
    this.requestQueue.set(url, requestPromise);

    try {
      const result = await requestPromise;
      
      // حفظ في الكاش إذا كان الطلب ناجحاً
      if (cacheKey && result) {
        MemoryManager.set(cacheKey, result);
      }

      return result;
    } finally {
      this.requestQueue.delete(url);
      this.activeRequests--;
    }
  }

  /**
   * انتظار توفر مكان للطلب
   */
  private static async waitForSlot(): Promise<void> {
    while (this.activeRequests >= this.MAX_CONCURRENT_REQUESTS) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    this.activeRequests++;
  }

  /**
   * تنفيذ الطلب مع إعادة المحاولة
   */
  private static async executeRequest(
    url: string, 
    options: RequestInit, 
    retries: number
  ): Promise<any> {
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 ثانية timeout

        const response = await fetch(url, {
          ...options,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
      } catch (error) {
        if (attempt === retries) {
          throw error;
        }

        // انتظار متزايد بين المحاولات
        const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * تحميل البيانات مسبقاً
   */
  static preloadData(urls: string[], cacheKeys?: string[]): void {
    urls.forEach((url, index) => {
      const cacheKey = cacheKeys?.[index];
      
      // تحميل في الخلفية دون انتظار
      this.optimizedFetch(url, {}, cacheKey).catch(error => {
        console.warn(`Preload failed for ${url}:`, error);
      });
    });
  }
}

// ===== تحسين العرض والتفاعل =====

export class RenderOptimizer {
  /**
   * Hook لتأخير التحديثات المتكررة
   */
  static useDebouncedValue<T>(value: T, delay: number): T {
    const [debouncedValue, setDebouncedValue] = useState<T>(value);

    useEffect(() => {
      const handler = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);

      return () => {
        clearTimeout(handler);
      };
    }, [value, delay]);

    return debouncedValue;
  }

  /**
   * Hook لتقييد معدل التحديثات
   */
  static useThrottledCallback<T extends (...args: any[]) => any>(
    callback: T,
    delay: number
  ): T {
    const throttledCallback = useRef(throttle(callback, delay));

    useEffect(() => {
      throttledCallback.current = throttle(callback, delay);
    }, [callback, delay]);

    return useCallback((...args: any[]) => {
      return throttledCallback.current(...args);
    }, []) as T;
  }

  /**
   * Hook للتحميل التدريجي للقوائم الطويلة
   */
  static useVirtualizedList<T>(
    items: T[],
    itemHeight: number,
    containerHeight: number
  ): { visibleItems: T[]; startIndex: number; endIndex: number } {
    const [scrollTop, setScrollTop] = useState(0);

    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length - 1
    );

    const visibleItems = items.slice(startIndex, endIndex + 1);

    return {
      visibleItems,
      startIndex,
      endIndex
    };
  }

  /**
   * Hook لتحسين إعادة العرض
   */
  static useOptimizedMemo<T>(
    factory: () => T,
    deps: React.DependencyList,
    isEqual?: (a: T, b: T) => boolean
  ): T {
    const memoizedValue = useMemo(factory, deps);
    const previousValue = useRef<T>(memoizedValue);

    if (isEqual && !isEqual(previousValue.current, memoizedValue)) {
      previousValue.current = memoizedValue;
    } else if (!isEqual) {
      previousValue.current = memoizedValue;
    }

    return previousValue.current;
  }
}

// ===== تحسين البيانات والحالة =====

export class DataOptimizer {
  /**
   * ضغط البيانات الكبيرة
   */
  static compressData(data: any): string {
    try {
      const jsonString = JSON.stringify(data);
      
      // ضغط بسيط باستخدام تشفير base64
      // في التطبيق الحقيقي، يمكن استخدام مكتبات ضغط أكثر تقدماً
      return btoa(jsonString);
    } catch (error) {
      console.error('فشل في ضغط البيانات:', error);
      return '';
    }
  }

  /**
   * إلغاء ضغط البيانات
   */
  static decompressData(compressedData: string): any {
    try {
      const jsonString = atob(compressedData);
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('فشل في إلغاء ضغط البيانات:', error);
      return null;
    }
  }

  /**
   * تقسيم البيانات الكبيرة إلى أجزاء
   */
  static chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    
    return chunks;
  }

  /**
   * إزالة البيانات المكررة
   */
  static removeDuplicates<T>(
    array: T[],
    keySelector?: (item: T) => any
  ): T[] {
    if (!keySelector) {
      return [...new Set(array)];
    }

    const seen = new Set();
    return array.filter(item => {
      const key = keySelector(item);
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * فرز محسن للمصفوفات الكبيرة
   */
  static optimizedSort<T>(
    array: T[],
    compareFn?: (a: T, b: T) => number
  ): T[] {
    // استخدام خوارزمية فرز محسنة للمصفوفات الكبيرة
    if (array.length < 1000) {
      return array.sort(compareFn);
    }

    // للمصفوفات الكبيرة، استخدام فرز تدريجي
    return this.mergeSort(array, compareFn);
  }

  /**
   * خوارزمية Merge Sort للمصفوفات الكبيرة
   */
  private static mergeSort<T>(
    array: T[],
    compareFn?: (a: T, b: T) => number
  ): T[] {
    if (array.length <= 1) {
      return array;
    }

    const middle = Math.floor(array.length / 2);
    const left = array.slice(0, middle);
    const right = array.slice(middle);

    return this.merge(
      this.mergeSort(left, compareFn),
      this.mergeSort(right, compareFn),
      compareFn
    );
  }

  /**
   * دمج المصفوفات المرتبة
   */
  private static merge<T>(
    left: T[],
    right: T[],
    compareFn?: (a: T, b: T) => number
  ): T[] {
    const result: T[] = [];
    let leftIndex = 0;
    let rightIndex = 0;

    while (leftIndex < left.length && rightIndex < right.length) {
      const comparison = compareFn 
        ? compareFn(left[leftIndex], right[rightIndex])
        : left[leftIndex] < right[rightIndex] ? -1 : 1;

      if (comparison <= 0) {
        result.push(left[leftIndex]);
        leftIndex++;
      } else {
        result.push(right[rightIndex]);
        rightIndex++;
      }
    }

    return result
      .concat(left.slice(leftIndex))
      .concat(right.slice(rightIndex));
  }
}

// ===== مراقبة الأداء =====

export class PerformanceMonitor {
  private static metrics = new Map<string, number[]>();

  /**
   * قياس وقت تنفيذ دالة
   */
  static async measureExecutionTime<T>(
    name: string,
    fn: () => Promise<T> | T
  ): Promise<T> {
    const startTime = performance.now();
    
    try {
      const result = await fn();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.recordMetric(name, duration);
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.recordMetric(`${name}_error`, duration);
      throw error;
    }
  }

  /**
   * تسجيل مقياس أداء
   */
  static recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const values = this.metrics.get(name)!;
    values.push(value);

    // الاحتفاظ بآخر 100 قياس فقط
    if (values.length > 100) {
      values.shift();
    }
  }

  /**
   * الحصول على إحصائيات الأداء
   */
  static getMetrics(name: string): {
    average: number;
    min: number;
    max: number;
    count: number;
  } | null {
    const values = this.metrics.get(name);
    
    if (!values || values.length === 0) {
      return null;
    }

    return {
      average: values.reduce((sum, val) => sum + val, 0) / values.length,
      min: Math.min(...values),
      max: Math.max(...values),
      count: values.length
    };
  }

  /**
   * الحصول على جميع المقاييس
   */
  static getAllMetrics(): { [key: string]: any } {
    const result: { [key: string]: any } = {};
    
    for (const [name] of this.metrics) {
      result[name] = this.getMetrics(name);
    }
    
    return result;
  }

  /**
   * مسح المقاييس
   */
  static clearMetrics(): void {
    this.metrics.clear();
  }

  /**
   * مراقبة استخدام الذاكرة
   */
  static getMemoryUsage(): {
    used: number;
    total: number;
    percentage: number;
  } {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
        percentage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100)
      };
    }

    return { used: 0, total: 0, percentage: 0 };
  }
}

// ===== تحسين الصور والوسائط =====

export class MediaOptimizer {
  /**
   * ضغط الصورة
   */
  static compressImage(
    file: File,
    maxWidth: number = 800,
    maxHeight: number = 600,
    quality: number = 0.8
  ): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // حساب الأبعاد الجديدة
        let { width, height } = img;
        
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
        
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }

        canvas.width = width;
        canvas.height = height;

        // رسم الصورة المضغوطة
        ctx?.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('فشل في ضغط الصورة'));
            }
          },
          'image/jpeg',
          quality
        );
      };

      img.onerror = () => reject(new Error('فشل في تحميل الصورة'));
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * تحميل الصور بشكل تدريجي
   */
  static lazyLoadImage(src: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => resolve(img);
      img.onerror = () => reject(new Error(`فشل في تحميل الصورة: ${src}`));
      
      img.src = src;
    });
  }
}

// تصدير جميع الفئات
export default {
  MemoryManager,
  NetworkOptimizer,
  RenderOptimizer,
  DataOptimizer,
  PerformanceMonitor,
  MediaOptimizer
};
