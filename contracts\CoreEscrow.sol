// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

/**
 * @title CoreEscrow - Enhanced Multi-Token P2P Trading Contract
 * @dev Core escrow contract for secure P2P trading with multi-token support
 * <AUTHOR> P2P Team
 * @notice This contract handles escrow for P2P trades with enhanced security features
 */
contract CoreEscrow is ReentrancyGuard, Pausable, AccessControl {
    using SafeERC20 for IERC20;

    // Role definitions
    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant ORACLE_ROLE = keccak256("ORACLE_ROLE");
    bytes32 public constant REPUTATION_ROLE = keccak256("REPUTATION_ROLE");

    // Trade status enumeration
    enum TradeStatus {
        Created,        // 0: Trade created, waiting for buyer
        Joined,         // 1: Buyer joined, waiting for payment
        PaymentSent,    // 2: Buyer confirmed payment sent
        PaymentReceived,// 3: Seller confirmed payment received
        Completed,      // 4: Trade completed successfully
        Disputed,       // 5: Trade in dispute
        Cancelled,      // 6: Trade cancelled
        Resolved        // 7: Dispute resolved
    }

    // Trade structure
    struct Trade {
        uint256 id;
        address seller;
        address buyer;
        address token;
        uint256 amount;
        uint256 pricePerToken;
        string currency;
        TradeStatus status;
        uint256 createdAt;
        uint256 lastActivity;
        bool sellerConfirmed;
        bool buyerConfirmed;
        string disputeReason;
        address disputeInitiator;
    }

    // Token support structure
    struct TokenInfo {
        bool isSupported;
        uint256 minAmount;
        uint256 maxAmount;
        uint256 feeRate; // Fee rate in basis points (100 = 1%)
        bool isActive;
    }

    // State variables
    mapping(uint256 => Trade) public trades;
    mapping(address => TokenInfo) public supportedTokens;
    mapping(address => uint256[]) public userTrades;
    mapping(address => bool) public blacklistedUsers;
    
    uint256 public nextTradeId = 1;
    uint256 public platformFeeRate = 50; // 0.5% in basis points
    address public feeRecipient;
    uint256 public disputeTimeLimit = 7 days;
    uint256 public tradeTimeLimit = 24 hours;

    // Events
    event TradeCreated(
        uint256 indexed tradeId,
        address indexed seller,
        address indexed token,
        uint256 amount,
        uint256 pricePerToken,
        string currency
    );

    event TradeJoined(
        uint256 indexed tradeId,
        address indexed buyer,
        uint256 timestamp
    );

    event PaymentSent(
        uint256 indexed tradeId,
        address indexed buyer,
        uint256 timestamp
    );

    event PaymentReceived(
        uint256 indexed tradeId,
        address indexed seller,
        uint256 timestamp
    );

    event TradeCompleted(
        uint256 indexed tradeId,
        address indexed seller,
        address indexed buyer,
        uint256 amount,
        uint256 fee
    );

    event TradeDisputed(
        uint256 indexed tradeId,
        address indexed initiator,
        string reason,
        uint256 timestamp
    );

    event TradeCancelled(
        uint256 indexed tradeId,
        address indexed canceller,
        uint256 timestamp
    );

    event TokenSupported(
        address indexed token,
        uint256 minAmount,
        uint256 maxAmount,
        uint256 feeRate
    );

    event TokenUnsupported(address indexed token);

    event UserBlacklisted(address indexed user, bool status);

    event FeeRateUpdated(uint256 oldRate, uint256 newRate);

    event FeeRecipientUpdated(address oldRecipient, address newRecipient);

    // Modifiers
    modifier onlyTradeParticipant(uint256 tradeId) {
        require(
            msg.sender == trades[tradeId].seller || 
            msg.sender == trades[tradeId].buyer,
            "Not a trade participant"
        );
        _;
    }

    modifier tradeExists(uint256 tradeId) {
        require(trades[tradeId].id != 0, "Trade does not exist");
        _;
    }

    modifier notBlacklisted(address user) {
        require(!blacklistedUsers[user], "User is blacklisted");
        _;
    }

    modifier validTradeStatus(uint256 tradeId, TradeStatus expectedStatus) {
        require(trades[tradeId].status == expectedStatus, "Invalid trade status");
        _;
    }

    /**
     * @dev Constructor
     * @param _feeRecipient Address to receive platform fees
     */
    constructor(address _feeRecipient) {
        require(_feeRecipient != address(0), "Invalid fee recipient");
        
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _grantRole(ADMIN_ROLE, msg.sender);
        
        feeRecipient = _feeRecipient;
    }

    /**
     * @dev Add support for a new token
     * @param token Token contract address
     * @param minAmount Minimum trade amount
     * @param maxAmount Maximum trade amount
     * @param feeRate Fee rate in basis points
     */
    function addSupportedToken(
        address token,
        uint256 minAmount,
        uint256 maxAmount,
        uint256 feeRate
    ) external onlyRole(ADMIN_ROLE) {
        require(token != address(0), "Invalid token address");
        require(maxAmount > minAmount, "Invalid amount range");
        require(feeRate <= 1000, "Fee rate too high"); // Max 10%

        supportedTokens[token] = TokenInfo({
            isSupported: true,
            minAmount: minAmount,
            maxAmount: maxAmount,
            feeRate: feeRate,
            isActive: true
        });

        emit TokenSupported(token, minAmount, maxAmount, feeRate);
    }

    /**
     * @dev Remove support for a token
     * @param token Token contract address
     */
    function removeSupportedToken(address token) external onlyRole(ADMIN_ROLE) {
        require(supportedTokens[token].isSupported, "Token not supported");
        
        delete supportedTokens[token];
        emit TokenUnsupported(token);
    }

    /**
     * @dev Check if a token is supported
     * @param token Token contract address
     * @return bool True if token is supported
     */
    function isTokenSupported(address token) external view returns (bool) {
        return supportedTokens[token].isSupported && supportedTokens[token].isActive;
    }

    /**
     * @dev Create a new trade
     * @param token Token contract address
     * @param amount Amount of tokens to trade
     * @param pricePerToken Price per token in fiat currency
     * @param currency Fiat currency code (e.g., "USD", "SAR")
     * @return tradeId The ID of the created trade
     */
    function createTrade(
        address token,
        uint256 amount,
        uint256 pricePerToken,
        string memory currency
    ) external nonReentrant whenNotPaused notBlacklisted(msg.sender) returns (uint256) {
        require(supportedTokens[token].isSupported && supportedTokens[token].isActive, "Token not supported");
        require(amount >= supportedTokens[token].minAmount, "Amount below minimum");
        require(amount <= supportedTokens[token].maxAmount, "Amount above maximum");
        require(pricePerToken > 0, "Invalid price");
        require(bytes(currency).length > 0, "Invalid currency");

        // Transfer tokens to escrow
        IERC20(token).safeTransferFrom(msg.sender, address(this), amount);

        uint256 tradeId = nextTradeId++;
        
        trades[tradeId] = Trade({
            id: tradeId,
            seller: msg.sender,
            buyer: address(0),
            token: token,
            amount: amount,
            pricePerToken: pricePerToken,
            currency: currency,
            status: TradeStatus.Created,
            createdAt: block.timestamp,
            lastActivity: block.timestamp,
            sellerConfirmed: false,
            buyerConfirmed: false,
            disputeReason: "",
            disputeInitiator: address(0)
        });

        userTrades[msg.sender].push(tradeId);

        emit TradeCreated(tradeId, msg.sender, token, amount, pricePerToken, currency);
        
        return tradeId;
    }

    /**
     * @dev Join an existing trade as buyer
     * @param tradeId ID of the trade to join
     */
    function joinTrade(uint256 tradeId) 
        external 
        nonReentrant 
        whenNotPaused 
        notBlacklisted(msg.sender)
        tradeExists(tradeId)
        validTradeStatus(tradeId, TradeStatus.Created)
    {
        Trade storage trade = trades[tradeId];
        require(trade.seller != msg.sender, "Cannot join own trade");
        require(block.timestamp <= trade.createdAt + tradeTimeLimit, "Trade expired");

        trade.buyer = msg.sender;
        trade.status = TradeStatus.Joined;
        trade.lastActivity = block.timestamp;

        userTrades[msg.sender].push(tradeId);

        emit TradeJoined(tradeId, msg.sender, block.timestamp);
    }

    /**
     * @dev Confirm payment has been sent (called by buyer)
     * @param tradeId ID of the trade
     */
    function confirmPaymentSent(uint256 tradeId)
        external
        nonReentrant
        whenNotPaused
        tradeExists(tradeId)
        validTradeStatus(tradeId, TradeStatus.Joined)
    {
        Trade storage trade = trades[tradeId];
        require(msg.sender == trade.buyer, "Only buyer can confirm payment sent");

        trade.status = TradeStatus.PaymentSent;
        trade.buyerConfirmed = true;
        trade.lastActivity = block.timestamp;

        emit PaymentSent(tradeId, msg.sender, block.timestamp);
    }

    /**
     * @dev Confirm payment has been received (called by seller)
     * @param tradeId ID of the trade
     */
    function confirmPaymentReceived(uint256 tradeId)
        external
        nonReentrant
        whenNotPaused
        tradeExists(tradeId)
        validTradeStatus(tradeId, TradeStatus.PaymentSent)
    {
        Trade storage trade = trades[tradeId];
        require(msg.sender == trade.seller, "Only seller can confirm payment received");

        trade.status = TradeStatus.Completed;
        trade.sellerConfirmed = true;
        trade.lastActivity = block.timestamp;

        // Calculate and transfer fees
        uint256 platformFee = (trade.amount * platformFeeRate) / 10000;
        uint256 sellerAmount = trade.amount - platformFee;

        // Transfer tokens to buyer
        IERC20(trade.token).safeTransfer(trade.buyer, sellerAmount);
        
        // Transfer platform fee
        if (platformFee > 0) {
            IERC20(trade.token).safeTransfer(feeRecipient, platformFee);
        }

        emit PaymentReceived(tradeId, msg.sender, block.timestamp);
        emit TradeCompleted(tradeId, trade.seller, trade.buyer, sellerAmount, platformFee);
    }

    /**
     * @dev Request a dispute for a trade
     * @param tradeId ID of the trade
     * @param reason Reason for the dispute
     */
    function requestDispute(uint256 tradeId, string memory reason)
        external
        nonReentrant
        whenNotPaused
        tradeExists(tradeId)
        onlyTradeParticipant(tradeId)
    {
        Trade storage trade = trades[tradeId];
        require(
            trade.status == TradeStatus.Joined || 
            trade.status == TradeStatus.PaymentSent,
            "Cannot dispute at this stage"
        );
        require(bytes(reason).length > 0, "Dispute reason required");

        trade.status = TradeStatus.Disputed;
        trade.disputeReason = reason;
        trade.disputeInitiator = msg.sender;
        trade.lastActivity = block.timestamp;

        emit TradeDisputed(tradeId, msg.sender, reason, block.timestamp);
    }

    /**
     * @dev Cancel a trade (only seller can cancel before buyer joins)
     * @param tradeId ID of the trade to cancel
     */
    function cancelTrade(uint256 tradeId)
        external
        nonReentrant
        whenNotPaused
        tradeExists(tradeId)
        validTradeStatus(tradeId, TradeStatus.Created)
    {
        Trade storage trade = trades[tradeId];
        require(msg.sender == trade.seller, "Only seller can cancel");

        trade.status = TradeStatus.Cancelled;
        trade.lastActivity = block.timestamp;

        // Return tokens to seller
        IERC20(trade.token).safeTransfer(trade.seller, trade.amount);

        emit TradeCancelled(tradeId, msg.sender, block.timestamp);
    }

    /**
     * @dev Get trade details
     * @param tradeId ID of the trade
     * @return Trade struct
     */
    function getTrade(uint256 tradeId) external view returns (Trade memory) {
        require(trades[tradeId].id != 0, "Trade does not exist");
        return trades[tradeId];
    }

    /**
     * @dev Get user's trade IDs
     * @param user User address
     * @return Array of trade IDs
     */
    function getUserTrades(address user) external view returns (uint256[] memory) {
        return userTrades[user];
    }

    /**
     * @dev Get token information
     * @param token Token address
     * @return TokenInfo struct
     */
    function getTokenInfo(address token) external view returns (TokenInfo memory) {
        return supportedTokens[token];
    }

    // Admin functions
    function setPlatformFeeRate(uint256 newRate) external onlyRole(ADMIN_ROLE) {
        require(newRate <= 1000, "Fee rate too high"); // Max 10%
        uint256 oldRate = platformFeeRate;
        platformFeeRate = newRate;
        emit FeeRateUpdated(oldRate, newRate);
    }

    function setFeeRecipient(address newRecipient) external onlyRole(ADMIN_ROLE) {
        require(newRecipient != address(0), "Invalid recipient");
        address oldRecipient = feeRecipient;
        feeRecipient = newRecipient;
        emit FeeRecipientUpdated(oldRecipient, newRecipient);
    }

    function setUserBlacklist(address user, bool status) external onlyRole(ADMIN_ROLE) {
        blacklistedUsers[user] = status;
        emit UserBlacklisted(user, status);
    }

    function pause() external onlyRole(ADMIN_ROLE) {
        _pause();
    }

    function unpause() external onlyRole(ADMIN_ROLE) {
        _unpause();
    }

    // Emergency functions
    function emergencyWithdraw(address token, uint256 amount) external onlyRole(DEFAULT_ADMIN_ROLE) {
        IERC20(token).safeTransfer(msg.sender, amount);
    }
}
