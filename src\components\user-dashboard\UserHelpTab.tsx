'use client';

import { useState } from 'react';
import {
  HelpCircle,
  Search,
  ChevronDown,
  ChevronUp,
  MessageSquare,
  Mail,
  Phone,
  ExternalLink,
  Book,
  Video,
  FileText
} from 'lucide-react';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
}

export default function UserHelpTab() {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('all');

  const faqs: FAQ[] = [
    {
      id: '1',
      question: 'كيف أبدأ التداول على المنصة؟',
      answer: 'للبدء في التداول، تحتاج أولاً إلى ربط محفظتك الرقمية، ثم يمكنك تصفح العروض المتاحة أو إنشاء عرض جديد.',
      category: 'trading'
    },
    {
      id: '2',
      question: 'ما هي رسوم المنصة؟',
      answer: 'تتقاضى المنصة رسوماً قدرها 1.5% من قيمة كل صفقة مكتملة، وهذه الرسوم تُخصم تلقائياً من البائع.',
      category: 'fees'
    },
    {
      id: '3',
      question: 'كيف يعمل نظام الضمان؟',
      answer: 'يستخدم النظام العقود الذكية لضمان أمان الصفقات. عند إنشاء صفقة، يتم حجز المبلغ في العقد الذكي حتى تأكيد استلام الدفع.',
      category: 'security'
    },
    {
      id: '4',
      question: 'ماذا أفعل في حالة النزاع؟',
      answer: 'في حالة حدوث نزاع، يمكنك رفع شكوى من خلال صفحة الصفقة، وسيتدخل فريق الدعم لحل النزاع خلال 24-48 ساعة.',
      category: 'disputes'
    },
    {
      id: '5',
      question: 'كيف أربط محفظتي؟',
      answer: 'اضغط على زر "ربط المحفظة" في أعلى الصفحة، واختر نوع المحفظة (MetaMask أو Trust Wallet)، ثم اتبع التعليمات.',
      category: 'wallet'
    }
  ];

  const categories = [
    { id: 'all', name: 'جميع الفئات' },
    { id: 'trading', name: 'التداول' },
    { id: 'wallet', name: 'المحفظة' },
    { id: 'security', name: 'الأمان' },
    { id: 'fees', name: 'الرسوم' },
    { id: 'disputes', name: 'النزاعات' }
  ];

  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = searchTerm === '' ||
      faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const toggleFAQ = (id: string) => {
    setExpandedFAQ(expandedFAQ === id ? null : id);
  };

  return (
    <div className="space-y-6">
      {/* رأس القسم */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <HelpCircle className="w-6 h-6 ml-3 text-blue-600" />
            المساعدة والدعم
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            احصل على المساعدة والإجابات على أسئلتك
          </p>
        </div>
      </div>

      {/* روابط سريعة للمساعدة */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
          <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
            <Book className="w-8 h-8 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">دليل المستخدم</h3>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            دليل شامل لاستخدام جميع ميزات المنصة
          </p>
          <button className="flex items-center space-x-2 rtl:space-x-reverse text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
            <span>قراءة الدليل</span>
            <ExternalLink className="w-4 h-4" />
          </button>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
          <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
            <Video className="w-8 h-8 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">فيديوهات تعليمية</h3>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            شاهد فيديوهات تعليمية لتعلم كيفية استخدام المنصة
          </p>
          <button className="flex items-center space-x-2 rtl:space-x-reverse text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300">
            <span>مشاهدة الفيديوهات</span>
            <ExternalLink className="w-4 h-4" />
          </button>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
          <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
            <MessageSquare className="w-8 h-8 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">الدردشة المباشرة</h3>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            تحدث مع فريق الدعم مباشرة للحصول على مساعدة فورية
          </p>
          <button className="flex items-center space-x-2 rtl:space-x-reverse text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300">
            <span>بدء المحادثة</span>
            <MessageSquare className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* الأسئلة الشائعة */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">الأسئلة الشائعة</h3>

        {/* البحث والفلاتر */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث في الأسئلة..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* قائمة الأسئلة */}
        <div className="space-y-4">
          {filteredFAQs.map((faq) => (
            <div key={faq.id} className="border border-gray-200 dark:border-gray-700 rounded-lg">
              <button
                onClick={() => toggleFAQ(faq.id)}
                className="w-full px-4 py-3 text-right flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <span className="font-medium text-gray-900 dark:text-white">
                  {faq.question}
                </span>
                {expandedFAQ === faq.id ? (
                  <ChevronUp className="w-5 h-5 text-gray-500" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-gray-500" />
                )}
              </button>
              {expandedFAQ === faq.id && (
                <div className="px-4 pb-3 text-gray-600 dark:text-gray-400">
                  {faq.answer}
                </div>
              )}
            </div>
          ))}
        </div>

        {filteredFAQs.length === 0 && (
          <div className="text-center py-8">
            <HelpCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              لا توجد نتائج
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              لم نجد أسئلة تطابق بحثك. جرب كلمات مختلفة أو تواصل معنا مباشرة.
            </p>
          </div>
        )}
      </div>

      {/* معلومات التواصل */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">تواصل معنا</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <Mail className="w-5 h-5 text-blue-600" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">البريد الإلكتروني</p>
              <p className="text-sm text-gray-600 dark:text-gray-400"><EMAIL></p>
            </div>
          </div>

          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <Phone className="w-5 h-5 text-green-600" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">الهاتف</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">+966 50 123 4567</p>
            </div>
          </div>

          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <MessageSquare className="w-5 h-5 text-purple-600" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">تيليجرام</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">@ikaros_support</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
