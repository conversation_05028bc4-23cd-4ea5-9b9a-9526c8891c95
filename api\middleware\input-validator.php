<?php
/**
 * نظام التحقق من المدخلات المحسن
 * Enhanced Input Validation System
 */

class InputValidator {
    private $errors = [];
    private $sanitizedData = [];
    
    /**
     * قواعد التحقق المحددة مسبقاً
     */
    private $predefinedRules = [
        'email' => '/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
        'phone' => '/^[\+]?[1-9][\d]{0,15}$/',
        'username' => '/^[a-zA-Z0-9_]{3,30}$/',
        'password' => '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/',
        'wallet_address' => '/^0x[a-fA-F0-9]{40}$/',
        'transaction_hash' => '/^0x[a-fA-F0-9]{64}$/',
        'currency_code' => '/^[A-Z]{3,10}$/',
        'country_code' => '/^[A-Z]{2,3}$/',
        'language_code' => '/^(ar|en)$/',
        'theme' => '/^(light|dark)$/',
        'trade_status' => '/^(pending|active|completed|cancelled|disputed)$/',
        'transaction_type' => '/^(deposit|withdrawal|trade|fee|refund)$/'
    ];
    
    /**
     * التحقق من البيانات
     */
    public function validate($data, $rules) {
        $this->errors = [];
        $this->sanitizedData = [];
        
        foreach ($rules as $field => $fieldRules) {
            $value = $data[$field] ?? null;
            $this->validateField($field, $value, $fieldRules);
        }
        
        return empty($this->errors);
    }
    
    /**
     * التحقق من حقل واحد
     */
    private function validateField($field, $value, $rules) {
        $rulesArray = is_string($rules) ? explode('|', $rules) : $rules;
        
        foreach ($rulesArray as $rule) {
            $this->applyRule($field, $value, $rule);
        }
    }
    
    /**
     * تطبيق قاعدة واحدة
     */
    private function applyRule($field, $value, $rule) {
        // تحليل القاعدة والمعاملات
        $ruleParts = explode(':', $rule);
        $ruleName = $ruleParts[0];
        $ruleParams = isset($ruleParts[1]) ? explode(',', $ruleParts[1]) : [];
        
        switch ($ruleName) {
            case 'required':
                if (empty($value) && $value !== '0' && $value !== 0) {
                    $this->addError($field, "الحقل $field مطلوب");
                    return;
                }
                break;
                
            case 'optional':
                if (empty($value)) {
                    $this->sanitizedData[$field] = null;
                    return;
                }
                break;
                
            case 'string':
                if (!is_string($value)) {
                    $this->addError($field, "الحقل $field يجب أن يكون نص");
                    return;
                }
                break;
                
            case 'numeric':
                if (!is_numeric($value)) {
                    $this->addError($field, "الحقل $field يجب أن يكون رقم");
                    return;
                }
                break;
                
            case 'integer':
                if (!filter_var($value, FILTER_VALIDATE_INT)) {
                    $this->addError($field, "الحقل $field يجب أن يكون رقم صحيح");
                    return;
                }
                break;
                
            case 'float':
                if (!filter_var($value, FILTER_VALIDATE_FLOAT)) {
                    $this->addError($field, "الحقل $field يجب أن يكون رقم عشري");
                    return;
                }
                break;
                
            case 'boolean':
                if (!is_bool($value) && !in_array($value, [0, 1, '0', '1', 'true', 'false'])) {
                    $this->addError($field, "الحقل $field يجب أن يكون قيمة منطقية");
                    return;
                }
                break;
                
            case 'min':
                $min = floatval($ruleParams[0]);
                if (is_numeric($value) && floatval($value) < $min) {
                    $this->addError($field, "الحقل $field يجب أن يكون أكبر من أو يساوي $min");
                    return;
                } elseif (is_string($value) && strlen($value) < $min) {
                    $this->addError($field, "الحقل $field يجب أن يحتوي على $min أحرف على الأقل");
                    return;
                }
                break;
                
            case 'max':
                $max = floatval($ruleParams[0]);
                if (is_numeric($value) && floatval($value) > $max) {
                    $this->addError($field, "الحقل $field يجب أن يكون أقل من أو يساوي $max");
                    return;
                } elseif (is_string($value) && strlen($value) > $max) {
                    $this->addError($field, "الحقل $field يجب أن يحتوي على $max أحرف كحد أقصى");
                    return;
                }
                break;
                
            case 'in':
                if (!in_array($value, $ruleParams)) {
                    $allowedValues = implode(', ', $ruleParams);
                    $this->addError($field, "الحقل $field يجب أن يكون أحد القيم التالية: $allowedValues");
                    return;
                }
                break;
                
            case 'regex':
                $pattern = $ruleParams[0];
                if (!preg_match($pattern, $value)) {
                    $this->addError($field, "الحقل $field لا يتطابق مع النمط المطلوب");
                    return;
                }
                break;
                
            case 'email':
                if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $this->addError($field, "الحقل $field يجب أن يكون بريد إلكتروني صحيح");
                    return;
                }
                break;
                
            case 'url':
                if (!filter_var($value, FILTER_VALIDATE_URL)) {
                    $this->addError($field, "الحقل $field يجب أن يكون رابط صحيح");
                    return;
                }
                break;
                
            case 'json':
                if (!$this->isValidJson($value)) {
                    $this->addError($field, "الحقل $field يجب أن يكون JSON صحيح");
                    return;
                }
                break;
                
            case 'date':
                if (!$this->isValidDate($value)) {
                    $this->addError($field, "الحقل $field يجب أن يكون تاريخ صحيح");
                    return;
                }
                break;
                
            case 'unique':
                // يتطلب معاملات: table,column,exclude_id
                if (count($ruleParams) >= 2) {
                    if (!$this->isUnique($value, $ruleParams[0], $ruleParams[1], $ruleParams[2] ?? null)) {
                        $this->addError($field, "الحقل $field موجود مسبقاً");
                        return;
                    }
                }
                break;
                
            case 'exists':
                // يتطلب معاملات: table,column
                if (count($ruleParams) >= 2) {
                    if (!$this->exists($value, $ruleParams[0], $ruleParams[1])) {
                        $this->addError($field, "الحقل $field غير موجود");
                        return;
                    }
                }
                break;
                
            default:
                // فحص القواعد المحددة مسبقاً
                if (isset($this->predefinedRules[$ruleName])) {
                    if (!preg_match($this->predefinedRules[$ruleName], $value)) {
                        $this->addError($field, "الحقل $field لا يتطابق مع النمط المطلوب لـ $ruleName");
                        return;
                    }
                }
                break;
        }
        
        // تنظيف وحفظ القيمة إذا مرت جميع القواعد
        $this->sanitizedData[$field] = $this->sanitizeValue($value, $ruleName);
    }
    
    /**
     * تنظيف القيمة
     */
    private function sanitizeValue($value, $type = null) {
        if ($value === null) return null;
        
        switch ($type) {
            case 'string':
                return htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
            case 'integer':
                return intval($value);
            case 'float':
                return floatval($value);
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'email':
                return filter_var(trim($value), FILTER_SANITIZE_EMAIL);
            case 'url':
                return filter_var(trim($value), FILTER_SANITIZE_URL);
            default:
                return is_string($value) ? htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8') : $value;
        }
    }
    
    /**
     * إضافة خطأ
     */
    private function addError($field, $message) {
        if (!isset($this->errors[$field])) {
            $this->errors[$field] = [];
        }
        $this->errors[$field][] = $message;
    }
    
    /**
     * الحصول على الأخطاء
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * الحصول على البيانات المنظفة
     */
    public function getSanitizedData() {
        return $this->sanitizedData;
    }
    
    /**
     * فحص صحة JSON
     */
    private function isValidJson($value) {
        if (!is_string($value)) return false;
        json_decode($value);
        return json_last_error() === JSON_ERROR_NONE;
    }
    
    /**
     * فحص صحة التاريخ
     */
    private function isValidDate($value, $format = 'Y-m-d H:i:s') {
        $date = DateTime::createFromFormat($format, $value);
        return $date && $date->format($format) === $value;
    }
    
    /**
     * فحص التفرد في قاعدة البيانات
     */
    private function isUnique($value, $table, $column, $excludeId = null) {
        global $connection; // يجب تمرير الاتصال بقاعدة البيانات
        
        if (!$connection) return true; // تجاهل إذا لم يكن هناك اتصال
        
        $sql = "SELECT COUNT(*) FROM $table WHERE $column = ?";
        $params = [$value];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $connection->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchColumn() == 0;
    }
    
    /**
     * فحص وجود القيمة في قاعدة البيانات
     */
    private function exists($value, $table, $column) {
        global $connection; // يجب تمرير الاتصال بقاعدة البيانات
        
        if (!$connection) return true; // تجاهل إذا لم يكن هناك اتصال
        
        $stmt = $connection->prepare("SELECT COUNT(*) FROM $table WHERE $column = ?");
        $stmt->execute([$value]);
        
        return $stmt->fetchColumn() > 0;
    }
    
    /**
     * التحقق السريع من البيانات
     */
    public static function quickValidate($data, $rules) {
        $validator = new self();
        $isValid = $validator->validate($data, $rules);
        
        return [
            'valid' => $isValid,
            'errors' => $validator->getErrors(),
            'data' => $validator->getSanitizedData()
        ];
    }
}

/**
 * دالة مساعدة للتحقق السريع
 */
function validateInput($data, $rules) {
    return InputValidator::quickValidate($data, $rules);
}
?>
