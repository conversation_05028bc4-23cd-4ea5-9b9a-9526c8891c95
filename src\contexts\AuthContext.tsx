'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { walletService } from '@/services/walletService';
import { databaseService } from '@/services/databaseService';
import { apiService } from '@/services/apiService';

export interface User {
  id?: string;
  walletAddress: string;
  username?: string;
  email?: string;
  fullName?: string;
  phone?: string;
  isVerified: boolean;
  isAdmin: boolean;
  isActive?: boolean;
  rating?: number;
  totalTrades?: number;
}

export interface AuthContextType {
  // حالة المصادقة
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  
  // حالة المحفظة
  isWalletConnected: boolean;
  walletAddress: string | null;
  walletBalance: string;
  walletNetwork: string;
  
  // دوال المصادقة
  login: (email: string, password: string) => Promise<void>;
  register: (userData: Partial<User>) => Promise<void>;
  logout: () => void;

  // دوال إدارة المدراء
  adminLogin: (username: string, password: string) => Promise<void>;
  adminLogout: () => Promise<void>;
  isAdmin: boolean;
  adminData: any;
  
  // دوال المحفظة
  connectWallet: () => Promise<void>;
  disconnectWallet: () => void;
  
  // دوال المساعدة
  checkAuthStatus: () => Promise<void>;
  updateUserProfile: (userData: Partial<User>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  // حالة المصادقة
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // حالة المحفظة
  const [isWalletConnected, setIsWalletConnected] = useState(false);
  const [walletAddress, setWalletAddress] = useState<string | null>(null);
  const [walletBalance, setWalletBalance] = useState('0');
  const [walletNetwork, setWalletNetwork] = useState('');

  // حالة المدراء
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [adminData, setAdminData] = useState<any>(null);

  // التحقق من حالة المصادقة عند بدء التطبيق
  useEffect(() => {
    checkAuthStatus();
  }, []);

  // مراقبة تغييرات المحفظة
  useEffect(() => {
    if (typeof window !== 'undefined' && window.ethereum) {
      // مراقبة تغيير الحسابات
      window.ethereum.on('accountsChanged', handleAccountsChanged);
      // مراقبة تغيير الشبكة
      window.ethereum.on('chainChanged', handleChainChanged);
      
      return () => {
        window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
        window.ethereum.removeListener('chainChanged', handleChainChanged);
      };
    }
  }, []);

  const handleAccountsChanged = async (accounts: string[]) => {
    if (accounts.length === 0) {
      // تم قطع الاتصال
      disconnectWallet();
    } else if (accounts[0] !== walletAddress) {
      // تم تغيير الحساب
      await updateWalletInfo(accounts[0]);
    }
  };

  const handleChainChanged = async () => {
    // إعادة تحميل معلومات المحفظة عند تغيير الشبكة
    if (isWalletConnected) {
      await updateWalletInfo();
    }
  };

  const updateWalletInfo = async (address?: string) => {
    try {
      const walletInfo = await walletService.getWalletInfo();
      if (walletInfo) {
        setIsWalletConnected(true);
        setWalletAddress(address || walletInfo.address);
        setWalletBalance(walletInfo.balance);
        setWalletNetwork(walletInfo.network);

        // إذا كان المستخدم مسجل، تحديث بيانات المستخدم
        if (isAuthenticated && user && user.walletAddress !== walletInfo.address) {
          try {
            await updateUserProfile({ walletAddress: walletInfo.address });
          } catch (profileError) {
            console.warn('تحذير في تحديث الملف الشخصي:', profileError);
          }
        }
      }
    } catch (error) {
      console.warn('تحذير في تحديث معلومات المحفظة:', error);
      // لا نرمي خطأ لتجنب كسر التطبيق
    }
  };

  const checkAuthStatus = async () => {
    setIsLoading(true);
    try {
      // التحقق من الجلسة المحفوظة أولاً (أهم من المحفظة)
      const savedSession = localStorage.getItem('userSession');
      if (savedSession) {
        try {
          const sessionData = JSON.parse(savedSession);

          // التحقق من انتهاء صلاحية الجلسة (7 أيام)
          const sessionExpiry = new Date(sessionData.timestamp);
          sessionExpiry.setDate(sessionExpiry.getDate() + 7);

          if (new Date() < sessionExpiry) {
            setUser(sessionData.user);
            setIsAuthenticated(true);
          } else {
            // انتهت صلاحية الجلسة
            localStorage.removeItem('userSession');
            localStorage.removeItem('userToken');
          }
        } catch (error) {
          console.error('Error parsing saved session:', error);
          localStorage.removeItem('userSession');
          localStorage.removeItem('userToken');
        }
      }

      // التحقق من حالة المحفظة (بشكل منفصل وآمن)
      try {
        const walletConnected = walletService.isWalletConnected();
        setIsWalletConnected(walletConnected);

        if (walletConnected) {
          const address = walletService.getCurrentAccount();
          setWalletAddress(address);

          // جلب معلومات المحفظة بشكل آمن
          try {
            const walletInfo = await walletService.getWalletInfo();
            if (walletInfo) {
              setWalletBalance(walletInfo.balance);
              setWalletNetwork(walletInfo.network);
            }
          } catch (walletError) {
            console.warn('تحذير في جلب معلومات المحفظة:', walletError);
            // لا نرمي خطأ، فقط نسجل تحذير
          }
        }
      } catch (walletError) {
        console.warn('تحذير في فحص حالة المحفظة:', walletError);
        // إعادة تعيين حالة المحفظة في حالة الخطأ
        setIsWalletConnected(false);
        setWalletAddress(null);
        setWalletBalance('0');
        setWalletNetwork('');
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      // في حالة خطأ عام، نتأكد من عدم تأثيره على المصادقة
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      // استدعاء API باستخدام apiService
      const data = await apiService.auth.login(email, password);

      if (data.success) {
        const userData = data.user;

        setUser(userData);
        setIsAuthenticated(true);

        // حفظ الجلسة والرمز المميز
        const sessionData = {
          user: userData,
          timestamp: new Date().toISOString()
        };
        localStorage.setItem('userSession', JSON.stringify(sessionData));

        // حفظ الرمز المميز إذا كان متوفراً
        if (data.token) {
          localStorage.setItem('userToken', data.token);
        }

        // توجيه المستخدم للوحة التحكم المناسبة
        if (typeof window !== 'undefined') {
          if (userData.isAdmin) {
            window.location.href = '/admin';
          } else {
            window.location.href = '/user-dashboard';
          }
        }
      } else {
        throw new Error(data.error || 'فشل في تسجيل الدخول');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const register = async (userData: Partial<User>) => {
    try {
      // استدعاء API باستخدام apiService
      const data = await apiService.auth.register(userData);

      if (data.success) {
        const newUser = data.user;

        setUser(newUser);
        setIsAuthenticated(true);

        // حفظ الجلسة
        const sessionData = {
          user: newUser,
          timestamp: new Date().toISOString()
        };
        localStorage.setItem('userSession', JSON.stringify(sessionData));

        // حفظ الرمز المميز إذا كان متوفراً
        if (data.token) {
          localStorage.setItem('userToken', data.token);
        }

        // إظهار رسالة ترحيب وتوجيه للوحة التحكم
        if (typeof window !== 'undefined') {
          // إظهار رسالة ترحيب
          setTimeout(() => {
            if (newUser.isAdmin) {
              window.location.href = '/admin?welcome=true';
            } else {
              window.location.href = '/user-dashboard?welcome=true';
            }
          }, 1000);
        }
      } else {
        throw new Error(data.error || 'فشل في التسجيل');
      }
    } catch (error: any) {
      console.error('Register error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      // إرسال طلب تسجيل الخروج للخادم
      if (user) {
        await apiService.auth.logout(user.id, localStorage.getItem('userToken') || undefined);
      }
    } catch (error) {
      console.warn('Error calling logout API:', error);
    } finally {
      // تنظيف البيانات المحلية
      setIsAuthenticated(false);
      setUser(null);
      localStorage.removeItem('userSession');
      localStorage.removeItem('userToken');

      // إعادة توجيه للصفحة الرئيسية
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    }
  };

  // دوال إدارة المدراء
  const adminLogin = async (username: string, password: string) => {
    try {
      setIsLoading(true);

      const response = await apiService.admin.login({ username, password });

      if (response.success) {
        // حفظ بيانات الجلسة الإدارية
        const sessionData = {
          ...response.admin,
          timestamp: new Date().toISOString(),
          loginMethod: 'credentials',
          expiresAt: response.admin.expiresAt || new Date(Date.now() + (8 * 60 * 60 * 1000)).toISOString()
        };

        localStorage.setItem('adminSession', JSON.stringify(sessionData));

        // حفظ رمز الجلسة إذا كان متوفراً
        if (response.token) {
          localStorage.setItem('adminToken', response.token);
        }

        // تحديث الحالة
        setIsAdmin(true);
        setAdminData(response.admin);

        // إذا كان المدير مستخدماً عادياً أيضاً، تحديث بيانات المستخدم
        if (response.admin.user_id) {
          setIsAuthenticated(true);
          setUser({
            id: response.admin.user_id,
            username: response.admin.username,
            email: response.admin.email,
            fullName: response.admin.fullName,
            walletAddress: response.admin.walletAddress,
            isAdmin: true,
            isVerified: true,
            isActive: true
          });
        }
      } else {
        throw new Error(response.error || 'فشل في تسجيل الدخول');
      }
    } catch (error) {
      console.error('Admin login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const adminLogout = async () => {
    try {
      // إرسال طلب تسجيل الخروج للخادم
      await apiService.admin.logout();
    } catch (error) {
      console.warn('Error calling admin logout API:', error);
    } finally {
      // تنظيف البيانات المحلية
      setIsAdmin(false);
      setAdminData(null);
      localStorage.removeItem('adminSession');
      localStorage.removeItem('adminToken');

      // إذا لم يكن هناك جلسة مستخدم عادية، تنظيف كامل
      const userSession = localStorage.getItem('userSession');
      if (!userSession) {
        setIsAuthenticated(false);
        setUser(null);
        setIsWalletConnected(false);
        setWalletAddress(null);
        setWalletBalance('0');
        setWalletNetwork('');
      }
    }
  };

  const connectWallet = async () => {
    try {
      const walletInfo = await walletService.connectWallet('metamask');
      setIsWalletConnected(true);
      setWalletAddress(walletInfo.address);
      setWalletBalance(walletInfo.balance);
      setWalletNetwork(walletInfo.network);

      // إذا كان المستخدم مسجل، ربط المحفظة بالحساب
      if (isAuthenticated && user) {
        try {
          await updateUserProfile({ walletAddress: walletInfo.address });
        } catch (profileError) {
          console.warn('تحذير في ربط المحفظة بالحساب:', profileError);
          // لا نرمي خطأ لأن الاتصال بالمحفظة نجح
        }
      }
    } catch (error) {
      console.error('Wallet connection error:', error);
      throw error;
    }
  };

  const disconnectWallet = () => {
    walletService.disconnectWallet();
    setIsWalletConnected(false);
    setWalletAddress(null);
    setWalletBalance('0');
    setWalletNetwork('');
  };

  const updateUserProfile = async (userData: Partial<User>) => {
    try {
      if (!user) return;

      // استدعاء API باستخدام apiService
      await apiService.users.updateProfile(userData);

      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);

      // تحديث الجلسة المحفوظة
      const sessionData = {
        user: updatedUser,
        timestamp: new Date().toISOString()
      };
      localStorage.setItem('userSession', JSON.stringify(sessionData));
    } catch (error) {
      console.error('Profile update error:', error);
      throw error;
    }
  };

  const value: AuthContextType = {
    // حالة المصادقة
    isAuthenticated,
    user,
    isLoading,
    
    // حالة المحفظة
    isWalletConnected,
    walletAddress,
    walletBalance,
    walletNetwork,
    
    // دوال المصادقة
    login,
    register,
    logout,

    // دوال إدارة المدراء
    adminLogin,
    adminLogout,
    isAdmin,
    adminData,

    // دوال المحفظة
    connectWallet,
    disconnectWallet,

    // دوال المساعدة
    checkAuthStatus,
    updateUserProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// AuthProvider مُصدر بالفعل أعلاه
