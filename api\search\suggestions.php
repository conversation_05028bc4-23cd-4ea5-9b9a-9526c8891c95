<?php
/**
 * API endpoint للاقتراحات الذكية في البحث
 * Search Suggestions API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";
require_once __DIR__ . "/../middleware/auth.php";

// التحقق من طريقة الطلب
validateRequestMethod(['GET']);

/**
 * الحصول على اقتراحات العناوين
 */
function getTitleSuggestions($connection, $query, $limit = 10) {
    $stmt = $connection->prepare("
        SELECT DISTINCT title, COUNT(*) as relevance
        FROM offers 
        WHERE status = 'active' AND title LIKE ?
        GROUP BY title
        ORDER BY relevance DESC, title ASC
        LIMIT ?
    ");
    
    $searchTerm = '%' . $query . '%';
    $stmt->execute([$searchTerm, $limit]);
    
    return array_map(function($row) {
        return [
            'text' => $row['title'],
            'type' => 'title',
            'relevance' => (int)$row['relevance']
        ];
    }, $stmt->fetchAll(PDO::FETCH_ASSOC));
}

/**
 * الحصول على اقتراحات أسماء المستخدمين
 */
function getUsernameSuggestions($connection, $query, $limit = 5) {
    $stmt = $connection->prepare("
        SELECT DISTINCT u.username, u.rating, u.total_trades
        FROM users u
        JOIN offers o ON u.id = o.user_id
        WHERE u.is_active = 1 AND o.status = 'active' AND u.username LIKE ?
        ORDER BY u.rating DESC, u.total_trades DESC
        LIMIT ?
    ");
    
    $searchTerm = '%' . $query . '%';
    $stmt->execute([$searchTerm, $limit]);
    
    return array_map(function($row) {
        return [
            'text' => $row['username'],
            'type' => 'username',
            'rating' => (float)$row['rating'],
            'totalTrades' => (int)$row['total_trades']
        ];
    }, $stmt->fetchAll(PDO::FETCH_ASSOC));
}

/**
 * الحصول على اقتراحات العملات
 */
function getCurrencySuggestions($connection, $query, $limit = 5) {
    $stmt = $connection->prepare("
        SELECT DISTINCT currency, COUNT(*) as count
        FROM offers 
        WHERE status = 'active' AND currency LIKE ?
        GROUP BY currency
        ORDER BY count DESC
        LIMIT ?
    ");
    
    $searchTerm = '%' . $query . '%';
    $stmt->execute([$searchTerm, $limit]);
    
    return array_map(function($row) {
        return [
            'text' => strtoupper($row['currency']),
            'type' => 'currency',
            'count' => (int)$row['count']
        ];
    }, $stmt->fetchAll(PDO::FETCH_ASSOC));
}

/**
 * الحصول على اقتراحات طرق الدفع
 */
function getPaymentMethodSuggestions($connection, $query, $limit = 5) {
    $stmt = $connection->prepare("
        SELECT DISTINCT payment_method, COUNT(*) as count
        FROM offers 
        WHERE status = 'active' AND payment_method LIKE ?
        GROUP BY payment_method
        ORDER BY count DESC
        LIMIT ?
    ");
    
    $searchTerm = '%' . $query . '%';
    $stmt->execute([$searchTerm, $limit]);
    
    return array_map(function($row) {
        return [
            'text' => $row['payment_method'],
            'type' => 'payment_method',
            'count' => (int)$row['count']
        ];
    }, $stmt->fetchAll(PDO::FETCH_ASSOC));
}

/**
 * الحصول على الكلمات المفتاحية الشائعة
 */
function getPopularKeywords($connection, $limit = 10) {
    // استخراج الكلمات الشائعة من العناوين والأوصاف
    $stmt = $connection->prepare("
        SELECT title, description
        FROM offers 
        WHERE status = 'active' AND (title IS NOT NULL OR description IS NOT NULL)
        ORDER BY created_at DESC
        LIMIT 100
    ");
    
    $stmt->execute();
    $offers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $keywords = [];
    $stopWords = ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك', 'and', 'or', 'the', 'a', 'an', 'is', 'are', 'was', 'were'];
    
    foreach ($offers as $offer) {
        $text = $offer['title'] . ' ' . $offer['description'];
        $words = preg_split('/\s+/', strtolower($text));
        
        foreach ($words as $word) {
            $word = trim($word, '.,!?;:"()[]{}');
            if (strlen($word) >= 3 && !in_array($word, $stopWords)) {
                $keywords[$word] = ($keywords[$word] ?? 0) + 1;
            }
        }
    }
    
    arsort($keywords);
    $topKeywords = array_slice($keywords, 0, $limit, true);
    
    return array_map(function($word, $count) {
        return [
            'text' => $word,
            'type' => 'keyword',
            'count' => $count
        ];
    }, array_keys($topKeywords), array_values($topKeywords));
}

/**
 * الحصول على الاقتراحات المخصصة للمستخدم
 */
function getPersonalizedSuggestions($connection, $userId, $limit = 5) {
    // اقتراحات بناءً على نشاط المستخدم السابق
    $stmt = $connection->prepare("
        SELECT DISTINCT o.currency, o.payment_method, COUNT(*) as frequency
        FROM trades t
        JOIN offers o ON (t.offer_id = o.id)
        WHERE (t.buyer_id = ? OR t.seller_id = ?)
        GROUP BY o.currency, o.payment_method
        ORDER BY frequency DESC
        LIMIT ?
    ");
    
    $stmt->execute([$userId, $userId, $limit]);
    $userPreferences = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return array_map(function($pref) {
        return [
            'text' => $pref['currency'] . ' - ' . $pref['payment_method'],
            'type' => 'personalized',
            'frequency' => (int)$pref['frequency']
        ];
    }, $userPreferences);
}

try {
    // التحقق من المصادقة
    $auth = requireAuth();
    $userId = $auth->getCurrentUserId();
    
    if (!$userId) {
        sendErrorResponse('المصادقة مطلوبة', 401);
    }
    
    // الحصول على معاملات البحث
    $query = trim($_GET['q'] ?? '');
    $type = $_GET['type'] ?? 'all'; // all, title, username, currency, payment_method
    $limit = min(20, max(5, (int)($_GET['limit'] ?? 10)));
    
    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    $suggestions = [];
    
    if (empty($query)) {
        // إذا لم يكن هناك استعلام، أرجع الاقتراحات الشائعة
        if ($type === 'all' || $type === 'keyword') {
            $suggestions = array_merge($suggestions, getPopularKeywords($connection, $limit));
        }
        
        if ($type === 'all' || $type === 'personalized') {
            $suggestions = array_merge($suggestions, getPersonalizedSuggestions($connection, $userId, 5));
        }
    } else {
        // البحث عن اقتراحات بناءً على الاستعلام
        if ($type === 'all' || $type === 'title') {
            $suggestions = array_merge($suggestions, getTitleSuggestions($connection, $query, $limit));
        }
        
        if ($type === 'all' || $type === 'username') {
            $suggestions = array_merge($suggestions, getUsernameSuggestions($connection, $query, 5));
        }
        
        if ($type === 'all' || $type === 'currency') {
            $suggestions = array_merge($suggestions, getCurrencySuggestions($connection, $query, 5));
        }
        
        if ($type === 'all' || $type === 'payment_method') {
            $suggestions = array_merge($suggestions, getPaymentMethodSuggestions($connection, $query, 5));
        }
    }
    
    // ترتيب الاقتراحات حسب الصلة
    usort($suggestions, function($a, $b) {
        $scoreA = $a['relevance'] ?? $a['count'] ?? $a['frequency'] ?? 0;
        $scoreB = $b['relevance'] ?? $b['count'] ?? $b['frequency'] ?? 0;
        return $scoreB - $scoreA;
    });
    
    // تحديد عدد النتائج
    $suggestions = array_slice($suggestions, 0, $limit);
    
    // إضافة معلومات إضافية
    $suggestions = array_map(function($suggestion, $index) {
        return array_merge($suggestion, [
            'id' => $index + 1,
            'highlighted' => $index < 3 // تمييز أول 3 اقتراحات
        ]);
    }, $suggestions, array_keys($suggestions));
    
    sendSuccessResponse([
        'suggestions' => $suggestions,
        'query' => $query,
        'type' => $type,
        'totalSuggestions' => count($suggestions),
        'hasMore' => false // يمكن تطوير هذا لاحقاً
    ], 'تم جلب الاقتراحات بنجاح');
    
} catch (Exception $e) {
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    error_log('Database error in suggestions.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}
?>
