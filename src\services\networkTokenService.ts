/**
 * خدمة جلب العملات والشبكات من البلوك تشين مباشرة
 * Network Token Service - Fetch tokens and networks from blockchain directly
 */

import { ethers } from 'ethers';

// أنواع البيانات
export interface NetworkToken {
  id: string;
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  networkId: string;
  chainId: number;
  isStablecoin: boolean;
  isActive: boolean;
  totalSupply?: string;
  marketCap?: number;
  price?: number;
  volume24h?: number;
  holders?: number;
  transfers24h?: number;
  contractVerified?: boolean;
  lastUpdated: string;
}

export interface NetworkInfo {
  id: string;
  name: string;
  chainId: number;
  symbol: string;
  rpcUrl: string;
  explorerUrl: string;
  isTestnet: boolean;
  isEnabled: boolean;
  blockTime: number;
  gasPrice: number;
  latency?: number;
  uptime?: number;
  blockHeight?: number;
  nodeCount?: number;
  totalTransactions?: number;
  dailyTransactions?: number;
  lastSync: string;
  supportedTokensCount: number;
}

// تكوين الشبكات الأساسية
const NETWORK_CONFIGS = {
  97: { // BSC Testnet
    name: 'BSC Testnet',
    symbol: 'tBNB',
    rpcUrl: 'https://data-seed-prebsc-1-s1.binance.org:8545/',
    explorerUrl: 'https://testnet.bscscan.com/',
    explorerApiUrl: 'https://api-testnet.bscscan.com/api',
    isTestnet: true,
    blockTime: 3,
    // العملات الشائعة على BSC Testnet
    commonTokens: [
      '******************************************', // USDT
      '******************************************', // USDC  
      '******************************************', // BUSD
      '******************************************', // DAI
      '******************************************', // ETH
      '******************************************', // BTCB
      '******************************************', // CAKE
    ]
  },
  56: { // BSC Mainnet
    name: 'BSC Mainnet',
    symbol: 'BNB',
    rpcUrl: 'https://bsc-dataseed1.binance.org/',
    explorerUrl: 'https://bscscan.com/',
    explorerApiUrl: 'https://api.bscscan.com/api',
    isTestnet: false,
    blockTime: 3,
    commonTokens: [
      '******************************************', // USDT
      '******************************************', // USDC
      '******************************************', // BUSD
      '******************************************', // DAI
      '******************************************', // ETH
      '******************************************', // BTCB
      '******************************************', // CAKE
    ]
  }
};

// ABI للعقود ERC20
const ERC20_ABI = [
  'function name() view returns (string)',
  'function symbol() view returns (string)',
  'function decimals() view returns (uint8)',
  'function totalSupply() view returns (uint256)',
  'function balanceOf(address) view returns (uint256)'
];

export class NetworkTokenService {
  private providers: Map<number, ethers.JsonRpcProvider> = new Map();

  constructor() {
    this.initializeProviders();
  }

  /**
   * تهيئة موفري RPC للشبكات
   */
  private initializeProviders() {
    Object.entries(NETWORK_CONFIGS).forEach(([chainId, config]) => {
      try {
        const provider = new ethers.JsonRpcProvider(config.rpcUrl);
        this.providers.set(parseInt(chainId), provider);
        console.log(`✅ تم تهيئة موفر RPC للشبكة: ${config.name}`);
      } catch (error) {
        console.error(`❌ فشل في تهيئة موفر RPC للشبكة ${config.name}:`, error);
      }
    });
  }

  /**
   * جلب معلومات الشبكات من البلوك تشين
   */
  async getNetworksFromBlockchain(): Promise<NetworkInfo[]> {
    const networks: NetworkInfo[] = [];

    for (const [chainId, config] of Object.entries(NETWORK_CONFIGS)) {
      try {
        const provider = this.providers.get(parseInt(chainId));
        if (!provider) continue;

        // جلب معلومات الشبكة من البلوك تشين
        const [blockNumber, feeData] = await Promise.all([
          provider.getBlockNumber().catch(() => 0),
          provider.getFeeData().catch(() => null)
        ]);

        const gasPrice = feeData?.gasPrice 
          ? Number(ethers.formatUnits(feeData.gasPrice, 'gwei'))
          : 5;

        const network: NetworkInfo = {
          id: chainId,
          name: config.name,
          chainId: parseInt(chainId),
          symbol: config.symbol,
          rpcUrl: config.rpcUrl,
          explorerUrl: config.explorerUrl,
          isTestnet: config.isTestnet,
          isEnabled: true,
          blockTime: config.blockTime,
          gasPrice: gasPrice,
          latency: await this.measureLatency(provider),
          uptime: 99.5, // سيتم حسابها من إحصائيات الشبكة
          blockHeight: blockNumber,
          nodeCount: 1,
          totalTransactions: 0,
          dailyTransactions: 0,
          lastSync: new Date().toISOString(),
          supportedTokensCount: config.commonTokens.length
        };

        networks.push(network);
        console.log(`✅ تم جلب معلومات الشبكة: ${config.name}`);
      } catch (error) {
        console.error(`❌ خطأ في جلب معلومات الشبكة ${config.name}:`, error);
      }
    }

    return networks;
  }

  /**
   * جلب العملات المدعومة من الشبكة
   */
  async getTokensFromBlockchain(chainId?: number): Promise<NetworkToken[]> {
    const tokens: NetworkToken[] = [];
    const networksToCheck = chainId ? [chainId] : Object.keys(NETWORK_CONFIGS).map(Number);

    for (const networkId of networksToCheck) {
      const config = NETWORK_CONFIGS[networkId as keyof typeof NETWORK_CONFIGS];
      if (!config) continue;

      const provider = this.providers.get(networkId);
      if (!provider) continue;

      console.log(`🔍 جلب العملات من الشبكة: ${config.name}`);

      for (const tokenAddress of config.commonTokens) {
        try {
          const tokenInfo = await this.getTokenInfoFromContract(networkId, tokenAddress);
          if (tokenInfo) {
            tokens.push(tokenInfo);
            console.log(`✅ تم جلب العملة: ${tokenInfo.symbol}`);
          }
        } catch (error) {
          console.error(`❌ خطأ في جلب العملة ${tokenAddress}:`, error);
        }
      }
    }

    return tokens;
  }

  /**
   * جلب معلومات عملة من العقد مباشرة
   */
  async getTokenInfoFromContract(chainId: number, address: string): Promise<NetworkToken | null> {
    try {
      const provider = this.providers.get(chainId);
      if (!provider) return null;

      // التحقق من أن العنوان هو عقد صالح
      const code = await provider.getCode(address);
      if (code === '0x') {
        console.warn(`⚠️ العنوان ${address} ليس عقد صالح`);
        return null;
      }

      const tokenContract = new ethers.Contract(address, ERC20_ABI, provider);

      // جلب معلومات العملة
      const [name, symbol, decimals, totalSupply] = await Promise.all([
        tokenContract.name().catch(() => 'Unknown Token'),
        tokenContract.symbol().catch(() => 'UNK'),
        tokenContract.decimals().catch(() => 18),
        tokenContract.totalSupply().catch(() => '0')
      ]);

      // تحديد إذا كانت العملة مستقرة
      const stablecoins = ['USDT', 'USDC', 'BUSD', 'DAI', 'TUSD', 'FRAX'];
      const isStablecoin = stablecoins.includes(symbol.toUpperCase());

      const token: NetworkToken = {
        id: `${chainId}-${address}`,
        address: address,
        symbol: symbol,
        name: name,
        decimals: Number(decimals),
        networkId: chainId.toString(),
        chainId: chainId,
        isStablecoin: isStablecoin,
        isActive: true,
        totalSupply: ethers.formatUnits(totalSupply, decimals),
        marketCap: 0,
        price: isStablecoin ? 1.0 : 0,
        volume24h: 0,
        holders: 0,
        transfers24h: 0,
        contractVerified: true,
        lastUpdated: new Date().toISOString()
      };

      return token;
    } catch (error) {
      console.error(`❌ خطأ في جلب معلومات العملة ${address}:`, error);
      return null;
    }
  }

  /**
   * قياس زمن الاستجابة للشبكة
   */
  private async measureLatency(provider: ethers.JsonRpcProvider): Promise<number> {
    try {
      const start = Date.now();
      await provider.getBlockNumber();
      const end = Date.now();
      return end - start;
    } catch (error) {
      return 1000; // قيمة افتراضية في حالة الخطأ
    }
  }

  /**
   * إضافة عملة جديدة للفحص
   */
  async addTokenToNetwork(chainId: number, tokenAddress: string): Promise<NetworkToken | null> {
    try {
      const tokenInfo = await this.getTokenInfoFromContract(chainId, tokenAddress);
      if (tokenInfo) {
        // إضافة العملة لقائمة العملات الشائعة
        const config = NETWORK_CONFIGS[chainId as keyof typeof NETWORK_CONFIGS];
        if (config && !config.commonTokens.includes(tokenAddress)) {
          config.commonTokens.push(tokenAddress);
        }
      }
      return tokenInfo;
    } catch (error) {
      console.error(`❌ خطأ في إضافة العملة ${tokenAddress}:`, error);
      return null;
    }
  }

  /**
   * التحقق من صحة عنوان العملة
   */
  async validateTokenAddress(chainId: number, address: string): Promise<boolean> {
    try {
      const provider = this.providers.get(chainId);
      if (!provider) return false;

      const code = await provider.getCode(address);
      return code !== '0x';
    } catch (error) {
      return false;
    }
  }

  /**
   * جلب أسعار العملات من API خارجي (اختياري)
   */
  async fetchTokenPrices(tokens: NetworkToken[]): Promise<NetworkToken[]> {
    // يمكن تنفيذ هذا لاحقاً مع APIs مثل CoinGecko أو CoinMarketCap
    return tokens.map(token => ({
      ...token,
      price: token.isStablecoin ? 1.0 : Math.random() * 100, // قيم تجريبية
      marketCap: Math.random() * 1000000,
      volume24h: Math.random() * 50000
    }));
  }
}

// إنشاء instance مشترك
export const networkTokenService = new NetworkTokenService();
