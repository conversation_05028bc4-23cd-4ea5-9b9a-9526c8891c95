import { NextRequest, NextResponse } from 'next/server';

/**
 * Token Management API Proxy
 * بروكسي API لإدارة العملات
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || '';

    // بناء URL للـ PHP API
    const phpApiUrl = new URL('http://localhost/ikaros-p2p/api/enhanced-contracts/token-management.php');
    
    // نسخ جميع المعاملات
    searchParams.forEach((value, key) => {
      phpApiUrl.searchParams.set(key, value);
    });

    console.log('🔄 Proxying token management GET request to:', phpApiUrl.toString());

    // إرسال الطلب إلى PHP API
    const response = await fetch(phpApiUrl.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        // نسخ headers المهمة
        'Authorization': request.headers.get('Authorization') || '',
        'Cookie': request.headers.get('Cookie') || '',
      },
    });

    const data = await response.json();
    
    console.log('📥 Token management GET response:', {
      status: response.status,
      action,
      success: data.success
    });

    return NextResponse.json(data, { status: response.status });

  } catch (error) {
    console.error('❌ Token management GET proxy error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || '';

    // بناء URL للـ PHP API
    const phpApiUrl = new URL('http://localhost/ikaros-p2p/api/enhanced-contracts/token-management.php');
    if (action) phpApiUrl.searchParams.set('action', action);

    console.log('🔄 Proxying token management POST request to:', phpApiUrl.toString());
    console.log('📤 Request body:', body);

    // إرسال الطلب إلى PHP API
    const response = await fetch(phpApiUrl.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        // نسخ headers المهمة
        'Authorization': request.headers.get('Authorization') || '',
        'Cookie': request.headers.get('Cookie') || '',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    
    console.log('📥 Token management POST response:', {
      status: response.status,
      action,
      success: data.success
    });

    return NextResponse.json(data, { status: response.status });

  } catch (error) {
    console.error('❌ Token management POST proxy error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || '';
    const id = searchParams.get('id') || '';

    // بناء URL للـ PHP API
    const phpApiUrl = new URL('http://localhost/ikaros-p2p/api/enhanced-contracts/token-management.php');
    if (action) phpApiUrl.searchParams.set('action', action);
    if (id) phpApiUrl.searchParams.set('id', id);

    console.log('🔄 Proxying token management PUT request to:', phpApiUrl.toString());

    // إرسال الطلب إلى PHP API
    const response = await fetch(phpApiUrl.toString(), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': request.headers.get('Authorization') || '',
        'Cookie': request.headers.get('Cookie') || '',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    
    console.log('📥 Token management PUT response:', {
      status: response.status,
      action,
      success: data.success
    });

    return NextResponse.json(data, { status: response.status });

  } catch (error) {
    console.error('❌ Token management PUT proxy error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || '';
    const id = searchParams.get('id') || '';

    // بناء URL للـ PHP API
    const phpApiUrl = new URL('http://localhost/ikaros-p2p/api/enhanced-contracts/token-management.php');
    
    // نسخ جميع المعاملات
    searchParams.forEach((value, key) => {
      phpApiUrl.searchParams.set(key, value);
    });

    console.log('🔄 Proxying token management DELETE request to:', phpApiUrl.toString());

    // إرسال الطلب إلى PHP API
    const response = await fetch(phpApiUrl.toString(), {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': request.headers.get('Authorization') || '',
        'Cookie': request.headers.get('Cookie') || '',
      },
    });

    const data = await response.json();
    
    console.log('📥 Token management DELETE response:', {
      status: response.status,
      action,
      success: data.success
    });

    return NextResponse.json(data, { status: response.status });

  } catch (error) {
    console.error('❌ Token management DELETE proxy error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
