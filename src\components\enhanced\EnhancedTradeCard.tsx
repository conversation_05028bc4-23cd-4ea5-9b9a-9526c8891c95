'use client';

import React, { useState } from 'react';
import { 
  Clock, 
  User, 
  Star, 
  Shield, 
  AlertCircle, 
  CheckCircle, 
  Loader2,
  ExternalLink,
  MessageCircle,
  DollarSign,
  Network,
  Coins
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { enhancedContractApiService } from '@/services/enhancedContractApiService';

interface EnhancedTradeCardProps {
  trade: {
    id: number;
    blockchain_trade_id?: number;
    seller_id: number;
    buyer_id: number;
    network_id: number;
    token_id: number;
    amount: number;
    price: number;
    currency: string;
    total_value: number;
    platform_fee: number;
    net_amount: number;
    status: string;
    contract_status: string;
    payment_method: string;
    selected_payment_methods: string[];
    seller_confirmed: boolean;
    buyer_confirmed: boolean;
    time_remaining?: number;
    can_cancel: boolean;
    can_dispute: boolean;
    is_disputed: boolean;
    is_completed: boolean;
    
    // معلومات الشبكة والعملة
    network_name: string;
    network_symbol: string;
    network_is_testnet: boolean;
    token_address: string;
    token_symbol: string;
    token_name: string;
    token_icon?: string;
    is_stablecoin: boolean;
    
    // معلومات المستخدمين
    seller_username: string;
    seller_name: string;
    seller_rating: number;
    seller_verified: boolean;
    seller_reputation_score: number;
    seller_reputation_level: string;
    
    buyer_username: string;
    buyer_name: string;
    buyer_rating: number;
    buyer_verified: boolean;
    buyer_reputation_score: number;
    buyer_reputation_level: string;
    
    // معلومات العقد الذكي
    create_transaction_hash?: string;
    join_transaction_hash?: string;
    payment_sent_transaction_hash?: string;
    payment_confirmed_transaction_hash?: string;
    complete_transaction_hash?: string;
    
    created_at: string;
    updated_at: string;
  };
  currentUserId: number;
  onAction?: (action: string, tradeId: number) => void;
  className?: string;
}

const EnhancedTradeCard: React.FC<EnhancedTradeCardProps> = ({
  trade,
  currentUserId,
  onAction,
  className = ''
}) => {
  const { t, isRTL } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const isSeller = currentUserId === trade.seller_id;
  const isBuyer = currentUserId === trade.buyer_id;
  const otherUser = isSeller ? {
    id: trade.buyer_id,
    username: trade.buyer_username,
    name: trade.buyer_name,
    rating: trade.buyer_rating,
    verified: trade.buyer_verified,
    reputation_score: trade.buyer_reputation_score,
    reputation_level: trade.buyer_reputation_level
  } : {
    id: trade.seller_id,
    username: trade.seller_username,
    name: trade.seller_name,
    rating: trade.seller_rating,
    verified: trade.seller_verified,
    reputation_score: trade.seller_reputation_score,
    reputation_level: trade.seller_reputation_level
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'created':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'joined':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'payment_sent':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'payment_confirmed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      case 'disputed':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getReputationColor = (level: string) => {
    switch (level) {
      case 'master':
        return 'text-purple-600 dark:text-purple-400';
      case 'expert':
        return 'text-blue-600 dark:text-blue-400';
      case 'advanced':
        return 'text-green-600 dark:text-green-400';
      case 'intermediate':
        return 'text-yellow-600 dark:text-yellow-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const formatTimeRemaining = (seconds: number) => {
    if (seconds <= 0) return t('expired');
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}${t('h')} ${minutes}${t('m')}`;
    }
    return `${minutes}${t('m')}`;
  };

  const handleAction = async (action: string) => {
    if (!onAction) return;

    try {
      setActionLoading(action);
      
      switch (action) {
        case 'confirm_payment_sent':
          await enhancedContractApiService.confirmPaymentSent(trade.id);
          break;
        case 'confirm_payment_received':
          await enhancedContractApiService.confirmPaymentReceived(trade.id);
          break;
        case 'request_dispute':
          await enhancedContractApiService.requestDispute(trade.id, 'User requested dispute');
          break;
      }
      
      onAction(action, trade.id);
    } catch (error) {
      console.error(`خطأ في تنفيذ الإجراء ${action}:`, error);
    } finally {
      setActionLoading(null);
    }
  };

  const openBlockchainExplorer = (txHash: string) => {
    const explorerUrl = trade.network_is_testnet 
      ? 'https://testnet.bscscan.com/tx/' 
      : 'https://bscscan.com/tx/';
    window.open(`${explorerUrl}${txHash}`, '_blank');
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden ${className}`}>
      {/* رأس البطاقة */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                #{trade.id}
              </span>
              {trade.blockchain_trade_id && (
                <span className="text-xs text-blue-600 dark:text-blue-400">
                  BC#{trade.blockchain_trade_id}
                </span>
              )}
            </div>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(trade.status)}`}>
              {t(`tradeStatus.${trade.status}`)}
            </span>
          </div>
          
          {trade.time_remaining && trade.time_remaining > 0 && (
            <div className="flex items-center gap-1 text-orange-600 dark:text-orange-400">
              <Clock className="w-4 h-4" />
              <span className="text-sm font-medium">
                {formatTimeRemaining(trade.time_remaining)}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* محتوى البطاقة */}
      <div className="p-4 space-y-4">
        {/* معلومات الشبكة والعملة */}
        <div className="flex items-center gap-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center gap-2">
            <Network className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {trade.network_name}
            </span>
            {trade.network_is_testnet && (
              <span className="px-1.5 py-0.5 bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400 rounded text-xs">
                {t('testnet')}
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Coins className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            {trade.token_icon ? (
              <img src={trade.token_icon} alt={trade.token_symbol} className="w-5 h-5 rounded-full" />
            ) : (
              <div className="w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center text-xs font-bold text-white">
                {trade.token_symbol.charAt(0)}
              </div>
            )}
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {trade.token_symbol}
            </span>
            {trade.is_stablecoin && (
              <span className="px-1.5 py-0.5 bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 rounded text-xs">
                {t('stablecoin')}
              </span>
            )}
          </div>
        </div>

        {/* تفاصيل الصفقة */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="text-sm text-gray-500 dark:text-gray-400">{t('amount')}</span>
            <div className="font-bold text-lg text-gray-900 dark:text-white">
              {trade.amount.toFixed(6)} {trade.token_symbol}
            </div>
          </div>
          <div>
            <span className="text-sm text-gray-500 dark:text-gray-400">{t('price')}</span>
            <div className="font-bold text-lg text-gray-900 dark:text-white">
              {trade.price.toFixed(2)} {trade.currency}
            </div>
          </div>
          <div>
            <span className="text-sm text-gray-500 dark:text-gray-400">{t('totalValue')}</span>
            <div className="font-bold text-lg text-green-600 dark:text-green-400">
              {trade.total_value.toFixed(2)} {trade.currency}
            </div>
          </div>
          <div>
            <span className="text-sm text-gray-500 dark:text-gray-400">{t('platformFee')}</span>
            <div className="font-medium text-gray-700 dark:text-gray-300">
              {trade.platform_fee.toFixed(6)} {trade.token_symbol}
            </div>
          </div>
        </div>

        {/* معلومات الطرف الآخر */}
        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                {otherUser.username.charAt(0).toUpperCase()}
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <span className="font-medium text-gray-900 dark:text-white">
                    {otherUser.username}
                  </span>
                  {otherUser.verified && (
                    <Shield className="w-4 h-4 text-green-500" />
                  )}
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <div className="flex items-center gap-1">
                    <Star className="w-3 h-3 text-yellow-500" />
                    <span className="text-gray-600 dark:text-gray-400">
                      {otherUser.rating.toFixed(1)}
                    </span>
                  </div>
                  <span className={`text-xs ${getReputationColor(otherUser.reputation_level)}`}>
                    {t(`reputationLevel.${otherUser.reputation_level}`)}
                  </span>
                  <span className="text-gray-500 dark:text-gray-400">
                    ({otherUser.reputation_score})
                  </span>
                </div>
              </div>
            </div>
            
            <button
              onClick={() => onAction?.('open_chat', trade.id)}
              className="p-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
            >
              <MessageCircle className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* طريقة الدفع */}
        <div className="flex items-center gap-2">
          <DollarSign className="w-4 h-4 text-gray-500 dark:text-gray-400" />
          <span className="text-sm text-gray-500 dark:text-gray-400">{t('paymentMethod')}:</span>
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {t(`paymentMethod.${trade.payment_method}`)}
          </span>
        </div>

        {/* معاملات البلوك تشين */}
        {(trade.create_transaction_hash || trade.join_transaction_hash || trade.payment_sent_transaction_hash || trade.payment_confirmed_transaction_hash) && (
          <div className="space-y-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {t('blockchainTransactions')}:
            </span>
            <div className="space-y-1">
              {trade.create_transaction_hash && (
                <button
                  onClick={() => openBlockchainExplorer(trade.create_transaction_hash!)}
                  className="flex items-center gap-2 text-xs text-blue-600 dark:text-blue-400 hover:underline"
                >
                  <span>{t('created')}</span>
                  <ExternalLink className="w-3 h-3" />
                </button>
              )}
              {trade.join_transaction_hash && (
                <button
                  onClick={() => openBlockchainExplorer(trade.join_transaction_hash!)}
                  className="flex items-center gap-2 text-xs text-blue-600 dark:text-blue-400 hover:underline"
                >
                  <span>{t('joined')}</span>
                  <ExternalLink className="w-3 h-3" />
                </button>
              )}
              {trade.payment_sent_transaction_hash && (
                <button
                  onClick={() => openBlockchainExplorer(trade.payment_sent_transaction_hash!)}
                  className="flex items-center gap-2 text-xs text-blue-600 dark:text-blue-400 hover:underline"
                >
                  <span>{t('paymentSent')}</span>
                  <ExternalLink className="w-3 h-3" />
                </button>
              )}
              {trade.payment_confirmed_transaction_hash && (
                <button
                  onClick={() => openBlockchainExplorer(trade.payment_confirmed_transaction_hash!)}
                  className="flex items-center gap-2 text-xs text-blue-600 dark:text-blue-400 hover:underline"
                >
                  <span>{t('paymentConfirmed')}</span>
                  <ExternalLink className="w-3 h-3" />
                </button>
              )}
            </div>
          </div>
        )}

        {/* أزرار الإجراءات */}
        <div className="flex gap-2 pt-2">
          {/* إجراءات المشتري */}
          {isBuyer && trade.status === 'joined' && (
            <button
              onClick={() => handleAction('confirm_payment_sent')}
              disabled={actionLoading === 'confirm_payment_sent'}
              className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white rounded-lg transition-colors flex items-center justify-center gap-2"
            >
              {actionLoading === 'confirm_payment_sent' ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <CheckCircle className="w-4 h-4" />
              )}
              {t('confirmPaymentSent')}
            </button>
          )}

          {/* إجراءات البائع */}
          {isSeller && trade.status === 'payment_sent' && (
            <button
              onClick={() => handleAction('confirm_payment_received')}
              disabled={actionLoading === 'confirm_payment_received'}
              className="flex-1 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white rounded-lg transition-colors flex items-center justify-center gap-2"
            >
              {actionLoading === 'confirm_payment_received' ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <CheckCircle className="w-4 h-4" />
              )}
              {t('confirmPaymentReceived')}
            </button>
          )}

          {/* طلب نزاع */}
          {trade.can_dispute && (
            <button
              onClick={() => handleAction('request_dispute')}
              disabled={actionLoading === 'request_dispute'}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded-lg transition-colors flex items-center justify-center gap-2"
            >
              {actionLoading === 'request_dispute' ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <AlertCircle className="w-4 h-4" />
              )}
              {t('requestDispute')}
            </button>
          )}

          {/* إلغاء الصفقة */}
          {trade.can_cancel && (
            <button
              onClick={() => onAction?.('cancel_trade', trade.id)}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              {t('cancel')}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedTradeCard;
