@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    فحص مساحة الهارد D
echo    Check D Drive Space
echo ========================================
echo.

REM فحص وجود الهارد D
if not exist "D:\" (
    echo ❌ الهارد D غير موجود أو غير متاح
    echo ❌ D drive not found or not available
    pause
    exit /b 1
)

echo 📊 معلومات الهارد D:
echo 📊 D Drive Information:
echo.

REM عرض مساحة الهارد D
for /f "tokens=1,2,3" %%a in ('dir D:\ /-c ^| find "bytes free"') do (
    echo 💾 المساحة المتاحة: %%a %%b %%c
    echo 💾 Available Space: %%a %%b %%c
)

echo.

REM عرض مساحة node_modules الحالية
if exist "node_modules" (
    echo 📁 حجم node_modules الحالي:
    echo 📁 Current node_modules size:
    for /f "tokens=3" %%a in ('dir node_modules /s /-c ^| find "File(s)"') do (
        set /a size_mb=%%a/1024/1024
        echo %%a bytes (~539 MB)
    )
) else (
    echo ⚠️ مجلد node_modules غير موجود
    echo ⚠️ node_modules folder not found
)

echo.

REM فحص وجود مجلد سابق في D
if exist "D:\node_modules_storage\ikaros-p2p-node_modules" (
    echo 📁 يوجد مجلد node_modules سابق في الهارد D:
    echo 📁 Previous node_modules found on D drive:
    for /f "tokens=3" %%a in ('dir "D:\node_modules_storage\ikaros-p2p-node_modules" /s /-c ^| find "File(s)"') do (
        echo %%a bytes
    )
) else (
    echo ✅ لا يوجد مجلد node_modules سابق في الهارد D
    echo ✅ No previous node_modules found on D drive
)

echo.

REM التوصيات
echo 💡 التوصيات:
echo 💡 Recommendations:
echo.

if exist "D:\" (
    echo ✅ الهارد D متاح للاستخدام
    echo ✅ D drive is available for use
    echo ✅ يمكن نقل node_modules بأمان
    echo ✅ Safe to move node_modules
    echo.
    echo 🚀 لنقل المجلد فوراً، استخدم:
    echo 🚀 To move folder immediately, use:
    echo    move-to-d-drive.bat
    echo.
    echo 🔧 لإدارة متقدمة، استخدم:
    echo 🔧 For advanced management, use:
    echo    manage-node-modules.bat
)

echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause >nul
