import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { describe, it, expect, jest, beforeEach } from '@jest/globals';

// Mock the translation hook
jest.mock('@/hooks/useAdminTranslation', () => ({
  useAdminTranslation: () => ({
    t: (key: string) => key,
    language: 'en',
    isRTL: false,
    getDirectionClasses: () => ({ textAlign: 'left', direction: 'ltr' }),
    formatNumber: (num: number) => num.toLocaleString(),
    formatCurrency: (amount: number) => `$${amount.toFixed(2)}`,
    formatDate: (date: string) => new Date(date).toLocaleDateString(),
    formatRelativeTime: (date: string) => 'just now'
  })
}));

import {
  AdminDataTable,
  AdminChart,
  AdminForm,
  AdminModal,
  AdminButton,
  AdminButtonGroup,
  AdminIconButton,
  AdminFAB,
  AdminStatsCard,
  AdminStatsGrid,
  AdminCompactStats,
  ConfirmationModal,
  AlertModal,
  LoadingModal
} from '@/components/admin/ui';

describe('AdminDataTable', () => {
  const mockData = [
    { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active', role: 'admin' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'inactive', role: 'user' },
    { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'active', role: 'moderator' }
  ];

  const mockColumns = [
    { key: 'name', label: 'Name', sortable: true, filterable: true },
    { key: 'email', label: 'Email', sortable: true, filterable: true },
    { key: 'status', label: 'Status', filterable: true },
    { key: 'role', label: 'Role', sortable: true }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders table with data', () => {
    render(<AdminDataTable data={mockData} columns={mockColumns} />);
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('moderator')).toBeInTheDocument();
  });

  it('handles sorting by column', () => {
    render(<AdminDataTable data={mockData} columns={mockColumns} />);
    const nameHeader = screen.getByText('Name');
    fireEvent.click(nameHeader);
    
    // Check if sorting icon appears
    expect(nameHeader.closest('th')).toContainHTML('ChevronUp');
  });

  it('handles search functionality', () => {
    render(<AdminDataTable data={mockData} columns={mockColumns} searchable />);
    const searchInput = screen.getByPlaceholderText('common.actions.search');
    
    fireEvent.change(searchInput, { target: { value: 'John' } });
    expect(searchInput).toHaveValue('John');
  });

  it('handles row selection', () => {
    const mockOnSelectionChange = jest.fn();
    render(
      <AdminDataTable 
        data={mockData} 
        columns={mockColumns} 
        selectable 
        onSelectionChange={mockOnSelectionChange}
      />
    );
    
    const firstCheckbox = screen.getAllByRole('checkbox')[1]; // Skip header checkbox
    fireEvent.click(firstCheckbox);
    
    expect(mockOnSelectionChange).toHaveBeenCalled();
  });

  it('handles pagination', () => {
    render(<AdminDataTable data={mockData} columns={mockColumns} pageSize={2} />);
    
    // Should show pagination controls
    expect(screen.getByText('1 of 2')).toBeInTheDocument();
    
    const nextButton = screen.getByRole('button', { name: /next/i });
    fireEvent.click(nextButton);
  });

  it('exports data to CSV', () => {
    // Mock URL.createObjectURL and document.createElement
    const mockCreateElement = jest.spyOn(document, 'createElement');
    const mockClick = jest.fn();
    mockCreateElement.mockReturnValue({
      href: '',
      download: '',
      click: mockClick
    } as any);

    render(<AdminDataTable data={mockData} columns={mockColumns} exportable />);
    
    const exportButton = screen.getByText('common.actions.export');
    fireEvent.click(exportButton);
    
    expect(mockClick).toHaveBeenCalled();
    mockCreateElement.mockRestore();
  });

  it('handles empty data state', () => {
    render(<AdminDataTable data={[]} columns={mockColumns} />);
    expect(screen.getByText('common.noData')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    render(<AdminDataTable data={mockData} columns={mockColumns} loading />);
    expect(screen.getByText('common.loading')).toBeInTheDocument();
  });
});

describe('AdminChart', () => {
  const mockChartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr'],
    datasets: [{
      label: 'Sales',
      data: [100, 200, 150, 300],
      backgroundColor: '#3B82F6'
    }]
  };

  it('renders chart with title', () => {
    render(<AdminChart type="bar" data={mockChartData} title="Sales Chart" />);
    expect(screen.getByText('Sales Chart')).toBeInTheDocument();
  });

  it('displays chart data information', () => {
    render(<AdminChart type="line" data={mockChartData} />);
    expect(screen.getByText('1 dataset(s) with 4 data points')).toBeInTheDocument();
  });

  it('handles loading state', () => {
    render(<AdminChart type="pie" data={mockChartData} loading />);
    expect(screen.getByText('common.loading')).toBeInTheDocument();
  });

  it('handles error state', () => {
    render(<AdminChart type="doughnut" data={mockChartData} error="Failed to load chart" />);
    expect(screen.getByText('Failed to load chart')).toBeInTheDocument();
  });

  it('shows legend when enabled', () => {
    render(<AdminChart type="area" data={mockChartData} showLegend />);
    expect(screen.getByText('Sales')).toBeInTheDocument();
  });

  it('handles refresh action', () => {
    const mockOnRefresh = jest.fn();
    render(<AdminChart type="bar" data={mockChartData} refreshable onRefresh={mockOnRefresh} />);
    
    const refreshButton = screen.getByTitle('common.actions.refresh');
    fireEvent.click(refreshButton);
    
    expect(mockOnRefresh).toHaveBeenCalled();
  });
});

describe('AdminForm', () => {
  const mockFields = [
    { name: 'name', label: 'Full Name', type: 'text' as const, required: true },
    { name: 'email', label: 'Email Address', type: 'email' as const, required: true },
    { name: 'age', label: 'Age', type: 'number' as const, validation: { min: 18, max: 100 } },
    { name: 'bio', label: 'Biography', type: 'textarea' as const, rows: 4 },
    { name: 'country', label: 'Country', type: 'select' as const, options: [
      { value: 'us', label: 'United States' },
      { value: 'uk', label: 'United Kingdom' }
    ]},
    { name: 'active', label: 'Active User', type: 'checkbox' as const },
    { name: 'password', label: 'Password', type: 'password' as const, required: true }
  ];

  const mockOnSubmit = jest.fn();

  beforeEach(() => {
    mockOnSubmit.mockClear();
  });

  it('renders all field types', () => {
    render(<AdminForm fields={mockFields} onSubmit={mockOnSubmit} />);
    
    expect(screen.getByLabelText('Full Name *')).toBeInTheDocument();
    expect(screen.getByLabelText('Email Address *')).toBeInTheDocument();
    expect(screen.getByLabelText('Age')).toBeInTheDocument();
    expect(screen.getByLabelText('Biography')).toBeInTheDocument();
    expect(screen.getByLabelText('Country')).toBeInTheDocument();
    expect(screen.getByLabelText('Active User')).toBeInTheDocument();
    expect(screen.getByLabelText('Password *')).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    render(<AdminForm fields={mockFields} onSubmit={mockOnSubmit} />);
    
    const submitButton = screen.getByText('common.actions.save');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('validation.required')).toBeInTheDocument();
    });
    
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('submits form with valid data', async () => {
    render(<AdminForm fields={mockFields} onSubmit={mockOnSubmit} />);
    
    fireEvent.change(screen.getByLabelText('Full Name *'), { target: { value: 'John Doe' } });
    fireEvent.change(screen.getByLabelText('Email Address *'), { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByLabelText('Password *'), { target: { value: 'password123' } });
    fireEvent.change(screen.getByLabelText('Age'), { target: { value: '25' } });
    
    const submitButton = screen.getByText('common.actions.save');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'password123',
        age: 25,
        bio: '',
        country: '',
        active: ''
      });
    });
  });

  it('handles password visibility toggle', () => {
    render(<AdminForm fields={mockFields} onSubmit={mockOnSubmit} />);
    
    const passwordInput = screen.getByLabelText('Password *');
    expect(passwordInput).toHaveAttribute('type', 'password');
    
    const toggleButton = passwordInput.parentElement?.querySelector('button');
    if (toggleButton) {
      fireEvent.click(toggleButton);
      expect(passwordInput).toHaveAttribute('type', 'text');
    }
  });

  it('shows loading state', () => {
    render(<AdminForm fields={mockFields} onSubmit={mockOnSubmit} loading />);
    
    const submitButton = screen.getByText('common.actions.save');
    expect(submitButton).toBeDisabled();
  });
});

describe('AdminModal', () => {
  const mockOnClose = jest.fn();

  beforeEach(() => {
    mockOnClose.mockClear();
  });

  it('renders modal when open', () => {
    render(
      <AdminModal isOpen={true} onClose={mockOnClose} title="Test Modal">
        <p>Modal content</p>
      </AdminModal>
    );
    
    expect(screen.getByText('Test Modal')).toBeInTheDocument();
    expect(screen.getByText('Modal content')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(
      <AdminModal isOpen={false} onClose={mockOnClose} title="Test Modal">
        <p>Modal content</p>
      </AdminModal>
    );
    
    expect(screen.queryByText('Test Modal')).not.toBeInTheDocument();
  });

  it('handles close button click', () => {
    render(
      <AdminModal isOpen={true} onClose={mockOnClose} title="Test Modal">
        <p>Modal content</p>
      </AdminModal>
    );
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('handles overlay click when enabled', () => {
    render(
      <AdminModal isOpen={true} onClose={mockOnClose} title="Test Modal" closeOnOverlayClick>
        <p>Modal content</p>
      </AdminModal>
    );
    
    const overlay = screen.getByText('Test Modal').closest('.fixed');
    if (overlay) {
      fireEvent.click(overlay);
      expect(mockOnClose).toHaveBeenCalled();
    }
  });

  it('renders with different types', () => {
    render(
      <AdminModal isOpen={true} onClose={mockOnClose} title="Success Modal" type="success">
        <p>Success message</p>
      </AdminModal>
    );
    
    expect(screen.getByText('Success Modal')).toBeInTheDocument();
  });
});

describe('AdminButton', () => {
  it('renders button with text', () => {
    render(<AdminButton>Click me</AdminButton>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const mockClick = jest.fn();
    render(<AdminButton onClick={mockClick}>Click me</AdminButton>);
    
    fireEvent.click(screen.getByText('Click me'));
    expect(mockClick).toHaveBeenCalled();
  });

  it('shows loading state', () => {
    render(<AdminButton loading>Loading</AdminButton>);
    expect(screen.getByText('Loading')).toBeInTheDocument();
  });

  it('is disabled when specified', () => {
    render(<AdminButton disabled>Disabled</AdminButton>);
    expect(screen.getByText('Disabled')).toBeDisabled();
  });

  it('renders with different variants', () => {
    render(<AdminButton variant="success">Success Button</AdminButton>);
    const button = screen.getByText('Success Button');
    expect(button).toHaveClass('bg-green-600');
  });

  it('renders with different sizes', () => {
    render(<AdminButton size="lg">Large Button</AdminButton>);
    const button = screen.getByText('Large Button');
    expect(button).toHaveClass('px-6', 'py-3');
  });
});

describe('AdminStatsCard', () => {
  it('renders stats card with basic data', () => {
    render(<AdminStatsCard title="Total Users" value={1234} />);
    
    expect(screen.getByText('Total Users')).toBeInTheDocument();
    expect(screen.getByText('1,234')).toBeInTheDocument();
  });

  it('displays trend information', () => {
    render(
      <AdminStatsCard 
        title="Revenue" 
        value="$50,000" 
        trend={{ value: 12, label: 'vs last month' }}
      />
    );
    
    expect(screen.getByText('12%')).toBeInTheDocument();
    expect(screen.getByText('vs last month')).toBeInTheDocument();
  });

  it('handles click events when clickable', () => {
    const mockClick = jest.fn();
    render(
      <AdminStatsCard 
        title="Clickable Card" 
        value={100} 
        clickable 
        onClick={mockClick}
      />
    );
    
    fireEvent.click(screen.getByText('Clickable Card').closest('div')!);
    expect(mockClick).toHaveBeenCalled();
  });

  it('shows loading state', () => {
    render(<AdminStatsCard title="Loading Card" value={100} loading />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });
});

describe('Confirmation Modal', () => {
  const mockOnConfirm = jest.fn();
  const mockOnClose = jest.fn();

  beforeEach(() => {
    mockOnConfirm.mockClear();
    mockOnClose.mockClear();
  });

  it('renders confirmation modal', () => {
    render(
      <ConfirmationModal
        isOpen={true}
        onClose={mockOnClose}
        onConfirm={mockOnConfirm}
        title="Confirm Action"
        message="Are you sure you want to proceed?"
      />
    );
    
    expect(screen.getByText('Confirm Action')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to proceed?')).toBeInTheDocument();
  });

  it('handles confirm action', () => {
    render(
      <ConfirmationModal
        isOpen={true}
        onClose={mockOnClose}
        onConfirm={mockOnConfirm}
        title="Confirm Action"
        message="Are you sure?"
      />
    );
    
    fireEvent.click(screen.getByText('common.actions.confirm'));
    expect(mockOnConfirm).toHaveBeenCalled();
  });

  it('handles cancel action', () => {
    render(
      <ConfirmationModal
        isOpen={true}
        onClose={mockOnClose}
        onConfirm={mockOnConfirm}
        title="Confirm Action"
        message="Are you sure?"
      />
    );
    
    fireEvent.click(screen.getByText('common.actions.cancel'));
    expect(mockOnClose).toHaveBeenCalled();
  });
});

describe('Alert Modal', () => {
  const mockOnClose = jest.fn();

  it('renders alert modal', () => {
    render(
      <AlertModal
        isOpen={true}
        onClose={mockOnClose}
        title="Alert"
        message="This is an alert message"
        type="warning"
      />
    );
    
    expect(screen.getByText('Alert')).toBeInTheDocument();
    expect(screen.getByText('This is an alert message')).toBeInTheDocument();
  });
});

describe('Loading Modal', () => {
  it('renders loading modal', () => {
    render(
      <LoadingModal
        isOpen={true}
        title="Loading..."
        message="Please wait while we process your request."
      />
    );
    
    expect(screen.getByText('Loading...')).toBeInTheDocument();
    expect(screen.getByText('Please wait while we process your request.')).toBeInTheDocument();
  });
});
