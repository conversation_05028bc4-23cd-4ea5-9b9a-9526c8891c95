import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // تحسينات الأداء
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Turbopack للتطوير السريع
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },

  // تحسينات إضافية
  poweredByHeader: false,
  compress: true,

  // تحسين الصور
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
  },

  // إعدادات webpack
  webpack: (config) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
    };
    return config;
  },

  // متغيرات البيئة
  env: {
    CUSTOM_KEY: 'my-value',
  },

  // إعادة التوجيه
  async redirects() {
    return [
      {
        source: '/trades-management',
        destination: '/trades',
        permanent: true,
      },
      {
        source: '/dashboard',
        destination: '/user-dashboard',
        permanent: true,
      },
    ];
  },

  // إعادة الكتابة للAPI
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost/ikaros-p2p/api/:path*',
      },
    ];
  },

  // إعدادات تجريبية
  experimental: {
    optimizePackageImports: ['lucide-react'],
    optimizeCss: true,
    optimizeServerReact: true,
  },

  // إعدادات الأمان
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
