// خدمة التفاعل مع العقود الذكية
import { ethers } from 'ethers';
import { ENHANCED_CONTRACT_ADDRESSES, ENHANCED_GAS_LIMITS, getEnhancedContractAddresses, getContractAddress, getSupportedTokens, getTokenBySymbol } from '@/constants';
import { networkService } from './networkService';

// ABI للعقد الذكي USDT Escrow المحسن
const ESCROW_ABI = [
  // دوال القراءة
  "function trades(uint256) view returns (uint256 id, address seller, address buyer, uint256 amount, uint256 feeAmount, uint8 status, uint256 createdAt, uint256 lastActivity, bool sellerCancelRequested, bool buyerCancelRequested, uint256 cancelRequestTime, uint256 paymentSentTime)",
  "function getTradeDetails(uint256 _tradeId) view returns (uint256 id, address seller, address buyer, uint256 amount, uint256 feeAmount, uint8 status, uint256 createdAt, uint256 lastActivity, bool sellerCancelRequested, bool buyerCancelRequested, uint256 cancelRequestTime, uint256 paymentSentTime)",
  "function getSellerActiveTrades(address _seller) view returns (uint256[] memory)",
  "function getBuyerActiveTrades(address _buyer) view returns (uint256[] memory)",
  "function hasActiveTrade(address _user, uint256 _tradeId) view returns (bool)",
  "function getUserActiveTradeCount(address _user) view returns (uint256)",
  "function isTradeExpired(uint256 _tradeId) view returns (bool)",
  "function canAutoDispute(uint256 _tradeId) view returns (bool)",
  "function getActiveTrades(uint256 _startId, uint256 _limit) view returns (uint256[] memory tradeIds, address[] memory sellers, address[] memory buyers, uint256[] memory amounts, uint8[] memory statuses)",
  "function getContractStats() view returns (uint256 totalTrades, uint256 activeTrades, uint256 completedTrades, uint256 cancelledTrades, uint256 disputedTrades, uint256 totalVolumeUSDT)",
  "function getTradesEligibleForAutoDispute(uint256 _limit) view returns (uint256[] memory)",
  "function getContractUSDTBalance() view returns (uint256)",
  "function feeRate() view returns (uint256)",
  "function feeWallet() view returns (address)",
  "function autoTimeoutDuration() view returns (uint256)",
  "function cancelWaitDuration() view returns (uint256)",
  "function autoDisputeDuration() view returns (uint256)",
  "function nextTradeId() view returns (uint256)",

  // دوال التحقق من حالة العقد
  "function paused() view returns (bool)",
  "function owner() view returns (address)",

  // دوال الكتابة
  "function createTrade(uint256 _amount)",
  "function joinTrade(uint256 _tradeId)",
  "function confirmPaymentSent(uint256 _tradeId)",
  "function confirmPaymentReceived(uint256 _tradeId)",
  "function confirmPayment(uint256 _tradeId)",
  "function completeTrade(uint256 _tradeId)",
  "function cancelTrade(uint256 _tradeId)",
  "function requestCancel(uint256 _tradeId)",
  "function executeCancelAfterTimeout(uint256 _tradeId)",
  "function autoCancel(uint256 _tradeId)",
  "function autoDispute(uint256 _tradeId)",
  "function requestDispute(uint256 _tradeId)",
  "function initiateDispute(uint256 _tradeId)",
  "function resolveDispute(uint256 _tradeId, bool _favorBuyer)",

  // دوال الإدارة
  "function setFeeWallet(address _newFeeWallet)",
  "function setFeeRate(uint256 _newFeeRate)",
  "function setAutoTimeoutDuration(uint256 _newDuration)",
  "function setCancelWaitDuration(uint256 _newDuration)",
  "function setAutoDisputeDuration(uint256 _newDuration)",
  "function pauseContract()",
  "function unpauseContract()",
  "function transferOwnership(address newOwner)",
  "function renounceOwnership()",
  "function emergencyWithdraw(address _token, uint256 _amount, address _to)",

  // الأحداث
  "event TradeCreated(uint256 indexed tradeId, address indexed seller, uint256 amount)",
  "event BuyerJoined(uint256 indexed tradeId, address indexed buyer)",
  "event PaymentSent(uint256 indexed tradeId, address indexed buyer)",
  "event PaymentConfirmed(uint256 indexed tradeId, address indexed seller)",
  "event TradeCompleted(uint256 indexed tradeId, uint256 buyerAmount, uint256 feeAmount)",
  "event CancelRequested(uint256 indexed tradeId, address indexed requester)",
  "event TradeCancelled(uint256 indexed tradeId, string reason)",
  "event TradeDisputed(uint256 indexed tradeId, address indexed caller)",
  "event AutoDisputeTriggered(uint256 indexed tradeId, string reason)",
  "event DisputeResolved(uint256 indexed tradeId, address winner)",
  "event FeeWalletUpdated(address newFeeWallet)",
  "event FeeRateUpdated(uint256 newFeeRate)",
  "event TimeoutDurationUpdated(uint256 newDuration)",
  "event AutoDisputeDurationUpdated(uint256 newDuration)",
  "event EmergencyWithdraw(address indexed token, uint256 amount, address indexed to)"
];

// ABI لعقد USDT (مبسط)
const USDT_ABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function transfer(address to, uint256 amount) returns (bool)",
  "function approve(address spender, uint256 amount) returns (bool)",
  "function allowance(address owner, address spender) view returns (uint256)",
  "function decimals() view returns (uint8)"
];

// تعداد حالات الصفقة
export enum TradeStatus {
  CREATED = 0,        // تم إنشاء الصفقة
  BUYER_JOINED = 1,   // انضم المشتري
  PAYMENT_SENT = 2,   // تم إرسال الدفعة
  COMPLETED = 3,      // تمت الصفقة بنجاح
  CANCELLED = 4,      // تم إلغاء الصفقة
  DISPUTED = 5        // صفقة متنازع عليها
}

export interface TradeData {
  id: number;
  seller: string;
  buyer: string;
  amount: number;
  feeAmount: number;
  status: TradeStatus;
  createdAt: number;
  lastActivity: number;
  sellerCancelRequested: boolean;
  buyerCancelRequested: boolean;
  cancelRequestTime: number;
  paymentSentTime: number;
}

export interface ContractStats {
  totalTrades: number;
  activeTrades: number;
  completedTrades: number;
  cancelledTrades: number;
  disputedTrades: number;
  totalVolumeUSDT: number;
}

export interface ActiveTradesData {
  tradeIds: number[];
  sellers: string[];
  buyers: string[];
  amounts: number[];
  statuses: TradeStatus[];
}

export interface ContractError {
  code: string | number;
  message: string;
  type: 'CONTRACT_NOT_FOUND' | 'NETWORK_ERROR' | 'TRANSACTION_FAILED' | 'INSUFFICIENT_FUNDS' | 'USER_REJECTED' | 'UNKNOWN';
}

export interface ContractService {
  // دوال القراءة
  getTrade(tradeId: number): Promise<TradeData>;
  getSellerActiveTrades(seller: string): Promise<number[]>;
  getBuyerActiveTrades(buyer: string): Promise<number[]>;
  hasActiveTrade(user: string, tradeId: number): Promise<boolean>;
  getUserActiveTradeCount(user: string): Promise<number>;
  isTradeExpired(tradeId: number): Promise<boolean>;
  canAutoDispute(tradeId: number): Promise<boolean>;
  getActiveTrades(startId: number, limit: number): Promise<ActiveTradesData>;
  getContractStats(): Promise<ContractStats>;
  getTradesEligibleForAutoDispute(limit: number): Promise<number[]>;
  getContractUSDTBalance(): Promise<string>;
  getUSDTBalance(userAddress: string): Promise<string>;
  getUSDTAllowance(userAddress: string): Promise<number>;
  getFeeRate(): Promise<number>;
  getFeeWallet(): Promise<string>;
  getAutoTimeoutDuration(): Promise<number>;
  getCancelWaitDuration(): Promise<number>;
  getAutoDisputeDuration(): Promise<number>;
  getNextTradeId(): Promise<number>;

  // دوال التحقق من حالة العقد
  isPaused(): Promise<boolean>;
  getOwner(): Promise<string>;

  // دوال الكتابة
  createTrade(amount: number): Promise<string>;
  joinTrade(tradeId: number): Promise<string>;
  confirmPaymentSent(tradeId: number): Promise<string>;
  confirmPaymentReceived(tradeId: number): Promise<string>;
  requestCancel(tradeId: number): Promise<string>;
  executeCancelAfterTimeout(tradeId: number): Promise<string>;
  autoCancel(tradeId: number): Promise<string>;
  autoDispute(tradeId: number): Promise<string>;
  requestDispute(tradeId: number): Promise<string>;
  resolveDispute(tradeId: number, favorBuyer: boolean): Promise<string>;

  // دوال الإدارة
  setFeeWallet(newFeeWallet: string): Promise<string>;
  setFeeRate(newFeeRate: number): Promise<string>;
  setAutoTimeoutDuration(newDuration: number): Promise<string>;
  setCancelWaitDuration(newDuration: number): Promise<string>;
  setAutoDisputeDuration(newDuration: number): Promise<string>;
  pauseContract(): Promise<string>;
  unpauseContract(): Promise<string>;
  transferOwnership(newOwner: string): Promise<string>;
  renounceOwnership(): Promise<string>;
  emergencyWithdraw(token: string, amount: number, to: string): Promise<string>;

  // دوال مساعدة
  approveUSDT(amount: number): Promise<string>;
  isConnected(): boolean;
  canRead(): boolean;
  canWrite(): boolean;
  supportsPauseUnpause(): boolean;
  ensureConnection(): Promise<boolean>;
  connect(): Promise<void>;
  getCurrentAccount(): Promise<string | null>;
  waitForTransaction(txHash: string): Promise<void>;

  // وضع تجريبي
  enableDemoMode(): void;
  isDemoMode(): boolean;
  getDemoTradeData(tradeId: number): TradeData;
}

class EthersContractService implements ContractService {
  private provider: ethers.BrowserProvider | null = null;
  private signer: ethers.Signer | null = null;
  private escrowContract: ethers.Contract | null = null;
  private usdtContract: ethers.Contract | null = null;
  private demoMode: boolean = false;

  constructor() {
    // تهيئة آمنة بدون طلب اتصال المحفظة
    this.safeInitialize();

    // الاستماع لتغييرات الحسابات
    if (typeof window !== 'undefined' && window.ethereum) {
      window.ethereum.on('accountsChanged', this.handleAccountsChanged.bind(this));
      window.ethereum.on('chainChanged', this.handleChainChanged.bind(this));
    }
  }

  private async handleAccountsChanged(accounts: string[]) {
    console.log('تغيير الحسابات:', accounts);
    if (accounts.length === 0) {
      // تم قطع الاتصال
      this.signer = null;
      console.log('تم قطع اتصال المحفظة');
    } else {
      // تغيير الحساب - إعادة إعداد signer
      try {
        if (this.provider) {
          this.signer = await this.provider.getSigner();
          await this.setupContractsWithSigner();
          console.log('تم تحديث الحساب المتصل');
        }
      } catch (error) {
        console.error('خطأ في تحديث الحساب:', error);
      }
    }
  }

  private handleChainChanged(chainId: string) {
    console.log('تغيير الشبكة:', chainId);
    // إعادة تحميل الصفحة عند تغيير الشبكة
    window.location.reload();
  }

  private async safeInitialize() {
    try {
      await this.initializeProvider();
      await this.setupContracts();
    } catch (error) {
      console.warn('تحذير في التهيئة الآمنة:', error);
      // تفعيل الوضع التوضيحي في حالة الفشل
      this.enableDemoMode();
    }
  }

  // وضع العرض التوضيحي
  enableDemoMode(): void {
    this.demoMode = true;
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.setItem('contract_demo_mode', 'true');
    }
    console.log('تم تفعيل الوضع التوضيحي للعقود الذكية');
  }

  isDemoMode(): boolean {
    // التحقق من وجود localStorage (متاح فقط في المتصفح)
    const localStorageDemo = typeof window !== 'undefined' && window.localStorage &&
                             localStorage.getItem('contract_demo_mode') === 'true';

    return this.demoMode || localStorageDemo ||
           process.env.NEXT_PUBLIC_DEFAULT_NETWORK === 'development';
  }

  getDemoTradeData(tradeId: number): TradeData {
    return {
      id: tradeId,
      seller: '0x1234567890123456789012345678901234567890',
      buyer: '0x0987654321098765432109876543210987654321',
      amount: 100,
      feeAmount: 1,
      status: TradeStatus.BUYER_JOINED,
      createdAt: Math.floor(Date.now() / 1000) - 3600, // منذ ساعة
      lastActivity: Math.floor(Date.now() / 1000) - 1800, // منذ 30 دقيقة
      sellerCancelRequested: false,
      buyerCancelRequested: false,
      cancelRequestTime: 0,
      paymentSentTime: 0
    };
  }

  // معالجة الأخطاء المحسنة
  private handleContractError(error: any): ContractError {
    let contractError: ContractError;

    if (error.code === 4001) {
      contractError = {
        code: error.code,
        message: 'تم رفض المعاملة من قبل المستخدم',
        type: 'USER_REJECTED'
      };
    } else if (error.code === 'INSUFFICIENT_FUNDS' || error.message?.includes('insufficient funds')) {
      contractError = {
        code: error.code || 'INSUFFICIENT_FUNDS',
        message: 'رصيد غير كافي لإتمام المعاملة',
        type: 'INSUFFICIENT_FUNDS'
      };
    } else if (error.code === 'NETWORK_ERROR' || error.message?.includes('network')) {
      contractError = {
        code: error.code || 'NETWORK_ERROR',
        message: 'خطأ في الاتصال بالشبكة',
        type: 'NETWORK_ERROR'
      };
    } else if (error.message?.includes('contract') || error.message?.includes('address')) {
      contractError = {
        code: error.code || 'CONTRACT_NOT_FOUND',
        message: 'العقد الذكي غير موجود أو غير صحيح',
        type: 'CONTRACT_NOT_FOUND'
      };
    } else if (error.message?.includes('transaction failed') || error.message?.includes('revert')) {
      contractError = {
        code: error.code || 'TRANSACTION_FAILED',
        message: 'فشلت المعاملة: ' + (error.reason || error.message),
        type: 'TRANSACTION_FAILED'
      };
    } else {
      contractError = {
        code: error.code || 'UNKNOWN',
        message: error.message || 'خطأ غير معروف في العقد الذكي',
        type: 'UNKNOWN'
      };
    }

    return contractError;
  }

  private async initializeProvider() {
    // إذا كان في الوضع التوضيحي، لا نحتاج لتهيئة المزود
    if (this.isDemoMode()) {
      console.log('🎭 تم تفعيل الوضع التوضيحي للعقود الذكية');
      return;
    }

    if (typeof window !== 'undefined' && window.ethereum) {
      try {
        // تهيئة المزود بدون طلب اتصال تلقائي
        this.provider = new ethers.BrowserProvider(window.ethereum);
        console.log('✅ تم تهيئة مزود العقود الذكية بنجاح');

        // التحقق من الشبكة
        try {
          const network = await this.provider.getNetwork();
          console.log('🌐 الشبكة المتصلة:', {
            name: network.name,
            chainId: Number(network.chainId)
          });
        } catch (networkError) {
          console.warn('⚠️ تحذير في التحقق من الشبكة:', networkError);
        }
      } catch (error) {
        const contractError = this.handleContractError(error);
        console.error('❌ خطأ في تهيئة المزود:', contractError.message);

        // تفعيل الوضع التوضيحي تلقائياً في حالة الفشل
        this.enableDemoMode();
        console.log('💡 تم تفعيل الوضع التوضيحي تلقائياً بسبب فشل تهيئة المزود');
      }
    } else {
      console.warn('⚠️ محفظة Ethereum غير متاحة - تم تفعيل الوضع التوضيحي');
      this.enableDemoMode();
    }
  }



  private async setupContracts() {
    if (!this.provider) {
      console.warn('Provider غير متاح لإعداد العقود');
      return;
    }

    try {
      // الحصول على عناوين العقود من خدمة الشبكة
      const contracts = networkService.getCurrentContracts();

      let escrowAddress = getContractAddress('CORE_ESCROW');
      let usdtAddress = getTokenBySymbol('USDT')?.address || '******************************************';

      if (contracts && contracts.escrow && contracts.usdt) {
        escrowAddress = contracts.escrow;
        usdtAddress = contracts.usdt;
      }

      console.log('إعداد العقود بالعناوين:', {
        escrow: escrowAddress,
        usdt: usdtAddress
      });

      // إنشاء عقد Escrow (للقراءة فقط)
      if (escrowAddress && ethers.isAddress(escrowAddress)) {
        try {
          this.escrowContract = new ethers.Contract(
            escrowAddress,
            ESCROW_ABI,
            this.provider
          );
          console.log('✅ تم إعداد عقد Escrow للقراءة بنجاح');

          // اختبار سريع للتأكد من عمل العقد
          try {
            await this.escrowContract.paused();
            console.log('✅ اختبار اتصال عقد Escrow نجح');
          } catch (testError) {
            console.warn('⚠️ فشل اختبار عقد Escrow:', testError);
          }
        } catch (contractError) {
          console.error('❌ فشل في إنشاء عقد Escrow:', contractError);
          this.escrowContract = null;
        }
      } else {
        console.error('❌ عنوان عقد Escrow غير صحيح:', escrowAddress);
      }

      // إنشاء عقد USDT (للقراءة فقط)
      if (usdtAddress && ethers.isAddress(usdtAddress)) {
        try {
          this.usdtContract = new ethers.Contract(
            usdtAddress,
            USDT_ABI,
            this.provider
          );
          console.log('✅ تم إعداد عقد USDT للقراءة بنجاح');
        } catch (contractError) {
          console.error('❌ فشل في إنشاء عقد USDT:', contractError);
          this.usdtContract = null;
        }
      } else {
        console.error('❌ عنوان عقد USDT غير صحيح:', usdtAddress);
      }
    } catch (error) {
      console.error('❌ خطأ عام في إعداد العقود:', error);
      // لا نرمي خطأ لتجنب كسر التطبيق
    }
  }

  // إعادة إعداد العقود عند تغيير الشبكة
  async reinitializeContracts() {
    await this.setupContracts();
  }

  // دوال القراءة
  async getTrade(tradeId: number): Promise<TradeData> {
    // إذا كان في الوضع التوضيحي، أرجع بيانات وهمية
    if (this.isDemoMode()) {
      return this.getDemoTradeData(tradeId);
    }

    if (!this.escrowContract) {
      const error = this.handleContractError({ message: 'العقد غير متصل' });
      throw new Error(error.message);
    }

    try {
      const trade = await this.escrowContract.getTradeDetails(tradeId);
      return {
        id: Number(trade.id),
        seller: trade.seller,
        buyer: trade.buyer,
        amount: Number(ethers.formatUnits(trade.amount, 18)),
        feeAmount: Number(ethers.formatUnits(trade.feeAmount, 18)),
        status: Number(trade.status) as TradeStatus,
        createdAt: Number(trade.createdAt),
        lastActivity: Number(trade.lastActivity),
        sellerCancelRequested: trade.sellerCancelRequested,
        buyerCancelRequested: trade.buyerCancelRequested,
        cancelRequestTime: Number(trade.cancelRequestTime),
        paymentSentTime: Number(trade.paymentSentTime)
      };
    } catch (error: any) {
      const contractError = this.handleContractError(error);
      console.error('خطأ في جلب بيانات الصفقة:', contractError);
      throw new Error(contractError.message);
    }
  }

  async getSellerActiveTrades(seller: string): Promise<number[]> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const trades = await this.escrowContract.getSellerActiveTrades(seller);
      return trades.map((trade: any) => Number(trade));
    } catch (error) {
      console.error('خطأ في جلب صفقات البائع:', error);
      throw error;
    }
  }

  async getBuyerActiveTrades(buyer: string): Promise<number[]> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const trades = await this.escrowContract.getBuyerActiveTrades(buyer);
      return trades.map((trade: any) => Number(trade));
    } catch (error) {
      console.error('خطأ في جلب صفقات المشتري:', error);
      throw error;
    }
  }

  async hasActiveTrade(user: string, tradeId: number): Promise<boolean> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      return await this.escrowContract.hasActiveTrade(user, tradeId);
    } catch (error) {
      console.error('خطأ في التحقق من الصفقة النشطة:', error);
      throw error;
    }
  }

  async getUserActiveTradeCount(user: string): Promise<number> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const count = await this.escrowContract.getUserActiveTradeCount(user);
      return Number(count);
    } catch (error) {
      console.error('خطأ في جلب عدد الصفقات النشطة:', error);
      throw error;
    }
  }

  async isTradeExpired(tradeId: number): Promise<boolean> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      return await this.escrowContract.isTradeExpired(tradeId);
    } catch (error) {
      console.error('خطأ في التحقق من انتهاء صلاحية الصفقة:', error);
      throw error;
    }
  }

  async canAutoDispute(tradeId: number): Promise<boolean> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      return await this.escrowContract.canAutoDispute(tradeId);
    } catch (error) {
      console.error('خطأ في التحقق من إمكانية النزاع التلقائي:', error);
      throw error;
    }
  }

  async getActiveTrades(startId: number, limit: number): Promise<ActiveTradesData> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const result = await this.escrowContract.getActiveTrades(startId, limit);
      return {
        tradeIds: result.tradeIds.map((id: any) => Number(id)),
        sellers: result.sellers,
        buyers: result.buyers,
        amounts: result.amounts.map((amount: any) => Number(ethers.formatUnits(amount, 18))),
        statuses: result.statuses.map((status: any) => Number(status) as TradeStatus)
      };
    } catch (error) {
      console.error('خطأ في جلب الصفقات النشطة:', error);
      throw error;
    }
  }

  async getContractStats(): Promise<ContractStats> {
    // في وضع التطوير، إرجاع بيانات وهمية واقعية
    if (process.env.NEXT_PUBLIC_DEFAULT_NETWORK === 'development') {
      return {
        totalTrades: 1247,
        activeTrades: 89,
        completedTrades: 1098,
        cancelledTrades: 45,
        disputedTrades: 15,
        totalVolumeUSDT: 2450000
      };
    }

    if (!this.escrowContract) {
      console.warn('العقد غير متصل - إرجاع إحصائيات افتراضية');
      return {
        totalTrades: 0,
        activeTrades: 0,
        completedTrades: 0,
        cancelledTrades: 0,
        disputedTrades: 0,
        totalVolumeUSDT: 0
      };
    }

    try {
      const stats = await this.escrowContract.getContractStats();
      return {
        totalTrades: Number(stats.totalTrades),
        activeTrades: Number(stats.activeTrades),
        completedTrades: Number(stats.completedTrades),
        cancelledTrades: Number(stats.cancelledTrades),
        disputedTrades: Number(stats.disputedTrades),
        totalVolumeUSDT: Number(ethers.formatUnits(stats.totalVolumeUSDT, 18))
      };
    } catch (error) {
      console.error('خطأ في جلب إحصائيات العقد:', error);
      // إرجاع إحصائيات افتراضية في حالة الخطأ
      return {
        totalTrades: 0,
        activeTrades: 0,
        completedTrades: 0,
        cancelledTrades: 0,
        disputedTrades: 0,
        totalVolumeUSDT: 0
      };
    }
  }

  async getTradesEligibleForAutoDispute(limit: number): Promise<number[]> {
    // في وضع التطوير، إرجاع قائمة وهمية من الصفقات
    if (process.env.NEXT_PUBLIC_DEFAULT_NETWORK === 'development') {
      return []; // لا توجد صفقات مؤهلة للنزاع في وضع التطوير
    }

    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const trades = await this.escrowContract.getTradesEligibleForAutoDispute(limit);
      return trades.map((trade: any) => Number(trade));
    } catch (error) {
      console.error('خطأ في جلب الصفقات المؤهلة للنزاع التلقائي:', error);
      throw error;
    }
  }

  async getContractUSDTBalance(): Promise<string> {
    // في وضع التطوير، إرجاع رصيد وهمي
    if (process.env.NEXT_PUBLIC_DEFAULT_NETWORK === 'development') {
      return '125000.50'; // رصيد وهمي بـ USDT
    }

    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const balance = await this.escrowContract.getContractUSDTBalance();
      return ethers.formatUnits(balance, 18);
    } catch (error) {
      console.error('خطأ في جلب رصيد العقد:', error);
      throw error;
    }
  }

  async getUSDTBalance(userAddress: string): Promise<string> {
    if (!this.usdtContract) throw new Error('عقد USDT غير متصل');

    try {
      const balance = await this.usdtContract.balanceOf(userAddress);
      return ethers.formatUnits(balance, 18);
    } catch (error) {
      console.error('خطأ في جلب رصيد USDT:', error);
      throw error;
    }
  }

  async getFeeRate(): Promise<number> {
    if (!this.escrowContract) {
      console.warn('العقد غير متصل - إرجاع قيمة افتراضية');
      return 100; // 1% كقيمة افتراضية
    }

    try {
      // استخدام feeRate() فقط (الوظيفة المولدة تلقائياً من public variable)
      const feeRate = await this.escrowContract.feeRate();
      return Number(feeRate);
    } catch (error) {
      console.error('خطأ في جلب معدل الرسوم:', error);
      return 100; // قيمة افتراضية في حالة الخطأ
    }
  }

  async getFeeWallet(): Promise<string> {
    try {
      // في وضع التطوير، إرجاع عنوان المدير من متغيرات البيئة
      if (process.env.NEXT_PUBLIC_DEFAULT_NETWORK === 'development') {
        return process.env.ADMIN_WALLET_ADDRESS || '******************************************';
      }

      // التأكد من الاتصال أولاً
      const isConnected = await this.ensureConnection();
      if (!isConnected || !this.escrowContract) {
        console.error('العقد غير متصل - تفاصيل التشخيص:');
        console.error('- Provider:', !!this.provider);
        console.error('- Signer:', !!this.signer);
        console.error('- EscrowContract:', !!this.escrowContract);
        console.error('- CanRead:', this.canRead());

        // إرجاع قيمة افتراضية بدلاً من رمي خطأ
        console.warn('استخدام عنوان المدير الافتراضي بسبب عدم توفر العقد');
        return process.env.ADMIN_WALLET_ADDRESS || '******************************************';
      }

      // استخدام feeWallet() (الوظيفة المولدة تلقائياً من public variable)
      console.log('محاولة استدعاء feeWallet من العقد...');
      const result = await this.escrowContract.feeWallet();
      console.log('✅ نتيجة feeWallet:', result);
      return result;
    } catch (error) {
      console.error('❌ خطأ في جلب محفظة الرسوم:', error);
      console.error('تفاصيل العقد:', {
        address: this.escrowContract?.target || this.escrowContract?.address,
        provider: !!this.provider,
        signer: !!this.signer,
        canRead: this.canRead()
      });

      // إرجاع قيمة افتراضية بدلاً من رمي خطأ
      console.warn('استخدام عنوان المدير الافتراضي بسبب خطأ في العقد');
      return process.env.ADMIN_WALLET_ADDRESS || '******************************************';
    }
  }

  async getAutoTimeoutDuration(): Promise<number> {
    // في وضع التطوير، إرجاع قيمة افتراضية (30 دقيقة)
    if (process.env.NEXT_PUBLIC_DEFAULT_NETWORK === 'development') {
      return 1800; // 30 دقيقة بالثواني
    }

    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const duration = await this.escrowContract.autoTimeoutDuration();
      return Number(duration);
    } catch (error) {
      console.error('خطأ في جلب مدة الإلغاء التلقائي:', error);
      throw error;
    }
  }

  async getCancelWaitDuration(): Promise<number> {
    // في وضع التطوير، إرجاع قيمة افتراضية (5 دقائق)
    if (process.env.NEXT_PUBLIC_DEFAULT_NETWORK === 'development') {
      return 300; // 5 دقائق بالثواني
    }

    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const duration = await this.escrowContract.cancelWaitDuration();
      return Number(duration);
    } catch (error) {
      console.error('خطأ في جلب مدة انتظار الإلغاء:', error);
      throw error;
    }
  }

  async getAutoDisputeDuration(): Promise<number> {
    // في وضع التطوير، إرجاع قيمة افتراضية (24 ساعة)
    if (process.env.NEXT_PUBLIC_DEFAULT_NETWORK === 'development') {
      return 86400; // 24 ساعة بالثواني
    }

    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const duration = await this.escrowContract.autoDisputeDuration();
      return Number(duration);
    } catch (error) {
      console.error('خطأ في جلب مدة النزاع التلقائي:', error);
      throw error;
    }
  }

  async getNextTradeId(): Promise<number> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const nextId = await this.escrowContract.nextTradeId();
      return Number(nextId);
    } catch (error) {
      console.error('خطأ في جلب معرف الصفقة التالي:', error);
      throw error;
    }
  }

  // دوال التحقق من حالة العقد
  async isPaused(): Promise<boolean> {
    if (!this.escrowContract) {
      console.warn('العقد غير متصل - افتراض أن العقد غير متوقف');
      return false;
    }

    try {
      // التحقق من وجود دالة paused في العقد
      if (!this.escrowContract.paused) {
        console.warn('العقد لا يدعم وظيفة pause/unpause');
        return false;
      }
      return await this.escrowContract.paused();
    } catch (error) {
      console.error('خطأ في التحقق من حالة الإيقاف:', error);
      // إذا فشل التحقق، افترض أن العقد غير متوقف
      return false;
    }
  }

  async getOwner(): Promise<string> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      // التحقق من وجود دالة owner في العقد
      if (!this.escrowContract.owner) {
        console.warn('العقد لا يحتوي على دالة owner');
        return '';
      }
      return await this.escrowContract.owner();
    } catch (error) {
      console.error('خطأ في جلب مالك العقد:', error);
      return '';
    }
  }

  // دوال الكتابة
  async createTrade(amount: number): Promise<string> {
    try {
      const contractWithSigner = this.ensureSignerAndContract('escrow');
      const amountWei = ethers.parseUnits(amount.toString(), 18);

      const tx = await contractWithSigner.createTrade(amountWei, {
        gasLimit: ENHANCED_GAS_LIMITS.CREATE_TRADE
      });

      return tx.hash;
    } catch (error) {
      console.error('خطأ في إنشاء الصفقة:', error);
      throw error;
    }
  }

  async joinTrade(tradeId: number): Promise<string> {
    try {
      const contractWithSigner = this.ensureSignerAndContract('escrow');

      const tx = await contractWithSigner.joinTrade(tradeId, {
        gasLimit: ENHANCED_GAS_LIMITS.JOIN_TRADE
      });

      return tx.hash;
    } catch (error) {
      console.error('خطأ في الانضمام للصفقة:', error);
      throw error;
    }
  }

  async confirmPaymentSent(tradeId: number): Promise<string> {
    try {
      const contractWithSigner = this.ensureSignerAndContract('escrow');

      const tx = await contractWithSigner.confirmPaymentSent(tradeId, {
        gasLimit: ENHANCED_GAS_LIMITS.CONFIRM_PAYMENT_SENT
      });

      return tx.hash;
    } catch (error) {
      console.error('خطأ في تأكيد إرسال الدفعة:', error);
      throw error;
    }
  }

  async confirmPaymentReceived(tradeId: number): Promise<string> {
    try {
      const contractWithSigner = this.ensureSignerAndContract('escrow');

      const tx = await contractWithSigner.confirmPaymentReceived(tradeId, {
        gasLimit: ENHANCED_GAS_LIMITS.CONFIRM_PAYMENT_RECEIVED
      });

      return tx.hash;
    } catch (error) {
      console.error('خطأ في تأكيد استلام الدفعة:', error);
      throw error;
    }
  }

  async requestCancel(tradeId: number): Promise<string> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const tx = await this.escrowContract.requestCancel(tradeId, {
        gasLimit: ENHANCED_GAS_LIMITS.CANCEL_TRADE
      });

      return tx.hash;
    } catch (error) {
      console.error('خطأ في طلب إلغاء الصفقة:', error);
      throw error;
    }
  }

  async executeCancelAfterTimeout(tradeId: number): Promise<string> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const tx = await this.escrowContract.executeCancelAfterTimeout(tradeId, {
        gasLimit: ENHANCED_GAS_LIMITS.CANCEL_TRADE
      });

      return tx.hash;
    } catch (error) {
      console.error('خطأ في تنفيذ الإلغاء بعد انتهاء المهلة:', error);
      throw error;
    }
  }

  async autoCancel(tradeId: number): Promise<string> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const tx = await this.escrowContract.autoCancel(tradeId, {
        gasLimit: ENHANCED_GAS_LIMITS.CANCEL_TRADE
      });

      return tx.hash;
    } catch (error) {
      console.error('خطأ في الإلغاء التلقائي:', error);
      throw error;
    }
  }

  async autoDispute(tradeId: number): Promise<string> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const tx = await this.escrowContract.autoDispute(tradeId, {
        gasLimit: ENHANCED_GAS_LIMITS.RESOLVE_DISPUTE
      });

      return tx.hash;
    } catch (error) {
      console.error('خطأ في النزاع التلقائي:', error);
      throw error;
    }
  }

  async requestDispute(tradeId: number): Promise<string> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const tx = await this.escrowContract.requestDispute(tradeId, {
        gasLimit: ENHANCED_GAS_LIMITS.RESOLVE_DISPUTE
      });

      return tx.hash;
    } catch (error) {
      console.error('خطأ في طلب النزاع:', error);
      throw error;
    }
  }

  async resolveDispute(tradeId: number, favorBuyer: boolean): Promise<string> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const tx = await this.escrowContract.resolveDispute(tradeId, favorBuyer, {
        gasLimit: ENHANCED_GAS_LIMITS.RESOLVE_DISPUTE
      });

      return tx.hash;
    } catch (error) {
      console.error('خطأ في حل النزاع:', error);
      throw error;
    }
  }

  // دوال الإدارة
  async setFeeWallet(newFeeWallet: string): Promise<string> {
    try {
      const contractWithSigner = this.ensureSignerAndContract('escrow');
      const tx = await contractWithSigner.setFeeWallet(newFeeWallet);
      return tx.hash;
    } catch (error) {
      console.error('خطأ في تحديث محفظة الرسوم:', error);
      throw error;
    }
  }

  async setFeeRate(newFeeRate: number): Promise<string> {
    try {
      const contractWithSigner = this.ensureSignerAndContract('escrow');
      const tx = await contractWithSigner.setFeeRate(newFeeRate);
      return tx.hash;
    } catch (error) {
      console.error('خطأ في تحديث معدل الرسوم:', error);
      throw error;
    }
  }

  async setAutoTimeoutDuration(newDuration: number): Promise<string> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const tx = await this.escrowContract.setAutoTimeoutDuration(newDuration);
      return tx.hash;
    } catch (error) {
      console.error('خطأ في تحديث مدة الإلغاء التلقائي:', error);
      throw error;
    }
  }

  async setCancelWaitDuration(newDuration: number): Promise<string> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const tx = await this.escrowContract.setCancelWaitDuration(newDuration);
      return tx.hash;
    } catch (error) {
      console.error('خطأ في تحديث مدة انتظار الإلغاء:', error);
      throw error;
    }
  }

  async setAutoDisputeDuration(newDuration: number): Promise<string> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const tx = await this.escrowContract.setAutoDisputeDuration(newDuration);
      return tx.hash;
    } catch (error) {
      console.error('خطأ في تحديث مدة النزاع التلقائي:', error);
      throw error;
    }
  }

  async pauseContract(): Promise<string> {
    try {
      // التحقق من دعم وظائف pause/unpause
      if (!this.supportsPauseUnpause()) {
        throw new Error('هذا العقد لا يدعم وظائف الإيقاف والتشغيل');
      }

      // التحقق من حالة العقد أولاً
      const isPaused = await this.isPaused();
      if (isPaused) {
        throw new Error('العقد متوقف بالفعل');
      }

      // التحقق من الصلاحيات
      const currentAccount = await this.getCurrentAccount();
      const owner = await this.getOwner();

      if (!currentAccount || !owner || currentAccount.toLowerCase() !== owner.toLowerCase()) {
        throw new Error('فقط مالك العقد يمكنه إيقاف العقد');
      }

      const contractWithSigner = this.ensureSignerAndContract('escrow');
      const tx = await contractWithSigner.pauseContract();
      return tx.hash;
    } catch (error) {
      console.error('خطأ في إيقاف العقد:', error);
      throw error;
    }
  }

  async unpauseContract(): Promise<string> {
    try {
      // التحقق من دعم وظائف pause/unpause
      if (!this.supportsPauseUnpause()) {
        throw new Error('هذا العقد لا يدعم وظائف الإيقاف والتشغيل');
      }

      // التحقق من حالة العقد أولاً
      const isPaused = await this.isPaused();
      if (!isPaused) {
        throw new Error('العقد يعمل بالفعل (غير متوقف)');
      }

      // التحقق من الصلاحيات
      const currentAccount = await this.getCurrentAccount();
      const owner = await this.getOwner();

      if (!currentAccount || !owner || currentAccount.toLowerCase() !== owner.toLowerCase()) {
        throw new Error('فقط مالك العقد يمكنه تشغيل العقد');
      }

      const contractWithSigner = this.ensureSignerAndContract('escrow');
      const tx = await contractWithSigner.unpauseContract();
      return tx.hash;
    } catch (error) {
      console.error('خطأ في تشغيل العقد:', error);
      throw error;
    }
  }

  async transferOwnership(newOwner: string): Promise<string> {
    try {
      // التحقق من صحة العنوان
      if (!ethers.isAddress(newOwner)) {
        throw new Error('عنوان المالك الجديد غير صحيح');
      }

      // التحقق من الصلاحيات
      const currentAccount = await this.getCurrentAccount();
      const owner = await this.getOwner();

      if (!currentAccount || !owner || currentAccount.toLowerCase() !== owner.toLowerCase()) {
        throw new Error('فقط مالك العقد يمكنه تحويل الملكية');
      }

      const contractWithSigner = this.ensureSignerAndContract('escrow');
      const tx = await contractWithSigner.transferOwnership(newOwner);
      return tx.hash;
    } catch (error) {
      console.error('خطأ في تحويل ملكية العقد:', error);
      throw error;
    }
  }

  async renounceOwnership(): Promise<string> {
    try {
      // التحقق من الصلاحيات
      const currentAccount = await this.getCurrentAccount();
      const owner = await this.getOwner();

      if (!currentAccount || !owner || currentAccount.toLowerCase() !== owner.toLowerCase()) {
        throw new Error('فقط مالك العقد يمكنه التخلي عن الملكية');
      }

      const contractWithSigner = this.ensureSignerAndContract('escrow');
      const tx = await contractWithSigner.renounceOwnership();
      return tx.hash;
    } catch (error) {
      console.error('خطأ في التخلي عن ملكية العقد:', error);
      throw error;
    }
  }

  async emergencyWithdraw(token: string, amount: number, to: string): Promise<string> {
    if (!this.escrowContract) throw new Error('العقد غير متصل');

    try {
      const amountWei = token === '******************************************'
        ? ethers.parseEther(amount.toString())
        : ethers.parseUnits(amount.toString(), 18);

      const tx = await this.escrowContract.emergencyWithdraw(token, amountWei, to);
      return tx.hash;
    } catch (error) {
      console.error('خطأ في السحب الطارئ:', error);
      throw error;
    }
  }

  async approveUSDT(amount: number): Promise<string> {
    try {
      const contractWithSigner = this.ensureSignerAndContract('usdt');
      const amountWei = ethers.parseUnits(amount.toString(), 18);
      const tx = await contractWithSigner.approve(getContractAddress('CORE_ESCROW'), amountWei);

      return tx.hash;
    } catch (error) {
      console.error('خطأ في الموافقة على USDT:', error);
      throw error;
    }
  }

  async getUSDTAllowance(userAddress: string): Promise<number> {
    if (!this.usdtContract) throw new Error('عقد USDT غير متصل');

    try {
      const allowance = await this.usdtContract.allowance(userAddress, getContractAddress('CORE_ESCROW'));
      return Number(ethers.formatUnits(allowance, 18));
    } catch (error) {
      console.error('خطأ في جلب موافقة USDT:', error);
      throw error;
    }
  }

  // دوال مساعدة
  isConnected(): boolean {
    return this.provider !== null && this.signer !== null && this.escrowContract !== null;
  }

  // التحقق من إمكانية القراءة (provider فقط)
  canRead(): boolean {
    return this.provider !== null && this.escrowContract !== null;
  }

  // التحقق من إمكانية الكتابة (signer مطلوب)
  canWrite(): boolean {
    return this.isConnected();
  }

  // التحقق من دعم وظائف pause/unpause
  supportsPauseUnpause(): boolean {
    if (!this.escrowContract) return false;
    return !!(this.escrowContract.pauseContract && this.escrowContract.unpauseContract && this.escrowContract.paused);
  }

  // التحقق من الاتصال الموجود وإعداد signer إذا لزم الأمر
  async ensureConnection(): Promise<boolean> {
    try {
      // إذا كان متصل بالفعل، لا حاجة لفعل شيء
      if (this.isConnected()) {
        return true;
      }

      // التحقق من وجود window.ethereum
      if (typeof window === 'undefined' || !window.ethereum) {
        console.warn('محفظة MetaMask غير متاحة');
        return false;
      }

      // إذا لم يكن provider موجود، أنشئه
      if (!this.provider) {
        try {
          this.provider = new ethers.BrowserProvider(window.ethereum);
          console.log('تم إنشاء provider جديد');
        } catch (error) {
          console.error('فشل في إنشاء provider:', error);
          return false;
        }
      }

      // إعداد العقود للقراءة فقط أولاً
      if (!this.escrowContract) {
        await this.setupContracts();
      }

      // إذا كان provider موجود لكن signer مفقود، حاول إعداد signer
      if (this.provider && !this.signer) {
        try {
          // التحقق من وجود حسابات متصلة
          const accounts = await window.ethereum.request({ method: 'eth_accounts' });
          if (accounts && accounts.length > 0) {
            // المحفظة متصلة، أعد إعداد signer
            this.signer = await this.provider.getSigner();
            await this.setupContractsWithSigner();
            console.log('تم إعادة إعداد signer للمحفظة المتصلة');
            return true;
          } else {
            console.log('لا توجد حسابات متصلة - العقد متاح للقراءة فقط');
            return this.canRead(); // إرجاع true إذا كان يمكن القراءة على الأقل
          }
        } catch (error) {
          console.warn('فشل في إعادة إعداد signer:', error);
          return this.canRead(); // إرجاع true إذا كان يمكن القراءة على الأقل
        }
      }

      return this.canRead(); // إرجاع true إذا كان يمكن القراءة على الأقل
    } catch (error) {
      console.error('خطأ في ensureConnection:', error);
      return false;
    }
  }

  async connect(): Promise<void> {
    if (typeof window === 'undefined' || !window.ethereum) {
      throw new Error('محفظة MetaMask غير مثبتة');
    }

    try {
      // طلب الاتصال بالمحفظة
      await window.ethereum.request({ method: 'eth_requestAccounts' });

      // تهيئة المزود إذا لم يكن موجوداً
      if (!this.provider) {
        this.provider = new ethers.BrowserProvider(window.ethereum);
      }

      // الحصول على signer للكتابة
      this.signer = await this.provider.getSigner();

      // إعادة إعداد العقود مع signer
      await this.setupContractsWithSigner();

      console.log('تم الاتصال بالعقود الذكية بنجاح');
    } catch (error) {
      console.error('خطأ في الاتصال بالعقد:', error);
      throw error;
    }
  }

  private async setupContractsWithSigner() {
    if (!this.provider || !this.signer) return;

    try {
      // الحصول على عناوين العقود
      const contracts = networkService.getCurrentContracts();
      let escrowAddress = getContractAddress('CORE_ESCROW');
      let usdtAddress = getTokenBySymbol('USDT')?.address || '******************************************';

      if (contracts && contracts.escrow && contracts.usdt) {
        escrowAddress = contracts.escrow;
        usdtAddress = contracts.usdt;
      }

      // إنشاء العقود مع signer للكتابة
      if (escrowAddress && ethers.isAddress(escrowAddress)) {
        this.escrowContract = new ethers.Contract(
          escrowAddress,
          ESCROW_ABI,
          this.signer
        );
      }

      if (usdtAddress && ethers.isAddress(usdtAddress)) {
        this.usdtContract = new ethers.Contract(
          usdtAddress,
          USDT_ABI,
          this.signer
        );
      }
    } catch (error) {
      console.error('خطأ في إعداد العقود مع signer:', error);
    }
  }

  // دالة مساعدة للتحقق من وجود signer وإعداد العقد للكتابة
  private ensureSignerAndContract(contractType: 'escrow' | 'usdt'): ethers.Contract {
    if (!this.signer) {
      throw new Error('يجب الاتصال بالمحفظة أولاً لتنفيذ هذه العملية');
    }

    const contract = contractType === 'escrow' ? this.escrowContract : this.usdtContract;
    if (!contract) {
      throw new Error(`عقد ${contractType === 'escrow' ? 'Escrow' : 'USDT'} غير متصل`);
    }

    // التأكد من أن العقد متصل بـ signer
    return contract.connect(this.signer) as ethers.Contract;
  }

  async waitForTransaction(txHash: string): Promise<void> {
    if (!this.provider) throw new Error('المزود غير متصل');

    try {
      const receipt = await this.provider.waitForTransaction(txHash);

      if (!receipt) {
        throw new Error('فشل في الحصول على إيصال المعاملة');
      }

      if (receipt.status === 0) {
        throw new Error('فشلت المعاملة في العقد الذكي');
      }
    } catch (error) {
      console.error('خطأ في انتظار تأكيد المعاملة:', error);
      throw error;
    }
  }

  async getCurrentAccount(): Promise<string | null> {
    if (!this.signer) return null;
    try {
      return await this.signer.getAddress();
    } catch (error) {
      console.error('خطأ في جلب عنوان الحساب:', error);
      return null;
    }
  }
}

// إنشاء مثيل واحد من الخدمة
export const contractService = new EthersContractService();
