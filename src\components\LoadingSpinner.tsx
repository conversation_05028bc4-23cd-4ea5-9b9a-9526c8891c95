'use client';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'white' | 'gray';
  text?: string;
  fullScreen?: boolean;
}

export default function LoadingSpinner({ 
  size = 'md', 
  color = 'primary', 
  text = 'جاري التحميل...', 
  fullScreen = false 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  const colorClasses = {
    primary: 'border-blue-600',
    white: 'border-white',
    gray: 'border-gray-600'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  };

  const spinner = (
    <div className="flex flex-col items-center justify-center space-y-4">
      {/* Spinner متقدم */}
      <div className="relative">
        <div className={`${sizeClasses[size]} border-4 border-gray-200 rounded-full animate-spin`}>
          <div className={`absolute inset-0 border-4 ${colorClasses[color]} border-t-transparent rounded-full animate-spin`}></div>
        </div>
        {/* نقاط متحركة */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="flex space-x-1">
            <div className={`w-1 h-1 bg-current rounded-full animate-pulse`} style={{animationDelay: '0s'}}></div>
            <div className={`w-1 h-1 bg-current rounded-full animate-pulse`} style={{animationDelay: '0.2s'}}></div>
            <div className={`w-1 h-1 bg-current rounded-full animate-pulse`} style={{animationDelay: '0.4s'}}></div>
          </div>
        </div>
      </div>
      
      {text && (
        <p className={`${textSizeClasses[size]} font-medium text-gray-600 animate-pulse`}>
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
        {spinner}
      </div>
    );
  }

  return spinner;
}

// مكونات مخصصة للحالات الشائعة
export function PageLoader() {
  return <LoadingSpinner size="lg" fullScreen text="جاري تحميل الصفحة..." />;
}

export function ButtonLoader() {
  return <LoadingSpinner size="sm" color="white" text="" />;
}

export function CardLoader() {
  return (
    <div className="animate-pulse">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-4 space-x-reverse mb-4">
          <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            <div className="h-3 bg-gray-300 rounded w-1/2"></div>
          </div>
        </div>
        <div className="space-y-3">
          <div className="h-4 bg-gray-300 rounded"></div>
          <div className="h-4 bg-gray-300 rounded w-5/6"></div>
          <div className="h-10 bg-gray-300 rounded"></div>
        </div>
      </div>
    </div>
  );
}

export function OffersLoader() {
  return (
    <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[...Array(6)].map((_, index) => (
        <CardLoader key={index} />
      ))}
    </div>
  );
}
