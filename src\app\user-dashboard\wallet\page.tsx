'use client';

import { useState, useEffect } from 'react';
import { 
  Wallet,
  Plus,
  Minus,
  ArrowUpRight,
  ArrowDownLeft,
  Copy,
  ExternalLink,
  RefreshCw,
  Eye,
  EyeOff,
  Shield,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  Filter,
  Download
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { useSearchParams, useRouter } from 'next/navigation';

interface WalletBalance {
  currency: string;
  balance: number;
  lockedBalance: number;
  usdValue: number;
  change24h: number;
}

interface Transaction {
  id: string;
  type: 'deposit' | 'withdrawal' | 'trade' | 'fee' | 'refund';
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  timestamp: string;
  description: string;
  txHash?: string;
  fee?: number;
  relatedTradeId?: string;
}

export default function WalletPage() {
  const { t, formatCurrency, formatDate } = useUserDashboardTranslation();
  const { user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [balances, setBalances] = useState<WalletBalance[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [showBalances, setShowBalances] = useState(true);
  const [selectedCurrency, setSelectedCurrency] = useState('all');
  const [transactionFilter, setTransactionFilter] = useState<'all' | 'deposit' | 'withdrawal' | 'trade'>('all');
  const [showDepositModal, setShowDepositModal] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);

  // التحقق من معاملات URL
  useEffect(() => {
    const action = searchParams.get('action');
    if (action === 'deposit') {
      setShowDepositModal(true);
    } else if (action === 'withdraw') {
      setShowWithdrawModal(true);
    }
  }, [searchParams]);

  // جلب بيانات المحفظة
  useEffect(() => {
    const fetchWalletData = async () => {
      try {
        setLoading(true);
        
        // محاكاة البيانات - يجب استبدالها بـ API حقيقي
        const mockBalances: WalletBalance[] = [
          {
            currency: 'USDT',
            balance: 2500.50,
            lockedBalance: 150.00,
            usdValue: 2500.50,
            change24h: 2.5
          },
          {
            currency: 'SAR',
            balance: 9375.75,
            lockedBalance: 562.50,
            usdValue: 2500.20,
            change24h: -1.2
          },
          {
            currency: 'BTC',
            balance: 0.05432,
            lockedBalance: 0.00150,
            usdValue: 2716.00,
            change24h: 5.8
          }
        ];

        const mockTransactions: Transaction[] = [
          {
            id: '1',
            type: 'deposit',
            amount: 1000,
            currency: 'USDT',
            status: 'completed',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            description: 'إيداع USDT',
            txHash: '0x1234...5678',
            fee: 5.00
          },
          {
            id: '2',
            type: 'trade',
            amount: -500,
            currency: 'USDT',
            status: 'completed',
            timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
            description: 'بيع USDT مقابل SAR',
            relatedTradeId: 'trade_123'
          },
          {
            id: '3',
            type: 'withdrawal',
            amount: -200,
            currency: 'SAR',
            status: 'pending',
            timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
            description: 'سحب إلى البنك الأهلي',
            fee: 10.00
          }
        ];

        setBalances(mockBalances);
        setTransactions(mockTransactions);
      } catch (error) {
        console.error('Error fetching wallet data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchWalletData();
  }, []);

  // تصفية المعاملات
  const filteredTransactions = transactions.filter(tx => {
    if (selectedCurrency !== 'all' && tx.currency !== selectedCurrency) return false;
    if (transactionFilter !== 'all' && tx.type !== transactionFilter) return false;
    return true;
  });

  // حساب إجمالي القيمة
  const totalUsdValue = balances.reduce((sum, balance) => sum + balance.usdValue, 0);

  // الحصول على أيقونة نوع المعاملة
  const getTransactionIcon = (type: Transaction['type']) => {
    switch (type) {
      case 'deposit':
        return <ArrowDownLeft className="w-4 h-4 text-green-500" />;
      case 'withdrawal':
        return <ArrowUpRight className="w-4 h-4 text-red-500" />;
      case 'trade':
        return <RefreshCw className="w-4 h-4 text-blue-500" />;
      case 'fee':
        return <Minus className="w-4 h-4 text-orange-500" />;
      case 'refund':
        return <Plus className="w-4 h-4 text-green-500" />;
      default:
        return <RefreshCw className="w-4 h-4 text-gray-500" />;
    }
  };

  // الحصول على لون حالة المعاملة
  const getStatusColor = (status: Transaction['status']) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'pending':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30';
      case 'failed':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      case 'cancelled':
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  // الحصول على نص الحالة
  const getStatusText = (status: Transaction['status']) => {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'pending':
        return 'قيد المعالجة';
      case 'failed':
        return 'فشلت';
      case 'cancelled':
        return 'ملغية';
      default:
        return 'غير معروف';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            المحفظة
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            إدارة أرصدتك ومعاملاتك المالية
          </p>
        </div>

        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <button
            onClick={() => setShowBalances(!showBalances)}
            className="flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
          >
            {showBalances ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            <span className="text-sm">{showBalances ? 'إخفاء' : 'إظهار'} الأرصدة</span>
          </button>
          
          <button
            onClick={() => setShowDepositModal(true)}
            className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>إيداع</span>
          </button>

          <button
            onClick={() => setShowWithdrawModal(true)}
            className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          >
            <Minus className="w-4 h-4" />
            <span>سحب</span>
          </button>
        </div>
      </div>

      {/* إجمالي القيمة */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-blue-100 text-sm">إجمالي القيمة</p>
            <p className="text-3xl font-bold">
              {showBalances ? formatCurrency(totalUsdValue, 'USD') : '****'}
            </p>
          </div>
          <Wallet className="w-12 h-12 text-blue-200" />
        </div>
      </div>

      {/* الأرصدة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {balances.map((balance) => (
          <div key={balance.currency} className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {balance.currency}
              </h3>
              <div className={`flex items-center space-x-1 rtl:space-x-reverse text-sm ${
                balance.change24h >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
              }`}>
                {balance.change24h >= 0 ? (
                  <TrendingUp className="w-3 h-3" />
                ) : (
                  <TrendingDown className="w-3 h-3" />
                )}
                <span>{Math.abs(balance.change24h)}%</span>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">الرصيد المتاح:</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {showBalances ? balance.balance.toLocaleString() : '****'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">الرصيد المحجوز:</span>
                <span className="font-medium text-orange-600 dark:text-orange-400">
                  {showBalances ? balance.lockedBalance.toLocaleString() : '****'}
                </span>
              </div>
              
              <div className="flex justify-between pt-2 border-t border-gray-200 dark:border-gray-600">
                <span className="text-gray-600 dark:text-gray-400">القيمة بالدولار:</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {showBalances ? formatCurrency(balance.usdValue, 'USD') : '****'}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* مودال الإيداع */}
      {showDepositModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">إيداع أموال</h3>
              <button
                onClick={() => setShowDepositModal(false)}
                className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
              >
                ×
              </button>
            </div>
            <div className="text-center py-8">
              <Plus className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                وظيفة الإيداع ستكون متاحة قريباً
              </p>
            </div>
          </div>
        </div>
      )}

      {/* مودال السحب */}
      {showWithdrawModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">سحب أموال</h3>
              <button
                onClick={() => setShowWithdrawModal(false)}
                className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
              >
                ×
              </button>
            </div>
            <div className="text-center py-8">
              <Minus className="w-12 h-12 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                وظيفة السحب ستكون متاحة قريباً
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
