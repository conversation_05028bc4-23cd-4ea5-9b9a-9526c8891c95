'use client';

import React from 'react';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Activity,
  PieChart,
  LineChart,
  Bar<PERSON>hart,
  Download,
  RefreshCw,
  Maximize2,
  Settings
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';

export interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string;
    borderWidth?: number;
    fill?: boolean;
  }>;
}

export interface AdminChartProps {
  type: 'line' | 'bar' | 'pie' | 'doughnut' | 'area';
  data: ChartData;
  title?: string;
  subtitle?: string;
  height?: number;
  loading?: boolean;
  error?: string;
  showLegend?: boolean;
  showGrid?: boolean;
  showTooltip?: boolean;
  responsive?: boolean;
  exportable?: boolean;
  refreshable?: boolean;
  onRefresh?: () => void;
  onExport?: () => void;
  className?: string;
}

export default function AdminChart({
  type,
  data,
  title,
  subtitle,
  height = 300,
  loading = false,
  error,
  showLegend = true,
  showGrid = true,
  showTooltip = true,
  responsive = true,
  exportable = false,
  refreshable = false,
  onRefresh,
  onExport,
  className = ''
}: AdminChartProps) {
  const { t, isRTL } = useAdminTranslation();

  const getChartIcon = () => {
    switch (type) {
      case 'line':
        return LineChart;
      case 'bar':
        return BarChart;
      case 'pie':
      case 'doughnut':
        return PieChart;
      case 'area':
        return Activity;
      default:
        return BarChart3;
    }
  };

  const ChartIcon = getChartIcon();

  // Mock chart rendering - في التطبيق الحقيقي، استخدم مكتبة مثل Chart.js أو Recharts
  const renderChart = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <RefreshCw className="w-8 h-8 animate-spin text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500 dark:text-gray-400">{t('common.loading')}</p>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <TrendingDown className="w-8 h-8 text-red-400 mx-auto mb-2" />
            <p className="text-red-500 dark:text-red-400">{error}</p>
          </div>
        </div>
      );
    }

    if (!data.datasets.length || !data.labels.length) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <ChartIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500 dark:text-gray-400">{t('common.noData')}</p>
          </div>
        </div>
      );
    }

    // Mock chart visualization
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <ChartIcon className="w-16 h-16 text-blue-500 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {type.charAt(0).toUpperCase() + type.slice(1)} Chart
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {data.datasets.length} dataset(s) with {data.labels.length} data points
          </p>
          
          {/* Mock data preview */}
          <div className="mt-4 space-y-2">
            {data.datasets.slice(0, 3).map((dataset, index) => (
              <div key={index} className="flex items-center justify-center gap-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: Array.isArray(dataset.backgroundColor) 
                    ? dataset.backgroundColor[0] 
                    : dataset.backgroundColor || '#3B82F6' }}
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">{dataset.label}</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {dataset.data.reduce((a, b) => a + b, 0).toLocaleString()}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      {(title || subtitle || exportable || refreshable) && (
        <div className={`p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={isRTL ? 'text-right' : 'text-left'}>
            {title && (
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
            )}
            {subtitle && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{subtitle}</p>
            )}
          </div>
          
          <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            {refreshable && (
              <button
                onClick={onRefresh}
                className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                title={t('common.actions.refresh')}
              >
                <RefreshCw className="w-4 h-4" />
              </button>
            )}
            
            {exportable && (
              <button
                onClick={onExport}
                className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                title={t('common.actions.export')}
              >
                <Download className="w-4 h-4" />
              </button>
            )}
            
            <button
              className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="Fullscreen"
            >
              <Maximize2 className="w-4 h-4" />
            </button>
            
            <button
              className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="Settings"
            >
              <Settings className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Chart Content */}
      <div className="p-4">
        <div style={{ height: `${height}px` }} className="relative">
          {renderChart()}
        </div>
        
        {/* Legend */}
        {showLegend && data.datasets.length > 0 && !loading && !error && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <div className={`flex flex-wrap gap-4 ${isRTL ? 'justify-end' : 'justify-start'}`}>
              {data.datasets.map((dataset, index) => (
                <div key={index} className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: Array.isArray(dataset.backgroundColor) 
                      ? dataset.backgroundColor[0] 
                      : dataset.backgroundColor || '#3B82F6' }}
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">{dataset.label}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Chart utility functions
export const createLineChartData = (labels: string[], datasets: Array<{
  label: string;
  data: number[];
  color?: string;
}>): ChartData => ({
  labels,
  datasets: datasets.map(dataset => ({
    label: dataset.label,
    data: dataset.data,
    borderColor: dataset.color || '#3B82F6',
    backgroundColor: dataset.color ? `${dataset.color}20` : '#3B82F620',
    borderWidth: 2,
    fill: false
  }))
});

export const createBarChartData = (labels: string[], datasets: Array<{
  label: string;
  data: number[];
  color?: string;
}>): ChartData => ({
  labels,
  datasets: datasets.map(dataset => ({
    label: dataset.label,
    data: dataset.data,
    backgroundColor: dataset.color || '#3B82F6',
    borderColor: dataset.color || '#3B82F6',
    borderWidth: 1
  }))
});

export const createPieChartData = (labels: string[], data: number[], colors?: string[]): ChartData => ({
  labels,
  datasets: [{
    label: 'Data',
    data,
    backgroundColor: colors || [
      '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
      '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
    ]
  }]
});

export const createAreaChartData = (labels: string[], datasets: Array<{
  label: string;
  data: number[];
  color?: string;
}>): ChartData => ({
  labels,
  datasets: datasets.map(dataset => ({
    label: dataset.label,
    data: dataset.data,
    borderColor: dataset.color || '#3B82F6',
    backgroundColor: dataset.color ? `${dataset.color}30` : '#3B82F630',
    borderWidth: 2,
    fill: true
  }))
});
