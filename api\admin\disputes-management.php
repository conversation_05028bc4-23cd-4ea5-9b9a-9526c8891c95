<?php
/**
 * API إدارة النزاعات المتقدمة للمدراء
 * Advanced Dispute Management API for Admins
 * 
 * يوفر وظائف شاملة لإدارة وحل النزاعات
 * Provides comprehensive functions for dispute management and resolution
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../../config/database.php';

// التحقق من صحة الجلسة والصلاحيات الإدارية
function validateAdminSession() {
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
        http_response_code(403);
        echo json_encode([
            'success' => false,
            'error' => 'غير مصرح لك بالوصول إلى هذا المورد',
            'error_en' => 'Access denied. Admin privileges required.'
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
    return $_SESSION['user_id'];
}

// تسجيل نشاط المدير
function logAdminActivity($connection, $admin_id, $action_type, $target_type, $target_id, $details = null) {
    try {
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        $stmt = $connection->prepare("
            INSERT INTO admin_activity_logs 
            (admin_id, action_type, target_type, target_id, details, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $details_json = $details ? json_encode($details, JSON_UNESCAPED_UNICODE) : null;
        $stmt->execute([$admin_id, $action_type, $target_type, $target_id, $details_json, $ip_address, $user_agent]);
    } catch (Exception $e) {
        error_log("Failed to log admin activity: " . $e->getMessage());
    }
}

// الحصول على النزاعات مع التفاصيل الكاملة
function getDisputes($connection, $status = null) {
    $where_clause = "WHERE t.status = 'disputed'";
    $params = [];
    
    if ($status && $status !== 'all') {
        // يمكن إضافة فلترة إضافية حسب حالة النزاع
        if ($status === 'pending') {
            $where_clause .= " AND t.dispute_resolved_by IS NULL";
        } elseif ($status === 'resolved') {
            $where_clause .= " AND t.dispute_resolved_by IS NOT NULL";
        }
    }
    
    $query = "
        SELECT 
            t.*,
            seller.username as seller_username,
            seller.wallet_address as seller_wallet,
            seller.email as seller_email,
            seller.rating as seller_rating,
            buyer.username as buyer_username,
            buyer.wallet_address as buyer_wallet,
            buyer.email as buyer_email,
            buyer.rating as buyer_rating,
            resolver.username as resolver_username,
            (SELECT COUNT(*) FROM messages WHERE trade_id = t.id) as message_count,
            (SELECT COUNT(*) FROM admin_trade_notes WHERE trade_id = t.id) as notes_count,
            (SELECT COUNT(*) FROM dispute_resolutions WHERE trade_id = t.id) as resolution_count,
            TIMESTAMPDIFF(HOUR, t.dispute_created_at, NOW()) as dispute_age_hours
        FROM trades t
        LEFT JOIN users seller ON t.seller_id = seller.id
        LEFT JOIN users buyer ON t.buyer_id = buyer.id
        LEFT JOIN users resolver ON t.dispute_resolved_by = resolver.id
        $where_clause
        ORDER BY 
            CASE WHEN t.dispute_resolved_by IS NULL THEN 0 ELSE 1 END,
            t.dispute_created_at DESC
    ";
    
    $stmt = $connection->prepare($query);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// الحصول على تفاصيل نزاع محدد
function getDisputeDetails($connection, $trade_id) {
    // معلومات الصفقة والنزاع
    $trade_query = "
        SELECT 
            t.*,
            seller.username as seller_username,
            seller.wallet_address as seller_wallet,
            seller.email as seller_email,
            seller.phone as seller_phone,
            buyer.username as buyer_username,
            buyer.wallet_address as buyer_wallet,
            buyer.email as buyer_email,
            buyer.phone as buyer_phone,
            resolver.username as resolver_username
        FROM trades t
        LEFT JOIN users seller ON t.seller_id = seller.id
        LEFT JOIN users buyer ON t.buyer_id = buyer.id
        LEFT JOIN users resolver ON t.dispute_resolved_by = resolver.id
        WHERE t.id = ? AND t.status = 'disputed'
    ";
    
    $stmt = $connection->prepare($trade_query);
    $stmt->execute([$trade_id]);
    $trade = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$trade) {
        return null;
    }
    
    // الرسائل
    $messages_query = "
        SELECT 
            m.*,
            u.username as sender_username
        FROM messages m
        LEFT JOIN users u ON m.sender_id = u.id
        WHERE m.trade_id = ?
        ORDER BY m.created_at ASC
    ";
    
    $stmt = $connection->prepare($messages_query);
    $stmt->execute([$trade_id]);
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // الملاحظات الإدارية
    $notes_query = "
        SELECT 
            atn.*,
            u.username as admin_username
        FROM admin_trade_notes atn
        LEFT JOIN users u ON atn.admin_id = u.id
        WHERE atn.trade_id = ?
        ORDER BY atn.created_at DESC
    ";
    
    $stmt = $connection->prepare($notes_query);
    $stmt->execute([$trade_id]);
    $notes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // قرارات النزاعات السابقة
    $resolutions_query = "
        SELECT 
            dr.*,
            u.username as admin_username
        FROM dispute_resolutions dr
        LEFT JOIN users u ON dr.admin_id = u.id
        WHERE dr.trade_id = ?
        ORDER BY dr.created_at DESC
    ";
    
    $stmt = $connection->prepare($resolutions_query);
    $stmt->execute([$trade_id]);
    $resolutions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'trade' => $trade,
        'messages' => $messages,
        'admin_notes' => $notes,
        'resolutions' => $resolutions
    ];
}

// حل النزاع
function resolveDispute($connection, $trade_id, $admin_id, $resolution_type, $admin_notes, $resolution_amount = 0) {
    try {
        $connection->beginTransaction();
        
        // التحقق من وجود النزاع
        $stmt = $connection->prepare("SELECT * FROM trades WHERE id = ? AND status = 'disputed'");
        $stmt->execute([$trade_id]);
        $trade = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$trade) {
            throw new Exception('النزاع غير موجود أو تم حله مسبقاً');
        }
        
        // تحديث حالة الصفقة
        $new_status = 'completed'; // يمكن تخصيص هذا حسب نوع القرار
        if ($resolution_type === 'cancelled') {
            $new_status = 'cancelled';
        }
        
        $stmt = $connection->prepare("
            UPDATE trades 
            SET status = ?, 
                dispute_resolved_by = ?, 
                dispute_resolution = ?, 
                dispute_resolved_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        $stmt->execute([$new_status, $admin_id, $admin_notes, $trade_id]);
        
        // إضافة قرار النزاع
        $stmt = $connection->prepare("
            INSERT INTO dispute_resolutions 
            (trade_id, admin_id, resolution_type, admin_notes, resolution_amount) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$trade_id, $admin_id, $resolution_type, $admin_notes, $resolution_amount]);
        
        // إضافة ملاحظة إدارية
        $stmt = $connection->prepare("
            INSERT INTO admin_trade_notes 
            (trade_id, admin_id, note, is_internal, visibility) 
            VALUES (?, ?, ?, FALSE, 'parties_only')
        ");
        $note_text = "تم حل النزاع: " . $admin_notes;
        $stmt->execute([$trade_id, $admin_id, $note_text]);
        
        // تسجيل النشاط
        logAdminActivity($connection, $admin_id, 'dispute_resolution', 'trade', $trade_id, [
            'resolution_type' => $resolution_type,
            'resolution_amount' => $resolution_amount,
            'notes_length' => strlen($admin_notes)
        ]);
        
        $connection->commit();
        return true;
        
    } catch (Exception $e) {
        $connection->rollBack();
        throw $e;
    }
}

// تصعيد النزاع
function escalateDispute($connection, $trade_id, $admin_id, $escalation_reason, $assigned_admin_id = null) {
    try {
        // إضافة ملاحظة تصعيد
        $stmt = $connection->prepare("
            INSERT INTO admin_trade_notes 
            (trade_id, admin_id, note, is_internal, visibility) 
            VALUES (?, ?, ?, TRUE, 'internal')
        ");
        $note_text = "تم تصعيد النزاع: " . $escalation_reason;
        $stmt->execute([$trade_id, $admin_id, $note_text]);
        
        // تسجيل النشاط
        logAdminActivity($connection, $admin_id, 'dispute_escalation', 'trade', $trade_id, [
            'escalation_reason' => $escalation_reason,
            'assigned_admin_id' => $assigned_admin_id
        ]);
        
        return true;
        
    } catch (Exception $e) {
        throw $e;
    }
}

// الحصول على إحصائيات النزاعات
function getDisputeStatistics($connection, $date_range = null) {
    $where_clause = "WHERE status = 'disputed' OR dispute_resolved_by IS NOT NULL";
    $params = [];
    
    if ($date_range) {
        $where_clause .= " AND dispute_created_at BETWEEN ? AND ?";
        $params = [$date_range['start'], $date_range['end']];
    }
    
    $query = "
        SELECT 
            COUNT(*) as total_disputes,
            COUNT(CASE WHEN dispute_resolved_by IS NULL THEN 1 END) as pending_disputes,
            COUNT(CASE WHEN dispute_resolved_by IS NOT NULL THEN 1 END) as resolved_disputes,
            AVG(CASE WHEN dispute_resolved_by IS NOT NULL 
                THEN TIMESTAMPDIFF(HOUR, dispute_created_at, dispute_resolved_at) END) as avg_resolution_time,
            (COUNT(CASE WHEN dispute_resolved_by IS NOT NULL THEN 1 END) * 100.0 / COUNT(*)) as resolution_rate
        FROM trades 
        $where_clause
    ";
    
    $stmt = $connection->prepare($query);
    $stmt->execute($params);
    $basic_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إحصائيات أنواع القرارات
    $resolution_query = "
        SELECT 
            resolution_type,
            COUNT(*) as count
        FROM dispute_resolutions dr
        JOIN trades t ON dr.trade_id = t.id
        " . ($date_range ? "WHERE dr.created_at BETWEEN ? AND ?" : "") . "
        GROUP BY resolution_type
    ";
    
    $stmt = $connection->prepare($resolution_query);
    $stmt->execute($date_range ? [$date_range['start'], $date_range['end']] : []);
    $resolution_types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return array_merge($basic_stats, ['resolution_types' => $resolution_types]);
}

// معالجة الطلبات
try {
    session_start();
    $admin_id = validateAdminSession();

    // Get database connection
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();

    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['action']) && $_GET['action'] === 'details' && isset($_GET['trade_id'])) {
                $details = getDisputeDetails($connection, $_GET['trade_id']);
                if (!$details) {
                    http_response_code(404);
                    echo json_encode([
                        'success' => false,
                        'error' => 'النزاع غير موجود',
                        'error_en' => 'Dispute not found'
                    ], JSON_UNESCAPED_UNICODE);
                    break;
                }
                
                echo json_encode([
                    'success' => true,
                    'data' => $details
                ], JSON_UNESCAPED_UNICODE);
                break;
            }
            
            if (isset($_GET['action']) && $_GET['action'] === 'statistics') {
                $date_range = null;
                if (isset($_GET['date_from']) && isset($_GET['date_to'])) {
                    $date_range = [
                        'start' => $_GET['date_from'],
                        'end' => $_GET['date_to']
                    ];
                }
                
                $stats = getDisputeStatistics($connection, $date_range);
                echo json_encode([
                    'success' => true,
                    'data' => $stats
                ], JSON_UNESCAPED_UNICODE);
                break;
            }
            
            // الحصول على قائمة النزاعات
            $status = $_GET['status'] ?? 'all';
            $disputes = getDisputes($connection, $status);
            
            echo json_encode([
                'success' => true,
                'data' => $disputes
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (isset($input['action']) && $input['action'] === 'resolve') {
                if (!isset($input['trade_id']) || !isset($input['resolution_type']) || !isset($input['admin_notes'])) {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'error' => 'معرف الصفقة ونوع القرار والملاحظات مطلوبة',
                        'error_en' => 'Trade ID, resolution type, and admin notes are required'
                    ], JSON_UNESCAPED_UNICODE);
                    break;
                }
                
                resolveDispute(
                    $connection,
                    $input['trade_id'],
                    $admin_id,
                    $input['resolution_type'],
                    $input['admin_notes'],
                    $input['resolution_amount'] ?? 0
                );
                
                echo json_encode([
                    'success' => true,
                    'message' => 'تم حل النزاع بنجاح',
                    'message_en' => 'Dispute resolved successfully'
                ], JSON_UNESCAPED_UNICODE);
                break;
            }
            
            if (isset($input['action']) && $input['action'] === 'escalate') {
                if (!isset($input['trade_id']) || !isset($input['escalation_reason'])) {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'error' => 'معرف الصفقة وسبب التصعيد مطلوبان',
                        'error_en' => 'Trade ID and escalation reason are required'
                    ], JSON_UNESCAPED_UNICODE);
                    break;
                }
                
                escalateDispute(
                    $connection,
                    $input['trade_id'],
                    $admin_id,
                    $input['escalation_reason'],
                    $input['assigned_admin_id'] ?? null
                );
                
                echo json_encode([
                    'success' => true,
                    'message' => 'تم تصعيد النزاع بنجاح',
                    'message_en' => 'Dispute escalated successfully'
                ], JSON_UNESCAPED_UNICODE);
                break;
            }
            
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'إجراء غير صحيح',
                'error_en' => 'Invalid action'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'error' => 'طريقة الطلب غير مدعومة',
                'error_en' => 'Method not allowed'
            ], JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في الخادم: ' . $e->getMessage(),
        'error_en' => 'Server error: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
