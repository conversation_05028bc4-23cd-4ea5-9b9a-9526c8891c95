/**
 * نظام تحسين الأداء الشامل
 * Comprehensive Performance Optimization System
 */

import { apiCache, userDataCache, staticDataCache } from './cacheManager';

// تحسين الصور
export class ImageOptimizer {
  private static readonly SUPPORTED_FORMATS = ['webp', 'avif', 'jpg', 'png'];
  private static readonly DEFAULT_QUALITY = 80;
  
  /**
   * تحسين رابط الصورة
   */
  static optimizeImageUrl(
    src: string, 
    width?: number, 
    height?: number, 
    quality: number = this.DEFAULT_QUALITY,
    format?: string
  ): string {
    if (!src) return '';
    
    // إذا كانت الصورة محلية، استخدم Next.js Image Optimization
    if (src.startsWith('/') || src.startsWith('./')) {
      const params = new URLSearchParams();
      if (width) params.set('w', width.toString());
      if (height) params.set('h', height.toString());
      if (quality !== this.DEFAULT_QUALITY) params.set('q', quality.toString());
      
      return `/_next/image?url=${encodeURIComponent(src)}&${params.toString()}`;
    }
    
    return src;
  }
  
  /**
   * تحديد أفضل تنسيق للصورة
   */
  static getBestFormat(): string {
    if (typeof window === 'undefined') return 'jpg';
    
    // فحص دعم WebP
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    
    try {
      const webpSupported = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
      if (webpSupported) return 'webp';
    } catch (e) {
      // تجاهل الخطأ
    }
    
    return 'jpg';
  }
  
  /**
   * تحميل الصورة مع Lazy Loading
   */
  static createLazyImage(src: string, alt: string, className?: string): HTMLImageElement {
    const img = document.createElement('img');
    img.alt = alt;
    img.className = className || '';
    img.loading = 'lazy';
    img.decoding = 'async';
    
    // استخدام Intersection Observer للتحميل التدريجي
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          img.src = src;
          observer.unobserve(img);
        }
      });
    });
    
    observer.observe(img);
    return img;
  }
}

// تحسين الشبكة
export class NetworkOptimizer {
  private static requestQueue = new Map<string, Promise<any>>();
  private static readonly MAX_CONCURRENT_REQUESTS = 6;
  private static activeRequests = 0;
  
  /**
   * طلب HTTP محسن
   */
  static async optimizedFetch(
    url: string,
    options: RequestInit = {},
    cacheKey?: string,
    cacheTTL?: number
  ): Promise<any> {
    // فحص الكاش أولاً
    if (cacheKey) {
      const cached = apiCache.get(cacheKey);
      if (cached) return cached;
    }
    
    // تجنب الطلبات المكررة
    if (this.requestQueue.has(url)) {
      return this.requestQueue.get(url);
    }
    
    // انتظار إذا كان هناك طلبات كثيرة
    await this.waitForSlot();
    
    const requestPromise = this.executeRequest(url, options);
    this.requestQueue.set(url, requestPromise);
    
    try {
      const result = await requestPromise;
      
      // حفظ في الكاش
      if (cacheKey && result) {
        apiCache.set(cacheKey, result, cacheTTL);
      }
      
      return result;
    } finally {
      this.requestQueue.delete(url);
      this.activeRequests--;
    }
  }
  
  /**
   * انتظار توفر مكان للطلب
   */
  private static async waitForSlot(): Promise<void> {
    while (this.activeRequests >= this.MAX_CONCURRENT_REQUESTS) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    this.activeRequests++;
  }
  
  /**
   * تنفيذ الطلب
   */
  private static async executeRequest(url: string, options: RequestInit): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000);
    
    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }
  
  /**
   * تحميل الموارد مسبقاً
   */
  static preloadResource(url: string, type: 'script' | 'style' | 'image' | 'fetch' = 'fetch'): void {
    if (typeof window === 'undefined') return;
    
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = url;
    
    switch (type) {
      case 'script':
        link.as = 'script';
        break;
      case 'style':
        link.as = 'style';
        break;
      case 'image':
        link.as = 'image';
        break;
      case 'fetch':
        link.as = 'fetch';
        link.crossOrigin = 'anonymous';
        break;
    }
    
    document.head.appendChild(link);
  }
}

// تحسين الذاكرة
export class MemoryOptimizer {
  private static observers = new Set<IntersectionObserver>();
  private static timers = new Set<NodeJS.Timeout>();
  
  /**
   * تنظيف الموارد
   */
  static cleanup(): void {
    // تنظيف المراقبين
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    
    // تنظيف المؤقتات
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();
    
    // تنظيف الكاش
    if (performance.memory && (performance.memory as any).usedJSHeapSize > 50 * 1024 * 1024) {
      apiCache.clear();
      userDataCache.clear();
    }
  }
  
  /**
   * مراقبة استخدام الذاكرة
   */
  static monitorMemory(): void {
    if (typeof window === 'undefined' || !performance.memory) return;
    
    const timer = setInterval(() => {
      const memory = performance.memory as any;
      const usedMB = memory.usedJSHeapSize / 1024 / 1024;
      const limitMB = memory.jsHeapSizeLimit / 1024 / 1024;
      
      // إذا تجاوز الاستخدام 80% من الحد الأقصى
      if (usedMB > limitMB * 0.8) {
        console.warn('High memory usage detected, cleaning up...');
        this.cleanup();
      }
    }, 30000); // فحص كل 30 ثانية
    
    this.timers.add(timer);
  }
  
  /**
   * إنشاء Intersection Observer محسن
   */
  static createOptimizedObserver(
    callback: IntersectionObserverCallback,
    options?: IntersectionObserverInit
  ): IntersectionObserver {
    const observer = new IntersectionObserver(callback, {
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    });
    
    this.observers.add(observer);
    return observer;
  }
}

// تحسين الأداء العام
export class PerformanceOptimizer {
  private static metrics = {
    apiCalls: 0,
    cacheHits: 0,
    cacheMisses: 0,
    renderTime: 0,
    loadTime: 0
  };
  
  /**
   * قياس أداء دالة
   */
  static async measurePerformance<T>(
    name: string,
    fn: () => Promise<T> | T
  ): Promise<T> {
    const start = performance.now();
    
    try {
      const result = await fn();
      const end = performance.now();
      
      console.log(`${name} took ${end - start} milliseconds`);
      return result;
    } catch (error) {
      const end = performance.now();
      console.error(`${name} failed after ${end - start} milliseconds:`, error);
      throw error;
    }
  }
  
  /**
   * تحسين الرندر
   */
  static optimizeRender(component: React.ComponentType<any>) {
    return React.memo(component, (prevProps, nextProps) => {
      // مقارنة سطحية محسنة
      const prevKeys = Object.keys(prevProps);
      const nextKeys = Object.keys(nextProps);
      
      if (prevKeys.length !== nextKeys.length) return false;
      
      for (const key of prevKeys) {
        if (prevProps[key] !== nextProps[key]) return false;
      }
      
      return true;
    });
  }
  
  /**
   * تحسين useEffect
   */
  static useOptimizedEffect(
    effect: React.EffectCallback,
    deps?: React.DependencyList,
    debounceMs?: number
  ): void {
    const debouncedEffect = React.useCallback(
      debounceMs ? this.debounce(effect, debounceMs) : effect,
      deps || []
    );
    
    React.useEffect(debouncedEffect, deps);
  }
  
  /**
   * Debounce function
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
      MemoryOptimizer.timers.add(timeout);
    };
  }
  
  /**
   * Throttle function
   */
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        const timer = setTimeout(() => inThrottle = false, limit);
        MemoryOptimizer.timers.add(timer);
      }
    };
  }
  
  /**
   * الحصول على إحصائيات الأداء
   */
  static getMetrics() {
    return {
      ...this.metrics,
      cacheStats: {
        api: apiCache.getStats(),
        userData: userDataCache.getStats(),
        staticData: staticDataCache.getStats()
      },
      memoryUsage: typeof window !== 'undefined' && performance.memory 
        ? {
            used: Math.round((performance.memory as any).usedJSHeapSize / 1024 / 1024),
            total: Math.round((performance.memory as any).totalJSHeapSize / 1024 / 1024),
            limit: Math.round((performance.memory as any).jsHeapSizeLimit / 1024 / 1024)
          }
        : null
    };
  }
  
  /**
   * تهيئة تحسينات الأداء
   */
  static initialize(): void {
    if (typeof window === 'undefined') return;
    
    // بدء مراقبة الذاكرة
    MemoryOptimizer.monitorMemory();
    
    // تحميل الموارد الحرجة مسبقاً
    NetworkOptimizer.preloadResource('/api/users/profile', 'fetch');
    NetworkOptimizer.preloadResource('/api/trades', 'fetch');
    
    // تنظيف عند إغلاق الصفحة
    window.addEventListener('beforeunload', () => {
      MemoryOptimizer.cleanup();
    });
    
    console.log('Performance optimization initialized');
  }
}

// تصدير جميع المحسنات
export default {
  ImageOptimizer,
  NetworkOptimizer,
  MemoryOptimizer,
  PerformanceOptimizer
};
