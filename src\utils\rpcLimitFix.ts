/**
 * حل مشكلة تجاوز حد طلبات RPC
 * RPC Limit Exceeded Fix
 */

import { ethers } from 'ethers';

// إعدادات RPC محسنة
export const RPC_CONFIG = {
  MAX_BLOCKS_PER_REQUEST: 100, // تقليل عدد البلوكات في الطلب الواحد
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 ثانية
  REQUEST_TIMEOUT: 10000, // 10 ثواني
  RATE_LIMIT_DELAY: 500, // 0.5 ثانية بين الطلبات
};

// قائمة RPC providers للـ BSC Testnet
export const BSC_TESTNET_RPCS = [
  'https://data-seed-prebsc-1-s1.binance.org:8545/',
  'https://data-seed-prebsc-2-s1.binance.org:8545/',
  'https://data-seed-prebsc-1-s2.binance.org:8545/',
  'https://data-seed-prebsc-2-s2.binance.org:8545/',
  'https://data-seed-prebsc-1-s3.binance.org:8545/',
];

/**
 * مدير RPC متعدد المصادر
 */
export class MultiRPCManager {
  private providers: ethers.JsonRpcProvider[] = [];
  private currentProviderIndex = 0;
  private failedProviders = new Set<number>();

  constructor(rpcUrls: string[] = BSC_TESTNET_RPCS) {
    this.providers = rpcUrls.map(url => new ethers.JsonRpcProvider(url, {
      chainId: 97,
      name: 'BSC Testnet'
    }));
  }

  /**
   * الحصول على provider نشط
   */
  getActiveProvider(): ethers.JsonRpcProvider {
    // إذا فشل جميع المزودين، أعد تعيينهم
    if (this.failedProviders.size >= this.providers.length) {
      this.failedProviders.clear();
      this.currentProviderIndex = 0;
    }

    // البحث عن provider يعمل
    while (this.failedProviders.has(this.currentProviderIndex)) {
      this.currentProviderIndex = (this.currentProviderIndex + 1) % this.providers.length;
    }

    return this.providers[this.currentProviderIndex];
  }

  /**
   * تسجيل فشل provider
   */
  markProviderAsFailed(provider: ethers.JsonRpcProvider): void {
    const index = this.providers.indexOf(provider);
    if (index !== -1) {
      this.failedProviders.add(index);
      console.warn(`🚨 RPC Provider ${index} failed, switching to next`);
    }
  }

  /**
   * التبديل إلى provider التالي
   */
  switchToNextProvider(): ethers.JsonRpcProvider {
    this.currentProviderIndex = (this.currentProviderIndex + 1) % this.providers.length;
    return this.getActiveProvider();
  }
}

/**
 * طلب محسن مع إعادة المحاولة
 */
export async function optimizedRequest<T>(
  requestFn: () => Promise<T>,
  maxRetries: number = RPC_CONFIG.RETRY_ATTEMPTS
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // تأخير بين المحاولات
      if (attempt > 1) {
        await new Promise(resolve => setTimeout(resolve, RPC_CONFIG.RETRY_DELAY * attempt));
      }

      const result = await Promise.race([
        requestFn(),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), RPC_CONFIG.REQUEST_TIMEOUT)
        )
      ]);

      return result;
    } catch (error: any) {
      lastError = error;
      
      // إذا كان خطأ حد RPC، انتظر أكثر
      if (error.message?.includes('limit exceeded') || error.code === -32005) {
        console.warn(`🚨 RPC limit exceeded, attempt ${attempt}/${maxRetries}`);
        await new Promise(resolve => setTimeout(resolve, RPC_CONFIG.RETRY_DELAY * 2));
      }
      
      // إذا كانت آخر محاولة، ارمي الخطأ
      if (attempt === maxRetries) {
        throw lastError;
      }
    }
  }

  throw lastError!;
}

/**
 * جلب السجلات مع تقسيم البلوكات
 */
export async function getLogsWithChunking(
  provider: ethers.JsonRpcProvider,
  filter: {
    address: string;
    fromBlock: number;
    toBlock: number;
    topics?: string[];
  }
): Promise<ethers.Log[]> {
  const { address, fromBlock, toBlock, topics } = filter;
  const totalBlocks = toBlock - fromBlock + 1;
  
  // إذا كان عدد البلوكات صغير، اطلب مباشرة
  if (totalBlocks <= RPC_CONFIG.MAX_BLOCKS_PER_REQUEST) {
    return optimizedRequest(() => provider.getLogs({
      address,
      fromBlock,
      toBlock,
      topics
    }));
  }

  // تقسيم إلى chunks
  const allLogs: ethers.Log[] = [];
  const chunkSize = RPC_CONFIG.MAX_BLOCKS_PER_REQUEST;
  
  for (let start = fromBlock; start <= toBlock; start += chunkSize) {
    const end = Math.min(start + chunkSize - 1, toBlock);
    
    console.log(`📊 Fetching logs from block ${start} to ${end}`);
    
    try {
      const logs = await optimizedRequest(() => provider.getLogs({
        address,
        fromBlock: start,
        toBlock: end,
        topics
      }));
      
      allLogs.push(...logs);
      
      // تأخير بين الطلبات لتجنب Rate Limiting
      await new Promise(resolve => setTimeout(resolve, RPC_CONFIG.RATE_LIMIT_DELAY));
      
    } catch (error: any) {
      console.error(`❌ Failed to fetch logs for blocks ${start}-${end}:`, error.message);
      
      // إذا كان خطأ حد RPC، تخطى هذا الجزء وتابع
      if (error.message?.includes('limit exceeded')) {
        console.warn(`⏭️ Skipping blocks ${start}-${end} due to RPC limit`);
        continue;
      }
      
      throw error;
    }
  }

  return allLogs;
}

/**
 * مدير العقد الذكي المحسن
 */
export class OptimizedContractManager {
  private rpcManager: MultiRPCManager;
  private contract: ethers.Contract | null = null;

  constructor(contractAddress: string, abi: any[]) {
    this.rpcManager = new MultiRPCManager();
    this.initContract(contractAddress, abi);
  }

  private initContract(address: string, abi: any[]): void {
    try {
      const provider = this.rpcManager.getActiveProvider();
      this.contract = new ethers.Contract(address, abi, provider);
    } catch (error) {
      console.error('❌ Failed to initialize contract:', error);
    }
  }

  /**
   * جلب الأحداث مع تحسينات
   */
  async getEvents(
    eventName: string,
    fromBlock: number,
    toBlock: number
  ): Promise<ethers.Log[]> {
    if (!this.contract) {
      throw new Error('Contract not initialized');
    }

    const filter = this.contract.filters[eventName]();
    
    return optimizedRequest(async () => {
      const provider = this.rpcManager.getActiveProvider();
      
      try {
        return await getLogsWithChunking(provider, {
          address: this.contract!.target as string,
          fromBlock,
          toBlock,
          topics: filter.topics
        });
      } catch (error: any) {
        // إذا فشل المزود الحالي، جرب التالي
        if (error.message?.includes('limit exceeded') || error.code === -32005) {
          this.rpcManager.markProviderAsFailed(provider);
          const newProvider = this.rpcManager.switchToNextProvider();
          this.contract = this.contract!.connect(newProvider);
          
          // أعد المحاولة مع المزود الجديد
          return await getLogsWithChunking(newProvider, {
            address: this.contract.target as string,
            fromBlock,
            toBlock,
            topics: filter.topics
          });
        }
        
        throw error;
      }
    });
  }

  /**
   * استدعاء دالة في العقد
   */
  async callFunction(functionName: string, ...args: any[]): Promise<any> {
    if (!this.contract) {
      throw new Error('Contract not initialized');
    }

    return optimizedRequest(async () => {
      try {
        return await this.contract![functionName](...args);
      } catch (error: any) {
        // إذا فشل المزود، جرب التالي
        if (error.message?.includes('limit exceeded')) {
          const provider = this.rpcManager.switchToNextProvider();
          this.contract = this.contract!.connect(provider);
          return await this.contract[functionName](...args);
        }
        
        throw error;
      }
    });
  }
}

/**
 * اختبار سرعة RPC providers
 */
export async function testRPCProviders(rpcUrls: string[] = BSC_TESTNET_RPCS): Promise<void> {
  console.log('🧪 Testing RPC providers...');
  
  for (let i = 0; i < rpcUrls.length; i++) {
    const url = rpcUrls[i];
    const provider = new ethers.JsonRpcProvider(url);
    
    try {
      const start = Date.now();
      await provider.getBlockNumber();
      const duration = Date.now() - start;
      
      console.log(`✅ RPC ${i + 1}: ${duration}ms - ${url}`);
    } catch (error: any) {
      console.log(`❌ RPC ${i + 1}: Failed - ${url} - ${error.message}`);
    }
  }
}

/**
 * تهيئة النظام المحسن
 */
export function initializeRPCOptimizations(): void {
  console.log('🚀 Initializing RPC optimizations...');
  
  // اختبار المزودين
  testRPCProviders().catch(console.error);
  
  // إعداد معالج الأخطاء العام
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason?.message?.includes('limit exceeded')) {
      console.warn('🚨 RPC limit exceeded detected, applying optimizations...');
      event.preventDefault(); // منع إظهار الخطأ في الكونسول
    }
  });
  
  console.log('✅ RPC optimizations initialized');
}
