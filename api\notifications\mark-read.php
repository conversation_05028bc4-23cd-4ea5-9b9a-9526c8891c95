<?php
/**
 * API endpoint لتحديد الإشعارات كمقروءة
 * Mark Notifications as Read API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'PUT') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $notificationId = $input['notification_id'] ?? null;
        $notificationIds = $input['notification_ids'] ?? [];
        $userId = $input['user_id'] ?? null;
        $markAsRead = $input['mark_as_read'] ?? true;
        
        // إذا تم توفير معرف إشعار واحد، أضفه للمصفوفة
        if ($notificationId) {
            $notificationIds[] = $notificationId;
        }
        
        if (empty($notificationIds) && !$userId) {
            throw new Exception('معرف الإشعار أو معرف المستخدم مطلوب');
        }
        
        $affectedRows = 0;
        
        if (!empty($notificationIds)) {
            // تحديد إشعارات محددة
            $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';
            
            if ($userId) {
                // تحديد إشعارات محددة لمستخدم محدد
                $stmt = $connection->prepare("
                    UPDATE notifications 
                    SET is_read = ?, updated_at = CURRENT_TIMESTAMP 
                    WHERE id IN ($placeholders) AND user_id = ?
                ");
                $params = [$markAsRead ? 1 : 0];
                $params = array_merge($params, $notificationIds);
                $params[] = $userId;
            } else {
                // تحديد إشعارات محددة لأي مستخدم
                $stmt = $connection->prepare("
                    UPDATE notifications 
                    SET is_read = ?, updated_at = CURRENT_TIMESTAMP 
                    WHERE id IN ($placeholders)
                ");
                $params = [$markAsRead ? 1 : 0];
                $params = array_merge($params, $notificationIds);
            }
            
            $stmt->execute($params);
            $affectedRows = $stmt->rowCount();
            
        } elseif ($userId) {
            // تحديد جميع إشعارات المستخدم
            $stmt = $connection->prepare("
                UPDATE notifications 
                SET is_read = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE user_id = ?
            ");
            $stmt->execute([$markAsRead ? 1 : 0, $userId]);
            $affectedRows = $stmt->rowCount();
        }
        
        $action = $markAsRead ? 'قراءة' : 'عدم قراءة';
        $message = "تم تحديد {$affectedRows} إشعار كـ{$action}";
        
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => [
                'affected_rows' => $affectedRows,
                'mark_as_read' => $markAsRead
            ]
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    error_log('Database error in notifications/mark-read.php: ' . $e->getMessage());
}
?>
