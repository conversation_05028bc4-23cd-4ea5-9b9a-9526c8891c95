<?php
/**
 * API endpoint لإعدادات المنصة
 * Platform Settings API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // جلب إعدادات المنصة
        $stmt = $connection->prepare("
            SELECT setting_key, setting_value, setting_type, description
            FROM platform_settings
            WHERE is_active = 1
            ORDER BY setting_key
        ");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تنظيم الإعدادات
        $formattedSettings = [];
        foreach ($settings as $setting) {
            $value = $setting['setting_value'];
            
            // تحويل القيم حسب النوع
            switch ($setting['setting_type']) {
                case 'boolean':
                    $value = (bool)$value;
                    break;
                case 'integer':
                    $value = (int)$value;
                    break;
                case 'float':
                    $value = (float)$value;
                    break;
                case 'json':
                    $value = json_decode($value, true);
                    break;
                default:
                    // string - لا حاجة لتحويل
                    break;
            }
            
            $formattedSettings[$setting['setting_key']] = [
                'value' => $value,
                'type' => $setting['setting_type'],
                'description' => $setting['description']
            ];
        }
        
        // إذا لم توجد إعدادات، أرجع إعدادات افتراضية
        if (empty($formattedSettings)) {
            $formattedSettings = getDefaultSettings();
        }
        
        echo json_encode([
            'success' => true,
            'data' => $formattedSettings
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'PUT') {
        // تحديث إعدادات المنصة
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        // التحقق من صلاحيات المدير (اختياري للتطوير)
        $skipAuth = $input['skip_auth'] ?? false;
        $userId = $input['user_id'] ?? null;
        
        if (!$skipAuth && $userId) {
            $stmt = $connection->prepare("SELECT is_admin FROM users WHERE id = ? AND is_active = 1");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user || !$user['is_admin']) {
                throw new Exception('غير مصرح لك بتعديل الإعدادات');
            }
        }
        
        $settings = $input['settings'] ?? $input;
        $updatedCount = 0;
        
        foreach ($settings as $key => $data) {
            if ($key === 'skip_auth' || $key === 'user_id') continue;
            
            $value = $data;
            $type = 'string';
            
            // إذا كانت البيانات في شكل مصفوفة مع نوع
            if (is_array($data) && isset($data['value'])) {
                $value = $data['value'];
                $type = $data['type'] ?? 'string';
            }
            
            // تحويل القيمة للنص للحفظ
            if ($type === 'json') {
                $value = json_encode($value);
            } elseif ($type === 'boolean') {
                $value = $value ? '1' : '0';
            } else {
                $value = (string)$value;
            }
            
            // تحديث أو إدراج الإعداد
            $stmt = $connection->prepare("
                INSERT INTO platform_settings (setting_key, setting_value, setting_type, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                ON DUPLICATE KEY UPDATE
                setting_value = VALUES(setting_value),
                setting_type = VALUES(setting_type),
                updated_at = CURRENT_TIMESTAMP
            ");
            
            $stmt->execute([$key, $value, $type]);
            $updatedCount++;
        }
        
        echo json_encode([
            'success' => true,
            'message' => "تم تحديث {$updatedCount} إعداد بنجاح"
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    error_log('Database error in admin/settings.php: ' . $e->getMessage());
}

/**
 * الحصول على الإعدادات الافتراضية
 */
function getDefaultSettings() {
    return [
        'platformFee' => [
            'value' => 1.5,
            'type' => 'float',
            'description' => 'رسوم المنصة بالنسبة المئوية'
        ],
        'minTradeAmount' => [
            'value' => 10,
            'type' => 'integer',
            'description' => 'أقل مبلغ للصفقة'
        ],
        'maxTradeAmount' => [
            'value' => 50000,
            'type' => 'integer',
            'description' => 'أكبر مبلغ للصفقة'
        ],
        'defaultTradeTimeout' => [
            'value' => 1800,
            'type' => 'integer',
            'description' => 'مهلة الصفقة الافتراضية بالثواني'
        ],
        'maintenanceMode' => [
            'value' => false,
            'type' => 'boolean',
            'description' => 'وضع الصيانة'
        ],
        'registrationEnabled' => [
            'value' => true,
            'type' => 'boolean',
            'description' => 'تفعيل التسجيل الجديد'
        ],
        'supportedCurrencies' => [
            'value' => ['SAR', 'AED', 'KWD', 'QAR', 'USD', 'EUR'],
            'type' => 'json',
            'description' => 'العملات المدعومة'
        ]
    ];
}
?>
