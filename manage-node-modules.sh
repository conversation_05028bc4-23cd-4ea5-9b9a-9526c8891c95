#!/bin/bash

# إعداد الألوان
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "========================================"
echo "    إدارة مجلد node_modules"
echo "    Node Modules Management"
echo "========================================"
echo -e "${NC}"

show_menu() {
    echo -e "${YELLOW}اختر العملية المطلوبة:${NC}"
    echo -e "${YELLOW}Choose operation:${NC}"
    echo
    echo -e "${GREEN}[1]${NC} حذف node_modules (Delete node_modules)"
    echo -e "${GREEN}[2]${NC} نقل إلى مجلد خارجي (Move to external folder)"
    echo -e "${GREEN}[3]${NC} إنشاء رابط رمزي (Create symbolic link)"
    echo -e "${GREEN}[4]${NC} استعادة من المجلد الخارجي (Restore from external)"
    echo -e "${GREEN}[5]${NC} تثبيت التبعيات (Install dependencies)"
    echo -e "${GREEN}[6]${NC} عرض حجم المجلد (Show folder size)"
    echo -e "${GREEN}[0]${NC} خروج (Exit)"
    echo
}

delete_modules() {
    echo
    echo -e "${YELLOW}جاري حذف node_modules...${NC}"
    echo -e "${YELLOW}Deleting node_modules...${NC}"
    
    if [ -d "node_modules" ]; then
        rm -rf node_modules
        echo -e "${GREEN}✅ تم حذف node_modules بنجاح${NC}"
        echo -e "${GREEN}✅ node_modules deleted successfully${NC}"
    else
        echo -e "${YELLOW}⚠️ مجلد node_modules غير موجود${NC}"
        echo -e "${YELLOW}⚠️ node_modules folder not found${NC}"
    fi
    
    read -p "اضغط Enter للمتابعة (Press Enter to continue)..."
}

move_modules() {
    echo
    echo -e "${YELLOW}جاري نقل node_modules إلى مجلد خارجي...${NC}"
    echo -e "${YELLOW}Moving node_modules to external folder...${NC}"
    
    if [ ! -d "node_modules" ]; then
        echo -e "${RED}❌ مجلد node_modules غير موجود${NC}"
        echo -e "${RED}❌ node_modules folder not found${NC}"
        read -p "اضغط Enter للمتابعة (Press Enter to continue)..."
        return
    fi
    
    mkdir -p "/d/node_modules_storage"

    if [ -d "/d/node_modules_storage/ikaros-p2p-node_modules" ]; then
        echo -e "${YELLOW}⚠️ المجلد الخارجي موجود مسبقاً، جاري الاستبدال...${NC}"
        echo -e "${YELLOW}⚠️ External folder exists, replacing...${NC}"
        rm -rf "/d/node_modules_storage/ikaros-p2p-node_modules"
    fi

    mv node_modules "/d/node_modules_storage/ikaros-p2p-node_modules"
    echo -e "${GREEN}✅ تم نقل node_modules بنجاح${NC}"
    echo -e "${GREEN}✅ node_modules moved successfully${NC}"
    
    read -p "اضغط Enter للمتابعة (Press Enter to continue)..."
}

create_link() {
    echo
    echo -e "${YELLOW}جاري إنشاء رابط رمزي...${NC}"
    echo -e "${YELLOW}Creating symbolic link...${NC}"
    
    if [ -d "node_modules" ]; then
        echo -e "${RED}❌ مجلد node_modules موجود، احذفه أولاً${NC}"
        echo -e "${RED}❌ node_modules exists, delete it first${NC}"
        read -p "اضغط Enter للمتابعة (Press Enter to continue)..."
        return
    fi
    
    if [ ! -d "/d/node_modules_storage/ikaros-p2p-node_modules" ]; then
        echo -e "${RED}❌ المجلد الخارجي غير موجود${NC}"
        echo -e "${RED}❌ External folder not found${NC}"
        read -p "اضغط Enter للمتابعة (Press Enter to continue)..."
        return
    fi

    ln -s "/d/node_modules_storage/ikaros-p2p-node_modules" node_modules
    echo -e "${GREEN}✅ تم إنشاء الرابط الرمزي بنجاح${NC}"
    echo -e "${GREEN}✅ Symbolic link created successfully${NC}"
    
    read -p "اضغط Enter للمتابعة (Press Enter to continue)..."
}

restore_modules() {
    echo
    echo -e "${YELLOW}جاري استعادة node_modules...${NC}"
    echo -e "${YELLOW}Restoring node_modules...${NC}"
    
    if [ -d "node_modules" ] || [ -L "node_modules" ]; then
        echo -e "${YELLOW}⚠️ مجلد node_modules موجود، جاري الحذف...${NC}"
        echo -e "${YELLOW}⚠️ node_modules exists, deleting...${NC}"
        rm -rf node_modules
    fi
    
    if [ ! -d "/d/node_modules_storage/ikaros-p2p-node_modules" ]; then
        echo -e "${RED}❌ المجلد الخارجي غير موجود${NC}"
        echo -e "${RED}❌ External folder not found${NC}"
        read -p "اضغط Enter للمتابعة (Press Enter to continue)..."
        return
    fi

    mv "/d/node_modules_storage/ikaros-p2p-node_modules" node_modules
    echo -e "${GREEN}✅ تم استعادة node_modules بنجاح${NC}"
    echo -e "${GREEN}✅ node_modules restored successfully${NC}"
    
    read -p "اضغط Enter للمتابعة (Press Enter to continue)..."
}

install_deps() {
    echo
    echo -e "${YELLOW}جاري تثبيت التبعيات...${NC}"
    echo -e "${YELLOW}Installing dependencies...${NC}"
    
    npm install
    echo -e "${GREEN}✅ تم تثبيت التبعيات بنجاح${NC}"
    echo -e "${GREEN}✅ Dependencies installed successfully${NC}"
    
    read -p "اضغط Enter للمتابعة (Press Enter to continue)..."
}

show_size() {
    echo
    echo -e "${YELLOW}جاري حساب حجم المجلدات...${NC}"
    echo -e "${YELLOW}Calculating folder sizes...${NC}"
    echo
    
    if [ -d "node_modules" ]; then
        echo -e "${BLUE}📁 حجم node_modules الحالي:${NC}"
        echo -e "${BLUE}📁 Current node_modules size:${NC}"
        du -sh node_modules 2>/dev/null || echo "لا يمكن حساب الحجم"
    else
        echo -e "${YELLOW}⚠️ مجلد node_modules غير موجود${NC}"
        echo -e "${YELLOW}⚠️ node_modules folder not found${NC}"
    fi
    
    if [ -d "/d/node_modules_storage/ikaros-p2p-node_modules" ]; then
        echo
        echo -e "${BLUE}📁 حجم المجلد الخارجي:${NC}"
        echo -e "${BLUE}📁 External folder size:${NC}"
        du -sh "/d/node_modules_storage/ikaros-p2p-node_modules" 2>/dev/null || echo "لا يمكن حساب الحجم"
    fi
    
    echo
    read -p "اضغط Enter للمتابعة (Press Enter to continue)..."
}

# الحلقة الرئيسية
while true; do
    clear
    echo -e "${BLUE}"
    echo "========================================"
    echo "    إدارة مجلد node_modules"
    echo "    Node Modules Management"
    echo "========================================"
    echo -e "${NC}"
    
    show_menu
    
    read -p "أدخل اختيارك (Enter choice): " choice
    
    case $choice in
        1) delete_modules ;;
        2) move_modules ;;
        3) create_link ;;
        4) restore_modules ;;
        5) install_deps ;;
        6) show_size ;;
        0) 
            echo
            echo -e "${GREEN}شكراً لاستخدام أداة إدارة node_modules${NC}"
            echo -e "${GREEN}Thank you for using node_modules management tool${NC}"
            echo
            exit 0
            ;;
        *) 
            echo -e "${RED}اختيار غير صحيح، حاول مرة أخرى${NC}"
            echo -e "${RED}Invalid choice, try again${NC}"
            sleep 2
            ;;
    esac
done
