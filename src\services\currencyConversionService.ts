/**
 * خدمة تحويل العملات
 * Currency Conversion Service
 */

import { SUPPORTED_CURRENCIES, Currency } from '@/utils/currencies';

export interface ConversionRate {
  from: string;
  to: string;
  rate: number;
  lastUpdated: Date;
  source: 'cache' | 'api' | 'fallback';
}

export interface ConversionResult {
  originalAmount: number;
  convertedAmount: number;
  rate: number;
  fromCurrency: string;
  toCurrency: string;
  lastUpdated: Date;
  source: string;
}

class CurrencyConversionService {
  private ratesCache: Map<string, ConversionRate> = new Map();
  private cacheExpiry = 5 * 60 * 1000; // 5 دقائق
  private fallbackRates: Record<string, number> = {
    // العملات التقليدية (مرتبة حسب الأهمية)
    'USD': 1.0,
    'SAR': 3.75,
    'AED': 3.67,
    'KWD': 0.31,
    'QAR': 3.64,
    'BHD': 0.38,
    'OMR': 0.38,
    'JOD': 0.71,
    'EGP': 31.0,
    'EUR': 0.92,
    'GBP': 0.79,
    'JPY': 150.0,
    'CAD': 1.35,
    'AUD': 1.52,
    'CHF': 0.88,
    'CNY': 7.25,
    'TRY': 27.0,
    'LBP': 15000.0,
    'MAD': 10.2,
    'TND': 3.1,
    'DZD': 135.0,
    'IQD': 1310.0,
    'SYP': 2512.0,
    'YER': 250.0,
    'PKR': 280.0,
    'INR': 83.0,
    'BDT': 110.0,
    'MYR': 4.7,
    'IDR': 15500.0,
    'SGD': 1.35,
    // العملات المستقرة (كلها تساوي تقريباً 1 USD)
    'USDT': 1.0,
    'USDC': 1.0,
    'BUSD': 1.0,
    'DAI': 1.0,
    'FDUSD': 1.0,
    'TUSD': 1.0,
    'USDD': 1.0,
    'FRAX': 1.0,
    'USDP': 1.0,
    'LUSD': 1.0,
    'GUSD': 1.0,
    'SUSD': 1.0,
    'USTC': 1.0,
    'PYUSD': 1.0
  };

  /**
   * تحويل مبلغ من عملة إلى أخرى
   */
  async convertAmount(
    amount: number,
    fromCurrency: string,
    toCurrency: string
  ): Promise<ConversionResult> {
    try {
      if (fromCurrency === toCurrency) {
        return {
          originalAmount: amount,
          convertedAmount: amount,
          rate: 1,
          fromCurrency,
          toCurrency,
          lastUpdated: new Date(),
          source: 'same_currency'
        };
      }

      const rate = await this.getExchangeRate(fromCurrency, toCurrency);
      const convertedAmount = amount * rate.rate;

      return {
        originalAmount: amount,
        convertedAmount: parseFloat(convertedAmount.toFixed(2)),
        rate: rate.rate,
        fromCurrency,
        toCurrency,
        lastUpdated: rate.lastUpdated,
        source: rate.source
      };
    } catch (error) {
      console.error('Currency conversion error:', error);
      throw new Error('Currency conversion failed');
    }
  }

  /**
   * الحصول على سعر الصرف بين عملتين
   */
  async getExchangeRate(fromCurrency: string, toCurrency: string): Promise<ConversionRate> {
    const cacheKey = `${fromCurrency}_${toCurrency}`;
    
    // التحقق من الكاش
    const cachedRate = this.ratesCache.get(cacheKey);
    if (cachedRate && this.isCacheValid(cachedRate.lastUpdated)) {
      return cachedRate;
    }

    try {
      // محاولة جلب السعر من API خارجي
      const apiRate = await this.fetchRateFromAPI(fromCurrency, toCurrency);
      if (apiRate) {
        this.ratesCache.set(cacheKey, apiRate);
        return apiRate;
      }
    } catch (error) {
      console.warn('Failed to fetch rate from API:', error);
    }

    // استخدام الأسعار الاحتياطية
    const fallbackRate = this.getFallbackRate(fromCurrency, toCurrency);
    this.ratesCache.set(cacheKey, fallbackRate);
    return fallbackRate;
  }

  /**
   * جلب سعر الصرف من API خارجي
   */
  private async fetchRateFromAPI(fromCurrency: string, toCurrency: string): Promise<ConversionRate | null> {
    try {
      // قائمة العملات المستقرة المدعومة (مرتبة حسب الأهمية)
      const stablecoins = ['USDT', 'USDC', 'BUSD', 'DAI', 'FDUSD', 'TUSD', 'USDD', 'FRAX', 'USDP', 'LUSD', 'GUSD', 'SUSD', 'USTC', 'PYUSD'];

      // إذا كانت العملة المصدر عملة مستقرة، نستخدم USD كبديل
      let baseCurrency = fromCurrency;
      let isFromStablecoin = stablecoins.includes(fromCurrency.toUpperCase());
      let isToStablecoin = stablecoins.includes(toCurrency.toUpperCase());

      // إذا كانت كلا العملتين مستقرتين، فالسعر = 1
      if (isFromStablecoin && isToStablecoin) {
        return {
          from: fromCurrency,
          to: toCurrency,
          rate: 1.0,
          lastUpdated: new Date(),
          source: 'api'
        };
      }

      // إذا كانت العملة المصدر مستقرة، نستخدم USD
      if (isFromStablecoin) {
        baseCurrency = 'USD';
      }

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(
        `https://api.exchangerate-api.com/v4/latest/${baseCurrency}`,
        {
          signal: controller.signal,
          headers: {
            'User-Agent': 'Ikaros P2P Platform'
          }
        }
      );

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (!data || !data.rates) {
        throw new Error('Invalid API response format');
      }

      let rate = 1.0;

      if (isFromStablecoin && !isToStablecoin) {
        // من عملة مستقرة إلى عملة تقليدية
        // العملة المستقرة = 1 USD، لذا نأخذ سعر العملة المستهدفة مقابل USD
        rate = data.rates[toCurrency] || 1.0;
      } else if (!isFromStablecoin && isToStablecoin) {
        // من عملة تقليدية إلى عملة مستقرة
        // نحتاج إلى عكس السعر لأن العملة المستقرة = 1 USD
        rate = 1.0 / (data.rates[fromCurrency] || 1.0);
      } else if (!isFromStablecoin && !isToStablecoin) {
        // من عملة تقليدية إلى عملة تقليدية أخرى
        if (data.rates[toCurrency]) {
          rate = data.rates[toCurrency];
        } else {
          throw new Error('Target currency not found in API response');
        }
      }

      return {
        from: fromCurrency,
        to: toCurrency,
        rate: parseFloat(rate.toFixed(6)),
        lastUpdated: new Date(),
        source: 'api'
      };
    } catch (error) {
      console.error('Error fetching rate from API:', error);
      return null;
    }
  }

  /**
   * الحصول على سعر احتياطي
   */
  private getFallbackRate(fromCurrency: string, toCurrency: string): ConversionRate {
    // قائمة العملات المستقرة (مرتبة حسب الأهمية)
    const stablecoins = ['USDT', 'USDC', 'BUSD', 'DAI', 'FDUSD', 'TUSD', 'USDD', 'FRAX', 'USDP', 'LUSD', 'GUSD', 'SUSD', 'USTC', 'PYUSD'];
    const isFromStablecoin = stablecoins.includes(fromCurrency.toUpperCase());
    const isToStablecoin = stablecoins.includes(toCurrency.toUpperCase());

    // إذا كانت كلا العملتين مستقرتين، فالسعر = 1
    if (isFromStablecoin && isToStablecoin) {
      return {
        from: fromCurrency,
        to: toCurrency,
        rate: 1.0,
        lastUpdated: new Date(),
        source: 'fallback'
      };
    }

    const fromRate = this.fallbackRates[fromCurrency] || 1;
    const toRate = this.fallbackRates[toCurrency] || 1;

    // تحويل عبر USD كعملة وسطية
    const rate = toRate / fromRate;

    return {
      from: fromCurrency,
      to: toCurrency,
      rate: parseFloat(rate.toFixed(6)),
      lastUpdated: new Date(),
      source: 'fallback'
    };
  }

  /**
   * التحقق من صحة الكاش
   */
  private isCacheValid(lastUpdated: Date): boolean {
    const now = new Date().getTime();
    const cacheTime = lastUpdated.getTime();
    return (now - cacheTime) < this.cacheExpiry;
  }

  /**
   * الحصول على العملات المدعومة
   */
  getSupportedCurrencies(): Currency[] {
    return SUPPORTED_CURRENCIES.filter(currency => currency.is_active);
  }

  /**
   * الحصول على معلومات العملة
   */
  getCurrencyInfo(currencyCode: string): Currency | undefined {
    return SUPPORTED_CURRENCIES.find(currency => currency.code === currencyCode);
  }

  /**
   * تنسيق المبلغ حسب العملة
   */
  formatAmount(amount: number, currencyCode: string): string {
    const currency = this.getCurrencyInfo(currencyCode);
    if (!currency) {
      return amount.toFixed(2);
    }

    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: currency.decimal_places,
      maximumFractionDigits: currency.decimal_places
    }).format(amount);
  }

  /**
   * تحديث الأسعار الاحتياطية
   */
  updateFallbackRates(rates: Record<string, number>): void {
    this.fallbackRates = { ...this.fallbackRates, ...rates };
  }

  /**
   * مسح الكاش
   */
  clearCache(): void {
    this.ratesCache.clear();
  }

  /**
   * الحصول على حالة الكاش
   */
  getCacheStatus(): { size: number; entries: Array<{ key: string; lastUpdated: Date; source: string }> } {
    const entries = Array.from(this.ratesCache.entries()).map(([key, value]) => ({
      key,
      lastUpdated: value.lastUpdated,
      source: value.source
    }));

    return {
      size: this.ratesCache.size,
      entries
    };
  }

  /**
   * تحويل متعدد العملات
   */
  async convertToMultipleCurrencies(
    amount: number,
    fromCurrency: string,
    targetCurrencies: string[]
  ): Promise<ConversionResult[]> {
    const results: ConversionResult[] = [];

    for (const toCurrency of targetCurrencies) {
      try {
        const result = await this.convertAmount(amount, fromCurrency, toCurrency);
        results.push(result);
      } catch (error) {
        console.error(`فشل في تحويل إلى ${toCurrency}:`, error);
      }
    }

    return results;
  }

  /**
   * حساب أفضل سعر صرف
   */
  async getBestExchangeRate(
    amount: number,
    fromCurrency: string,
    targetCurrencies: string[]
  ): Promise<ConversionResult | null> {
    try {
      const conversions = await this.convertToMultipleCurrencies(amount, fromCurrency, targetCurrencies);
      
      if (conversions.length === 0) {
        return null;
      }

      // العثور على أفضل معدل (أعلى مبلغ محول)
      return conversions.reduce((best, current) => 
        current.convertedAmount > best.convertedAmount ? current : best
      );
    } catch (error) {
      console.error('خطأ في حساب أفضل سعر صرف:', error);
      return null;
    }
  }

  /**
   * تحديث سعر في الوقت الفعلي
   */
  async refreshRate(fromCurrency: string, toCurrency: string): Promise<ConversionRate> {
    const cacheKey = `${fromCurrency}_${toCurrency}`;
    this.ratesCache.delete(cacheKey); // حذف من الكاش لإجبار التحديث
    return await this.getExchangeRate(fromCurrency, toCurrency);
  }
}

export const currencyConversionService = new CurrencyConversionService();
export default currencyConversionService;
