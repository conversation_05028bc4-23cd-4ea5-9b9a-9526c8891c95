'use client';

import { useContext } from 'react';
import { LanguageContext } from '@/contexts/LanguageContext';
import {
  formatNumber as formatNumberUtil,
  formatCurrency as formatCurrencyUtil,
  convertArabicToEnglishNumbers
} from '@/utils/numberUtils';

// استيراد ملفات الترجمة المخصصة
import arTranslations from '@/locales/user-dashboard/ar.json';
import enTranslations from '@/locales/user-dashboard/en.json';

type TranslationKey = string;
type TranslationParams = Record<string, string | number>;

interface UserDashboardTranslations {
  ar: typeof arTranslations;
  en: typeof enTranslations;
}

const translations: UserDashboardTranslations = {
  ar: arTranslations,
  en: enTranslations
};

/**
 * Hook مخصص للترجمة في لوحة تحكم المستخدم
 * يوفر ترجمات مخصصة منفصلة عن ملفات الترجمة الرئيسية
 */
export function useUserDashboardTranslation() {
  const context = useContext(LanguageContext);
  const currentLang = (context?.language || 'ar') as keyof UserDashboardTranslations;

  /**
   * دالة الترجمة الرئيسية
   * @param key مفتاح الترجمة (مثل: "dashboard.title")
   * @param params معاملات اختيارية للاستبدال في النص
   * @returns النص المترجم
   */
  const t = (key: TranslationKey, params?: TranslationParams): string => {
    try {
      // تقسيم المفتاح إلى أجزاء
      const keys = key.split('.');
      let value: any = translations[currentLang] || translations.ar;

      // البحث عن القيمة في الكائن المتداخل
      for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
          value = value[k];
        } else {
          // إذا لم يتم العثور على المفتاح، جرب اللغة الافتراضية (العربية)
          if (currentLang !== 'ar') {
            let fallbackValue: any = translations.ar;
            for (const fallbackKey of keys) {
              if (fallbackValue && typeof fallbackValue === 'object' && fallbackKey in fallbackValue) {
                fallbackValue = fallbackValue[fallbackKey];
              } else {
                return key; // إرجاع المفتاح نفسه إذا لم يتم العثور على الترجمة
              }
            }
            value = fallbackValue;
          } else {
            return key; // إرجاع المفتاح نفسه إذا لم يتم العثور على الترجمة
          }
          break;
        }
      }

      // التأكد من أن القيمة نص
      if (typeof value !== 'string') {
        return key;
      }

      // استبدال المعاملات في النص
      if (params) {
        value = Object.entries(params).reduce((text, [paramKey, paramValue]) => {
          return text.replace(new RegExp(`{{${paramKey}}}`, 'g'), String(paramValue));
        }, value);
      }

      // تحويل الأرقام العربية إلى إنجليزية
      return convertArabicToEnglishNumbers(value);
    } catch (error) {
      console.warn(`Translation error for key "${key}":`, error);
      return key;
    }
  };

  /**
   * دالة للحصول على ترجمات متعددة دفعة واحدة
   * @param keys مصفوفة من مفاتيح الترجمة
   * @returns كائن يحتوي على الترجمات
   */
  const getTranslations = (keys: TranslationKey[]): Record<string, string> => {
    return keys.reduce((acc, key) => {
      acc[key] = t(key);
      return acc;
    }, {} as Record<string, string>);
  };

  /**
   * دالة للتحقق من وجود ترجمة لمفتاح معين
   * @param key مفتاح الترجمة
   * @returns true إذا كانت الترجمة موجودة
   */
  const hasTranslation = (key: TranslationKey): boolean => {
    try {
      const keys = key.split('.');
      let value: any = translations[currentLang] || translations.ar;

      for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
          value = value[k];
        } else {
          return false;
        }
      }

      return typeof value === 'string';
    } catch {
      return false;
    }
  };

  /**
   * دالة للحصول على جميع الترجمات لقسم معين
   * @param section القسم (مثل: "dashboard", "trades")
   * @returns كائن يحتوي على جميع ترجمات القسم
   */
  const getSectionTranslations = (section: string): Record<string, any> => {
    try {
      const sectionData = translations[currentLang]?.[section as keyof typeof translations[typeof currentLang]];
      return sectionData || {};
    } catch {
      return {};
    }
  };

  /**
   * دالة لتنسيق الأرقام حسب اللغة
   * @param number الرقم المراد تنسيقه
   * @param options خيارات التنسيق
   * @returns الرقم منسق
   */
  const formatNumber = (
    number: number,
    options?: Intl.NumberFormatOptions
  ): string => {
    const locale = 'en-US'; // استخدام الإنجليزية دائماً للأرقام
    return formatNumberUtil(number, locale, options);
  };

  /**
   * دالة لتنسيق التاريخ حسب اللغة
   * @param date التاريخ المراد تنسيقه
   * @param options خيارات التنسيق
   * @returns التاريخ منسق
   */
  const formatDate = (
    date: Date | string | number,
    options?: Intl.DateTimeFormatOptions
  ): string => {
    const locale = currentLang === 'ar' ? 'ar-SA' : 'en-US';
    const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date;
    return new Intl.DateTimeFormat(locale, options).format(dateObj);
  };

  /**
   * دالة لتنسيق العملة حسب اللغة
   * @param amount المبلغ
   * @param currency رمز العملة
   * @returns المبلغ منسق كعملة
   */
  const formatCurrency = (amount: number, currency: string = 'USD'): string => {
    return formatCurrencyUtil(amount, currency, 'en-US');
  };

  return {
    t,
    getTranslations,
    hasTranslation,
    getSectionTranslations,
    formatNumber,
    formatDate,
    formatCurrency,
    language: currentLang,
    isRTL: currentLang === 'ar'
  };
}

export default useUserDashboardTranslation;
