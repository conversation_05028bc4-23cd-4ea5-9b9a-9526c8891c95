/**
 * تحسينات RPC لتجنب حدود الطلبات
 * RPC Optimizations to Avoid Rate Limits
 */

import { ethers } from 'ethers';

/**
 * إعدادات RPC محسنة
 */
export const RPC_CONFIG = {
  // حد أقصى للبلوكات في الطلب الواحد
  MAX_BLOCK_RANGE: 1000,
  
  // حد أقصى للبلوكات في chunk واحد
  CHUNK_SIZE: 100,
  
  // تأخير بين الطلبات (بالميلي ثانية)
  REQUEST_DELAY: 100,
  
  // عدد المحاولات عند فشل الطلب
  MAX_RETRIES: 3,
  
  // تأخير إعادة المحاولة (بالميلي ثانية)
  RETRY_DELAY: 1000,
  
  // حد أقصى للنتائج في الطلب الواحد
  MAX_RESULTS: 10000
};

/**
 * قائمة RPC providers بديلة لـ BSC Testnet
 */
export const BSC_TESTNET_RPCS = [
  'https://data-seed-prebsc-1-s1.binance.org:8545/',
  'https://data-seed-prebsc-2-s1.binance.org:8545/',
  'https://data-seed-prebsc-1-s2.binance.org:8545/',
  'https://data-seed-prebsc-2-s2.binance.org:8545/',
  'https://data-seed-prebsc-1-s3.binance.org:8545/',
  'https://data-seed-prebsc-2-s3.binance.org:8545/'
];

/**
 * قائمة RPC providers بديلة لـ BSC Mainnet
 */
export const BSC_MAINNET_RPCS = [
  'https://bsc-dataseed.binance.org/',
  'https://bsc-dataseed1.defibit.io/',
  'https://bsc-dataseed1.ninicoin.io/',
  'https://bsc-dataseed2.defibit.io/',
  'https://bsc-dataseed3.defibit.io/',
  'https://bsc-dataseed4.defibit.io/'
];

/**
 * كلاس لإدارة RPC providers متعددة
 */
export class MultiRPCProvider {
  private providers: ethers.JsonRpcProvider[] = [];
  private currentProviderIndex = 0;
  private failedProviders = new Set<number>();

  constructor(rpcUrls: string[]) {
    this.providers = rpcUrls.map(url => new ethers.JsonRpcProvider(url));
  }

  /**
   * الحصول على provider نشط
   */
  getCurrentProvider(): ethers.JsonRpcProvider {
    // إذا فشل جميع الـ providers، نعيد تعيينهم
    if (this.failedProviders.size === this.providers.length) {
      console.log('🔄 إعادة تعيين جميع RPC providers');
      this.failedProviders.clear();
      this.currentProviderIndex = 0;
    }

    // البحث عن provider يعمل
    while (this.failedProviders.has(this.currentProviderIndex)) {
      this.currentProviderIndex = (this.currentProviderIndex + 1) % this.providers.length;
    }

    return this.providers[this.currentProviderIndex];
  }

  /**
   * تسجيل فشل provider
   */
  markProviderAsFailed(provider: ethers.JsonRpcProvider): void {
    const index = this.providers.indexOf(provider);
    if (index !== -1) {
      this.failedProviders.add(index);
      console.warn(`⚠️ تم تسجيل فشل RPC provider ${index + 1}`);
      
      // الانتقال للـ provider التالي
      this.currentProviderIndex = (this.currentProviderIndex + 1) % this.providers.length;
    }
  }

  /**
   * تنفيذ طلب مع إعادة المحاولة على providers مختلفة
   */
  async executeWithRetry<T>(
    operation: (provider: ethers.JsonRpcProvider) => Promise<T>,
    maxRetries: number = RPC_CONFIG.MAX_RETRIES
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      const provider = this.getCurrentProvider();
      
      try {
        const result = await operation(provider);
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.error(`❌ فشل الطلب على provider ${this.currentProviderIndex + 1}:`, lastError.message);
        
        // إذا كان الخطأ بسبب حد الطلبات، نسجل فشل الـ provider
        if (this.isRateLimitError(lastError)) {
          this.markProviderAsFailed(provider);
        }
        
        // تأخير قبل إعادة المحاولة
        if (attempt < maxRetries - 1) {
          await this.delay(RPC_CONFIG.RETRY_DELAY * (attempt + 1));
        }
      }
    }

    throw lastError || new Error('فشل في تنفيذ الطلب على جميع الـ providers');
  }

  /**
   * التحقق من أن الخطأ بسبب حد الطلبات
   */
  private isRateLimitError(error: Error): boolean {
    const message = error.message.toLowerCase();
    return message.includes('limit exceeded') ||
           message.includes('too many requests') ||
           message.includes('rate limit') ||
           message.includes('too many results');
  }

  /**
   * تأخير
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * إنشاء multi-RPC provider للشبكة المحددة
 */
export function createMultiRPCProvider(isTestnet: boolean = true): MultiRPCProvider {
  const rpcUrls = isTestnet ? BSC_TESTNET_RPCS : BSC_MAINNET_RPCS;
  return new MultiRPCProvider(rpcUrls);
}

/**
 * تحسين طلب getLogs لتجنب حدود RPC
 */
export async function optimizedGetLogs(
  provider: ethers.JsonRpcProvider,
  filter: ethers.Filter,
  maxBlockRange: number = RPC_CONFIG.MAX_BLOCK_RANGE
): Promise<ethers.Log[]> {
  const fromBlock = filter.fromBlock as number || 0;
  const toBlock = filter.toBlock as number || await provider.getBlockNumber();
  
  const allLogs: ethers.Log[] = [];
  
  // تقسيم النطاق إلى chunks أصغر
  for (let start = fromBlock; start <= toBlock; start += maxBlockRange) {
    const end = Math.min(start + maxBlockRange - 1, toBlock);
    
    const chunkFilter = {
      ...filter,
      fromBlock: start,
      toBlock: end
    };
    
    try {
      console.log(`🔍 جلب logs من البلوك ${start} إلى ${end}`);
      const logs = await provider.getLogs(chunkFilter);
      allLogs.push(...logs);
      
      // تأخير قصير لتجنب إرهاق RPC
      await new Promise(resolve => setTimeout(resolve, RPC_CONFIG.REQUEST_DELAY));
      
    } catch (error) {
      console.error(`خطأ في جلب logs من ${start} إلى ${end}:`, error);

      // إذا كان النطاق كبير جداً، نقسمه أكثر
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('limit exceeded') && maxBlockRange > 10) {
        const smallerRange = Math.floor(maxBlockRange / 2);
        console.log(`🔄 تقليل نطاق البلوكات إلى ${smallerRange}`);

        const smallerLogs = await optimizedGetLogs(provider, {
          ...filter,
          fromBlock: start,
          toBlock: end
        }, smallerRange);

        allLogs.push(...smallerLogs);
      } else {
        throw error;
      }
    }
  }
  
  return allLogs;
}

/**
 * تحسين طلب queryFilter للعقود الذكية
 */
export async function optimizedQueryFilter(
  contract: ethers.Contract,
  filter: ethers.ContractEventName,
  fromBlock: number,
  toBlock: number,
  chunkSize: number = RPC_CONFIG.CHUNK_SIZE
): Promise<(ethers.EventLog | ethers.Log)[]> {
  const allEvents: (ethers.EventLog | ethers.Log)[] = [];

  for (let start = fromBlock; start <= toBlock; start += chunkSize) {
    const end = Math.min(start + chunkSize - 1, toBlock);

    try {
      console.log(`🔍 جلب أحداث ${filter} من البلوك ${start} إلى ${end}`);
      const events = await contract.queryFilter(filter, start, end);
      allEvents.push(...events);

      // تأخير قصير
      await new Promise(resolve => setTimeout(resolve, RPC_CONFIG.REQUEST_DELAY));

    } catch (error) {
      console.error(`خطأ في جلب أحداث ${filter} من ${start} إلى ${end}:`, error);

      // تقليل حجم الـ chunk إذا كان الخطأ بسبب حد الطلبات
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('limit exceeded') && chunkSize > 10) {
        const smallerChunk = Math.floor(chunkSize / 2);
        console.log(`🔄 تقليل حجم chunk إلى ${smallerChunk}`);

        const smallerEvents = await optimizedQueryFilter(
          contract, filter, start, end, smallerChunk
        );

        allEvents.push(...smallerEvents);
      } else {
        throw error;
      }
    }
  }

  return allEvents;
}

/**
 * إعدادات provider محسنة
 */
export function createOptimizedProvider(rpcUrl: string): ethers.JsonRpcProvider {
  return new ethers.JsonRpcProvider(rpcUrl, undefined, {
    // تحديد حد أقصى للطلبات المتزامنة
    batchMaxCount: 10,
    batchMaxSize: 1024 * 1024, // 1MB
    batchStallTime: 10, // 10ms

    // إعدادات الشبكة
    staticNetwork: true
  });
}

/**
 * اختبار سرعة RPC providers
 */
export async function testRPCProviders(rpcUrls: string[]): Promise<string[]> {
  console.log('🧪 اختبار سرعة RPC providers...');
  
  const results: { url: string; latency: number }[] = [];
  
  for (const url of rpcUrls) {
    try {
      const provider = new ethers.JsonRpcProvider(url);
      const startTime = Date.now();
      
      await provider.getBlockNumber();
      
      const latency = Date.now() - startTime;
      results.push({ url, latency });
      
      console.log(`✅ ${url}: ${latency}ms`);
    } catch (error) {
      console.error(`❌ ${url}: فشل`);
    }
  }
  
  // ترتيب حسب السرعة
  results.sort((a, b) => a.latency - b.latency);
  
  console.log('🏆 أفضل RPC providers:');
  results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.url} (${result.latency}ms)`);
  });
  
  return results.map(r => r.url);
}

// تصدير للاستخدام في وحدة التحكم
if (typeof window !== 'undefined') {
  (window as any).rpcOptimizations = {
    createMultiRPCProvider,
    optimizedGetLogs,
    optimizedQueryFilter,
    testRPCProviders,
    RPC_CONFIG,
    BSC_TESTNET_RPCS,
    BSC_MAINNET_RPCS
  };
}
