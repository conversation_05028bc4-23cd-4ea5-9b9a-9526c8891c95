'use client';

import { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  Users, 
  DollarSign, 
  Shield,
  Activity
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

export default function SimplePlatformStats() {
  const { t } = useTranslation();
  const [stats, setStats] = useState({
    totalTrades: 50000,
    totalUsers: 12500,
    totalVolume: 2.5,
    successRate: 99.8,
    onlineUsers: 1250
  });

  // Real-time updates for online users
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(prev => ({
        ...prev,
        onlineUsers: prev.onlineUsers + Math.floor(Math.random() * 20) - 10
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 dark:from-black dark:via-gray-900 dark:to-gray-800 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-96 h-96 bg-blue-500/10 rounded-full filter blur-3xl" />
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500/10 rounded-full filter blur-3xl" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-white/10 backdrop-blur-sm text-white rounded-full px-6 py-3 mb-6 border border-white/20">
            <Activity className="w-5 h-5 ml-2" />
            <span className="text-sm font-medium">{t('home.platformStats.title')}</span>
          </div>
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
            {t('home.platformStats.title')}
          </h2>
          <p className="text-lg sm:text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed px-4">
            {t('home.platformStats.subtitle')}
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 sm:gap-6 lg:gap-8 mb-16">
          {/* Total Trades */}
          <div className="group relative">
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 lg:p-8 border border-white/20 shadow-2xl hover:bg-white/15 transition-all duration-300">
              <div className="flex items-center justify-between mb-4 sm:mb-6">
                <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <TrendingUp className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" />
                </div>
                <div className="text-right">
                  <span className="text-xs px-2 sm:px-3 py-1 rounded-full bg-white/20 text-white border border-white/30">
                    +25%
                  </span>
                </div>
              </div>
              <div className="mb-2 sm:mb-3">
                <div className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-white">
                  {stats.totalTrades.toLocaleString()}+
                </div>
              </div>
              <div className="text-blue-100 font-medium text-sm sm:text-base">
                {t('home.platformStats.metrics.totalTrades.label')}
              </div>
            </div>
          </div>

          {/* Total Users */}
          <div className="group relative">
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 lg:p-8 border border-white/20 shadow-2xl hover:bg-white/15 transition-all duration-300">
              <div className="flex items-center justify-between mb-4 sm:mb-6">
                <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-xl bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Users className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" />
                </div>
                <div className="text-right">
                  <span className="text-xs px-2 sm:px-3 py-1 rounded-full bg-white/20 text-white border border-white/30">
                    +18%
                  </span>
                </div>
              </div>
              <div className="mb-2 sm:mb-3">
                <div className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-white">
                  {stats.totalUsers.toLocaleString()}+
                </div>
              </div>
              <div className="text-blue-100 font-medium text-sm sm:text-base">
                {t('home.platformStats.metrics.totalUsers.label')}
              </div>
            </div>
          </div>

          {/* Trading Volume */}
          <div className="group relative">
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 lg:p-8 border border-white/20 shadow-2xl hover:bg-white/15 transition-all duration-300">
              <div className="flex items-center justify-between mb-4 sm:mb-6">
                <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <DollarSign className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" />
                </div>
                <div className="text-right">
                  <span className="text-xs px-2 sm:px-3 py-1 rounded-full bg-white/20 text-white border border-white/30">
                    +32%
                  </span>
                </div>
              </div>
              <div className="mb-2 sm:mb-3">
                <div className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-white">
                  ${stats.totalVolume.toFixed(1)}M+
                </div>
              </div>
              <div className="text-blue-100 font-medium text-sm sm:text-base">
                {t('home.platformStats.metrics.totalVolume.label')}
              </div>
            </div>
          </div>

          {/* Success Rate */}
          <div className="group relative">
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 lg:p-8 border border-white/20 shadow-2xl hover:bg-white/15 transition-all duration-300">
              <div className="flex items-center justify-between mb-4 sm:mb-6">
                <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Shield className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" />
                </div>
                <div className="text-right">
                  <span className="text-xs px-2 sm:px-3 py-1 rounded-full bg-white/20 text-white border border-white/30">
                    {t('home.platformStats.stable')}
                  </span>
                </div>
              </div>
              <div className="mb-2 sm:mb-3">
                <div className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-white">
                  {stats.successRate.toFixed(1)}%
                </div>
              </div>
              <div className="text-blue-100 font-medium text-sm sm:text-base">
                {t('home.platformStats.metrics.successRate.label')}
              </div>
            </div>
          </div>

          {/* Online Users */}
          <div className="group relative">
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 lg:p-8 border border-white/20 shadow-2xl hover:bg-white/15 transition-all duration-300">
              <div className="flex items-center justify-between mb-4 sm:mb-6">
                <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-xl bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Activity className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" />
                </div>
                <div className="text-right">
                  <span className="text-xs px-2 sm:px-3 py-1 rounded-full bg-white/20 text-white border border-white/30">
                    {t('home.platformStats.metrics.onlineUsers.growth')}
                  </span>
                </div>
              </div>
              <div className="mb-2 sm:mb-3">
                <div className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-white">
                  {stats.onlineUsers.toLocaleString()}+
                </div>
              </div>
              <div className="text-blue-100 font-medium text-sm sm:text-base">
                {t('home.platformStats.metrics.onlineUsers.label')}
              </div>
              <div className="flex items-center mt-2 text-green-400 text-xs sm:text-sm">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse" />
                {t('home.platformStats.metrics.onlineUsers.growth')}
              </div>
            </div>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
          <div className="text-center px-2">
            <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
              <Shield className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" />
            </div>
            <h4 className="text-lg sm:text-xl font-bold text-white mb-1 sm:mb-2">100%</h4>
            <p className="text-blue-200 text-sm sm:text-base leading-relaxed">{t('home.platformStats.quickStats.fundProtection')}</p>
          </div>

          <div className="text-center px-2">
            <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
              <TrendingUp className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" />
            </div>
            <h4 className="text-lg sm:text-xl font-bold text-white mb-1 sm:mb-2">0.5%</h4>
            <p className="text-blue-200 text-sm sm:text-base leading-relaxed">{t('home.platformStats.quickStats.lowFees')}</p>
          </div>

          <div className="text-center px-2">
            <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
              <Users className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" />
            </div>
            <h4 className="text-lg sm:text-xl font-bold text-white mb-1 sm:mb-2">24/7</h4>
            <p className="text-blue-200 text-sm sm:text-base leading-relaxed">{t('home.platformStats.quickStats.continuousSupport')}</p>
          </div>

          <div className="text-center px-2">
            <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
              <Activity className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" />
            </div>
            <h4 className="text-lg sm:text-xl font-bold text-white mb-1 sm:mb-2">50K+</h4>
            <p className="text-blue-200 text-sm sm:text-base leading-relaxed">{t('home.platformStats.quickStats.satisfiedUsers')}</p>
          </div>
        </div>
      </div>
    </section>
  );
}
