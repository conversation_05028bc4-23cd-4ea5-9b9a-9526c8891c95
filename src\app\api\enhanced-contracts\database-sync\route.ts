/**
 * Next.js API Route Proxy for Database Sync
 * بروكسي Next.js لمزامنة قاعدة البيانات
 */

import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // استخراج المعاملات من URL
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || '';
    const active = searchParams.get('active') || '';
    const networkId = searchParams.get('network_id') || '';

    // بناء URL للـ PHP API
    const phpApiUrl = new URL('http://localhost/ikaros-p2p/api/enhanced-contracts/database-sync.php');
    if (action) phpApiUrl.searchParams.set('action', action);
    if (active) phpApiUrl.searchParams.set('active', active);
    if (networkId) phpApiUrl.searchParams.set('network_id', networkId);

    console.log('🔄 Proxying database request to:', phpApiUrl.toString());

    // إرسال الطلب إلى PHP API
    const response = await fetch(phpApiUrl.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    console.log('📡 Database API Response Status:', response.status);

    if (!response.ok) {
      throw new Error(`Database API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('📊 Database API Data:', data);

    // إرجاع البيانات مع headers صحيحة
    return NextResponse.json(data, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error: any) {
    console.error('❌ Database Proxy Error:', error);

    // في حالة فشل قاعدة البيانات، إرجاع بيانات فارغة
    const action = new URL(request.url).searchParams.get('action');
    
    const fallbackData = {
      success: true,
      data: action === 'networks' ? {
        networks: [],
        count: 0,
        filters: { active_only: true },
        summary: { total_networks: 0, active_networks: 0, testnet_networks: 0, mainnet_networks: 0 }
      } : {
        tokens: [],
        count: 0,
        filters: { active_only: true },
        summary: { total_tokens: 0, active_tokens: 0, stablecoins: 0, total_market_cap: 0, total_volume_24h: 0 }
      },
      message: 'لا توجد بيانات محفوظة في قاعدة البيانات',
      message_en: 'No data saved in database',
      timestamp: new Date().toISOString(),
      source: 'database_empty'
    };

    return NextResponse.json(fallbackData, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || '';

    // بناء URL للـ PHP API
    const phpApiUrl = new URL('http://localhost/ikaros-p2p/api/enhanced-contracts/database-sync.php');
    if (action) phpApiUrl.searchParams.set('action', action);

    console.log('🔄 Proxying database sync request to:', phpApiUrl.toString());
    console.log('📤 Request body:', body);

    // إرسال الطلب إلى PHP API
    const response = await fetch(phpApiUrl.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(body),
    });

    console.log('📡 Database Sync Response Status:', response.status);

    if (!response.ok) {
      throw new Error(`Database Sync API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('📊 Database Sync Response:', data);

    return NextResponse.json(data, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error: any) {
    console.error('❌ Database Sync Error:', error);

    return NextResponse.json({
      success: false,
      error: 'فشل في مزامنة قاعدة البيانات',
      error_en: 'Failed to sync database',
      details: error.message,
      timestamp: new Date().toISOString()
    }, {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}



export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
