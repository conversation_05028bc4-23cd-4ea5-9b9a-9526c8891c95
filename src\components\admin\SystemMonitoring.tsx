'use client';

import React, { useState, useEffect } from 'react';
import {
  Activity,
  Server,
  Database,
  Cpu,
  MemoryStick,
  HardDrive,
  Wifi,
  Globe,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Zap,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  LineChart,
  Monitor,
  Settings,
  RefreshCw,
  Download,
  Search,
  Filter,
  Eye,
  Play,
  Pause,
  Square,
  RotateCcw,
  Tool,
  Gauge,
  Thermometer,
  Battery,
  Signal,
  Cloud,
  CloudOff,
  WifiOff,
  AlertCircle,
  Info,
  CheckSquare,
  X,
  Plus,
  Minus,
  ArrowUp,
  ArrowDown,
  Calendar,
  Timer,
  Target,
  Layers,
  Network,
  FileText,
  Bug,
  Wrench
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';

interface SystemService {
  id: string;
  name: string;
  type: 'database' | 'api' | 'blockchain' | 'cache' | 'queue' | 'storage' | 'email' | 'sms' | 'websocket' | 'cdn' | 'loadBalancer' | 'monitoring';
  status: 'healthy' | 'warning' | 'critical' | 'down' | 'maintenance' | 'unknown' | 'degraded' | 'recovering';
  uptime: number; // percentage
  responseTime: number; // ms
  lastCheck: string;
  version: string;
  endpoint?: string;
  port?: number;
  dependencies: string[];
  metrics: {
    requestsPerSecond: number;
    errorRate: number;
    throughput: number;
    availability: number;
  };
}

interface SystemResource {
  id: string;
  name: string;
  type: 'cpu' | 'memory' | 'disk' | 'network' | 'bandwidth' | 'storage' | 'database' | 'cache';
  usage: number; // percentage
  total: number;
  used: number;
  available: number;
  unit: string;
  threshold: {
    warning: number;
    critical: number;
  };
  trend: 'up' | 'down' | 'stable';
  history: Array<{
    timestamp: string;
    value: number;
  }>;
}

interface SystemAlert {
  id: string;
  type: 'highCpuUsage' | 'highMemoryUsage' | 'diskSpaceLow' | 'serviceDown' | 'highErrorRate' | 'slowResponse' | 'connectionIssues' | 'securityBreach' | 'unusualActivity' | 'maintenanceRequired';
  severity: 'info' | 'warning' | 'critical' | 'emergency';
  title: string;
  message: string;
  timestamp: string;
  acknowledged: boolean;
  resolved: boolean;
  serviceId?: string;
  resourceId?: string;
  metadata?: any;
}

interface SystemMonitoringProps {
  className?: string;
}

export default function SystemMonitoring({ className = '' }: SystemMonitoringProps) {
  const { t, language, isRTL, getDirectionClasses, formatNumber, formatCurrency, formatDate, formatRelativeTime } = useAdminTranslation();
  const dirClasses = getDirectionClasses();
  
  const [activeTab, setActiveTab] = useState<'overview' | 'services' | 'resources' | 'alerts' | 'logs' | 'diagnostics'>('overview');
  const [timeRange, setTimeRange] = useState('last24Hours');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds
  const [selectedService, setSelectedService] = useState<SystemService | null>(null);
  const [selectedResource, setSelectedResource] = useState<SystemResource | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  // Mock data - replace with real data
  const services: SystemService[] = [
    {
      id: 'database',
      name: 'PostgreSQL Database',
      type: 'database',
      status: 'healthy',
      uptime: 99.9,
      responseTime: 15,
      lastCheck: '2024-01-20T10:00:00Z',
      version: '14.2',
      endpoint: 'localhost:5432',
      port: 5432,
      dependencies: [],
      metrics: {
        requestsPerSecond: 450,
        errorRate: 0.1,
        throughput: 2500,
        availability: 99.9
      }
    },
    {
      id: 'api',
      name: 'REST API Server',
      type: 'api',
      status: 'healthy',
      uptime: 99.8,
      responseTime: 120,
      lastCheck: '2024-01-20T10:00:00Z',
      version: '2.1.0',
      endpoint: 'api.ikaros.com',
      port: 443,
      dependencies: ['database', 'cache'],
      metrics: {
        requestsPerSecond: 1200,
        errorRate: 0.3,
        throughput: 5000,
        availability: 99.8
      }
    },
    {
      id: 'blockchain',
      name: 'Blockchain Node',
      type: 'blockchain',
      status: 'warning',
      uptime: 98.5,
      responseTime: 800,
      lastCheck: '2024-01-20T10:00:00Z',
      version: '1.0.0',
      endpoint: 'node.ikaros.com',
      port: 8545,
      dependencies: [],
      metrics: {
        requestsPerSecond: 50,
        errorRate: 1.2,
        throughput: 200,
        availability: 98.5
      }
    },
    {
      id: 'cache',
      name: 'Redis Cache',
      type: 'cache',
      status: 'healthy',
      uptime: 99.95,
      responseTime: 5,
      lastCheck: '2024-01-20T10:00:00Z',
      version: '7.0.0',
      endpoint: 'localhost:6379',
      port: 6379,
      dependencies: [],
      metrics: {
        requestsPerSecond: 2000,
        errorRate: 0.05,
        throughput: 8000,
        availability: 99.95
      }
    },
    {
      id: 'queue',
      name: 'Message Queue',
      type: 'queue',
      status: 'critical',
      uptime: 95.2,
      responseTime: 200,
      lastCheck: '2024-01-20T10:00:00Z',
      version: '3.8.0',
      endpoint: 'queue.ikaros.com',
      port: 5672,
      dependencies: [],
      metrics: {
        requestsPerSecond: 300,
        errorRate: 2.5,
        throughput: 1000,
        availability: 95.2
      }
    }
  ];

  const resources: SystemResource[] = [
    {
      id: 'cpu',
      name: 'CPU Usage',
      type: 'cpu',
      usage: 65,
      total: 100,
      used: 65,
      available: 35,
      unit: '%',
      threshold: { warning: 70, critical: 85 },
      trend: 'up',
      history: []
    },
    {
      id: 'memory',
      name: 'Memory Usage',
      type: 'memory',
      usage: 78,
      total: 32,
      used: 25,
      available: 7,
      unit: 'GB',
      threshold: { warning: 80, critical: 90 },
      trend: 'stable',
      history: []
    },
    {
      id: 'disk',
      name: 'Disk Usage',
      type: 'disk',
      usage: 45,
      total: 1000,
      used: 450,
      available: 550,
      unit: 'GB',
      threshold: { warning: 80, critical: 90 },
      trend: 'up',
      history: []
    },
    {
      id: 'network',
      name: 'Network Usage',
      type: 'network',
      usage: 35,
      total: 1000,
      used: 350,
      available: 650,
      unit: 'Mbps',
      threshold: { warning: 70, critical: 85 },
      trend: 'down',
      history: []
    }
  ];

  const alerts: SystemAlert[] = [
    {
      id: '1',
      type: 'highCpuUsage',
      severity: 'warning',
      title: 'High CPU Usage Detected',
      message: 'CPU usage has exceeded 65% for the last 10 minutes',
      timestamp: '2024-01-20T10:00:00Z',
      acknowledged: false,
      resolved: false,
      resourceId: 'cpu'
    },
    {
      id: '2',
      type: 'serviceDown',
      severity: 'critical',
      title: 'Message Queue Service Critical',
      message: 'Message queue service is experiencing high error rates and degraded performance',
      timestamp: '2024-01-20T09:45:00Z',
      acknowledged: true,
      resolved: false,
      serviceId: 'queue'
    },
    {
      id: '3',
      type: 'slowResponse',
      severity: 'warning',
      title: 'Blockchain Node Slow Response',
      message: 'Blockchain node response time has increased to 800ms',
      timestamp: '2024-01-20T09:30:00Z',
      acknowledged: false,
      resolved: false,
      serviceId: 'blockchain'
    }
  ];

  const systemStats = {
    overallHealth: 85, // percentage
    totalServices: services.length,
    healthyServices: services.filter(s => s.status === 'healthy').length,
    warningServices: services.filter(s => s.status === 'warning').length,
    criticalServices: services.filter(s => s.status === 'critical').length,
    downServices: services.filter(s => s.status === 'down').length,
    averageUptime: services.reduce((sum, s) => sum + s.uptime, 0) / services.length,
    averageResponseTime: services.reduce((sum, s) => sum + s.responseTime, 0) / services.length,
    totalAlerts: alerts.length,
    criticalAlerts: alerts.filter(a => a.severity === 'critical').length,
    unacknowledgedAlerts: alerts.filter(a => !a.acknowledged).length
  };

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        // Refresh data
        console.log('Refreshing system data...');
      }, refreshInterval * 1000);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const handleServiceAction = (serviceId: string, action: string) => {
    console.log(`Action ${action} for service ${serviceId}`);
    // Implement service actions
  };

  const handleAlertAction = (alertId: string, action: string) => {
    console.log(`Action ${action} for alert ${alertId}`);
    // Implement alert actions
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'green';
      case 'warning': return 'yellow';
      case 'critical': return 'red';
      case 'down': return 'red';
      case 'maintenance': return 'blue';
      case 'degraded': return 'orange';
      case 'recovering': return 'purple';
      default: return 'gray';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'info': return 'blue';
      case 'warning': return 'yellow';
      case 'critical': return 'red';
      case 'emergency': return 'red';
      default: return 'gray';
    }
  };

  const getServiceIcon = (type: string) => {
    switch (type) {
      case 'database': return Database;
      case 'api': return Globe;
      case 'blockchain': return Network;
      case 'cache': return Zap;
      case 'queue': return Layers;
      case 'storage': return HardDrive;
      case 'email': return FileText;
      case 'sms': return FileText;
      case 'websocket': return Wifi;
      case 'cdn': return Cloud;
      case 'loadBalancer': return Target;
      case 'monitoring': return Monitor;
      default: return Server;
    }
  };

  const getResourceIcon = (type: string) => {
    switch (type) {
      case 'cpu': return Cpu;
      case 'memory': return MemoryStick;
      case 'disk': return HardDrive;
      case 'network': return Wifi;
      case 'bandwidth': return Signal;
      case 'storage': return HardDrive;
      case 'database': return Database;
      case 'cache': return Zap;
      default: return Activity;
    }
  };

  const StatusBadge = ({ status }: { status: string }) => {
    const color = getStatusColor(status);
    const StatusIcon = status === 'healthy' ? CheckCircle : 
                      status === 'warning' ? AlertTriangle :
                      status === 'critical' || status === 'down' ? XCircle :
                      status === 'maintenance' ? Settings :
                      status === 'recovering' ? RefreshCw : Clock;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${color}-100 text-${color}-800 dark:bg-${color}-900/30 dark:text-${color}-400`}>
        <StatusIcon className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'} ${status === 'recovering' ? 'animate-spin' : ''}`} />
        {t(`monitoring.status.${status}`)}
      </span>
    );
  };

  const SeverityBadge = ({ severity }: { severity: string }) => {
    const color = getSeverityColor(severity);
    const SeverityIcon = severity === 'info' ? Info : 
                        severity === 'warning' ? AlertTriangle :
                        severity === 'critical' || severity === 'emergency' ? AlertCircle : Info;
    
    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-${color}-100 text-${color}-800 dark:bg-${color}-900/30 dark:text-${color}-400`}>
        <SeverityIcon className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
        {severity.charAt(0).toUpperCase() + severity.slice(1)}
      </span>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className={`flex flex-col sm:flex-row sm:items-center justify-between gap-4 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
        <div className={isRTL ? 'text-right' : 'text-left'}>
          <h3 className={`text-xl font-bold text-gray-900 dark:text-white flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Activity className={`w-6 h-6 text-blue-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            {t('monitoring.title')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('monitoring.subtitle')}</p>
        </div>
        <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <label className="text-sm text-gray-600 dark:text-gray-400">Auto Refresh:</label>
            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`p-1 rounded transition-colors ${autoRefresh ? 'text-green-600 dark:text-green-400' : 'text-gray-400'}`}
            >
              {autoRefresh ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
            </button>
          </div>
          <button 
            onClick={() => console.log('Manual refresh')}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('monitoring.actions.refresh')}
          </button>
          <button className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
            <Download className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            Export Report
          </button>
        </div>
      </div>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Overall Health</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{systemStats.overallHealth}%</p>
            </div>
            <Gauge className={`w-8 h-8 ${systemStats.overallHealth >= 90 ? 'text-green-500' : systemStats.overallHealth >= 70 ? 'text-yellow-500' : 'text-red-500'}`} />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Healthy Services</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">{systemStats.healthyServices}/{systemStats.totalServices}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Critical Alerts</p>
              <p className="text-2xl font-bold text-red-600 dark:text-red-400">{systemStats.criticalAlerts}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Uptime</p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{systemStats.averageUptime.toFixed(1)}%</p>
            </div>
            <TrendingUp className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Response</p>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">{Math.round(systemStats.averageResponseTime)}ms</p>
            </div>
            <Timer className="w-8 h-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Unack. Alerts</p>
              <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">{systemStats.unacknowledgedAlerts}</p>
            </div>
            <AlertCircle className="w-8 h-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className={`flex space-x-8 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          <button
            onClick={() => setActiveTab('overview')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'overview'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Activity className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('monitoring.overview')}
            </div>
          </button>
          <button
            onClick={() => setActiveTab('services')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'services'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Server className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('monitoring.services')} ({services.length})
            </div>
          </button>
          <button
            onClick={() => setActiveTab('resources')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'resources'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Cpu className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('monitoring.resources')} ({resources.length})
            </div>
          </button>
          <button
            onClick={() => setActiveTab('alerts')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'alerts'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <AlertTriangle className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('monitoring.alerts')} ({alerts.length})
            </div>
          </button>
          <button
            onClick={() => setActiveTab('logs')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'logs'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <FileText className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('monitoring.logs')}
            </div>
          </button>
          <button
            onClick={() => setActiveTab('diagnostics')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'diagnostics'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Wrench className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('monitoring.diagnostics')}
            </div>
          </button>
        </nav>
      </div>

      {/* Services Tab */}
      {activeTab === 'services' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    Service & Type
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    Status & Health
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    Performance
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    Metrics
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {services.map((service) => {
                  const ServiceIcon = getServiceIcon(service.type);
                  return (
                    <tr key={service.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <td className="py-4 px-4">
                        <div className={isRTL ? 'text-right' : 'text-left'}>
                          <div className={`font-medium text-gray-900 dark:text-white mb-1 flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <ServiceIcon className={`w-4 h-4 text-gray-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                            {service.name}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-300">
                            {t(`monitoring.services.${service.type}`)} • v{service.version}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {service.endpoint}
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={`space-y-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                          <StatusBadge status={service.status} />
                          <div className="text-sm text-gray-600 dark:text-gray-300">
                            Uptime: {service.uptime.toFixed(2)}%
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Last check: {formatRelativeTime(service.lastCheck)}
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={isRTL ? 'text-right' : 'text-left'}>
                          <div className="text-sm text-gray-900 dark:text-white">
                            Response: {service.responseTime}ms
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-300">
                            RPS: {formatNumber(service.metrics.requestsPerSecond)}
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-300">
                            Error Rate: {service.metrics.errorRate}%
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={isRTL ? 'text-right' : 'text-left'}>
                          <div className="text-sm text-gray-900 dark:text-white">
                            Throughput: {formatNumber(service.metrics.throughput)}
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-300">
                            Availability: {service.metrics.availability}%
                          </div>
                          {service.dependencies.length > 0 && (
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              Deps: {service.dependencies.join(', ')}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <button
                            onClick={() => {
                              setSelectedService(service);
                              setShowDetails(true);
                            }}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 p-1 rounded transition-colors"
                            title="View Details"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleServiceAction(service.id, 'restart')}
                            className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 p-1 rounded transition-colors"
                            title={t('monitoring.actions.restart')}
                          >
                            <RotateCcw className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleServiceAction(service.id, 'diagnose')}
                            className="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 p-1 rounded transition-colors"
                            title={t('monitoring.actions.diagnose')}
                          >
                            <Bug className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleServiceAction(service.id, 'stop')}
                            className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 p-1 rounded transition-colors"
                            title={t('monitoring.actions.stop')}
                          >
                            <Square className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Resources Tab */}
      {activeTab === 'resources' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {resources.map((resource) => {
            const ResourceIcon = getResourceIcon(resource.type);
            const usageColor = resource.usage >= resource.threshold.critical ? 'red' :
                              resource.usage >= resource.threshold.warning ? 'yellow' : 'green';
            const TrendIcon = resource.trend === 'up' ? ArrowUp :
                             resource.trend === 'down' ? ArrowDown : Minus;

            return (
              <div key={resource.id} className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <ResourceIcon className={`w-5 h-5 text-gray-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    <h4 className="font-medium text-gray-900 dark:text-white">{resource.name}</h4>
                  </div>
                  <TrendIcon className={`w-4 h-4 ${
                    resource.trend === 'up' ? 'text-red-500' :
                    resource.trend === 'down' ? 'text-green-500' : 'text-gray-500'
                  }`} />
                </div>

                <div className="space-y-3">
                  <div>
                    <div className={`flex justify-between text-sm mb-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">Usage</span>
                      <span className={`font-medium text-${usageColor}-600 dark:text-${usageColor}-400`}>
                        {resource.usage}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className={`bg-${usageColor}-500 h-2 rounded-full transition-all duration-300`}
                        style={{ width: `${resource.usage}%` }}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className={isRTL ? 'text-right' : 'text-left'}>
                      <span className="text-gray-600 dark:text-gray-400">Used:</span>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {formatNumber(resource.used)} {resource.unit}
                      </div>
                    </div>
                    <div className={isRTL ? 'text-right' : 'text-left'}>
                      <span className="text-gray-600 dark:text-gray-400">Available:</span>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {formatNumber(resource.available)} {resource.unit}
                      </div>
                    </div>
                  </div>

                  <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
                    <div className={`flex justify-between text-xs ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-500 dark:text-gray-400">Warning: {resource.threshold.warning}%</span>
                      <span className="text-gray-500 dark:text-gray-400">Critical: {resource.threshold.critical}%</span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Alerts Tab */}
      {activeTab === 'alerts' && (
        <div className="space-y-4">
          {alerts.map((alert) => (
            <div key={alert.id} className={`bg-white dark:bg-gray-800 rounded-lg p-4 border-l-4 border-${getSeverityColor(alert.severity)}-500`}>
              <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                  <div className={`flex items-center gap-3 mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <h4 className="font-medium text-gray-900 dark:text-white">{alert.title}</h4>
                    <SeverityBadge severity={alert.severity} />
                    {alert.acknowledged && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                        <CheckSquare className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                        Acknowledged
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">{alert.message}</p>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {formatRelativeTime(alert.timestamp)} • {formatDate(alert.timestamp)}
                  </div>
                </div>
                <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  {!alert.acknowledged && (
                    <button
                      onClick={() => handleAlertAction(alert.id, 'acknowledge')}
                      className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors"
                    >
                      Acknowledge
                    </button>
                  )}
                  {!alert.resolved && (
                    <button
                      onClick={() => handleAlertAction(alert.id, 'resolve')}
                      className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors"
                    >
                      Resolve
                    </button>
                  )}
                  <button
                    onClick={() => handleAlertAction(alert.id, 'dismiss')}
                    className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Logs Tab */}
      {activeTab === 'logs' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">System Logs</h4>
            <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <select className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm">
                <option value="all">All Logs</option>
                <option value="error">Error Logs</option>
                <option value="warning">Warning Logs</option>
                <option value="info">Info Logs</option>
              </select>
              <button className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors">
                Download
              </button>
            </div>
          </div>
          <div className="bg-gray-900 dark:bg-gray-950 rounded p-4 font-mono text-sm text-green-400 h-96 overflow-y-auto">
            <div className="space-y-1">
              <div>[2024-01-20 10:00:00] INFO: System health check completed successfully</div>
              <div>[2024-01-20 09:58:30] WARN: CPU usage exceeded 65% threshold</div>
              <div>[2024-01-20 09:55:15] ERROR: Message queue connection timeout</div>
              <div>[2024-01-20 09:50:00] INFO: Database backup completed</div>
              <div>[2024-01-20 09:45:22] WARN: Blockchain node response time increased</div>
              <div>[2024-01-20 09:40:10] INFO: Cache cleared successfully</div>
              <div>[2024-01-20 09:35:45] ERROR: Failed to send notification email</div>
              <div>[2024-01-20 09:30:00] INFO: Scheduled maintenance task started</div>
            </div>
          </div>
        </div>
      )}

      {/* Diagnostics Tab */}
      {activeTab === 'diagnostics' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">System Information</h4>
            <div className="space-y-2 text-sm">
              <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <span className="text-gray-600 dark:text-gray-400">OS:</span>
                <span className="text-gray-900 dark:text-white">Ubuntu 22.04 LTS</span>
              </div>
              <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <span className="text-gray-600 dark:text-gray-400">Kernel:</span>
                <span className="text-gray-900 dark:text-white">5.15.0-91</span>
              </div>
              <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <span className="text-gray-600 dark:text-gray-400">Architecture:</span>
                <span className="text-gray-900 dark:text-white">x86_64</span>
              </div>
              <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <span className="text-gray-600 dark:text-gray-400">CPU Cores:</span>
                <span className="text-gray-900 dark:text-white">8</span>
              </div>
              <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <span className="text-gray-600 dark:text-gray-400">Total RAM:</span>
                <span className="text-gray-900 dark:text-white">32 GB</span>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Health Checks</h4>
            <div className="space-y-3">
              <button className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors">
                Run Full Health Check
              </button>
              <button className="w-full px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors">
                Test Database Connection
              </button>
              <button className="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded transition-colors">
                Check API Endpoints
              </button>
              <button className="w-full px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded transition-colors">
                Validate Blockchain Sync
              </button>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Performance Tests</h4>
            <div className="space-y-3">
              <button className="w-full px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded transition-colors">
                Load Test API
              </button>
              <button className="w-full px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white rounded transition-colors">
                Stress Test Database
              </button>
              <button className="w-full px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded transition-colors">
                Network Latency Test
              </button>
              <button className="w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors">
                Security Scan
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
