<?php
/**
 * API endpoint لإدارة التقييمات
 * Reviews Management API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = new Database();
    $connection = $db->connect();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $userId = $_GET['user_id'] ?? null;
        $targetUserId = $_GET['target_user_id'] ?? null;
        $type = $_GET['type'] ?? 'all'; // received, given, all
        $getAllReviews = $_GET['all'] ?? false;

        if ($getAllReviews) {
            // جلب جميع التقييمات (للاختبار والإدارة)
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
            $offset = ($page - 1) * $limit;

            // الحصول على العدد الإجمالي
            $countStmt = $connection->prepare("SELECT COUNT(*) as total FROM reviews");
            $countStmt->execute();
            $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

            // جلب التقييمات
            $stmt = $connection->prepare("
                SELECT
                    r.*,
                    reviewer.username as reviewer_username,
                    reviewer.full_name as reviewer_name,
                    reviewer.is_verified as reviewer_verified,
                    reviewed.username as reviewed_username,
                    reviewed.full_name as reviewed_name,
                    reviewed.is_verified as reviewed_verified
                FROM reviews r
                JOIN users reviewer ON r.reviewer_id = reviewer.id
                JOIN users reviewed ON r.reviewed_id = reviewed.id
                ORDER BY r.created_at DESC
                LIMIT ? OFFSET ?
            ");

            $stmt->execute([$limit, $offset]);
            $reviews = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // تنسيق البيانات
            foreach ($reviews as &$review) {
                $review['reviewer'] = [
                    'id' => $review['reviewer_id'],
                    'username' => $review['reviewer_username'],
                    'full_name' => $review['reviewer_name'],
                    'is_verified' => (bool)$review['reviewer_verified']
                ];

                $review['reviewed_user'] = [
                    'id' => $review['reviewed_user_id'],
                    'username' => $review['reviewed_username'],
                    'full_name' => $review['reviewed_name'],
                    'is_verified' => (bool)$review['reviewed_verified']
                ];

                // إزالة البيانات المكررة
                unset($review['reviewer_username'], $review['reviewer_name'],
                      $review['reviewer_verified'], $review['reviewed_username'],
                      $review['reviewed_name'], $review['reviewed_verified']);
            }

            echo json_encode([
                'success' => true,
                'data' => $reviews,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => intval($totalCount),
                    'total_pages' => ceil($totalCount / $limit)
                ]
            ]);

        } elseif ($targetUserId) {
            // جلب تقييمات مستخدم محدد
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
            $offset = ($page - 1) * $limit;
            
            // الحصول على العدد الإجمالي
            $countStmt = $connection->prepare("
                SELECT COUNT(*) as total
                FROM reviews r
                WHERE r.reviewed_id = ?
            ");
            $countStmt->execute([$targetUserId]);
            $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // جلب التقييمات
            $stmt = $connection->prepare("
                SELECT 
                    r.*,
                    reviewer.username as reviewer_username,
                    reviewer.full_name as reviewer_name,
                    reviewer.is_verified as reviewer_verified,
                    t.id as trade_id,
                    t.amount as trade_amount,
                    t.currency as trade_currency
                FROM reviews r
                JOIN users reviewer ON r.reviewer_id = reviewer.id
                LEFT JOIN trades t ON r.trade_id = t.id
                WHERE r.reviewed_id = ?
                ORDER BY r.created_at DESC
                LIMIT ? OFFSET ?
            ");
            
            $stmt->execute([$targetUserId, $limit, $offset]);
            $reviews = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // تنسيق البيانات
            foreach ($reviews as &$review) {
                $review['reviewer'] = [
                    'id' => $review['reviewer_id'],
                    'username' => $review['reviewer_username'],
                    'full_name' => $review['reviewer_name'],
                    'is_verified' => (bool)$review['reviewer_verified']
                ];
                
                if ($review['trade_id']) {
                    $review['trade'] = [
                        'id' => $review['trade_id'],
                        'amount' => floatval($review['trade_amount']),
                        'currency' => $review['trade_currency']
                    ];
                }
                
                // إزالة البيانات المكررة
                unset($review['reviewer_username'], $review['reviewer_name'], 
                      $review['reviewer_verified'], $review['trade_id'], 
                      $review['trade_amount'], $review['trade_currency']);
            }
            
            echo json_encode([
                'success' => true,
                'data' => $reviews,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => intval($totalCount),
                    'total_pages' => ceil($totalCount / $limit)
                ]
            ]);
            
        } elseif ($userId) {
            // جلب تقييمات المستخدم (المُستلمة أو المُرسلة)
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
            $offset = ($page - 1) * $limit;
            
            $whereCondition = '';
            $params = [$userId];
            
            if ($type === 'received') {
                $whereCondition = 'r.reviewed_id = ?';
            } elseif ($type === 'given') {
                $whereCondition = 'r.reviewer_id = ?';
            } else {
                $whereCondition = '(r.reviewed_id = ? OR r.reviewer_id = ?)';
                $params[] = $userId;
            }
            
            // الحصول على العدد الإجمالي
            $countStmt = $connection->prepare("
                SELECT COUNT(*) as total
                FROM reviews r
                WHERE $whereCondition
            ");
            $countStmt->execute($params);
            $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // جلب التقييمات
            $stmt = $connection->prepare("
                SELECT 
                    r.*,
                    reviewer.username as reviewer_username,
                    reviewer.full_name as reviewer_name,
                    reviewer.is_verified as reviewer_verified,
                    reviewed.username as reviewed_username,
                    reviewed.full_name as reviewed_name,
                    reviewed.is_verified as reviewed_verified,
                    t.id as trade_id,
                    t.amount as trade_amount,
                    t.currency as trade_currency
                FROM reviews r
                JOIN users reviewer ON r.reviewer_id = reviewer.id
                JOIN users reviewed ON r.reviewed_id = reviewed.id
                LEFT JOIN trades t ON r.trade_id = t.id
                WHERE $whereCondition
                ORDER BY r.created_at DESC
                LIMIT ? OFFSET ?
            ");
            
            $params[] = $limit;
            $params[] = $offset;
            $stmt->execute($params);
            $reviews = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // تنسيق البيانات
            foreach ($reviews as &$review) {
                $review['reviewer'] = [
                    'id' => $review['reviewer_id'],
                    'username' => $review['reviewer_username'],
                    'full_name' => $review['reviewer_name'],
                    'is_verified' => (bool)$review['reviewer_verified']
                ];
                
                $review['reviewed_user'] = [
                    'id' => $review['reviewed_id'],
                    'username' => $review['reviewed_username'],
                    'full_name' => $review['reviewed_name'],
                    'is_verified' => (bool)$review['reviewed_verified']
                ];
                
                if ($review['trade_id']) {
                    $review['trade'] = [
                        'id' => $review['trade_id'],
                        'amount' => floatval($review['trade_amount']),
                        'currency' => $review['trade_currency']
                    ];
                }
                
                // إزالة البيانات المكررة
                unset($review['reviewer_username'], $review['reviewer_name'], 
                      $review['reviewer_verified'], $review['reviewed_username'], 
                      $review['reviewed_name'], $review['reviewed_verified'],
                      $review['trade_id'], $review['trade_amount'], $review['trade_currency']);
            }
            
            echo json_encode([
                'success' => true,
                'data' => $reviews,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => intval($totalCount),
                    'total_pages' => ceil($totalCount / $limit)
                ]
            ]);
            
        } else {
            throw new Exception('معرف المستخدم مطلوب');
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // إضافة تقييم جديد
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $requiredFields = ['reviewer_id', 'reviewed_id', 'trade_id', 'rating', 'comment'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                throw new Exception("الحقل $field مطلوب");
            }
        }
        
        // التحقق من صحة التقييم
        $rating = intval($input['rating']);
        if ($rating < 1 || $rating > 5) {
            throw new Exception('التقييم يجب أن يكون بين 1 و 5');
        }
        
        // التحقق من أن المراجع لا يقيم نفسه
        if ($input['reviewer_id'] == $input['reviewed_id']) {
            throw new Exception('لا يمكنك تقييم نفسك');
        }
        
        // التحقق من وجود الصفقة وأن المستخدم جزء منها
        $stmt = $connection->prepare("
            SELECT id FROM trades 
            WHERE id = ? AND (seller_id = ? OR buyer_id = ?) AND status = 'completed'
        ");
        $stmt->execute([$input['trade_id'], $input['reviewer_id'], $input['reviewer_id']]);
        
        if (!$stmt->fetch()) {
            throw new Exception('الصفقة غير موجودة أو غير مكتملة');
        }
        
        // التحقق من عدم وجود تقييم سابق لنفس الصفقة
        $stmt = $connection->prepare("
            SELECT id FROM reviews 
            WHERE trade_id = ? AND reviewer_id = ?
        ");
        $stmt->execute([$input['trade_id'], $input['reviewer_id']]);
        
        if ($stmt->fetch()) {
            throw new Exception('لقد قمت بتقييم هذه الصفقة مسبقاً');
        }
        
        // إدراج التقييم
        $stmt = $connection->prepare("
            INSERT INTO reviews (
                reviewer_id, reviewed_id, trade_id, rating,
                comment, created_at
            ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ");

        $stmt->execute([
            $input['reviewer_id'],
            $input['reviewed_id'],
            $input['trade_id'],
            $rating,
            $input['comment']
        ]);
        
        $reviewId = $connection->lastInsertId();
        
        // تحديث متوسط تقييم المستخدم
        $stmt = $connection->prepare("
            UPDATE users
            SET rating = (
                SELECT AVG(rating)
                FROM reviews
                WHERE reviewed_id = ?
            )
            WHERE id = ?
        ");
        $stmt->execute([$input['reviewed_id'], $input['reviewed_id']]);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة التقييم بنجاح',
            'data' => ['id' => $reviewId]
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    error_log('Database error in reviews/index.php: ' . $e->getMessage());
}
?>
