// ثوابت المشروع

// Base URL للـ API
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '/api';

export const APP_CONFIG = {
  name: 'IKAROS P2P',
  nameEn: 'IKAROS P2P',
  description: 'Safe and reliable platform for direct USDT trading between individuals',
  version: '1.0.0',
  author: 'IKAROS Team',
  website: 'https://ikaros-p2p.com',
  supportEmail: '<EMAIL>',
  supportPhone: '+966 50 123 4567'
};

export const SUPPORTED_COUNTRIES = [
  { code: 'SA', name: 'Saudi Arabia', nameKey: 'countries.saudiArabia', currency: 'SAR', flag: '🇸🇦' },
  { code: 'AE', name: 'United Arab Emirates', nameKey: 'countries.uae', currency: 'AED', flag: '🇦🇪' },
  { code: 'KW', name: 'Kuwait', nameKey: 'countries.kuwait', currency: 'KWD', flag: '🇰🇼' },
  { code: 'QA', name: 'Qatar', nameKey: 'countries.qatar', currency: 'QAR', flag: '🇶🇦' },
  { code: 'BH', name: 'Bahrain', nameKey: 'countries.bahrain', currency: 'BHD', flag: '🇧🇭' },
  { code: 'OM', name: 'Oman', nameKey: 'countries.oman', currency: 'OMR', flag: '🇴🇲' },
  { code: 'JO', name: 'Jordan', nameKey: 'countries.jordan', currency: 'JOD', flag: '🇯🇴' },
  { code: 'LB', name: 'Lebanon', nameKey: 'countries.lebanon', currency: 'LBP', flag: '🇱🇧' },
  { code: 'EG', name: 'Egypt', nameKey: 'countries.egypt', currency: 'EGP', flag: '🇪🇬' },
  { code: 'MA', name: 'Morocco', nameKey: 'countries.morocco', currency: 'MAD', flag: '🇲🇦' }
];

// العملات المستقرة المدعومة (مرتبة حسب الأهمية)
export const SUPPORTED_STABLECOINS = [
  {
    symbol: 'USDT',
    name: 'Tether USD',
    nameAr: 'تيثر دولار أمريكي',
    icon: '₮',
    priority: 1,
    description: 'العملة المستقرة الأكثر تداولاً في العالم',
    descriptionEn: 'The most traded stablecoin in the world',
    network: 'BSC',
    contractAddress: '******************************************'
  },
  {
    symbol: 'USDC',
    name: 'USD Coin',
    nameAr: 'عملة الدولار الأمريكي',
    icon: '🪙',
    priority: 2,
    description: 'عملة مستقرة مدعومة بالكامل بالدولار الأمريكي',
    descriptionEn: 'Fully backed USD stablecoin',
    network: 'BSC',
    contractAddress: '******************************************'
  },
  {
    symbol: 'BUSD',
    name: 'Binance USD',
    nameAr: 'بينانس دولار أمريكي',
    icon: '🟡',
    priority: 3,
    description: 'العملة المستقرة الرسمية لمنصة بينانس',
    descriptionEn: 'Official Binance stablecoin',
    network: 'BSC',
    contractAddress: '******************************************'
  },
  {
    symbol: 'DAI',
    name: 'Dai Stablecoin',
    nameAr: 'داي عملة مستقرة',
    icon: '◈',
    priority: 4,
    description: 'عملة مستقرة لامركزية مدعومة بضمانات',
    descriptionEn: 'Decentralized collateral-backed stablecoin',
    network: 'BSC',
    contractAddress: '******************************************'
  },
  {
    symbol: 'FDUSD',
    name: 'First Digital USD',
    nameAr: 'فيرست ديجيتال دولار أمريكي',
    icon: '💎',
    priority: 5,
    description: 'عملة مستقرة جديدة مدعومة بالدولار',
    descriptionEn: 'New USD-backed stablecoin',
    network: 'BSC',
    contractAddress: '0xc5f0f7b66764F6ec8C8Dff7BA683102295E16409'
  },
  {
    symbol: 'TUSD',
    name: 'TrueUSD',
    nameAr: 'ترو دولار أمريكي',
    icon: '🔷',
    priority: 6,
    description: 'عملة مستقرة موثوقة مع شفافية كاملة',
    descriptionEn: 'Trusted stablecoin with full transparency',
    network: 'BSC',
    contractAddress: '0x40af3827F39D0EAcBF4A168f8D4ee67c121D11c9'
  },
  {
    symbol: 'USDD',
    name: 'USDD',
    nameAr: 'يو إس دي دي',
    icon: '🔺',
    priority: 7,
    description: 'عملة مستقرة لامركزية من ترون',
    descriptionEn: 'Decentralized stablecoin from TRON',
    network: 'BSC',
    contractAddress: '0xd17479997F34dd9156Deef8F95A52D81D265be9c'
  },
  {
    symbol: 'FRAX',
    name: 'Frax',
    nameAr: 'فراكس',
    icon: '🔶',
    priority: 8,
    description: 'عملة مستقرة خوارزمية جزئياً',
    descriptionEn: 'Fractional-algorithmic stablecoin',
    network: 'BSC',
    contractAddress: '0x90C97F71E18723b0Cf0dfa30ee176Ab653E89F40'
  },
  {
    symbol: 'USDP',
    name: 'Pax Dollar',
    nameAr: 'باكس دولار',
    icon: '🔸',
    priority: 9,
    description: 'عملة مستقرة منظمة من باكسوس',
    descriptionEn: 'Regulated stablecoin by Paxos',
    network: 'BSC',
    contractAddress: '******************************************'
  },
  {
    symbol: 'LUSD',
    name: 'Liquity USD',
    nameAr: 'ليكويتي دولار أمريكي',
    icon: '🔹',
    priority: 10,
    description: 'عملة مستقرة لامركزية مدعومة بالإيثيريوم',
    descriptionEn: 'Decentralized stablecoin backed by ETH',
    network: 'BSC',
    contractAddress: '******************************************'
  },
  {
    symbol: 'GUSD',
    name: 'Gemini Dollar',
    nameAr: 'جيميني دولار',
    icon: '💠',
    priority: 11,
    description: 'عملة مستقرة منظمة من جيميني',
    descriptionEn: 'Regulated stablecoin by Gemini',
    network: 'BSC',
    contractAddress: '******************************************'
  },
  {
    symbol: 'SUSD',
    name: 'Synthetix USD',
    nameAr: 'سينثيتكس دولار أمريكي',
    icon: '⚡',
    priority: 12,
    description: 'عملة مستقرة اصطناعية من سينثيتكس',
    descriptionEn: 'Synthetic stablecoin from Synthetix',
    network: 'BSC',
    contractAddress: '******************************************'
  },
  {
    symbol: 'USTC',
    name: 'TerraClassicUSD',
    nameAr: 'تيرا كلاسيك دولار أمريكي',
    icon: '🌍',
    priority: 13,
    description: 'عملة مستقرة من تيرا كلاسيك',
    descriptionEn: 'Stablecoin from Terra Classic',
    network: 'BSC',
    contractAddress: '******************************************'
  },
  {
    symbol: 'PYUSD',
    name: 'PayPal USD',
    nameAr: 'باي بال دولار أمريكي',
    icon: '💳',
    priority: 14,
    description: 'عملة مستقرة من باي بال',
    descriptionEn: 'Stablecoin by PayPal',
    network: 'BSC',
    contractAddress: '******************************************'
  }
];

// طرق الدفع الذكية حسب نوع العرض
export const PAYMENT_METHODS_CONFIG = {
  // طرق الدفع للبيع (البائع يستلم المال)
  sell: [
    {
      id: 'bank_transfer',
      name: 'Receive Bank Transfer',
      nameAr: 'استلام تحويل بنكي',
      icon: '🏦',
      description: 'استلام تحويل بنكي',
      processingTime: '1-3 أيام',
      fees: 'مجاني',
      popularity: 95
    },
    {
      id: 'quick_transfer',
      name: 'Receive Quick Transfer',
      nameAr: 'استلام تحويل سريع',
      icon: '⚡',
      description: 'استلام تحويل سريع',
      processingTime: '15-30 دقيقة',
      fees: 'منخفضة',
      popularity: 85
    },
    {
      id: 'instant_transfer',
      name: 'Receive Instant Transfer',
      nameAr: 'استلام تحويل فوري',
      icon: '🚀',
      description: 'استلام تحويل فوري',
      processingTime: '1-5 دقائق',
      fees: 'متوسطة',
      popularity: 75
    },
    {
      id: 'cash_deposit',
      name: 'Cash Deposit',
      nameAr: 'إيداع نقدي',
      icon: '💵',
      description: 'إيداع نقدي في البنك',
      processingTime: 'فوري',
      fees: 'مجاني',
      popularity: 60
    },
    {
      id: 'mobile_wallet',
      name: 'Receive via Mobile Wallet',
      nameAr: 'استلام عبر المحفظة الرقمية',
      icon: '📱',
      description: 'استلام عبر المحفظة الرقمية',
      processingTime: 'فوري',
      fees: 'منخفضة',
      popularity: 80
    },
    {
      id: 'paypal',
      name: 'Receive via PayPal',
      nameAr: 'استلام عبر باي بال',
      icon: '💙',
      description: 'استلام عبر PayPal',
      processingTime: 'فوري',
      fees: 'متوسطة',
      popularity: 70
    },
    {
      id: 'wise',
      name: 'Receive via Wise',
      nameAr: 'استلام عبر وايز',
      icon: '🌐',
      description: 'استلام عبر Wise',
      processingTime: '1-2 أيام',
      fees: 'منخفضة',
      popularity: 65
    },
    {
      id: 'stc_pay',
      name: 'Receive via STC Pay',
      nameAr: 'استلام عبر STC Pay',
      icon: '📱',
      description: 'استلام عبر STC Pay',
      processingTime: 'فوري',
      fees: 'مجاني',
      popularity: 85
    },
    {
      id: 'mada',
      name: 'Receive via Mada',
      nameAr: 'استلام عبر مدى',
      icon: '💳',
      description: 'استلام عبر بطاقة مدى',
      processingTime: 'فوري',
      fees: 'منخفضة',
      popularity: 90
    },
    {
      id: 'urpay',
      name: 'Receive via UrPay',
      nameAr: 'استلام عبر يور باي',
      icon: '💰',
      description: 'استلام عبر UrPay',
      processingTime: 'فوري',
      fees: 'منخفضة',
      popularity: 75
    },
    {
      id: 'alinma_pay',
      name: 'Receive via Alinma Pay',
      nameAr: 'استلام عبر الإنماء باي',
      icon: '🏛️',
      description: 'استلام عبر الإنماء باي',
      processingTime: 'فوري',
      fees: 'مجاني',
      popularity: 82
    },
    {
      id: 'rajhi_bank',
      name: 'Receive via Al Rajhi Bank',
      nameAr: 'استلام عبر بنك الراجحي',
      icon: '🏦',
      description: 'استلام عبر بنك الراجحي',
      processingTime: '15-30 دقيقة',
      fees: 'مجاني',
      popularity: 88
    }
  ],

  // طرق الدفع للشراء (المشتري يرسل المال)
  buy: [
    {
      id: 'bank_transfer',
      name: 'Send Bank Transfer',
      nameAr: 'إرسال تحويل بنكي',
      icon: '🏦',
      description: 'إرسال تحويل بنكي',
      processingTime: '1-3 أيام عمل',
      fees: 'مجاني',
      popularity: 95
    },
    {
      id: 'quick_transfer',
      name: 'Send Quick Transfer',
      nameAr: 'إرسال تحويل سريع',
      icon: '⚡',
      description: 'إرسال تحويل سريع',
      processingTime: '15-30 دقيقة',
      fees: 'منخفضة',
      popularity: 85
    },
    {
      id: 'instant_transfer',
      name: 'Send Instant Transfer',
      nameAr: 'إرسال تحويل فوري',
      icon: '🚀',
      description: 'إرسال تحويل فوري',
      processingTime: '1-5 دقائق',
      fees: 'متوسطة',
      popularity: 75
    },
    {
      id: 'cash_payment',
      name: 'Cash Payment',
      nameAr: 'دفع نقدي مباشر',
      icon: '💵',
      description: 'دفع نقدي مباشر',
      processingTime: 'فوري',
      fees: 'مجاني',
      popularity: 50
    },
    {
      id: 'mobile_wallet',
      name: 'Pay via Mobile Wallet',
      nameAr: 'دفع عبر المحفظة الرقمية',
      icon: '📱',
      description: 'دفع عبر المحفظة الرقمية',
      processingTime: 'فوري',
      fees: 'منخفضة',
      popularity: 80
    },
    {
      id: 'credit_card',
      name: 'Pay with Credit Card',
      nameAr: 'دفع بالبطاقة الائتمانية',
      icon: '💳',
      description: 'دفع بالبطاقة الائتمانية',
      processingTime: 'فوري',
      fees: 'عالية',
      popularity: 60
    },
    {
      id: 'debit_card',
      name: 'Pay with Debit Card',
      nameAr: 'دفع بالبطاقة المصرفية',
      icon: '💳',
      description: 'دفع بالبطاقة المصرفية',
      processingTime: 'فوري',
      fees: 'منخفضة',
      popularity: 70
    },
    {
      id: 'paypal',
      name: 'Pay via PayPal',
      nameAr: 'دفع عبر باي بال',
      icon: '💙',
      description: 'دفع عبر PayPal',
      processingTime: 'فوري',
      fees: 'متوسطة',
      popularity: 70
    },
    {
      id: 'apple_pay',
      name: 'Pay via Apple Pay',
      nameAr: 'دفع عبر آبل باي',
      icon: '🍎',
      description: 'دفع عبر Apple Pay',
      processingTime: 'فوري',
      fees: 'منخفضة',
      popularity: 55
    },
    {
      id: 'google_pay',
      name: 'Pay via Google Pay',
      nameAr: 'دفع عبر جوجل باي',
      icon: '🔴',
      description: 'دفع عبر Google Pay',
      processingTime: 'فوري',
      fees: 'منخفضة',
      popularity: 55
    },
    {
      id: 'stc_pay',
      name: 'Pay via STC Pay',
      nameAr: 'دفع عبر STC Pay',
      icon: '📱',
      description: 'دفع عبر STC Pay',
      processingTime: 'فوري',
      fees: 'مجاني',
      popularity: 85
    },
    {
      id: 'mada',
      name: 'Pay with Mada',
      nameAr: 'دفع بكارت مدى',
      icon: '💳',
      description: 'دفع بكارت مدى',
      processingTime: 'فوري',
      fees: 'منخفضة',
      popularity: 90
    },
    {
      id: 'urpay',
      name: 'Pay via UrPay',
      nameAr: 'دفع عبر يور باي',
      icon: '💰',
      description: 'دفع عبر UrPay',
      processingTime: 'فوري',
      fees: 'منخفضة',
      popularity: 75
    },
    {
      id: 'western_union',
      name: 'Pay via Western Union',
      nameAr: 'دفع عبر ويسترن يونيون',
      icon: '🌍',
      description: 'دفع عبر Western Union',
      processingTime: '1-2 أيام',
      fees: 'عالية',
      popularity: 45
    },
    {
      id: 'money_gram',
      name: 'Pay via MoneyGram',
      nameAr: 'دفع عبر موني جرام',
      icon: '💸',
      description: 'دفع عبر MoneyGram',
      processingTime: '1-2 أيام',
      fees: 'عالية',
      popularity: 40
    },
    {
      id: 'alinma_pay',
      name: 'Pay via Alinma Pay',
      nameAr: 'دفع عبر الإنماء باي',
      icon: '🏛️',
      description: 'دفع عبر الإنماء باي',
      processingTime: 'فوري',
      fees: 'مجاني',
      popularity: 82
    },
    {
      id: 'rajhi_bank',
      name: 'Pay via Al Rajhi Bank',
      nameAr: 'دفع عبر بنك الراجحي',
      icon: '🏦',
      description: 'دفع عبر بنك الراجحي',
      processingTime: '15-30 دقيقة',
      fees: 'مجاني',
      popularity: 88
    }
  ]
};

// دالة للحصول على طرق الدفع حسب نوع العرض
export const getPaymentMethodsByOfferType = (offerType: 'buy' | 'sell') => {
  return PAYMENT_METHODS_CONFIG[offerType].sort((a, b) => b.popularity - a.popularity);
};

// للتوافق مع الكود القديم
export const PAYMENT_METHODS = PAYMENT_METHODS_CONFIG.sell;

export const TRADE_STATUS_KEYS = {
  CREATED: 'created',
  BUYER_JOINED: 'buyerJoined',
  PAYMENT_SENT: 'paymentSent',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  DISPUTED: 'disputed'
};

export const TRADE_STATUS_COLORS = {
  CREATED: 'bg-blue-100 text-blue-800',
  BUYER_JOINED: 'bg-yellow-100 text-yellow-800',
  PAYMENT_SENT: 'bg-orange-100 text-orange-800',
  COMPLETED: 'bg-green-100 text-green-800',
  CANCELLED: 'bg-red-100 text-red-800',
  DISPUTED: 'bg-purple-100 text-purple-800'
};

export const CURRENCY_SYMBOLS = {
  ar: {
    SAR: 'ر.س',
    AED: 'د.إ',
    KWD: 'د.ك',
    QAR: 'ر.ق',
    BHD: 'د.ب',
    OMR: 'ر.ع',
    JOD: 'د.أ',
    LBP: 'ل.ل',
    EGP: 'ج.م',
    MAD: 'د.م',
    USD: '$',
    EUR: '€'
  },
  en: {
    SAR: 'SAR',
    AED: 'AED',
    KWD: 'KWD',
    QAR: 'QAR',
    BHD: 'BHD',
    OMR: 'OMR',
    JOD: 'JOD',
    LBP: 'LBP',
    EGP: 'EGP',
    MAD: 'MAD',
    USD: '$',
    EUR: '€'
  }
};

export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    FORGOT_PASSWORD: '/api/auth/forgot-password',
    RESET_PASSWORD: '/api/auth/reset-password',
    VERIFY_EMAIL: '/api/auth/verify-email'
  },
  USERS: {
    PROFILE: '/api/users/profile',
    UPDATE_PROFILE: '/api/users/profile',
    SETTINGS: '/api/users/settings',
    SECURITY_LOGS: '/api/users/security-logs'
  },
  OFFERS: {
    LIST: '/api/offers',
    CREATE: '/api/offers',
    UPDATE: '/api/offers',
    DELETE: '/api/offers',
    MY_OFFERS: '/api/offers/my-offers',
    SYNC_CONTRACT: '/api/offers/sync-contract'
  },
  TRADES: {
    LIST: '/api/trades',
    CREATE: '/api/trades',
    UPDATE: '/api/trades',
    MESSAGES: '/api/trades/messages',
    CONFIRM_PAYMENT: '/api/trades/confirm-payment',
    COMPLETE: '/api/trades/complete',
    CANCEL: '/api/trades/cancel',
    DISPUTE: '/api/trades/dispute',
    SYNC_CONTRACT: '/api/trades/sync-contract',
    UPDATE_STATUS: '/api/trades/update-status'
  },
  ADMIN: {
    STATS: '/api/admin/stats',
    USERS: '/api/admin/users',
    TRADES: '/api/admin/trades',
    DISPUTES: '/api/admin/disputes'
  },
  CONTRACT: {
    EVENTS: '/api/contract-events',
    LAST_BLOCK: '/api/contract-events/last-block',
    SYNC: '/api/contract/sync'
  }
};

export const VALIDATION_RULES = {
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    MESSAGE_KEY: 'validation.emailInvalid'
  },
  PASSWORD: {
    MIN_LENGTH: 8,
    PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    MESSAGE_KEY: 'validation.passwordRequirements'
  },
  PHONE: {
    PATTERN: /^[+]?[0-9\s\-\(\)]{10,15}$/,
    MESSAGE_KEY: 'validation.phoneInvalid'
  },
  AMOUNT: {
    MIN: 10,
    MAX: 100000,
    MESSAGE_KEY: 'validation.amountRange'
  }
};

export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100
};

export const CACHE_KEYS = {
  USER_PROFILE: 'user_profile',
  OFFERS_LIST: 'offers_list',
  TRADES_LIST: 'trades_list',
  PLATFORM_STATS: 'platform_stats'
};

export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_PREFERENCES: 'user_preferences',
  THEME: 'theme',
  LANGUAGE: 'language'
};

export const WEBSOCKET_EVENTS = {
  TRADE_UPDATED: 'trade_updated',
  MESSAGE_RECEIVED: 'message_received',
  OFFER_UPDATED: 'offer_updated',
  USER_ONLINE: 'user_online',
  USER_OFFLINE: 'user_offline'
};

export const NOTIFICATION_TYPES = {
  TRADE_CREATED: 'trade_created',
  TRADE_UPDATED: 'trade_updated',
  PAYMENT_RECEIVED: 'payment_received',
  TRADE_COMPLETED: 'trade_completed',
  TRADE_CANCELLED: 'trade_cancelled',
  TRADE_DISPUTED: 'trade_disputed',
  MESSAGE_RECEIVED: 'message_received'
};

export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
  TRADE_NOT_FOUND: 'TRADE_NOT_FOUND',
  OFFER_NOT_AVAILABLE: 'OFFER_NOT_AVAILABLE',
  USER_NOT_VERIFIED: 'USER_NOT_VERIFIED',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED'
};

export const SUCCESS_MESSAGE_KEYS = {
  LOGIN_SUCCESS: 'loginSuccess',
  REGISTER_SUCCESS: 'registerSuccess',
  PROFILE_UPDATED: 'profileUpdated',
  OFFER_CREATED: 'offerCreated',
  TRADE_CREATED: 'tradeCreated',
  PAYMENT_CONFIRMED: 'paymentConfirmed',
  TRADE_COMPLETED: 'tradeCompleted'
};

export const ERROR_MESSAGE_KEYS = {
  NETWORK_ERROR: 'errors.networkError',
  UNAUTHORIZED: 'errors.unauthorized',
  FORBIDDEN: 'errors.forbidden',
  NOT_FOUND: 'errors.notFound',
  VALIDATION_ERROR: 'errors.validationError',
  SERVER_ERROR: 'errors.serverError'
};

export const SOCIAL_LINKS = {
  FACEBOOK: 'https://facebook.com/ikaros-p2p',
  TWITTER: 'https://twitter.com/ikaros_p2p',
  INSTAGRAM: 'https://instagram.com/ikaros_p2p',
  LINKEDIN: 'https://linkedin.com/company/ikaros-p2p',
  TELEGRAM: 'https://t.me/ikaros_p2p',
  YOUTUBE: 'https://youtube.com/c/ikaros-p2p'
};

export const LEGAL_LINKS = {
  TERMS_OF_SERVICE: '/terms',
  PRIVACY_POLICY: '/privacy',
  SECURITY_POLICY: '/security',
  DISCLAIMER: '/disclaimer',
  AML_POLICY: '/aml-policy',
  KYC_POLICY: '/kyc-policy'
};

export const FEATURE_FLAGS = {
  ENABLE_2FA: true,
  ENABLE_KYC: true,
  ENABLE_CHAT: true,
  ENABLE_NOTIFICATIONS: true,
  ENABLE_MOBILE_APP: false,
  ENABLE_ADVANCED_TRADING: false
};

export const LIMITS = {
  MAX_OFFERS_PER_USER: 10,
  MAX_ACTIVE_TRADES: 5,
  MAX_MESSAGE_LENGTH: 500,
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  MAX_IMAGES_PER_MESSAGE: 3
};

export const TIMEOUTS = {
  API_REQUEST: 30000, // 30 seconds
  WEBSOCKET_RECONNECT: 5000, // 5 seconds
  NOTIFICATION_DISPLAY: 5000, // 5 seconds
  AUTO_LOGOUT: 30 * 60 * 1000 // 30 minutes
};

// إعدادات البلوك تشين
export const BLOCKCHAIN_CONFIG = {
  BSC_TESTNET: {
    chainId: '0x61', // 97 in decimal
    chainName: 'BSC Testnet',
    nativeCurrency: {
      name: 'BNB',
      symbol: 'BNB',
      decimals: 18
    },
    rpcUrls: ['https://data-seed-prebsc-1-s1.binance.org:8545/'],
    blockExplorerUrls: ['https://testnet.bscscan.com/']
  },
  BSC_MAINNET: {
    chainId: '0x38', // 56 in decimal
    chainName: 'BSC Mainnet',
    nativeCurrency: {
      name: 'BNB',
      symbol: 'BNB',
      decimals: 18
    },
    rpcUrls: ['https://bsc-dataseed.binance.org/'],
    blockExplorerUrls: ['https://bscscan.com/']
  }
};

// عناوين العقود الذكية المحسنة الجديدة
export const ENHANCED_CONTRACT_ADDRESSES = {
  TESTNET: {
    CORE_ESCROW: process.env.NEXT_PUBLIC_CORE_ESCROW_ADDRESS || '0xAb25F1fFDB7fC4EA75abDcc662CeD61E4b20F7f2',
    REPUTATION_MANAGER: process.env.NEXT_PUBLIC_REPUTATION_MANAGER_ADDRESS || '0x56A6914523413b0e7344f57466A6239fCC97b913',
    ORACLE_MANAGER: process.env.NEXT_PUBLIC_ORACLE_MANAGER_ADDRESS || '0xB70715392F62628Ccd1258AAF691384bE8C023b6',
    ADMIN_MANAGER: process.env.NEXT_PUBLIC_ADMIN_MANAGER_ADDRESS || '0x5A9FD8082ADA38678721D59AAB4d4F76883c5575',
    ESCROW_INTEGRATOR: process.env.NEXT_PUBLIC_ESCROW_INTEGRATOR_ADDRESS || '0xc0Ec65ffA95aca485e4C2e9b35B20C85FA36F0e0'
  },
  MAINNET: {
    CORE_ESCROW: process.env.NEXT_PUBLIC_MAINNET_CORE_ESCROW_CONTRACT || '',
    REPUTATION_MANAGER: process.env.NEXT_PUBLIC_MAINNET_REPUTATION_MANAGER_CONTRACT || '',
    ORACLE_MANAGER: process.env.NEXT_PUBLIC_MAINNET_ORACLE_MANAGER_CONTRACT || '',
    ADMIN_MANAGER: process.env.NEXT_PUBLIC_MAINNET_ADMIN_MANAGER_CONTRACT || '',
    ESCROW_INTEGRATOR: process.env.NEXT_PUBLIC_MAINNET_ESCROW_INTEGRATOR_CONTRACT || ''
  }
};

// العملات المستقرة المدعومة للعقود المحسنة
export const ENHANCED_SUPPORTED_TOKENS = {
  TESTNET: {
    USDT: {
      address: process.env.NEXT_PUBLIC_USDT_CONTRACT_ADDRESS || '******************************************',
      symbol: 'USDT',
      name: 'Tether USD',
      nameAr: 'تيثر دولار أمريكي',
      decimals: 18,
      icon: '/tokens/usdt.svg',
      priority: 1,
      isActive: true
    },
    USDC: {
      address: process.env.NEXT_PUBLIC_USDC_CONTRACT_ADDRESS || '******************************************',
      symbol: 'USDC',
      name: 'USD Coin',
      nameAr: 'عملة الدولار الأمريكي',
      decimals: 18,
      icon: '/tokens/usdc.svg',
      priority: 2,
      isActive: true
    },
    BUSD: {
      address: process.env.NEXT_PUBLIC_BUSD_CONTRACT_ADDRESS || '******************************************',
      symbol: 'BUSD',
      name: 'Binance USD',
      nameAr: 'بينانس دولار أمريكي',
      decimals: 18,
      icon: '/tokens/busd.svg',
      priority: 3,
      isActive: true
    },
    DAI: {
      address: process.env.NEXT_PUBLIC_DAI_CONTRACT_ADDRESS || '******************************************',
      symbol: 'DAI',
      name: 'Dai Stablecoin',
      nameAr: 'عملة داي المستقرة',
      decimals: 18,
      icon: '/tokens/dai.svg',
      priority: 4,
      isActive: true
    },
    FDUSD: {
      address: process.env.NEXT_PUBLIC_FDUSD_CONTRACT_ADDRESS || '0xc5f0f7b66764F6ec8C8Dff7BA683102295E16409',
      symbol: 'FDUSD',
      name: 'First Digital USD',
      nameAr: 'فيرست ديجيتال دولار أمريكي',
      decimals: 18,
      icon: '/tokens/fdusd.svg',
      priority: 5,
      isActive: true
    },
    TUSD: {
      address: process.env.NEXT_PUBLIC_TUSD_CONTRACT_ADDRESS || '******************************************',
      symbol: 'TUSD',
      name: 'TrueUSD',
      nameAr: 'ترو دولار أمريكي',
      decimals: 18,
      icon: '/tokens/tusd.svg',
      priority: 6,
      isActive: true
    }
  },
  MAINNET: {
    USDT: {
      address: '******************************************',
      symbol: 'USDT',
      name: 'Tether USD',
      nameAr: 'تيثر دولار أمريكي',
      decimals: 18,
      icon: '/tokens/usdt.svg',
      priority: 1,
      isActive: true
    },
    USDC: {
      address: '******************************************',
      symbol: 'USDC',
      name: 'USD Coin',
      nameAr: 'عملة الدولار الأمريكي',
      decimals: 18,
      icon: '/tokens/usdc.svg',
      priority: 2,
      isActive: true
    },
    BUSD: {
      address: '******************************************',
      symbol: 'BUSD',
      name: 'Binance USD',
      nameAr: 'بينانس دولار أمريكي',
      decimals: 18,
      icon: '/tokens/busd.svg',
      priority: 3,
      isActive: true
    },
    DAI: {
      address: '******************************************',
      symbol: 'DAI',
      name: 'Dai Stablecoin',
      nameAr: 'عملة داي المستقرة',
      decimals: 18,
      icon: '/tokens/dai.svg',
      priority: 4,
      isActive: true
    },
    FDUSD: {
      address: '0xc5f0f7b66764F6ec8C8Dff7BA683102295E16409',
      symbol: 'FDUSD',
      name: 'First Digital USD',
      nameAr: 'فيرست ديجيتال دولار أمريكي',
      decimals: 18,
      icon: '/tokens/fdusd.svg',
      priority: 5,
      isActive: true
    },
    TUSD: {
      address: '******************************************',
      symbol: 'TUSD',
      name: 'TrueUSD',
      nameAr: 'ترو دولار أمريكي',
      decimals: 18,
      icon: '/tokens/tusd.svg',
      priority: 6,
      isActive: true
    }
  }
};

// عناوين العقود القديمة (للتوافق مع النسخة السابقة - سيتم إزالتها لاحقاً)
export const LEGACY_CONTRACT_ADDRESSES = {
  USDT_ESCROW: '0xEcB2851e50e7CB5ea05c29b7e5b2221913bFce43',
  USDT_TOKEN: '******************************************'
};

// عناوين العقود للشبكات المختلفة (محدث)
export const NETWORK_CONTRACTS = {
  TESTNET: ENHANCED_CONTRACT_ADDRESSES.TESTNET,
  MAINNET: ENHANCED_CONTRACT_ADDRESSES.MAINNET
};

// دالة للحصول على عنوان العقد الصحيح حسب النوع والشبكة
export function getContractAddress(contractType: keyof typeof ENHANCED_CONTRACT_ADDRESSES.TESTNET, isMainnet: boolean = false): string {
  const network = isMainnet ? 'MAINNET' : 'TESTNET';
  return ENHANCED_CONTRACT_ADDRESSES[network][contractType];
}

// دالة للحصول على عنوان العقد الرئيسي (Core Escrow) - للتوافق مع النسخة السابقة
export function getMainContractAddress(isMainnet: boolean = false): string {
  return getContractAddress('CORE_ESCROW', isMainnet);
}

// دالة للحصول على جميع عناوين العقود للشبكة الحالية
export function getAllContractAddresses(isMainnet: boolean = false) {
  const network = isMainnet ? 'MAINNET' : 'TESTNET';
  return ENHANCED_CONTRACT_ADDRESSES[network];
}

// دالة للحصول على قائمة العملات المستقرة المدعومة
export function getSupportedStablecoins(isMainnet: boolean = false) {
  const network = isMainnet ? 'MAINNET' : 'TESTNET';
  return ENHANCED_SUPPORTED_TOKENS[network];
}

// دالة للحصول على عملة مستقرة محددة
export function getStablecoinInfo(symbol: string, isMainnet: boolean = false) {
  const tokens = getSupportedStablecoins(isMainnet);
  return tokens[symbol as keyof typeof tokens];
}

// دالة للحصول على قائمة العملات مرتبة حسب الأولوية
export function getSortedStablecoins(isMainnet: boolean = false) {
  const tokens = getSupportedStablecoins(isMainnet);
  return Object.values(tokens).sort((a, b) => a.priority - b.priority);
}

// دالة للتحقق من دعم عملة معينة
export function isStablecoinSupported(symbol: string, isMainnet: boolean = false): boolean {
  const tokens = getSupportedStablecoins(isMainnet);
  return symbol in tokens && tokens[symbol as keyof typeof tokens].isActive;
}

// إعدادات الغاز المحسنة للعقد الجديد
export const ENHANCED_GAS_LIMITS = {
  // Core Escrow Operations
  CREATE_TRADE: 400000,
  JOIN_TRADE: 250000,
  CONFIRM_PAYMENT_SENT: 180000,
  CONFIRM_PAYMENT_RECEIVED: 300000,
  CANCEL_TRADE: 200000,
  AUTO_CANCEL: 250000,
  REQUEST_DISPUTE: 220000,
  AUTO_DISPUTE: 250000,
  RESOLVE_DISPUTE: 350000,

  // Token Operations (متعدد العملات)
  APPROVE_TOKEN: 120000,
  TRANSFER_TOKEN: 100000,

  // Reputation Manager
  UPDATE_REPUTATION: 150000,
  GET_REPUTATION: 50000,
  RATE_USER: 200000,
  GET_USER_RATINGS: 80000,

  // Oracle Manager
  UPDATE_PRICE: 100000,
  GET_PRICE: 50000,

  // Admin Manager
  ADMIN_FUNCTIONS: 250000,
  EMERGENCY_PAUSE: 100000,
  UPDATE_SETTINGS: 150000,

  // Escrow Integrator
  MULTI_TOKEN_OPERATION: 500000,
  BATCH_OPERATIONS: 800000
};

// إعدادات الغاز القديمة (للتوافق)
export const GAS_LIMITS = ENHANCED_GAS_LIMITS;

// إعدادات الشبكة الافتراضية
export const DEFAULT_NETWORK = process.env.NEXT_PUBLIC_DEFAULT_NETWORK || 'testnet';

// دوال مساعدة للعقد المحسن
export const getEnhancedContractAddresses = (isTestnet: boolean = true) => {
  return isTestnet ? ENHANCED_CONTRACT_ADDRESSES.TESTNET : ENHANCED_CONTRACT_ADDRESSES.MAINNET;
};

export const getSupportedTokens = (isTestnet: boolean = true) => {
  return isTestnet ? ENHANCED_SUPPORTED_TOKENS.TESTNET : ENHANCED_SUPPORTED_TOKENS.MAINNET;
};

export const getTokenBySymbol = (symbol: string, isTestnet: boolean = true) => {
  const tokens = getSupportedTokens(isTestnet);
  return tokens[symbol as keyof typeof tokens] || null;
};

export const isTokenSupported = (tokenAddress: string, isTestnet: boolean = true) => {
  const tokens = getSupportedTokens(isTestnet);
  return Object.values(tokens).some(token => token.address.toLowerCase() === tokenAddress.toLowerCase());
};

// دالة للحصول على عناوين العقود حسب الشبكة (للتوافق مع النسخة السابقة)
export const getContractAddresses = (isTestnet: boolean = true) => {
  return getEnhancedContractAddresses(isTestnet);
};

// ABI للعقد الذكي
export const ESCROW_ABI = [
  {
    "inputs": [
      {"internalType": "address", "name": "_usdtToken", "type": "address"},
      {"internalType": "uint256", "name": "_feePercentage", "type": "uint256"}
    ],
    "stateMutability": "nonpayable",
    "type": "constructor"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}
    ],
    "name": "BuyerJoined",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "winner", "type": "address"}
    ],
    "name": "DisputeResolved",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"},
      {"indexed": true, "internalType": "address", "name": "newOwner", "type": "address"}
    ],
    "name": "OwnershipTransferred",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "seller", "type": "address"}
    ],
    "name": "PaymentConfirmed",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}
    ],
    "name": "PaymentSent",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "reason", "type": "address"}
    ],
    "name": "TradeCancelled",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "buyerAmount", "type": "address"},
      {"indexed": false, "internalType": "uint256", "name": "feeAmount", "type": "uint256"}
    ],
    "name": "TradeCompleted",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "seller", "type": "address"},
      {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}
    ],
    "name": "TradeCreated",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "caller", "type": "address"}
    ],
    "name": "TradeDisputed",
    "type": "event"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_tradeId", "type": "uint256"}
    ],
    "name": "autoCancel",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_tradeId", "type": "uint256"}
    ],
    "name": "autoDispute",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_tradeId", "type": "uint256"}
    ],
    "name": "cancelTrade",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_tradeId", "type": "uint256"}
    ],
    "name": "confirmPaymentReceived",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_tradeId", "type": "uint256"}
    ],
    "name": "confirmPaymentSent",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_amount", "type": "uint256"}
    ],
    "name": "createTrade",
    "outputs": [
      {"internalType": "uint256", "name": "", "type": "uint256"}
    ],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "feePercentage",
    "outputs": [
      {"internalType": "uint256", "name": "", "type": "uint256"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_tradeId", "type": "uint256"}
    ],
    "name": "getTrade",
    "outputs": [
      {
        "components": [
          {"internalType": "uint256", "name": "id", "type": "uint256"},
          {"internalType": "address", "name": "seller", "type": "address"},
          {"internalType": "address", "name": "buyer", "type": "address"},
          {"internalType": "uint256", "name": "amount", "type": "uint256"},
          {"internalType": "enum USDTEscrow.TradeStatus", "name": "status", "type": "uint8"},
          {"internalType": "uint256", "name": "createdAt", "type": "uint256"},
          {"internalType": "uint256", "name": "lastActivity", "type": "uint256"}
        ],
        "internalType": "struct USDTEscrow.Trade",
        "name": "",
        "type": "tuple"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_tradeId", "type": "uint256"}
    ],
    "name": "joinTrade",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "nextTradeId",
    "outputs": [
      {"internalType": "uint256", "name": "", "type": "uint256"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "owner",
    "outputs": [
      {"internalType": "address", "name": "", "type": "address"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "renounceOwnership",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_tradeId", "type": "uint256"}
    ],
    "name": "requestDispute",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_tradeId", "type": "uint256"},
      {"internalType": "address", "name": "_winner", "type": "address"}
    ],
    "name": "resolveDispute",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_newFeePercentage", "type": "uint256"}
    ],
    "name": "setFeePercentage",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "", "type": "uint256"}
    ],
    "name": "trades",
    "outputs": [
      {"internalType": "uint256", "name": "id", "type": "uint256"},
      {"internalType": "address", "name": "seller", "type": "address"},
      {"internalType": "address", "name": "buyer", "type": "address"},
      {"internalType": "uint256", "name": "amount", "type": "uint256"},
      {"internalType": "enum USDTEscrow.TradeStatus", "name": "status", "type": "uint8"},
      {"internalType": "uint256", "name": "createdAt", "type": "uint256"},
      {"internalType": "uint256", "name": "lastActivity", "type": "uint256"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "address", "name": "newOwner", "type": "address"}
    ],
    "name": "transferOwnership",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "usdtToken",
    "outputs": [
      {"internalType": "contract IERC20", "name": "", "type": "address"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_amount", "type": "uint256"}
    ],
    "name": "withdrawFees",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

// ABI للعقد الأساسي المحسن (Core Escrow)
export const CORE_ESCROW_ABI = [
  // Constructor
  {
    "inputs": [
      {"internalType": "address", "name": "_reputationManager", "type": "address"},
      {"internalType": "address", "name": "_oracleManager", "type": "address"},
      {"internalType": "address", "name": "_adminManager", "type": "address"}
    ],
    "stateMutability": "nonpayable",
    "type": "constructor"
  },

  // Events
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "seller", "type": "address"},
      {"indexed": true, "internalType": "address", "name": "token", "type": "address"},
      {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}
    ],
    "name": "TradeCreated",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}
    ],
    "name": "TradeJoined",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}
    ],
    "name": "PaymentSent",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "seller", "type": "address"}
    ],
    "name": "PaymentConfirmed",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "tradeId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "winner", "type": "address"},
      {"indexed": false, "internalType": "uint8", "name": "resolution", "type": "uint8"}
    ],
    "name": "DisputeResolved",
    "type": "event"
  },

  // Core Functions
  {
    "inputs": [
      {"internalType": "address", "name": "_token", "type": "address"},
      {"internalType": "uint256", "name": "_amount", "type": "uint256"},
      {"internalType": "uint256", "name": "_pricePerToken", "type": "uint256"},
      {"internalType": "string", "name": "_currency", "type": "string"}
    ],
    "name": "createTrade",
    "outputs": [
      {"internalType": "uint256", "name": "tradeId", "type": "uint256"}
    ],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_tradeId", "type": "uint256"}
    ],
    "name": "joinTrade",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_tradeId", "type": "uint256"}
    ],
    "name": "confirmPaymentSent",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_tradeId", "type": "uint256"}
    ],
    "name": "confirmPaymentReceived",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_tradeId", "type": "uint256"}
    ],
    "name": "requestDispute",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },

  // View Functions
  {
    "inputs": [
      {"internalType": "uint256", "name": "_tradeId", "type": "uint256"}
    ],
    "name": "getTrade",
    "outputs": [
      {
        "components": [
          {"internalType": "uint256", "name": "id", "type": "uint256"},
          {"internalType": "address", "name": "seller", "type": "address"},
          {"internalType": "address", "name": "buyer", "type": "address"},
          {"internalType": "address", "name": "token", "type": "address"},
          {"internalType": "uint256", "name": "amount", "type": "uint256"},
          {"internalType": "uint256", "name": "pricePerToken", "type": "uint256"},
          {"internalType": "string", "name": "currency", "type": "string"},
          {"internalType": "uint8", "name": "status", "type": "uint8"},
          {"internalType": "uint256", "name": "createdAt", "type": "uint256"},
          {"internalType": "uint256", "name": "lastActivity", "type": "uint256"}
        ],
        "internalType": "struct ICoreEscrow.Trade",
        "name": "",
        "type": "tuple"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "address", "name": "_token", "type": "address"}
    ],
    "name": "isTokenSupported",
    "outputs": [
      {"internalType": "bool", "name": "", "type": "bool"}
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

// ABI لمدير السمعة (Reputation Manager)
export const REPUTATION_MANAGER_ABI = [
  {
    "inputs": [
      {"internalType": "address", "name": "_user", "type": "address"}
    ],
    "name": "getUserReputation",
    "outputs": [
      {"internalType": "uint256", "name": "score", "type": "uint256"},
      {"internalType": "uint256", "name": "totalTrades", "type": "uint256"},
      {"internalType": "uint256", "name": "successfulTrades", "type": "uint256"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "address", "name": "_user", "type": "address"},
      {"internalType": "bool", "name": "_successful", "type": "bool"}
    ],
    "name": "updateReputation",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

// ABI لمدير الأوراكل (Oracle Manager)
export const ORACLE_MANAGER_ABI = [
  {
    "inputs": [
      {"internalType": "address", "name": "_token", "type": "address"},
      {"internalType": "string", "name": "_currency", "type": "string"}
    ],
    "name": "getPrice",
    "outputs": [
      {"internalType": "uint256", "name": "price", "type": "uint256"},
      {"internalType": "uint256", "name": "timestamp", "type": "uint256"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "address", "name": "_token", "type": "address"},
      {"internalType": "string", "name": "_currency", "type": "string"},
      {"internalType": "uint256", "name": "_price", "type": "uint256"}
    ],
    "name": "updatePrice",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

// ABI لمدير الإدارة (Admin Manager)
export const ADMIN_MANAGER_ABI = [
  {
    "inputs": [
      {"internalType": "address", "name": "_token", "type": "address"},
      {"internalType": "bool", "name": "_supported", "type": "bool"}
    ],
    "name": "setSupportedToken",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "_tradeId", "type": "uint256"},
      {"internalType": "uint8", "name": "_resolution", "type": "uint8"}
    ],
    "name": "resolveDispute",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "bool", "name": "_paused", "type": "bool"}
    ],
    "name": "setPaused",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

// ABI لمتكامل الضمان (Escrow Integrator)
export const ESCROW_INTEGRATOR_ABI = [
  {
    "inputs": [
      {"internalType": "address[]", "name": "_tokens", "type": "address[]"},
      {"internalType": "uint256[]", "name": "_amounts", "type": "uint256[]"},
      {"internalType": "uint256[]", "name": "_prices", "type": "uint256[]"},
      {"internalType": "string", "name": "_currency", "type": "string"}
    ],
    "name": "createMultiTokenTrade",
    "outputs": [
      {"internalType": "uint256", "name": "tradeId", "type": "uint256"}
    ],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256[]", "name": "_tradeIds", "type": "uint256[]"}
    ],
    "name": "batchProcessTrades",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

// ABI مبسط للتوكنات ERC20
export const ERC20_ABI = [
  {
    "inputs": [
      {"internalType": "address", "name": "owner", "type": "address"}
    ],
    "name": "balanceOf",
    "outputs": [
      {"internalType": "uint256", "name": "", "type": "uint256"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "address", "name": "spender", "type": "address"},
      {"internalType": "uint256", "name": "amount", "type": "uint256"}
    ],
    "name": "approve",
    "outputs": [
      {"internalType": "bool", "name": "", "type": "bool"}
    ],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "address", "name": "owner", "type": "address"},
      {"internalType": "address", "name": "spender", "type": "address"}
    ],
    "name": "allowance",
    "outputs": [
      {"internalType": "uint256", "name": "", "type": "uint256"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "address", "name": "to", "type": "address"},
      {"internalType": "uint256", "name": "amount", "type": "uint256"}
    ],
    "name": "transfer",
    "outputs": [
      {"internalType": "bool", "name": "", "type": "bool"}
    ],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "decimals",
    "outputs": [
      {"internalType": "uint8", "name": "", "type": "uint8"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "symbol",
    "outputs": [
      {"internalType": "string", "name": "", "type": "string"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "name",
    "outputs": [
      {"internalType": "string", "name": "", "type": "string"}
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

// تجميع جميع ABIs للعقد المحسن
export const ENHANCED_CONTRACT_ABIS = {
  CORE_ESCROW: CORE_ESCROW_ABI,
  REPUTATION_MANAGER: REPUTATION_MANAGER_ABI,
  ORACLE_MANAGER: ORACLE_MANAGER_ABI,
  ADMIN_MANAGER: ADMIN_MANAGER_ABI,
  ESCROW_INTEGRATOR: ESCROW_INTEGRATOR_ABI,
  ERC20: ERC20_ABI
};

// حالات التداول للعقد المحسن
export const ENHANCED_TRADE_STATUS = {
  CREATED: 0,
  JOINED: 1,
  PAYMENT_SENT: 2,
  PAYMENT_CONFIRMED: 3,
  COMPLETED: 4,
  CANCELLED: 5,
  DISPUTED: 6,
  RESOLVED: 7
} as const;

// أنواع حل النزاعات
export const DISPUTE_RESOLUTION_TYPES = {
  SELLER_WINS: 0,
  BUYER_WINS: 1,
  PARTIAL_REFUND: 2,
  FULL_REFUND: 3
} as const;

// إعدادات العقد المحسن
export const ENHANCED_CONTRACT_SETTINGS = {
  // الحد الأدنى والأقصى للمبالغ (بالوحدة الأساسية للتوكن)
  MIN_TRADE_AMOUNT: '1000000000000000000', // 1 token
  MAX_TRADE_AMOUNT: '1000000000000000000000000', // 1M tokens

  // مهلة زمنية للعمليات (بالثواني)
  PAYMENT_TIMEOUT: 1800, // 30 دقيقة
  DISPUTE_TIMEOUT: 86400, // 24 ساعة
  AUTO_CANCEL_TIMEOUT: 3600, // 1 ساعة

  // رسوم المنصة (بالنقاط الأساسية - 100 = 1%)
  PLATFORM_FEE_RATE: 50, // 0.5%
  DISPUTE_FEE_RATE: 100, // 1%

  // حدود السمعة
  MIN_REPUTATION_SCORE: 0,
  MAX_REPUTATION_SCORE: 1000,
  INITIAL_REPUTATION_SCORE: 100
};

// دالة للحصول على ABI حسب نوع العقد
export const getContractABI = (contractType: keyof typeof ENHANCED_CONTRACT_ABIS) => {
  return ENHANCED_CONTRACT_ABIS[contractType];
};

// تم نقل دالة getContractAddress إلى الأعلى لتجنب التكرار