'use client';

import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Clock,
  CreditCard,
  MessageSquare,
  Star,
  Shield,
  Calculator,
  AlertTriangle,
  AlertCircle,
  CheckCircle,
  Gift,
  Zap
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useCreateOfferTranslation } from '@/hooks/useCreateOfferTranslation';
import { notificationService } from '@/services/notificationService';
import { apiGet, apiPost, handleApiError } from '@/utils/apiClient';
import CurrencySelector from './CurrencySelector';
import { getPaymentMethodsByOfferType, SUPPORTED_STABLECOINS } from '@/constants';

interface CreateOfferFormData {
  offer_type: 'buy' | 'sell';
  amount: string;
  min_amount: string;
  max_amount: string;
  price: string;
  currency: string;
  stablecoin: string;
  payment_methods: string[];
  terms: string;
  time_limit: number;
  auto_reply: string;
  is_premium: boolean;
}

interface FreeOfferLimits {
  canCreateFreeOffer: boolean;
  reason?: string;
  currentCount: number;
  maxAllowed: number;
  remainingSlots: number;
  maxAmount: number;
  minAmount: number;
  timeLimit: number;
  resetDate: string;
  monthlyUsage: {
    used: number;
    limit: number;
    remaining: number;
    percentage: number;
  };
  userPlan: {
    name: string;
    name_ar: string;
    monthly_free_offers: number;
    is_default: boolean;
  };
}

export default function CreateOfferForm() {
  const { user, isAuthenticated } = useAuth();
  const { t } = useCreateOfferTranslation();
  
  const [formData, setFormData] = useState<CreateOfferFormData>({
    offer_type: 'sell',
    amount: '',
    min_amount: '',
    max_amount: '',
    price: '',
    currency: 'SAR',
    stablecoin: 'USDT',
    payment_methods: [],
    terms: '',
    time_limit: 1800,
    auto_reply: '',
    is_premium: false
  });

  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [freeOfferLimits, setFreeOfferLimits] = useState<FreeOfferLimits | null>(null);
  const [convertedAmount, setConvertedAmount] = useState<number>(0);
  const [exchangeRate, setExchangeRate] = useState<number>(0);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // الحصول على طرق الدفع حسب نوع العرض
  const availablePaymentMethods = getPaymentMethodsByOfferType(formData.offer_type);

  const steps = [
    { id: 1, title: t('steps.basic'), icon: DollarSign },
    { id: 2, title: t('steps.payment'), icon: CreditCard },
    { id: 3, title: t('steps.review'), icon: CheckCircle }
  ];

  // تحميل حدود العروض المجانية عند تحميل المكون
  useEffect(() => {
    if (isAuthenticated) {
      checkFreeOfferLimits();
    }
  }, [isAuthenticated]);

  // تحديث الحد الأدنى والأقصى عند تغيير المبلغ
  useEffect(() => {
    if (formData.amount) {
      const amount = parseFloat(formData.amount);
      if (!formData.min_amount) {
        setFormData(prev => ({ ...prev, min_amount: Math.min(amount * 0.1, 10).toString() }));
      }
      if (!formData.max_amount) {
        setFormData(prev => ({ ...prev, max_amount: amount.toString() }));
      }
    }
  }, [formData.amount]);

  // إعادة تعيين طرق الدفع عند تغيير نوع العرض
  useEffect(() => {
    // إذا كانت هناك طرق دفع مختارة، تحقق من توافقها مع النوع الجديد
    if (formData.payment_methods.length > 0) {
      const newAvailableMethods = getPaymentMethodsByOfferType(formData.offer_type);
      const validMethods = formData.payment_methods.filter(methodId =>
        newAvailableMethods.some(method => method.id === methodId)
      );

      // إذا تغيرت طرق الدفع المتاحة، حدث القائمة
      if (validMethods.length !== formData.payment_methods.length) {
        setFormData(prev => ({
          ...prev,
          payment_methods: validMethods
        }));

        // إشعار المستخدم بالتغيير
        if (validMethods.length < formData.payment_methods.length) {
          notificationService.info(
            formData.offer_type === 'buy'
              ? 'تم تحديث طرق الدفع لتناسب عمليات الشراء'
              : 'تم تحديث طرق الدفع لتناسب عمليات البيع'
          );
        }
      }
    }
  }, [formData.offer_type]);

  const checkFreeOfferLimits = async () => {
    try {
      const result = await apiGet('offers/check-free-offer-limits.php');

      if (result.success) {
        setFreeOfferLimits(result.data);
      }
    } catch (error) {
      console.error('Error fetching free offer limits:', error);
      notificationService.error(t('messages.fetchLimitsError'));
    }
  };

  const handleInputChange = (field: keyof CreateOfferFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // مسح الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleConversionUpdate = (convertedAmount: number, rate: number) => {
    setConvertedAmount(convertedAmount);
    setExchangeRate(rate);
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      if (!formData.amount || parseFloat(formData.amount) <= 0) {
        newErrors.amount = t('validation.invalidAmount');
      }
      if (!formData.price || parseFloat(formData.price) <= 0) {
        newErrors.price = t('validation.invalidPrice');
      }
      if (formData.min_amount && parseFloat(formData.min_amount) > parseFloat(formData.amount)) {
        newErrors.min_amount = t('validation.minAmountTooHigh');
      }
      if (formData.max_amount && parseFloat(formData.max_amount) > parseFloat(formData.amount)) {
        newErrors.max_amount = t('validation.maxAmountTooHigh');
      }
    }

    if (step === 2) {
      if (formData.payment_methods.length === 0) {
        newErrors.payment_methods = t('validation.noPaymentMethods');
      }
      if (!formData.terms.trim()) {
        newErrors.terms = t('validation.noTerms');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;

    setIsLoading(true);
    try {
      // تحديد ما إذا كان العرض ضمن الحد الشهري
      const isWithinLimit = freeOfferLimits?.canCreateFreeOffer || false;

      const result = await apiPost('offers/index.php', {
        ...formData,
        is_free: isWithinLimit, // ضمن الحد الشهري
        user_id: user?.id
      });

      if (result.success) {
        notificationService.success('تم نشر العرض بنجاح!');
        // إعادة توجيه إلى صفحة العروض
        window.location.href = '/offers';
      } else {
        notificationService.error(result.error || t('messages.offerCreationFailed'));
      }
    } catch (error) {
      console.error('Error creating offer:', error);
      notificationService.error(handleApiError(error));
    } finally {
      setIsLoading(false);
    }
  };

  const calculateTotal = () => {
    const amount = parseFloat(formData.amount) || 0;
    const price = parseFloat(formData.price) || 0;
    return amount * price;
  };

  const calculateFees = () => {
    const total = calculateTotal();
    return total * 0.015; // 1.5% رسوم ثابتة لجميع العروض
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <Shield className="w-16 h-16 mx-auto text-gray-400 mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            {t('auth.loginRequired')}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {t('auth.loginRequiredDesc')}
          </p>
          <a href="/login" className="btn btn-primary">
            {t('auth.login')}
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8 relative">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-6 lg:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {t('title')}
          </h1>
          <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
            {t('subtitle')}
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4 rtl:space-x-reverse">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center w-full sm:w-auto">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.id
                    ? 'bg-blue-600 border-blue-600 text-white'
                    : 'border-gray-300 text-gray-400'
                }`}>
                  <step.icon className="w-5 h-5" />
                </div>
                <span className={`mr-3 text-sm font-medium hidden sm:inline ${
                  currentStep >= step.id ? 'text-blue-600' : 'text-gray-400'
                }`}>
                  {step.title}
                </span>
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-4 hidden sm:block ${
                    currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>

          {/* عرض عنوان الخطوة الحالية على الشاشات الصغيرة */}
          <div className="text-center mt-4 sm:hidden">
            <span className="text-lg font-medium text-blue-600">
              {steps.find(step => step.id === currentStep)?.title}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* النموذج الرئيسي */}
          <div className="lg:col-span-2 order-2 lg:order-1">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              
              {/* الخطوة الأولى: نوع العرض والمبلغ */}
              {currentStep === 1 && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    {t('steps.basic')}
                  </h3>

                  {/* نوع العرض */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      {t('fields.offerType')}
                    </label>
                    <div className="grid grid-cols-2 gap-4">
                      <button
                        type="button"
                        onClick={() => handleInputChange('offer_type', 'buy')}
                        className={`p-4 border-2 rounded-lg text-center transition-colors ${
                          formData.offer_type === 'buy'
                            ? 'border-green-500 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                        }`}
                      >
                        <TrendingUp className="w-6 h-6 mx-auto mb-2" />
                        <div className="font-medium">{t('offerType.buy')}</div>
                        <div className="text-xs text-gray-500">{t('offerType.buyDescription')}</div>
                      </button>

                      <button
                        type="button"
                        onClick={() => handleInputChange('offer_type', 'sell')}
                        className={`p-4 border-2 rounded-lg text-center transition-colors ${
                          formData.offer_type === 'sell'
                            ? 'border-red-500 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                        }`}
                      >
                        <TrendingDown className="w-6 h-6 mx-auto mb-2" />
                        <div className="font-medium">{t('offerType.sell')}</div>
                        <div className="text-xs text-gray-500">{t('offerType.sellDescription')}</div>
                      </button>
                    </div>
                  </div>

                  {/* العملة المستقرة */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {t('fields.stablecoin')}
                    </label>
                    <select
                      value={formData.stablecoin}
                      onChange={(e) => handleInputChange('stablecoin', e.target.value)}
                      className="form-input"
                    >
                      {SUPPORTED_STABLECOINS.map((stablecoin) => (
                        <option key={stablecoin.symbol} value={stablecoin.symbol}>
                          {stablecoin.symbol} - {stablecoin.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* العملة المحلية */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {t('fields.localCurrency')}
                    </label>
                    <CurrencySelector
                      selectedCurrency={formData.currency}
                      onCurrencyChange={(currency) => handleInputChange('currency', currency)}
                      amount={parseFloat(formData.amount) || 0}
                      stablecoin={formData.stablecoin}
                      showConversion={true}
                      onConversionUpdate={handleConversionUpdate}
                      showFlag={true}
                      showFullName={true}
                    />
                  </div>

                  {/* المبلغ */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('fields.totalAmount')} *
                      </label>
                      <div className="input-with-currency">
                        <input
                          type="number"
                          value={formData.amount}
                          onChange={(e) => handleInputChange('amount', e.target.value)}
                          className={`form-input ${errors.amount ? 'border-red-500' : ''}`}
                          placeholder={t('placeholders.amount')}
                          min="0"
                          step="0.01"
                        />
                        <span className="currency-symbol">
                          {formData.stablecoin}
                        </span>
                      </div>
                      {errors.amount && (
                        <p className="text-red-500 text-xs mt-1">{errors.amount}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('fields.minAmount')}
                      </label>
                      <div className="input-with-currency">
                        <input
                          type="number"
                          value={formData.min_amount}
                          onChange={(e) => handleInputChange('min_amount', e.target.value)}
                          className={`form-input ${errors.min_amount ? 'border-red-500' : ''}`}
                          placeholder={t('placeholders.amount')}
                          min="0"
                          step="0.01"
                        />
                        <span className="currency-symbol">
                          {formData.stablecoin}
                        </span>
                      </div>
                      {errors.min_amount && (
                        <p className="text-red-500 text-xs mt-1">{errors.min_amount}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('fields.maxAmount')}
                      </label>
                      <div className="input-with-currency">
                        <input
                          type="number"
                          value={formData.max_amount}
                          onChange={(e) => handleInputChange('max_amount', e.target.value)}
                          className={`form-input ${errors.max_amount ? 'border-red-500' : ''}`}
                          placeholder={t('placeholders.amount')}
                          min="0"
                          step="0.01"
                        />
                        <span className="currency-symbol">
                          {formData.stablecoin}
                        </span>
                      </div>
                      {errors.max_amount && (
                        <p className="text-red-500 text-xs mt-1">{errors.max_amount}</p>
                      )}
                    </div>
                  </div>

                  {/* السعر */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {t('fields.pricePerUnit', { unit: formData.stablecoin })} *
                    </label>
                    <div className="input-with-currency">
                      <input
                        type="number"
                        value={formData.price}
                        onChange={(e) => handleInputChange('price', e.target.value)}
                        className={`form-input ${errors.price ? 'border-red-500' : ''}`}
                        placeholder={t('placeholders.price')}
                        min="0"
                        step="0.01"
                      />
                      <span className="currency-symbol">
                        {formData.currency}
                      </span>
                    </div>
                    {errors.price && (
                      <p className="text-red-500 text-xs mt-1">{errors.price}</p>
                    )}
                    {exchangeRate > 0 && (
                      <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                        {t('conversion.marketPrice', { price: exchangeRate.toFixed(4), currency: formData.currency })}
                      </p>
                    )}
                  </div>

                  {/* معلومات الحد الشهري للنشر */}
                  {freeOfferLimits && (
                    <div className={`rounded-lg p-4 ${
                      freeOfferLimits.canCreateFreeOffer
                        ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                        : 'bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800'
                    }`}>
                      <div className="flex items-center space-x-3 rtl:space-x-reverse mb-3">
                        {freeOfferLimits.canCreateFreeOffer ? (
                          <Gift className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        ) : (
                          <AlertCircle className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
                        )}
                        <div className="flex-1">
                          <h4 className={`font-medium ${
                            freeOfferLimits.canCreateFreeOffer
                              ? 'text-blue-800 dark:text-blue-300'
                              : 'text-yellow-800 dark:text-yellow-300'
                          }`}>
                            {freeOfferLimits.canCreateFreeOffer
                              ? 'يمكنك نشر عرض جديد'
                              : 'تم الوصول للحد الشهري للنشر'
                            }
                          </h4>
                          <p className={`text-sm ${
                            freeOfferLimits.canCreateFreeOffer
                              ? 'text-blue-600 dark:text-blue-400'
                              : 'text-yellow-600 dark:text-yellow-400'
                          }`}>
                            {freeOfferLimits.canCreateFreeOffer
                              ? `نشرت ${freeOfferLimits.monthlyUsage.used} من ${freeOfferLimits.monthlyUsage.limit} عروض هذا الشهر`
                              : 'لقد وصلت للحد الأقصى لنشر العروض هذا الشهر'
                            }
                          </p>
                        </div>
                      </div>

                      {/* شريط التقدم */}
                      <div className="mb-2">
                        <div className={`flex justify-between text-xs mb-1 ${
                          freeOfferLimits.canCreateFreeOffer
                            ? 'text-blue-700 dark:text-blue-300'
                            : 'text-yellow-700 dark:text-yellow-300'
                        }`}>
                          <span>الباقة: {freeOfferLimits.userPlan.name_ar}</span>
                          <span>{freeOfferLimits.monthlyUsage.percentage}%</span>
                        </div>
                        <div className={`w-full rounded-full h-2 ${
                          freeOfferLimits.canCreateFreeOffer
                            ? 'bg-blue-200 dark:bg-blue-800'
                            : 'bg-yellow-200 dark:bg-yellow-800'
                        }`}>
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${
                              freeOfferLimits.canCreateFreeOffer
                                ? 'bg-blue-600 dark:bg-blue-400'
                                : 'bg-yellow-600 dark:bg-yellow-400'
                            }`}
                            style={{ width: `${Math.min(freeOfferLimits.monthlyUsage.percentage, 100)}%` }}
                          ></div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className={`text-xs ${
                          freeOfferLimits.canCreateFreeOffer
                            ? 'text-blue-600 dark:text-blue-400'
                            : 'text-yellow-600 dark:text-yellow-400'
                        }`}>
                          يتم التجديد في: {new Date(freeOfferLimits.resetDate).toLocaleDateString('ar-SA')}
                        </div>

                        {!freeOfferLimits.canCreateFreeOffer && freeOfferLimits.userPlan.is_default && (
                          <button
                            type="button"
                            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors"
                            onClick={() => window.open('/subscriptions', '_blank')}
                          >
                            ترقية الباقة
                          </button>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* الخطوة الثانية: طرق الدفع والشروط */}
              {currentStep === 2 && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    {t('steps.payment')}
                  </h3>

                  {/* طرق الدفع */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      {t('fields.paymentMethods')} *
                      <span className="text-xs text-blue-600 dark:text-blue-400 ml-2">
                        ({formData.offer_type === 'buy' ? 'طرق الدفع المتاحة للشراء' : 'طرق استلام المال للبيع'})
                      </span>
                    </label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {availablePaymentMethods.map((method) => (
                        <div
                          key={method.id}
                          className={`payment-method-card relative overflow-hidden rounded-2xl border-2 cursor-pointer transition-all duration-300 ${
                            formData.payment_methods.includes(method.id)
                              ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 shadow-xl scale-105'
                              : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-lg hover:scale-102'
                          }`}
                          onClick={() => {
                            const methods = formData.payment_methods.includes(method.id)
                              ? formData.payment_methods.filter(m => m !== method.id)
                              : [...formData.payment_methods, method.id];
                            handleInputChange('payment_methods', methods);
                          }}
                        >
                          {/* أيقونة التحديد */}
                          {formData.payment_methods.includes(method.id) && (
                            <div className="absolute top-3 left-3 rtl:left-auto rtl:right-3 bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center shadow-lg z-20 animate-bounce">
                              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                          )}

                          {/* شارات الجودة */}
                          <div className="absolute top-3 right-3 rtl:right-auto rtl:left-3 flex gap-1 z-10">
                            {method.popularity >= 90 && (
                              <div className="w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-md" title="الأفضل">
                                <span className="text-white text-[8px]">⭐</span>
                              </div>
                            )}
                            {method.popularity >= 80 && method.popularity < 90 && (
                              <div className="w-4 h-4 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-md" title="موصى به">
                                <span className="text-white text-[8px]">✓</span>
                              </div>
                            )}
                            {method.processingTime === 'فوري' && (
                              <div className="w-4 h-4 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-full flex items-center justify-center shadow-md" title="فوري">
                                <span className="text-white text-[8px]">⚡</span>
                              </div>
                            )}
                            {method.fees === 'مجاني' && (
                              <div className="w-4 h-4 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center shadow-md" title="مجاني">
                                <span className="text-white text-[8px]">💰</span>
                              </div>
                            )}
                          </div>

                          {/* محتوى البطاقة */}
                          <div className="p-4">
                            {/* الأيقونة والاسم */}
                            <div className="text-center mb-3">
                              <div className="text-3xl mb-2">{method.icon}</div>
                              <h3 className="text-sm font-semibold text-gray-900 dark:text-white leading-tight">
                                {method.nameAr}
                              </h3>
                            </div>

                            {/* المعلومات */}
                            <div className="space-y-2 text-xs">
                              <div className="flex items-center justify-between">
                                <span className="text-gray-500 dark:text-gray-400 flex items-center">
                                  <span className="mr-1">⏱️</span>
                                  الوقت
                                </span>
                                <span className="text-gray-700 dark:text-gray-300 font-medium">{method.processingTime}</span>
                              </div>

                              <div className="flex items-center justify-between">
                                <span className="text-gray-500 dark:text-gray-400 flex items-center">
                                  <span className="mr-1">💰</span>
                                  التكلفة
                                </span>
                                <span className="text-gray-700 dark:text-gray-300 font-medium">{method.fees}</span>
                              </div>

                              {/* شريط الشعبية */}
                              <div className="mt-3">
                                <div className="flex items-center justify-between mb-1">
                                  <span className="text-gray-500 dark:text-gray-400">الشعبية</span>
                                  <span className="text-gray-700 dark:text-gray-300 font-medium">{method.popularity}%</span>
                                </div>
                                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                                  <div
                                    className={`h-1.5 rounded-full transition-all duration-1000 ${
                                      method.popularity >= 80 ? 'bg-gradient-to-r from-green-400 to-green-600' :
                                      method.popularity >= 60 ? 'bg-gradient-to-r from-yellow-400 to-yellow-600' : 'bg-gradient-to-r from-red-400 to-red-600'
                                    }`}
                                    style={{ width: `${method.popularity}%` }}
                                  ></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    {errors.payment_methods && (
                      <p className="text-red-500 text-xs mt-1">{errors.payment_methods}</p>
                    )}
                  </div>

                  {/* شروط التداول */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {t('fields.terms')} *
                    </label>
                    <textarea
                      value={formData.terms}
                      onChange={(e) => handleInputChange('terms', e.target.value)}
                      className={`form-input min-h-[120px] ${errors.terms ? 'border-red-500' : ''}`}
                      placeholder={t('placeholders.terms')}
                      rows={5}
                    />
                    {errors.terms && (
                      <p className="text-red-500 text-xs mt-1">{errors.terms}</p>
                    )}
                  </div>

                  {/* الحد الزمني */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {t('fields.timeLimit')}
                    </label>
                    <select
                      value={formData.time_limit}
                      onChange={(e) => handleInputChange('time_limit', parseInt(e.target.value))}
                      className="form-input"
                    >
                      <option value={900}>{t('timeOptions.15min')}</option>
                      <option value={1800}>{t('timeOptions.30min')}</option>
                      <option value={3600}>{t('timeOptions.1hour')}</option>
                      <option value={7200}>{t('timeOptions.2hours')}</option>
                      <option value={14400}>{t('timeOptions.4hours')}</option>
                      <option value={28800}>{t('timeOptions.8hours')}</option>
                      <option value={86400}>{t('timeOptions.24hours')}</option>
                    </select>
                  </div>

                  {/* الرد التلقائي */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {t('fields.autoReply')}
                    </label>
                    <textarea
                      value={formData.auto_reply}
                      onChange={(e) => handleInputChange('auto_reply', e.target.value)}
                      className="form-input min-h-[80px]"
                      placeholder={t('placeholders.autoReply')}
                      rows={3}
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {t('newForm.autoReplyHelp')}
                    </p>
                  </div>
                </div>
              )}

              {/* الخطوة الثالثة: المراجعة والنشر */}
              {currentStep === 3 && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    {t('steps.review')}
                  </h3>

                  {/* ملخص العرض */}
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-6">
                    <h4 className="font-medium text-gray-900 dark:text-white mb-4">{t('summary.title')}</h4>

                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">{t('preview.type')}:</span>
                          <span className={`font-medium ${
                            formData.offer_type === 'buy' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {formData.offer_type === 'buy' ? t('offerType.buy') : t('offerType.sell')}
                          </span>
                        </div>

                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">{t('summary.currency')}:</span>
                          <span className="font-medium">{formData.stablecoin} → {formData.currency}</span>
                        </div>

                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">{t('preview.amount')}:</span>
                          <span className="font-medium">{formData.amount} {formData.stablecoin}</span>
                        </div>

                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">{t('preview.price')}:</span>
                          <span className="font-medium">{formData.price} {formData.currency}</span>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">{t('summary.minimum')}:</span>
                          <span className="font-medium">{formData.min_amount || t('summary.notSpecified')} {formData.stablecoin}</span>
                        </div>

                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">{t('summary.maximum')}:</span>
                          <span className="font-medium">{formData.max_amount || t('summary.notSpecified')} {formData.stablecoin}</span>
                        </div>

                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">{t('summary.timeLimit')}:</span>
                          <span className="font-medium">{Math.floor(formData.time_limit / 60)} {t('summary.minutes')}</span>
                        </div>

                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">{t('summary.paymentMethodsCount')}:</span>
                          <span className="font-medium">{formData.payment_methods.length} {t('summary.methods')}</span>
                        </div>
                      </div>
                    </div>

                    <div className="border-t border-gray-200 dark:border-gray-600 mt-4 pt-4">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-medium text-gray-900 dark:text-white">{t('preview.total')}:</span>
                        <span className="text-xl font-bold text-blue-600 dark:text-blue-400">
                          {calculateTotal().toLocaleString('en-US')} {formData.currency}
                        </span>
                      </div>

                      <div className="flex justify-between items-center mt-2">
                        <span className="text-gray-600 dark:text-gray-400">{t('preview.fees')}:</span>
                        <span className="font-medium">
                          {`${calculateFees().toLocaleString('en-US')} ${formData.currency}`}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* طرق الدفع المختارة */}
                  {formData.payment_methods.length > 0 && (
                    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-4">
                      <h5 className="font-medium text-gray-900 dark:text-white mb-3">{t('summary.selectedPaymentMethods')}:</h5>
                      <div className="flex flex-wrap gap-2">
                        {formData.payment_methods.map((methodId) => {
                          const method = availablePaymentMethods.find(m => m.id === methodId);
                          return method ? (
                            <span key={methodId} className="inline-flex items-center px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-sm rounded-full">
                              <span className="mr-1">{method.icon}</span>
                              {method.nameAr}
                            </span>
                          ) : null;
                        })}
                      </div>
                    </div>
                  )}

                  {/* شروط التداول */}
                  {formData.terms && (
                    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-4">
                      <h5 className="font-medium text-gray-900 dark:text-white mb-2">{t('summary.tradingTerms')}:</h5>
                      <p className="text-gray-600 dark:text-gray-400 text-sm whitespace-pre-wrap">
                        {formData.terms}
                      </p>
                    </div>
                  )}

                  {/* الرد التلقائي */}
                  {formData.auto_reply && (
                    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-4">
                      <h5 className="font-medium text-gray-900 dark:text-white mb-2">{t('summary.autoReplyMessage')}:</h5>
                      <p className="text-gray-600 dark:text-gray-400 text-sm whitespace-pre-wrap">
                        {formData.auto_reply}
                      </p>
                    </div>
                  )}

                  {/* تأكيد النشر */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
                    <div className="flex items-start space-x-3 rtl:space-x-reverse">
                      <CheckCircle className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                      <div>
                        <h5 className="font-medium text-blue-800 dark:text-blue-300">{t('summary.readyToPublish')}</h5>
                        <p className="text-blue-600 dark:text-blue-400 text-sm mt-1">
                          {t('summary.confirmationMessage')}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* أزرار التنقل */}
              <div className="flex flex-col sm:flex-row justify-between gap-4 mt-8">
                <button
                  onClick={prevStep}
                  disabled={currentStep === 1}
                  className="btn btn-secondary disabled:opacity-50 disabled:cursor-not-allowed order-2 sm:order-1"
                >
                  {t('actions.previous')}
                </button>

                {currentStep < steps.length ? (
                  <button
                    onClick={nextStep}
                    className="btn btn-primary order-1 sm:order-2"
                  >
                    {t('actions.next')}
                  </button>
                ) : (
                  <button
                    onClick={handleSubmit}
                    disabled={isLoading || (freeOfferLimits ? !freeOfferLimits.canCreateFreeOffer : false)}
                    className="btn btn-primary disabled:opacity-50 order-1 sm:order-2"
                  >
                    {isLoading ? t('actions.creating') :
                     (freeOfferLimits && !freeOfferLimits.canCreateFreeOffer) ? 'تم الوصول للحد الشهري للنشر' :
                     t('actions.publish')}
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* الشريط الجانبي - معاينة العرض */}
          <div className="lg:col-span-1 order-1 lg:order-2">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 lg:p-6 lg:sticky lg:top-8">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <Calculator className="w-5 h-5 mr-2" />
                {t('preview.title')}
              </h3>

              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">{t('preview.type')}:</span>
                  <span className={`font-medium ${
                    formData.offer_type === 'buy' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {formData.offer_type === 'buy' ? t('offerType.buy') : t('offerType.sell')}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">{t('preview.amount')}:</span>
                  <span className="font-medium">
                    {formData.amount || '0'} {formData.stablecoin}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">{t('preview.price')}:</span>
                  <span className="font-medium">
                    {formData.price || '0'} {formData.currency}
                  </span>
                </div>

                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">{t('preview.total')}:</span>
                    <span className="font-bold text-lg">
                      {calculateTotal().toLocaleString('en-US')} {formData.currency}
                    </span>
                  </div>

                  <div className="flex justify-between mt-2">
                    <span className="text-gray-600 dark:text-gray-400">{t('preview.fees')}:</span>
                    <span className="font-medium">
                      {`${calculateFees().toLocaleString('en-US')} ${formData.currency}`}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* طبقة القفل للمستخدمين غير المسجلين */}
      {!isAuthenticated && (
        <div className="auth-overlay fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="auth-modal bg-white dark:bg-gray-800 rounded-2xl max-w-md w-full p-8 text-center">
            <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
              <Shield className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>

            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
              {t('auth.loginRequired')}
            </h3>

            <p className="text-gray-600 dark:text-gray-400 mb-8 leading-relaxed">
              {t('auth.loginRequiredMessage')}
            </p>

            <div className="flex flex-col sm:flex-row gap-4">
              <a
                href="/login"
                className="flex-1 inline-flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
              >
                {t('auth.login')}
              </a>
              <a
                href="/register"
                className="flex-1 inline-flex items-center justify-center px-6 py-3 bg-white text-blue-600 border-2 border-blue-600 hover:bg-blue-50 dark:bg-gray-700 dark:text-blue-400 dark:border-blue-400 dark:hover:bg-gray-600 rounded-lg font-medium transition-colors"
              >
                {t('auth.register')}
              </a>
            </div>

            <p className="text-xs text-gray-500 dark:text-gray-400 mt-6">
              {t('auth.secureMessage')}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
