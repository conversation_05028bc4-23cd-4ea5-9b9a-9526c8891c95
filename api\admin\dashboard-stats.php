<?php
/**
 * API endpoint لإحصائيات لوحة الأدمن
 * Admin Dashboard Statistics API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    // Get database connection
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // إحصائيات المستخدمين
    $userStats = [];
    
    // إجمالي المستخدمين
    $stmt = $connection->prepare("SELECT COUNT(*) as total FROM users");
    $stmt->execute();
    $userStats['total'] = $stmt->fetch()['total'];
    
    // المستخدمين النشطين
    $stmt = $connection->prepare("SELECT COUNT(*) as active FROM users WHERE is_active = 1");
    $stmt->execute();
    $userStats['active'] = $stmt->fetch()['active'];
    
    // المستخدمين المحققين
    $stmt = $connection->prepare("SELECT COUNT(*) as verified FROM users WHERE is_verified = 1");
    $stmt->execute();
    $userStats['verified'] = $stmt->fetch()['verified'];
    
    // المديرين
    $stmt = $connection->prepare("SELECT COUNT(*) as admins FROM users WHERE is_admin = 1");
    $stmt->execute();
    $userStats['admins'] = $stmt->fetch()['admins'];
    
    // إحصائيات العروض (إذا كان الجدول موجود)
    $offerStats = [];
    $checkOffersTable = $connection->prepare("SHOW TABLES LIKE 'offers'");
    $checkOffersTable->execute();

    if ($checkOffersTable->rowCount() > 0) {
        try {
            // إجمالي العروض
            $stmt = $connection->prepare("SELECT COUNT(*) as total FROM offers");
            $stmt->execute();
            $offerStats['total'] = $stmt->fetch()['total'];

            // التحقق من وجود عمود status
            $checkStatusColumn = $connection->prepare("SHOW COLUMNS FROM offers LIKE 'status'");
            $checkStatusColumn->execute();

            if ($checkStatusColumn->rowCount() > 0) {
                // العروض النشطة
                $stmt = $connection->prepare("SELECT COUNT(*) as active FROM offers WHERE status = 'active'");
                $stmt->execute();
                $offerStats['active'] = $stmt->fetch()['active'];
            } else {
                // إذا لم يكن عمود status موجود، افترض أن جميع العروض نشطة
                $offerStats['active'] = $offerStats['total'];
            }
        } catch (Exception $e) {
            $offerStats = ['total' => 0, 'active' => 0, 'error' => $e->getMessage()];
        }
    } else {
        $offerStats = ['total' => 0, 'active' => 0];
    }
    
    // إحصائيات التداولات (إذا كان الجدول موجود)
    $tradeStats = [];
    $checkTradesTable = $connection->prepare("SHOW TABLES LIKE 'trades'");
    $checkTradesTable->execute();

    if ($checkTradesTable->rowCount() > 0) {
        try {
            // إجمالي التداولات
            $stmt = $connection->prepare("SELECT COUNT(*) as total FROM trades");
            $stmt->execute();
            $tradeStats['total'] = $stmt->fetch()['total'];

            // التحقق من وجود عمود status
            $checkStatusColumn = $connection->prepare("SHOW COLUMNS FROM trades LIKE 'status'");
            $checkStatusColumn->execute();

            if ($checkStatusColumn->rowCount() > 0) {
                // التداولات المكتملة
                $stmt = $connection->prepare("SELECT COUNT(*) as completed FROM trades WHERE status = 'completed'");
                $stmt->execute();
                $tradeStats['completed'] = $stmt->fetch()['completed'];

                // التحقق من وجود عمود amount
                $checkAmountColumn = $connection->prepare("SHOW COLUMNS FROM trades LIKE 'amount'");
                $checkAmountColumn->execute();

                if ($checkAmountColumn->rowCount() > 0) {
                    // إجمالي الحجم
                    $stmt = $connection->prepare("SELECT SUM(amount) as volume FROM trades WHERE status = 'completed'");
                    $stmt->execute();
                    $result = $stmt->fetch();
                    $tradeStats['volume'] = $result['volume'] ?? 0;
                } else {
                    $tradeStats['volume'] = 0;
                }
            } else {
                // إذا لم يكن عمود status موجود، افترض أن جميع التداولات مكتملة
                $tradeStats['completed'] = $tradeStats['total'];
                $tradeStats['volume'] = 0;
            }
        } catch (Exception $e) {
            $tradeStats = ['total' => 0, 'completed' => 0, 'volume' => 0, 'error' => $e->getMessage()];
        }
    } else {
        $tradeStats = ['total' => 0, 'completed' => 0, 'volume' => 0];
    }
    
    // إحصائيات النشاط الإداري من الجداول الجديدة
    $activityStats = [];

    // فحص جدول admin_activity_logs الجديد
    $checkAdminActivityTable = $connection->prepare("SHOW TABLES LIKE 'admin_activity_logs'");
    $checkAdminActivityTable->execute();

    if ($checkAdminActivityTable->rowCount() > 0) {
        try {
            // إجمالي أنشطة المدراء
            $stmt = $connection->prepare("SELECT COUNT(*) as total FROM admin_activity_logs");
            $stmt->execute();
            $activityStats['admin_total'] = $stmt->fetch()['total'];

            // أنشطة المدراء اليوم
            $stmt = $connection->prepare("SELECT COUNT(*) as today FROM admin_activity_logs WHERE DATE(created_at) = CURDATE()");
            $stmt->execute();
            $activityStats['admin_today'] = $stmt->fetch()['today'];

            // آخر أنشطة المدراء
            $stmt = $connection->prepare("
                SELECT aal.action_type, aal.target_type, aal.created_at, u.username as admin_name
                FROM admin_activity_logs aal
                LEFT JOIN users u ON aal.admin_id = u.id
                ORDER BY aal.created_at DESC
                LIMIT 5
            ");
            $stmt->execute();
            $activityStats['admin_recent'] = $stmt->fetchAll();

            // إحصائيات أنواع الأنشطة الإدارية
            $stmt = $connection->prepare("
                SELECT action_type, COUNT(*) as count
                FROM admin_activity_logs
                WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAYS)
                GROUP BY action_type
                ORDER BY count DESC
                LIMIT 10
            ");
            $stmt->execute();
            $activityStats['admin_actions_week'] = $stmt->fetchAll();

        } catch (Exception $e) {
            $activityStats['admin_total'] = 0;
            $activityStats['admin_today'] = 0;
            $activityStats['admin_recent'] = [];
            $activityStats['admin_actions_week'] = [];
            $activityStats['admin_error'] = $e->getMessage();
        }
    } else {
        $activityStats['admin_total'] = 0;
        $activityStats['admin_today'] = 0;
        $activityStats['admin_recent'] = [];
        $activityStats['admin_actions_week'] = [];
    }

    // فحص جدول activity_logs العادي (إذا كان موجود)
    $checkActivityTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
    $checkActivityTable->execute();

    if ($checkActivityTable->rowCount() > 0) {
        try {
            // إجمالي الأنشطة العامة
            $stmt = $connection->prepare("SELECT COUNT(*) as total FROM activity_logs");
            $stmt->execute();
            $activityStats['general_total'] = $stmt->fetch()['total'];

            // الأنشطة العامة اليوم
            $stmt = $connection->prepare("SELECT COUNT(*) as today FROM activity_logs WHERE DATE(created_at) = CURDATE()");
            $stmt->execute();
            $activityStats['general_today'] = $stmt->fetch()['today'];

        } catch (Exception $e) {
            $activityStats['general_total'] = 0;
            $activityStats['general_today'] = 0;
            $activityStats['general_error'] = $e->getMessage();
        }
    } else {
        $activityStats['general_total'] = 0;
        $activityStats['general_today'] = 0;
    }

    // إحصائيات النزاعات من الجداول الجديدة
    $disputeStats = [];

    // فحص جدول dispute_resolutions
    $checkDisputeResolutionsTable = $connection->prepare("SHOW TABLES LIKE 'dispute_resolutions'");
    $checkDisputeResolutionsTable->execute();

    if ($checkDisputeResolutionsTable->rowCount() > 0) {
        try {
            // إجمالي قرارات النزاعات
            $stmt = $connection->prepare("SELECT COUNT(*) as total FROM dispute_resolutions");
            $stmt->execute();
            $disputeStats['resolutions_total'] = $stmt->fetch()['total'];

            // قرارات النزاعات هذا الشهر
            $stmt = $connection->prepare("
                SELECT COUNT(*) as monthly
                FROM dispute_resolutions
                WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())
            ");
            $stmt->execute();
            $disputeStats['resolutions_monthly'] = $stmt->fetch()['monthly'];

            // إحصائيات أنواع القرارات
            $stmt = $connection->prepare("
                SELECT resolution_type, COUNT(*) as count
                FROM dispute_resolutions
                GROUP BY resolution_type
            ");
            $stmt->execute();
            $disputeStats['resolution_types'] = $stmt->fetchAll();

        } catch (Exception $e) {
            $disputeStats['resolutions_error'] = $e->getMessage();
        }
    }

    // فحص جدول dispute_statistics
    $checkDisputeStatsTable = $connection->prepare("SHOW TABLES LIKE 'dispute_statistics'");
    $checkDisputeStatsTable->execute();

    if ($checkDisputeStatsTable->rowCount() > 0) {
        try {
            // آخر إحصائيات النزاعات
            $stmt = $connection->prepare("
                SELECT * FROM dispute_statistics
                ORDER BY date_recorded DESC
                LIMIT 1
            ");
            $stmt->execute();
            $disputeStats['latest_stats'] = $stmt->fetch();

            // متوسط وقت حل النزاعات
            $stmt = $connection->prepare("
                SELECT AVG(avg_resolution_time) as avg_time
                FROM dispute_statistics
                WHERE date_recorded >= DATE_SUB(CURDATE(), INTERVAL 30 DAYS)
            ");
            $stmt->execute();
            $result = $stmt->fetch();
            $disputeStats['avg_resolution_time'] = $result['avg_time'] ?? 0;

        } catch (Exception $e) {
            $disputeStats['stats_error'] = $e->getMessage();
        }
    }

    // إحصائيات التداولات المتقدمة من الجداول الجديدة
    $advancedTradeStats = [];

    // فحص جدول trade_statistics
    $checkTradeStatsTable = $connection->prepare("SHOW TABLES LIKE 'trade_statistics'");
    $checkTradeStatsTable->execute();

    if ($checkTradeStatsTable->rowCount() > 0) {
        try {
            // آخر إحصائيات التداولات
            $stmt = $connection->prepare("
                SELECT * FROM trade_statistics
                ORDER BY date_recorded DESC
                LIMIT 1
            ");
            $stmt->execute();
            $advancedTradeStats['latest_stats'] = $stmt->fetch();

            // إحصائيات الأسبوع الماضي
            $stmt = $connection->prepare("
                SELECT
                    SUM(total_trades) as week_trades,
                    SUM(completed_trades) as week_completed,
                    SUM(disputed_trades) as week_disputed,
                    AVG(completion_rate) as avg_completion_rate,
                    SUM(total_volume) as week_volume
                FROM trade_statistics
                WHERE date_recorded >= DATE_SUB(CURDATE(), INTERVAL 7 DAYS)
            ");
            $stmt->execute();
            $advancedTradeStats['week_summary'] = $stmt->fetch();

        } catch (Exception $e) {
            $advancedTradeStats['stats_error'] = $e->getMessage();
        }
    }

    // إحصائيات ملاحظات المدراء
    $notesStats = [];

    // فحص جدول admin_trade_notes
    $checkNotesTable = $connection->prepare("SHOW TABLES LIKE 'admin_trade_notes'");
    $checkNotesTable->execute();

    if ($checkNotesTable->rowCount() > 0) {
        try {
            // إجمالي الملاحظات
            $stmt = $connection->prepare("SELECT COUNT(*) as total FROM admin_trade_notes");
            $stmt->execute();
            $notesStats['total'] = $stmt->fetch()['total'];

            // الملاحظات هذا الشهر
            $stmt = $connection->prepare("
                SELECT COUNT(*) as monthly
                FROM admin_trade_notes
                WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())
            ");
            $stmt->execute();
            $notesStats['monthly'] = $stmt->fetch()['monthly'];

            // إحصائيات أنواع الملاحظات
            $stmt = $connection->prepare("
                SELECT visibility, COUNT(*) as count
                FROM admin_trade_notes
                GROUP BY visibility
            ");
            $stmt->execute();
            $notesStats['by_visibility'] = $stmt->fetchAll();

        } catch (Exception $e) {
            $notesStats['error'] = $e->getMessage();
        }
    }

    // معلومات النظام
    $systemStats = [
        'php_version' => PHP_VERSION,
        'mysql_version' => $connection->getAttribute(PDO::ATTR_SERVER_VERSION),
        'server_time' => date('Y-m-d H:i:s'),
        'uptime' => function_exists('sys_getloadavg') ? (sys_getloadavg()[0] ?? 'غير متاح') : 'غير متاح على Windows'
    ];
    
    // Success response
    echo json_encode([
        'success' => true,
        'data' => [
            'users' => $userStats,
            'offers' => $offerStats,
            'trades' => $tradeStats,
            'activity' => $activityStats,
            'disputes' => $disputeStats,
            'advanced_trades' => $advancedTradeStats,
            'admin_notes' => $notesStats,
            'system' => $systemStats
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    // Log database errors (in production, use proper logging)
    error_log('Database error in dashboard-stats.php: ' . $e->getMessage());
}
?>
