'use client';

import { useState, useEffect, useRef } from 'react';
import {
  Send,
  Paperclip,
  Image,
  MoreVertical,
  Shield,
  X,
  Flag,
  MessageSquare
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
// import { databaseService } from '@/services/databaseService';
import { walletService } from '@/services/walletService';
import { notificationService } from '@/services/notificationService';

// واجهة رسالة الدردشة
interface ChatMessage {
  id: string;
  trade_id: string;
  sender_id: string;
  sender_name: string;
  sender_role: 'seller' | 'buyer';
  message_type: 'text' | 'image' | 'file' | 'system';
  content: string;
  file_url?: string;
  file_name?: string;
  file_size?: number;
  timestamp: string;
  is_read: boolean;
  is_flagged: boolean;
  is_system: boolean;
  is_own: boolean;
}

// واجهة معلومات الصفقة
interface TradeInfo {
  id: string;
  blockchain_trade_id: number;
  seller_id: string;
  buyer_id: string;
  seller_name: string;
  buyer_name: string;
  amount: number;
  currency: string;
  stablecoin: string;
  status: string;
  is_seller: boolean;
  is_buyer: boolean;
}

interface TradeChatComponentProps {
  trade: TradeInfo;
  isOpen: boolean;
  onClose: () => void;
}

export default function TradeChatComponent({ trade, isOpen, onClose }: TradeChatComponentProps) {
  const { t, language } = useTranslation();

  // المراجع
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // الحالات
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [userAddress, setUserAddress] = useState<string | null>(null);

  // تحميل البيانات عند فتح الدردشة
  useEffect(() => {
    if (isOpen) {
      loadMessages();
      getUserAddress();
    }
  }, [isOpen, trade.id]);

  // التمرير إلى آخر رسالة
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  /**
   * الحصول على عنوان المستخدم الحالي
   */
  const getUserAddress = () => {
    const address = walletService.getCurrentAccount();
    setUserAddress(address);
  };

  /**
   * تحميل رسائل الدردشة
   */
  const loadMessages = async () => {
    try {
      setIsLoading(true);
      
      // في الوضع التجريبي، إنشاء رسائل وهمية
      const mockMessages: ChatMessage[] = [
        {
          id: 'msg-1',
          trade_id: trade.id,
          sender_id: trade.seller_id,
          sender_name: trade.seller_name,
          sender_role: 'seller',
          message_type: 'system',
          content: t('tradeChat.messages.tradeStarted'),
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          is_read: true,
          is_flagged: false,
          is_system: true,
          is_own: false
        },
        {
          id: 'msg-2',
          trade_id: trade.id,
          sender_id: trade.seller_id,
          sender_name: trade.seller_name,
          sender_role: 'seller',
          message_type: 'text',
          content: t('tradeChat.messages.sellerWelcome'),
          timestamp: new Date(Date.now() - 3500000).toISOString(),
          is_read: true,
          is_flagged: false,
          is_system: false,
          is_own: trade.is_seller
        },
        {
          id: 'msg-3',
          trade_id: trade.id,
          sender_id: trade.buyer_id,
          sender_name: trade.buyer_name,
          sender_role: 'buyer',
          message_type: 'text',
          content: t('tradeChat.messages.buyerResponse'),
          timestamp: new Date(Date.now() - 3000000).toISOString(),
          is_read: true,
          is_flagged: false,
          is_system: false,
          is_own: trade.is_buyer
        }
      ];

      setMessages(mockMessages);
    } catch (error) {
      console.error('خطأ في تحميل الرسائل:', error);
      notificationService.error(t('tradeChat.errors.loadMessages'));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * إرسال رسالة جديدة
   */
  const sendMessage = async () => {
    if (!newMessage.trim() || isSending) return;

    try {
      setIsSending(true);

      const messageData: ChatMessage = {
        id: `msg-${Date.now()}`,
        trade_id: trade.id,
        sender_id: userAddress || '',
        sender_name: trade.is_seller ? trade.seller_name : trade.buyer_name,
        sender_role: trade.is_seller ? 'seller' : 'buyer',
        message_type: 'text',
        content: newMessage.trim(),
        timestamp: new Date().toISOString(),
        is_read: false,
        is_flagged: false,
        is_system: false,
        is_own: true
      };

      // إضافة الرسالة محلياً
      setMessages(prev => [...prev, messageData]);
      setNewMessage('');

      // في التطبيق الحقيقي، إرسال إلى الخادم
      // await databaseService.sendChatMessage(messageData);

      notificationService.success(t('tradeChat.messages.messageSent'));
    } catch (error) {
      console.error('خطأ في إرسال الرسالة:', error);
      notificationService.error(t('tradeChat.errors.sendMessage'));
    } finally {
      setIsSending(false);
    }
  };

  /**
   * التعامل مع الضغط على Enter
   */
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  /**
   * التمرير إلى آخر رسالة
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  /**
   * تنسيق الوقت
   */
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return date.toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  /**
   * الإبلاغ عن رسالة
   */
  const reportMessage = (_messageId: string) => {
    // في التطبيق الحقيقي، إرسال تقرير للإدارة
    notificationService.success(t('tradeChat.messages.messageReported'));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-2xl h-[600px] flex flex-col">
        {/* رأس الدردشة */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center">
              <MessageSquare className="w-5 h-5 text-primary-600 dark:text-primary-400" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">
                {t('tradeChat.title')}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('tradeChat.subtitle', { 
                  tradeId: trade.blockchain_trade_id,
                  otherParty: trade.is_seller ? trade.buyer_name : trade.seller_name
                })}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
              title={t('tradeChat.actions.moreOptions')}
            >
              <MoreVertical className="w-5 h-5" />
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* منطقة الرسائل */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          ) : (
            <>
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.is_own ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[70%] ${
                      message.is_system
                        ? 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-center py-2 px-4 rounded-lg text-sm'
                        : message.is_own
                        ? 'bg-primary-600 text-white rounded-lg rounded-br-sm'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg rounded-bl-sm'
                    } p-3`}
                  >
                    {!message.is_system && !message.is_own && (
                      <div className="text-xs font-medium mb-1 opacity-70">
                        {message.sender_name}
                      </div>
                    )}
                    <div className="break-words">{message.content}</div>
                    <div className={`text-xs mt-1 flex items-center justify-between ${
                      message.is_own ? 'text-primary-100' : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      <span>{formatTime(message.timestamp)}</span>
                      {!message.is_system && !message.is_own && (
                        <button
                          onClick={() => reportMessage(message.id)}
                          className="opacity-0 group-hover:opacity-100 hover:text-red-500 transition-all"
                          title={t('tradeChat.actions.report')}
                        >
                          <Flag className="w-3 h-3" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </>
          )}
        </div>

        {/* منطقة إدخال الرسالة */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-end gap-3">
            <div className="flex-1">
              <textarea
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder={t('tradeChat.input.placeholder')}
                className="w-full resize-none border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                rows={2}
                maxLength={1000}
              />
              <div className="flex items-center justify-between mt-2">
                <div className="flex items-center gap-2">
                  <button
                    className="p-1 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                    title={t('tradeChat.actions.attachFile')}
                  >
                    <Paperclip className="w-4 h-4" />
                  </button>
                  <button
                    className="p-1 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                    title={t('tradeChat.actions.attachImage')}
                  >
                    <Image className="w-4 h-4" />
                  </button>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {newMessage.length}/1000
                </span>
              </div>
            </div>
            <button
              onClick={sendMessage}
              disabled={!newMessage.trim() || isSending}
              className="btn btn-primary px-4 py-2 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Send className="w-4 h-4" />
              {isSending ? t('tradeChat.actions.sending') : t('tradeChat.actions.send')}
            </button>
          </div>
        </div>

        {/* تحذير الأمان */}
        <div className="px-4 pb-4">
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <Shield className="w-4 h-4 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-yellow-800 dark:text-yellow-300">
                <strong>{t('tradeChat.security.title')}:</strong> {t('tradeChat.security.warning')}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
