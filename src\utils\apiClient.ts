/**
 * عميل API موحد
 * Unified API Client
 */

// الحصول على رابط API الأساسي
export const getApiBaseUrl = (): string => {
  // استخدام الرابط المباشر لـ XAMPP في جميع الحالات
  return process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost/ikaros-p2p/api';
};

// دالة مساعدة لإنشاء رابط API كامل
export const createApiUrl = (endpoint: string): string => {
  const baseUrl = getApiBaseUrl();
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${baseUrl}/${cleanEndpoint}`;
};

// دالة مساعدة لإرسال طلبات GET
export const apiGet = async (endpoint: string, options?: RequestInit) => {
  const url = createApiUrl(endpoint);

  console.log('API GET to:', url);

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Accept-Language': 'ar',
        'Content-Type': 'application/json'
      },
      ...options
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
    }

    const data = await response.json();
    console.log('API GET response:', data);
    return data;
  } catch (error) {
    console.error('API GET error:', error);
    throw error;
  }
};

// دالة مساعدة لإرسال طلبات POST
export const apiPost = async (endpoint: string, data?: any, options?: RequestInit) => {
  const url = createApiUrl(endpoint);

  console.log('API POST to:', url);
  console.log('API POST data:', data);

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Accept-Language': 'ar'
      },
      body: data ? JSON.stringify(data) : undefined,
      ...options
    });

    console.log('API POST response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API POST error:', errorText);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  } catch (error) {
    console.error('API POST error:', error);
    throw error;
  }
};

// دالة مساعدة للتعامل مع أخطاء API
export const handleApiError = (error: any): string => {
  if (error instanceof SyntaxError) {
    return 'استجابة غير صحيحة من الخادم';
  } else if (error.message?.includes('HTTP error')) {
    return 'خطأ في الشبكة، يرجى المحاولة مرة أخرى';
  } else if (error.message?.includes('Failed to fetch')) {
    return 'فشل في الاتصال بالخادم، تحقق من الاتصال بالإنترنت';
  } else {
    return error.message || 'حدث خطأ غير متوقع';
  }
};