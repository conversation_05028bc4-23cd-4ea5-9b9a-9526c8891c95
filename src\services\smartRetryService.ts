/**
 * خدمة إعادة المحاولة الذكية
 * Smart Retry Service
 */

import { failureTrackingService } from './failureTrackingService';
import { notificationService } from './notificationService';

interface RetryConfig {
  maxAttempts: number;
  baseDelay: number; // بالميلي ثانية
  maxDelay: number;
  backoffMultiplier: number;
  retryableErrors: string[];
  showUserPrompt: boolean;
}

interface RetryResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  attempts: number;
  totalTime: number;
}

class SmartRetryService {
  private defaultConfig: RetryConfig = {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2,
    retryableErrors: [
      'network_timeout',
      'packet_loss',
      'connection_error',
      'server_unavailable',
      'rate_limit'
    ],
    showUserPrompt: true
  };

  // تنفيذ طلب مع إعادة المحاولة الذكية
  public async executeWithRetry<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {},
    context: {
      requestType: string;
      stage: string;
      userId?: string;
      tradeId?: string;
    }
  ): Promise<RetryResult<T>> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const startTime = Date.now();
    let lastError: any;
    
    for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
      try {
        const result = await operation();
        
        // نجح الطلب
        if (attempt > 1) {
          notificationService.success(`تم الإرسال بنجاح بعد ${attempt} محاولات`);
        }
        
        return {
          success: true,
          data: result,
          attempts: attempt,
          totalTime: Date.now() - startTime
        };
        
      } catch (error: any) {
        lastError = error;
        
        // تسجيل حالة الفشل
        failureTrackingService.logFailure({
          networkCondition: this.getCurrentNetworkCondition(),
          requestType: context.requestType as any,
          stage: context.stage as any,
          errorType: this.categorizeError(error),
          errorMessage: error.message || 'Unknown error',
          requestUrl: this.extractUrl(error),
          responseTime: Date.now() - startTime,
          retryAttempt: attempt,
          userAgent: navigator.userAgent,
          userId: context.userId,
          tradeId: context.tradeId,
          additionalData: { originalError: error }
        });
        
        // التحقق من إمكانية إعادة المحاولة
        if (!this.isRetryableError(error, finalConfig.retryableErrors)) {
          break;
        }
        
        // إذا كانت هذه المحاولة الأخيرة
        if (attempt === finalConfig.maxAttempts) {
          break;
        }
        
        // عرض رسالة للمستخدم إذا لزم الأمر
        if (finalConfig.showUserPrompt && attempt === 1) {
          const shouldRetry = await this.askUserForRetry(error, context.requestType);
          if (!shouldRetry) {
            break;
          }
        }
        
        // انتظار قبل إعادة المحاولة
        const delay = this.calculateDelay(attempt, finalConfig);
        await this.delay(delay);
        
        notificationService.info(`إعادة المحاولة ${attempt + 1}/${finalConfig.maxAttempts}...`);
      }
    }
    
    // فشل جميع المحاولات
    return {
      success: false,
      error: lastError?.message || 'Unknown error',
      attempts: finalConfig.maxAttempts,
      totalTime: Date.now() - startTime
    };
  }

  // تصنيف نوع الخطأ
  private categorizeError(error: any): 'network_timeout' | 'packet_loss' | 'server_error' | 'blockchain_error' | 'validation_error' {
    const message = error.message?.toLowerCase() || '';
    
    if (message.includes('timeout') || message.includes('aborted')) {
      return 'network_timeout';
    }
    if (message.includes('packet loss') || message.includes('network')) {
      return 'packet_loss';
    }
    if (message.includes('server') || error.status >= 500) {
      return 'server_error';
    }
    if (message.includes('blockchain') || message.includes('contract')) {
      return 'blockchain_error';
    }
    
    return 'validation_error';
  }

  // التحقق من إمكانية إعادة المحاولة
  private isRetryableError(error: any, retryableErrors: string[]): boolean {
    const errorType = this.categorizeError(error);
    return retryableErrors.includes(errorType);
  }

  // حساب تأخير إعادة المحاولة (Exponential Backoff)
  private calculateDelay(attempt: number, config: RetryConfig): number {
    const delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1);
    return Math.min(delay, config.maxDelay);
  }

  // سؤال المستخدم عن إعادة المحاولة
  private async askUserForRetry(error: any, requestType: string): Promise<boolean> {
    return new Promise((resolve) => {
      const errorMessage = this.getUserFriendlyErrorMessage(error);
      
      // إنشاء modal للسؤال
      const modal = document.createElement('div');
      modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
      modal.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-4">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center ml-3">
              <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">فشل في الإرسال</h3>
          </div>
          
          <p class="text-gray-600 dark:text-gray-300 mb-4">${errorMessage}</p>
          
          <div class="flex space-x-3 rtl:space-x-reverse">
            <button id="retry-btn" class="btn btn-primary flex-1">
              إعادة المحاولة
            </button>
            <button id="cancel-btn" class="btn btn-secondary flex-1">
              إلغاء
            </button>
          </div>
        </div>
      `;
      
      document.body.appendChild(modal);
      
      // معالجة الأحداث
      modal.querySelector('#retry-btn')?.addEventListener('click', () => {
        document.body.removeChild(modal);
        resolve(true);
      });
      
      modal.querySelector('#cancel-btn')?.addEventListener('click', () => {
        document.body.removeChild(modal);
        resolve(false);
      });
      
      // إغلاق عند النقر خارج المودال
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          document.body.removeChild(modal);
          resolve(false);
        }
      });
    });
  }

  // رسالة خطأ مفهومة للمستخدم
  private getUserFriendlyErrorMessage(error: any): string {
    const errorType = this.categorizeError(error);
    
    switch (errorType) {
      case 'network_timeout':
        return 'انتهت مهلة الاتصال. قد يكون الاتصال بالإنترنت بطيئاً.';
      case 'packet_loss':
        return 'فشل الإرسال بسبب انقطاع في الاتصال.';
      case 'server_error':
        return 'خطأ في الخادم. يرجى المحاولة مرة أخرى.';
      case 'blockchain_error':
        return 'خطأ في شبكة البلوك تشين. قد تكون الشبكة مزدحمة.';
      default:
        return 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
    }
  }

  // استخراج URL من الخطأ
  private extractUrl(error: any): string {
    return error.config?.url || error.url || 'unknown';
  }

  // الحصول على حالة الشبكة الحالية
  private getCurrentNetworkCondition(): string {
    // يمكن ربطها مع networkSimulationService
    return 'unknown';
  }

  // دالة انتظار
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // إعدادات مخصصة لأنواع مختلفة من الطلبات
  public getConfigForRequestType(requestType: string): Partial<RetryConfig> {
    switch (requestType) {
      case 'create_offer':
      case 'join_trade':
        return {
          maxAttempts: 3,
          showUserPrompt: true,
          retryableErrors: ['network_timeout', 'packet_loss', 'server_error']
        };
      
      case 'confirm_payment':
      case 'complete_trade':
        return {
          maxAttempts: 5, // أهمية عالية
          showUserPrompt: true,
          baseDelay: 2000,
          retryableErrors: ['network_timeout', 'packet_loss', 'server_error', 'blockchain_error']
        };
      
      case 'sync':
        return {
          maxAttempts: 2,
          showUserPrompt: false, // تلقائي للمزامنة
          retryableErrors: ['network_timeout', 'packet_loss']
        };
      
      default:
        return {};
    }
  }

  // دالة مساعدة لتنفيذ طلبات HTTP مع إعادة المحاولة
  public async fetchWithRetry(
    url: string,
    options: RequestInit = {},
    context: { requestType: string; stage: string; userId?: string; tradeId?: string }
  ): Promise<RetryResult<Response>> {
    const config = this.getConfigForRequestType(context.requestType);
    
    return this.executeWithRetry(
      () => fetch(url, options),
      config,
      context
    );
  }
}

// إنشاء مثيل واحد للخدمة
export const smartRetryService = new SmartRetryService();
export default smartRetryService;
