// خدمة المحفظة المحسنة - Enhanced Wallet Service
// تعمل بجانب walletService الموجود دون تضارب

import { ethers } from 'ethers';
import { walletService } from './walletService';
import { smartNotificationService } from './smartNotificationService';
import { notificationService } from './notificationService';

// أنواع البيانات المحسنة
export interface EnhancedWalletBalance {
  currency: string;
  balance: number;
  usdValue: number;
  change24h: number;
  isStablecoin: boolean;
  contractAddress?: string;
  decimals: number;
  lastUpdated: Date;
}

export interface EnhancedTransaction {
  id: string;
  type: 'deposit' | 'withdrawal' | 'trade_escrow' | 'trade_release' | 'contract_interaction';
  amount: number;
  currency: string;
  fee: number;
  gasUsed?: number;
  gasPrice?: string;
  transactionHash?: string;
  blockNumber?: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  description: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  confirmations?: number;
  fromAddress?: string;
  toAddress?: string;
}

export interface WalletStats {
  totalBalance: number;
  totalBalanceUSD: number;
  change24h: number;
  transactionCount: number;
  successRate: number;
  avgTransactionTime: number;
  topCurrency: string;
}

export interface WalletSecurity {
  isHardwareWallet: boolean;
  hasBackup: boolean;
  lastSecurityCheck: Date;
  riskLevel: 'low' | 'medium' | 'high';
  recommendations: string[];
}

class EnhancedWalletService {
  private balanceCache: Map<string, EnhancedWalletBalance> = new Map();
  private transactionCache: Map<string, EnhancedTransaction[]> = new Map();
  private priceCache: Map<string, number> = new Map();
  private isInitialized = false;
  private updateInterval: NodeJS.Timeout | null = null;

  /**
   * تهيئة الخدمة المحسنة
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // تهيئة التحديث التلقائي للأرصدة
      this.startAutoUpdate();
      
      // تهيئة مراقبة أحداث المحفظة
      this.setupWalletEventListeners();
      
      this.isInitialized = true;
      console.log('✅ تم تهيئة خدمة المحفظة المحسنة');
    } catch (error) {
      console.error('❌ خطأ في تهيئة خدمة المحفظة المحسنة:', error);
    }
  }

  /**
   * بدء التحديث التلقائي
   */
  private startAutoUpdate(): void {
    // تحديث كل 30 ثانية
    this.updateInterval = setInterval(() => {
      this.updateBalancesFromBlockchain();
    }, 30000);
  }

  /**
   * إعداد مستمعي أحداث المحفظة
   */
  private setupWalletEventListeners(): void {
    // مراقبة تغيير الحسابات
    walletService.onAccountChange((accounts) => {
      if (accounts.length === 0) {
        this.clearCache();
        smartNotificationService.createSmartNotification({
          type: 'security',
          priority: 'high',
          title: 'تم قطع اتصال المحفظة',
          message: 'تم قطع اتصال محفظتك. يرجى إعادة الاتصال للمتابعة.',
          isPersistent: true
        });
      } else {
        this.refreshWalletData();
        smartNotificationService.createSmartNotification({
          type: 'system',
          priority: 'medium',
          title: 'تم تغيير حساب المحفظة',
          message: `تم التبديل إلى الحساب: ${this.formatAddress(accounts[0])}`,
          isPersistent: false
        });
      }
    });

    // مراقبة تغيير الشبكة
    walletService.onChainChange((chainId) => {
      this.clearCache();
      this.refreshWalletData();
      smartNotificationService.createSmartNotification({
        type: 'system',
        priority: 'medium',
        title: 'تم تغيير الشبكة',
        message: `تم التبديل إلى شبكة جديدة: ${chainId}`,
        isPersistent: false
      });
    });
  }

  /**
   * الحصول على الأرصدة المحسنة
   */
  async getEnhancedBalances(userId: number, forceRefresh = false): Promise<EnhancedWalletBalance[]> {
    const cacheKey = `balances_${userId}`;
    
    if (!forceRefresh && this.balanceCache.has(cacheKey)) {
      const cached = this.balanceCache.get(cacheKey);
      if (cached && this.isCacheValid(cached.lastUpdated)) {
        return [cached];
      }
    }

    try {
      // جلب الأرصدة من API
      const response = await fetch(`/api/wallet/index.php?user_id=${userId}&action=balance`);
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'فشل في جلب الأرصدة');
      }

      const balances: EnhancedWalletBalance[] = [];
      
      for (const [currency, balance] of Object.entries(result.data.balances)) {
        const enhancedBalance: EnhancedWalletBalance = {
          currency,
          balance: Number(balance),
          usdValue: await this.getUSDValue(currency, Number(balance)),
          change24h: await this.get24hChange(currency),
          isStablecoin: this.isStablecoin(currency),
          decimals: this.getCurrencyDecimals(currency),
          lastUpdated: new Date()
        };

        balances.push(enhancedBalance);
        this.balanceCache.set(`${cacheKey}_${currency}`, enhancedBalance);
      }

      return balances;
    } catch (error) {
      console.error('خطأ في جلب الأرصدة المحسنة:', error);
      throw error;
    }
  }

  /**
   * الحصول على المعاملات المحسنة
   */
  async getEnhancedTransactions(
    userId: number, 
    options: {
      page?: number;
      limit?: number;
      type?: string;
      currency?: string;
      status?: string;
    } = {}
  ): Promise<{ transactions: EnhancedTransaction[]; pagination: any }> {
    const { page = 1, limit = 20, type = 'all', currency = 'all', status = 'all' } = options;
    
    try {
      const params = new URLSearchParams({
        user_id: userId.toString(),
        action: 'transactions',
        page: page.toString(),
        limit: limit.toString()
      });

      if (type !== 'all') params.append('type', type);
      if (currency !== 'all') params.append('currency', currency);
      if (status !== 'all') params.append('status', status);

      const response = await fetch(`/api/wallet/index.php?${params}`);
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'فشل في جلب المعاملات');
      }

      const enhancedTransactions: EnhancedTransaction[] = result.data.map((tx: any) => ({
        id: tx.id,
        type: tx.type,
        amount: Number(tx.amount),
        currency: tx.currency,
        fee: Number(tx.fee || 0),
        transactionHash: tx.transaction_hash,
        status: tx.status,
        description: tx.description,
        createdAt: new Date(tx.created_at),
        updatedAt: new Date(tx.updated_at || tx.created_at),
        metadata: tx.metadata ? JSON.parse(tx.metadata) : {}
      }));

      // إضافة معلومات إضافية من البلوك تشين إذا كان هناك hash
      for (const tx of enhancedTransactions) {
        if (tx.transactionHash && walletService.isWalletConnected()) {
          try {
            const blockchainInfo = await this.getTransactionFromBlockchain(tx.transactionHash);
            if (blockchainInfo) {
              tx.gasUsed = blockchainInfo.gasUsed;
              tx.gasPrice = blockchainInfo.gasPrice;
              tx.blockNumber = blockchainInfo.blockNumber;
              tx.confirmations = blockchainInfo.confirmations;
              tx.fromAddress = blockchainInfo.from;
              tx.toAddress = blockchainInfo.to;
            }
          } catch (error) {
            console.warn(`فشل في جلب معلومات المعاملة من البلوك تشين: ${tx.transactionHash}`, error);
          }
        }
      }

      return {
        transactions: enhancedTransactions,
        pagination: result.pagination
      };
    } catch (error) {
      console.error('خطأ في جلب المعاملات المحسنة:', error);
      throw error;
    }
  }

  /**
   * الحصول على إحصائيات المحفظة
   */
  async getWalletStats(userId: number): Promise<WalletStats> {
    try {
      const balances = await this.getEnhancedBalances(userId);
      const { transactions } = await this.getEnhancedTransactions(userId, { limit: 100 });

      const totalBalance = balances.reduce((sum, b) => sum + b.balance, 0);
      const totalBalanceUSD = balances.reduce((sum, b) => sum + b.usdValue, 0);
      const change24h = balances.reduce((sum, b) => sum + (b.change24h * b.usdValue / 100), 0);
      
      const completedTransactions = transactions.filter(t => t.status === 'completed');
      const successRate = transactions.length > 0 ? (completedTransactions.length / transactions.length) * 100 : 0;
      
      // حساب متوسط وقت المعاملة (بالدقائق)
      const avgTransactionTime = completedTransactions.length > 0 
        ? completedTransactions.reduce((sum, tx) => {
            const timeDiff = tx.updatedAt.getTime() - tx.createdAt.getTime();
            return sum + (timeDiff / (1000 * 60)); // تحويل إلى دقائق
          }, 0) / completedTransactions.length
        : 0;

      // العملة الأكثر استخداماً
      const currencyCount = transactions.reduce((acc, tx) => {
        acc[tx.currency] = (acc[tx.currency] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      const topCurrency = Object.entries(currencyCount)
        .sort(([,a], [,b]) => b - a)[0]?.[0] || 'USDT';

      return {
        totalBalance,
        totalBalanceUSD,
        change24h: totalBalanceUSD > 0 ? (change24h / totalBalanceUSD) * 100 : 0,
        transactionCount: transactions.length,
        successRate,
        avgTransactionTime,
        topCurrency
      };
    } catch (error) {
      console.error('خطأ في حساب إحصائيات المحفظة:', error);
      throw error;
    }
  }

  /**
   * تحديث الأرصدة من البلوك تشين
   */
  private async updateBalancesFromBlockchain(): Promise<void> {
    if (!walletService.isWalletConnected()) return;

    try {
      const walletInfo = await walletService.getWalletInfo();
      if (!walletInfo) return;

      // تحديث رصيد BNB
      const bnbBalance = parseFloat(walletInfo.balance);
      
      // تحديث رصيد USDT (إذا كان العقد متاح)
      // TODO: إضافة عناوين العقود للعملات المختلفة
      
      // إرسال إشعار إذا كان هناك تغيير كبير في الرصيد
      // TODO: مقارنة مع الرصيد السابق وإرسال إشعار عند التغيير
      
    } catch (error) {
      console.error('خطأ في تحديث الأرصدة من البلوك تشين:', error);
    }
  }

  /**
   * الحصول على معلومات المعاملة من البلوك تشين
   */
  private async getTransactionFromBlockchain(txHash: string): Promise<any> {
    // TODO: تنفيذ جلب معلومات المعاملة من البلوك تشين
    return null;
  }

  /**
   * الحصول على القيمة بالدولار
   */
  private async getUSDValue(currency: string, amount: number): Promise<number> {
    try {
      const price = await this.getCurrencyPrice(currency);
      return amount * price;
    } catch (error) {
      console.error(`خطأ في جلب سعر ${currency}:`, error);
      return 0;
    }
  }

  /**
   * الحصول على سعر العملة
   */
  private async getCurrencyPrice(currency: string): Promise<number> {
    const cacheKey = `price_${currency}`;
    
    if (this.priceCache.has(cacheKey)) {
      return this.priceCache.get(cacheKey)!;
    }

    try {
      // TODO: تنفيذ جلب الأسعار من API خارجي
      const mockPrices: Record<string, number> = {
        'USDT': 1.0,
        'BTC': 45000,
        'ETH': 3000,
        'BNB': 300
      };

      const price = mockPrices[currency] || 0;
      this.priceCache.set(cacheKey, price);
      
      // انتهاء صلاحية الكاش بعد 5 دقائق
      setTimeout(() => {
        this.priceCache.delete(cacheKey);
      }, 5 * 60 * 1000);

      return price;
    } catch (error) {
      console.error(`خطأ في جلب سعر ${currency}:`, error);
      return 0;
    }
  }

  /**
   * الحصول على التغيير في 24 ساعة
   */
  private async get24hChange(currency: string): Promise<number> {
    // TODO: تنفيذ جلب التغيير في 24 ساعة
    return Math.random() * 10 - 5; // قيمة وهمية للاختبار
  }

  /**
   * التحقق من كون العملة مستقرة
   */
  private isStablecoin(currency: string): boolean {
    const stablecoins = ['USDT', 'USDC', 'BUSD', 'DAI', 'FDUSD', 'TUSD', 'USDD', 'FRAX', 'USDP', 'LUSD', 'GUSD', 'SUSD', 'USTC', 'PYUSD'];
    return stablecoins.includes(currency.toUpperCase());
  }

  /**
   * الحصول على عدد الخانات العشرية للعملة
   */
  private getCurrencyDecimals(currency: string): number {
    const decimals: Record<string, number> = {
      'USDT': 6,
      'USDC': 6,
      'BTC': 8,
      'ETH': 18,
      'BNB': 18
    };
    return decimals[currency] || 18;
  }

  /**
   * التحقق من صحة الكاش
   */
  private isCacheValid(lastUpdated: Date): boolean {
    const now = new Date();
    const diffMinutes = (now.getTime() - lastUpdated.getTime()) / (1000 * 60);
    return diffMinutes < 5; // صالح لمدة 5 دقائق
  }

  /**
   * مسح الكاش
   */
  private clearCache(): void {
    this.balanceCache.clear();
    this.transactionCache.clear();
    this.priceCache.clear();
  }

  /**
   * تحديث بيانات المحفظة
   */
  private async refreshWalletData(): Promise<void> {
    this.clearCache();
    // TODO: إعادة تحميل البيانات
  }

  /**
   * تنسيق العنوان
   */
  private formatAddress(address: string): string {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  }

  /**
   * تنظيف الموارد
   */
  destroy(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    this.clearCache();
    this.isInitialized = false;
  }
}

// إنشاء مثيل واحد من الخدمة
export const enhancedWalletService = new EnhancedWalletService();

// تصدير الخدمة كـ default أيضاً
export default enhancedWalletService;
