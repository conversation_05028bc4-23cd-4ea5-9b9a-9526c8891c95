import React, { useState, useEffect } from 'react';
import { Fuel, Wallet, RefreshCw, ExternalLink, AlertTriangle } from 'lucide-react';
import { contractService } from '@/services/contractService';
import { notificationService } from '@/services/notificationService';

interface GasInfo {
  pauseGas?: string;
  unpauseGas?: string;
  transferOwnershipGas?: string;
  error?: string;
}

interface BalanceInfo {
  bnb: number;
  address: string;
  isLow: boolean;
}

export const GasAndBalanceInfo: React.FC = () => {
  const [balanceInfo, setBalanceInfo] = useState<BalanceInfo | null>(null);
  const [gasInfo, setGasInfo] = useState<GasInfo>({});
  const [isLoading, setIsLoading] = useState(false);

  const loadInfo = async () => {
    setIsLoading(true);
    try {
      // التحقق من الاتصال
      const isConnected = await contractService.ensureConnection();
      if (!isConnected) {
        setBalanceInfo(null);
        setGasInfo({ error: 'المحفظة غير متصلة' });
        setIsLoading(false);
        return;
      }

      // جلب معلومات الرصيد
      const provider = (contractService as any).provider;
      const signer = await provider.getSigner();
      const address = await signer.getAddress();
      const balance = await provider.getBalance(address);
      const balanceInBNB = Number(balance) / 1e18;

      setBalanceInfo({
        bnb: balanceInBNB,
        address,
        isLow: balanceInBNB < 0.001
      });

      // تقدير Gas للوظائف المختلفة
      const contract = (contractService as any).escrowContract;
      const gasEstimates: GasInfo = {};

      try {
        // تقدير gas لوظيفة pause
        if (contract && contract.pauseContract) {
          const pauseGas = await contract.pauseContract.estimateGas();
          gasEstimates.pauseGas = pauseGas.toString();
        }
      } catch (error: any) {
        console.log('Cannot estimate pause gas:', error.message);
      }

      try {
        // تقدير gas لوظيفة unpause
        if (contract && contract.unpauseContract) {
          const unpauseGas = await contract.unpauseContract.estimateGas();
          gasEstimates.unpauseGas = unpauseGas.toString();
        }
      } catch (error: any) {
        console.log('Cannot estimate unpause gas:', error.message);
      }

      try {
        // تقدير gas لتحويل الملكية (مع عنوان وهمي)
        if (contract && contract.transferOwnership) {
          const dummyAddress = '0x0000000000000000000000000000000000000001';
          const transferGas = await contract.transferOwnership.estimateGas(dummyAddress);
          gasEstimates.transferOwnershipGas = transferGas.toString();
        }
      } catch (error: any) {
        console.log('Cannot estimate transfer ownership gas:', error.message);
      }

      setGasInfo(gasEstimates);

    } catch (error: any) {
      console.error('Error loading gas and balance info:', error);
      setGasInfo({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadInfo();
  }, []);

  const formatGas = (gas: string) => {
    const gasNumber = parseInt(gas);
    return gasNumber.toLocaleString();
  };

  const estimateGasCost = (gas: string, gasPriceGwei: number = 5) => {
    const gasNumber = parseInt(gas);
    const gasCostWei = gasNumber * gasPriceGwei * 1e9;
    const gasCostBNB = gasCostWei / 1e18;
    return gasCostBNB.toFixed(6);
  };

  const openFaucet = () => {
    window.open('https://testnet.binance.org/faucet-smart', '_blank');
  };

  const openExplorer = () => {
    if (balanceInfo?.address) {
      window.open(`https://testnet.bscscan.com/address/${balanceInfo.address}`, '_blank');
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
            <Fuel className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              الرصيد والتكاليف
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              معلومات BNB وتقديرات Gas
            </p>
          </div>
        </div>
        <button
          onClick={loadInfo}
          disabled={isLoading}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          تحديث
        </button>
      </div>

      {gasInfo.error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="w-5 h-5" />
            <span className="font-medium">خطأ</span>
          </div>
          <p className="text-red-600 mt-1">{gasInfo.error}</p>
        </div>
      )}

      {/* معلومات الرصيد */}
      {balanceInfo && (
        <div className="mb-6">
          <div className={`p-6 rounded-xl border-l-4 ${
            balanceInfo.isLow
              ? 'bg-red-50 border-red-400 dark:bg-red-900/20 dark:border-red-600'
              : 'bg-green-50 border-green-400 dark:bg-green-900/20 dark:border-green-600'
          }`}>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                  balanceInfo.isLow ? 'bg-red-100 dark:bg-red-900/30' : 'bg-green-100 dark:bg-green-900/30'
                }`}>
                  <Wallet className={`w-6 h-6 ${balanceInfo.isLow ? 'text-red-600' : 'text-green-600'}`} />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">
                    رصيد المحفظة
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 font-mono" title={balanceInfo.address}>
                    {balanceInfo.address.slice(0, 10)}...{balanceInfo.address.slice(-8)}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <button
                  onClick={openExplorer}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                  title="عرض في BSCScan"
                >
                  <ExternalLink className="w-4 h-4" />
                </button>
                {balanceInfo.isLow && (
                  <button
                    onClick={openFaucet}
                    className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    احصل على BNB
                  </button>
                )}
              </div>
            </div>

            <div className="flex items-center gap-3">
              <span className="text-3xl font-bold text-gray-900 dark:text-white">
                {balanceInfo.bnb.toFixed(6)}
              </span>
              <span className="text-lg font-medium text-gray-600 dark:text-gray-400">
                BNB
              </span>
              {balanceInfo.isLow && (
                <div className="flex items-center gap-1 px-2 py-1 bg-red-100 dark:bg-red-900/30 rounded-full">
                  <AlertTriangle className="w-4 h-4 text-red-500" />
                  <span className="text-xs text-red-600 dark:text-red-400 font-medium">
                    رصيد منخفض
                  </span>
                </div>
              )}
            </div>

            {balanceInfo.isLow && (
              <div className="mt-3 p-3 bg-red-100 dark:bg-red-900/30 rounded-lg">
                <p className="text-sm text-red-700 dark:text-red-300">
                  ⚠️ تحتاج على الأقل 0.001 BNB لتنفيذ المعاملات
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* معلومات Gas */}
      <div>
        <h4 className="font-medium text-gray-900 dark:text-white mb-3">
          تقديرات Gas للوظائف
        </h4>
        
        <div className="space-y-3">
          {gasInfo.pauseGas && (
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg min-w-0">
              <span className="text-sm text-gray-600 dark:text-gray-400 flex-shrink-0">إيقاف العقد (Pause)</span>
              <div className="text-right min-w-0">
                <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {formatGas(gasInfo.pauseGas)} Gas
                </div>
                <div className="text-xs text-gray-500 truncate">
                  ≈ {estimateGasCost(gasInfo.pauseGas)} BNB
                </div>
              </div>
            </div>
          )}

          {gasInfo.unpauseGas && (
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg min-w-0">
              <span className="text-sm text-gray-600 dark:text-gray-400 flex-shrink-0">تشغيل العقد (Unpause)</span>
              <div className="text-right min-w-0">
                <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {formatGas(gasInfo.unpauseGas)} Gas
                </div>
                <div className="text-xs text-gray-500 truncate">
                  ≈ {estimateGasCost(gasInfo.unpauseGas)} BNB
                </div>
              </div>
            </div>
          )}

          {gasInfo.transferOwnershipGas && (
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg min-w-0">
              <span className="text-sm text-gray-600 dark:text-gray-400 flex-shrink-0">تحويل الملكية</span>
              <div className="text-right min-w-0">
                <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {formatGas(gasInfo.transferOwnershipGas)} Gas
                </div>
                <div className="text-xs text-gray-500 truncate">
                  ≈ {estimateGasCost(gasInfo.transferOwnershipGas)} BNB
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <p className="text-xs text-blue-600 dark:text-blue-400">
            💡 التقديرات تعتمد على سعر Gas = 5 Gwei. الأسعار الفعلية قد تختلف حسب ازدحام الشبكة.
          </p>
        </div>
      </div>
    </div>
  );
};

export default GasAndBalanceInfo;
