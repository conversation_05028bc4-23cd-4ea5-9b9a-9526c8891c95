'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet, apiPut, apiDelete, handleApiError } from '@/utils/apiClient';

export interface Notification {
  id: number;
  user_id: number;
  type: 'trade' | 'offer' | 'system' | 'security' | 'marketing' | 'payment' | 'dispute';
  title: string;
  message: string;
  data?: any;
  is_read: boolean;
  action_url?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  created_at: string;
  updated_at: string;
  read_at?: string;
  
  // معلومات إضافية
  icon?: string;
  color?: string;
  category?: string;
}

export interface NotificationFilters {
  type?: string;
  is_read?: boolean;
  priority?: string;
  period?: 'today' | 'week' | 'month' | 'all';
}

interface UseUserNotificationsOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  filters?: NotificationFilters;
  pageSize?: number;
}

interface UseUserNotificationsReturn {
  notifications: Notification[];
  unreadNotifications: Notification[];
  readNotifications: Notification[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  unreadCount: number;
  currentPage: number;
  totalPages: number;
  refresh: () => Promise<void>;
  updateFilters: (filters: NotificationFilters) => void;
  loadMore: () => Promise<void>;
  markAsRead: (notificationId: number) => Promise<void>;
  markAsUnread: (notificationId: number) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: number) => Promise<void>;
  deleteAllRead: () => Promise<void>;
  filters: NotificationFilters;
  hasMore: boolean;
}

/**
 * Hook لإدارة إشعارات المستخدم
 */
export function useUserNotifications(options: UseUserNotificationsOptions = {}): UseUserNotificationsReturn {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<NotificationFilters>(options.filters || {});
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [unreadCount, setUnreadCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  const {
    autoRefresh = true,
    refreshInterval = 30 * 1000, // 30 ثانية للإشعارات
    pageSize = 20
  } = options;

  /**
   * جلب إشعارات المستخدم من API
   */
  const fetchNotifications = useCallback(async (page = 1, append = false) => {
    if (!user?.id) {
      setError('معرف المستخدم غير متاح');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // بناء معاملات الاستعلام
      const queryParams = new URLSearchParams({
        user_id: user.id.toString(),
        page: page.toString(),
        limit: pageSize.toString(),
        ...filters
      });

      const result = await apiGet(`notifications/index.php?${queryParams.toString()}`);

      if (!result.success) {
        throw new Error(result.error || 'فشل في جلب الإشعارات');
      }

      const { 
        notifications: newNotifications, 
        total, 
        unread_count,
        current_page, 
        total_pages 
      } = result.data;

      if (append) {
        setNotifications(prev => [...prev, ...newNotifications]);
      } else {
        setNotifications(newNotifications);
      }

      setTotalCount(total);
      setUnreadCount(unread_count);
      setCurrentPage(current_page);
      setHasMore(current_page < total_pages);

    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('Error fetching notifications:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.id, filters, pageSize]);

  /**
   * تحديد إشعار كمقروء
   */
  const markAsRead = useCallback(async (notificationId: number) => {
    try {
      const result = await apiPut(`notifications/mark-read.php`, {
        notification_id: notificationId,
        is_read: true
      });

      if (!result.success) {
        throw new Error(result.error || 'فشل في تحديث حالة الإشعار');
      }

      // تحديث الإشعار في القائمة المحلية
      setNotifications(prev => prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, is_read: true, read_at: new Date().toISOString() }
          : notification
      ));

      // تقليل عدد الإشعارات غير المقروءة
      setUnreadCount(prev => Math.max(0, prev - 1));

    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('Error marking notification as read:', err);
    }
  }, []);

  /**
   * تحديد إشعار كغير مقروء
   */
  const markAsUnread = useCallback(async (notificationId: number) => {
    try {
      const result = await apiPut(`notifications/mark-read.php`, {
        notification_id: notificationId,
        is_read: false
      });

      if (!result.success) {
        throw new Error(result.error || 'فشل في تحديث حالة الإشعار');
      }

      // تحديث الإشعار في القائمة المحلية
      setNotifications(prev => prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, is_read: false, read_at: undefined }
          : notification
      ));

      // زيادة عدد الإشعارات غير المقروءة
      setUnreadCount(prev => prev + 1);

    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('Error marking notification as unread:', err);
    }
  }, []);

  /**
   * تحديد جميع الإشعارات كمقروءة
   */
  const markAllAsRead = useCallback(async () => {
    try {
      const result = await apiPut(`notifications/mark-read.php`, {
        mark_all: true,
        user_id: user?.id
      });

      if (!result.success) {
        throw new Error(result.error || 'فشل في تحديث الإشعارات');
      }

      // تحديث جميع الإشعارات في القائمة المحلية
      setNotifications(prev => prev.map(notification => ({
        ...notification,
        is_read: true,
        read_at: new Date().toISOString()
      })));

      setUnreadCount(0);

    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('Error marking all notifications as read:', err);
    }
  }, [user?.id]);

  /**
   * حذف إشعار
   */
  const deleteNotification = useCallback(async (notificationId: number) => {
    try {
      const result = await apiDelete(`notifications/index.php?notification_id=${notificationId}`);

      if (!result.success) {
        throw new Error(result.error || 'فشل في حذف الإشعار');
      }

      // إزالة الإشعار من القائمة المحلية
      const deletedNotification = notifications.find(n => n.id === notificationId);
      setNotifications(prev => prev.filter(notification => notification.id !== notificationId));
      setTotalCount(prev => prev - 1);

      // تقليل عدد الإشعارات غير المقروءة إذا كان الإشعار المحذوف غير مقروء
      if (deletedNotification && !deletedNotification.is_read) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }

    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('Error deleting notification:', err);
    }
  }, [notifications]);

  /**
   * حذف جميع الإشعارات المقروءة
   */
  const deleteAllRead = useCallback(async () => {
    try {
      const result = await apiDelete(`notifications/index.php?delete_all_read=true&user_id=${user?.id}`);

      if (!result.success) {
        throw new Error(result.error || 'فشل في حذف الإشعارات');
      }

      // إزالة الإشعارات المقروءة من القائمة المحلية
      const readNotificationsCount = notifications.filter(n => n.is_read).length;
      setNotifications(prev => prev.filter(notification => !notification.is_read));
      setTotalCount(prev => prev - readNotificationsCount);

    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('Error deleting read notifications:', err);
    }
  }, [notifications, user?.id]);

  /**
   * تحديث المرشحات
   */
  const updateFilters = useCallback((newFilters: NotificationFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1);
  }, []);

  /**
   * تحميل المزيد من الإشعارات
   */
  const loadMore = useCallback(async () => {
    if (hasMore && !loading) {
      await fetchNotifications(currentPage + 1, true);
    }
  }, [hasMore, loading, currentPage, fetchNotifications]);

  /**
   * تحديث الإشعارات يدوياً
   */
  const refresh = useCallback(async () => {
    setCurrentPage(1);
    await fetchNotifications(1, false);
  }, [fetchNotifications]);

  // جلب الإشعارات عند تحميل المكون أو تغيير المرشحات
  useEffect(() => {
    if (user?.id) {
      fetchNotifications(1, false);
    }
  }, [fetchNotifications]);

  // التحديث التلقائي
  useEffect(() => {
    if (!autoRefresh || !user?.id) return;

    const interval = setInterval(() => {
      fetchNotifications(1, false);
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchNotifications, user?.id]);

  // تصفية الإشعارات حسب الحالة
  const unreadNotifications = notifications.filter(notification => !notification.is_read);
  const readNotifications = notifications.filter(notification => notification.is_read);

  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    notifications,
    unreadNotifications,
    readNotifications,
    loading,
    error,
    totalCount,
    unreadCount,
    currentPage,
    totalPages,
    refresh,
    updateFilters,
    loadMore,
    markAsRead,
    markAsUnread,
    markAllAsRead,
    deleteNotification,
    deleteAllRead,
    filters,
    hasMore
  };
}

export default useUserNotifications;
