'use client';

import { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Clock,
  Star,
  Shield,
  ChevronDown,
  Loader2,
  AlertCircle,
  RefreshCw,
  Eye,
  Grid,
  List,

  X,
  MessageCircle,
  Play,
  Share2,
  Flag,
  Heart,
  HeartOff,
  ExternalLink
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useOffersTranslation } from '@/hooks/useOffersTranslation';
import { useApiList } from '@/hooks/useEnhancedApi';
import { useAuth } from '@/contexts/AuthContext';
import { notificationService } from '@/services/notificationService';
import { SUPPORTED_STABLECOINS } from '@/constants';
import { apiGet, handleApiError } from '@/utils/apiClient';

interface Offer {
  id: string;
  offer_type: 'buy' | 'sell';
  currency: string;
  stablecoin: string;
  amount: number;
  price: number;
  total_value: number;
  payment_methods: string[];
  time_limit: number;
  terms: string;
  is_active: boolean;
  is_premium: boolean;
  views_count: number;
  user: {
    id: string;
    username: string;
    full_name: string;
    rating: number;
    total_trades: number;
    completed_trades: number;
    is_verified: boolean;
    is_online: boolean;
    last_seen: string;
    completion_rate: number;
    country_code?: string;
    profile_image?: string;
  };
  created_at: string;
  updated_at: string;
  location?: string;
  auto_reply: boolean;
  margin_percentage?: number;
  min_amount?: number;
  max_amount?: number;
}

interface FilterOptions {
  offer_type: 'all' | 'buy' | 'sell';
  currency: string;
  stablecoin: string;
  payment_method: string;
  min_amount: string;
  max_amount: string;
  min_price: string;
  max_price: string;
  location: string;
  verified_only: boolean;
  premium_only: boolean;
  sort_by: 'price' | 'amount' | 'rating' | 'created_at' | 'views_count' | 'total_trades';
  sort_order: 'asc' | 'desc';
}

interface OfferStats {
  total_offers: number;
  buy_offers: number;
  sell_offers: number;
  avg_price: number;
  price_range: {
    min: number;
    max: number;
  };
}

export default function OffersPage() {
  const { t: globalT } = useTranslation();
  const { t, isLoaded: translationsLoaded } = useOffersTranslation();
  const { isAuthenticated } = useAuth();

  // دالة مساعدة للتحقق من المصادقة
  const requireAuth = (callback: () => void) => {
    if (isAuthenticated) {
      callback();
    } else {
      // توجيه المستخدم لصفحة تسجيل الدخول
      window.location.href = '/login';
    }
  };
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [savedOffers, setSavedOffers] = useState<Set<string>>(new Set());
  const [stats, setStats] = useState<OfferStats | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [allOffers, setAllOffers] = useState<Offer[]>([]);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [filters, setFilters] = useState<FilterOptions>({
    offer_type: 'all',
    currency: 'SAR',
    stablecoin: 'USDT',
    payment_method: '',
    min_amount: '',
    max_amount: '',
    min_price: '',
    max_price: '',
    location: '',
    verified_only: false,
    premium_only: false,
    sort_by: 'created_at',
    sort_order: 'desc'
  });



  // استخدام hook لجلب العروض (يجب أن يكون قبل أي conditional return)
  const {
    data: offers,
    loading,
    error,
    refresh,
    pagination,
    hasNextPage,
    nextPage
  } = useApiList<Offer>(
    async (page: number, limit: number) => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        search: searchQuery,
        type: filters.offer_type !== 'all' ? filters.offer_type : '',
        currency: filters.currency !== 'all' ? filters.currency : '',
        stablecoin: filters.stablecoin !== 'all' ? filters.stablecoin : '',
        payment_method: filters.payment_method || '',
        min_amount: filters.min_amount || '',
        max_amount: filters.max_amount || '',
        min_price: filters.min_price || '',
        max_price: filters.max_price || '',
        verified_only: filters.verified_only ? '1' : '',
        premium_only: filters.premium_only ? '1' : '',
        sort: filters.sort_by || 'created_at',
        order: filters.sort_order || 'desc'
      });

      // إزالة المعاملات الفارغة
      Object.keys(Object.fromEntries(params)).forEach(key => {
        if (!params.get(key)) {
          params.delete(key);
        }
      });

      const result = await apiGet(`offers/index.php?${params}`);

      if (!result.success) {
        // عرض رسالة خطأ مناسبة للمستخدم بدلاً من الرسالة التقنية
        const userFriendlyError = result.error?.includes('database') || result.error?.includes('connection') || result.error?.includes('SQLSTATE')
          ? t('messages.connectionError')
          : result.error || t('messages.loadError');
        throw new Error(userFriendlyError);
      }

      // تحديث الإحصائيات إذا كانت متوفرة
      if (result.stats) {
        setStats(result.stats);
      }

      return {
        success: true,
        data: result.data || [],
        pagination: result.pagination
      };
    },
    {
      itemsPerPage: 20,
      immediate: true,
      showNotifications: false
    }
  );

  // تجميع البيانات عند تحديث offers
  useEffect(() => {
    if (offers) {
      if (pagination?.currentPage === 1) {
        // الصفحة الأولى - استبدال البيانات
        setAllOffers(offers);
      } else {
        // الصفحات التالية - إضافة البيانات
        setAllOffers(prev => [...prev, ...offers]);
      }
    }
  }, [offers, pagination?.currentPage]);

  // مراقبة تغييرات الفلاتر والبحث للتحديث التلقائي
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setAllOffers([]); // مسح البيانات المجمعة عند تغيير الفلاتر
      refresh(); // تحديث البيانات عند تغيير الفلاتر
      fetchStats(); // تحديث الإحصائيات أيضاً
    }, 500); // تأخير 500ms لتجنب الطلبات المتكررة

    return () => clearTimeout(timeoutId);
  }, [filters, searchQuery]); // إزالة refresh من dependencies لتجنب infinite loop

  // دالة جلب الإحصائيات
  const fetchStats = async () => {
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost/ikaros-p2p/api';
      const params = new URLSearchParams({
        type: filters.offer_type !== 'all' ? filters.offer_type : '',
        currency: filters.currency !== 'all' ? filters.currency : '',
        stablecoin: filters.stablecoin !== 'all' ? filters.stablecoin : '',
        payment_method: filters.payment_method || '',
        verified_only: filters.verified_only ? '1' : '',
        premium_only: filters.premium_only ? '1' : ''
      });

      // إزالة المعاملات الفارغة
      Object.keys(Object.fromEntries(params)).forEach(key => {
        if (!params.get(key)) {
          params.delete(key);
        }
      });

      const response = await fetch(`${apiUrl}/offers/stats.php?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setStats(result.data);
      } else {
        console.warn('Stats API returned error:', result.error);
        // تعيين إحصائيات افتراضية في حالة الخطأ
        setStats({
          total_offers: 0,
          buy_offers: 0,
          sell_offers: 0,
          avg_price: 0,
          price_range: { min: 0, max: 0 }
        });
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
      // تعيين إحصائيات افتراضية في حالة الخطأ
      setStats({
        total_offers: 0,
        buy_offers: 0,
        sell_offers: 0,
        avg_price: 0,
        price_range: { min: 0, max: 0 }
      });
    }
  };

  // تحميل العروض المحفوظة من localStorage
  useEffect(() => {
    const saved = localStorage.getItem('savedOffers');
    if (saved) {
      setSavedOffers(new Set(JSON.parse(saved)));
    }

    // جلب الإحصائيات عند التحميل الأول
    fetchStats();
  }, []);

  // حفظ العروض المحفوظة في localStorage
  useEffect(() => {
    localStorage.setItem('savedOffers', JSON.stringify([...savedOffers]));
  }, [savedOffers]);

  const handleFilterChange = (key: keyof FilterOptions, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };



  const resetFilters = () => {
    setFilters({
      offer_type: 'all',
      currency: 'SAR',
      stablecoin: 'USDT',
      payment_method: '',
      min_amount: '',
      max_amount: '',
      min_price: '',
      max_price: '',
      location: '',
      verified_only: false,
      premium_only: false,
      sort_by: 'created_at',
      sort_order: 'desc'
    });
    setSearchQuery('');
  };

  // حساب عدد الفلاتر النشطة
  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.offer_type !== 'all') count++;
    if (filters.currency !== 'SAR') count++;
    if (filters.stablecoin !== 'USDT') count++;
    if (filters.payment_method) count++;
    if (filters.min_amount) count++;
    if (filters.max_amount) count++;
    if (filters.min_price) count++;
    if (filters.max_price) count++;
    if (filters.location) count++;
    if (filters.verified_only) count++;
    if (filters.premium_only) count++;
    if (searchQuery) count++;
    return count;
  };

  // دوال التعامل مع إجراءات العروض
  const handleStartTrade = (offer: Offer) => {
    requireAuth(() => {
      // التوجه لصفحة بدء التداول
      window.location.href = `/trade/start/${offer.id}`;
    });
  };

  const handleContactUser = (offer: Offer) => {
    requireAuth(() => {
      // فتح نافذة الدردشة
      window.location.href = `/chat/${offer.user.id}`;
    });
  };

  const handleViewOffer = (offer: Offer) => {
    // عرض تفاصيل العرض
    window.location.href = `/offers/${offer.id}`;
  };

  const handleSaveOffer = (offerId: string) => {
    const newSavedOffers = new Set(savedOffers);
    if (savedOffers.has(offerId)) {
      newSavedOffers.delete(offerId);
      notificationService.success(t('messages.offerUnsaved'));
    } else {
      newSavedOffers.add(offerId);
      notificationService.success(t('messages.offerSaved'));
    }
    setSavedOffers(newSavedOffers);
  };

  const handleShareOffer = (offer: Offer) => {
    const url = `${window.location.origin}/offers/${offer.id}`;
    if (navigator.share) {
      navigator.share({
        title: `${offer.offer_type === 'buy' ? t('buy') : t('sell')} ${offer.stablecoin}`,
        text: `${t('price')}: ${offer.price} ${offer.currency}`,
        url: url
      });
    } else {
      navigator.clipboard.writeText(url);
      notificationService.success('تم نسخ الرابط');
    }
  };

  const handleReportOffer = (offer: Offer) => {
    // التوجه لصفحة الإبلاغ
    window.location.href = `/report/offer/${offer.id}`;
  };

  // عرض loading إذا لم تحمل الترجمات بعد
  if (!translationsLoaded) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600 dark:text-gray-400">{globalT('common.loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="container-custom">
        {/* زر إظهار/إخفاء الفلاتر للموبايل */}
        <div className="lg:hidden mb-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="w-full flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white"
          >
            <span className="flex items-center">
              <Filter className="w-5 h-5 mr-2" />
              {t('filters')}
              {getActiveFiltersCount() > 0 && (
                <span className="mr-2 px-2 py-1 bg-blue-600 text-white text-xs rounded-full">
                  {getActiveFiltersCount()}
                </span>
              )}
            </span>
            <ChevronDown className={`w-5 h-5 transition-transform duration-200 ${showFilters ? 'rotate-180' : ''}`} />
          </button>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* الشريط الجانبي للفلاتر */}
          <div className={`w-full lg:w-80 ${showFilters ? 'block' : 'hidden lg:block'}`}>
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 lg:p-6 lg:sticky lg:top-8">
              <div className="hidden lg:flex items-center justify-between mb-4">
                <h2 className="flex text-lg font-semibold text-gray-900 dark:text-white items-center">
                  <Filter className="w-5 h-5 mr-2" />
                  {t('filters')}
                  {getActiveFiltersCount() > 0 && (
                    <span className="mr-2 px-2 py-1 bg-blue-600 text-white text-xs rounded-full">
                      {getActiveFiltersCount()}
                    </span>
                  )}
                </h2>

                {getActiveFiltersCount() > 0 && (
                  <button
                    onClick={() => {
                      setFilters({
                        offer_type: 'all',
                        currency: 'SAR',
                        stablecoin: 'USDT',
                        payment_method: '',
                        min_amount: '',
                        max_amount: '',
                        min_price: '',
                        max_price: '',
                        location: '',
                        verified_only: false,
                        premium_only: false,
                        sort_by: 'created_at',
                        sort_order: 'desc'
                      });
                      setSearchQuery('');
                    }}
                    className="text-xs text-gray-500 dark:text-gray-400 hover:text-red-500 transition-colors"
                  >
                    {t('clearAll')}
                  </button>
                )}
              </div>

              {/* إحصائيات سريعة */}
              <div className="relative mb-4 md:mb-6">
                {loading && (
                  <div className="absolute top-2 right-2 z-10">
                    <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
                  </div>
                )}

                {stats ? (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 md:gap-3 p-3 md:p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg transition-all duration-300">
                    <div className="text-center group hover:bg-white dark:hover:bg-gray-600 rounded-lg p-2 transition-colors cursor-pointer"
                         onClick={() => setFilters(prev => ({ ...prev, offer_type: 'all' }))}>
                      <div className="text-base lg:text-lg font-bold text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform">
                        {stats.total_offers}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{t('totalOffers')}</div>
                    </div>

                    <div className="text-center group hover:bg-white dark:hover:bg-gray-600 rounded-lg p-2 transition-colors cursor-pointer"
                         onClick={() => setFilters(prev => ({ ...prev, offer_type: 'buy' }))}>
                      <div className="text-base lg:text-lg font-bold text-green-600 dark:text-green-400 group-hover:scale-110 transition-transform">
                        {stats.buy_offers}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{t('buyOffers')}</div>
                    </div>

                    <div className="text-center group hover:bg-white dark:hover:bg-gray-600 rounded-lg p-2 transition-colors cursor-pointer"
                         onClick={() => setFilters(prev => ({ ...prev, offer_type: 'sell' }))}>
                      <div className="text-base lg:text-lg font-bold text-red-600 dark:text-red-400 group-hover:scale-110 transition-transform">
                        {stats.sell_offers}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{t('sellOffers')}</div>
                    </div>

                    <div className="text-center group hover:bg-white dark:hover:bg-gray-600 rounded-lg p-2 transition-colors">
                      <div className="text-base lg:text-lg font-bold text-purple-600 dark:text-purple-400 group-hover:scale-110 transition-transform">
                        {stats.avg_price ? stats.avg_price.toFixed(2) : '0.00'}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{t('avgPrice')}</div>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 md:gap-3 p-3 md:p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    {[...Array(4)].map((_, index) => (
                      <div key={index} className="text-center animate-pulse">
                        <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded mb-1"></div>
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* البحث */}
              <div className="mb-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder={t('searchPlaceholder')}
                    className="w-full pl-10 pr-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                  />
                </div>
              </div>

              {/* نوع العرض */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('offerType')}
                </label>
                <select
                  value={filters.offer_type}
                  onChange={(e) => handleFilterChange('offer_type', e.target.value)}
                  className="w-full px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                >
                  <option value="all">{t('allTypes')}</option>
                  <option value="buy">{t('buy')}</option>
                  <option value="sell">{t('sell')}</option>
                </select>
              </div>

              {/* العملة والعملة المستقرة */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('currency')}
                  </label>
                  <select
                    value={filters.currency}
                    onChange={(e) => handleFilterChange('currency', e.target.value)}
                    className="w-full px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                  >
                    <option value="all">{t('allCurrencies')}</option>
                    <option value="SAR">SAR</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="AED">AED</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('stablecoin')}
                  </label>
                  <select
                    value={filters.stablecoin}
                    onChange={(e) => handleFilterChange('stablecoin', e.target.value)}
                    className="w-full px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                  >
                    <option value="all">{t('allStablecoins')}</option>
                    {SUPPORTED_STABLECOINS.map((stablecoin) => (
                      <option key={stablecoin.symbol} value={stablecoin.symbol}>
                        {stablecoin.symbol} - {stablecoin.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* نطاق المبلغ */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('amountRange')}
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    value={filters.min_amount}
                    onChange={(e) => handleFilterChange('min_amount', e.target.value)}
                    placeholder={t('minAmount')}
                    className="px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                  />
                  <input
                    type="number"
                    value={filters.max_amount}
                    onChange={(e) => handleFilterChange('max_amount', e.target.value)}
                    placeholder={t('maxAmount')}
                    className="px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                  />
                </div>
              </div>

              {/* نطاق السعر */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('priceRange')}
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    value={filters.min_price}
                    onChange={(e) => handleFilterChange('min_price', e.target.value)}
                    placeholder={t('minPrice')}
                    className="px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                  />
                  <input
                    type="number"
                    value={filters.max_price}
                    onChange={(e) => handleFilterChange('max_price', e.target.value)}
                    placeholder={t('maxPrice')}
                    className="px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                  />
                </div>
              </div>

              {/* خيارات متقدمة */}
              <div className="mb-4 space-y-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <label className="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.verified_only}
                    onChange={(e) => handleFilterChange('verified_only', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="mr-2 text-sm text-gray-700 dark:text-gray-300 flex items-center">
                    <Shield className="w-4 h-4 mr-1 text-green-500" />
                    {t('verifiedOnly')}
                  </span>
                </label>
                <label className="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.premium_only}
                    onChange={(e) => handleFilterChange('premium_only', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="mr-2 text-sm text-gray-700 dark:text-gray-300 flex items-center">
                    <Star className="w-4 h-4 mr-1 text-yellow-500" />
                    {t('premiumOnly')}
                  </span>
                </label>
              </div>

              {/* ترتيب النتائج */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('sortBy')}
                </label>
                <select
                  value={`${filters.sort_by}_${filters.sort_order}`}
                  onChange={(e) => {
                    const [sortBy, sortOrder] = e.target.value.split('_');
                    handleFilterChange('sort_by', sortBy);
                    handleFilterChange('sort_order', sortOrder);
                  }}
                  className="w-full px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                >
                  <option value="created_at_desc">{t('sortNewest')}</option>
                  <option value="created_at_asc">{t('sortOldest')}</option>
                  <option value="price_asc">{t('sortPriceAsc')}</option>
                  <option value="price_desc">{t('sortPriceDesc')}</option>
                  <option value="amount_asc">{t('sortAmountAsc')}</option>
                  <option value="amount_desc">{t('sortAmountDesc')}</option>
                  <option value="rating_desc">{t('sortRatingDesc')}</option>
                  <option value="total_trades_desc">{t('sortTradesDesc')}</option>
                  <option value="views_count_desc">{t('sortPopular')}</option>
                </select>
              </div>

              {/* أزرار الإجراءات */}
              <div className="space-y-3">
                <button
                  onClick={resetFilters}
                  className="w-full px-4 py-2.5 text-sm text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 flex items-center justify-center"
                >
                  <X className="w-4 h-4 mr-2" />
                  {t('resetFilters')}
                </button>

                {/* زر إغلاق الفلاتر للموبايل */}
                <button
                  onClick={() => setShowFilters(false)}
                  className="lg:hidden w-full px-4 py-2.5 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 flex items-center justify-center"
                >
                  {t('applyFilters')}
                </button>
              </div>
            </div>
          </div>

          {/* المحتوى الرئيسي */}
          <div className="flex-1">


            {/* رأس الصفحة */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  {t('title')}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {pagination?.totalItems ? t('totalFound', { count: pagination.totalItems }) : t('loading')}
                </p>
              </div>

              <div className="flex items-center space-x-3 space-x-reverse mt-4 sm:mt-0">
                {/* مؤشر التحديث التلقائي */}
                {loading && (
                  <div className="flex items-center text-xs text-blue-600 dark:text-blue-400">
                    <Loader2 className="w-3 h-3 animate-spin mr-1" />
                    <span>{t('updating')}</span>
                  </div>
                )}

                {/* أزرار عرض */}
                <div className="hidden sm:flex rounded-lg border border-gray-300 dark:border-gray-600 overflow-hidden">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 transition-colors ${
                      viewMode === 'grid'
                        ? 'bg-blue-500 text-white'
                        : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                    }`}
                    title={globalT('common.gridView')}
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 transition-colors ${
                      viewMode === 'list'
                        ? 'bg-blue-500 text-white'
                        : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                    }`}
                    title={globalT('common.listView')}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>

                {/* زر التحديث */}
                <button
                  onClick={refresh}
                  disabled={loading}
                  className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                </button>
              </div>
            </div>

            {/* قائمة العروض */}
            {loading ? (
              <div className="flex justify-center py-12">
                <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                <p className="text-red-600 dark:text-red-400">{error}</p>
                <button
                  onClick={refresh}
                  className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {t('common.retry')}
                </button>
              </div>
            ) : !allOffers || !Array.isArray(allOffers) || allOffers.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <p className="text-gray-600 dark:text-gray-400 text-lg">{t('noOffers')}</p>
                <p className="text-gray-500 dark:text-gray-500 text-sm mt-2">
                  {getActiveFiltersCount() > 0
                    ? t('noOffersWithFilters')
                    : t('noOffersAvailable')
                  }
                </p>

                {getActiveFiltersCount() > 0 && (
                  <button
                    onClick={() => {
                      setFilters({
                        offer_type: 'all',
                        currency: 'SAR',
                        stablecoin: 'USDT',
                        payment_method: '',
                        min_amount: '',
                        max_amount: '',
                        min_price: '',
                        max_price: '',
                        location: '',
                        verified_only: false,
                        premium_only: false,
                        sort_by: 'created_at',
                        sort_order: 'desc'
                      });
                      setSearchQuery('');
                    }}
                    className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    {t('clearAll')}
                  </button>
                )}
              </div>
            ) : (
              <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6' : 'space-y-4'}>
                {allOffers.map((offer) => (
                  <div
                    key={offer.id}
                    className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200 ${
                      viewMode === 'list'
                        ? 'p-4 md:p-6 flex flex-col md:flex-row md:items-center md:space-x-6 md:space-x-reverse'
                        : 'p-4 md:p-6'
                    }`}
                  >
                    {/* محتوى البطاقة */}
                    <div className={viewMode === 'list' ? 'flex-1 md:mr-6' : ''}>
                      <div className="flex justify-between items-start mb-4">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className={`w-3 h-3 rounded-full ${offer.offer_type === 'buy' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                        <span className="font-medium text-gray-900 dark:text-white">
                          {offer.offer_type === 'buy' ? t('buy') : t('sell')} {offer.stablecoin}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {offer.user.username}
                      </span>
                    </div>

                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">{t('amount')}:</span>
                        <span className="font-medium text-gray-900 dark:text-white">{offer.amount} {offer.stablecoin}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">{t('price')}:</span>
                        <span className="font-medium text-gray-900 dark:text-white">{offer.price} {offer.currency}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">{t('total')}:</span>
                        <span className="font-bold text-lg text-gray-900 dark:text-white">{offer.total_value} {offer.currency}</span>
                      </div>
                    </div>

                    {/* معلومات المستخدم */}
                    <div className="flex items-center space-x-3 space-x-reverse mb-4">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-yellow-500 fill-current" />
                          <span className="text-sm text-gray-600 dark:text-gray-400 ml-1">{offer.user.rating}</span>
                        </div>
                        {offer.user.is_verified && (
                          <div title={t('verified')}>
                            <Shield className="w-4 h-4 text-green-500" />
                          </div>
                        )}
                        {offer.user.is_online && (
                          <div className="w-2 h-2 bg-green-500 rounded-full" title={t('online')} />
                        )}
                      </div>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {offer.user.completion_rate}% {t('completionRate')}
                      </span>
                    </div>

                    {/* طرق الدفع */}
                    {offer.payment_methods && offer.payment_methods.length > 0 && (
                      <div className="mb-4">
                        <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">{t('paymentMethods')}</div>
                        <div className="flex flex-wrap gap-1">
                          {offer.payment_methods.slice(0, 3).map((method, index) => (
                            <span key={index} className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded">
                              {method}
                            </span>
                          ))}
                          {offer.payment_methods.length > 3 && (
                            <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded">
                              +{offer.payment_methods.length - 3}
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* معلومات إضافية */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-2 space-x-reverse text-xs text-gray-500 dark:text-gray-400">
                        <Eye className="w-3 h-3" />
                        <span>{offer.views_count}</span>
                        <Clock className="w-3 h-3 ml-2" />
                        <span>{Math.round(offer.time_limit / 60)} {t('minutes')}</span>
                      </div>
                      <button
                        onClick={() => handleSaveOffer(offer.id)}
                        className="p-2 text-gray-400 hover:text-red-500 transition-colors"
                        title={savedOffers.has(offer.id) ? t('messages.offerUnsaved') : t('messages.offerSaved')}
                      >
                        {savedOffers.has(offer.id) ? (
                          <Heart className="w-4 h-4 fill-current text-red-500" />
                        ) : (
                          <HeartOff className="w-4 h-4" />
                        )}
                      </button>
                    </div>

                    {/* أزرار الإجراءات */}
                    <div className="space-y-2">
                      {/* الزر الرئيسي - بدء التداول */}
                      <button
                        onClick={() => handleStartTrade(offer)}
                        className="w-full flex items-center justify-center space-x-2 space-x-reverse px-4 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 font-medium shadow-sm"
                        title={!isAuthenticated ? 'يتطلب تسجيل الدخول' : ''}
                      >
                        <Play className="w-4 h-4" />
                        <span>{t('actions.startTrade')}</span>
                        {!isAuthenticated && (
                          <span className="text-xs opacity-75">*</span>
                        )}
                      </button>

                      {/* أزرار ثانوية */}
                      <div className="grid grid-cols-3 gap-2">
                        <button
                          onClick={() => handleContactUser(offer)}
                          className="flex items-center justify-center space-x-1 space-x-reverse px-3 py-2 text-sm text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                          title={!isAuthenticated ? 'يتطلب تسجيل الدخول' : (offer.offer_type === 'sell' ? t('actions.contactSeller') : t('actions.contactBuyer'))}
                        >
                          <MessageCircle className="w-3 h-3" />
                          <span className="hidden sm:inline">{t('actions.contactSeller').split(' ')[0]}</span>
                          {!isAuthenticated && (
                            <span className="text-xs opacity-75">*</span>
                          )}
                        </button>

                        <button
                          onClick={() => handleViewOffer(offer)}
                          className="flex items-center justify-center space-x-1 space-x-reverse px-3 py-2 text-sm text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                          title={t('actions.viewOffer')}
                        >
                          <ExternalLink className="w-3 h-3" />
                          <span className="hidden sm:inline">{t('actions.viewOffer').split(' ')[0]}</span>
                        </button>

                        <button
                          onClick={() => handleShareOffer(offer)}
                          className="flex items-center justify-center space-x-1 space-x-reverse px-3 py-2 text-sm text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                          title={t('actions.shareOffer')}
                        >
                          <Share2 className="w-3 h-3" />
                          <span className="hidden sm:inline">{t('actions.shareOffer').split(' ')[0]}</span>
                        </button>
                      </div>

                      {/* زر الإبلاغ */}
                      <button
                        onClick={() => handleReportOffer(offer)}
                        className="w-full flex items-center justify-center space-x-1 space-x-reverse px-3 py-1 text-xs text-gray-500 dark:text-gray-400 hover:text-red-500 transition-colors"
                      >
                        <Flag className="w-3 h-3" />
                        <span>{t('actions.reportOffer')}</span>
                      </button>
                    </div>
                    </div>

                    {/* أزرار الإجراءات للعرض القائمة */}
                    {viewMode === 'list' && (
                      <div className="flex flex-col space-y-2 md:w-48 md:flex-shrink-0">
                        <button
                          onClick={() => handleStartTrade(offer)}
                          className="w-full flex items-center justify-center space-x-2 space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                        >
                          <Play className="w-4 h-4" />
                          <span>{t('actions.startTrade')}</span>
                        </button>
                        <div className="grid grid-cols-2 gap-1">
                          <button
                            onClick={() => handleContactUser(offer)}
                            className="flex items-center justify-center px-2 py-1 text-xs text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                          >
                            <MessageCircle className="w-3 h-3" />
                          </button>
                          <button
                            onClick={() => handleViewOffer(offer)}
                            className="flex items-center justify-center px-2 py-1 text-xs text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                          >
                            <ExternalLink className="w-3 h-3" />
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* زر تحميل المزيد */}
            {hasNextPage && allOffers && allOffers.length > 0 && (
              <div className="text-center mt-8">
                <button
                  onClick={() => {
                    setIsLoadingMore(true);
                    nextPage();
                    // سيتم إيقاف التحميل عند وصول البيانات الجديدة
                    setTimeout(() => setIsLoadingMore(false), 1000);
                  }}
                  disabled={loading || isLoadingMore}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium inline-flex items-center space-x-2 space-x-reverse"
                >
                  {loading || isLoadingMore ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>{globalT('common.loading')}</span>
                    </>
                  ) : (
                    <>
                      <span>{t('loadMore')}</span>
                      {pagination?.totalItems && (
                        <span className="text-blue-200 text-sm">
                          ({pagination.totalItems - allOffers.length} {t('remaining')})
                        </span>
                      )}
                    </>
                  )}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}