<?php
/**
 * API endpoint لتسجيل الخروج
 * Logout API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";

// التحقق من طريقة الطلب
validateRequestMethod(['POST']);

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/../../.env')) {
    $lines = file(__DIR__ . '/../../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

// مفتاح JWT من متغيرات البيئة
$jwtSecret = $_ENV['JWT_SECRET'] ?? 'default-secret-key';

/**
 * التحقق من صحة JWT token
 */
function verifyJWT($token, $secret) {
    $parts = explode('.', $token);
    if (count($parts) !== 3) {
        return false;
    }

    list($header, $payload, $signature) = $parts;

    $validSignature = hash_hmac('sha256', $header . "." . $payload, $secret, true);
    $validSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($validSignature));

    if (!hash_equals($signature, $validSignature)) {
        return false;
    }

    $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $payload)), true);

    return $payload;
}

try {
    // الحصول على البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        sendErrorResponse('بيانات غير صحيحة');
    }

    $userId = $input['userId'] ?? null;
    $sessionToken = $input['token'] ?? null;
    $accessToken = $input['accessToken'] ?? null;
    $refreshToken = $input['refreshToken'] ?? null;

    // الحصول على رمز المصادقة من الهيدر
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $accessToken = $matches[1];
    }

    // التحقق من صحة الرمز المميز إذا تم توفيره
    $tokenUserId = null;
    if ($accessToken) {
        $tokenPayload = verifyJWT($accessToken, $jwtSecret);
        if ($tokenPayload && isset($tokenPayload['user_id'])) {
            $tokenUserId = $tokenPayload['user_id'];
        }
    }

    // استخدام معرف المستخدم من الرمز المميز إذا لم يتم توفيره
    if (!$userId && $tokenUserId) {
        $userId = $tokenUserId;
    }

    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // Log the logout activity if user ID is provided (تم تعطيله مؤقتاً)
    if ($userId) {
        try {
            // التحقق من وجود جدول activity_logs أولاً
            $checkTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
            $checkTable->execute();

            if ($checkTable->rowCount() > 0) {
                // التحقق من أعمدة الجدول
                $checkColumns = $connection->prepare("SHOW COLUMNS FROM activity_logs LIKE 'entity_type'");
                $checkColumns->execute();

                if ($checkColumns->rowCount() > 0) {
                    $logStmt = $connection->prepare("
                        INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data)
                        VALUES (?, 'user_logout', 'user', ?, ?, ?, ?)
                    ");

                    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                    $logoutData = json_encode([
                        'logout_time' => date('Y-m-d H:i:s'),
                        'session_token' => $sessionToken ? substr($sessionToken, 0, 8) . '...' : null
                    ]);

                    $logStmt->execute([$userId, $userId, $ipAddress, $userAgent, $logoutData]);
                }
            }
        } catch (Exception $logError) {
            // تجاهل أخطاء تسجيل النشاط
            error_log('Activity log error: ' . $logError->getMessage());
        }
    }
    
    // في التطبيق الحقيقي، يجب:
    // 1. إبطال رمز الجلسة في قاعدة البيانات
    // 2. مسح بيانات الجلسة من الخادم
    // 3. إضافة الرمز إلى القائمة السوداء

    // إبطال الجلسة إذا كان لدينا معرف المستخدم
    if ($userId && $sessionToken) {
        try {
            require_once __DIR__ . '/../middleware/auth.php';
            $authMiddleware = new AuthMiddleware();
            $authMiddleware->invalidateSession($sessionToken);
        } catch (Exception $sessionError) {
            // تجاهل أخطاء إبطال الجلسة
            error_log('Session invalidation error: ' . $sessionError->getMessage());
        }
    }

    // في المستقبل، يمكن إضافة JWT tokens إلى قائمة سوداء
    // TODO: Implement JWT blacklist functionality

    sendSuccessResponse([
        'message' => 'تم تسجيل الخروج بنجاح',
        'timestamp' => date('Y-m-d H:i:s')
    ], 'تم تسجيل الخروج بنجاح');

} catch (Exception $e) {
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    error_log('Database error in logout.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}
?>
