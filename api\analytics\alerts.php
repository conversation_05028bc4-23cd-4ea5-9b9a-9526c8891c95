<?php
/**
 * API endpoint لإدارة التنبيهات والتحذيرات
 * Alerts and Warnings Management API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    // Get database connection
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';
    
    switch ($method) {
        case 'GET':
            if ($action === 'list') {
                echo json_encode(getAlerts($connection));
            } elseif ($action === 'stats') {
                echo json_encode(getAlertsStats($connection));
            } elseif ($action === 'active') {
                echo json_encode(getActiveAlerts($connection));
            } else {
                echo json_encode(getAllAlertsData($connection));
            }
            break;
            
        case 'POST':
            if ($action === 'create') {
                echo json_encode(createAlert($connection));
            } elseif ($action === 'resolve') {
                echo json_encode(resolveAlert($connection));
            } elseif ($action === 'archive') {
                echo json_encode(archiveAlert($connection));
            } else {
                throw new Exception('Invalid action for POST request');
            }
            break;
            
        case 'PUT':
            echo json_encode(updateAlert($connection));
            break;
            
        case 'DELETE':
            echo json_encode(deleteAlert($connection));
            break;
            
        default:
            throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// === دوال مساعدة ===

/**
 * الحصول على جميع بيانات التنبيهات
 */
function getAllAlertsData($connection) {
    return [
        'success' => true,
        'data' => [
            'alerts' => getAlerts($connection)['data'],
            'stats' => getAlertsStats($connection)['data'],
            'active' => getActiveAlerts($connection)['data']
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ];
}

/**
 * الحصول على قائمة التنبيهات
 */
function getAlerts($connection) {
    $filters = [
        'type' => $_GET['type'] ?? 'all',
        'priority' => $_GET['priority'] ?? 'all',
        'status' => $_GET['status'] ?? 'all',
        'timeRange' => $_GET['timeRange'] ?? 'all',
        'limit' => (int)($_GET['limit'] ?? 50),
        'offset' => (int)($_GET['offset'] ?? 0)
    ];
    
    // إنشاء جدول التنبيهات إذا لم يكن موجود
    createAlertsTableIfNotExists($connection);
    
    // بناء الاستعلام
    $whereConditions = [];
    $params = [];
    
    if ($filters['type'] !== 'all') {
        $whereConditions[] = "type = :type";
        $params[':type'] = $filters['type'];
    }
    
    if ($filters['priority'] !== 'all') {
        $whereConditions[] = "priority = :priority";
        $params[':priority'] = $filters['priority'];
    }
    
    if ($filters['status'] !== 'all') {
        if ($filters['status'] === 'active') {
            $whereConditions[] = "is_resolved = 0 AND is_archived = 0";
        } elseif ($filters['status'] === 'resolved') {
            $whereConditions[] = "is_resolved = 1";
        } elseif ($filters['status'] === 'archived') {
            $whereConditions[] = "is_archived = 1";
        }
    }
    
    // فلتر الوقت
    if ($filters['timeRange'] !== 'all') {
        $timeRanges = [
            '1h' => 'DATE_SUB(NOW(), INTERVAL 1 HOUR)',
            '24h' => 'DATE_SUB(NOW(), INTERVAL 24 HOUR)',
            '7d' => 'DATE_SUB(NOW(), INTERVAL 7 DAY)',
            '30d' => 'DATE_SUB(NOW(), INTERVAL 30 DAY)'
        ];
        
        if (isset($timeRanges[$filters['timeRange']])) {
            $whereConditions[] = "created_at >= " . $timeRanges[$filters['timeRange']];
        }
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // الاستعلام الرئيسي
    $sql = "
        SELECT 
            id, type, priority, title, message, source, 
            affected_users, estimated_impact, tags,
            is_resolved, is_archived, is_read,
            created_at, updated_at, resolved_at
        FROM alerts 
        $whereClause
        ORDER BY 
            CASE priority 
                WHEN 'urgent' THEN 4 
                WHEN 'high' THEN 3 
                WHEN 'medium' THEN 2 
                ELSE 1 
            END DESC,
            created_at DESC
        LIMIT :limit OFFSET :offset
    ";
    
    $stmt = $connection->prepare($sql);
    
    // ربط المعاملات
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':limit', $filters['limit'], PDO::PARAM_INT);
    $stmt->bindValue(':offset', $filters['offset'], PDO::PARAM_INT);
    
    $stmt->execute();
    $alerts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تحويل البيانات
    foreach ($alerts as &$alert) {
        $alert['tags'] = $alert['tags'] ? json_decode($alert['tags'], true) : [];
        $alert['is_resolved'] = (bool)$alert['is_resolved'];
        $alert['is_archived'] = (bool)$alert['is_archived'];
        $alert['is_read'] = (bool)$alert['is_read'];
        $alert['affected_users'] = (int)$alert['affected_users'];
    }
    
    // عدد إجمالي التنبيهات
    $countSql = "SELECT COUNT(*) as total FROM alerts $whereClause";
    $countStmt = $connection->prepare($countSql);
    foreach ($params as $key => $value) {
        $countStmt->bindValue($key, $value);
    }
    $countStmt->execute();
    $total = $countStmt->fetch()['total'];
    
    return [
        'success' => true,
        'data' => [
            'alerts' => $alerts,
            'pagination' => [
                'total' => (int)$total,
                'limit' => $filters['limit'],
                'offset' => $filters['offset'],
                'hasMore' => ($filters['offset'] + $filters['limit']) < $total
            ]
        ]
    ];
}

/**
 * الحصول على إحصائيات التنبيهات
 */
function getAlertsStats($connection) {
    createAlertsTableIfNotExists($connection);
    
    $stats = [
        'total' => 0,
        'active' => 0,
        'resolved' => 0,
        'archived' => 0,
        'by_priority' => [
            'urgent' => 0,
            'high' => 0,
            'medium' => 0,
            'low' => 0
        ],
        'by_type' => [
            'security' => 0,
            'system' => 0,
            'trade' => 0,
            'contract' => 0,
            'performance' => 0
        ],
        'recent_activity' => []
    ];
    
    try {
        // إجمالي التنبيهات
        $stmt = $connection->prepare("SELECT COUNT(*) as total FROM alerts");
        $stmt->execute();
        $stats['total'] = (int)$stmt->fetch()['total'];
        
        // التنبيهات النشطة
        $stmt = $connection->prepare("SELECT COUNT(*) as active FROM alerts WHERE is_resolved = 0 AND is_archived = 0");
        $stmt->execute();
        $stats['active'] = (int)$stmt->fetch()['active'];
        
        // التنبيهات المحلولة
        $stmt = $connection->prepare("SELECT COUNT(*) as resolved FROM alerts WHERE is_resolved = 1");
        $stmt->execute();
        $stats['resolved'] = (int)$stmt->fetch()['resolved'];
        
        // التنبيهات المؤرشفة
        $stmt = $connection->prepare("SELECT COUNT(*) as archived FROM alerts WHERE is_archived = 1");
        $stmt->execute();
        $stats['archived'] = (int)$stmt->fetch()['archived'];
        
        // حسب الأولوية
        $stmt = $connection->prepare("
            SELECT priority, COUNT(*) as count 
            FROM alerts 
            WHERE is_resolved = 0 AND is_archived = 0
            GROUP BY priority
        ");
        $stmt->execute();
        $priorityStats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        foreach ($priorityStats as $priority => $count) {
            if (isset($stats['by_priority'][$priority])) {
                $stats['by_priority'][$priority] = (int)$count;
            }
        }
        
        // حسب النوع
        $stmt = $connection->prepare("
            SELECT type, COUNT(*) as count 
            FROM alerts 
            WHERE is_resolved = 0 AND is_archived = 0
            GROUP BY type
        ");
        $stmt->execute();
        $typeStats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        foreach ($typeStats as $type => $count) {
            if (isset($stats['by_type'][$type])) {
                $stats['by_type'][$type] = (int)$count;
            }
        }
        
        // النشاط الأخير
        $stmt = $connection->prepare("
            SELECT title, type, priority, created_at
            FROM alerts 
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        $stmt->execute();
        $stats['recent_activity'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log('Error in getAlertsStats: ' . $e->getMessage());
    }
    
    return [
        'success' => true,
        'data' => $stats
    ];
}

/**
 * الحصول على التنبيهات النشطة
 */
function getActiveAlerts($connection) {
    createAlertsTableIfNotExists($connection);
    
    $stmt = $connection->prepare("
        SELECT 
            id, type, priority, title, message, source,
            affected_users, estimated_impact, tags,
            created_at
        FROM alerts 
        WHERE is_resolved = 0 AND is_archived = 0
        ORDER BY 
            CASE priority 
                WHEN 'urgent' THEN 4 
                WHEN 'high' THEN 3 
                WHEN 'medium' THEN 2 
                ELSE 1 
            END DESC,
            created_at DESC
        LIMIT 20
    ");
    $stmt->execute();
    $alerts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تحويل البيانات
    foreach ($alerts as &$alert) {
        $alert['tags'] = $alert['tags'] ? json_decode($alert['tags'], true) : [];
        $alert['affected_users'] = (int)$alert['affected_users'];
    }
    
    return [
        'success' => true,
        'data' => $alerts
    ];
}

/**
 * إنشاء تنبيه جديد
 */
function createAlert($connection) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $requiredFields = ['type', 'priority', 'title', 'message'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            throw new Exception("Missing required field: $field");
        }
    }
    
    createAlertsTableIfNotExists($connection);
    
    $stmt = $connection->prepare("
        INSERT INTO alerts (
            type, priority, title, message, source,
            affected_users, estimated_impact, tags,
            created_at
        ) VALUES (
            :type, :priority, :title, :message, :source,
            :affected_users, :estimated_impact, :tags,
            NOW()
        )
    ");
    
    $stmt->execute([
        ':type' => $input['type'],
        ':priority' => $input['priority'],
        ':title' => $input['title'],
        ':message' => $input['message'],
        ':source' => $input['source'] ?? 'System',
        ':affected_users' => $input['affected_users'] ?? 0,
        ':estimated_impact' => $input['estimated_impact'] ?? 'low',
        ':tags' => json_encode($input['tags'] ?? [])
    ]);
    
    return [
        'success' => true,
        'data' => [
            'id' => $connection->lastInsertId(),
            'message' => 'Alert created successfully'
        ]
    ];
}

/**
 * حل تنبيه
 */
function resolveAlert($connection) {
    $input = json_decode(file_get_contents('php://input'), true);
    $alertId = $input['id'] ?? null;
    
    if (!$alertId) {
        throw new Exception('Alert ID is required');
    }
    
    createAlertsTableIfNotExists($connection);
    
    $stmt = $connection->prepare("
        UPDATE alerts 
        SET is_resolved = 1, resolved_at = NOW(), updated_at = NOW()
        WHERE id = :id
    ");
    $stmt->execute([':id' => $alertId]);
    
    return [
        'success' => true,
        'data' => [
            'message' => 'Alert resolved successfully'
        ]
    ];
}

/**
 * أرشفة تنبيه
 */
function archiveAlert($connection) {
    $input = json_decode(file_get_contents('php://input'), true);
    $alertId = $input['id'] ?? null;
    
    if (!$alertId) {
        throw new Exception('Alert ID is required');
    }
    
    createAlertsTableIfNotExists($connection);
    
    $stmt = $connection->prepare("
        UPDATE alerts 
        SET is_archived = 1, updated_at = NOW()
        WHERE id = :id
    ");
    $stmt->execute([':id' => $alertId]);
    
    return [
        'success' => true,
        'data' => [
            'message' => 'Alert archived successfully'
        ]
    ];
}

/**
 * تحديث تنبيه
 */
function updateAlert($connection) {
    $input = json_decode(file_get_contents('php://input'), true);
    $alertId = $input['id'] ?? null;
    
    if (!$alertId) {
        throw new Exception('Alert ID is required');
    }
    
    createAlertsTableIfNotExists($connection);
    
    $updateFields = [];
    $params = [':id' => $alertId];
    
    $allowedFields = ['title', 'message', 'priority', 'estimated_impact', 'tags'];
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            $updateFields[] = "$field = :$field";
            $params[":$field"] = $field === 'tags' ? json_encode($input[$field]) : $input[$field];
        }
    }
    
    if (empty($updateFields)) {
        throw new Exception('No fields to update');
    }
    
    $updateFields[] = "updated_at = NOW()";
    
    $sql = "UPDATE alerts SET " . implode(', ', $updateFields) . " WHERE id = :id";
    $stmt = $connection->prepare($sql);
    $stmt->execute($params);
    
    return [
        'success' => true,
        'data' => [
            'message' => 'Alert updated successfully'
        ]
    ];
}

/**
 * حذف تنبيه
 */
function deleteAlert($connection) {
    $alertId = $_GET['id'] ?? null;
    
    if (!$alertId) {
        throw new Exception('Alert ID is required');
    }
    
    createAlertsTableIfNotExists($connection);
    
    $stmt = $connection->prepare("DELETE FROM alerts WHERE id = :id");
    $stmt->execute([':id' => $alertId]);
    
    return [
        'success' => true,
        'data' => [
            'message' => 'Alert deleted successfully'
        ]
    ];
}

/**
 * إنشاء جدول التنبيهات إذا لم يكن موجود
 */
function createAlertsTableIfNotExists($connection) {
    $sql = "
        CREATE TABLE IF NOT EXISTS alerts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type VARCHAR(50) NOT NULL,
            priority ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium',
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            source VARCHAR(100) DEFAULT 'System',
            affected_users INT DEFAULT 0,
            estimated_impact ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low',
            tags JSON,
            is_resolved BOOLEAN DEFAULT FALSE,
            is_archived BOOLEAN DEFAULT FALSE,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            resolved_at TIMESTAMP NULL,
            INDEX idx_type (type),
            INDEX idx_priority (priority),
            INDEX idx_status (is_resolved, is_archived),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $connection->exec($sql);
}
?>
