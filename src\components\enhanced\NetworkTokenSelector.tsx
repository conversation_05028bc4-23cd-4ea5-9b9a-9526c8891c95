'use client';

import React, { useState, useEffect } from 'react';
import { ChevronDown, Network, Coins, AlertCircle, CheckCircle } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { enhancedContractApiService } from '@/services/enhancedContractApiService';

interface NetworkInfo {
  id: number;
  network_name: string;
  network_symbol: string;
  chain_id: number;
  is_testnet: boolean;
  is_active: boolean;
}

interface TokenInfo {
  id: number;
  network_id: number;
  token_address: string;
  token_symbol: string;
  token_name: string;
  decimals: number;
  is_stablecoin: boolean;
  is_active: boolean;
  icon_url?: string;
  min_trade_amount: number;
  max_trade_amount: number;
  platform_fee_rate: number;
}

interface NetworkTokenSelectorProps {
  selectedNetworkId?: number;
  selectedTokenId?: number;
  onNetworkChange: (networkId: number) => void;
  onTokenChange: (tokenId: number, tokenInfo: TokenInfo) => void;
  disabled?: boolean;
  showOnlyStablecoins?: boolean;
  className?: string;
}

const NetworkTokenSelector: React.FC<NetworkTokenSelectorProps> = ({
  selectedNetworkId,
  selectedTokenId,
  onNetworkChange,
  onTokenChange,
  disabled = false,
  showOnlyStablecoins = true,
  className = ''
}) => {
  const { t, isRTL } = useTranslation();
  const [networks, setNetworks] = useState<NetworkInfo[]>([]);
  const [tokens, setTokens] = useState<TokenInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [networkDropdownOpen, setNetworkDropdownOpen] = useState(false);
  const [tokenDropdownOpen, setTokenDropdownOpen] = useState(false);

  // جلب الشبكات المدعومة
  useEffect(() => {
    loadNetworks();
  }, []);

  // جلب العملات عند تغيير الشبكة
  useEffect(() => {
    if (selectedNetworkId) {
      loadTokens(selectedNetworkId);
    }
  }, [selectedNetworkId, showOnlyStablecoins]);

  const loadNetworks = async () => {
    try {
      setLoading(true);
      const response = await enhancedContractApiService.getSupportedNetworks(undefined, true);
      setNetworks(response.networks || []);
      setError(null);
    } catch (err) {
      setError('فشل في جلب الشبكات المدعومة');
      console.error('خطأ في جلب الشبكات:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadTokens = async (networkId: number) => {
    try {
      const response = await enhancedContractApiService.getSupportedTokens(
        networkId,
        showOnlyStablecoins ? true : undefined,
        true
      );
      setTokens(response.tokens || []);
    } catch (err) {
      setError('فشل في جلب العملات المدعومة');
      console.error('خطأ في جلب العملات:', err);
    }
  };

  const handleNetworkSelect = (network: NetworkInfo) => {
    onNetworkChange(network.id);
    setNetworkDropdownOpen(false);
    // إعادة تعيين العملة المختارة عند تغيير الشبكة
    if (selectedTokenId && tokens.length > 0) {
      const firstToken = tokens[0];
      onTokenChange(firstToken.id, firstToken);
    }
  };

  const handleTokenSelect = (token: TokenInfo) => {
    onTokenChange(token.id, token);
    setTokenDropdownOpen(false);
  };

  const selectedNetwork = networks.find(n => n.id === selectedNetworkId);
  const selectedToken = tokens.find(t => t.id === selectedTokenId);

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-2"></div>
          <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20 mb-2"></div>
          <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg ${className}`}>
        <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
          <AlertCircle className="w-5 h-5" />
          <span className="text-sm">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* اختيار الشبكة */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          <Network className="w-4 h-4 inline-block ml-1" />
          {t('network')}
        </label>
        <div className="relative">
          <button
            type="button"
            onClick={() => !disabled && setNetworkDropdownOpen(!networkDropdownOpen)}
            disabled={disabled}
            className={`
              w-full px-4 py-3 text-right bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 
              rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 
              disabled:opacity-50 disabled:cursor-not-allowed
              ${networkDropdownOpen ? 'ring-2 ring-blue-500 border-blue-500' : ''}
            `}
          >
            <div className="flex items-center justify-between">
              {selectedNetwork ? (
                <div className="flex items-center gap-3">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                    selectedNetwork.is_testnet ? 'bg-orange-500' : 'bg-green-500'
                  }`}>
                    {selectedNetwork.network_symbol}
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {selectedNetwork.network_name}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Chain ID: {selectedNetwork.chain_id}
                    </div>
                  </div>
                </div>
              ) : (
                <span className="text-gray-500 dark:text-gray-400">
                  {t('selectNetwork')}
                </span>
              )}
              <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${
                networkDropdownOpen ? 'rotate-180' : ''
              }`} />
            </div>
          </button>

          {networkDropdownOpen && (
            <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-auto">
              {networks.map((network) => (
                <button
                  key={network.id}
                  onClick={() => handleNetworkSelect(network)}
                  className="w-full px-4 py-3 text-right hover:bg-gray-50 dark:hover:bg-gray-700 focus:bg-gray-50 dark:focus:bg-gray-700 focus:outline-none"
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                      network.is_testnet ? 'bg-orange-500' : 'bg-green-500'
                    }`}>
                      {network.network_symbol}
                    </div>
                    <div className="text-right flex-1">
                      <div className="font-medium text-gray-900 dark:text-white">
                        {network.network_name}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Chain ID: {network.chain_id}
                        {network.is_testnet && (
                          <span className="mr-2 px-1.5 py-0.5 bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400 rounded text-xs">
                            {t('testnet')}
                          </span>
                        )}
                      </div>
                    </div>
                    {selectedNetworkId === network.id && (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    )}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* اختيار العملة */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          <Coins className="w-4 h-4 inline-block ml-1" />
          {t('token')}
        </label>
        <div className="relative">
          <button
            type="button"
            onClick={() => !disabled && selectedNetworkId && setTokenDropdownOpen(!tokenDropdownOpen)}
            disabled={disabled || !selectedNetworkId || tokens.length === 0}
            className={`
              w-full px-4 py-3 text-right bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 
              rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 
              disabled:opacity-50 disabled:cursor-not-allowed
              ${tokenDropdownOpen ? 'ring-2 ring-blue-500 border-blue-500' : ''}
            `}
          >
            <div className="flex items-center justify-between">
              {selectedToken ? (
                <div className="flex items-center gap-3">
                  {selectedToken.icon_url ? (
                    <img 
                      src={selectedToken.icon_url} 
                      alt={selectedToken.token_symbol}
                      className="w-6 h-6 rounded-full"
                    />
                  ) : (
                    <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-xs font-bold text-white">
                      {selectedToken.token_symbol.charAt(0)}
                    </div>
                  )}
                  <div className="text-right">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {selectedToken.token_symbol}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {selectedToken.token_name}
                      {selectedToken.is_stablecoin && (
                        <span className="mr-2 px-1.5 py-0.5 bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 rounded text-xs">
                          {t('stablecoin')}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <span className="text-gray-500 dark:text-gray-400">
                  {!selectedNetworkId ? t('selectNetworkFirst') : t('selectToken')}
                </span>
              )}
              <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${
                tokenDropdownOpen ? 'rotate-180' : ''
              }`} />
            </div>
          </button>

          {tokenDropdownOpen && tokens.length > 0 && (
            <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-auto">
              {tokens.map((token) => (
                <button
                  key={token.id}
                  onClick={() => handleTokenSelect(token)}
                  className="w-full px-4 py-3 text-right hover:bg-gray-50 dark:hover:bg-gray-700 focus:bg-gray-50 dark:focus:bg-gray-700 focus:outline-none"
                >
                  <div className="flex items-center gap-3">
                    {token.icon_url ? (
                      <img 
                        src={token.icon_url} 
                        alt={token.token_symbol}
                        className="w-6 h-6 rounded-full"
                      />
                    ) : (
                      <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-xs font-bold text-white">
                        {token.token_symbol.charAt(0)}
                      </div>
                    )}
                    <div className="text-right flex-1">
                      <div className="font-medium text-gray-900 dark:text-white">
                        {token.token_symbol}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {token.token_name}
                        {token.is_stablecoin && (
                          <span className="mr-2 px-1.5 py-0.5 bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 rounded text-xs">
                            {t('stablecoin')}
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-gray-400">
                        {t('fee')}: {(token.platform_fee_rate * 100).toFixed(2)}%
                      </div>
                    </div>
                    {selectedTokenId === token.id && (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    )}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* معلومات إضافية عن العملة المختارة */}
      {selectedToken && (
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="text-sm text-blue-800 dark:text-blue-200">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <span className="font-medium">{t('minAmount')}:</span>
                <span className="mr-1">{selectedToken.min_trade_amount} {selectedToken.token_symbol}</span>
              </div>
              <div>
                <span className="font-medium">{t('maxAmount')}:</span>
                <span className="mr-1">{selectedToken.max_trade_amount} {selectedToken.token_symbol}</span>
              </div>
              <div>
                <span className="font-medium">{t('platformFee')}:</span>
                <span className="mr-1">{(selectedToken.platform_fee_rate * 100).toFixed(2)}%</span>
              </div>
              <div>
                <span className="font-medium">{t('decimals')}:</span>
                <span className="mr-1">{selectedToken.decimals}</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NetworkTokenSelector;
