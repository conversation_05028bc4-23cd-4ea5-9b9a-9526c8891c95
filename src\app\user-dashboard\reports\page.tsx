'use client';

import { useState } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Calendar, 
  Download, 
  FileText, 
  PieChart,
  Activity,
  Target,
  Award,
  AlertCircle
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';

interface ReportData {
  period: string;
  totalTrades: number;
  successfulTrades: number;
  volume: number;
  profit: number;
  loss: number;
  fees: number;
  winRate: number;
  averageTradeSize: number;
}

export default function ReportsPage() {
  const { t, formatCurrency, formatNumber } = useUserDashboardTranslation();
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [reportType, setReportType] = useState('trading');

  // بيانات تجريبية للتقارير
  const reportData: ReportData = {
    period: 'الشهر الحالي',
    totalTrades: 45,
    successfulTrades: 42,
    volume: 125000,
    profit: 8500,
    loss: 1200,
    fees: 750,
    winRate: 93.3,
    averageTradeSize: 2777
  };

  const periods = [
    { value: 'week', label: 'هذا الأسبوع' },
    { value: 'month', label: 'هذا الشهر' },
    { value: 'quarter', label: 'هذا الربع' },
    { value: 'year', label: 'هذا العام' },
    { value: 'custom', label: 'فترة مخصصة' }
  ];

  const reportTypes = [
    { value: 'trading', label: 'تقرير التداول', icon: BarChart3 },
    { value: 'financial', label: 'التقرير المالي', icon: DollarSign },
    { value: 'performance', label: 'تقرير الأداء', icon: TrendingUp }
  ];

  const exportFormats = [
    { value: 'pdf', label: 'PDF', icon: FileText },
    { value: 'excel', label: 'Excel', icon: Download },
    { value: 'csv', label: 'CSV', icon: Download }
  ];

  const handleExport = (format: string) => {
    console.log(`Exporting ${reportType} report as ${format}`);
    // هنا سيتم تنفيذ منطق التصدير
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <BarChart3 className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('reports.title')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {t('reports.subtitle')}
            </p>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Report Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              نوع التقرير
            </label>
            <select
              value={reportType}
              onChange={(e) => setReportType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {reportTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          {/* Period */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              الفترة الزمنية
            </label>
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {periods.map((period) => (
                <option key={period.value} value={period.value}>
                  {period.label}
                </option>
              ))}
            </select>
          </div>

          {/* Export */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              تصدير التقرير
            </label>
            <div className="flex space-x-2 rtl:space-x-reverse">
              {exportFormats.map((format) => (
                <button
                  key={format.value}
                  onClick={() => handleExport(format.value)}
                  className="flex-1 flex items-center justify-center space-x-1 rtl:space-x-reverse px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  <format.icon className="w-4 h-4" />
                  <span className="text-sm">{format.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Trading Report */}
      {reportType === 'trading' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Trades */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('reports.tradingReport.totalTrades')}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatNumber(reportData.totalTrades)}
                </p>
              </div>
              <Activity className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
          </div>

          {/* Successful Trades */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('reports.tradingReport.successfulTrades')}
                </p>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {formatNumber(reportData.successfulTrades)}
                </p>
              </div>
              <Target className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
          </div>

          {/* Volume */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('reports.tradingReport.volume')}
                </p>
                <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {formatCurrency(reportData.volume, 'SAR')}
                </p>
              </div>
              <BarChart3 className="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
          </div>

          {/* Win Rate */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  معدل النجاح
                </p>
                <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  {formatNumber(reportData.winRate)}%
                </p>
              </div>
              <Award className="w-8 h-8 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>
      )}

      {/* Financial Report */}
      {reportType === 'financial' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Profit */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('reports.financialReport.income')}
                </p>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {formatCurrency(reportData.profit, 'SAR')}
                </p>
                <div className="flex items-center space-x-1 rtl:space-x-reverse mt-1">
                  <TrendingUp className="w-4 h-4 text-green-500" />
                  <span className="text-sm text-green-600 dark:text-green-400">+12.5%</span>
                </div>
              </div>
              <TrendingUp className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
          </div>

          {/* Loss */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('reports.financialReport.expenses')}
                </p>
                <p className="text-2xl font-bold text-red-600 dark:text-red-400">
                  {formatCurrency(reportData.loss, 'SAR')}
                </p>
                <div className="flex items-center space-x-1 rtl:space-x-reverse mt-1">
                  <TrendingDown className="w-4 h-4 text-red-500" />
                  <span className="text-sm text-red-600 dark:text-red-400">-3.2%</span>
                </div>
              </div>
              <TrendingDown className="w-8 h-8 text-red-600 dark:text-red-400" />
            </div>
          </div>

          {/* Net Profit */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {t('reports.financialReport.netProfit')}
                </p>
                <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {formatCurrency(reportData.profit - reportData.loss - reportData.fees, 'SAR')}
                </p>
                <div className="flex items-center space-x-1 rtl:space-x-reverse mt-1">
                  <TrendingUp className="w-4 h-4 text-blue-500" />
                  <span className="text-sm text-blue-600 dark:text-blue-400">+8.7%</span>
                </div>
              </div>
              <DollarSign className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>
      )}

      {/* Performance Report */}
      {reportType === 'performance' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Performance Metrics */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              مؤشرات الأداء
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">معدل الفوز</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {formatNumber(reportData.winRate)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">متوسط حجم الصفقة</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {formatCurrency(reportData.averageTradeSize, 'SAR')}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">أفضل صفقة</span>
                <span className="font-semibold text-green-600 dark:text-green-400">
                  +{formatCurrency(2500, 'SAR')}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">أسوأ صفقة</span>
                <span className="font-semibold text-red-600 dark:text-red-400">
                  -{formatCurrency(800, 'SAR')}
                </span>
              </div>
            </div>
          </div>

          {/* Consistency Chart */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              الثبات في الأداء
            </h3>
            <div className="flex items-center justify-center h-32">
              <PieChart className="w-16 h-16 text-gray-400" />
              <div className="ml-4 text-center">
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">85%</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">معدل الثبات</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Report Summary */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          ملخص التقرير - {reportData.period}
        </h3>
        <div className="prose dark:prose-invert max-w-none">
          <p className="text-gray-600 dark:text-gray-400">
            خلال {reportData.period}، قمت بتنفيذ {formatNumber(reportData.totalTrades)} صفقة 
            بمعدل نجاح {formatNumber(reportData.winRate)}%. 
            حققت أرباحاً إجمالية قدرها {formatCurrency(reportData.profit, 'SAR')} 
            مع خسائر بقيمة {formatCurrency(reportData.loss, 'SAR')}، 
            مما يعني صافي ربح قدره {formatCurrency(reportData.profit - reportData.loss - reportData.fees, 'SAR')}.
          </p>
          <p className="text-gray-600 dark:text-gray-400 mt-3">
            متوسط حجم الصفقة كان {formatCurrency(reportData.averageTradeSize, 'SAR')}، 
            وإجمالي الحجم المتداول {formatCurrency(reportData.volume, 'SAR')}. 
            الرسوم المدفوعة بلغت {formatCurrency(reportData.fees, 'SAR')}.
          </p>
        </div>
      </div>
    </div>
  );
}
