/**
 * خدمة سجل نشاط المدراء
 * Admin Activity Log Service
 * 
 * يوفر وظائف لعرض وإدارة سجل نشاط المدراء
 * Provides functions for viewing and managing admin activity logs
 */

import { API_BASE_URL } from '@/constants';

// أنواع البيانات
export interface ActivityFilters {
  admin_id?: number;
  action_type?: string | string[];
  target_type?: string;
  target_id?: number;
  date_from?: string;
  date_to?: string;
  ip_address?: string;
}

export interface Pagination {
  page: number;
  limit: number;
}

export interface PaginationResponse {
  current_page: number;
  per_page: number;
  total: number;
  total_pages: number;
}

export interface ActivityLog {
  id: number;
  admin_id: number;
  action_type: string;
  target_type: string;
  target_id: number;
  details: any;
  ip_address: string;
  user_agent: string;
  created_at: string;
  admin_username: string;
  admin_full_name: string;
}

export interface ActivityStatistics {
  basic_stats: {
    total_activities: number;
    active_admins: number;
    unique_ips: number;
    active_days: number;
  };
  activity_types: Array<{
    action_type: string;
    count: number;
    admin_count: number;
  }>;
  admin_stats: Array<{
    username: string;
    full_name: string;
    activity_count: number;
    action_types_count: number;
    last_activity: string;
  }>;
  daily_activity: Array<{
    activity_date: string;
    activity_count: number;
    admin_count: number;
  }>;
}

export interface ActionType {
  action_type: string;
  count: number;
}

export interface ActiveAdmin {
  id: number;
  username: string;
  full_name: string;
  activity_count: number;
  last_activity: string;
}

export interface ActivityListResponse {
  logs: ActivityLog[];
  pagination: PaginationResponse;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  error_en?: string;
  message?: string;
  message_en?: string;
  pagination?: PaginationResponse;
}

class AdminActivityService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/admin`;
  }

  /**
   * الحصول على سجل النشاط مع فلترة
   * Get activity logs with filtering
   */
  async getActivityLogs(
    filters: ActivityFilters = {},
    pagination: Pagination = { page: 1, limit: 50 }
  ): Promise<ActivityListResponse> {
    try {
      const params = new URLSearchParams();
      params.append('action', 'logs');
      
      // إضافة معاملات الفلترة
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(`${key}[]`, v.toString()));
          } else {
            params.append(key, value.toString());
          }
        }
      });
      
      // إضافة معاملات الترقيم
      params.append('page', pagination.page.toString());
      params.append('limit', pagination.limit.toString());
      
      const response = await fetch(`${this.baseUrl}/admin-activity.php?${params}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const result: ApiResponse<ActivityLog[]> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في جلب سجل النشاط');
      }
      
      return {
        logs: result.data || [],
        pagination: result.pagination || {
          current_page: 1,
          per_page: 50,
          total: 0,
          total_pages: 0
        }
      };
    } catch (error) {
      console.error('Error fetching activity logs:', error);
      throw error;
    }
  }

  /**
   * الحصول على إحصائيات النشاط
   * Get activity statistics
   */
  async getActivityStatistics(dateRange?: { start: string; end: string }): Promise<ActivityStatistics> {
    try {
      const params = new URLSearchParams();
      params.append('action', 'statistics');
      
      if (dateRange) {
        params.append('date_from', dateRange.start);
        params.append('date_to', dateRange.end);
      }
      
      const response = await fetch(`${this.baseUrl}/admin-activity.php?${params}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const result: ApiResponse<ActivityStatistics> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في جلب إحصائيات النشاط');
      }
      
      return result.data || {
        basic_stats: {
          total_activities: 0,
          active_admins: 0,
          unique_ips: 0,
          active_days: 0
        },
        activity_types: [],
        admin_stats: [],
        daily_activity: []
      };
    } catch (error) {
      console.error('Error fetching activity statistics:', error);
      throw error;
    }
  }

  /**
   * الحصول على أنواع النشاط المتاحة
   * Get available action types
   */
  async getActionTypes(): Promise<ActionType[]> {
    try {
      const params = new URLSearchParams();
      params.append('action', 'action_types');
      
      const response = await fetch(`${this.baseUrl}/admin-activity.php?${params}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const result: ApiResponse<ActionType[]> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في جلب أنواع النشاط');
      }
      
      return result.data || [];
    } catch (error) {
      console.error('Error fetching action types:', error);
      throw error;
    }
  }

  /**
   * الحصول على قائمة المدراء النشطين
   * Get active admins list
   */
  async getActiveAdmins(): Promise<ActiveAdmin[]> {
    try {
      const params = new URLSearchParams();
      params.append('action', 'active_admins');
      
      const response = await fetch(`${this.baseUrl}/admin-activity.php?${params}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const result: ApiResponse<ActiveAdmin[]> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في جلب قائمة المدراء');
      }
      
      return result.data || [];
    } catch (error) {
      console.error('Error fetching active admins:', error);
      throw error;
    }
  }

  /**
   * تصدير سجل النشاط
   * Export activity log
   */
  async exportActivityLog(filters: ActivityFilters = {}): Promise<Blob> {
    try {
      const params = new URLSearchParams();
      params.append('action', 'export');
      
      // إضافة معاملات الفلترة
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(`${key}[]`, v.toString()));
          } else {
            params.append(key, value.toString());
          }
        }
      });
      
      const response = await fetch(`${this.baseUrl}/admin-activity.php?${params}`, {
        method: 'GET',
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('فشل في تصدير سجل النشاط');
      }
      
      return await response.blob();
    } catch (error) {
      console.error('Error exporting activity log:', error);
      throw error;
    }
  }

  /**
   * الحصول على ترجمة نوع النشاط
   * Get action type translation
   */
  getActionTypeTranslation(actionType: string): { ar: string; en: string } {
    const translations: Record<string, { ar: string; en: string }> = {
      'trade_status_update': {
        ar: 'تحديث حالة الصفقة',
        en: 'Trade Status Update'
      },
      'dispute_resolution': {
        ar: 'حل النزاع',
        en: 'Dispute Resolution'
      },
      'user_suspension': {
        ar: 'تعليق المستخدم',
        en: 'User Suspension'
      },
      'trade_note_added': {
        ar: 'إضافة ملاحظة للصفقة',
        en: 'Trade Note Added'
      },
      'dispute_escalation': {
        ar: 'تصعيد النزاع',
        en: 'Dispute Escalation'
      },
      'user_verification': {
        ar: 'توثيق المستخدم',
        en: 'User Verification'
      },
      'system_settings_update': {
        ar: 'تحديث إعدادات النظام',
        en: 'System Settings Update'
      },
      'admin_login': {
        ar: 'تسجيل دخول المدير',
        en: 'Admin Login'
      },
      'admin_logout': {
        ar: 'تسجيل خروج المدير',
        en: 'Admin Logout'
      }
    };
    
    return translations[actionType] || { ar: actionType, en: actionType };
  }

  /**
   * الحصول على ترجمة نوع الهدف
   * Get target type translation
   */
  getTargetTypeTranslation(targetType: string): { ar: string; en: string } {
    const translations: Record<string, { ar: string; en: string }> = {
      'trade': { ar: 'صفقة', en: 'Trade' },
      'user': { ar: 'مستخدم', en: 'User' },
      'offer': { ar: 'عرض', en: 'Offer' },
      'dispute': { ar: 'نزاع', en: 'Dispute' },
      'system': { ar: 'نظام', en: 'System' },
      'admin': { ar: 'مدير', en: 'Admin' }
    };
    
    return translations[targetType] || { ar: targetType, en: targetType };
  }

  /**
   * تنسيق تفاصيل النشاط للعرض
   * Format activity details for display
   */
  formatActivityDetails(details: any): string {
    if (!details) return '';
    
    try {
      if (typeof details === 'string') {
        details = JSON.parse(details);
      }
      
      const formatted = Object.entries(details)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ');
      
      return formatted;
    } catch (error) {
      return details.toString();
    }
  }

  /**
   * الحصول على لون حالة النشاط
   * Get activity status color
   */
  getActivityStatusColor(actionType: string): string {
    const colorMap: Record<string, string> = {
      'trade_status_update': 'text-blue-600 dark:text-blue-400',
      'dispute_resolution': 'text-green-600 dark:text-green-400',
      'user_suspension': 'text-red-600 dark:text-red-400',
      'trade_note_added': 'text-yellow-600 dark:text-yellow-400',
      'dispute_escalation': 'text-orange-600 dark:text-orange-400',
      'user_verification': 'text-purple-600 dark:text-purple-400',
      'system_settings_update': 'text-indigo-600 dark:text-indigo-400',
      'admin_login': 'text-green-600 dark:text-green-400',
      'admin_logout': 'text-gray-600 dark:text-gray-400'
    };
    
    return colorMap[actionType] || 'text-gray-600 dark:text-gray-400';
  }
}

export const adminActivityService = new AdminActivityService();
export default adminActivityService;
