<?php
/**
 * نظام الحماية من هجمات CSRF
 * CSRF Protection System
 */

class CSRFProtection {
    private $tokenName = 'csrf_token';
    private $sessionKey = 'csrf_tokens';
    private $tokenLifetime = 3600; // ساعة واحدة
    private $maxTokens = 10; // حد أقصى للتوكنات المحفوظة

    public function __construct() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // تهيئة مصفوفة التوكنات في الجلسة
        if (!isset($_SESSION[$this->sessionKey])) {
            $_SESSION[$this->sessionKey] = [];
        }

        // تنظيف التوكنات المنتهية الصلاحية
        $this->cleanupExpiredTokens();
    }
    
    /**
     * توليد توكن CSRF جديد
     */
    public function generateToken($action = 'default') {
        $token = bin2hex(random_bytes(32));
        $timestamp = time();

        // حفظ التوكن في الجلسة
        $_SESSION[$this->sessionKey][$token] = [
            'action' => $action,
            'timestamp' => $timestamp,
            'used' => false
        ];

        // تنظيف التوكنات الزائدة
        $this->limitTokenCount();

        return $token;
    }

    /**
     * التحقق من صحة توكن CSRF
     */
    public function validateToken($token, $action = 'default', $singleUse = true) {
        if (empty($token)) {
            return false;
        }

        // فحص وجود التوكن في الجلسة
        if (!isset($_SESSION[$this->sessionKey][$token])) {
            return false;
        }

        $tokenData = $_SESSION[$this->sessionKey][$token];

        // فحص انتهاء صلاحية التوكن
        if (time() - $tokenData['timestamp'] > $this->tokenLifetime) {
            unset($_SESSION[$this->sessionKey][$token]);
            return false;
        }

        // فحص العملية المرتبطة بالتوكن
        if ($tokenData['action'] !== $action) {
            return false;
        }

        // فحص إذا كان التوكن مستخدم مسبقاً (للاستخدام الواحد)
        if ($singleUse && $tokenData['used']) {
            return false;
        }

        // تحديد التوكن كمستخدم
        if ($singleUse) {
            $_SESSION[$this->sessionKey][$token]['used'] = true;
        }

        return true;
    }
    
    /**
     * التحقق من طلب HTTP
     */
    public function validateRequest($action = 'default', $singleUse = true) {
        $token = $this->getTokenFromRequest();
        
        if (!$token) {
            throw new Exception('CSRF token مفقود');
        }
        
        if (!$this->validateToken($token, $action, $singleUse)) {
            throw new Exception('CSRF token غير صحيح أو منتهي الصلاحية');
        }
        
        return true;
    }
    
    /**
     * استخراج التوكن من الطلب
     */
    private function getTokenFromRequest() {
        // فحص في الهيدر أولاً
        $headers = getallheaders();
        if (isset($headers['X-CSRF-Token'])) {
            return $headers['X-CSRF-Token'];
        }
        
        // فحص في POST data
        if (isset($_POST[$this->tokenName])) {
            return $_POST[$this->tokenName];
        }
        
        // فحص في JSON body
        $input = json_decode(file_get_contents('php://input'), true);
        if (isset($input[$this->tokenName])) {
            return $input[$this->tokenName];
        }
        
        // فحص في GET parameters (غير مستحسن لكن للتوافق)
        if (isset($_GET[$this->tokenName])) {
            return $_GET[$this->tokenName];
        }
        
        return null;
    }
    
    /**
     * تنظيف التوكنات المنتهية الصلاحية
     */
    private function cleanupExpiredTokens() {
        $currentTime = time();
        
        foreach ($_SESSION[$this->sessionKey] as $token => $data) {
            if ($currentTime - $data['timestamp'] > $this->tokenLifetime) {
                unset($_SESSION[$this->sessionKey][$token]);
            }
        }
    }
    
    /**
     * تحديد عدد التوكنات المحفوظة
     */
    private function limitTokenCount() {
        $tokens = $_SESSION[$this->sessionKey];
        
        if (count($tokens) > $this->maxTokens) {
            // ترتيب حسب الوقت وحذف الأقدم
            uasort($tokens, function($a, $b) {
                return $a['timestamp'] - $b['timestamp'];
            });
            
            $tokensToRemove = count($tokens) - $this->maxTokens;
            $removedCount = 0;
            
            foreach ($tokens as $token => $data) {
                if ($removedCount >= $tokensToRemove) break;
                unset($_SESSION[$this->sessionKey][$token]);
                $removedCount++;
            }
        }
    }
    
    /**
     * الحصول على توكن للاستخدام في النماذج
     */
    public function getTokenForForm($action = 'default') {
        return $this->generateToken($action);
    }
    
    /**
     * إنشاء حقل مخفي للنماذج
     */
    public function getHiddenField($action = 'default') {
        $token = $this->generateToken($action);
        return "<input type=\"hidden\" name=\"{$this->tokenName}\" value=\"{$token}\">";
    }
    
    /**
     * الحصول على توكن للاستخدام في JavaScript
     */
    public function getTokenForAjax($action = 'default') {
        return [
            'token' => $this->generateToken($action),
            'name' => $this->tokenName
        ];
    }
    
    /**
     * إلغاء توكن محدد
     */
    public function revokeToken($token) {
        if (isset($_SESSION[$this->sessionKey][$token])) {
            unset($_SESSION[$this->sessionKey][$token]);
            return true;
        }
        return false;
    }
    
    /**
     * إلغاء جميع التوكنات
     */
    public function revokeAllTokens() {
        $_SESSION[$this->sessionKey] = [];
        return true;
    }
    
    /**
     * الحصول على إحصائيات التوكنات
     */
    public function getTokenStats() {
        $tokens = $_SESSION[$this->sessionKey];
        $stats = [
            'total_tokens' => count($tokens),
            'active_tokens' => 0,
            'expired_tokens' => 0,
            'used_tokens' => 0,
            'actions' => []
        ];
        
        $currentTime = time();
        
        foreach ($tokens as $token => $data) {
            if ($currentTime - $data['timestamp'] > $this->tokenLifetime) {
                $stats['expired_tokens']++;
            } else {
                $stats['active_tokens']++;
            }
            
            if ($data['used']) {
                $stats['used_tokens']++;
            }
            
            $action = $data['action'];
            if (!isset($stats['actions'][$action])) {
                $stats['actions'][$action] = 0;
            }
            $stats['actions'][$action]++;
        }
        
        return $stats;
    }
    
    /**
     * تعيين إعدادات مخصصة
     */
    public function setConfig($config) {
        if (isset($config['token_name'])) {
            $this->tokenName = $config['token_name'];
        }
        
        if (isset($config['session_key'])) {
            $this->sessionKey = $config['session_key'];
        }
        
        if (isset($config['token_lifetime'])) {
            $this->tokenLifetime = $config['token_lifetime'];
        }
        
        if (isset($config['max_tokens'])) {
            $this->maxTokens = $config['max_tokens'];
        }
    }
}

/**
 * دالة مساعدة للحصول على instance من CSRFProtection
 */
function getCSRFProtection() {
    static $instance = null;
    if ($instance === null) {
        $instance = new CSRFProtection();
    }
    return $instance;
}

/**
 * دالة مساعدة لتوليد توكن CSRF
 */
function generateCSRFToken($action = 'default') {
    return getCSRFProtection()->generateToken($action);
}

/**
 * دالة مساعدة للتحقق من توكن CSRF
 */
function validateCSRFToken($token, $action = 'default', $singleUse = true) {
    return getCSRFProtection()->validateToken($token, $action, $singleUse);
}

/**
 * دالة مساعدة للتحقق من الطلب
 */
function validateCSRFRequest($action = 'default', $singleUse = true) {
    return getCSRFProtection()->validateRequest($action, $singleUse);
}

/**
 * Middleware للتحقق التلقائي من CSRF
 */
function csrfMiddleware($action = 'default', $excludeMethods = ['GET', 'HEAD', 'OPTIONS']) {
    $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    
    // تجاهل الطرق المستثناة
    if (in_array($method, $excludeMethods)) {
        return true;
    }
    
    try {
        validateCSRFRequest($action);
        return true;
    } catch (Exception $e) {
        http_response_code(403);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage(),
            'error_code' => 'CSRF_TOKEN_INVALID'
        ]);
        exit();
    }
}
?>
