<?php
/**
 * API لجلب العروض المرتبطة بالعقد الذكي
 * Get offers linked to smart contract
 */

// تضمين إعدادات CORS
require_once __DIR__ . '/../cors.php';

// التحقق من طريقة الطلب
validateRequestMethod(['GET']);

try {
    // تضمين إعدادات قاعدة البيانات
    require_once __DIR__ . '/../config/database.php';
    
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // جلب العروض التي لها blockchain_trade_id
    $query = "
        SELECT 
            o.id,
            o.user_id,
            o.offer_type,
            o.amount,
            o.min_amount,
            o.max_amount,
            o.price,
            o.currency,
            o.stablecoin,
            o.payment_methods,
            o.terms,
            o.is_active,
            o.blockchain_trade_id,
            o.contract_status,
            o.available_amount,
            o.created_at,
            o.updated_at,
            o.last_sync_at,
            u.username,
            u.full_name,
            u.wallet_address,
            u.rating,
            u.total_trades,
            u.is_verified
        FROM offers o
        LEFT JOIN users u ON o.user_id = u.id
        WHERE o.blockchain_trade_id IS NOT NULL
        AND o.is_active = 1
        ORDER BY o.created_at DESC
    ";
    
    $stmt = $connection->prepare($query);
    $stmt->execute();
    $offers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تنسيق البيانات
    $formattedOffers = [];
    foreach ($offers as $offer) {
        $formattedOffers[] = [
            'id' => $offer['id'],
            'user_id' => $offer['user_id'],
            'offer_type' => $offer['offer_type'],
            'amount' => (float) $offer['amount'],
            'min_amount' => (float) $offer['min_amount'],
            'max_amount' => (float) $offer['max_amount'],
            'price' => (float) $offer['price'],
            'currency' => $offer['currency'],
            'stablecoin' => $offer['stablecoin'],
            'payment_methods' => json_decode($offer['payment_methods'], true),
            'terms' => $offer['terms'],
            'is_active' => (bool) $offer['is_active'],
            'blockchain_trade_id' => (int) $offer['blockchain_trade_id'],
            'contract_status' => $offer['contract_status'],
            'available_amount' => (float) $offer['available_amount'],
            'created_at' => $offer['created_at'],
            'updated_at' => $offer['updated_at'],
            'last_sync_at' => $offer['last_sync_at'],
            'user' => [
                'username' => $offer['username'],
                'full_name' => $offer['full_name'],
                'wallet_address' => $offer['wallet_address'],
                'rating' => (float) $offer['rating'],
                'total_trades' => (int) $offer['total_trades'],
                'is_verified' => (bool) $offer['is_verified']
            ]
        ];
    }
    
    sendSuccessResponse([
        'data' => $formattedOffers,
        'count' => count($formattedOffers)
    ], 'تم جلب العروض المرتبطة بالعقد الذكي بنجاح');
    
} catch (Exception $e) {
    logError("Error fetching offers with blockchain ID: " . $e->getMessage());
    sendErrorResponse('خطأ في جلب العروض المرتبطة بالعقد الذكي: ' . $e->getMessage(), 500);
}
?>
