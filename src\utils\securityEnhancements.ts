/**
 * تحسينات الأمان لمنصة إيكاروس P2P
 * Security Enhancements for Ikaros P2P Platform
 */

import crypto from 'crypto';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';

// ===== تشفير البيانات الحساسة =====

export class DataEncryption {
  private static readonly ALGORITHM = 'aes-256-gcm';
  private static readonly KEY_LENGTH = 32;
  private static readonly IV_LENGTH = 16;
  private static readonly TAG_LENGTH = 16;

  /**
   * تشفير البيانات الحساسة
   */
  static encrypt(text: string, secretKey: string): string {
    try {
      const key = crypto.scryptSync(secretKey, 'salt', this.KEY_LENGTH);
      const iv = crypto.randomBytes(this.IV_LENGTH);
      const cipher = crypto.createCipher(this.ALGORITHM, key);
      
      cipher.setAAD(Buffer.from('ikaros-p2p', 'utf8'));
      
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const tag = cipher.getAuthTag();
      
      return iv.toString('hex') + ':' + tag.toString('hex') + ':' + encrypted;
    } catch (error) {
      throw new Error('فشل في تشفير البيانات');
    }
  }

  /**
   * فك تشفير البيانات
   */
  static decrypt(encryptedData: string, secretKey: string): string {
    try {
      const parts = encryptedData.split(':');
      if (parts.length !== 3) {
        throw new Error('تنسيق البيانات المشفرة غير صحيح');
      }

      const [ivHex, tagHex, encrypted] = parts;
      const key = crypto.scryptSync(secretKey, 'salt', this.KEY_LENGTH);
      const iv = Buffer.from(ivHex, 'hex');
      const tag = Buffer.from(tagHex, 'hex');
      
      const decipher = crypto.createDecipher(this.ALGORITHM, key);
      decipher.setAuthTag(tag);
      decipher.setAAD(Buffer.from('ikaros-p2p', 'utf8'));
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      throw new Error('فشل في فك تشفير البيانات');
    }
  }

  /**
   * إنشاء hash آمن للكلمات السرية
   */
  static async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
  }

  /**
   * التحقق من كلمة السر
   */
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return await bcrypt.compare(password, hash);
  }

  /**
   * إنشاء مفتاح عشوائي آمن
   */
  static generateSecureKey(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * إنشاء رمز OTP آمن
   */
  static generateOTP(length: number = 6): string {
    const digits = '0123456789';
    let otp = '';
    
    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, digits.length);
      otp += digits[randomIndex];
    }
    
    return otp;
  }
}

// ===== إدارة الجلسات الآمنة =====

export class SecureSessionManager {
  private static readonly SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 ساعة
  private static readonly REFRESH_THRESHOLD = 2 * 60 * 60 * 1000; // ساعتان

  /**
   * إنشاء JWT token آمن
   */
  static createToken(payload: any, expiresIn: string = '24h'): string {
    const secret = process.env.JWT_SECRET || 'fallback-secret-key';
    
    return jwt.sign(
      {
        ...payload,
        iat: Math.floor(Date.now() / 1000),
        jti: crypto.randomUUID() // معرف فريد للتوكن
      },
      secret,
      {
        expiresIn,
        issuer: 'ikaros-p2p',
        audience: 'ikaros-users'
      }
    );
  }

  /**
   * التحقق من صحة التوكن
   */
  static verifyToken(token: string): any {
    try {
      const secret = process.env.JWT_SECRET || 'fallback-secret-key';
      
      return jwt.verify(token, secret, {
        issuer: 'ikaros-p2p',
        audience: 'ikaros-users'
      });
    } catch (error) {
      throw new Error('توكن غير صالح');
    }
  }

  /**
   * تحديث التوكن إذا كان قريب من الانتهاء
   */
  static refreshTokenIfNeeded(token: string): string | null {
    try {
      const decoded = this.verifyToken(token);
      const now = Math.floor(Date.now() / 1000);
      const timeUntilExpiry = decoded.exp - now;
      
      // إذا كان التوكن سينتهي خلال ساعتين، أنشئ توكن جديد
      if (timeUntilExpiry < this.REFRESH_THRESHOLD / 1000) {
        const newPayload = { ...decoded };
        delete newPayload.iat;
        delete newPayload.exp;
        delete newPayload.jti;
        
        return this.createToken(newPayload);
      }
      
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * إبطال التوكن (إضافة إلى قائمة سوداء)
   */
  static async revokeToken(token: string): Promise<void> {
    try {
      const decoded = this.verifyToken(token);
      
      // في التطبيق الحقيقي، يجب حفظ معرف التوكن في قاعدة البيانات أو Redis
      // للتحقق منه لاحقاً
      console.log(`Token revoked: ${decoded.jti}`);
    } catch (error) {
      // التوكن غير صالح أصلاً
    }
  }
}

// ===== حماية من الهجمات =====

export class AttackProtection {
  /**
   * تحديد معدل الطلبات العام
   */
  static generalRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 دقيقة
    max: 100, // حد أقصى 100 طلب لكل IP
    message: {
      error: 'تم تجاوز الحد المسموح من الطلبات. حاول مرة أخرى لاحقاً.',
      retryAfter: '15 minutes'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // تجاهل الطلبات من IPs موثوقة
      const trustedIPs = ['127.0.0.1', '::1'];
      return trustedIPs.includes(req.ip);
    }
  });

  /**
   * تحديد معدل طلبات تسجيل الدخول
   */
  static loginRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 دقيقة
    max: 5, // حد أقصى 5 محاولات تسجيل دخول
    message: {
      error: 'تم تجاوز الحد المسموح من محاولات تسجيل الدخول. حاول مرة أخرى بعد 15 دقيقة.',
      retryAfter: '15 minutes'
    },
    skipSuccessfulRequests: true
  });

  /**
   * تحديد معدل طلبات إنشاء الحساب
   */
  static registrationRateLimit = rateLimit({
    windowMs: 60 * 60 * 1000, // ساعة واحدة
    max: 3, // حد أقصى 3 حسابات جديدة لكل IP
    message: {
      error: 'تم تجاوز الحد المسموح من إنشاء الحسابات. حاول مرة أخرى بعد ساعة.',
      retryAfter: '1 hour'
    }
  });

  /**
   * تحديد معدل طلبات API
   */
  static apiRateLimit = rateLimit({
    windowMs: 1 * 60 * 1000, // دقيقة واحدة
    max: 60, // حد أقصى 60 طلب API لكل دقيقة
    message: {
      error: 'تم تجاوز الحد المسموح من طلبات API. حاول مرة أخرى بعد دقيقة.',
      retryAfter: '1 minute'
    }
  });

  /**
   * تنظيف وتعقيم المدخلات
   */
  static sanitizeInput(input: string): string {
    if (typeof input !== 'string') {
      return '';
    }

    return input
      .trim()
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // إزالة script tags
      .replace(/javascript:/gi, '') // إزالة javascript: URLs
      .replace(/on\w+\s*=/gi, '') // إزالة event handlers
      .replace(/[<>'"]/g, (match) => { // تشفير الأحرف الخطيرة
        const entities: { [key: string]: string } = {
          '<': '&lt;',
          '>': '&gt;',
          '"': '&quot;',
          "'": '&#x27;'
        };
        return entities[match] || match;
      });
  }

  /**
   * التحقق من صحة عنوان البريد الإلكتروني
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email) && email.length <= 254;
  }

  /**
   * التحقق من قوة كلمة السر
   */
  static validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('كلمة السر يجب أن تكون 8 أحرف على الأقل');
    }

    if (password.length > 128) {
      errors.push('كلمة السر طويلة جداً');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('كلمة السر يجب أن تحتوي على حرف صغير واحد على الأقل');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('كلمة السر يجب أن تحتوي على حرف كبير واحد على الأقل');
    }

    if (!/\d/.test(password)) {
      errors.push('كلمة السر يجب أن تحتوي على رقم واحد على الأقل');
    }

    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('كلمة السر يجب أن تحتوي على رمز خاص واحد على الأقل');
    }

    // التحقق من كلمات السر الشائعة
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey'
    ];

    if (commonPasswords.includes(password.toLowerCase())) {
      errors.push('كلمة السر شائعة جداً، اختر كلمة سر أكثر تعقيداً');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * التحقق من صحة عنوان المحفظة
   */
  static validateWalletAddress(address: string): boolean {
    // التحقق من عنوان Ethereum/BSC
    const ethAddressRegex = /^0x[a-fA-F0-9]{40}$/;
    return ethAddressRegex.test(address);
  }

  /**
   * كشف محاولات SQL Injection
   */
  static detectSQLInjection(input: string): boolean {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i,
      /(--|\/\*|\*\/|;|'|"|`)/,
      /(\bOR\b|\bAND\b).*?[=<>]/i,
      /\b(WAITFOR|DELAY)\b/i
    ];

    return sqlPatterns.some(pattern => pattern.test(input));
  }

  /**
   * كشف محاولات XSS
   */
  static detectXSS(input: string): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/i,
      /on\w+\s*=/i,
      /<iframe\b[^>]*>/i,
      /<object\b[^>]*>/i,
      /<embed\b[^>]*>/i
    ];

    return xssPatterns.some(pattern => pattern.test(input));
  }
}

// ===== إعدادات الأمان للتطبيق =====

export class SecurityConfig {
  /**
   * إعدادات Helmet للحماية
   */
  static getHelmetConfig() {
    return helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
          fontSrc: ["'self'", "https://fonts.gstatic.com"],
          imgSrc: ["'self'", "data:", "https:"],
          scriptSrc: ["'self'"],
          connectSrc: ["'self'", "https://bsc-dataseed.binance.org"],
          frameSrc: ["'none'"],
          objectSrc: ["'none'"],
          upgradeInsecureRequests: []
        }
      },
      crossOriginEmbedderPolicy: false,
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      }
    });
  }

  /**
   * إعدادات CORS الآمنة
   */
  static getCORSConfig() {
    return {
      origin: process.env.NODE_ENV === 'production' 
        ? ['https://ikaros-p2p.com', 'https://www.ikaros-p2p.com']
        : ['http://localhost:3000', 'http://127.0.0.1:3000'],
      credentials: true,
      optionsSuccessStatus: 200,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    };
  }

  /**
   * إعدادات الجلسة الآمنة
   */
  static getSessionConfig() {
    return {
      secret: process.env.SESSION_SECRET || 'fallback-session-secret',
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000, // 24 ساعة
        sameSite: 'strict' as const
      }
    };
  }
}

// ===== مراقبة الأمان =====

export class SecurityMonitoring {
  /**
   * تسجيل محاولة أمنية مشبوهة
   */
  static async logSecurityEvent(event: {
    type: 'login_attempt' | 'sql_injection' | 'xss_attempt' | 'rate_limit' | 'invalid_token';
    ip: string;
    userAgent?: string;
    userId?: string;
    details?: any;
  }): Promise<void> {
    const logEntry = {
      timestamp: new Date().toISOString(),
      type: event.type,
      ip: event.ip,
      userAgent: event.userAgent,
      userId: event.userId,
      details: event.details,
      severity: this.getEventSeverity(event.type)
    };

    // في التطبيق الحقيقي، يجب حفظ هذا في قاعدة البيانات أو نظام مراقبة
    console.warn('Security Event:', logEntry);

    // إرسال تنبيه إذا كان الحدث خطير
    if (logEntry.severity === 'high') {
      await this.sendSecurityAlert(logEntry);
    }
  }

  /**
   * تحديد مستوى خطورة الحدث الأمني
   */
  private static getEventSeverity(eventType: string): 'low' | 'medium' | 'high' {
    const severityMap: { [key: string]: 'low' | 'medium' | 'high' } = {
      'login_attempt': 'low',
      'sql_injection': 'high',
      'xss_attempt': 'high',
      'rate_limit': 'medium',
      'invalid_token': 'medium'
    };

    return severityMap[eventType] || 'low';
  }

  /**
   * إرسال تنبيه أمني
   */
  private static async sendSecurityAlert(logEntry: any): Promise<void> {
    // في التطبيق الحقيقي، يجب إرسال تنبيه عبر البريد الإلكتروني أو Slack
    console.error('SECURITY ALERT:', logEntry);
  }

  /**
   * التحقق من IP مشبوه
   */
  static async checkSuspiciousIP(ip: string): Promise<boolean> {
    // في التطبيق الحقيقي، يجب التحقق من قواعد بيانات IPs المشبوهة
    const suspiciousIPs = [
      // قائمة IPs معروفة بأنها مشبوهة
    ];

    return suspiciousIPs.includes(ip);
  }
}

// تصدير جميع الفئات
export default {
  DataEncryption,
  SecureSessionManager,
  AttackProtection,
  SecurityConfig,
  SecurityMonitoring
};
