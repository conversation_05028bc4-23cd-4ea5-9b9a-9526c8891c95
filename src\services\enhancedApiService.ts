/**
 * خدمة APIs المحسنة للعقود الذكية
 * Enhanced APIs Service for Smart Contracts
 */

import { API_BASE_URL } from '@/constants';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

class EnhancedApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/enhanced-contracts`;
  }

  /**
   * طلب HTTP عام
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API Request Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // ==================== Oracle Manager APIs ====================

  /**
   * جلب أسعار العملات المستقرة
   */
  async getPrices(params: {
    network_id?: number;
    token_id?: number;
    currency?: string;
  } = {}): Promise<ApiResponse<any[]>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.request(`/oracle.php?action=prices&${queryParams.toString()}`);
  }

  /**
   * جلب أسعار صرف العملات المحلية
   */
  async getExchangeRates(params: {
    base_currency?: string;
    target_currency?: string;
  } = {}): Promise<ApiResponse<any[]>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.request(`/oracle.php?action=exchange-rates&${queryParams.toString()}`);
  }

  /**
   * تحديث سعر عملة مستقرة
   */
  async updatePrice(data: {
    network_id: number;
    token_id: number;
    currency: string;
    price: number;
    confidence_level?: number;
    updated_by?: string;
  }): Promise<ApiResponse> {
    return this.request('/oracle.php?action=update-price', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  /**
   * تحديث سعر صرف عملة محلية
   */
  async updateExchangeRate(data: {
    base_currency?: string;
    target_currency: string;
    exchange_rate: number;
    updated_by?: string;
    source?: string;
  }): Promise<ApiResponse> {
    return this.request('/oracle.php?action=update-exchange-rate', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // ==================== Reputation Manager APIs ====================

  /**
   * جلب سمعة المستخدم
   */
  async getUserReputation(params: {
    user_id: number;
    network_id?: number;
  }): Promise<ApiResponse<any>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.request(`/reputation.php?action=user-reputation&${queryParams.toString()}`);
  }

  /**
   * جلب تقييمات المستخدم
   */
  async getUserRatings(params: {
    user_id: number;
    network_id?: number;
    type?: 'received' | 'given';
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<PaginatedResponse<any>>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.request(`/reputation.php?action=user-ratings&${queryParams.toString()}`);
  }

  /**
   * جلب إحصائيات الشبكة
   */
  async getNetworkStats(networkId: number = 1): Promise<ApiResponse<any>> {
    return this.request(`/reputation.php?action=network-stats&network_id=${networkId}`);
  }

  /**
   * جلب أفضل المستخدمين
   */
  async getTopUsers(params: {
    network_id?: number;
    sort_by?: 'reputation_score' | 'total_trades' | 'average_rating';
    limit?: number;
  } = {}): Promise<ApiResponse<any[]>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.request(`/reputation.php?action=top-users&${queryParams.toString()}`);
  }

  /**
   * تقييم مستخدم
   */
  async rateUser(data: {
    trade_id: number;
    rater_id: number;
    rated_id: number;
    network_id?: number;
    rating: number;
    comment?: string;
    trade_role: 'seller' | 'buyer';
  }): Promise<ApiResponse> {
    return this.request('/reputation.php?action=rate-user', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // ==================== Admin Manager APIs ====================

  /**
   * جلب إعدادات النظام
   */
  async getSystemSettings(): Promise<ApiResponse<any>> {
    return this.request('/admin.php?action=system-settings');
  }

  /**
   * جلب ملفات المشرفين
   */
  async getAdminProfiles(): Promise<ApiResponse<any[]>> {
    return this.request('/admin.php?action=admin-profiles');
  }

  /**
   * جلب قرارات حل النزاعات
   */
  async getDisputeResolutions(params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<any>>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.request(`/admin.php?action=dispute-resolutions&${queryParams.toString()}`);
  }

  /**
   * جلب المستخدمين في القائمة السوداء
   */
  async getBlacklistedUsers(): Promise<ApiResponse<any[]>> {
    return this.request('/admin.php?action=blacklisted-users');
  }

  /**
   * جلب إحصائيات الإدارة
   */
  async getAdminStats(): Promise<ApiResponse<any>> {
    return this.request('/admin.php?action=admin-stats');
  }

  /**
   * جلب سجل النشاط
   */
  async getActivityLogs(params: {
    page?: number;
    limit?: number;
    admin_id?: number;
    action_type?: string;
  } = {}): Promise<ApiResponse<PaginatedResponse<any>>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.request(`/admin.php?action=activity-logs&${queryParams.toString()}`);
  }

  // ==================== Escrow Integrator APIs ====================

  /**
   * جلب حالة التكامل
   */
  async getIntegrationStatus(params: {
    trade_id?: number;
    network_id?: number;
  } = {}): Promise<ApiResponse<any>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.request(`/integrator.php?action=integration-status&${queryParams.toString()}`);
  }

  /**
   * جلب سجلات التكامل
   */
  async getIntegrationLogs(params: {
    page?: number;
    limit?: number;
    trade_id?: number;
    status?: string;
    integration_type?: string;
  } = {}): Promise<ApiResponse<PaginatedResponse<any>>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.request(`/integrator.php?action=integration-logs&${queryParams.toString()}`);
  }

  /**
   * جلب صحة النظام
   */
  async getSystemHealth(): Promise<ApiResponse<any>> {
    return this.request('/integrator.php?action=system-health');
  }

  /**
   * إنشاء صفقة متكاملة
   */
  async createIntegratedTrade(data: {
    trade_data: any;
    network_id?: number;
    enable_reputation_update?: boolean;
    enable_price_validation?: boolean;
  }): Promise<ApiResponse<any>> {
    return this.request('/integrator.php?action=create-integrated-trade', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // ==================== Network APIs ====================

  /**
   * جلب إحصائيات الشبكة
   */
  async getNetworkStats(): Promise<ApiResponse<any>> {
    return this.request('/networks.php?action=stats');
  }

  /**
   * جلب إحصائيات العقود
   */
  async getContractStats(): Promise<ApiResponse<any[]>> {
    return this.request('/networks.php?action=contracts');
  }
}

// إنشاء مثيل واحد من الخدمة
export const enhancedApiService = new EnhancedApiService();
