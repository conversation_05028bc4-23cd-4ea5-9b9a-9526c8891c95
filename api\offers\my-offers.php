<?php
/**
 * API endpoint لجلب عروض المستخدم
 * User Offers API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = new Database();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // جلب عروض المستخدم
        $userId = $_GET['user_id'] ?? null;
        
        if (!$userId) {
            throw new Exception('معرف المستخدم مطلوب');
        }
        
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        
        $status = $_GET['status'] ?? 'all'; // active, inactive, all
        $offerType = $_GET['type'] ?? 'all'; // buy, sell, all
        
        // بناء الاستعلام
        $whereConditions = ['user_id = ?'];
        $params = [$userId];
        
        if ($status === 'active') {
            $whereConditions[] = 'is_active = 1';
        } elseif ($status === 'inactive') {
            $whereConditions[] = 'is_active = 0';
        }
        
        if ($offerType !== 'all') {
            $whereConditions[] = 'offer_type = ?';
            $params[] = $offerType;
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // الحصول على العدد الإجمالي
        $countStmt = $connection->prepare("
            SELECT COUNT(*) as total
            FROM offers
            WHERE $whereClause
        ");
        $countStmt->execute($params);
        $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // جلب العروض
        $stmt = $connection->prepare("
            SELECT
                id,
                offer_type,
                amount,
                min_amount,
                max_amount,
                price,
                currency,
                'USDT' as stablecoin,
                payment_methods,
                terms,
                auto_reply,
                1 as is_active,
                0 as views_count,
                time_limit,
                created_at,
                updated_at,
                expires_at
            FROM offers
            WHERE $whereClause
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        $offers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تنسيق البيانات
        foreach ($offers as &$offer) {
            $offer['payment_methods'] = json_decode($offer['payment_methods'], true) ?: [];
            $offer['is_active'] = (bool)$offer['is_active'];
        }
        
        echo json_encode([
            'success' => true,
            'data' => $offers,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => intval($totalCount),
                'total_pages' => ceil($totalCount / $limit)
            ]
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // تحديث عرض
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $offerId = $input['offer_id'] ?? null;
        $userId = $input['user_id'] ?? null;
        
        if (!$offerId || !$userId) {
            throw new Exception('معرف العرض ومعرف المستخدم مطلوبان');
        }
        
        // التحقق من ملكية العرض
        $stmt = $connection->prepare("SELECT id FROM offers WHERE id = ? AND user_id = ?");
        $stmt->execute([$offerId, $userId]);
        
        if (!$stmt->fetch()) {
            throw new Exception('العرض غير موجود أو غير مملوك لك');
        }
        
        // تحديث البيانات المسموحة فقط
        $allowedFields = ['amount', 'min_amount', 'max_amount', 'price', 'payment_methods', 'terms', 'auto_reply', 'is_active'];
        $updateFields = [];
        $updateValues = [];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                if ($field === 'payment_methods') {
                    $updateFields[] = "$field = ?";
                    $updateValues[] = json_encode($input[$field]);
                } else {
                    $updateFields[] = "$field = ?";
                    $updateValues[] = $input[$field];
                }
            }
        }
        
        if (empty($updateFields)) {
            throw new Exception('لا توجد بيانات للتحديث');
        }
        
        $updateValues[] = $offerId;
        
        $stmt = $connection->prepare("
            UPDATE offers 
            SET " . implode(', ', $updateFields) . ", updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        ");
        
        $stmt->execute($updateValues);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث العرض بنجاح'
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        // حذف عرض
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $offerId = $input['offer_id'] ?? null;
        $userId = $input['user_id'] ?? null;
        
        if (!$offerId || !$userId) {
            throw new Exception('معرف العرض ومعرف المستخدم مطلوبان');
        }
        
        // التحقق من ملكية العرض وعدم وجود صفقات نشطة
        $stmt = $connection->prepare("
            SELECT o.id 
            FROM offers o
            LEFT JOIN trades t ON o.id = t.offer_id AND t.status IN ('created', 'joined', 'payment_sent')
            WHERE o.id = ? AND o.user_id = ? AND t.id IS NULL
        ");
        $stmt->execute([$offerId, $userId]);
        
        if (!$stmt->fetch()) {
            throw new Exception('لا يمكن حذف العرض. قد يكون غير موجود أو لديه صفقات نشطة');
        }
        
        // حذف العرض (soft delete)
        $stmt = $connection->prepare("
            UPDATE offers 
            SET is_active = 0, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        ");
        
        $stmt->execute([$offerId]);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف العرض بنجاح'
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    error_log('Database error in offers/my-offers.php: ' . $e->getMessage());
}
?>
