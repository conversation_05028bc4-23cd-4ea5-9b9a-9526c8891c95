<?php
/**
 * إعدادات قاعدة البيانات
 * Database Configuration
 */

class Database {
    private $host = '127.0.0.1';
    private $port = '3306';
    private $dbname = 'ikaros_p2p';
    private $username = 'root';
    private $password = '';
    private $connection;
    
    public function __construct() {
        $this->connect();
    }
    
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};port={$this->port};dbname={$this->dbname};charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch (PDOException $e) {
            // في حالة فشل الاتصال، إرجاع خطأ مفصل
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("فشل في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }
    
    public function getConnection() {
        if (!$this->connection) {
            $this->connect();
        }
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database query failed: " . $e->getMessage());
            throw new Exception("فشل في تنفيذ الاستعلام: " . $e->getMessage());
        }
    }
    
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($data);
            return $this->connection->lastInsertId();
        } catch (PDOException $e) {
            error_log("Database insert failed: " . $e->getMessage());
            throw new Exception("فشل في إدراج البيانات: " . $e->getMessage());
        }
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $key) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute(array_merge($data, $whereParams));
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Database update failed: " . $e->getMessage());
            throw new Exception("فشل في تحديث البيانات: " . $e->getMessage());
        }
    }
    
    public function delete($table, $where, $whereParams = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($whereParams);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Database delete failed: " . $e->getMessage());
            throw new Exception("فشل في حذف البيانات: " . $e->getMessage());
        }
    }
    
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    public function commit() {
        return $this->connection->commit();
    }
    
    public function rollback() {
        return $this->connection->rollback();
    }
    
    public function lastInsertId() {
        return $this->connection->lastInsertId();
    }
    
    public function tableExists($tableName) {
        try {
            $sql = "SHOW TABLES LIKE :tableName";
            $stmt = $this->connection->prepare($sql);
            $stmt->execute(['tableName' => $tableName]);
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    public function createTableIfNotExists($tableName, $schema) {
        if (!$this->tableExists($tableName)) {
            try {
                $this->connection->exec($schema);
                return true;
            } catch (PDOException $e) {
                error_log("Failed to create table {$tableName}: " . $e->getMessage());
                return false;
            }
        }
        return true;
    }
    
    public function __destruct() {
        $this->connection = null;
    }
}

// دالة مساعدة للحصول على اتصال قاعدة البيانات
function getDbConnection() {
    static $db = null;
    if ($db === null) {
        $db = new Database();
    }
    return $db;
}

// إنشاء الجداول المطلوبة إذا لم تكن موجودة
function initializeTables() {
    $db = getDbConnection();
    
    // جدول أحداث العقود
    $contractEventsSchema = "
        CREATE TABLE IF NOT EXISTS contract_events (
            id INT AUTO_INCREMENT PRIMARY KEY,
            contract_address VARCHAR(42) NOT NULL,
            event_type VARCHAR(100) NOT NULL,
            event_data TEXT,
            transaction_hash VARCHAR(66),
            block_number BIGINT,
            gas_used BIGINT,
            is_read TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_contract_address (contract_address),
            INDEX idx_event_type (event_type),
            INDEX idx_created_at (created_at),
            INDEX idx_is_read (is_read)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $db->createTableIfNotExists('contract_events', $contractEventsSchema);
}

// تهيئة الجداول عند تحميل الملف
try {
    initializeTables();
} catch (Exception $e) {
    error_log("Failed to initialize tables: " . $e->getMessage());
}
?>
