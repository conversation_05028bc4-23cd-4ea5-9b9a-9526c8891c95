'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { adminWalletService, AdminWalletInfo } from '@/services/adminWalletService';
import { apiService } from '@/services/apiService';

interface AdminUser {
  id: number;
  admin_id: number;
  username: string;
  email: string;
  fullName: string;
  walletAddress: string;
  isAdmin: boolean;
  adminRole: string;
  permissions: string[];
  sessionTimeout: number;
  loginMethod: string;
  lastLogin: string;
  expiresAt: string;
}

interface AdminContextType {
  // حالة المصادقة
  isAuthenticated: boolean;
  adminUser: AdminUser | null;
  isLoading: boolean;
  
  // حالة المحفظة
  isWalletConnected: boolean;
  walletInfo: AdminWalletInfo | null;
  
  // دوال المصادقة
  login: (username: string, password: string, walletAddress?: string) => Promise<void>;
  logout: () => void;
  
  // دوال المحفظة
  connectWallet: () => Promise<AdminWalletInfo>;
  disconnectWallet: () => void;
  
  // دوال المساعدة
  checkAuthStatus: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  getSessionTimeLeft: () => string;
}

const AdminContext = createContext<AdminContextType | undefined>(undefined);

interface AdminProviderProps {
  children: ReactNode;
}

export function AdminProvider({ children }: AdminProviderProps) {
  // حالة المصادقة
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // حالة المحفظة
  const [isWalletConnected, setIsWalletConnected] = useState(false);
  const [walletInfo, setWalletInfo] = useState<AdminWalletInfo | null>(null);

  // التحقق من حالة المصادقة عند بدء التطبيق
  useEffect(() => {
    checkAuthStatus();
  }, []);

  // التحقق من حالة المحفظة عند بدء التطبيق
  useEffect(() => {
    checkWalletStatus();
  }, []);

  // التحقق من حالة المصادقة
  const checkAuthStatus = async () => {
    setIsLoading(true);
    try {
      // التحقق من الجلسة المحفوظة
      const savedSession = sessionStorage.getItem('admin_session');
      if (!savedSession) {
        setIsAuthenticated(false);
        setAdminUser(null);
        return;
      }

      const sessionData = JSON.parse(savedSession);
      
      // التحقق من انتهاء الجلسة
      if (Date.now() > new Date(sessionData.expiresAt).getTime()) {
        logout();
        return;
      }

      // التحقق من صحة الجلسة مع الخادم
      const response = await apiService.admin.validateSession();
      if (response.success && response.adminData) {
        setAdminUser(response.adminData);
        setIsAuthenticated(true);
      } else {
        logout();
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      logout();
    } finally {
      setIsLoading(false);
    }
  };

  // التحقق من حالة المحفظة
  const checkWalletStatus = async () => {
    try {
      const connected = adminWalletService.isConnected();
      setIsWalletConnected(connected);
      
      if (connected) {
        const info = await adminWalletService.getCurrentWalletInfo();
        setWalletInfo(info);
      }
    } catch (error) {
      console.error('Error checking wallet status:', error);
      setIsWalletConnected(false);
      setWalletInfo(null);
    }
  };

  // تسجيل الدخول
  const login = async (username: string, password: string, walletAddress?: string) => {
    try {
      const response = await apiService.admin.login({
        username,
        password,
        walletAddress
      });

      if (response.success && response.adminData) {
        setAdminUser(response.adminData);
        setIsAuthenticated(true);

        // حفظ الجلسة
        const sessionData = {
          adminData: response.adminData,
          sessionToken: response.sessionToken,
          expiresAt: response.expiresAt,
          permissions: response.permissions
        };
        
        sessionStorage.setItem('admin_session', JSON.stringify(sessionData));
        sessionStorage.setItem('admin_token', response.sessionToken);
      } else {
        throw new Error(response.message || 'فشل في تسجيل الدخول');
      }
    } catch (error: any) {
      console.error('Admin login error:', error);
      throw error;
    }
  };

  // تسجيل الخروج
  const logout = () => {
    try {
      // إرسال طلب تسجيل الخروج للخادم
      apiService.admin.logout().catch(console.error);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // مسح البيانات المحلية
      setIsAuthenticated(false);
      setAdminUser(null);
      sessionStorage.removeItem('admin_session');
      sessionStorage.removeItem('admin_token');
      
      // قطع اتصال المحفظة
      disconnectWallet();
    }
  };

  // الاتصال بالمحفظة
  const connectWallet = async (): Promise<AdminWalletInfo> => {
    try {
      const info = await adminWalletService.connectAdminWallet();
      setWalletInfo(info);
      setIsWalletConnected(true);
      return info;
    } catch (error: any) {
      console.error('Wallet connection error:', error);
      throw error;
    }
  };

  // قطع اتصال المحفظة
  const disconnectWallet = () => {
    adminWalletService.disconnectAdminWallet();
    setWalletInfo(null);
    setIsWalletConnected(false);
  };

  // التحقق من الصلاحيات
  const hasPermission = (permission: string): boolean => {
    if (!adminUser || !adminUser.permissions) return false;
    
    // Super admin له جميع الصلاحيات
    if (adminUser.adminRole === 'super_admin') return true;
    
    return adminUser.permissions.includes(permission);
  };

  // الحصول على الوقت المتبقي للجلسة
  const getSessionTimeLeft = (): string => {
    if (!adminUser?.expiresAt) return '';

    const expiryTime = new Date(adminUser.expiresAt).getTime();
    const now = Date.now();
    const timeLeft = expiryTime - now;

    if (timeLeft <= 0) return 'منتهية الصلاحية';

    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}س ${minutes}د`;
    } else {
      return `${minutes}د`;
    }
  };

  const value: AdminContextType = {
    // حالة المصادقة
    isAuthenticated,
    adminUser,
    isLoading,
    
    // حالة المحفظة
    isWalletConnected,
    walletInfo,
    
    // دوال المصادقة
    login,
    logout,
    
    // دوال المحفظة
    connectWallet,
    disconnectWallet,
    
    // دوال المساعدة
    checkAuthStatus,
    hasPermission,
    getSessionTimeLeft,
  };

  return (
    <AdminContext.Provider value={value}>
      {children}
    </AdminContext.Provider>
  );
}

export function useAdmin() {
  const context = useContext(AdminContext);
  if (context === undefined) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
}

export default AdminContext;
