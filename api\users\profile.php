<?php
/**
 * API endpoint لإدارة الملف الشخصي
 * Profile Management API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $userId = $_GET['user_id'] ?? null;
        $getAllUsers = $_GET['all'] ?? false;
        $current = $_GET['current'] ?? false;

        if ($getAllUsers) {
            // جلب جميع المستخدمين (للإدارة)
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(100, max(10, intval($_GET['limit'] ?? 50)));
            $offset = ($page - 1) * $limit;

            $stmt = $connection->prepare("
                SELECT id, wallet_address, username, email, full_name, phone,
                       is_verified, is_admin, rating, total_trades, completed_trades,
                       total_volume, created_at, last_login, country_code
                FROM users
                WHERE is_active = 1
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            ");

            $stmt->execute([$limit, $offset]);
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // الحصول على العدد الإجمالي
            $countStmt = $connection->prepare("SELECT COUNT(*) as total FROM users WHERE is_active = 1");
            $countStmt->execute();
            $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

            echo json_encode([
                'success' => true,
                'data' => $users,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => intval($totalCount),
                    'total_pages' => ceil($totalCount / $limit)
                ]
            ]);

        } elseif ($current) {
            // جلب المستخدم الحالي (مؤقتاً نرجع أول مستخدم)
            $stmt = $connection->prepare("
                SELECT id, wallet_address, username, email, full_name, phone,
                       is_verified, is_admin, rating, total_trades, completed_trades,
                       total_volume, created_at, last_login, country_code, bio,
                       telegram_username, whatsapp_number, profile_image
                FROM users
                WHERE is_active = 1
                ORDER BY created_at ASC
                LIMIT 1
            ");

            $stmt->execute();
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                throw new Exception('لا يوجد مستخدمون في النظام');
            }

            echo json_encode([
                'success' => true,
                'data' => $user
            ]);

        } else {
            // جلب مستخدم محدد
            if (!$userId) {
                throw new Exception('معرف المستخدم مطلوب');
            }

            $stmt = $connection->prepare("
                SELECT id, wallet_address, username, email, full_name, phone,
                       is_verified, is_admin, rating, total_trades, completed_trades,
                       total_volume, created_at, last_login, country_code, bio,
                       telegram_username, whatsapp_number, profile_image
                FROM users
                WHERE id = ? AND is_active = 1
            ");

            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                throw new Exception('المستخدم غير موجود');
            }

            echo json_encode([
                'success' => true,
                'data' => $user
            ]);
        }

    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        // تحديث بيانات المستخدم
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }

        $userId = $input['user_id'] ?? null;

        if (!$userId) {
            throw new Exception('معرف المستخدم مطلوب');
        }

        // التحقق من وجود المستخدم
        $stmt = $connection->prepare("SELECT id FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$userId]);

        if (!$stmt->fetch()) {
            throw new Exception('المستخدم غير موجود');
        }

        // تحديث البيانات المسموحة فقط
        $allowedFields = ['full_name', 'phone', 'bio', 'telegram_username', 'whatsapp_number', 'country_code'];
        $updateFields = [];
        $updateValues = [];

        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updateFields[] = "$field = ?";
                $updateValues[] = $input[$field];
            }
        }

        if (empty($updateFields)) {
            throw new Exception('لا توجد بيانات للتحديث');
        }

        $updateValues[] = $userId;

        $stmt = $connection->prepare("
            UPDATE users
            SET " . implode(', ', $updateFields) . ", updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");

        $stmt->execute($updateValues);

        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث الملف الشخصي بنجاح'
        ]);

    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);

    error_log('Database error in profile.php: ' . $e->getMessage());
}
?>
