/**
 * نظام إدارة التخزين المؤقت المحسن
 * Enhanced Cache Management System
 */

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  tags?: string[];
}

interface CacheConfig {
  maxSize: number;
  defaultTTL: number;
  cleanupInterval: number;
  enableCompression: boolean;
  enablePersistence: boolean;
}

class CacheManager {
  private cache = new Map<string, CacheItem<any>>();
  private config: CacheConfig;
  private cleanupTimer?: NodeJS.Timeout;
  private stats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    evictions: 0
  };

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: 1000,
      defaultTTL: 5 * 60 * 1000, // 5 دقائق
      cleanupInterval: 60 * 1000, // دقيقة واحدة
      enableCompression: false,
      enablePersistence: false,
      ...config
    };

    this.startCleanupTimer();
    this.loadFromPersistence();
  }

  /**
   * حفظ البيانات في الكاش
   */
  set<T>(key: string, data: T, ttl?: number, tags?: string[]): void {
    const now = Date.now();
    const itemTTL = ttl || this.config.defaultTTL;

    // تنظيف الكاش إذا وصل للحد الأقصى
    if (this.cache.size >= this.config.maxSize) {
      this.evictLeastUsed();
    }

    const item: CacheItem<T> = {
      data: this.config.enableCompression ? this.compress(data) : data,
      timestamp: now,
      ttl: itemTTL,
      accessCount: 0,
      lastAccessed: now,
      tags
    };

    this.cache.set(key, item);
    this.stats.sets++;

    if (this.config.enablePersistence) {
      this.saveToPersistence();
    }
  }

  /**
   * استرجاع البيانات من الكاش
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      this.stats.misses++;
      return null;
    }

    const now = Date.now();
    
    // فحص انتهاء الصلاحية
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    // تحديث إحصائيات الوصول
    item.accessCount++;
    item.lastAccessed = now;
    this.stats.hits++;

    return this.config.enableCompression ? this.decompress(item.data) : item.data;
  }

  /**
   * حذف عنصر من الكاش
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.stats.deletes++;
    }
    return deleted;
  }

  /**
   * حذف عناصر متعددة بناءً على العلامات
   */
  deleteByTag(tag: string): number {
    let deletedCount = 0;
    
    for (const [key, item] of this.cache.entries()) {
      if (item.tags && item.tags.includes(tag)) {
        this.cache.delete(key);
        deletedCount++;
      }
    }
    
    this.stats.deletes += deletedCount;
    return deletedCount;
  }

  /**
   * حذف عناصر متعددة بناءً على نمط المفتاح
   */
  deleteByPattern(pattern: RegExp): number {
    let deletedCount = 0;
    
    for (const key of this.cache.keys()) {
      if (pattern.test(key)) {
        this.cache.delete(key);
        deletedCount++;
      }
    }
    
    this.stats.deletes += deletedCount;
    return deletedCount;
  }

  /**
   * مسح الكاش بالكامل
   */
  clear(): void {
    this.cache.clear();
    this.resetStats();
  }

  /**
   * فحص وجود مفتاح في الكاش
   */
  has(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;
    
    const now = Date.now();
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * الحصول على حجم الكاش
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * الحصول على إحصائيات الكاش
   */
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0 
      ? (this.stats.hits / (this.stats.hits + this.stats.misses)) * 100 
      : 0;

    return {
      ...this.stats,
      hitRate: Math.round(hitRate * 100) / 100,
      size: this.cache.size,
      maxSize: this.config.maxSize,
      memoryUsage: this.getMemoryUsage()
    };
  }

  /**
   * إزالة العناصر الأقل استخداماً
   */
  private evictLeastUsed(): void {
    if (this.cache.size === 0) return;

    // ترتيب العناصر حسب عدد الوصول والوقت الأخير للوصول
    const entries = Array.from(this.cache.entries()).sort((a, b) => {
      const [, itemA] = a;
      const [, itemB] = b;
      
      // أولوية للعناصر الأقل استخداماً
      if (itemA.accessCount !== itemB.accessCount) {
        return itemA.accessCount - itemB.accessCount;
      }
      
      // ثم الأقدم في الوصول
      return itemA.lastAccessed - itemB.lastAccessed;
    });

    // حذف 10% من العناصر الأقل استخداماً
    const toEvict = Math.max(1, Math.floor(this.cache.size * 0.1));
    
    for (let i = 0; i < toEvict && i < entries.length; i++) {
      const [key] = entries[i];
      this.cache.delete(key);
      this.stats.evictions++;
    }
  }

  /**
   * تنظيف العناصر المنتهية الصلاحية
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => {
      this.cache.delete(key);
      this.stats.evictions++;
    });
  }

  /**
   * بدء مؤقت التنظيف
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * إيقاف مؤقت التنظيف
   */
  stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
  }

  /**
   * ضغط البيانات (مبسط)
   */
  private compress<T>(data: T): string {
    try {
      return JSON.stringify(data);
    } catch {
      return String(data);
    }
  }

  /**
   * إلغاء ضغط البيانات
   */
  private decompress<T>(compressedData: string): T {
    try {
      return JSON.parse(compressedData);
    } catch {
      return compressedData as unknown as T;
    }
  }

  /**
   * حساب استخدام الذاكرة التقريبي
   */
  private getMemoryUsage(): number {
    let totalSize = 0;
    
    for (const [key, item] of this.cache.entries()) {
      totalSize += key.length * 2; // UTF-16
      totalSize += JSON.stringify(item).length * 2;
    }
    
    return totalSize;
  }

  /**
   * حفظ الكاش في التخزين المحلي
   */
  private saveToPersistence(): void {
    if (!this.config.enablePersistence || typeof localStorage === 'undefined') {
      return;
    }

    try {
      const cacheData = Array.from(this.cache.entries());
      localStorage.setItem('cache_data', JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Failed to save cache to localStorage:', error);
    }
  }

  /**
   * تحميل الكاش من التخزين المحلي
   */
  private loadFromPersistence(): void {
    if (!this.config.enablePersistence || typeof localStorage === 'undefined') {
      return;
    }

    try {
      const cacheData = localStorage.getItem('cache_data');
      if (cacheData) {
        const entries = JSON.parse(cacheData);
        this.cache = new Map(entries);
        
        // تنظيف العناصر المنتهية الصلاحية
        this.cleanup();
      }
    } catch (error) {
      console.warn('Failed to load cache from localStorage:', error);
    }
  }

  /**
   * إعادة تعيين الإحصائيات
   */
  private resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0
    };
  }

  /**
   * تدمير الكاش وتنظيف الموارد
   */
  destroy(): void {
    this.stopCleanupTimer();
    this.clear();
  }
}

// إنشاء instances مختلفة للاستخدامات المختلفة
export const apiCache = new CacheManager({
  maxSize: 500,
  defaultTTL: 5 * 60 * 1000, // 5 دقائق
  enablePersistence: true
});

export const userDataCache = new CacheManager({
  maxSize: 100,
  defaultTTL: 10 * 60 * 1000, // 10 دقائق
  enablePersistence: true
});

export const staticDataCache = new CacheManager({
  maxSize: 200,
  defaultTTL: 60 * 60 * 1000, // ساعة واحدة
  enablePersistence: true
});

export const temporaryCache = new CacheManager({
  maxSize: 50,
  defaultTTL: 30 * 1000, // 30 ثانية
  enablePersistence: false
});

export default CacheManager;
