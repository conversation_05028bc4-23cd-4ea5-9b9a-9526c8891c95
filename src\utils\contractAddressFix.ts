/**
 * إصلاح عنوان العقد الذكي
 * Contract Address Fix Utility
 */

import { ethers } from 'ethers';

/**
 * إصلاح checksum العنوان
 */
export function fixContractAddress(address: string): string {
  try {
    // تحويل إلى أحرف صغيرة أولاً ثم تطبيق checksum
    const lowercaseAddress = address.toLowerCase();
    const checksumAddress = ethers.getAddress(lowercaseAddress);
    return checksumAddress;
  } catch (error) {
    console.error('خطأ في إصلاح العنوان:', error);
    throw new Error(`عنوان غير صالح: ${address}`);
  }
}

/**
 * التحقق من صحة العنوان
 */
export function isValidContractAddress(address: string): boolean {
  try {
    if (!address || !address.startsWith('0x') || address.length !== 42) {
      return false;
    }
    
    const lowercaseAddress = address.toLowerCase();
    ethers.getAddress(lowercaseAddress);
    return true;
  } catch {
    return false;
  }
}

/**
 * الحصول على عنوان العقد المصحح من متغيرات البيئة
 * يستخدم العقد المحسن الجديد (Core Escrow) بدلاً من العقد القديم
 */
export function getFixedContractAddress(): string | null {
  // استخدام العقد المحسن الجديد
  const contractAddress = process.env.NEXT_PUBLIC_CORE_ESCROW_ADDRESS || process.env.NEXT_PUBLIC_ESCROW_CONTRACT_ADDRESS;

  if (!contractAddress) {
    console.error('❌ عنوان العقد غير محدد في متغيرات البيئة');
    return null;
  }

  try {
    return fixContractAddress(contractAddress);
  } catch (error) {
    console.error('❌ فشل في إصلاح عنوان العقد:', error);
    return null;
  }
}

/**
 * الحصول على عنوان عقد محدد من العقود المحسنة
 */
export function getEnhancedContractAddress(contractType: 'core' | 'reputation' | 'oracle' | 'admin' | 'integrator'): string | null {
  const addressMap = {
    core: process.env.NEXT_PUBLIC_CORE_ESCROW_ADDRESS,
    reputation: process.env.NEXT_PUBLIC_REPUTATION_MANAGER_ADDRESS,
    oracle: process.env.NEXT_PUBLIC_ORACLE_MANAGER_ADDRESS,
    admin: process.env.NEXT_PUBLIC_ADMIN_MANAGER_ADDRESS,
    integrator: process.env.NEXT_PUBLIC_ESCROW_INTEGRATOR_ADDRESS
  };

  const contractAddress = addressMap[contractType];

  if (!contractAddress) {
    console.error(`❌ عنوان العقد ${contractType} غير محدد في متغيرات البيئة`);
    return null;
  }

  try {
    return fixContractAddress(contractAddress);
  } catch (error) {
    console.error(`❌ فشل في إصلاح عنوان العقد ${contractType}:`, error);
    return null;
  }
}

/**
 * اختبار العنوان وطباعة النتائج
 */
export function testContractAddress(): void {
  console.log('🧪 اختبار عنوان العقد الذكي...');
  
  const originalAddress = process.env.NEXT_PUBLIC_ESCROW_CONTRACT_ADDRESS;
  
  if (!originalAddress) {
    console.error('❌ عنوان العقد غير محدد');
    return;
  }
  
  console.log(`📍 العنوان الأصلي: ${originalAddress}`);
  
  try {
    const isValid = isValidContractAddress(originalAddress);
    console.log(`✅ صحة العنوان: ${isValid ? 'صالح' : 'غير صالح'}`);
    
    if (isValid) {
      const fixedAddress = fixContractAddress(originalAddress);
      console.log(`✅ العنوان المصحح: ${fixedAddress}`);
      
      // مقارنة العناوين
      if (originalAddress !== fixedAddress) {
        console.log('⚠️ العنوان الأصلي يحتاج إصلاح checksum');
        console.log(`📝 استخدم هذا العنوان: ${fixedAddress}`);
      } else {
        console.log('✅ العنوان الأصلي صحيح');
      }
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار العنوان:', error);
  }
}

// تشغيل الاختبار تلقائياً في وضع التطوير
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // تأخير التشغيل قليلاً للسماح بتحميل متغيرات البيئة
  setTimeout(() => {
    testContractAddress();
  }, 1000);
}

// تصدير للاستخدام في وحدة التحكم
if (typeof window !== 'undefined') {
  (window as any).contractAddressFix = {
    fixContractAddress,
    isValidContractAddress,
    getFixedContractAddress,
    testContractAddress
  };
}
