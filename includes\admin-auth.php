<?php
/**
 * ملف المصادقة للأدمن
 * Admin Authentication File
 */

/**
 * التحقق من جلسة الأدمن
 * Validate admin session
 */
function validateAdminSession() {
    // التحقق من وجود جلسة
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // التحقق من وجود معرف المدير في الجلسة
    if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_logged_in'])) {
        // للتطوير: السماح بالوصول للمدير الافتراضي فقط
        if (isDevelopmentMode()) {
            return getDefaultAdminId();
        }

        http_response_code(401);
        echo json_encode([
            'success' => false,
            'error' => 'غير مصرح بالوصول',
            'error_en' => 'Unauthorized access'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // التحقق من صحة الجلسة في قاعدة البيانات
    try {
        $db = DatabaseManager::getInstance()->getDatabase();
        $connection = $db->getConnection();

        $stmt = $connection->prepare("
            SELECT au.id, au.user_id, au.is_active, u.is_active as user_active
            FROM admin_users au
            JOIN users u ON au.user_id = u.id
            WHERE au.id = ? AND au.is_active = 1 AND u.is_active = 1
        ");
        $stmt->execute([$_SESSION['admin_id']]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$admin) {
            // جلسة غير صحيحة
            session_unset();
            session_destroy();

            http_response_code(401);
            echo json_encode([
                'success' => false,
                'error' => 'جلسة غير صحيحة',
                'error_en' => 'Invalid session'
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }

        // تحديث آخر نشاط
        $updateStmt = $connection->prepare("UPDATE admin_users SET last_activity = NOW() WHERE id = ?");
        $updateStmt->execute([$admin['id']]);

        return $admin['id'];

    } catch (Exception $e) {
        error_log('Error validating admin session: ' . $e->getMessage());

        // في حالة خطأ قاعدة البيانات، للتطوير فقط
        if (isDevelopmentMode()) {
            return getDefaultAdminId();
        }

        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'خطأ في النظام',
            'error_en' => 'System error'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

/**
 * التحقق من وضع التطوير
 * Check if in development mode
 */
function isDevelopmentMode() {
    return (
        (isset($_ENV['APP_ENV']) && $_ENV['APP_ENV'] === 'development') ||
        (isset($_SERVER['SERVER_NAME']) && in_array($_SERVER['SERVER_NAME'], ['localhost', '127.0.0.1'])) ||
        (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE === true)
    );
}

/**
 * الحصول على معرف المدير الافتراضي
 * Get default admin ID for development
 */
function getDefaultAdminId() {
    try {
        $db = DatabaseManager::getInstance()->getDatabase();
        $connection = $db->getConnection();

        $stmt = $connection->prepare("
            SELECT au.id
            FROM admin_users au
            JOIN users u ON au.user_id = u.id
            WHERE u.username = 'admin' AND au.admin_role = 'super_admin' AND au.is_active = 1
            LIMIT 1
        ");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result ? $result['id'] : 1;
    } catch (Exception $e) {
        error_log('Error getting default admin ID: ' . $e->getMessage());
        return 1; // fallback
    }
}

/**
 * التحقق من صلاحيات الأدمن
 * Check admin permissions
 */
function checkAdminPermission($permission = 'admin') {
    $admin_id = validateAdminSession();

    try {
        $db = DatabaseManager::getInstance()->getDatabase();
        $connection = $db->getConnection();

        $stmt = $connection->prepare("
            SELECT admin_role, permissions,
                   can_manage_users, can_manage_trades, can_resolve_disputes,
                   can_manage_contracts, can_view_analytics, can_manage_settings,
                   can_emergency_actions, can_manage_admins
            FROM admin_users
            WHERE id = ? AND is_active = 1
        ");
        $stmt->execute([$admin_id]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$admin) {
            return false;
        }

        // Super admin has all permissions
        if ($admin['admin_role'] === 'super_admin') {
            return true;
        }

        // Check specific permissions
        $permissions = json_decode($admin['permissions'], true) ?: [];
        if (in_array($permission, $permissions)) {
            return true;
        }

        // Check boolean permissions
        $permissionMap = [
            'manage_users' => $admin['can_manage_users'],
            'manage_trades' => $admin['can_manage_trades'],
            'resolve_disputes' => $admin['can_resolve_disputes'],
            'manage_contracts' => $admin['can_manage_contracts'],
            'view_analytics' => $admin['can_view_analytics'],
            'manage_settings' => $admin['can_manage_settings'],
            'emergency_actions' => $admin['can_emergency_actions'],
            'manage_admins' => $admin['can_manage_admins']
        ];

        return isset($permissionMap[$permission]) ? (bool)$permissionMap[$permission] : false;

    } catch (Exception $e) {
        error_log('Error checking admin permission: ' . $e->getMessage());

        // في وضع التطوير، السماح بجميع الصلاحيات
        if (isDevelopmentMode()) {
            return true;
        }

        return false;
    }
}

/**
 * تسجيل نشاط الأدمن
 * Log admin activity
 */
function logAdminActivity($action, $target_type = null, $target_id = null, $data = null) {
    try {
        $admin_id = validateAdminSession();

        $db = DatabaseManager::getInstance()->getDatabase();
        $connection = $db->getConnection();

        // التحقق من وجود جدول admin_activity_logs
        $checkTable = $connection->prepare("SHOW TABLES LIKE 'admin_activity_logs'");
        $checkTable->execute();

        if ($checkTable->rowCount() > 0) {
            $stmt = $connection->prepare("
                INSERT INTO admin_activity_logs
                (admin_id, action_type, target_type, target_id, details, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $admin_id,
                $action,
                $target_type,
                $target_id,
                $data ? json_encode($data) : null,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
        } else {
            // استخدام جدول activity_logs العام كبديل
            $stmt = $connection->prepare("
                INSERT INTO activity_logs
                (user_id, action, entity_type, entity_id, data, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");

            // الحصول على user_id من admin_id
            $adminStmt = $connection->prepare("SELECT user_id FROM admin_users WHERE id = ?");
            $adminStmt->execute([$admin_id]);
            $adminUser = $adminStmt->fetch(PDO::FETCH_ASSOC);
            $userId = $adminUser ? $adminUser['user_id'] : $admin_id;

            $stmt->execute([
                $userId,
                'admin_' . $action,
                $target_type,
                $target_id,
                $data ? json_encode($data) : null,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
        }

        return true;

    } catch (Exception $e) {
        error_log('Error logging admin activity: ' . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على معلومات الأدمن الحالي
 * Get current admin info
 */
function getCurrentAdminInfo() {
    try {
        $admin_id = validateAdminSession();

        $db = DatabaseManager::getInstance()->getDatabase();
        $connection = $db->getConnection();

        $stmt = $connection->prepare("
            SELECT au.id, au.admin_role, au.permissions, au.department,
                   au.can_manage_users, au.can_manage_trades, au.can_resolve_disputes,
                   au.can_manage_contracts, au.can_view_analytics, au.can_manage_settings,
                   au.can_emergency_actions, au.can_manage_admins, au.session_timeout_hours,
                   au.two_factor_enabled, au.last_activity, au.created_at,
                   u.username, u.email, u.full_name, u.wallet_address, u.last_login
            FROM admin_users au
            JOIN users u ON au.user_id = u.id
            WHERE au.id = ? AND au.is_active = 1 AND u.is_active = 1
        ");
        $stmt->execute([$admin_id]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($admin) {
            $admin['permissions'] = json_decode($admin['permissions'], true) ?: [];

            // إضافة الصلاحيات البوليانية إلى قائمة الصلاحيات
            $booleanPermissions = [];
            if ($admin['can_manage_users']) $booleanPermissions[] = 'manage_users';
            if ($admin['can_manage_trades']) $booleanPermissions[] = 'manage_trades';
            if ($admin['can_resolve_disputes']) $booleanPermissions[] = 'resolve_disputes';
            if ($admin['can_manage_contracts']) $booleanPermissions[] = 'manage_contracts';
            if ($admin['can_view_analytics']) $booleanPermissions[] = 'view_analytics';
            if ($admin['can_manage_settings']) $booleanPermissions[] = 'manage_settings';
            if ($admin['can_emergency_actions']) $booleanPermissions[] = 'emergency_actions';
            if ($admin['can_manage_admins']) $booleanPermissions[] = 'manage_admins';

            $admin['all_permissions'] = array_unique(array_merge($admin['permissions'], $booleanPermissions));

            // إزالة الحقول الحساسة
            unset($admin['can_manage_users'], $admin['can_manage_trades'], $admin['can_resolve_disputes']);
            unset($admin['can_manage_contracts'], $admin['can_view_analytics'], $admin['can_manage_settings']);
            unset($admin['can_emergency_actions'], $admin['can_manage_admins']);
        }

        return $admin;

    } catch (Exception $e) {
        error_log('Error getting admin info: ' . $e->getMessage());

        // في وضع التطوير، إرجاع معلومات افتراضية
        if (isDevelopmentMode()) {
            return [
                'id' => $admin_id ?? 1,
                'username' => 'admin',
                'email' => '<EMAIL>',
                'full_name' => 'مدير النظام',
                'admin_role' => 'super_admin',
                'permissions' => ['super_admin'],
                'all_permissions' => ['super_admin', 'manage_users', 'manage_trades', 'resolve_disputes', 'manage_contracts', 'view_analytics', 'manage_settings', 'emergency_actions', 'manage_admins'],
                'wallet_address' => '******************************************'
            ];
        }

        return null;
    }
}

/**
 * تسجيل خروج الأدمن
 * Admin logout
 */
function adminLogout() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // تسجيل النشاط
    logAdminActivity('logout');
    
    // مسح الجلسة
    session_unset();
    session_destroy();
    
    return true;
}

/**
 * التحقق من IP المسموح
 * Check allowed IP
 */
function checkAllowedIP() {
    // للتطوير: السماح لجميع IPs
    return true;
    
    // في الإنتاج: التحقق من IPs المسموحة
    /*
    $allowed_ips = [
        '127.0.0.1',
        '::1',
        // إضافة IPs أخرى حسب الحاجة
    ];
    
    $client_ip = $_SERVER['REMOTE_ADDR'] ?? '';
    return in_array($client_ip, $allowed_ips);
    */
}

/**
 * التحقق من معدل الطلبات
 * Check rate limiting
 */
function checkRateLimit($action = 'general', $limit = 100, $window = 3600) {
    // للتطوير: عدم تطبيق حدود
    return true;
    
    // في الإنتاج: تطبيق حدود الطلبات
    /*
    try {
        $admin_id = validateAdminSession();
        $client_ip = $_SERVER['REMOTE_ADDR'] ?? '';
        $key = "rate_limit_{$action}_{$admin_id}_{$client_ip}";
        
        // استخدام Redis أو قاعدة البيانات لتتبع الطلبات
        // هذا مثال مبسط
        
        return true;
        
    } catch (Exception $e) {
        error_log('Error checking rate limit: ' . $e->getMessage());
        return false;
    }
    */
}
?>
