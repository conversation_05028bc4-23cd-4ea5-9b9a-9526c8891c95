<?php
/**
 * API لجلب أرصدة المحفظة
 * Wallet Balance API
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";

require_once __DIR__ . '/../../config/database.php';

try {
    $db = new Database();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $userId = $_GET['user_id'] ?? null;
        
        if (!$userId) {
            throw new Exception('معرف المستخدم مطلوب');
        }
        
        // التحقق من وجود المستخدم أو إنشاء بيانات افتراضية
        $stmt = $connection->prepare("
            SELECT id, username, wallet_address
            FROM users
            WHERE id = ?
        ");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            // إنشاء بيانات افتراضية للاختبار
            $user = [
                'id' => $userId,
                'username' => 'User' . $userId,
                'wallet_address' => '0x' . str_repeat('0', 40)
            ];
        }
        
        // جلب أرصدة المحفظة من جدول wallet_transactions
        $stmt = $connection->prepare("
            SELECT 
                currency,
                SUM(CASE 
                    WHEN transaction_type IN ('deposit', 'trade_credit') THEN amount 
                    WHEN transaction_type IN ('withdraw', 'trade_debit', 'fee') THEN -amount 
                    ELSE 0 
                END) as total_balance,
                SUM(CASE 
                    WHEN transaction_type IN ('deposit', 'trade_credit') AND status = 'completed' THEN amount 
                    WHEN transaction_type IN ('withdraw', 'trade_debit', 'fee') AND status = 'completed' THEN -amount 
                    ELSE 0 
                END) as available_balance,
                SUM(CASE 
                    WHEN status = 'pending' THEN amount 
                    ELSE 0 
                END) as locked_balance
            FROM wallet_transactions 
            WHERE user_id = ? 
            GROUP BY currency
            HAVING total_balance > 0
        ");
        $stmt->execute([$userId]);
        $balances = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // إضافة معلومات إضافية لكل عملة
        $formattedBalances = [];
        foreach ($balances as $balance) {
            $currency = $balance['currency'];
            $totalBalance = floatval($balance['total_balance']);
            $availableBalance = floatval($balance['available_balance']);
            $lockedBalance = floatval($balance['locked_balance']);
            
            // حساب القيمة بالدولار (محاكاة - يجب ربطها بـ API أسعار حقيقي)
            $usdRate = getCurrencyUSDRate($currency);
            $usdValue = $totalBalance * $usdRate;
            
            // حساب التغيير خلال 24 ساعة (محاكاة)
            $change24h = rand(-500, 500) / 100; // تغيير عشوائي للاختبار
            
            $formattedBalances[] = [
                'currency' => $currency,
                'symbol' => getCurrencySymbol($currency),
                'total' => number_format($totalBalance, 2, '.', ''),
                'available' => number_format($availableBalance, 2, '.', ''),
                'locked' => number_format($lockedBalance, 2, '.', ''),
                'usd_value' => number_format($usdValue, 2, '.', ''),
                'change_24h' => number_format($change24h, 2, '.', ''),
                'last_updated' => date('Y-m-d H:i:s')
            ];
        }
        
        // إذا لم توجد أرصدة، إضافة رصيد USDT افتراضي
        if (empty($formattedBalances)) {
            $formattedBalances[] = [
                'currency' => 'USDT',
                'symbol' => 'USDT',
                'total' => '0.00',
                'available' => '0.00',
                'locked' => '0.00',
                'usd_value' => '0.00',
                'change_24h' => '0.00',
                'last_updated' => date('Y-m-d H:i:s')
            ];
        }
        
        echo json_encode([
            'success' => true,
            'data' => $formattedBalances,
            'user_info' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'wallet_address' => $user['wallet_address']
            ]
        ]);
        
    } else {
        throw new Exception('طريقة الطلب غير مدعومة');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات'
    ]);
}

/**
 * الحصول على معدل تحويل العملة إلى الدولار
 */
function getCurrencyUSDRate($currency) {
    $rates = [
        'USDT' => 1.0,
        'USDC' => 1.0,
        'USD' => 1.0,
        'SAR' => 0.267, // 1 SAR = 0.267 USD تقريباً
        'AED' => 0.272, // 1 AED = 0.272 USD تقريباً
        'EUR' => 1.1,   // 1 EUR = 1.1 USD تقريباً
        'BTC' => 45000, // سعر تجريبي
        'ETH' => 3000   // سعر تجريبي
    ];
    
    return $rates[$currency] ?? 1.0;
}

/**
 * الحصول على رمز العملة
 */
function getCurrencySymbol($currency) {
    $symbols = [
        'USDT' => 'USDT',
        'USDC' => 'USDC',
        'USD' => '$',
        'SAR' => 'ر.س',
        'AED' => 'د.إ',
        'EUR' => '€',
        'BTC' => '₿',
        'ETH' => 'Ξ'
    ];
    
    return $symbols[$currency] ?? $currency;
}
?>
