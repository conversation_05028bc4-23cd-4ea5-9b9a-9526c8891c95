/**
 * نظام معالجة الأخطاء الشامل متعدد اللغات
 * يوفر معالجة موحدة للأخطاء مع رسائل واضحة ومفهومة
 */

import { notificationService } from '@/services/notificationService';

// أنواع الأخطاء المختلفة
export enum ErrorType {
  WALLET_ERROR = 'WALLET_ERROR',
  CONTRACT_ERROR = 'CONTRACT_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  API_ERROR = 'API_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// رموز الأخطاء المحددة
export enum ErrorCode {
  // أخطاء المحفظة
  WALLET_NOT_CONNECTED = 'WALLET_NOT_CONNECTED',
  WALLET_CONNECTION_FAILED = 'WALLET_CONNECTION_FAILED',
  USER_REJECTED_TRANSACTION = 'USER_REJECTED_TRANSACTION',
  INSUFFICIENT_FUNDS = 'INSUFFICIENT_FUNDS',
  INSUFFICIENT_ALLOWANCE = 'INSUFFICIENT_ALLOWANCE',
  
  // أخطاء العقد الذكي
  CONTRACT_NOT_DEPLOYED = 'CONTRACT_NOT_DEPLOYED',
  CONTRACT_CALL_FAILED = 'CONTRACT_CALL_FAILED',
  TRANSACTION_FAILED = 'TRANSACTION_FAILED',
  TRADE_NOT_FOUND = 'TRADE_NOT_FOUND',
  TRADE_ALREADY_EXISTS = 'TRADE_ALREADY_EXISTS',
  TRADE_EXPIRED = 'TRADE_EXPIRED',
  
  // أخطاء الشبكة
  NETWORK_DISCONNECTED = 'NETWORK_DISCONNECTED',
  WRONG_NETWORK = 'WRONG_NETWORK',
  NETWORK_CONGESTION = 'NETWORK_CONGESTION',
  RPC_ERROR = 'RPC_ERROR',
  
  // أخطاء التحقق
  INVALID_AMOUNT = 'INVALID_AMOUNT',
  INVALID_ADDRESS = 'INVALID_ADDRESS',
  INVALID_PARAMETERS = 'INVALID_PARAMETERS',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  
  // أخطاء API
  API_TIMEOUT = 'API_TIMEOUT',
  API_RATE_LIMIT = 'API_RATE_LIMIT',
  API_SERVER_ERROR = 'API_SERVER_ERROR',
  API_NOT_FOUND = 'API_NOT_FOUND',
  
  // أخطاء عامة
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  OPERATION_CANCELLED = 'OPERATION_CANCELLED'
}

// واجهة تفاصيل الخطأ
export interface ErrorDetails {
  code: ErrorCode;
  type: ErrorType;
  message: string;
  originalError?: any;
  context?: string;
  timestamp: Date;
  userId?: string;
  transactionHash?: string;
  blockNumber?: number;
}

// واجهة خيارات معالجة الخطأ
export interface ErrorHandlingOptions {
  showNotification?: boolean;
  logError?: boolean;
  reportToService?: boolean;
  fallbackAction?: () => void;
  customMessage?: string;
  notificationDuration?: number;
}

class ErrorHandler {
  private static instance: ErrorHandler;
  private errorLogs: ErrorDetails[] = [];
  private maxLogSize = 100;

  private constructor() {}

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * معالجة الخطأ الرئيسية
   */
  handleError(
    error: any, 
    context: string = 'Unknown', 
    options: ErrorHandlingOptions = {}
  ): ErrorDetails {
    const errorDetails = this.parseError(error, context);
    
    // تسجيل الخطأ
    if (options.logError !== false) {
      this.logError(errorDetails);
    }

    // عرض الإشعار
    if (options.showNotification !== false) {
      this.showErrorNotification(errorDetails, options);
    }

    // إرسال التقرير للخدمة
    if (options.reportToService) {
      this.reportErrorToService(errorDetails);
    }

    // تنفيذ الإجراء البديل
    if (options.fallbackAction) {
      try {
        options.fallbackAction();
      } catch (fallbackError) {
        console.error('خطأ في الإجراء البديل:', fallbackError);
      }
    }

    return errorDetails;
  }

  /**
   * تحليل الخطأ وتحديد نوعه ورمزه
   */
  private parseError(error: any, context: string): ErrorDetails {
    let code = ErrorCode.UNKNOWN_ERROR;
    let type = ErrorType.UNKNOWN_ERROR;
    let message = 'حدث خطأ غير متوقع';

    if (typeof error === 'string') {
      message = error;
    } else if (error?.message) {
      message = error.message;
    }

    // تحليل أخطاء المحفظة
    if (this.isWalletError(error, message)) {
      type = ErrorType.WALLET_ERROR;
      code = this.getWalletErrorCode(error, message);
    }
    // تحليل أخطاء العقد الذكي
    else if (this.isContractError(error, message)) {
      type = ErrorType.CONTRACT_ERROR;
      code = this.getContractErrorCode(error, message);
    }
    // تحليل أخطاء الشبكة
    else if (this.isNetworkError(error, message)) {
      type = ErrorType.NETWORK_ERROR;
      code = this.getNetworkErrorCode(error, message);
    }
    // تحليل أخطاء التحقق
    else if (this.isValidationError(error, message)) {
      type = ErrorType.VALIDATION_ERROR;
      code = this.getValidationErrorCode(error, message);
    }
    // تحليل أخطاء API
    else if (this.isApiError(error, message)) {
      type = ErrorType.API_ERROR;
      code = this.getApiErrorCode(error, message);
    }

    return {
      code,
      type,
      message: this.getLocalizedMessage(code, message),
      originalError: error,
      context,
      timestamp: new Date(),
      transactionHash: error?.transactionHash,
      blockNumber: error?.blockNumber
    };
  }

  /**
   * التحقق من أخطاء المحفظة
   */
  private isWalletError(error: any, message: string): boolean {
    const walletKeywords = [
      'wallet', 'metamask', 'user rejected', 'denied', 'cancelled',
      'insufficient funds', 'allowance', 'balance', 'محفظة', 'رفض', 'رصيد'
    ];
    
    return walletKeywords.some(keyword => 
      message.toLowerCase().includes(keyword.toLowerCase()) ||
      error?.code?.toString().includes('4001') // User rejected
    );
  }

  /**
   * الحصول على رمز خطأ المحفظة
   */
  private getWalletErrorCode(error: any, message: string): ErrorCode {
    if (error?.code === 4001 || message.includes('rejected') || message.includes('denied')) {
      return ErrorCode.USER_REJECTED_TRANSACTION;
    }
    if (message.includes('insufficient') || message.includes('رصيد')) {
      return ErrorCode.INSUFFICIENT_FUNDS;
    }
    if (message.includes('allowance') || message.includes('موافقة')) {
      return ErrorCode.INSUFFICIENT_ALLOWANCE;
    }
    if (message.includes('not connected') || message.includes('غير متصل')) {
      return ErrorCode.WALLET_NOT_CONNECTED;
    }
    return ErrorCode.WALLET_CONNECTION_FAILED;
  }

  /**
   * التحقق من أخطاء العقد الذكي
   */
  private isContractError(error: any, message: string): boolean {
    const contractKeywords = [
      'contract', 'transaction', 'revert', 'gas', 'execution',
      'عقد', 'معاملة', 'تنفيذ', 'فشل'
    ];
    
    return contractKeywords.some(keyword => 
      message.toLowerCase().includes(keyword.toLowerCase())
    ) || error?.code?.toString().includes('CALL_EXCEPTION');
  }

  /**
   * الحصول على رمز خطأ العقد الذكي
   */
  private getContractErrorCode(error: any, message: string): ErrorCode {
    if (message.includes('not found') || message.includes('غير موجود')) {
      return ErrorCode.TRADE_NOT_FOUND;
    }
    if (message.includes('already exists') || message.includes('موجود بالفعل')) {
      return ErrorCode.TRADE_ALREADY_EXISTS;
    }
    if (message.includes('expired') || message.includes('منتهي')) {
      return ErrorCode.TRADE_EXPIRED;
    }
    if (message.includes('revert') || message.includes('failed')) {
      return ErrorCode.TRANSACTION_FAILED;
    }
    return ErrorCode.CONTRACT_CALL_FAILED;
  }

  /**
   * التحقق من أخطاء الشبكة
   */
  private isNetworkError(error: any, message: string): boolean {
    const networkKeywords = [
      'network', 'connection', 'timeout', 'rpc', 'provider',
      'شبكة', 'اتصال', 'انقطاع'
    ];
    
    return networkKeywords.some(keyword => 
      message.toLowerCase().includes(keyword.toLowerCase())
    ) || error?.code?.toString().includes('NETWORK_ERROR');
  }

  /**
   * الحصول على رمز خطأ الشبكة
   */
  private getNetworkErrorCode(error: any, message: string): ErrorCode {
    if (message.includes('wrong network') || message.includes('شبكة خاطئة')) {
      return ErrorCode.WRONG_NETWORK;
    }
    if (message.includes('timeout') || message.includes('انقطاع')) {
      return ErrorCode.API_TIMEOUT;
    }
    if (message.includes('congestion') || message.includes('ازدحام')) {
      return ErrorCode.NETWORK_CONGESTION;
    }
    return ErrorCode.NETWORK_DISCONNECTED;
  }

  /**
   * التحقق من أخطاء التحقق
   */
  private isValidationError(error: any, message: string): boolean {
    const validationKeywords = [
      'invalid', 'required', 'validation', 'format',
      'غير صحيح', 'مطلوب', 'تحقق', 'تنسيق'
    ];
    
    return validationKeywords.some(keyword => 
      message.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * الحصول على رمز خطأ التحقق
   */
  private getValidationErrorCode(error: any, message: string): ErrorCode {
    if (message.includes('amount') || message.includes('مبلغ')) {
      return ErrorCode.INVALID_AMOUNT;
    }
    if (message.includes('address') || message.includes('عنوان')) {
      return ErrorCode.INVALID_ADDRESS;
    }
    if (message.includes('required') || message.includes('مطلوب')) {
      return ErrorCode.MISSING_REQUIRED_FIELD;
    }
    return ErrorCode.INVALID_PARAMETERS;
  }

  /**
   * التحقق من أخطاء API
   */
  private isApiError(error: any, message: string): boolean {
    return error?.status || error?.response || message.includes('API') || message.includes('server');
  }

  /**
   * الحصول على رمز خطأ API
   */
  private getApiErrorCode(error: any, message: string): ErrorCode {
    const status = error?.status || error?.response?.status;
    
    if (status === 404) return ErrorCode.API_NOT_FOUND;
    if (status === 429) return ErrorCode.API_RATE_LIMIT;
    if (status >= 500) return ErrorCode.API_SERVER_ERROR;
    if (message.includes('timeout')) return ErrorCode.API_TIMEOUT;
    
    return ErrorCode.API_SERVER_ERROR;
  }

  /**
   * الحصول على الرسالة المترجمة
   */
  private getLocalizedMessage(code: ErrorCode, originalMessage: string): string {
    // خريطة الرسائل المترجمة
    const messageMap: Record<ErrorCode, { ar: string; en: string }> = {
      [ErrorCode.WALLET_NOT_CONNECTED]: {
        ar: 'المحفظة غير متصلة، يرجى ربط المحفظة أولاً',
        en: 'Wallet not connected, please connect your wallet first'
      },
      [ErrorCode.USER_REJECTED_TRANSACTION]: {
        ar: 'تم رفض المعاملة من قبل المستخدم',
        en: 'Transaction rejected by user'
      },
      [ErrorCode.INSUFFICIENT_FUNDS]: {
        ar: 'الرصيد غير كافي لإتمام العملية',
        en: 'Insufficient funds to complete the operation'
      },
      [ErrorCode.CONTRACT_CALL_FAILED]: {
        ar: 'فشل في استدعاء العقد الذكي، يرجى المحاولة مرة أخرى',
        en: 'Smart contract call failed, please try again'
      },
      [ErrorCode.NETWORK_DISCONNECTED]: {
        ar: 'انقطع الاتصال بالشبكة، يرجى التحقق من الاتصال',
        en: 'Network disconnected, please check your connection'
      },
      [ErrorCode.WRONG_NETWORK]: {
        ar: 'شبكة خاطئة، يرجى التبديل إلى BSC Testnet',
        en: 'Wrong network, please switch to BSC Testnet'
      },
      [ErrorCode.INVALID_AMOUNT]: {
        ar: 'المبلغ المدخل غير صحيح',
        en: 'Invalid amount entered'
      },
      [ErrorCode.API_TIMEOUT]: {
        ar: 'انتهت مهلة الاتصال، يرجى المحاولة مرة أخرى',
        en: 'Connection timeout, please try again'
      },
      [ErrorCode.UNKNOWN_ERROR]: {
        ar: 'حدث خطأ غير متوقع',
        en: 'An unexpected error occurred'
      }
    };

    // الحصول على اللغة الحالية (افتراضياً العربية)
    const currentLanguage = localStorage.getItem('language') || 'ar';
    const messages = messageMap[code];
    
    if (messages) {
      return currentLanguage === 'ar' ? messages.ar : messages.en;
    }
    
    return originalMessage;
  }

  /**
   * عرض إشعار الخطأ
   */
  private showErrorNotification(errorDetails: ErrorDetails, options: ErrorHandlingOptions): void {
    const message = options.customMessage || errorDetails.message;
    const duration = options.notificationDuration || 5000;
    
    notificationService.error(message, {
      duration,
      icon: this.getErrorIcon(errorDetails.type)
    });
  }

  /**
   * الحصول على أيقونة الخطأ
   */
  private getErrorIcon(errorType: ErrorType): string {
    const iconMap: Record<ErrorType, string> = {
      [ErrorType.WALLET_ERROR]: '👛',
      [ErrorType.CONTRACT_ERROR]: '📄',
      [ErrorType.NETWORK_ERROR]: '🌐',
      [ErrorType.VALIDATION_ERROR]: '⚠️',
      [ErrorType.API_ERROR]: '🔌',
      [ErrorType.UNKNOWN_ERROR]: '❌'
    };
    
    return iconMap[errorType] || '❌';
  }

  /**
   * تسجيل الخطأ
   */
  private logError(errorDetails: ErrorDetails): void {
    // إضافة للسجل المحلي
    this.errorLogs.unshift(errorDetails);
    
    // الحفاظ على حجم السجل
    if (this.errorLogs.length > this.maxLogSize) {
      this.errorLogs = this.errorLogs.slice(0, this.maxLogSize);
    }
    
    // طباعة في وحدة التحكم
    console.error('🚨 خطأ في التطبيق:', {
      code: errorDetails.code,
      type: errorDetails.type,
      message: errorDetails.message,
      context: errorDetails.context,
      timestamp: errorDetails.timestamp,
      originalError: errorDetails.originalError
    });
  }

  /**
   * إرسال تقرير الخطأ للخدمة
   */
  private async reportErrorToService(errorDetails: ErrorDetails): Promise<void> {
    try {
      // في بيئة الإنتاج، يمكن إرسال التقرير لخدمة مراقبة الأخطاء
      if (process.env.NODE_ENV === 'production') {
        // await errorReportingService.report(errorDetails);
      }
    } catch (reportError) {
      console.error('فشل في إرسال تقرير الخطأ:', reportError);
    }
  }

  /**
   * الحصول على سجل الأخطاء
   */
  getErrorLogs(): ErrorDetails[] {
    return [...this.errorLogs];
  }

  /**
   * مسح سجل الأخطاء
   */
  clearErrorLogs(): void {
    this.errorLogs = [];
  }

  /**
   * معالجة سريعة للأخطاء الشائعة
   */
  static handleCommonError(error: any, context: string = 'Unknown'): void {
    const handler = ErrorHandler.getInstance();
    handler.handleError(error, context, {
      showNotification: true,
      logError: true
    });
  }

  /**
   * معالجة أخطاء المحفظة
   */
  static handleWalletError(error: any, context: string = 'Wallet'): void {
    const handler = ErrorHandler.getInstance();
    handler.handleError(error, context, {
      showNotification: true,
      logError: true,
      fallbackAction: () => {
        // يمكن إضافة إجراء بديل مثل إعادة محاولة الاتصال
      }
    });
  }

  /**
   * معالجة أخطاء العقد الذكي
   */
  static handleContractError(error: any, context: string = 'Contract'): void {
    const handler = ErrorHandler.getInstance();
    handler.handleError(error, context, {
      showNotification: true,
      logError: true,
      reportToService: true
    });
  }
}

// تصدير instance واحد
export const errorHandler = ErrorHandler.getInstance();

// تصدير الدوال المساعدة
export const { handleCommonError, handleWalletError, handleContractError } = ErrorHandler;
