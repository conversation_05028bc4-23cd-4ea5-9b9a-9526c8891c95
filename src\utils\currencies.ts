// نظام العملات الذكي - بدون حاجة للترجمة
export interface Currency {
  code: string;           // الرمز العالمي (SAR, USD, AED)
  symbol: string;         // الرمز المالي (﷼, $, د.إ)
  name_en: string;        // الاسم بالإنجليزية
  name_ar: string;        // الاسم بالعربية
  country_code: string;   // رمز الدولة
  decimal_places: number; // عدد الخانات العشرية
  is_active: boolean;     // هل العملة نشطة
  sort_order: number;     // ترتيب العرض
  flag_emoji: string;     // علم الدولة
}

// قاعدة بيانات العملات المدعومة
export const SUPPORTED_CURRENCIES: Currency[] = [
  {
    code: 'SAR',
    symbol: '﷼',
    name_en: 'Saudi Riyal',
    name_ar: 'ريال سعودي',
    country_code: 'SA',
    decimal_places: 2,
    is_active: true,
    sort_order: 1,
    flag_emoji: '🇸🇦'
  },
  {
    code: 'AED',
    symbol: 'د.إ',
    name_en: 'UAE Dirham',
    name_ar: 'درهم إماراتي',
    country_code: 'AE',
    decimal_places: 2,
    is_active: true,
    sort_order: 2,
    flag_emoji: '🇦🇪'
  },
  {
    code: 'KWD',
    symbol: 'د.ك',
    name_en: 'Kuwaiti Dinar',
    name_ar: 'دينار كويتي',
    country_code: 'KW',
    decimal_places: 3,
    is_active: true,
    sort_order: 3,
    flag_emoji: '🇰🇼'
  },
  {
    code: 'QAR',
    symbol: 'ر.ق',
    name_en: 'Qatari Riyal',
    name_ar: 'ريال قطري',
    country_code: 'QA',
    decimal_places: 2,
    is_active: true,
    sort_order: 4,
    flag_emoji: '🇶🇦'
  },
  {
    code: 'BHD',
    symbol: 'د.ب',
    name_en: 'Bahraini Dinar',
    name_ar: 'دينار بحريني',
    country_code: 'BH',
    decimal_places: 3,
    is_active: true,
    sort_order: 5,
    flag_emoji: '🇧🇭'
  },
  {
    code: 'OMR',
    symbol: 'ر.ع',
    name_en: 'Omani Rial',
    name_ar: 'ريال عماني',
    country_code: 'OM',
    decimal_places: 3,
    is_active: true,
    sort_order: 6,
    flag_emoji: '🇴🇲'
  },
  {
    code: 'JOD',
    symbol: 'د.أ',
    name_en: 'Jordanian Dinar',
    name_ar: 'دينار أردني',
    country_code: 'JO',
    decimal_places: 3,
    is_active: true,
    sort_order: 7,
    flag_emoji: '🇯🇴'
  },
  {
    code: 'EGP',
    symbol: 'ج.م',
    name_en: 'Egyptian Pound',
    name_ar: 'جنيه مصري',
    country_code: 'EG',
    decimal_places: 2,
    is_active: true,
    sort_order: 8,
    flag_emoji: '🇪🇬'
  },
  {
    code: 'USD',
    symbol: '$',
    name_en: 'US Dollar',
    name_ar: 'دولار أمريكي',
    country_code: 'US',
    decimal_places: 2,
    is_active: true,
    sort_order: 9,
    flag_emoji: '🇺🇸'
  },
  {
    code: 'EUR',
    symbol: '€',
    name_en: 'Euro',
    name_ar: 'يورو',
    country_code: 'EU',
    decimal_places: 2,
    is_active: true,
    sort_order: 10,
    flag_emoji: '🇪🇺'
  },
  {
    code: 'GBP',
    symbol: '£',
    name_en: 'British Pound',
    name_ar: 'جنيه إسترليني',
    country_code: 'GB',
    decimal_places: 2,
    is_active: true,
    sort_order: 11,
    flag_emoji: '🇬🇧'
  },
  {
    code: 'JPY',
    symbol: '¥',
    name_en: 'Japanese Yen',
    name_ar: 'ين ياباني',
    country_code: 'JP',
    decimal_places: 0,
    is_active: true,
    sort_order: 12,
    flag_emoji: '🇯🇵'
  },
  {
    code: 'CAD',
    symbol: 'C$',
    name_en: 'Canadian Dollar',
    name_ar: 'دولار كندي',
    country_code: 'CA',
    decimal_places: 2,
    is_active: true,
    sort_order: 13,
    flag_emoji: '🇨🇦'
  },
  {
    code: 'AUD',
    symbol: 'A$',
    name_en: 'Australian Dollar',
    name_ar: 'دولار أسترالي',
    country_code: 'AU',
    decimal_places: 2,
    is_active: true,
    sort_order: 14,
    flag_emoji: '🇦🇺'
  },
  {
    code: 'CHF',
    symbol: 'Fr',
    name_en: 'Swiss Franc',
    name_ar: 'فرنك سويسري',
    country_code: 'CH',
    decimal_places: 2,
    is_active: true,
    sort_order: 15,
    flag_emoji: '🇨🇭'
  },
  {
    code: 'CNY',
    symbol: '¥',
    name_en: 'Chinese Yuan',
    name_ar: 'يوان صيني',
    country_code: 'CN',
    decimal_places: 2,
    is_active: true,
    sort_order: 16,
    flag_emoji: '🇨🇳'
  },
  {
    code: 'TRY',
    symbol: '₺',
    name_en: 'Turkish Lira',
    name_ar: 'ليرة تركية',
    country_code: 'TR',
    decimal_places: 2,
    is_active: true,
    sort_order: 17,
    flag_emoji: '🇹🇷'
  },
  {
    code: 'LBP',
    symbol: 'ل.ل',
    name_en: 'Lebanese Pound',
    name_ar: 'ليرة لبنانية',
    country_code: 'LB',
    decimal_places: 2,
    is_active: true,
    sort_order: 18,
    flag_emoji: '🇱🇧'
  },
  {
    code: 'MAD',
    symbol: 'د.م',
    name_en: 'Moroccan Dirham',
    name_ar: 'درهم مغربي',
    country_code: 'MA',
    decimal_places: 2,
    is_active: true,
    sort_order: 19,
    flag_emoji: '🇲🇦'
  },
  {
    code: 'TND',
    symbol: 'د.ت',
    name_en: 'Tunisian Dinar',
    name_ar: 'دينار تونسي',
    country_code: 'TN',
    decimal_places: 3,
    is_active: true,
    sort_order: 20,
    flag_emoji: '🇹🇳'
  },
  {
    code: 'DZD',
    symbol: 'د.ج',
    name_en: 'Algerian Dinar',
    name_ar: 'دينار جزائري',
    country_code: 'DZ',
    decimal_places: 2,
    is_active: true,
    sort_order: 21,
    flag_emoji: '🇩🇿'
  },
  {
    code: 'IQD',
    symbol: 'ع.د',
    name_en: 'Iraqi Dinar',
    name_ar: 'دينار عراقي',
    country_code: 'IQ',
    decimal_places: 3,
    is_active: true,
    sort_order: 22,
    flag_emoji: '🇮🇶'
  },
  {
    code: 'SYP',
    symbol: 'ل.س',
    name_en: 'Syrian Pound',
    name_ar: 'ليرة سورية',
    country_code: 'SY',
    decimal_places: 2,
    is_active: true,
    sort_order: 23,
    flag_emoji: '🇸🇾'
  },
  {
    code: 'YER',
    symbol: 'ر.ي',
    name_en: 'Yemeni Rial',
    name_ar: 'ريال يمني',
    country_code: 'YE',
    decimal_places: 2,
    is_active: true,
    sort_order: 24,
    flag_emoji: '🇾🇪'
  },
  {
    code: 'PKR',
    symbol: '₨',
    name_en: 'Pakistani Rupee',
    name_ar: 'روبية باكستانية',
    country_code: 'PK',
    decimal_places: 2,
    is_active: true,
    sort_order: 25,
    flag_emoji: '🇵🇰'
  },
  {
    code: 'INR',
    symbol: '₹',
    name_en: 'Indian Rupee',
    name_ar: 'روبية هندية',
    country_code: 'IN',
    decimal_places: 2,
    is_active: true,
    sort_order: 26,
    flag_emoji: '🇮🇳'
  },
  {
    code: 'BDT',
    symbol: '৳',
    name_en: 'Bangladeshi Taka',
    name_ar: 'تاكا بنغلاديشية',
    country_code: 'BD',
    decimal_places: 2,
    is_active: true,
    sort_order: 27,
    flag_emoji: '🇧🇩'
  },
  {
    code: 'MYR',
    symbol: 'RM',
    name_en: 'Malaysian Ringgit',
    name_ar: 'رينغيت ماليزي',
    country_code: 'MY',
    decimal_places: 2,
    is_active: true,
    sort_order: 28,
    flag_emoji: '🇲🇾'
  },
  {
    code: 'IDR',
    symbol: 'Rp',
    name_en: 'Indonesian Rupiah',
    name_ar: 'روبية إندونيسية',
    country_code: 'ID',
    decimal_places: 0,
    is_active: true,
    sort_order: 29,
    flag_emoji: '🇮🇩'
  },
  {
    code: 'SGD',
    symbol: 'S$',
    name_en: 'Singapore Dollar',
    name_ar: 'دولار سنغافوري',
    country_code: 'SG',
    decimal_places: 2,
    is_active: true,
    sort_order: 30,
    flag_emoji: '🇸🇬'
  }
];

// دوال مساعدة
export const getCurrencyByCode = (code: string): Currency | undefined => {
  return SUPPORTED_CURRENCIES.find(currency => currency.code === code);
};

export const getActiveCurrencies = (): Currency[] => {
  return SUPPORTED_CURRENCIES
    .filter(currency => currency.is_active)
    .sort((a, b) => a.sort_order - b.sort_order);
};

export const formatCurrencyAmount = (
  amount: number, 
  currencyCode: string, 
  locale: string = 'ar-SA'
): string => {
  const currency = getCurrencyByCode(currencyCode);
  if (!currency) return amount.toString();

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: currency.decimal_places,
    maximumFractionDigits: currency.decimal_places
  }).format(amount);
};

export const getCurrencyName = (code: string, language: 'ar' | 'en'): string => {
  const currency = getCurrencyByCode(code);
  if (!currency) return code;
  
  return language === 'ar' ? currency.name_ar : currency.name_en;
};

// مكون اختيار العملة
export const getCurrencyDisplay = (code: string, language: 'ar' | 'en' = 'ar') => {
  const currency = getCurrencyByCode(code);
  if (!currency) return { code, name: code, symbol: '', flag: '' };

  return {
    code: currency.code,
    name: language === 'ar' ? currency.name_ar : currency.name_en,
    symbol: currency.symbol,
    flag: currency.flag_emoji
  };
};

// للاستخدام في قاعدة البيانات
export interface DatabaseCurrency {
  id: number;
  code: string;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

// تحويل من قاعدة البيانات إلى العرض
export const mapDatabaseCurrency = (dbCurrency: DatabaseCurrency): Currency | null => {
  const baseCurrency = getCurrencyByCode(dbCurrency.code);
  if (!baseCurrency) return null;

  return {
    ...baseCurrency,
    is_active: dbCurrency.is_active,
    sort_order: dbCurrency.sort_order
  };
};

// للتصدير إلى JSON
export const exportCurrenciesConfig = () => {
  return {
    version: '1.0',
    last_updated: new Date().toISOString(),
    currencies: SUPPORTED_CURRENCIES
  };
};

// تحديث العملات من الإعدادات
export const updateCurrencyStatus = (code: string, isActive: boolean): Currency[] => {
  return SUPPORTED_CURRENCIES.map(currency => 
    currency.code === code 
      ? { ...currency, is_active: isActive }
      : currency
  );
};

// البحث في العملات
export const searchCurrencies = (query: string, language: 'ar' | 'en' = 'ar'): Currency[] => {
  const searchTerm = query.toLowerCase();
  
  return SUPPORTED_CURRENCIES.filter(currency => {
    const name = language === 'ar' ? currency.name_ar : currency.name_en;
    return (
      currency.code.toLowerCase().includes(searchTerm) ||
      name.toLowerCase().includes(searchTerm) ||
      currency.symbol.includes(searchTerm)
    );
  });
};
