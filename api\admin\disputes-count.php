<?php
/**
 * API لحساب عدد النزاعات النشطة
 * API for counting active disputes
 */

require_once '../includes/cors.php';
require_once '../config/database.php';

try {
    // التحقق من صلاحيات الإدارة
    checkAdminPermissions();
    
    $database = new Database();
    $db = $database->getConnection();
    
    // حساب النزاعات النشطة
    $query = "SELECT 
                COUNT(*) as active_disputes,
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as recent_disputes,
                COUNT(CASE WHEN priority = 'high' THEN 1 END) as high_priority_disputes
              FROM disputes 
              WHERE status IN ('open', 'investigating', 'pending_resolution')";
    
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // إحصائيات إضافية
    $detailsQuery = "SELECT 
                        d.id,
                        d.trade_id,
                        d.reason,
                        d.priority,
                        d.created_at,
                        t.amount,
                        u1.username as complainant_username,
                        u2.username as respondent_username
                     FROM disputes d
                     LEFT JOIN trades t ON d.trade_id = t.id
                     LEFT JOIN users u1 ON d.complainant_id = u1.id
                     LEFT JOIN users u2 ON d.respondent_id = u2.id
                     WHERE d.status IN ('open', 'investigating', 'pending_resolution')
                     ORDER BY d.priority DESC, d.created_at ASC
                     LIMIT 10";
    
    $detailsStmt = $db->prepare($detailsQuery);
    $detailsStmt->execute();
    $recentDisputes = $detailsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تسجيل النشاط
    logActivity('disputes_count_check', [
        'active_disputes' => $result['active_disputes'],
        'recent_disputes' => $result['recent_disputes']
    ]);
    
    sendSuccess([
        'activeDisputes' => (int)$result['active_disputes'],
        'recentDisputes' => (int)$result['recent_disputes'],
        'highPriorityDisputes' => (int)$result['high_priority_disputes'],
        'disputeDetails' => $recentDisputes,
        'timestamp' => date('c')
    ]);
    
} catch (Exception $e) {
    error_log("Error in disputes-count.php: " . $e->getMessage());
    sendError('فشل في جلب إحصائيات النزاعات', 500, [
        'error' => $e->getMessage(),
        'file' => __FILE__
    ]);
}
?>
