<?php
/**
 * API endpoint لإدارة العروض
 * Offers Management API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = new Database();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // جلب العروض مع الفلترة
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        
        // فلاتر البحث
        $offerType = $_GET['type'] ?? 'all'; // buy, sell, all
        $currency = $_GET['currency'] ?? 'all';
        $stablecoin = $_GET['stablecoin'] ?? 'all';
        $paymentMethod = $_GET['payment_method'] ?? 'all';
        $minAmount = floatval($_GET['min_amount'] ?? 0);
        $maxAmount = floatval($_GET['max_amount'] ?? 0);
        $minPrice = floatval($_GET['min_price'] ?? 0);
        $maxPrice = floatval($_GET['max_price'] ?? 0);
        $location = $_GET['location'] ?? '';
        $verifiedOnly = isset($_GET['verified_only']) && $_GET['verified_only'] === 'true';
        $premiumOnly = isset($_GET['premium_only']) && $_GET['premium_only'] === 'true';
        $sortBy = $_GET['sort'] ?? 'created_at';
        $sortOrder = $_GET['order'] ?? 'DESC';
        $search = $_GET['search'] ?? '';
        
        // بناء الاستعلام
        $whereConditions = ['o.is_active = 1'];
        $params = [];
        
        if ($offerType !== 'all') {
            $whereConditions[] = 'o.offer_type = ?';
            $params[] = $offerType;
        }
        
        if ($currency !== 'all') {
            $whereConditions[] = 'o.currency = ?';
            $params[] = $currency;
        }

        if ($stablecoin !== 'all') {
            $whereConditions[] = 'o.stablecoin = ?';
            $params[] = $stablecoin;
        }

        if ($minAmount > 0) {
            $whereConditions[] = 'o.amount >= ?';
            $params[] = $minAmount;
        }

        if ($maxAmount > 0) {
            $whereConditions[] = 'o.amount <= ?';
            $params[] = $maxAmount;
        }

        if ($minPrice > 0) {
            $whereConditions[] = 'o.price >= ?';
            $params[] = $minPrice;
        }

        if ($maxPrice > 0) {
            $whereConditions[] = 'o.price <= ?';
            $params[] = $maxPrice;
        }

        if (!empty($location)) {
            $whereConditions[] = 'u.country_code = ?';
            $params[] = $location;
        }

        if ($verifiedOnly) {
            $whereConditions[] = 'u.is_verified = 1';
        }

        // تعليق مؤقت حتى إضافة العمود
        // if ($premiumOnly) {
        //     $whereConditions[] = 'o.is_premium = 1';
        // }
        
        if (!empty($search)) {
            $whereConditions[] = '(u.username LIKE ? OR u.full_name LIKE ? OR o.terms LIKE ?)';
            $searchTerm = "%$search%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        if ($paymentMethod !== 'all') {
            $whereConditions[] = 'JSON_CONTAINS(o.payment_methods, ?)';
            $params[] = json_encode($paymentMethod);
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // الحصول على العدد الإجمالي
        $countStmt = $connection->prepare("
            SELECT COUNT(*) as total
            FROM offers o
            JOIN users u ON o.user_id = u.id
            WHERE $whereClause
        ");
        $countStmt->execute($params);
        $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // جلب العروض
        $validSortColumns = ['created_at', 'price', 'amount', 'rating', 'views_count', 'total_trades'];
        $sortBy = in_array($sortBy, $validSortColumns) ? $sortBy : 'created_at';
        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';

        // تحديد العمود للترتيب
        $orderColumn = $sortBy;
        if ($sortBy === 'rating') {
            $orderColumn = 'u.rating';
        } elseif ($sortBy === 'total_trades') {
            $orderColumn = 'u.total_trades';
        } else {
            $orderColumn = 'o.' . $sortBy;
        }
        
        $stmt = $connection->prepare("
            SELECT
                o.id,
                o.offer_type,
                o.amount,
                o.min_amount,
                o.max_amount,
                o.price,
                o.currency,
                o.stablecoin,
                o.payment_methods,
                o.terms,
                o.auto_reply,
                o.views_count,
                o.time_limit,
                o.is_premium,
                o.created_at,
                o.updated_at,
                (o.amount * o.price) as total_value,
                u.id as user_id,
                u.username,
                u.full_name,
                u.rating,
                u.total_trades,
                u.completed_trades,
                u.is_verified,
                u.last_login,
                u.country_code,
                u.profile_image,
                CASE
                    WHEN u.last_login > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 1
                    ELSE 0
                END as is_online
            FROM offers o
            JOIN users u ON o.user_id = u.id
            WHERE $whereClause
            ORDER BY $orderColumn $sortOrder
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        $offers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تنسيق البيانات
        foreach ($offers as &$offer) {
            $offer['payment_methods'] = json_decode($offer['payment_methods'], true) ?: [];
            $offer['amount'] = floatval($offer['amount']);
            $offer['min_amount'] = floatval($offer['min_amount']);
            $offer['max_amount'] = floatval($offer['max_amount']);
            $offer['price'] = floatval($offer['price']);
            $offer['total_value'] = floatval($offer['total_value']);
            $offer['views_count'] = intval($offer['views_count']);
            $offer['time_limit'] = intval($offer['time_limit']);
            $offer['is_premium'] = (bool)$offer['is_premium'];

            $offer['user'] = [
                'id' => $offer['user_id'],
                'username' => $offer['username'],
                'full_name' => $offer['full_name'],
                'rating' => floatval($offer['rating']),
                'total_trades' => intval($offer['total_trades']),
                'completed_trades' => intval($offer['completed_trades']),
                'is_verified' => (bool)$offer['is_verified'],
                'is_online' => (bool)$offer['is_online'],
                'country_code' => $offer['country_code'],
                'profile_image' => $offer['profile_image'],
                'last_seen' => $offer['last_login']
            ];

            // حساب معدل الإنجاز
            $offer['user']['completion_rate'] = $offer['user']['total_trades'] > 0
                ? round(($offer['user']['completed_trades'] / $offer['user']['total_trades']) * 100, 1)
                : 0;

            // إزالة البيانات المكررة
            unset($offer['user_id'], $offer['username'], $offer['full_name'],
                  $offer['rating'], $offer['total_trades'], $offer['completed_trades'],
                  $offer['is_verified'], $offer['last_login'], $offer['country_code'],
                  $offer['profile_image'], $offer['is_online']);
        }
        
        // إحصائيات إضافية
        $stats = [
            'total_offers' => intval($totalCount),
            'buy_offers' => 0,
            'sell_offers' => 0,
            'avg_price' => 0,
            'price_range' => ['min' => 0, 'max' => 0]
        ];

        if (!empty($offers)) {
            $buyCount = array_filter($offers, fn($o) => $o['offer_type'] === 'buy');
            $sellCount = array_filter($offers, fn($o) => $o['offer_type'] === 'sell');
            $prices = array_column($offers, 'price');

            $stats['buy_offers'] = count($buyCount);
            $stats['sell_offers'] = count($sellCount);
            $stats['avg_price'] = round(array_sum($prices) / count($prices), 2);
            $stats['price_range'] = [
                'min' => min($prices),
                'max' => max($prices)
            ];
        }

        echo json_encode([
            'success' => true,
            'data' => $offers,
            'pagination' => [
                'currentPage' => $page,
                'totalPages' => ceil($totalCount / $limit),
                'totalItems' => intval($totalCount),
                'itemsPerPage' => $limit,
                'hasNextPage' => $page < ceil($totalCount / $limit),
                'hasPrevPage' => $page > 1
            ],
            'stats' => $stats,
            'filters_applied' => [
                'type' => $offerType,
                'currency' => $currency,
                'stablecoin' => $stablecoin,
                'payment_method' => $paymentMethod,
                'verified_only' => $verifiedOnly,
                'premium_only' => $premiumOnly,
                'location' => $location,
                'search' => $search
            ]
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // إنشاء عرض جديد
        $rawInput = file_get_contents('php://input');
        $input = json_decode($rawInput, true);

        // تسجيل البيانات المستلمة للتشخيص
        error_log("Received POST data: " . $rawInput);

        if (!$input) {
            throw new Exception('بيانات غير صحيحة: ' . json_last_error_msg());
        }
        
        // تحديد اللغة من الهيدر
        $lang = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? 'ar';
        $isArabic = strpos($lang, 'ar') !== false;

        // رسائل الخطأ
        $messages = [
            'ar' => [
                'invalid_data' => 'بيانات غير صحيحة',
                'field_required' => 'الحقل مطلوب',
                'invalid_offer_type' => 'نوع العرض غير صحيح',
                'invalid_amount_price' => 'الكمية والسعر يجب أن يكونا أكبر من صفر',
                'user_not_found' => 'المستخدم غير موجود',
                'free_offers_disabled' => 'العروض المجانية غير مفعلة حالياً',
                'free_offers_limit_reached' => 'لقد وصلت للحد الأقصى من العروض المجانية',
                'amount_exceeds_max' => 'المبلغ يتجاوز الحد الأقصى للعروض المجانية',
                'amount_below_min' => 'المبلغ أقل من الحد الأدنى للعروض المجانية',
                'offer_created' => 'تم إنشاء العرض بنجاح'
            ],
            'en' => [
                'invalid_data' => 'Invalid data',
                'field_required' => 'Field is required',
                'invalid_offer_type' => 'Invalid offer type',
                'invalid_amount_price' => 'Amount and price must be greater than zero',
                'user_not_found' => 'User not found',
                'free_offers_disabled' => 'Free offers are currently disabled',
                'free_offers_limit_reached' => 'You have reached the maximum number of free offers',
                'amount_exceeds_max' => 'Amount exceeds the maximum limit for free offers',
                'amount_below_min' => 'Amount is below the minimum limit for free offers',
                'offer_created' => 'Offer created successfully'
            ]
        ];

        $msg = $messages[$isArabic ? 'ar' : 'en'];

        // التحقق من البيانات المطلوبة
        $requiredFields = ['user_id', 'offer_type', 'amount', 'price', 'currency'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                throw new Exception($msg['field_required'] . ": $field");
            }
        }

        // التحقق من صحة البيانات
        if (!in_array($input['offer_type'], ['buy', 'sell'])) {
            throw new Exception($msg['invalid_offer_type']);
        }

        if ($input['amount'] <= 0 || $input['price'] <= 0) {
            throw new Exception($msg['invalid_amount_price']);
        }
        
        // التحقق من وجود المستخدم
        $stmt = $connection->prepare("SELECT id FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$input['user_id']]);
        if (!$stmt->fetch()) {
            throw new Exception($msg['user_not_found']);
        }

        // التحقق من حدود العروض المجانية إذا كان العرض مجاني
        if (isset($input['is_free']) && $input['is_free']) {
            // جلب إعدادات العروض المجانية
            $stmt = $connection->prepare("
                SELECT setting_key, setting_value
                FROM platform_settings
                WHERE setting_key IN ('freeOffersEnabled', 'maxFreeOffersPerUser', 'maxFreeOfferAmount', 'freeOfferMinAmount')
            ");
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            // التحقق من تفعيل العروض المجانية
            if (!isset($settings['freeOffersEnabled']) || !$settings['freeOffersEnabled']) {
                throw new Exception($msg['free_offers_disabled']);
            }

            // التحقق من عدد العروض المجانية الحالية
            $maxFreeOffers = intval($settings['maxFreeOffersPerUser'] ?? 3);
            $stmt = $connection->prepare("
                SELECT COUNT(*) as current_count
                FROM offers
                WHERE user_id = ? AND is_free = 1 AND is_active = 1
            ");
            $stmt->execute([$input['user_id']]);
            $currentCount = $stmt->fetch(PDO::FETCH_ASSOC)['current_count'];

            if ($currentCount >= $maxFreeOffers) {
                throw new Exception($msg['free_offers_limit_reached'] . " ({$maxFreeOffers})");
            }

            // التحقق من حدود المبلغ
            $maxAmount = floatval($settings['maxFreeOfferAmount'] ?? 1000);
            $minAmount = floatval($settings['freeOfferMinAmount'] ?? 10);

            if ($input['amount'] > $maxAmount) {
                throw new Exception($msg['amount_exceeds_max'] . " ({$maxAmount} USDT)");
            }

            if ($input['amount'] < $minAmount) {
                throw new Exception($msg['amount_below_min'] . " ({$minAmount} USDT)");
            }
        }
        
        // إنشاء العرض مع دعم العقد الذكي والعروض المجانية
        $stmt = $connection->prepare("
            INSERT INTO offers (
                user_id, offer_type, amount, min_amount, max_amount,
                price, currency, stablecoin, payment_methods, terms,
                auto_reply, time_limit, is_free, blockchain_trade_id, contract_status,
                transaction_hash, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ");

        $paymentMethods = json_encode($input['payment_methods'] ?? []);

        // معلومات العقد الذكي (اختيارية)
        $blockchainTradeId = $input['blockchain_trade_id'] ?? null;
        $contractStatus = $input['contract_status'] ?? 'pending';
        $transactionHash = $input['transaction_hash'] ?? null;

        $stmt->execute([
            $input['user_id'],
            $input['offer_type'],
            $input['amount'],
            $input['min_amount'] ?? $input['amount'],
            $input['max_amount'] ?? $input['amount'],
            $input['price'],
            $input['currency'],
            $input['stablecoin'] ?? 'USDT',
            $paymentMethods,
            $input['terms'] ?? '',
            $input['auto_reply'] ?? '',
            $input['time_limit'] ?? 1800,
            $input['is_free'] ?? false,
            $blockchainTradeId,
            $contractStatus,
            $transactionHash
        ]);

        $offerId = $connection->lastInsertId();

        // إذا كان العرض مجاني، أضفه إلى جدول العروض المجانية وحدث العداد الشهري
        if (isset($input['is_free']) && $input['is_free']) {
            // جلب مدة صلاحية العرض المجاني
            $stmt = $connection->prepare("
                SELECT setting_value
                FROM platform_settings
                WHERE setting_key = 'freeOfferTimeLimit'
            ");
            $stmt->execute();
            $timeLimit = intval($stmt->fetchColumn() ?: 86400); // 24 ساعة افتراضياً

            $expiresAt = date('Y-m-d H:i:s', time() + $timeLimit);

            $stmt = $connection->prepare("
                INSERT INTO user_free_offers (user_id, offer_id, expires_at, created_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ");
            $stmt->execute([$input['user_id'], $offerId, $expiresAt]);

            // تحديث عداد الاستخدام الشهري
            require_once __DIR__ . '/check-free-offer-limits.php';
            incrementMonthlyUsage($connection, $input['user_id']);
        }

        // إذا تم توفير معرف العقد الذكي، قم بتحديث حالة المزامنة
        if ($blockchainTradeId) {
            $stmt = $connection->prepare("
                UPDATE offers
                SET sync_status = 'synced', contract_created_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $stmt->execute([$offerId]);
        }
        
        echo json_encode([
            'success' => true,
            'message' => $msg['offer_created'],
            'data' => ['id' => $offerId]
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);

    error_log('Database error in offers/index.php: ' . $e->getMessage());
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);

    error_log('General error in offers/index.php: ' . $e->getMessage());
}
?>
