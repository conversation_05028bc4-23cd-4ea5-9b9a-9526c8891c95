'use client';

import { useState, useEffect } from 'react';
import {
  Star,
  Filter,
  Search,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  User,
  Calendar,
  TrendingUp,
  Award,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet, handleApiError } from '@/utils/apiClient';

interface Review {
  id: string;
  reviewer: {
    username: string;
    isVerified: boolean;
    totalTrades: number;
  };
  rating: number;
  comment: string;
  tradeId: string;
  tradeType: 'buy' | 'sell';
  amount: number;
  currency: string;
  timestamp: string;
  isPositive: boolean;
  tags: string[];
}

interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  positiveReviews: number;
  negativeReviews: number;
  ratingDistribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

export default function ReviewsPage() {
  const { t, formatDate, formatCurrency } = useUserDashboardTranslation();
  const { user } = useAuth();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [stats, setStats] = useState<ReviewStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'positive' | 'negative'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // جلب التقييمات
  useEffect(() => {
    const fetchReviews = async () => {
      if (!user?.id) {
        setError('معرف المستخدم غير متاح');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // استدعاء API الحقيقي لجلب التقييمات المستلمة
        const response = await apiGet(`reviews/index.php?user_id=${user.id}&type=received&limit=20`);

        if (!response.success) {
          throw new Error(response.message || 'فشل في جلب التقييمات');
        }

        // تحويل البيانات من API إلى تنسيق Review
        const apiReviews = response.data || [];
        const formattedReviews: Review[] = apiReviews.map((review: any) => ({
          id: review.id.toString(),
          reviewer: {
            username: review.reviewer.username || 'مستخدم غير معروف',
            isVerified: Boolean(review.reviewer.is_verified),
            totalTrades: parseInt(review.reviewer.total_trades) || 0
          },
          rating: parseInt(review.rating) || 0,
          comment: review.comment || '',
          tradeId: review.trade_id || '',
          tradeType: review.trade_type || 'buy',
          amount: parseFloat(review.amount) || 0,
          currency: review.currency || 'USDT',
          timestamp: review.timestamp || new Date().toISOString(),
          isPositive: Boolean(review.is_positive),
          tags: review.tags || []
        }));

        setReviews(formattedReviews);

        // تحويل الإحصائيات
        if (response.stats) {
          const apiStats = response.stats;
          setStats({
            totalReviews: parseInt(apiStats.total_reviews) || 0,
            averageRating: parseFloat(apiStats.average_rating) || 0,
            positiveReviews: parseInt(apiStats.positive_reviews) || 0,
            negativeReviews: parseInt(apiStats.negative_reviews) || 0,
            ratingDistribution: {
              5: parseInt(apiStats.rating_distribution['5']) || 0,
              4: parseInt(apiStats.rating_distribution['4']) || 0,
              3: parseInt(apiStats.rating_distribution['3']) || 0,
              2: parseInt(apiStats.rating_distribution['2']) || 0,
              1: parseInt(apiStats.rating_distribution['1']) || 0
            }
          });
        }
      } catch (err) {
        const errorMessage = handleApiError(err);
        setError(errorMessage);
        console.error('Error fetching reviews:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchReviews();
  }, [user?.id]);

  // تصفية التقييمات
  const filteredReviews = reviews.filter(review => {
    const matchesFilter = filter === 'all' || 
                         (filter === 'positive' && review.isPositive) ||
                         (filter === 'negative' && !review.isPositive);
    const matchesSearch = review.reviewer.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.comment.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  // رسم النجوم
  const renderStars = (rating: number, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizeClasses = {
      sm: 'w-3 h-3',
      md: 'w-4 h-4',
      lg: 'w-5 h-5'
    };

    return (
      <div className="flex items-center space-x-1 rtl:space-x-reverse">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating
                ? 'text-yellow-400 fill-current'
                : 'text-gray-300 dark:text-gray-600'
            }`}
          />
        ))}
      </div>
    );
  };

  // عرض رسالة الخطأ
  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6">
          <div className="flex items-center gap-3">
            <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
            <div>
              <h3 className="text-red-800 dark:text-red-200 font-medium">خطأ في تحميل التقييمات</h3>
              <p className="text-red-600 dark:text-red-400 text-sm mt-1">{error}</p>
            </div>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            التقييمات والمراجعات
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            آراء المتداولين الآخرين عن تعاملك
          </p>
        </div>
      </div>

      {/* إحصائيات التقييمات */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <Star className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">متوسط التقييم</p>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stats.averageRating.toFixed(1)}
                  </p>
                  {renderStars(Math.round(stats.averageRating), 'sm')}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                <ThumbsUp className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">تقييمات إيجابية</p>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {stats.positiveReviews}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
                <ThumbsDown className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">تقييمات سلبية</p>
                <p className="text-2xl font-bold text-red-600 dark:text-red-400">
                  {stats.negativeReviews}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                <MessageSquare className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي التقييمات</p>
                <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {stats.totalReviews}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* المرشحات والبحث */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* البحث */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 rtl:right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="البحث في التقييمات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 rtl:pr-10 pr-3 rtl:pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* المرشحات */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Filter className="w-4 h-4 text-gray-500" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as typeof filter)}
                className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع التقييمات</option>
                <option value="positive">الإيجابية</option>
                <option value="negative">السلبية</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* قائمة التقييمات */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        {loading ? (
          <div className="p-6">
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded-lg" />
                </div>
              ))}
            </div>
          </div>
        ) : filteredReviews.length > 0 ? (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredReviews.map((review) => (
              <div key={review.id} className="p-6">
                <div className="flex items-start space-x-4 rtl:space-x-reverse">
                  {/* صورة المستخدم */}
                  <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                    <User className="w-6 h-6 text-gray-500 dark:text-gray-400" />
                  </div>

                  {/* محتوى التقييم */}
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {review.reviewer.username}
                        </h3>
                        {review.reviewer.isVerified && (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        )}
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          ({review.reviewer.totalTrades} صفقة)
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        {renderStars(review.rating)}
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {formatDate(review.timestamp, { 
                            day: 'numeric', 
                            month: 'short',
                            year: 'numeric'
                          })}
                        </span>
                      </div>
                    </div>

                    <p className="text-gray-700 dark:text-gray-300 mb-3">
                      {review.comment}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                        <span>
                          {review.tradeType === 'buy' ? 'شراء' : 'بيع'} {review.amount} {review.currency}
                        </span>
                        <span>•</span>
                        <span>صفقة #{review.tradeId}</span>
                      </div>

                      {review.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {review.tags.map((tag) => (
                            <span
                              key={tag}
                              className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-12 text-center">
            <Star className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              لا توجد تقييمات
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {filter === 'all' 
                ? 'لم تحصل على أي تقييمات بعد'
                : `لا توجد تقييمات ${filter === 'positive' ? 'إيجابية' : 'سلبية'}`
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
