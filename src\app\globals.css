/* تحسينات الهيدر */
@import '../styles/header-enhancements.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Light Theme Colors */
  --background: #ffffff;
  --foreground: #1f2937;
  --primary: #3b82f6;
  --primary-dark: #1d4ed8;
  --success: #22c55e;
  --warning: #f59e0b;
  --danger: #ef4444;
  --border: #e5e7eb;
  --card: #ffffff;
  --muted: #f9fafb;
  --header-bg: rgba(255, 255, 255, 0.95);
  --hero-bg: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --section-bg: #f9fafb;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
}

/* Dark Theme Colors */
.dark {
  --background: #0f172a;
  --foreground: #f1f5f9;
  --primary: #3b82f6;
  --primary-dark: #1d4ed8;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --border: #334155;
  --card: #1e293b;
  --muted: #475569;
  --header-bg: rgba(15, 23, 42, 0.95);
  --hero-bg: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  --section-bg: #0f172a;
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-cairo), 'Cairo', 'Noto Sans Arabic', 'Segoe UI', system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* دعم RTL */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .ltr {
  direction: ltr;
  text-align: left;
}

/* تحسينات للخطوط العربية */
.font-arabic {
  font-family: var(--font-cairo), 'Cairo', 'Noto Sans Arabic', 'Segoe UI', system-ui, sans-serif;
}

/* أحجام الخطوط المحسنة للعربية */
.text-xs-ar { font-size: 13px; line-height: 1.5; }
.text-sm-ar { font-size: 15px; line-height: 1.6; }
.text-base-ar { font-size: 17px; line-height: 1.7; }
.text-lg-ar { font-size: 19px; line-height: 1.7; }
.text-xl-ar { font-size: 21px; line-height: 1.6; }
.text-2xl-ar { font-size: 25px; line-height: 1.5; }
.text-3xl-ar { font-size: 31px; line-height: 1.4; }
.text-4xl-ar { font-size: 37px; line-height: 1.3; }

/* تحسينات للعناوين العربية */
.heading-arabic {
  font-family: var(--font-cairo), 'Cairo', 'Noto Sans Arabic', 'Segoe UI', system-ui, sans-serif;
  font-weight: 700;
  letter-spacing: -0.025em;
}

/* تحسينات للنصوص العربية */
.body-arabic {
  font-family: var(--font-cairo), 'Cairo', 'Noto Sans Arabic', 'Segoe UI', system-ui, sans-serif;
  font-weight: 400;
  line-height: 1.8;
}

/* تأثيرات الحركة */
.animate-fade-in {
  animation: fadeIn 0.8s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(40px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* تأثيرات التدرج */
.gradient-text {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-border {
  position: relative;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 12px;
  padding: 2px;
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 2px;
  background: white;
  border-radius: 10px;
  z-index: -1;
}

/* تحسينات للأزرار */
.btn {
  @apply px-6 py-3 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 relative overflow-hidden inline-flex items-center justify-center;
  font-family: var(--font-cairo), 'Cairo', 'Tajawal', system-ui, sans-serif;
  font-size: 16px;
  min-height: 44px; /* الحد الأدنى للمس */
}

.dark .btn {
  @apply focus:ring-offset-gray-800;
}

.btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover:before {
  left: 100%;
}

.btn-primary {
  @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 focus:ring-blue-500 shadow-lg hover:shadow-xl;
}

.dark .btn-primary {
  @apply from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700;
}

.btn-secondary {
  @apply bg-white text-gray-700 border-2 border-gray-300 hover:bg-gray-50 hover:border-gray-400 focus:ring-gray-500 shadow-sm hover:shadow-md;
}

.dark .btn-secondary {
  @apply bg-gray-700 text-gray-200 border-gray-600 hover:bg-gray-600 hover:border-gray-500;
}

.btn-success {
  @apply bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 focus:ring-green-500 shadow-lg hover:shadow-xl;
}

.dark .btn-success {
  @apply from-green-500 to-green-600 hover:from-green-600 hover:to-green-700;
}

.btn-danger {
  @apply bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 focus:ring-red-500 shadow-lg hover:shadow-xl;
}

.dark .btn-danger {
  @apply from-red-500 to-red-600 hover:from-red-600 hover:to-red-700;
}

.btn-outline-primary {
  @apply border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white focus:ring-blue-500;
}

.dark .btn-outline-primary {
  @apply border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-gray-900;
}

.btn-lg {
  @apply px-8 py-4 text-lg;
  min-height: 52px;
  font-size: 18px;
}

.btn-sm {
  @apply px-4 py-2 text-sm;
  min-height: 36px;
  font-size: 14px;
}

.btn-xl {
  @apply px-10 py-5 text-xl;
  min-height: 60px;
  font-size: 20px;
}

.btn-warning {
  @apply bg-gradient-to-r from-yellow-500 to-yellow-600 text-white hover:from-yellow-600 hover:to-yellow-700 focus:ring-yellow-500 shadow-lg hover:shadow-xl;
}

.dark .btn-warning {
  @apply from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-gray-900;
}

.btn-info {
  @apply bg-gradient-to-r from-cyan-500 to-cyan-600 text-white hover:from-cyan-600 hover:to-cyan-700 focus:ring-cyan-500 shadow-lg hover:shadow-xl;
}

.dark .btn-info {
  @apply from-cyan-400 to-cyan-500 hover:from-cyan-500 hover:to-cyan-600;
}

.btn-outline-secondary {
  @apply border-2 border-gray-400 text-gray-600 hover:bg-gray-400 hover:text-white focus:ring-gray-500;
}

.dark .btn-outline-secondary {
  @apply border-gray-500 text-gray-400 hover:bg-gray-500 hover:text-gray-900;
}

/* تحسينات للحقول */
.input-field {
  @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  font-family: var(--font-cairo), 'Cairo', 'Tajawal', system-ui, sans-serif;
  min-height: 44px; /* الحد الأدنى للمس */
}

.input-field:focus {
  @apply shadow-lg;
}

.input-field:disabled {
  @apply bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed;
}

.input-field.error {
  @apply border-red-500 focus:ring-red-500;
}

.dark .input-field {
  @apply shadow-sm;
}

.dark .input-field:focus {
  @apply shadow-lg;
}

/* تحسينات للكروت */
.card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 backdrop-blur-sm;
}

.card-hover {
  @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-2 hover:border-primary-200 dark:hover:border-primary-600;
}

.card-gradient {
  @apply bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900;
}

.card-featured {
  @apply border-2 border-primary-200 dark:border-primary-600 bg-gradient-to-br from-primary-50 to-white dark:from-primary-900/20 dark:to-gray-800 relative;
}

/* إصلاح مشكلة الأرقام الكبيرة */
.stats-card {
  @apply bg-white rounded-xl shadow-sm border border-gray-200 p-4 backdrop-blur-sm overflow-hidden;
  min-height: 120px;
}

.stats-number {
  @apply text-2xl font-bold text-gray-900 truncate;
  font-size: clamp(1.25rem, 4vw, 2rem);
  line-height: 1.2;
  word-break: break-all;
}

.stats-label {
  @apply text-sm text-gray-600 mt-1;
  font-size: clamp(0.75rem, 2.5vw, 0.875rem);
}

/* تحسينات خاصة بقسم المقارنة */
.comparison-mobile-card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 mb-4;
  transition: all 0.3s ease;
}

.comparison-mobile-card:hover {
  @apply shadow-xl;
  transform: translateY(-2px);
}

.comparison-feature-icon {
  @apply w-10 h-10 rounded-xl flex items-center justify-center;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}

.dark .comparison-feature-icon {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
}

.comparison-advantage-badge {
  @apply inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium;
  background: rgba(34, 197, 94, 0.1);
  color: rgb(34, 197, 94);
}

.dark .comparison-advantage-badge {
  background: rgba(34, 197, 94, 0.2);
  color: rgb(74, 222, 128);
}

.comparison-limitation-badge {
  @apply inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium;
  background: rgba(239, 68, 68, 0.1);
  color: rgb(239, 68, 68);
}

.dark .comparison-limitation-badge {
  background: rgba(239, 68, 68, 0.2);
  color: rgb(248, 113, 113);
}

/* تحسينات خاصة بقسم الأمان والثقة */
.trust-security-card {
  @apply bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 transition-all duration-300;
}

.trust-security-card:hover {
  @apply bg-white/10 shadow-xl;
  transform: translateY(-2px);
}

.trust-indicator-icon {
  @apply w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-xl lg:rounded-2xl flex items-center justify-center mx-auto shadow-lg;
  transition: all 0.3s ease;
}

.trust-indicator-icon:hover {
  transform: scale(1.1);
}

.trust-indicator-title {
  @apply text-lg sm:text-xl lg:text-2xl font-bold text-white mb-1 lg:mb-2;
  font-size: clamp(1rem, 3vw, 1.5rem);
}

.trust-indicator-description {
  @apply text-blue-200 text-xs sm:text-sm lg:text-base leading-relaxed;
  font-size: clamp(0.75rem, 2.5vw, 1rem);
}

/* انيميشن خاص للشهادات الأمنية */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.trust-float {
  animation: float 3s ease-in-out infinite;
}

.trust-float:nth-child(2) {
  animation-delay: 0.5s;
}

.trust-float:nth-child(3) {
  animation-delay: 1s;
}

.trust-float:nth-child(4) {
  animation-delay: 1.5s;
}

.card-featured:before {
  content: '';
  position: absolute;
  top: 0;
  width: 0;
  height: 0;
  border-style: solid;
}

[dir="rtl"] .card-featured:before {
  left: 0;
  border-width: 20px 0 0 20px;
  border-color: #3b82f6 transparent transparent transparent;
}

[dir="ltr"] .card-featured:before {
  right: 0;
  border-width: 0 20px 20px 0;
  border-color: transparent #3b82f6 transparent transparent;
}

.card-featured:after {
  content: '⭐';
  position: absolute;
  top: 2px;
  font-size: 10px;
  color: white;
}

[dir="rtl"] .card-featured:after {
  left: 2px;
}

[dir="ltr"] .card-featured:after {
  right: 2px;
}

/* تحسينات للنماذج */
.form-input {
  @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
  font-family: var(--font-cairo), 'Cairo', 'Tajawal', system-ui, sans-serif;
  font-size: 16px; /* منع التكبير في iOS */
}

/* تنسيق RTL/LTR للنماذج */
[dir="rtl"] .form-input {
  text-align: right;
}

[dir="ltr"] .form-input {
  text-align: left;
}

/* تحسينات خاصة للقوائم المنسدلة */
.form-select {
  @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
  font-family: var(--font-cairo), 'Cairo', 'Tajawal', system-ui, sans-serif;
  font-size: 16px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
}

[dir="rtl"] .form-select {
  text-align: right;
  background-position: left 0.5rem center;
  padding-left: 2.5rem;
  padding-right: 1rem;
}

[dir="ltr"] .form-select {
  text-align: left;
  background-position: right 0.5rem center;
  padding-right: 2.5rem;
  padding-left: 1rem;
}

.form-input::placeholder {
  @apply text-gray-400 dark:text-gray-500;
  font-family: var(--font-cairo), 'Cairo', 'Tajawal', system-ui, sans-serif;
}

.form-input:focus {
  @apply ring-2 ring-blue-500 border-blue-500 shadow-lg;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
  font-family: var(--font-cairo), 'Cairo', 'Tajawal', system-ui, sans-serif;
}

[dir="rtl"] .form-label {
  text-align: right;
}

[dir="ltr"] .form-label {
  text-align: left;
}

/* تحسينات للأيقونات في حقول الإدخال */
.input-with-icon {
  @apply relative;
}

.input-icon-right {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none;
}

.input-icon-left {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400;
}

.input-with-icon .form-input {
  @apply pr-12;
}

.input-with-icon.has-left-icon .form-input {
  @apply pl-12;
}

.input-with-icon .input-icon-right {
  @apply right-4;
}

.input-with-icon .input-icon-left {
  @apply left-4;
}

/* تحسينات للحالات */
.status-active {
  @apply bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium;
}

.status-pending {
  @apply bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium;
}

.status-cancelled {
  @apply bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium;
}

/* تحسينات للتخطيط */
.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.section-padding {
  @apply py-12 lg:py-20;
}

/* تحسينات للاستجابة */
@media (max-width: 640px) {
  .mobile-padding {
    @apply px-4;
  }
  
  .mobile-text {
    @apply text-sm;
  }
}

/* تحسينات للتمرير */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* تأثيرات التحميل */
.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

/* تحسينات للنصوص */
.text-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* تحسينات خاصة للنصوص العربية */
[dir="rtl"] .stablecoin-card {
  text-align: right;
  direction: rtl;
}

[dir="ltr"] .stablecoin-card {
  text-align: left;
  direction: ltr;
}

.stablecoin-card .coin-name {
  font-family: var(--font-cairo), 'Cairo', 'Tajawal', system-ui, sans-serif;
  font-weight: 600;
  line-height: 1.2;
}

.stablecoin-card .coin-description {
  font-family: var(--font-cairo), 'Cairo', 'Tajawal', system-ui, sans-serif;
  line-height: 1.4;
}

/* تحسينات التجاوب للصفحة الرئيسية */
@media (max-width: 640px) {
  /* تحسينات للجوال */
  .hero-title {
    font-size: 2rem !important;
    line-height: 1.2 !important;
  }

  .hero-subtitle {
    font-size: 1rem !important;
    line-height: 1.4 !important;
  }

  .hero-description {
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
  }

  .stats-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .trust-indicators {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 1rem !important;
  }

  .cta-buttons {
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  .bottom-stats {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .bottom-stats .divider {
    display: none !important;
  }
}

/* تحسينات إضافية للتجاوب المحسن */
@media (max-width: 480px) {
  /* تحسينات للشاشات الصغيرة جداً */
  .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* تحسين البطاقات على الجوالات */
  .mobile-card-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.75rem !important;
  }

  .mobile-card-padding {
    padding: 0.75rem !important;
  }

  .mobile-icon-size {
    width: 2.5rem !important;
    height: 2.5rem !important;
  }

  .mobile-text-sm {
    font-size: 0.75rem !important;
    line-height: 1.2 !important;
  }

  .mobile-text-base {
    font-size: 0.875rem !important;
    line-height: 1.3 !important;
  }

  .mobile-text-lg {
    font-size: 1rem !important;
    line-height: 1.4 !important;
  }

  /* تحسين المسافات */
  .mobile-spacing-sm {
    margin-bottom: 0.5rem !important;
  }

  .mobile-spacing-md {
    margin-bottom: 0.75rem !important;
  }

  .mobile-spacing-lg {
    margin-bottom: 1rem !important;
  }
}

/* تحسينات للأجهزة اللوحية */
@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-grid-2 {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .tablet-grid-3 {
    grid-template-columns: repeat(3, 1fr) !important;
  }

  .tablet-padding {
    padding: 1.5rem !important;
  }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1025px) {
  .desktop-grid-4 {
    grid-template-columns: repeat(4, 1fr) !important;
  }

  .desktop-grid-5 {
    grid-template-columns: repeat(5, 1fr) !important;
  }

  .desktop-padding {
    padding: 2rem !important;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  /* تحسينات للتابلت */
  .hero-title {
    font-size: 2.5rem !important;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .trust-indicators {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  /* تحسينات للتابلت الكبير */
  .stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .trust-indicators {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

/* تحسينات التجاوب الإضافية */
@layer utilities {
  /* تحسينات عامة للتجاوب */
  .responsive-container {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .responsive-text-xl {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  .responsive-text-2xl {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  .responsive-text-3xl {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  .responsive-text-4xl {
    @apply text-3xl sm:text-4xl lg:text-5xl;
  }

  .responsive-text-5xl {
    @apply text-4xl sm:text-5xl lg:text-6xl;
  }

  /* تحسينات للشبكات */
  .responsive-grid-2 {
    @apply grid grid-cols-1 sm:grid-cols-2;
  }

  .responsive-grid-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  .responsive-grid-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4;
  }

  /* تحسينات للأزرار */
  .responsive-button {
    @apply px-4 py-2 sm:px-6 sm:py-3 lg:px-8 lg:py-4;
    @apply text-sm sm:text-base lg:text-lg;
  }

  .responsive-button-icon {
    @apply w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6;
  }

  /* تحسينات للمسافات */
  .responsive-gap {
    @apply gap-4 sm:gap-6 lg:gap-8;
  }

  .responsive-margin {
    @apply mb-6 sm:mb-8 lg:mb-12;
  }

  .responsive-padding {
    @apply py-8 sm:py-12 lg:py-16;
  }
}

[dir="rtl"] .stablecoin-card .coin-name,
[dir="rtl"] .stablecoin-card .coin-description {
  text-align: right;
  direction: rtl;
}

[dir="ltr"] .stablecoin-card .coin-name,
[dir="ltr"] .stablecoin-card .coin-description {
  text-align: left;
  direction: ltr;
}

/* تحسينات إضافية للنصوص العربية */
[dir="rtl"] .stablecoin-card {
  unicode-bidi: embed;
}

[dir="rtl"] .stablecoin-card * {
  text-align: right !important;
}

[dir="ltr"] .stablecoin-card * {
  text-align: left !important;
}

/* تحسين خاص لرمز العملة */
.stablecoin-card .coin-symbol {
  font-family: 'Inter', system-ui, sans-serif;
  font-weight: 700;
  letter-spacing: 0.5px;
}

/* تحسينات إضافية للوضع المظلم */
.dark .form-input {
  background-color: #1e293b;
  border-color: #475569;
  color: #f1f5f9;
}

.dark .form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .form-select {
  background-color: #1e293b;
  border-color: #475569;
  color: #f1f5f9;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23cbd5e1' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

.dark .form-select:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .form-label {
  color: #cbd5e1;
}

/* تحسينات الأزرار للوضع المظلم */
.dark .btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* تحسينات الكروت للوضع المظلم */
.dark .card-hover:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* تحسينات للتباين في الوضع المظلم */
.dark .stats-card {
  background-color: #1e293b;
  border-color: #475569;
}

.dark .stats-number {
  color: #f1f5f9;
}

.dark .stats-label {
  color: #cbd5e1;
}

/* تحسينات للحالات في الوضع المظلم */
.dark .status-active {
  background-color: rgba(34, 197, 94, 0.2);
  color: #4ade80;
}

.dark .status-pending {
  background-color: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.dark .status-cancelled {
  background-color: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

/* تحسينات للتفاعل */
.interactive-element {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-element:hover {
  transform: translateY(-1px);
}

.interactive-element:active {
  transform: translateY(0);
}

/* تحسينات للظلال */
.shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.dark .shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.3), 0 10px 20px -2px rgba(0, 0, 0, 0.2);
}

/* تحسينات للصفحات الجديدة */

/* تحسينات للدردشة */
.chat-message {
  @apply max-w-xs lg:max-w-md px-4 py-2 rounded-lg;
}

.chat-message.sent {
  @apply bg-blue-500 text-white ml-auto;
}

.chat-message.received {
  @apply bg-white dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-600;
}

/* تحسينات للنماذج */
.form-input {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white;
}

.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none;
}

/* تحسينات للأزرار */
.btn-primary {
  @apply px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium;
}

.btn-secondary {
  @apply px-4 py-2 text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors;
}

.btn-danger {
  @apply px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium;
}

/* تحسينات للبطاقات */
.card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700;
}

.card-header {
  @apply p-6 border-b border-gray-200 dark:border-gray-700;
}

.card-body {
  @apply p-6;
}

/* تحسينات للحالات */
.status-online {
  @apply w-2 h-2 bg-green-500 rounded-full;
}

.status-offline {
  @apply w-2 h-2 bg-gray-400 rounded-full;
}

.status-verified {
  @apply text-green-500;
}

/* تحسينات للتنبيهات */
.alert {
  @apply p-4 rounded-lg border;
}

.alert-warning {
  @apply bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200;
}

.alert-success {
  @apply bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-200;
}

.alert-error {
  @apply bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200;
}

.alert-info {
  @apply bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200;
}

/* تحسينات للتداول */
.trade-step {
  @apply flex items-center space-x-2 space-x-reverse p-3 rounded-lg border;
}

.trade-step.active {
  @apply bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800;
}

.trade-step.completed {
  @apply bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800;
}

.trade-step.pending {
  @apply bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600;
}

/* تحسينات للإحصائيات */
.stat-card {
  @apply text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg;
}

.stat-value {
  @apply text-lg font-bold text-gray-900 dark:text-white;
}

.stat-label {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* تحسينات إضافية للتجاوب - صفحة إنشاء العرض */
@media (max-width: 640px) {
  /* تحسين أزرار نوع العرض على الشاشات الصغيرة */
  .offer-type-button {
    padding: 0.75rem !important;
    font-size: 0.875rem !important;
  }

  /* تحسين الخطوات على الشاشات الصغيرة */
  .progress-step {
    flex-direction: column !important;
    text-align: center !important;
  }

  /* تحسين النماذج على الشاشات الصغيرة */
  .form-input {
    font-size: 16px !important; /* منع التكبير على iOS */
  }

  /* تحسين أزرار التنقل */
  .navigation-buttons {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  /* تحسين معاينة العرض */
  .offer-preview {
    position: static !important;
    margin-bottom: 1.5rem !important;
  }

  /* تحسين الشبكات */
  .amount-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }
}

/* تحسينات للأجهزة اللوحية الصغيرة */
@media (min-width: 641px) and (max-width: 768px) {
  .amount-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* تحسينات لمكون اختيار العملة */
.currency-selector {
  position: relative;
}

.currency-selector .dropdown-arrow {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 1;
}

[dir="rtl"] .currency-selector .dropdown-arrow {
  right: auto;
  left: 1rem;
}

/* تحسين خاص لموضع السهم في مكون العملة */
.currency-selector-button {
  position: relative;
  padding-right: 3rem !important; /* مساحة للسهم */
}

[dir="rtl"] .currency-selector-button {
  padding-right: 1rem !important;
  padding-left: 3rem !important;
}

.currency-dropdown-arrow {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 2;
}

[dir="rtl"] .currency-dropdown-arrow {
  right: auto;
  left: 0.75rem;
}

/* تحسين عرض الأرقام */
.number-display {
  font-family: 'Inter', 'Roboto', system-ui, sans-serif !important;
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.025em;
}

/* تحسين التحويل */
.conversion-display {
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.dark .conversion-display {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

/* تحسينات طبقة القفل للمستخدمين غير المسجلين */
.auth-overlay {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-out;
}

.auth-modal {
  animation: slideUp 0.4s ease-out;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.dark .auth-modal {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* تحسين تجاوب طبقة القفل */
@media (max-width: 640px) {
  .auth-modal {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }
}

/* تحسينات خاصة للحقول مع رموز العملة */
.input-with-currency {
  position: relative;
}

.input-with-currency .form-input {
  padding-right: 4rem;
}

[dir="rtl"] .input-with-currency .form-input {
  padding-right: 1rem;
  padding-left: 4rem;
}

.currency-symbol {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  font-size: 0.875rem;
  pointer-events: none;
  z-index: 1;
}

[dir="rtl"] .currency-symbol {
  right: auto;
  left: 0.75rem;
}

.dark .currency-symbol {
  color: #9ca3af;
}

/* تحسينات شارات الجودة */
.payment-method-card .w-4 {
  transition: all 0.3s ease;
  animation: fadeInScale 0.5s ease-out;
}

.payment-method-card:hover .w-4 {
  transform: scale(1.1);
  filter: brightness(1.1);
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* تحسينات بطاقات طرق الدفع الجديدة */
.payment-method-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  min-height: 140px;
}

.payment-method-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.dark .payment-method-card:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.payment-method-card.selected {
  animation: selectedGlow 0.6s ease-out;
}

/* فئات مساعدة */
.scale-102 {
  transform: scale(1.02);
}

/* تأثير التحديد */
@keyframes selectedGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* تحسين الشبكة للتجاوب */
@media (max-width: 768px) {
  .payment-method-card {
    min-height: 120px;
  }
}

@media (max-width: 640px) {
  .payment-method-card {
    min-height: 100px;
  }
}



@keyframes selectedPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

/* شريط الشعبية المتحرك */
.popularity-bar {
  animation: fillBar 1s ease-out 0.5s both;
}

@keyframes fillBar {
  from {
    width: 0%;
  }
  to {
    width: var(--target-width);
  }
}
