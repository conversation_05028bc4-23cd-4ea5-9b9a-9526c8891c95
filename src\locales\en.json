{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "view": "View", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "copy": "Copy", "copied": "<PERSON>pied", "download": "Download", "upload": "Upload", "submit": "Submit", "reset": "Reset", "online": "Online", "offline": "Offline", "minute": "minute", "minutes": "minutes", "hour": "hour", "hours": "hours", "day": "day", "days": "days", "week": "week", "weeks": "weeks", "months": "months", "years": "years", "all": "All", "none": "None", "yes": "Yes", "no": "No", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "warning": "Warning", "info": "Information", "filters": "Filters", "retry": "Retry", "seller": "<PERSON><PERSON>", "buyer": "Buyer", "viewDetails": "View Details", "moreActions": "More Actions", "hideSensitiveData": "Hide Sensitive Data", "showSensitiveData": "Show Sensitive Data", "details": "Details", "messages": "Messages", "notes": "Notes", "history": "History", "buy": "Buy", "sell": "<PERSON>ll", "username": "Username", "wallet": "Wallet", "rating": "Rating", "trades": "Trades", "export": "Export", "perPage": "Per Page", "dateFrom": "Date From", "dateTo": "Date To", "clearFilters": "Clear Filters", "selected": "Selected", "selectAll": "Select All", "showing": "Showing", "of": "of", "results": "results", "unknown": "Unknown", "goToHome": "Go to Home", "notAvailable": "Not Available", "justNow": "Just now", "page": "Page", "apply": "Apply", "goToAdminLogin": "Go to <PERSON><PERSON>", "superior": "Superior", "limited": "Limited", "step": "Step"}, "time": {"justNow": "Just now", "minutesAgo": "{{count}} minutes ago", "hoursAgo": "{{count}} hours ago", "daysAgo": "{{count}} days ago", "weeksAgo": "{{count}} weeks ago", "monthsAgo": "{{count}} months ago", "yearsAgo": "{{count}} years ago"}, "navigation": {"home": "Home", "offers": "Offers", "dashboard": "Dashboard", "wallet": "Wallet", "support": "Support", "login": "<PERSON><PERSON>", "register": "Sign Up", "logout": "Logout", "settings": "Settings", "reviews": "Reviews", "admin": "Admin", "howItWorks": "How It Works", "createOffer": "Create Offer", "trades": "Trades", "transactions": "Transactions", "dashboardDesc": "Manage your account and track activity", "tradesDesc": "Track your trades and trading history", "settingsDesc": "Account and security settings", "createOfferDesc": "Create new offers for buying and selling", "offersDesc": "Browse and manage available offers", "walletDesc": "Manage your wallet and balances", "reviewsDesc": "View and manage reviews", "notificationsDesc": "Manage notifications and alerts", "menu": "<PERSON><PERSON>"}, "header": {"title": "IKAROS P2P", "subtitle": "Digital Currency Exchange Platform", "notifications": "Notifications", "profile": "Profile", "language": "Language", "theme": "Theme", "lightMode": "Light Mode", "darkMode": "Dark Mode", "user": "User", "verified": "Verified", "welcome": "Welcome", "selectLanguage": "Select Language"}, "userDashboard": {"title": "Dashboard", "welcome": "Welcome, {{name}}", "overview": "Overview", "quickActions": "Quick Actions", "recentActivity": "Recent Activity", "statistics": "Statistics", "totalTrades": "Total Trades", "completedTrades": "Completed Trades", "totalVolume": "Total Volume", "averageRating": "Average Rating", "activeOffers": "Active Offers", "pendingTrades": "Pending Trades", "verified": "Verified", "unverified": "Unverified", "stats": {"overview": "Overview", "description": "Comprehensive summary of your activity and statistics", "totalTrades": "Total Trades", "totalVolume": "Total Volume", "rating": "Rating", "completionRate": "Completion Rate"}, "profile": {"title": "Profile", "noEmail": "No email", "memberSince": "Member since", "editProfile": "Edit Profile", "notifications": "Notifications"}, "wallet": {"title": "Wallet"}, "activity": {"title": "Recent Activity", "noActivity": "No activity yet", "startTrading": "Start Trading"}}, "home": {"hero": {"badge": "Secure & Licensed Web3 Platform", "title": "Stablecoin Exchange", "titleHighlight": "with Complete Safety & Trust", "subtitle": "IKAROS P2P - The leading platform for USDT trading with individuals using smart contracts on BSC network", "description": "Discover the world of secure decentralized trading with smart contract guarantees and the best market prices", "ctaPrimary": "Start Trading Now", "ctaSecondary": "Explore Platform", "stats": {"title": "Live Platform Statistics", "totalTrades": "Total Trades", "totalUsers": "Active Users", "totalVolume": "Trading Volume", "averageRating": "Average Rating", "activeOffers": "Active Offers", "onlineUsers": "Online Now"}, "features": {"smartContracts": "Audited Smart Contracts", "rating": "Rating 4.9/5", "global": "Available Globally"}, "liveUpdate": "Live Statistics Update"}, "whyIkaros": {"title": "Why Choose IKAROS P2P?", "subtitle": "Outperform competitors with exclusive features", "description": "Comprehensive comparison showing our platform's superiority over traditional platforms", "comparison": {"title": "Comparison with Other Platforms", "ikaros": "IKAROS P2P", "others": "Other Platforms", "feature": "Feature", "ikarosDesc": "Our Advanced Platform", "othersDesc": "Traditional Platforms", "features": {"smartContracts": {"title": "Smart Contracts", "ikaros": "Complete protection with smart contracts", "others": "Limited or no protection"}, "fees": {"title": "Fees", "ikaros": "Lowest fees in the market", "others": "High and hidden fees"}, "speed": {"title": "Speed", "ikaros": "Instant transactions", "others": "Slow transactions"}, "security": {"title": "Security", "ikaros": "Advanced security with BSC", "others": "Traditional security"}, "support": {"title": "Support", "ikaros": "24/7 specialized support", "others": "Limited support"}, "verification": {"title": "Verification", "ikaros": "Quick and secure verification", "others": "Complex procedures"}}}, "tryDifference": "Try the Difference Yourself", "joinThousands": "Join thousands of traders who chose the best", "startTradingFree": "Start Trading for Free", "stats": {"successRate": "Success Rate", "averageTime": "15 minutes", "averageTimeLabel": "Average Trade Time", "userRating": "User Rating"}, "limitedOffer": "Exclusive Offer for New Users", "smartContractsTitle": "Advanced Smart Contracts", "smartContractsDesc": "Complete fund protection", "feesTitle": "Low Fees", "feesDesc": "Lowest fees in the market", "speedTitle": "Superior Speed", "speedDesc": "Instant transactions", "securityTitle": "Advanced Security", "securityDesc": "Multi-level protection", "supportTitle": "Continuous Support", "supportDesc": "24/7 support team"}, "features": {"title": "IKAROS Platform Features", "subtitle": "Advanced technologies for exceptional trading experience", "description": "Discover exclusive features that make our platform the first choice for traders", "list": {"smartContracts": {"title": "Smart Contracts", "description": "Complete protection for your funds with audited smart contracts on BSC network", "benefits": ["100% Protection", "Complete Transparency", "Automatic Execution"]}, "lowFees": {"title": "Low Fees", "description": "Lowest fees in the market with complete transparency in costs", "benefits": ["Only 0.5% fees", "No hidden fees", "Discounts for active traders"]}, "fastTransactions": {"title": "Fast Transactions", "description": "Instant trade execution with fastest processing times", "benefits": ["Instant execution", "Quick confirmation", "Available 24/7"]}, "multiCurrency": {"title": "Multi-Currency", "description": "Support for various local currencies and different payment methods", "benefits": ["20+ local currencies", "Various payment methods", "Bank transfers"]}, "secureChat": {"title": "Secure <PERSON>", "description": "Encrypted chat system for secure communication with the other party", "benefits": ["End-to-end encryption", "Secure messages", "File sharing"]}, "support247": {"title": "24/7 Support", "description": "Specialized support team available around the clock to help you", "benefits": ["24/7 support", "Quick response", "Instant solutions"]}}, "keyFeatures": "Key Features", "tryFeature": "Try This Feature Now", "exclusiveFeature": "Exclusive Feature", "quickStats": {"fundProtection": "Fund Protection", "lowFees": "Low Fees", "continuousSupport": "Continuous Support", "satisfiedUsers": "Satisfied Users"}}, "testimonials": {"title": "What Our Customers Say?", "subtitle": "Real experiences from satisfied users", "description": "Read real user experiences and discover why they trust our platform", "reviews": {"review1": {"name": "<PERSON>", "role": "Active Trader", "rating": 5, "comment": "Best P2P trading platform I've used. Security and speed are unmatched.", "trades": "200+ trades", "location": "Riyadh, Saudi Arabia"}, "review2": {"name": "<PERSON><PERSON>", "role": "Investor", "rating": 5, "comment": "Smart contracts give complete confidence. Haven't faced any issues in over 150 trades.", "trades": "150+ trades", "location": "Dubai, UAE"}, "review3": {"name": "<PERSON>", "role": "Professional Trader", "rating": 5, "comment": "Low fees and high speed made me move all my trading to this platform.", "trades": "500+ trades", "location": "Cairo, Egypt"}}, "stats": {"averageRating": "Average Rating", "satisfiedUsers": "Satisfied Users", "satisfactionRate": "Satisfaction Rate", "successfulTrades": "Successful Trades"}, "autoPlay": "Auto-play Reviews"}, "trustSecurity": {"title": "Who Do You Trust?", "subtitle": "Advanced security and complete trust", "description": "Learn about the security and protection levels we provide for you", "features": {"blockchain": {"title": "Blockchain Technology", "description": "Using secure and reliable BSC network", "details": "Audited and open-source smart contracts"}, "encryption": {"title": "Advanced Encryption", "description": "Protecting all data with latest encryption technologies", "details": "AES-256 encryption and SSL/TLS"}, "verification": {"title": "Comprehensive Verification", "description": "Multi-level verification system for users", "details": "KYC and identity and phone verification"}, "insurance": {"title": "Fund Insurance", "description": "Insurance fund to protect user funds", "details": "Comprehensive insurance against risks"}}, "stats": {"smartContracts": "Audited Smart Contracts", "encryptionLevel": "Encryption Level", "verifiedUsers": "Verified Users", "insuranceCoverage": "Insurance Coverage"}, "status": {"audited": "Audited", "connected": "Connected", "identityVerified": "Identity Verified"}, "insurance": {"comprehensive": "Comprehensive Insurance Coverage", "fullProtection": "Complete protection against all potential risks"}, "badge": {"certified": "Certified & Trusted"}, "quickStats": {"fundProtection": "Fund Protection", "advancedEncryption": "Advanced Encryption", "verifiedUsers": "Verified Users", "comprehensiveInsurance": "Comprehensive Insurance"}, "hardcodedValues": {"bscNetwork": "BSC Network", "fundProtectionLabel": "Fund Protection", "dataEncryption": "Data Encryption", "sslTls": "SSL/TLS", "kycVerification": "KYC Verification"}}, "howItWorks": {"title": "How Does IKAROS Platform Work?", "subtitle": "Simple and secure process in just three steps", "description": "Learn how to use the platform easily and safely", "steps": {"step1": {"title": "Choose the Right Offer", "description": "Browse available offers and choose the best one based on price, payment method, and rating", "details": "Advanced filtering by amount, currency, and payment method"}, "step2": {"title": "Complete Payment Process", "description": "Transfer the amount to the other party and confirm the transfer completion on the platform", "details": "Secure chat system to coordinate the transfer process"}, "step3": {"title": "Receive Your Currency Instantly", "description": "After the other party's confirmation, you'll receive your currency in your wallet automatically", "details": "Smart contract ensures safe fund release"}}, "startFirstTrade": "Start Your First Trade Now", "step1": {"feature1": "Advanced Filtering", "feature2": "Price Comparison", "feature3": "User Reviews", "feature4": "Multiple Payment Methods"}, "step2": {"feature1": "Secure <PERSON>", "feature2": "Payment Confirmation", "feature3": "Smart Contract Protection", "feature4": "Instant Support"}, "step3": {"feature1": "Auto Release", "feature2": "Instant Confirmation", "feature3": "Live Notifications", "feature4": "Complete Record"}, "demo": {"step1": {"title": "Browse Available Offers", "description": "Use advanced filters to find the best offers", "searchPlaceholder": "Search for USDT offers...", "trustedSeller": "Trusted Seller", "rating": "Rating 4.9 ⭐", "perUSDT": "per USDT"}, "step2": {"title": "Communicate and Complete Payment", "description": "Use secure chat to coordinate the transfer process", "message1": "Hello, I want to buy 1000 USDT", "message2": "Welcome, I'll send you the account details", "message3": "Account number: **********", "placeholder": "Type your message..."}, "step3": {"title": "Receive Your Crypto Instantly", "description": "Smart contract automatically releases funds after confirmation", "successTitle": "Trade Completed Successfully!", "successMessage": "1000 USDT has been transferred to your wallet", "balance": "USDT Balance"}}}, "platformStats": {"title": "Platform Statistics", "subtitle": "Real numbers reflecting user trust", "description": "Watch the platform's growth and increasing user trust", "metrics": {"totalTrades": {"label": "Total Trades", "value": "50,000+", "growth": "+25% this month"}, "totalUsers": {"label": "Registered Users", "value": "12,500+", "growth": "+18% this month"}, "totalVolume": {"label": "Trading Volume", "value": "$2.5M+", "growth": "+32% this month"}, "successRate": {"label": "Success Rate", "value": "99.8%", "growth": "Stable"}, "averageTime": {"label": "Average Trade Time", "value": "15 minutes", "growth": "-20% improvement", "suffix": " minutes"}, "onlineUsers": {"label": "Online Now", "value": "1,250+", "growth": "Live"}}, "features": {"smartContracts": {"title": "Audited Smart Contracts", "description": "Complete fund protection"}, "fastTransactions": {"title": "Instant Transactions", "description": "Fast and secure execution"}, "awards": {"title": "Awards & Recognition", "description": "Globally recognized"}, "globalCoverage": {"title": "Global Coverage", "description": "Available in 50+ countries"}}, "liveActivity": {"title": "Live Platform Activity", "newTrades": "New Trades", "newUsers": "New Users", "tradingVolume": "Trading Volume", "lastMinute": "in the last minute", "lastHour": "in the last hour"}, "stable": "Stable", "quickStats": {"fundProtection": "Fund Protection", "lowFees": "Low Fees", "continuousSupport": "Continuous Support", "satisfiedUsers": "Satisfied Users"}, "secureProtection": "100% Secure", "secureProtectionDesc": "Complete fund protection", "fastTransactions": "Fast", "fastTransactionsDesc": "Instant transactions", "trustedPlatform": "Trusted", "trustedPlatformDesc": "50,000+ users", "profitableTrading": "Profitable", "profitableTradingDesc": "Best prices"}, "offers": {"title": "Latest Offers", "subtitle": "Diverse offers from trusted traders", "viewAll": "View All Offers", "noOffers": "No offers available at the moment"}, "cta": {"title": "Start Your Safe Trading Journey", "subtitle": "Join thousands of successful traders", "description": "Don't miss the opportunity to join the largest P2P trading community in the Arab region", "primaryButton": "Create Free Account", "secondaryButton": "Browse Offers", "features": ["Free registration with no fees", "Welcome bonus for new users", "Specialized technical support", "Safe and guaranteed trading"], "exclusiveBadge": "Exclusive Offer for New Users", "trustIndicators": {"secure": {"title": "100% Secure", "description": "Complete fund protection"}, "fast": {"title": "Fast", "description": "Instant transactions"}, "trusted": {"title": "Trusted", "description": "50,000+ users"}, "profitable": {"title": "Profitable", "description": "Best prices"}}, "bottomStats": {"successfulTrades": "successful trades", "satisfiedUsers": "satisfied users", "userRating": "user rating", "successRate": "success rate"}, "urgencyIndicator": "Limited offer - Join now and get welcome bonus"}}, "wallet": {"title": "Wallet", "subtitle": "Manage your wallet and balances", "notConnected": {"title": "Wallet not connected", "description": "Please connect your wallet to continue"}, "connecting": "Connecting", "disconnected": "Disconnected", "checking": "Checking wallet...", "status": "Status", "address": "Address", "network": "Network", "balance": "Balance", "totalBalance": "Total Balance", "balances": "Balances", "transactions": "Transactions", "history": "History", "noTransactions": "No transactions", "noTransactionsDesc": "You haven't made any transactions yet", "transactionHash": "Transaction Hash", "amount": "Amount", "type": "Type", "date": "Date", "confirmed": "Confirmed", "pending": "Pending", "failed": "Failed", "disconnect": "Disconnect", "addressCopied": "Address copied", "copyFailed": "Failed to copy", "copyAddress": "Copy Address", "viewInExplorer": "View in Explorer", "clickToConnect": "Click to connect", "details": "Details", "refresh": "Refresh", "refreshed": "Refreshed", "refreshing": "Refreshing", "refreshFailed": "Refresh failed", "connected": {"title": "Wallet Connected", "disconnect": "Disconnect", "address": "Address", "balance": "Balance", "network": "Network", "copy": "Copy", "copyAddress": "Copy Address", "viewOnExplorer": "View on Explorer"}, "disconnectFailed": "Failed to disconnect", "connectionFailed": "Connection failed", "explorer": "Explorer", "error": "Error", "noWalletsFound": "No installed wallets found", "downloadMetaMask": "Download MetaMask", "refreshBalances": "Refresh Balances", "connect": {"title": "Connect Your Digital Wallet", "description": "Connect your wallet to start secure trading on IKAROS P2P platform", "button": "Connect Wallet", "chooseWallet": "<PERSON><PERSON>", "metamask": "MetaMask", "walletConnect": "WalletConnect", "trustWallet": "Trust Wallet", "available": "Available", "notInstalled": "Not Installed", "connectAnyWallet": "Connect any wallet", "noWallet": "Don't have a digital wallet?", "learnHow": "Learn how to create a wallet"}, "metamask": {"description": "The most popular Web3 wallet"}, "trustwallet": {"description": "Secure multi-currency wallet"}, "errors": {"notInstalled": "MetaMask is not installed. Please install MetaMask first.", "userRejected": "Connection request was rejected by user", "connectionFailed": "Failed to connect to wallet", "networkError": "Network error", "insufficientBalance": "Insufficient balance", "transactionFailed": "Transaction failed", "unsupportedNetwork": "Unsupported network", "metamaskNotInstalled": "MetaMask is not installed. Please install MetaMask from https://metamask.io", "trustwalletNotInstalled": "Trust Wallet is not installed. Please install Trust Wallet or use MetaMask", "noWalletInstalled": "No digital wallet installed. Please install MetaMask or Trust Wallet", "walletNotAvailable": "Wallet is not available. Please reload the page and try again", "unknownError": "Unknown wallet error"}, "متاح_2cd2": "Availableة", "متصل_37ef": "Connectedة", "مضاف_a8f0": "Added", "مدعو_47cc": "Supported", "تثبي_5725": "Install", "aي_75ef": "Any", "والم_f3c4": "And Try"}, "trade": {"status": {"created": "Created", "buyerJoined": "Buyer Joined", "paymentSent": "Payment Sent", "completed": "Completed", "cancelled": "Cancelled", "disputed": "Disputed", "pending": "Pending", "payment_confirmed": "Payment Confirmed"}, "actions": {"confirmPayment": "Confirm Payment", "markPaid": "<PERSON> as <PERSON><PERSON>", "cancel": "Cancel Trade", "dispute": "Open Dispute", "complete": "Complete Trade"}, "loading_9cd6": "Loading", "verification_a4e0": "Verification", "authentication_1378": "Authentication", "من_99fb": "From", "حالا_013e": "States", "قطري_9086": "Qatari", "دينا_1801": "<PERSON><PERSON>", "بحري_1815": "Bahraini"}, "trades": {"title": "Trades", "subtitle": "Track and manage all your trades", "all": "All Trades", "active": "Active", "completed": "Completed", "cancelled": "Cancelled", "disputed": "Disputed", "noTrades": {"title": "No Trades Found", "description": "You haven't made any trades yet or no trades match your filters", "clearFilters": "Clear Filters"}, "noTradesDesc": "You haven't made any trades yet", "tradeId": "Trade ID", "partner": "Partner", "type": "Type", "amount": "Amount", "status": {"${trade": {}, "created": "Created", "joined": "Joined", "paymentSent": "Payment Sent", "completed": "Completed", "cancelled": "Cancelled", "disputed": "Disputed"}, "date": "Date", "viewDetails": "View Details", "table": {"you": "You", "title": "My Trades", "myTrades": "My Trades", "myVolume": "My Volume", "headers": {"id": "Trade ID", "participants": "Participants", "amount": "Amount", "status": "Status", "date": "Date", "actions": "Actions"}, "seller": "<PERSON><PERSON>", "buyer": "Buyer"}, "messages": {"dataRefreshed": "Trade data refreshed"}, "wallet": {"notConnected": "Wallet Not Connected", "notConnectedDesc": "Connect your wallet to view trade details", "connect": "Connect Wallet"}, "stats": {"total": "Total Trades", "active": "Active Trades", "completed": "Completed Trades", "cancelled": "Cancelled Trades", "disputed": "Disputed Trades", "volume": "Total Volume"}, "filters": {"title": "Trade Filters", "allStatuses": "All Statuses", "allRoles": "All Roles", "seller": "<PERSON><PERSON>", "buyer": "Buyer", "allDates": "All Dates", "today": "Today", "thisWeek": "This Week", "thisMonth": "This Month"}, "actions": {"refresh": "Refresh", "export": "Export", "viewDetails": "View Details", "chat": "Cha<PERSON>", "more": "More"}, "search": {"placeholder": "Search trades..."}, "sort": {"sortBy": "Sort By", "date": "Date", "amount": "Amount", "totalValue": "Total Value", "status": "Status", "order": "Order", "descending": "Descending", "ascending": "Ascending"}, "loading": "Loading trades..."}, "reviews": {"title": "Reviews", "subtitle": "View and manage your reviews", "received": "Received Reviews", "given": "Given Reviews", "noReviews": "No reviews", "noReviewsDesc": "You haven't received any reviews yet", "rating": "Rating", "comment": "Comment", "reviewer": "Reviewer", "reviewedUser": "Reviewed User", "trade": "Trade", "date": "Date", "writeReview": "Write Review", "editReview": "Edit Review", "deleteReview": "Delete Review", "userProfile": {"memberSince": "Member since", "completionRate": "Completion Rate", "avgResponseTime": "Avg Response Time", "positiveReviews": "Positive Reviews", "totalTrades": "Total Trades", "verified": "Verified"}, "distribution": {"title": "Rating Distribution"}, "filters": {"title": "Filters", "rating": "Rating", "allRatings": "All Ratings", "stars": "stars", "star": "star", "tradeType": "Trade Type", "allTrades": "All Trades", "buy": "Buy", "sell": "<PERSON>ll", "apply": "Apply Filters"}, "achievements": {"title": "Achievements", "trustedTrader": "Trusted Trader", "trustedTraderDesc": "More than 100 successful trades", "quickResponse": "Quick Response", "quickResponseDesc": "Average response under 10 minutes", "highCompletion": "High Completion Rate", "highCompletionDesc": "More than 95% completion"}, "list": {"title": "Reviews", "searchPlaceholder": "Search reviews...", "noReviews": "No reviews found", "noReviewsDesc": "No reviews match the search criteria", "helpful": "Helpful", "notHelpful": "Not Helpful", "loadMore": "Load More Reviews"}, "errorLoading": "Error loading reviews"}, "admin": {"title": "Admin Panel", "login": "<PERSON><PERSON>", "dashboard": "Dashboard", "users": "User Management", "trades": "Trade Management", "disputes": "Dispute Resolution", "settings": "System Settings", "reports": "Reports", "checking": "Checking permissions...", "accessDenied": "Access Denied", "generalManager": "General Manager", "adminAccessRequired": "This page requires administrative privileges to access", "goToLogin": "Go to Login Page", "messages": {"loginSuccess": "Welcome to Admin Dashboard", "logoutSuccess": "Logged out successfully"}, "tableHeaders": {"user": "User", "rating": "Rating", "trades": "Trades", "volume": "Volume", "status": "Status", "actions": "Actions", "tradeId": "Trade ID", "buyer": "Buyer", "seller": "<PERSON><PERSON>", "amount": "Amount"}, "alerts": {"viewUserDetails": "View {userName} details", "viewTradeDetails": "View trade {tradeId} details"}, "error_dc5b": "Error", "loading_9698": "Loading", "الaن_0d23": "الCreate", "الصف_6293": "الTrade", "filter_32b9": "Filter", "النش_e8c4": "الActiveين", "للصف_e574": "للTrade", "logout_6080": "Logout", "غير_8bec": "Not", "محدد_4ebc": "Specified", "حدث_ee64": "Event", "النز_1898": "Disputes", "فشل_a838": "Failed", "حل_7898": "Resolve", "النز_9512": "Dispute", "تصعي_72e9": "Escalate", "تم_a5eb": "Done", "الان_56d7": "Join", "aرسا_6dbd": "Send", "الدف_4ee6": "Payment", "استل_3103": "Receive", "متنا_1710": "Disputed", "عليه_46c1": "On It", "التد_30b4": "Trades", "تحدي_0614": "Refresh", "aضاف_b950": "Add", "المل_9233": "Note", "تصدي_e4d7": "Export", "ترجم_7859": "Translation", "تعلي_0215": "Comment", "توثي_698a": "Documentation", "الهد_ee84": "Target"}, "notifications": {"title": "Notifications", "subtitle": "Track all notifications and alerts", "markAllRead": "<PERSON> as <PERSON>", "clearAll": "Clear All", "noNotifications": "No notifications", "noNotificationsDesc": "Notifications will appear here when they arrive", "unreadCount": "{{count}} unread notifications", "types": {"trade": "Trade", "offer": "Offer", "review": "Review", "system": "System", "payment": "Payment", "dispute": "Dispute", "general": "General"}, "walletConnected": "<PERSON><PERSON> connected successfully", "walletDisconnected": "Wallet disconnected", "transactionSent": "Transaction sent", "transactionConfirmed": "Transaction confirmed", "transactionFailed": "Transaction failed", "userRejected": "Transaction rejected by user", "networkError": "Network error, please check your connection", "contractError": "Smart contract error, please try again", "filters": {"${item": {}}, "actions": {"view": "View"}, "justNow": "Just now", "searchPlaceholder": "Search notifications...", "markAsRead": "<PERSON> <PERSON>", "delete": "Delete Notification", "viewAll": "View All Notifications", "الشب_4dc8": "Network"}, "createOffer": {"title": "Create <PERSON>er", "subtitle": "Create your offer to sell or buy and earn from stablecoin trading", "wallet": {"notConnected": "Wallet Not Connected", "notConnectedDesc": "You need to connect your wallet to create offers and interact with smart contracts", "connect": "Connect Wallet", "connected": "Wallet Connected", "address": "Address", "balance": "USDT Balance"}, "progress": {"step1": "Set Amount and Limits", "step2": "Payment Methods and Review", "step3": "Terms and Publish"}, "offerType": {"title": "Offer Type", "sell": "Sell Stablecoin", "sellDesc": "I sell stablecoin for local currency", "buy": "Buy Stablecoin", "buyDesc": "I buy stablecoin with local currency"}, "step1": {"title": "Set Amount and Limits", "cryptoAmountSell": "Crypto amount to sell", "cryptoAmountBuy": "Crypto amount to buy", "totalFiatReceive": "Total amount to receive", "totalFiatPay": "Total amount to pay", "fiatCurrencyReceive": "Currency to receive", "fiatCurrencyPay": "Currency to pay", "minOrderAmount": "Minimum order amount", "maxOrderAmount": "Maximum order amount", "totalAmount": "Total Amount (USDT)", "currency": "Local Currency", "minAmount": "Minimum Trade Amount (USDT)", "maxAmount": "Maximum Trade Amount (USDT)", "limits": {"explanation": "Limits Explanation", "minAmountExplanation": "Minimum Amount: The smallest amount a buyer can purchase in a single trade", "maxAmountExplanation": "Maximum Amount: The largest amount a buyer can purchase in a single trade", "example": "Example: If you have 1000 USDT and set minimum 100 and maximum 500, buyers can purchase between 100-500 USDT per trade", "exampleTitle": "Practical Example", "benefits": {"title": "Benefits of Setting Limits:", "benefit1": "Better control over trade sizes", "benefit2": "Reduce risks from large trades", "benefit3": "Attract a wider range of buyers", "benefit4": "Better management of available liquidity"}, "recommendations": {"title": "Limit Recommendations:", "rec1": "Minimum: 5-10% of total amount", "rec2": "Maximum: 30-50% of total amount", "rec3": "For beginners: Start with small limits", "rec4": "For experts: Gradually increase limits"}}, "tips": {"title": "Important Tips:", "tip1": "Make sure you have the required amount in your wallet", "tip2": "Minimum amount helps reduce small trades", "tip3": "Maximum amount sets the largest possible trade"}}, "step2": {"title": "Set Price and Payment Methods", "priceLabel": "Price per USDT", "paymentMethods": "Accepted Payment Methods", "suggestedPrice": "Suggested Price", "liveRate": "Live Rate", "reviewTitle": "Payment Methods and Review", "offerSummary": "Offer Summary", "offerType": "Offer Type", "amount": "Amount", "requiredAmount": "Required Amount", "unitPrice": "Unit Price", "platformFee": "Platform Fee", "netAmount": "Net Amount"}, "step3": {"title": "Terms and Publish Offer", "terms": "Offer Terms (Optional)", "termsPlaceholder": "Add any special terms for your offer...", "autoReply": "Auto Welcome Message (Optional)", "autoReplyPlaceholder": "Hello! Thank you for choosing my offer...", "warnings": {"title": "Important Warnings:", "warning1": "Verify all information before publishing", "warning2": "Don't share personal information in terms", "warning3": "Follow platform rules and terms of service", "warning4": "Be available to respond to inquiries"}}, "preview": {"title": "Offer Preview", "type": "Type", "amount": "Amount", "price": "Price", "totalValue": "Total Value"}, "tips": {"title": "Tips for Success", "competitivePrice": "Competitive Price", "competitivePriceDesc": "Check market prices to set appropriate rate", "quickResponse": "Quick Response", "quickResponseDesc": "Reply to messages within 5 minutes", "highSecurity": "High Security", "highSecurityDesc": "Verify buyer identity before transfer", "excellentService": "Excellent Service", "excellentServiceDesc": "Be polite and helpful with customers"}, "buttons": {"next": "Next", "previous": "Previous", "publish": "Publish Offer", "publishing": "Publishing...", "refreshRates": "Refresh Rates", "useSuggested": "Use Suggested"}, "validation": {"amountRequired": "Total amount is required", "amountPositive": "Amount must be greater than zero", "amountMinLimit": "Amount must be greater than {{min}} USDT", "amountMaxLimit": "Amount must be less than {{max}} USDT", "minAmountRequired": "Minimum amount is required", "minAmountPositive": "Minimum amount must be greater than zero", "minAmountMinLimit": "Minimum amount must be greater than {{min}} USDT", "minAmountGreaterThanTotal": "Minimum amount cannot be greater than total amount", "maxAmountRequired": "Maximum amount is required", "maxAmountPositive": "Maximum amount must be greater than zero", "maxGreaterThanMin": "Maximum amount must be greater than minimum", "maxNotGreaterThanTotal": "Maximum amount cannot be greater than total amount", "priceRequired": "Price is required", "pricePositive": "Price must be greater than zero", "paymentMethodRequired": "At least one payment method must be selected", "completeStepOne": "Please complete all data in step one", "autoReplyMessageRequired": "Auto reply message is required"}, "messages": {"walletRequired": "Please connect wallet first", "insufficientBalance": "Insufficient balance", "creating": "Creating offer...", "connectingContract": "Connecting to smart contract...", "checkingAllowance": "Checking USDT allowance...", "requestingApproval": "Requesting USDT spending approval...", "approvalSuccess": "Approval granted successfully!", "creatingInContract": "Creating offer in smart contract...", "waitingConfirmation": "Waiting for transaction confirmation...", "success": "Offer created successfully and funds locked!", "successDescription": "Successfully created {{amount}} {{stablecoin}} offer. Transaction ID: {{txHash}}", "error": "Error creating offer", "exchangeRateError": "Failed to fetch exchange rates", "ratesUpdated": "Exchange rates updated", "ratesUpdateError": "Failed to update exchange rates", "platformSettingsLoaded": "Platform settings loaded", "offerSavedToDatabase": "Offer saved to database"}, "steps": {"basicInfo": "Basic Information", "paymentMethods": "Payment Methods", "review": "Review", "advanced": "Advanced Settings"}, "form": {"offerType": "Offer Type", "buyDesc": "Buy stablecoin with local currency", "sellDesc": "Sell stablecoin for local currency", "amount": "Total Amount", "price": "Price per unit", "minAmount": "Minimum trade amount", "maxAmount": "Maximum trade amount", "currency": "Local Currency", "stablecoin": "Stablecoin", "calculation": "Offer Calculation", "totalValue": "Total Value", "rate": "Exchange Rate", "paymentMethods": "Accepted Payment Methods", "terms": "Offer Terms", "termsPlaceholder": "Add any special terms for your offer (optional)...", "timeLimit": "Trade Time Limit", "reviewOffer": "Review Offer", "creating": "Creating...", "createOffer": "Create Offer", "manualPrice": "Manual Price", "marketPrice": "Market Price", "currentMarketPrice": "Current Market Price", "marginType": "Margin Type", "fixedAmount": "Fixed Amount", "percentage": "Percentage", "marginValue": "Margin Value", "advancedSettings": "Advanced Settings", "location": "Location", "locationPlaceholder": "Enter your location...", "locationHelp": "Your location helps buyers find local offers", "enableAutoReply": "Enable Auto Reply", "autoReplyMessage": "Auto Reply Message", "autoReplyPlaceholder": "Enter your auto reply message...", "premiumOffer": "Premium Offer", "premiumBadge": "Premium", "premiumDescription": "Premium offers get higher visibility", "hidePreview": "Hide Preview", "showPreview": "Show Preview", "offerSummary": "Offer Summary", "methods": "Payment Methods"}, "calculations": {"automaticCalculation": "Automatic Offer Calculation", "offeredAmount": "Offered Amount", "currentExchangeRate": "Current Exchange Rate", "totalValue": "Total Value", "platformFeeLabel": "Platform Fee", "netAmountLabel": "Net Amount", "referencePrice": "Reference Price", "refreshRates": "Refresh Rates", "useReferencePrice": "Use Reference Price"}}, "tradePage": {"title": "Trade", "notFound": "Trade not found", "buyAmount": "Buy {amount} USDT", "atPrice": "at {price} {currency}", "timeRemaining": "Time Remaining", "status": {"waitingConfirmation": "Waiting for confirmation", "completed": "Completed", "pending": "Pending", "paymentSent": "Payment Sent", "cancelled": "Cancelled", "disputed": "Disputed"}, "details": {"title": "Trade Details", "amount": "Amount", "price": "Price", "totalAmount": "Total Amount"}, "seller": {"title": "<PERSON><PERSON>", "rating": "{rating} ({trades} trades)", "online": "Online now", "offline": "Offline"}, "bankInfo": {"title": "Bank Information", "bank": "Bank", "accountNumber": "Account Number", "accountName": "Account Name", "copy": "Copy"}, "wallet": {"notConnected": "Wallet not connected", "notConnectedDesc": "You need to connect your wallet to interact with smart contracts", "connect": "Connect Wallet"}, "chat": {"title": "Cha<PERSON>", "placeholder": "Type your message...", "send": "Send", "attach": "Attach file"}, "tradeStatus": {"title": "Trade Status", "created": "Trade created", "paymentSent": "Payment sent", "waitingConfirmation": "Waiting for seller confirmation", "releaseTokens": "Release tokens"}, "actions": {"title": "Actions", "confirmReceived": "Confirm payment received", "confirmSent": "Confirm payment sent", "uploadProof": "Upload payment proof", "cancelTrade": "Cancel trade", "reportIssue": "Report issue", "processing": "Processing..."}, "instructions": {"title": "Important Instructions", "instruction1": "Make sure to transfer to the correct account", "instruction2": "Keep proof of transfer", "instruction3": "Don't release tokens before payment confirmation", "instruction4": "Contact support for any issues"}, "messages": {"walletRequired": "Please connect wallet first", "confirmingPayment": "Confirming payment received...", "confirmingSent": "Confirming payment sent...", "cancelling": "Cancelling trade...", "copied": "Copied to clipboard", "paymentProofUploaded": "Payment proof uploaded", "transactionFailed": "Unknown error", "cancelConfirm": "Are you sure you want to cancel this trade?"}}, "auth": {"getStarted": "Get Started", "start": "Start", "quickStart": "Quick Start", "instantAccess": "Instant Access", "connectWalletOnly": "Connect Wallet Only", "walletOnlyDesc": "Start trading instantly without registration", "loginDesc": "Access your existing account", "registerDesc": "Create new account with additional features", "secureTrading": "Secure trading protected by smart contracts", "loginRequired": "Login required", "loginRequiredDesc": "Please login to access this page", "accessDenied": "Access Denied", "sessionExpired": "Session expired", "invalidCredentials": "Invalid credentials", "login": {"title": "<PERSON><PERSON>", "subtitle": "Welcome back! Please login to continue", "email": "Email", "emailPlaceholder": "Enter your email", "password": "Password", "passwordPlaceholder": "Enter your password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "loginButton": "<PERSON><PERSON>", "noAccount": "Don't have an account?", "createAccount": "Create new account"}, "register": {"title": "Create New Account", "subtitle": "Join IKAROS P2P platform", "fullName": "Full Name", "fullNamePlaceholder": "Enter your full name", "phone": "Phone Number", "phonePlaceholder": "Enter your phone number", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Re-enter password", "agreeToTerms": "I agree to", "terms": "Terms and Conditions", "privacy": "Privacy Policy", "registerButton": "Create Account", "haveAccount": "Already have an account?", "loginLink": "<PERSON><PERSON>"}, "validation": {"emailRequired": "Email is required", "emailInvalid": "Invalid email", "passwordRequired": "Password is required", "passwordMinLength": "Password must be at least 6 characters", "confirmPasswordRequired": "Password confirmation is required", "fullNameRequired": "Full name is required", "phoneRequired": "Phone number is required", "phoneInvalid": "Invalid phone number", "passwordMismatch": "Passwords do not match", "termsRequired": "You must agree to terms and conditions"}, "messages": {"processing": "Processing...", "loginSuccess": "Login successful!", "registerSuccess": "Account created successfully! Please verify your email.", "error": "An error occurred, please try again"}, "security": {"title": "Advanced Protection", "ssl": "256-bit SSL encryption", "twoFactor": "Two-factor authentication", "fraudProtection": "Fraud protection"}, "loginRequiredMessage": "Please login to continue"}, "homepage": {"stats": {"title": "Platform Statistics"}, "messages": {"buyStarted": "Purchase process started for offer {offerId}"}}, "settings": {"title": "Settings", "subtitle": "Manage platform and wallet settings", "sections": {"title": "Settings Sections", "profile": "Profile", "security": "Security", "notifications": "Notifications", "privacy": "Privacy", "wallet": "Wallet & Network", "appearance": "Appearance"}, "profile": {"title": "Profile", "username": "Username", "fullName": "Full Name", "email": "Email", "phone": "Phone", "country": "Country", "bio": "Bio", "avatar": "Avatar", "language": "Preferred Language", "timezone": "Timezone", "currency": "Preferred Currency"}, "security": {"title": "Security", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "twoFactor": "Two-Factor Authentication", "enable2FA": "Enable 2FA", "disable2FA": "Disable 2FA", "autoLogout": "Auto Logout", "loginHistory": "Login History", "deleteAccount": "Delete Account"}, "notifications": {"title": "Notifications", "emailNotifications": "Email Notifications", "smsNotifications": "SMS Notifications", "pushNotifications": "Push Notifications", "tradeNotifications": "Trade Notifications", "marketingNotifications": "Marketing Notifications", "trades": "Trade Notifications", "tradesDesc": "Receive notifications when trades are updated", "showBalances": "Show Balances", "showBalancesDesc": "Display wallet balances in interface", "autoConnect": "Auto Connect", "autoConnectDesc": "Automatically connect wallet when visiting site"}, "privacy": {"title": "Privacy", "profileVisibility": "Profile Visibility", "showOnlineStatus": "Show Online Status", "allowDirectMessages": "Allow Direct Messages", "privacyLevel": "Privacy Level"}, "appearance": {"title": "Appearance Settings", "theme": "Theme", "themeDesc": "Choose your preferred theme", "light": "Light", "dark": "Dark", "system": "Auto (System)"}, "wallet": {"title": "Wallet & Network", "status": "Wallet Status", "connected": "Connected", "notConnected": "Not Connected", "pleaseConnect": "Please connect wallet", "connect": "Connect Wallet", "disconnect": "Disconnect"}, "network": {"current": "Current Network", "testnet": "Testnet", "mainnet": "Mainnet", "testMode": "Test Mode", "testModeDesc": "Switch between testnet and mainnet", "testnetLabel": "Testnet", "mainnetLabel": "Mainnet", "mainnetWarning": "Warning: Mainnet", "mainnetWarningDesc": "You are now on mainnet. All transactions will be real and cost actual money.", "disconnected": "Disconnected", "switchedTo": "Switched to {networkName}", "unsupported": "Unsupported network", "switchFailed": "Failed to switch network", "selectNetwork": "Select Network", "selectNetworkDescription": "Choose the appropriate network for trading", "testnetDescription": "Test network", "mainnetDescription": "Main network", "testnetNote": "Testnet: For testing only", "mainnetNote": "Mainnet: For real trading"}, "info": {"title": "Important Information", "tip1": "Use testnet for testing and learning", "tip2": "Mainnet requires real BNB for transaction fees", "tip3": "Make sure you have sufficient balance before transactions", "tip4": "Keep backup of your wallet keys"}, "messages": {"networkInfoError": "Error fetching network info:", "walletRequired": "Please connect wallet first", "switchingNetwork": "Switching network...", "networkSwitchFailed": "Failed to switch network", "networkSwitchError": "Network switch error", "walletConnectFailed": "Failed to connect wallet", "settingsSaved": "Setting<PERSON> saved"}}, "welcome": {"greeting": "Welcome {{name}}! 🎉", "skip": "<PERSON><PERSON>", "next": "Next", "getStarted": "Get Started", "steps": {"welcome": {"title": "Welcome to IKAROS P2P!", "description": "Your account has been created successfully. Let us help you get started with simple steps."}, "wallet": {"title": "Connect Wallet", "description": "Connect your digital wallet to start secure trading protected by smart contracts."}, "explore": {"title": "Explore Platform", "description": "Now you can browse offers or create your first offer to start trading."}}, "benefits": {"secure": "Secure trading protected by smart contracts", "features": "Advanced features and easy-to-use interface", "trading": "Diverse and profitable trading opportunities"}, "wallet": {"connected": "Wallet connected successfully!", "notConnected": "Wallet not connected yet"}, "quickActions": {"browseOffers": "Browse Offers", "createOffer": "Create Offer"}}, "dashboard": {"wallet": {"notConnected": "Wallet not connected", "notConnectedDesc": "To view your personal data and interact with smart contracts, please connect your wallet", "connect": "Connect Wallet"}, "stats": {"activeTrades": "Active Trades", "walletBalance": "Wallet Balance", "totalTrades": "Total Trades", "totalVolume": "Total Volume", "rating": "Rating", "completionRate": "Completion Rate"}, "viewAll": "View All", "noTrades": "No trades yet", "startTrading": "Start Trading Now", "errors": {"loadingData": "Failed to load dashboard data"}, "welcome": "Welcome", "subtitle": "Manage your trades and offers easily", "status": {"completed": "Completed", "active": "Active", "cancelled": "Cancelled"}, "tabs": {"overview": "Overview", "trades": "Trades", "offers": "My Offers", "settings": "Settings"}, "recentTrades": "Recent Trades", "activeOffers": "Active Offers", "allTrades": "All Trades", "myOffers": "My Offers", "accountSettings": "Account <PERSON><PERSON>", "buy": "Buy", "sell": "<PERSON>ll", "with": "with", "atPrice": "at price", "filter": "Filter", "export": "Export", "createNewOffer": "Create <PERSON>er", "active": "Active", "inactive": "Inactive", "price": "Price", "paymentMethods": "Payment Methods", "views": "Views", "personalInfo": "Personal Information", "fullName": "Full Name", "email": "Email", "phone": "Phone", "securitySettings": "Security Settings", "twoFactor": "Two-Factor Authentication", "twoFactorDesc": "Additional protection for your account", "emailNotifications": "Email Notifications", "emailNotificationsDesc": "Receive trade updates", "activate": "Activate", "saveChanges": "Save Changes", "tableHeaders": {"tradeId": "Trade ID", "type": "Type", "amount": "Amount", "price": "Price", "partner": "Partner", "status": "Status", "date": "Date"}, "buttons": {"settings": "Settings", "notifications": "Notifications", "createOffer": "Create Offer"}, "management_14a8": "Management", "comprehensive_d483": "Comprehensive", "contract_407d": "Contract", "smart_9c5f": "Smart", "transactions_aa00": "Transactions", "النش_9269": "الActiveة", "لaدا_e787": "لManagement", "verify_c929": "Verify", "لجمي_4fee": "For All", "معام_82ab": "Your Transactions", "مكتم_03ea": "Completed", "اليو_5770": "Today", "في_a7b6": "In", "الان_d1fe": "Waiting", "الحج_e3eb": "Volume", "الaج_88fc": "Total", "نصائ_cc27": "Tips", "حالh_a280": "Status", "المز_e917": "Sync", "بانت_d9b5": "Regularly", "للتa_2849": "To Ensure", "تطاب_a20a": "Match", "البي_36da": "Data", "استخ_f220": "Use", "خاصي_2905": "Feature", "فرض_c3e3": "Force", "وجود_800d": "Existence", "تباي_9007": "<PERSON><PERSON><PERSON>", "راقب_86a4": "Monitor", "معام_8a11": "Transactions", "الغا_8da6": "Gas", "لتحس_6e60": "To Improve", "تكال_679a": "Costs", "احتف_352c": "Keep", "بنسخ_d16a": "With Copies", "المه_93b0": "Task", "للمر_64d3": "For Review", "المس_5df9": "Future"}, "offers": {"title": "Available Offers", "subtitle": "Choose from hundreds of offers from trusted traders", "featured": "Featured", "trades": "trades", "availableAmount": "Available Amount", "price": "Price", "paymentMethod": "Payment Method", "avgTime": "Avg Time", "completionRate": "Completion Rate", "buyNow": "Buy Now", "buy": "Buy", "sell": "<PERSON>ll", "amount": "Amount", "totalValue": "Total Value", "timeLimit": "Time Limit", "paymentMethods": "Payment Methods", "completedTrades": "Completed Trades", "startTrade": "Start Trade", "viewDetails": "View Details", "searchPlaceholder": "Search offers...", "sortPriceAsc": "Price: Low to High", "sortPriceDesc": "Price: High to Low", "sortAmountAsc": "Amount: Low to High", "sortAmountDesc": "Amount: High to Low", "sortNewest": "Newest First", "noOffers": "No offers available", "noOffersDesc": "No offers match your search criteria", "noOffersWithFilters": "No offers match the selected filters", "noOffersAvailable": "No offers available at the moment", "tradeCreated": "Trade created successfully", "search": {"placeholder": "Search for trader or offer..."}, "filters": {"title": "Filters", "allTypes": "All Types", "allCurrencies": "All Currencies", "allPaymentMethods": "All Payment Methods", "country": "Country", "paymentMethod": "Payment Method", "currency": "<PERSON><PERSON><PERSON><PERSON>", "minAmount": "<PERSON>", "maxAmount": "<PERSON>", "sortBy": "Sort By"}, "sort": {"price": "Price", "rating": "Rating", "trades": "Number of Trades", "newest": "Newest", "created_at": "Creation Date"}, "card": {"seller": "<PERSON><PERSON>", "price": "Price", "available": "Available", "minAmount": "<PERSON>", "maxAmount": "<PERSON>", "paymentMethods": "Payment Methods", "rating": "Rating", "trades": "trades", "online": "Online", "offline": "Offline", "verified": "Verified", "buyNow": "Buy Now", "viewDetails": "View Details", "processing": "Processing...", "avgTime": "Avg Time", "completionRate": "Completion Rate"}, "stats": {"available": "offers available", "online": "traders online", "bestPrice": "best price", "avgRating": "avg rating"}, "noResults": {"title": "No matching offers", "clearFilters": "Clear Filters"}, "loadMore": "Load More", "totalFound": "Found {{count}} offers", "resetFilters": "Reset Filters", "applyFilters": "Apply Filters", "totalOffers": "Total Offers", "buyOffers": "Buy Offers", "sellOffers": "<PERSON><PERSON>", "avgPrice": "Average Price", "offerType": "Offer Type", "allTypes": "All Types", "allCurrencies": "All Currencies", "allStablecoins": "All Stablecoins", "stablecoin": "Stablecoin", "amountRange": "Amount Range", "priceRange": "Price Range", "minPrice": "<PERSON>", "maxPrice": "Max Price", "verifiedOnly": "Verified Traders Only", "premiumOnly": "Premium Offers Only", "verified": "Verified", "online": "Online", "minutes": "minutes", "sortOldest": "Oldest First", "sortBy": "Sort By", "sortPopular": "Most Viewed", "trade": "Trade", "pagination": {"showing": "Showing {start} - {end} of {total} offers", "page": "Page {current} of {total}"}, "messages": {"loadError": "Error loading offers", "retryLoad": "Retry", "walletRequired": "Please connect wallet first", "startingTrade": "Starting trade...", "offerNotFound": "Offer not found", "offerNotActive": "Offer not active", "offerNotOnBlockchain": "Offer not found on blockchain", "checkingContract": "Checking smart contract...", "contractTradeNotFound": "Trade not found in smart contract", "offerNotAvailable": "Offer not available", "cannotBuyOwnOffer": "Cannot buy your own offer", "joiningTrade": "Joining trade...", "waitingConfirmation": "Waiting for transaction confirmation...", "tradeStartedSuccess": "Trade started successfully!", "tradeStartError": "Error starting trade"}, "unavailable": "Unavailable", "terms": "Offer Terms", "removedFromFavorites": "Removed from favorites", "addedToFavorites": "Added to favorites", "activeTraders": "Active Traders", "avgRating": "Average Rating", "verifiedTraders": "Verified Traders", "advancedSort": "Advanced Sort", "sortRatingDesc": "Rating: High to Low", "sortTradesDesc": "Trades: High to Low", "sortResponseTime": "Response Time: Fast to Slow", "minAmount": "<PERSON>", "maxAmount": "<PERSON>", "allPaymentMethods": "All Payment Methods", "country": "Country", "allCountries": "All Countries", "minRating": "Minimum Rating", "anyRating": "Any Rating", "minTrades": "Minimum Trades", "anyTrades": "Any Number"}, "footer": {"company": {"title": "IKAROS P2P", "description": "IKAROS P2P platform is the optimal solution for exchanging USDT safely and quickly. We provide you with a secure and guaranteed environment with smart contracts on BSC network to ensure protection of your funds."}, "links": {"title": "Quick Links", "home": "Home", "offers": "Offers", "about": "About Us", "howItWorks": "How It Works", "fees": "Fees", "security": "Security Policy", "support": "Help", "contact": "Contact Us", "terms": "Terms of Service", "privacy": "Privacy Policy", "disclaimer": "Disclaimer", "faq": "FAQ"}, "contact": {"title": "Contact Us", "email": "<EMAIL>", "phone": "+966 50 123 4567", "address": "Riyadh, Saudi Arabia", "workingHours": "Working Hours", "support247": "24/7 Continuous Support"}, "stats": {"security": "Guaranteed Security", "activeUsers": "active users", "minutes": "minutes", "avgTime": "average transaction time"}, "social": {"title": "Follow Us", "facebook": "Facebook", "twitter": "Twitter", "telegram": "Telegram", "instagram": "Instagram"}, "copyright": "© {year} IKAROS P2P. All rights reserved.", "legal": {"warning": "Warning", "riskDisclaimer": "Cryptocurrency trading involves risks. Please trade carefully and responsibly.", "compliance": "IKAROS P2P platform is licensed and regulated according to local laws. We are committed to the highest standards of security and transparency."}}, "visitorDashboard": {"title": "Visitor Dashboard", "subtitle": "Explore IKAROS P2P platform and view latest statistics and available offers", "stats": {"totalTrades": "Total Trades", "totalUsers": "Total Users", "totalVolume": "Total Volume", "avgRating": "Average Rating"}, "featuredOffers": {"title": "Featured Offers", "viewAll": "View All Offers"}, "features": {"security": {"title": "Guaranteed Security", "description": "Smart contracts on BSC network ensure 100% protection of your funds"}, "speed": {"title": "Lightning Speed", "description": "Fast transactions completed in minutes with lowest possible fees"}, "global": {"title": "Comprehensive Coverage", "description": "Service available throughout the Arab world with local currency support"}}, "cta": {"title": "Start Your Trading Journey Now", "description": "Join thousands of users who trust IKAROS P2P platform", "register": "Create Free Account", "browseOffers": "Browse Offers"}}, "adminDashboard": {"title": "Admin Dashboard", "subtitle": "Monitor and manage IKAROS P2P platform - Full administrative privileges", "adminOnly": "Admins Only", "loginMethod": {"wallet": "Login via Wallet", "credentials": "Login via Credentials"}, "systemAdmin": "System Administrator", "walletNotConnected": "Wallet not connected", "walletNotConnectedDesc": "To resolve disputes and interact with smart contracts, you must connect your wallet first", "connectWallet": "Connect Wallet", "stats": {"totalUsers": "Total Users", "activeUsers": "Active Users", "totalTrades": "Total Trades", "activeTrades": "Active Trades", "totalVolume": "Total Volume", "monthlyVolume": "Monthly Volume", "pendingDisputes": "Pending Disputes", "resolvedDisputes": "Resolved Disputes", "platformFees": "Platform Fees", "monthlyFees": "Monthly Fees", "completedTrades": "Completed Trades", "disputedTrades": "Disputed Trades", "completionRate": "Completion Rate"}, "tabs": {"overview": "Overview", "analytics": "Advanced Analytics", "trading": "Trading Interface", "users": "Users", "trades": "Trades", "disputes": "Disputes", "notifications": "Notification Management", "contracts": "Contract Management", "events": "Smart Contract Events", "admins": "Admins", "settings": "Settings"}, "overview": {"tradingVolume": "Trading Volume", "total": "Total", "thisMonth": "This Month", "recentActivity": "Recent Activity"}, "users": {"title": "User Management", "searchPlaceholder": "Search for user..."}, "trades": {"title": "Trade Management", "resolveForSeller": "Resolve dispute for seller", "resolveForBuyer": "Resolve dispute for buyer", "tradeDetails": "Trade Details", "tradeInfo": "Trade Information", "tradeId": "Trade ID", "type": "Trade Type", "amount": "Amount", "price": "Price", "totalAmount": "Total Amount", "paymentMethod": "Payment Method", "participants": "Trade Participants", "timeline": "Trade Timeline", "createdAt": "Created At", "updatedAt": "Updated At", "completedAt": "Completed At", "disputeCreatedAt": "Dispute Created At", "messagesComingSoon": "Messages coming soon", "notesComingSoon": "Notes coming soon", "historyComingSoon": "History coming soon", "updateStatus": "Update Status", "addNote": "Add Note", "viewOnBlockchain": "View on Blockchain", "description": "Comprehensive trade management and monitoring", "searchPlaceholder": "Search trades...", "status": "Trade Status", "currency": "<PERSON><PERSON><PERSON><PERSON>", "bulkActions": "Bulk Actions", "noTrades": "No trades found", "actions": "Actions", "resolveDispute": "Resolve Dispute"}, "disputes": {"title": "Dispute Management", "noDisputes": "No disputes currently", "noDisputesDesc": "Disputes will be displayed here when they occur", "description": "Manage and resolve trade disputes", "totalDisputes": "Total Disputes", "pendingDisputes": "Pending Disputes", "resolvedDisputes": "Resolved Disputes", "avgResolutionTime": "Average Resolution Time", "searchPlaceholder": "Search disputes...", "pending": "Pending", "resolved": "Resolved", "activeDisputes": "Active Disputes", "urgent": "<PERSON><PERSON>", "ago": "ago", "tradeAmount": "Trade Amount", "participants": "Participants", "paymentMethod": "Payment Method", "reason": "Dispute Reason", "messages": "Messages", "notes": "Admin Notes", "hasProof": "Has Proof", "resolve": "Resolve Dispute", "escalate": "Escalate", "timeElapsed": "Time Elapsed"}, "settings": {"title": "Platform Settings"}, "status": {"active": "Active", "suspended": "Suspended", "completed": "Completed", "disputed": "Disputed", "pending": "Pending"}, "actions": {"suspend": "Suspend", "activate": "Activate", "complete": "Complete", "cancel": "Cancel"}, "messages": {"userActionSuccess": "User {action} successfully", "tradeActionSuccess": "Trade {action} successfully", "disputeResolved": "Di<PERSON><PERSON> resolved in favor of {party}", "seller": "seller", "buyer": "buyer", "walletConnectFailed": "Failed to connect wallet", "disputeResolveError": "Error resolving dispute", "confirmDispute": "Are you sure you want to resolve dispute in favor of {party}?", "resolvingDispute": "Resolving dispute...", "tradeSelected": "Selected trade with {trader}", "createOfferRedirect": "You will be redirected to create offer page", "loginSuccess": "Login successful", "logoutSuccess": "Logout successful"}, "notifications": {"title": "Notification Management", "settings": "Notification Settings", "sendBroadcast": "Send Broadcast Notification", "stats": "Notification Statistics", "totalSent": "Total Sent", "todaySent": "Sent Today", "openRate": "Open Rate", "types": "Notification Types", "recent": "Recent Notifications"}}, "comments": {"platformStats": "Platform statistics data", "featuredOffers": "Featured offers data", "loadingSimulation": "Data loading simulation", "mainTitle": "Main title", "platformStatistics": "Platform statistics", "featuredOffersSection": "Featured offers", "platformFeatures": "Platform features", "callToAction": "Call to action", "mockOffers": "Mock offers data", "mockStats": "Platform statistics", "dataLoading": "Data loading simulation", "buyProcess": "Purchase process simulation", "heroSection": "Hero section", "liveOffers": "Live offers section", "featuresSection": "Features section", "howItWorks": "How it works section", "ctaSection": "Call to action section", "coloredTopBar": "Colored top bar", "sellerInfo": "Seller information", "offerDetails": "Offer details", "paymentMethod": "Payment method", "additionalStats": "Additional statistics", "buyButton": "Buy button", "hoverEffect": "Hover effect", "mockData": "Mock statistics data", "mockUsers": "Mock users data", "mockTrades": "Mock trades data", "authCheck": "Check login status when component loads", "walletCheck": "Check wallet status", "sessionExpired": "Session expired", "loginSuccess": "Login success handler", "notLoggedIn": "If not logged in", "waitingTransaction": "Simulate waiting for transaction confirmation", "mainHeader": "Header", "warningNotConnected": "Wallet not connected warning", "quickStats": "Quick statistics", "tabsSection": "Tabs", "overviewTab": "Overview tab", "detailedStats": "Detailed statistics", "recentActivitySection": "Recent activity", "usersTab": "Users tab", "tradesTab": "Trades tab", "disputesTab": "Disputes tab", "adminsTab": "Admin management tab", "recentActivity": {"tradeCompleted": "Trade {tradeId} completed", "newUserJoined": "New user joined: {userName}", "disputeReported": "Dispute reported in trade {tradeId}", "minutesAgo": "{minutes} minutes ago"}, "trades": {"title": "Advanced Trade Management", "description": "Comprehensive management of all trades and transactions on the platform", "searchPlaceholder": "Search by trade ID, username, or wallet address...", "noTrades": "No trades", "tradeId": "Trade ID", "participants": "Participants", "amount": "Amount", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "completedAt": "Completed At", "actions": "Actions", "type": "Type", "currency": "<PERSON><PERSON><PERSON><PERSON>", "paymentMethod": "Payment Method", "totalAmount": "Total Amount", "price": "Price", "bulkActions": "Bulk Actions", "addNote": "Add Note", "updateStatus": "Update Status", "resolveDispute": "Resolve Dispute", "resolveForSeller": "Resolve for <PERSON><PERSON>", "resolveForBuyer": "Resolve for Buyer", "tradeDetails": "Trade Details", "tradeInfo": "Trade Information", "timeline": "Timeline", "disputeCreatedAt": "Dispute Created At", "messagesComingSoon": "Messages coming soon", "notesComingSoon": "Notes coming soon", "historyComingSoon": "History coming soon", "viewOnBlockchain": "View on Blockchain"}, "disputes": {"title": "Dispute Management", "description": "Manage and resolve disputes between users", "searchPlaceholder": "Search disputes...", "noDisputes": "No disputes currently", "noDisputesDesc": "All trades are running smoothly!", "totalDisputes": "Total Disputes", "pendingDisputes": "Pending Disputes", "resolvedDisputes": "Resolved Disputes", "avgResolutionTime": "Avg Resolution Time", "activeDisputes": "Active Disputes", "tradeAmount": "Trade Amount", "participants": "Participants", "paymentMethod": "Payment Method", "reason": "Dispute Reason", "messages": "Messages", "notes": "Notes", "ago": "ago", "hasProof": "Has payment proof", "resolve": "Resolve", "escalate": "Escalate", "urgent": "<PERSON><PERSON>", "pending": "Pending", "resolved": "Resolved", "timeElapsed": "Time Elapsed"}}, "paymentMethods": {"bankTransfer": "Bank Transfer", "quickTransfer": "Quick Transfer", "instantTransfer": "Instant Transfer", "cash": "Cash", "mobileWallet": "Mobile Wallet", "creditCard": "Credit Card", "visa": "Visa", "mastercard": "Mastercard", "paypal": "PayPal", "westernUnion": "Western Union", "skrill": "Skrill", "neteller": "<PERSON><PERSON>", "perfectMoney": "Perfect Money", "wise": "Wise (TransferWise)", "revolut": "Revolut", "applePay": "Apple Pay", "googlePay": "Google Pay", "samsungPay": "Samsung Pay", "zelle": "<PERSON><PERSON>", "venmo": "Ven<PERSON>", "cashApp": "Cash App", "description": {"bankTransfer": "Traditional bank transfer", "quickTransfer": "Quick transfer within minutes", "instantTransfer": "Instant transfer within seconds", "cash": "Direct cash payment", "mobileWallet": "Digital wallet on mobile", "creditCard": "Credit or debit card", "paypal": "Global digital wallet", "westernUnion": "International money transfer", "skrill": "European digital wallet", "neteller": "Digital wallet for payments", "perfectMoney": "Digital payment system", "wise": "International transfers at low rates", "revolut": "European digital bank", "applePay": "Apple contactless payments", "googlePay": "Google fast payments", "samsungPay": "Samsung smart payments", "zelle": "American instant transfers", "venmo": "Social payment app", "cashApp": "American payment app"}, "digitalWallet": "Digital Wallet"}, "adminManagement": {"title": "Admin Management", "subtitle": "Add and manage admin accounts and permissions", "addNewAdmin": "Add New Admin", "stats": {"totalAdmins": "Total Admins", "activeAdmins": "Active Admins", "superAdmins": "Super Admins"}, "adminsList": "Admins List", "tableHeaders": {"admin": "Admin", "walletAddress": "Wallet Address", "permissions": "Permissions", "lastLogin": "Last Login", "status": "Status", "actions": "Actions"}, "permissions": {"superAdmin": "Super Admin", "userManagement": "User Management", "tradeManagement": "Trade Management", "systemSettings": "System Settings", "adminDashboard": "Admin Dashboard"}, "status": {"active": "Active", "inactive": "Inactive"}, "actions": {"activate": "Activate", "deactivate": "Deactivate", "edit": "Edit", "delete": "Delete"}, "form": {"title": "Add New Admin", "fullName": "Full Name", "username": "Username", "email": "Email", "walletAddress": "Wallet Address", "password": "Password", "confirmPassword": "Confirm Password", "cancel": "Cancel", "addAdmin": "Add Admin"}, "messages": {"loadFailed": "Failed to load admin list", "passwordMismatch": "Passwords do not match", "addSuccess": "Admin added successfully", "addFailed": "Failed to add admin", "statusUpdated": "Admin status updated", "statusUpdateFailed": "Failed to update admin status", "deleteConfirm": "Are you sure you want to delete this admin?", "deleteSuccess": "Admin deleted successfully", "deleteFailed": "Failed to delete admin", "addressCopied": "Address copied", "neverLoggedIn": "Never logged in", "editFeatureComingSoon": "Edit feature coming soon"}}, "messages": {"loginSuccess": "Login successful", "registerSuccess": "Account created successfully", "profileUpdated": "Profile updated successfully", "offerCreated": "Offer created successfully", "tradeCreated": "Trade created successfully", "paymentConfirmed": "Payment confirmed successfully", "tradeCompleted": "Trade completed successfully"}, "validation": {"emailInvalid": "Invalid email address", "passwordRequirements": "Password must contain at least 8 characters, uppercase letter, lowercase letter, number, and special character", "phoneInvalid": "Invalid phone number", "amountRange": "Amount must be between 10 and 100,000"}, "errors": {"networkError": "Network error, please try again", "unauthorized": "Unauthorized, please login", "forbidden": "This action is not allowed", "notFound": "Requested item not found", "validationError": "Invalid input data", "serverError": "Server error, please try again later", "noAdminAccess": "No admin access", "noAccess": "Access denied", "adminAccessRequired": "Admin access required", "loginRequired": "Login required"}, "countries": {"saudiArabia": "Saudi Arabia", "uae": "United Arab Emirates", "kuwait": "Kuwait", "qatar": "Qatar", "bahrain": "Bahrain", "oman": "Oman", "jordan": "Jordan", "lebanon": "Lebanon", "egypt": "Egypt", "morocco": "Morocco", "saudi": "Saudi Arabia"}, "stablecoins": {"title": "Stablecoins", "selectCoin": "Select Stablecoin", "usdt": {"name": "Tether USD", "description": "The most traded stablecoin in the world"}, "usdc": {"name": "USD Coin", "description": "Fully backed stablecoin by US Dollar"}, "busd": {"name": "Binance USD", "description": "Official stablecoin of Binance"}, "dai": {"name": "Dai Stablecoin", "description": "Decentralized stablecoin backed by collateral"}, "fdusd": {"name": "First Digital USD", "description": "New stablecoin backed by US Dollar"}, "tusd": {"name": "TrueUSD", "description": "Trusted stablecoin with full transparency"}}, "offline": {"reconnecting": "Reconnecting...", "title": "You are offline", "reconnectingDescription": "Attempting to reconnect...", "description": "Please check your internet connection", "status": {"online": "Online", "offline": "Offline"}, "retry": "Retry", "goHome": "Go Home", "availablePages": "Available Pages", "tips": {"title": "Tips while offline", "tip1": "Check your internet connection", "tip2": "Try refreshing the page", "tip3": "Some features may be limited offline"}, "technical": {"serviceWorker": "Service Worker", "cacheVersion": "Cache Version", "lastUpdate": "Last Update"}}, "transactions": {"title": "Transactions", "messages": {"loadFailed": "Failed to load transactions", "noBlockchainId": "No blockchain ID found", "refreshSuccess": "Transactions refreshed successfully", "syncSuccess": "Sync completed successfully"}, "stats": {"total": "Total Transactions", "completed": "Completed", "pending": "Pending", "volume": "Total Volume"}, "filters": {"allTypes": "All Types", "offers": "Offers", "trades": "Trades", "allStatus": "All Status", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled", "failed": "Failed", "allSyncStatus": "All Sync Status", "synced": "Synced", "syncPending": "Sync Pending", "syncFailed": "Sync Failed", "searchPlaceholder": "Search transactions..."}, "table": {"type": "Type", "id": "ID", "amount": "Amount", "status": "Status", "blockchain": "Blockchain", "timestamp": "Timestamp", "actions": "Actions"}, "types": {"${transaction": {}, "${selectedTransaction": {}}, "status": {"${transaction": {}, "${selectedTransaction": {}}, "syncStatus": {"${transaction": {}, "${selectedTransaction": {}}, "actions": {"viewDetails": "View Details", "refresh": "Refresh", "forceSync": "Force Sync"}, "details": {"title": "Transaction Details", "type": "Transaction Type", "status": "Transaction Status", "amount": "Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "blockchainInfo": "Blockchain Information", "blockchainId": "Blockchain ID", "transactionHash": "Transaction Hash", "contractStatus": "Contract Status", "syncStatus": "Sync Status", "addresses": "Addresses", "sellerAddress": "<PERSON><PERSON> Address", "buyerAddress": "Buyer Address", "gasInfo": "Gas Information", "gasUsed": "Gas Used", "gasPrice": "Gas Price", "timestamps": "Timestamps", "createdAt": "Created At", "updatedAt": "Updated At", "lastSyncAt": "Last Sync At"}}, "SAR": "SAR", "a": "a", "T": "T", "id": "ID", "amount": "Amount", "status": "Status", "created_at": "Created At", "desc": "Description", "metamask": "MetaMask", "active": "Active", "completed": "Completed", "suspended": "Suspended", "disputed": "Disputed", "pending": "Pending", "suspend": "Suspend", "adminLogin": {"messages": {"loginSuccess": "Admin login successful", "noAdminPermissions": "No admin permissions", "loginFailed": "Admin login failed", "invalidCredentials": "Invalid admin credentials", "permissionCheckError": "Error checking admin permissions"}, "helpText": {"title": "Admin Login <PERSON>", "walletInfo": "Connect your admin wallet to login", "credentialsInfo": "Use your admin credentials to login", "adminAddress": "Admin Address"}}, "adminSettings": {"messages": {"settingsLoaded": "Settings loaded successfully", "loadFailed": "Failed to load settings", "settingsSaved": "Setting<PERSON> saved successfully", "saveFailed": "Failed to save settings", "settingsReset": "Settings reset to default"}, "sections": {"network": "Network Settings", "platform": "Platform Settings", "trading": "Trading Settings", "security": "Security Settings", "contracts": "Contract Settings"}, "title": "<PERSON><PERSON>s", "subtitle": "Manage platform settings and configurations", "actions": {"reset": "Reset to De<PERSON>ult", "saving": "Saving...", "save": "Save Settings"}, "warnings": {"unsavedChanges": "Unsaved Changes", "unsavedChangesDesc": "You have unsaved changes. Save before leaving?"}, "network": {"title": "Network Configuration", "testnet": "Testnet Mode", "mainnet": "Mainnet Mode", "adminOnly": "Admin Only Access", "adminOnlyDesc": "Only admins can access the platform", "currentNetwork": "Current Network", "disconnected": "Disconnected", "testnetDesc": "Test network for development", "mainnetDesc": "Production network"}, "platform": {"title": "Platform Configuration", "platformFee": "Platform Fee (%)", "currentFee": "Current Fee", "minTradeAmount": "Minimum Trade Amount", "maxTradeAmount": "Maximum Trade Amount", "maintenanceMode": "Maintenance Mode", "maintenanceModeDesc": "Platform is under maintenance", "registrationEnabled": "Registration Enabled", "registrationEnabledDesc": "Allow new user registrations"}, "trading": {"title": "Trading Configuration", "defaultTimeout": "Default Trade Timeout", "seconds": "seconds", "minutes": "minutes", "maxOffersPerUser": "Max Offers Per User", "autoDisputeTimeout": "Auto Dispute Timeout", "hours": "hours", "supportedCurrencies": "Supported Currencies", "addCurrency": "Add <PERSON>cy", "add": "Add", "days": "days"}, "security": {"title": "Security Configuration", "requireKYC": "Require KYC Verification", "requireKYCDesc": "Users must complete KYC to trade", "kycThreshold": "KYC Threshold Amount", "maxDisputeTime": "Maximum Dispute Time"}, "contracts": {"title": "Smart Contract Configuration", "warning": "Warning: Contract Changes", "warningDesc": "Changing contract settings requires careful consideration", "escrowContract": "Escrow Contract Address", "usdtContract": "USDT Contract Address", "adminWallet": "Admin Wallet Address", "currentAdmin": "Current Admin"}}, "text": "Text", "-": "-", "security": "Security", "system": "System", "contract": "Contract", "urgent": "<PERSON><PERSON>", "high": "High", "medium": "Medium", "timestamp": "Timestamp", "priority": "Priority", "type": "Type", "analytics": {"totalVolume": "Total Volume", "totalTrades": "Total Trades", "activeUsers": "Active Users", "platformRevenue": "Platform Revenue", "timeRange": {"24h": "Last 24 Hours", "7d": "Last 7 Days", "30d": "Last 30 Days", "90d": "Last 90 Days"}, "errorLoading": "Error loading analytics data", "title": "Analytics Dashboard", "subtitle": "Platform performance and statistics", "refresh": "Refresh Data", "export": "Export Report", "volumeChart": "Volume Chart", "tradesChart": "Trades Chart", "trades": "Trades", "completionRate": "Completion Rate", "completionRateDesc": "Percentage of completed trades", "averageTradeSize": "Average Trade Size", "perTrade": "per trade", "topCurrencies": "Top Currencies", "recentTrades": "Recent Trades", "viewAll": "View All", "table": {"time": "Time", "type": "Type", "amount": "Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status"}, "periods": {"24h": "24h", "7d": "7d", "30d": "30d", "1y": "1y"}, "error": "Analytics Error", "autoRefresh": "Auto Refresh", "totalUsers": "Total Users", "lastUpdated": "Last Updated", "timezone": "Timezone"}, "chart": {"js": "Chart"}, "2d": "2D", "charts": {"realtime": "Real-time Charts", "tradingVolume": "Trading Volume", "tradingVolumeTitle": "Trading Volume Over Time", "price": "Price", "priceHistory": "Price History", "newUsers": "New Users", "activeUsers": "Active Users", "userActivity": "User Activity", "distribution": "Distribution", "currencyDistribution": "Currency Distribution"}, "trading": {"title": "Trading Interface", "subtitle": "Advanced trading tools and features", "createOffer": "Create <PERSON>er", "buy": "Buy Orders", "sell": "Sell Orders", "filters": {"priceRange": "Price Range", "min": "Min", "max": "Max", "amountRange": "Amount Range", "paymentMethod": "Payment Method", "allMethods": "All Methods", "traderType": "Trader Type", "allTraders": "All Traders", "verifiedOnly": "Verified Only", "onlineOnly": "Online Only", "highRating": "High Rating (4.5+)"}, "searchPlaceholder": "Search offers...", "sort": {"priceAsc": "Price: Low to High", "priceDesc": "Price: High to Low", "ratingDesc": "Rating: High to Low", "amountDesc": "Amount: High to Low", "timeAsc": "Time: Oldest First"}, "noOffers": "No offers available", "noOffersDesc": "No offers match your criteria", "createFirstOffer": "Create First Offer", "verified": "Verified", "online": "Online", "offline": "Offline", "trades": "trades", "price": "Price", "perUSDT": "per USDT", "available": "Available", "limits": "Limits", "timeLeft": "Time Left", "paymentMethods": "Payment Methods", "terms": "Terms", "calculator": "Calculator", "viewProfile": "View Profile", "sellTo": "Sell to", "buyFrom": "Buy from", "marketStats": "Market Statistics", "buyOffers": "Buy Offers", "sellOffers": "<PERSON><PERSON>", "avgPrice": "Average Price", "totalVolume": "Total Volume"}, "price": "Price", "rating": "Rating", "cancelled": "Cancelled", "failed": "Failed", "Yes": "Yes", "Exists": "Exists", "contractEvents": {"messages": {"loadFailed": "Failed to load contract events", "monitoringStopped": "Event monitoring stopped", "monitoringStarted": "Event monitoring started", "monitoringRestarted": "Event monitoring restarted", "eventProcessed": "Event processed successfully"}, "types": {"tradeCreated": "Trade Created", "buyerJoined": "Buyer Joined", "paymentSent": "Payment Sent", "paymentConfirmed": "Payment Confirmed", "tradeCompleted": "Trade Completed", "tradeCancelled": "Trade Cancelled", "tradeDisputed": "Trade Disputed", "disputeResolved": "Dispute Resolved"}, "title": "Smart Contract Events", "actions": {"restart": "Restart Monitoring", "stop": "Stop Monitoring", "start": "Start Monitoring", "forceProcess": "Force Process", "viewOnBscscan": "View on BSCScan"}, "stats": {"totalProcessed": "Total Processed", "processedToday": "Processed Today", "lastBlock": "Last Block", "errors": "Errors"}, "status": {"monitoring": "Monitoring", "stopped": "Stopped", "lastError": "Last Error", "processed": "Processed", "pending": "Pending"}, "filters": {"allTypes": "All Event Types", "allStatus": "All Status", "processed": "Processed", "unprocessed": "Unprocessed", "tradeIdPlaceholder": "Search by Trade ID..."}, "table": {"eventType": "Event Type", "tradeId": "Trade ID", "amount": "Amount", "status": "Status", "timestamp": "Timestamp", "actions": "Actions"}}, "contractManagement": {"messages": {"updateInterval": "Update interval set to {interval} seconds", "loadError": "Error loading contract data", "loadFailed": "Failed to load contract information", "feeRateInvalid": "Invalid fee rate. Must be between 0 and 100", "feeRateUpdateFailed": "Failed to update fee rate", "walletAddressInvalid": "Invalid wallet address", "feeWalletUpdateFailed": "Failed to update fee wallet", "timeoutsUpdated": "Timeouts updated successfully", "timeoutsUpdateFailed": "Failed to update timeouts", "autoDisputeFailed": "Failed to trigger auto dispute", "contractPauseFailed": "Failed to pause contract", "contractUnpauseFailed": "Failed to unpause contract"}, "timeUnits": {"hours": "hours", "minutes": "minutes"}, "title": "Contract Management", "currentNetwork": "Current Network", "disconnected": "Disconnected", "testnet": "Testnet", "pauseContract": "Pause Contract", "unpauseContract": "Unpause Contract", "tabs": {"stats": "Statistics", "settings": "Settings", "disputes": "Auto Disputes"}, "stats": {"totalTrades": "Total Trades", "activeTrades": "Active Trades", "totalVolume": "Total Volume", "contractBalance": "Contract Balance"}, "settings": {"feeSettings": "<PERSON><PERSON>", "feeRate": "Fee Rate (%)", "update": "Update", "feeWallet": "<PERSON>e Wallet Address", "timeoutSettings": "Timeout Settings"}, "labels": {"currentFees": "Current Fees", "autoTimeoutDuration": "Auto Timeout Duration", "currentDuration": "Current Duration", "cancelWaitDuration": "Cancel Wait Duration", "autoDisputeDuration": "Auto Dispute Duration", "updateAllTimeouts": "Update All Timeouts", "eligibleTradesTitle": "Eligible Trades for Auto Dispute", "noEligibleTrades": "No eligible trades found", "tradeEligibleDescription": "Trade is eligible for auto dispute", "triggerDispute": "<PERSON><PERSON>"}, "disputes": {"refresh": "Refresh"}}, "currencies": {"noResults": "No currencies found"}, "pagination": {"showing": "Showing", "of": "of", "previous": "Previous", "next": "Next"}, "remove": "Remove", "current": "Current", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal": "<PERSON><PERSON><PERSON>", "": {}, "verified": "Verified", "rejected": "Rejected", "uploaded": "Uploaded", "network": {"unsupported": "Unsupported Network", "switchFailed": "Failed to switch network", "disconnected": "Network Disconnected", "testnet": "Testnet", "mainnet": "Mainnet", "selectNetwork": "Select Network", "selectNetworkDescription": "Choose the network for trading", "testnetDescription": "Test network for development", "mainnetDescription": "Main network for live trading", "testnetNote": "Testnet: For testing only", "mainnetNote": "Mainnet: For real trading"}, "payment": "Payment", "low": "Low", "pwa": {"updateAvailable": "Update Available", "install": {"ios": {"title": "Install on iOS", "step1": "Tap the share button", "step2": "Scroll down and tap \"Add to Home Screen\"", "step3": "Tap \"Add\" to confirm"}, "android": {"title": "Install on Android", "step1": "Tap the menu button", "step2": "Tap \"Add to Home Screen\"", "step3": "Tap \"Add\" to confirm"}, "desktop": {"title": "Install on Desktop", "step1": "Click the install button in the address bar", "step2": "Click \"Install\" in the popup", "step3": "The app will be added to your desktop"}, "button": "Install App"}, "installBanner": {"title": "Install IKAROS P2P", "description": "Install our app for a better experience"}, "status": {"online": "Online", "offline": "Offline", "ready": "Ready"}}, "connected": "Connected", "connecting": "Connecting", "disconnected": "Disconnected", "realtime": {"connected": "Connected", "connecting": "Connecting...", "error": "Connection Error", "disconnected": "Disconnected", "justNow": "Just now"}, "error": "Error", "message": "Message", "critical": "Critical", "confirmed": "Confirmed", "buy": "Buy", "Testnet": "Testnet", "tradeChat": {"messages": {"tradeStarted": "Trade has been started", "sellerWelcome": "Welcome! Please follow the payment instructions", "buyerResponse": "Thank you, I will proceed with payment", "messageSent": "Message sent", "messageReported": "Message reported"}, "errors": {"loadMessages": "Failed to load messages", "sendMessage": "Failed to send message"}, "title": "Trade Chat", "actions": {"moreOptions": "More Options", "report": "Report", "attachFile": "Attach File", "attachImage": "Attach Image", "sending": "Sending...", "send": "Send"}, "input": {"placeholder": "Type your message..."}, "security": {"title": "Security Notice", "warning": "Never share personal information or payment details in chat"}}, "seller": "<PERSON><PERSON>", "tradeDetails": {"actions": {"confirmingPaymentSent": "Confirming payment sent...", "releasingFunds": "Releasing funds...", "requestingCancel": "Requesting cancellation...", "requestingDispute": "Requesting dispute...", "refresh": "Refresh", "confirmPaymentSent": "Confirm Payment Sent", "releaseFunds": "Release Funds", "openChat": "Open Chat", "requestCancel": "Request Cancel", "requestDispute": "Request Dispute"}, "messages": {"waitingConfirmation": "Waiting for confirmation...", "paymentSentConfirmed": "Payment sent confirmed", "fundsReleased": "Funds released successfully", "confirmCancel": "Are you sure you want to cancel this trade?", "cancelRequested": "Cancellation requested", "confirmDispute": "Are you sure you want to open a dispute?", "disputeRequested": "Dispute requested", "copied": "Copied to clipboard"}, "title": "Trade Details", "sections": {"basicInfo": "Basic Information", "participants": "Participants", "actions": "Actions"}, "fields": {"status": "Status", "amount": "Amount", "totalValue": "Total Value", "price": "Price", "platformFee": "Platform Fee", "seller": "<PERSON><PERSON>", "buyer": "Buyer"}}, "created": "Created", "joined": "Joined", "today": "Today", "week": "Week", "month": "Month", "success": "Success", "walletconnect": "WalletConnect", "trustwallet": "Trust Wallet", "fee": "Fee", "reward": "<PERSON><PERSON>", "name": "Name", "dark": "Dark", "AED": "AED", "USD": "USD", "EUR": "EUR", "en": "English", "user": "User", "offer": "Offer", "dispute": "Dispute", "loading": "Loading", "info": "Info", "warning": "Warning", "ar": "Arabic", "alertsCenter": {"title": "Advanced Alerts Center", "subtitle": "Comprehensive alerts and warnings management with smart integration", "filters": "Filters", "refresh": "Refresh", "search": {"placeholder": "Search alerts..."}, "stats": {"total": "Total Alerts", "active": "Active", "resolved": "Resolved", "critical": "Critical", "high": "High", "medium": "Medium", "low": "Low"}, "sorting": {"newest": "Newest First", "oldest": "Oldest First", "highPriority": "High Priority First", "lowPriority": "Low Priority First", "byType": "By Type"}, "bulkActions": {"selected": "Specified", "resolveAll": "Resolve All", "archive": "Archive"}, "filterOptions": {"type": "Type", "priority": "Priority", "status": "Status", "timeRange": "Time Range", "allTypes": "All Types", "allPriorities": "All Priorities", "allStatuses": "All Statuses", "allTimeRanges": "All Time Ranges", "trade": "Trading", "security": "Security", "system": "System", "contract": "Smart Contract", "performance": "Performance", "urgent": "<PERSON><PERSON>", "active": "Active", "archived": "Archived", "lastHour": "Last Hour", "last24Hours": "Last 24 Hours", "lastWeek": "Last Week", "lastMonth": "Last Month"}, "alertTypes": {"security": "Security", "system": "System", "trade": "Trading", "urgent": "<PERSON><PERSON>"}, "mockAlerts": {"suspiciousLogin": {"title": "Suspicious Login Attempt", "message": "Multiple failed login attempts detected from suspicious IP"}, "highMemoryUsage": {"title": "High Memory Usage", "message": "Memory usage reached 89% of total capacity"}, "highGasFees": {"title": "High Gas Fees", "message": "Gas fees increased to 45 Gwei, may affect transactions"}}, "errors": {"loadingAlerts": "Error loading alerts"}}}