'use client';

import { useState, useEffect } from 'react';
import { 
  ShoppingBag,
  Plus,
  Filter,
  Search,
  Eye,
  Edit,
  Pause,
  Play,
  Trash2,
  Copy,
  TrendingUp,
  TrendingDown,
  Clock,
  Users,
  MessageSquare,
  BarChart3,
  Settings,
  ArrowUpDown
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet, handleApiError } from '@/utils/apiClient';

interface Offer {
  id: string;
  type: 'buy' | 'sell';
  amount: number;
  minAmount: number;
  maxAmount: number;
  currency: string;
  price: number;
  margin: number;
  paymentMethods: string[];
  terms: string;
  autoReply: string;
  timeLimit: number; // بالدقائق
  status: 'active' | 'paused' | 'expired' | 'draft';
  views: number;
  responses: number;
  completedTrades: number;
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
}

type OfferFilter = 'all' | 'active' | 'paused' | 'expired' | 'draft';
type SortField = 'createdAt' | 'views' | 'responses' | 'amount' | 'price';
type SortOrder = 'asc' | 'desc';

export default function OffersPage() {
  const { t, formatCurrency, formatDate } = useUserDashboardTranslation();
  const { user } = useAuth();
  const [offers, setOffers] = useState<Offer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<OfferFilter>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<SortField>('createdAt');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');

  // جلب العروض
  useEffect(() => {
    const fetchOffers = async () => {
      if (!user?.id) {
        setError('معرف المستخدم غير متاح');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // استدعاء API الحقيقي لجلب عروض المستخدم
        const response = await apiGet(`offers/my-offers.php?user_id=${user.id}&status=${filter}`);

        if (!response.success) {
          throw new Error(response.message || 'فشل في جلب العروض');
        }

        // تحويل البيانات من API إلى تنسيق Offer
        const apiOffers = response.data || [];
        const formattedOffers = apiOffers.map((offer: any) => ({
          id: offer.id,
          type: offer.offer_type,
          amount: parseFloat(offer.amount),
          currency: offer.currency,
          price: parseFloat(offer.price),
          status: offer.is_active ? 'active' : 'inactive',
          createdAt: offer.created_at,
          views: parseInt(offer.views) || 0,
          responses: parseInt(offer.responses) || 0
        }));

        setOffers(formattedOffers);
      } catch (error) {
        console.error('Error fetching offers:', error);
        const errorMessage = handleApiError(error);
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchOffers();
  }, [filter, user?.id]);

  // تصفية وترتيب العروض
  const filteredOffers = offers
    .filter(offer => {
      if (filter !== 'all' && offer.status !== filter) return false;
      if (searchTerm && !offer.currency.toLowerCase().includes(searchTerm.toLowerCase())) return false;
      return true;
    })
    .sort((a, b) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];
      
      if (typeof aValue === 'string') {
        return sortOrder === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    });

  // الحصول على لون الحالة
  const getStatusColor = (status: Offer['status']) => {
    switch (status) {
      case 'active':
        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';
      case 'paused':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30';
      case 'expired':
        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';
      case 'draft':
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  // الحصول على نص الحالة
  const getStatusText = (status: Offer['status']) => {
    switch (status) {
      case 'active':
        return 'نشط';
      case 'paused':
        return 'متوقف';
      case 'expired':
        return 'منتهي';
      case 'draft':
        return 'مسودة';
      default:
        return 'غير معروف';
    }
  };

  // إحصائيات سريعة
  const stats = {
    total: offers.length,
    active: offers.filter(o => o.status === 'active').length,
    paused: offers.filter(o => o.status === 'paused').length,
    totalViews: offers.reduce((sum, o) => sum + o.views, 0),
    totalResponses: offers.reduce((sum, o) => sum + o.responses, 0)
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            {t('offers.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            إدارة وتتبع عروضك
          </p>
        </div>

        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <a
            href="/user-dashboard/offers/create"
            className="flex items-center space-x-2 rtl:space-x-reverse bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>إنشاء عرض جديد</span>
          </a>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-2 sm:grid-cols-5 gap-4">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <ShoppingBag className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <span className="text-sm text-gray-600 dark:text-gray-400">الإجمالي</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{stats.total}</p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <Play className="w-5 h-5 text-green-600 dark:text-green-400" />
            <span className="text-sm text-gray-600 dark:text-gray-400">نشطة</span>
          </div>
          <p className="text-2xl font-bold text-green-600 dark:text-green-400 mt-1">{stats.active}</p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <Pause className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            <span className="text-sm text-gray-600 dark:text-gray-400">متوقفة</span>
          </div>
          <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400 mt-1">{stats.paused}</p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <Eye className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            <span className="text-sm text-gray-600 dark:text-gray-400">المشاهدات</span>
          </div>
          <p className="text-2xl font-bold text-purple-600 dark:text-purple-400 mt-1">{stats.totalViews}</p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <MessageSquare className="w-5 h-5 text-orange-600 dark:text-orange-400" />
            <span className="text-sm text-gray-600 dark:text-gray-400">الردود</span>
          </div>
          <p className="text-2xl font-bold text-orange-600 dark:text-orange-400 mt-1">{stats.totalResponses}</p>
        </div>
      </div>

      {/* المرشحات والبحث */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* البحث */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 rtl:right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="البحث بالعملة..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 rtl:pr-10 pr-3 rtl:pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* المرشحات */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Filter className="w-4 h-4 text-gray-500" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as OfferFilter)}
                className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع العروض</option>
                <option value="active">النشطة</option>
                <option value="paused">المتوقفة</option>
                <option value="expired">المنتهية</option>
                <option value="draft">المسودات</option>
              </select>
            </div>

            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <ArrowUpDown className="w-4 h-4 text-gray-500" />
              <select
                value={`${sortField}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-');
                  setSortField(field as SortField);
                  setSortOrder(order as SortOrder);
                }}
                className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="createdAt-desc">الأحدث أولاً</option>
                <option value="createdAt-asc">الأقدم أولاً</option>
                <option value="views-desc">الأكثر مشاهدة</option>
                <option value="responses-desc">الأكثر رداً</option>
                <option value="amount-desc">المبلغ (الأعلى)</option>
                <option value="price-desc">السعر (الأعلى)</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* قائمة العروض */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        {loading ? (
          <div className="p-6">
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded-lg" />
                </div>
              ))}
            </div>
          </div>
        ) : filteredOffers.length > 0 ? (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredOffers.map((offer) => (
              <div key={offer.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 rtl:space-x-reverse flex-1">
                    {/* نوع العرض */}
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                      offer.type === 'buy' ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30'
                    }`}>
                      {offer.type === 'buy' ? (
                        <TrendingUp className="w-6 h-6 text-green-600 dark:text-green-400" />
                      ) : (
                        <TrendingDown className="w-6 h-6 text-red-600 dark:text-red-400" />
                      )}
                    </div>

                    {/* تفاصيل العرض */}
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {offer.type === 'buy' ? 'شراء' : 'بيع'} {offer.currency}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(offer.status)}`}>
                          {getStatusText(offer.status)}
                        </span>
                        {offer.margin > 0 && (
                          <span className="text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded-full">
                            +{offer.margin}%
                          </span>
                        )}
                        {offer.margin < 0 && (
                          <span className="text-xs bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 px-2 py-1 rounded-full">
                            {offer.margin}%
                          </span>
                        )}
                      </div>
                      
                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-3">
                        <div>
                          <p className="text-xs text-gray-500 dark:text-gray-400">السعر</p>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {formatCurrency(offer.price, 'SAR')}
                          </p>
                        </div>
                        
                        <div>
                          <p className="text-xs text-gray-500 dark:text-gray-400">النطاق</p>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {offer.minAmount} - {offer.maxAmount} {offer.currency}
                          </p>
                        </div>
                        
                        <div>
                          <p className="text-xs text-gray-500 dark:text-gray-400">المشاهدات</p>
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <Eye className="w-3 h-3 text-gray-400" />
                            <span className="text-sm font-medium text-gray-900 dark:text-white">{offer.views}</span>
                          </div>
                        </div>
                        
                        <div>
                          <p className="text-xs text-gray-500 dark:text-gray-400">الردود</p>
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <MessageSquare className="w-3 h-3 text-gray-400" />
                            <span className="text-sm font-medium text-gray-900 dark:text-white">{offer.responses}</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4 rtl:space-x-reverse text-xs text-gray-500 dark:text-gray-400">
                        <div className="flex items-center space-x-1 rtl:space-x-reverse">
                          <Clock className="w-3 h-3" />
                          <span>مهلة: {offer.timeLimit} دقيقة</span>
                        </div>
                        
                        <span>•</span>
                        
                        <span>تم الإنشاء: {formatDate(offer.createdAt, { 
                          day: 'numeric', 
                          month: 'short'
                        })}</span>
                        
                        <span>•</span>
                        
                        <span>{offer.completedTrades} صفقة مكتملة</span>
                      </div>
                    </div>
                  </div>

                  {/* الإجراءات */}
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <button className="p-2 text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors" title="عرض">
                      <Eye className="w-4 h-4" />
                    </button>
                    
                    <button className="p-2 text-gray-500 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors" title="تعديل">
                      <Edit className="w-4 h-4" />
                    </button>
                    
                    {offer.status === 'active' ? (
                      <button className="p-2 text-gray-500 dark:text-gray-400 hover:text-yellow-600 dark:hover:text-yellow-400 transition-colors" title="إيقاف مؤقت">
                        <Pause className="w-4 h-4" />
                      </button>
                    ) : (
                      <button className="p-2 text-gray-500 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors" title="تفعيل">
                        <Play className="w-4 h-4" />
                      </button>
                    )}
                    
                    <button className="p-2 text-gray-500 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors" title="نسخ">
                      <Copy className="w-4 h-4" />
                    </button>
                    
                    <button className="p-2 text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors" title="حذف">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-12 text-center">
            <ShoppingBag className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              لا توجد عروض
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {filter === 'all' 
                ? 'لم تقم بإنشاء أي عروض بعد'
                : `لا توجد عروض ${getStatusText(filter as Offer['status'])}`
              }
            </p>
            <a
              href="/user-dashboard/offers/create"
              className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>إنشاء عرض جديد</span>
            </a>
          </div>
        )}
      </div>
    </div>
  );
}
