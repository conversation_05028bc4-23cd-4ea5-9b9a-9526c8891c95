'use client';

import React, { useState, useEffect } from 'react';
import {
  Network,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Settings,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Clock,
  Zap,
  Shield,
  Activity,
  TrendingUp,
  Check,
  X,
  DollarSign,
  Globe,
  Server,
  Wifi,
  WifiOff,
  RefreshCw,
  Upload,
  ArrowUp,
  ArrowDown,
  Flag,
  Star,
  Coins,
  Database,
  BarChart3,
  PieChart,
  LineChart,
  Monitor,
  HardDrive,
  Cpu,
  MemoryStick,
  Link,
  ExternalLink,
  Copy,
  TestTube,
  Power,
  PowerOff
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { bscscanTestnet } from '@/services/bscscanService';

interface Network {
  id: string;
  name: string;
  chainId: number;
  type: 'mainnet' | 'testnet' | 'devnet' | 'private';
  status: 'active' | 'inactive' | 'maintenance' | 'error' | 'syncing';
  rpcUrl: string;
  explorerUrl: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  blockTime: number; // in seconds
  gasPrice: number;
  latency: number; // in ms
  uptime: number; // percentage
  blockHeight: number;
  nodeCount: number;
  isEnabled: boolean;
  lastSync: string;
  totalTransactions: number;
  dailyTransactions: number;
}

interface Token {
  id: string;
  name: string;
  symbol: string;
  address: string;
  networkId: string;
  type: 'native' | 'erc20' | 'erc721' | 'erc1155' | 'bep20' | 'trc20' | 'spl';
  decimals: number;
  totalSupply: number;
  marketCap: number;
  price: number;
  volume24h: number;
  status: 'active' | 'inactive' | 'pending' | 'error';
  isEnabled: boolean;
  logoUrl?: string;
  description?: string;
  website?: string;
  contractVerified: boolean;
  lastUpdated: string;
  holders: number;
  transfers24h: number;
}

interface NetworkTokenManagementProps {
  className?: string;
  isWalletConnected?: boolean;
  walletAddress?: string;
  currentNetwork?: string;
}

export default function NetworkTokenManagement({
  className = '',
  isWalletConnected = false,
  walletAddress = '',
  currentNetwork = ''
}: NetworkTokenManagementProps) {
  const { t, language, isRTL, getDirectionClasses, formatNumber, formatCurrency, formatDate, formatRelativeTime } = useAdminTranslation();
  const dirClasses = getDirectionClasses();

  const [activeTab, setActiveTab] = useState<'networks' | 'tokens'>('networks');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [selectedNetwork, setSelectedNetwork] = useState<Network | null>(null);
  const [selectedToken, setSelectedToken] = useState<Token | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  // حالة البيانات المحملة من API
  const [networks, setNetworks] = useState<Network[]>([]);
  const [tokens, setTokens] = useState<Token[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // حالات للتغييرات المعلقة من البلوك تشين
  const [pendingNetworks, setPendingNetworks] = useState<Network[]>([]);
  const [pendingTokens, setPendingTokens] = useState<Token[]>([]);
  const [hasPendingChanges, setHasPendingChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // استخدام حالة المحفظة من المكون الرئيسي (سيتم تمريرها كـ props لاحقاً)

  // تم حذف وظائف المحفظة - سيتم استخدام الاتصال الرئيسي من AdminDashboard

  // وظائف جلب البيانات من قاعدة البيانات أولاً، ثم من الشبكة
  const fetchNetworks = async (fromBlockchain = false) => {
    try {
      console.log(`🔄 Fetching networks from ${fromBlockchain ? 'blockchain' : 'database'}...`);

      // جلب من قاعدة البيانات أولاً
      const dbResponse = await fetch('/api/enhanced-contracts/database-sync?action=networks&active=1');
      const dbData = await dbResponse.json();

      // إذا كانت قاعدة البيانات تحتوي على بيانات وليس طلب تحديث من البلوك تشين
      if (dbData.success && dbData.data.networks.length > 0 && !fromBlockchain) {
        const formattedNetworks: Network[] = dbData.data.networks.map((network: any) => ({
          id: network.id.toString(),
          name: network.network_name,
          chainId: parseInt(network.chain_id),
          type: network.is_testnet ? 'testnet' : 'mainnet',
          status: network.is_active ? 'active' : 'inactive',
          rpcUrl: network.rpc_url,
          explorerUrl: network.explorer_url,
          nativeCurrency: {
            name: network.network_name,
            symbol: network.network_symbol,
            decimals: 18
          },
          blockTime: network.block_time_seconds || 3,
          gasPrice: parseFloat(network.gas_price_gwei) || 5,
          latency: 100,
          uptime: 99.5,
          blockHeight: 0,
          nodeCount: 1,
          isEnabled: network.is_active,
          lastSync: network.updated_at || network.created_at || new Date().toISOString(),
          totalTransactions: network.daily_transactions || 0,
          dailyTransactions: network.daily_transactions || 0
        }));


        setNetworks(formattedNetworks);
        return;
      }

      // إذا لم توجد بيانات في قاعدة البيانات أو طلب تحديث، جلب من البلوك تشين
      const response = await fetch('/api/enhanced-contracts/networks?action=networks&active=1');
      const data = await response.json();

      if (data.success && data.data && data.data.networks) {
        // حفظ البيانات في قاعدة البيانات
        try {
          console.log('💾 Saving networks to database...');
          const syncResponse = await fetch('/api/enhanced-contracts/database-sync?action=sync-networks', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              networks: data.data.networks
            }),
          });

          const syncResult = await syncResponse.json();
          console.log('💾 Database sync result:', syncResult);
        } catch (syncError) {
          console.warn('⚠️ Failed to sync to database:', syncError);
        }

        // تحويل البيانات من API إلى تنسيق المكون
        const formattedNetworks: Network[] = data.data.networks.map((network: any) => ({
          id: network.id.toString(),
          name: network.network_name,
          chainId: parseInt(network.chain_id),
          type: network.is_testnet ? 'testnet' : 'mainnet',
          status: network.is_active ? 'active' : 'inactive',
          rpcUrl: network.rpc_url,
          explorerUrl: network.explorer_url,
          nativeCurrency: {
            name: network.network_name,
            symbol: network.network_symbol,
            decimals: 18
          },
          blockTime: network.block_time_seconds || 3,
          gasPrice: parseFloat(network.gas_price_gwei) || 5,
          latency: 100,
          uptime: 99.5,
          blockHeight: 0,
          nodeCount: 1,
          isEnabled: network.is_active,
          lastSync: network.updated_at || network.created_at || new Date().toISOString(),
          totalTransactions: network.daily_transactions || 0,
          dailyTransactions: network.daily_transactions || 0
        }));

        console.log('✅ Formatted networks from blockchain:', formattedNetworks);
        setNetworks(formattedNetworks);
      } else {
        console.error('❌ Blockchain API response invalid:', data);
        throw new Error(data.message || 'فشل في جلب الشبكات');
      }
    } catch (error) {
      console.error('Error fetching networks:', error);
      setError('فشل في جلب بيانات الشبكات');
      // استخدام بيانات افتراضية في حالة الخطأ
      setNetworks(getDefaultNetworks());
    }
  };

  const fetchTokens = async (fromBlockchain = false) => {
    try {
      console.log(`🔄 Fetching tokens from ${fromBlockchain ? 'blockchain' : 'database'}...`);

      // جلب من قاعدة البيانات أولاً
      const dbResponse = await fetch('/api/enhanced-contracts/database-sync?action=tokens&active=1');
      console.log('📡 Database tokens response status:', dbResponse.status);
      const dbData = await dbResponse.json();
      console.log('📊 Database Tokens Response:', dbData);

      // إذا كانت قاعدة البيانات تحتوي على بيانات وليس طلب تحديث من البلوك تشين
      if (dbData.success && dbData.data.tokens.length > 0 && !fromBlockchain) {
        const formattedTokens: Token[] = dbData.data.tokens.map((token: any) => ({
          id: token.id.toString(),
          name: token.token_name,
          symbol: token.token_symbol,
          address: token.token_address,
          networkId: token.network_id.toString(),
          type: token.token_symbol === 'ETH' || token.token_symbol === 'BNB' ? 'native' : 'erc20',
          decimals: token.decimals,
          totalSupply: 0,
          marketCap: parseFloat(token.market_cap) || 0,
          price: parseFloat(token.current_price_usd) || 1.0,
          volume24h: parseFloat(token.volume_24h) || 0,
          status: token.is_active ? 'active' : 'inactive',
          isEnabled: token.is_active,
          contractVerified: true,
          lastUpdated: token.updated_at || token.created_at || new Date().toISOString(),
          holders: token.total_offers || 0,
          transfers24h: token.total_trades || 0
        }));

        console.log('✅ Tokens loaded from database:', formattedTokens);
        setTokens(formattedTokens);
        return;
      }

      // إذا لم توجد بيانات في قاعدة البيانات أو طلب تحديث، جلب من البلوك تشين
      console.log('🔄 Fetching tokens from blockchain...');
      const response = await fetch('/api/enhanced-contracts/networks?action=tokens&active=1');
      console.log('📡 Blockchain tokens response status:', response.status);
      const data = await response.json();
      console.log('📊 Blockchain Tokens API Response:', data);

      if (data.success && data.data && data.data.tokens) {
        // حفظ البيانات في قاعدة البيانات
        try {
          console.log('💾 Saving tokens to database...');
          const syncResponse = await fetch('/api/enhanced-contracts/database-sync?action=sync-tokens', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              tokens: data.data.tokens
            }),
          });

          const syncResult = await syncResponse.json();
          console.log('💾 Tokens database sync result:', syncResult);
        } catch (syncError) {
          console.warn('⚠️ Failed to sync tokens to database:', syncError);
        }

        // تحويل البيانات من API إلى تنسيق المكون
        const formattedTokens: Token[] = data.data.tokens.map((token: any) => ({
          id: token.id.toString(),
          name: token.token_name,
          symbol: token.token_symbol,
          address: token.token_address,
          networkId: token.network_id.toString(),
          type: token.token_symbol === 'ETH' || token.token_symbol === 'BNB' ? 'native' : 'erc20',
          decimals: token.decimals,
          totalSupply: 0,
          marketCap: parseFloat(token.market_cap) || 0,
          price: parseFloat(token.current_price_usd) || 1.0,
          volume24h: parseFloat(token.volume_24h) || 0,
          status: token.is_active ? 'active' : 'inactive',
          isEnabled: token.is_active,
          contractVerified: true,
          lastUpdated: token.updated_at || token.created_at || new Date().toISOString(),
          holders: token.total_offers || 0,
          transfers24h: token.total_trades || 0
        }));

        console.log('✅ Formatted tokens from blockchain:', formattedTokens);
        setTokens(formattedTokens);
      } else {
        console.error('❌ Blockchain tokens API response invalid:', data);
        throw new Error(data.message || 'فشل في جلب العملات');
      }
    } catch (error) {
      console.error('Error fetching tokens:', error);
      setError('فشل في جلب بيانات العملات');
      // استخدام بيانات افتراضية في حالة الخطأ
      setTokens(getDefaultTokens());
    }
  };

  // بيانات افتراضية في حالة فشل API
  const getDefaultNetworks = (): Network[] => {
    return [
    {
      id: '1',
      name: 'BSC Testnet',
      chainId: 97,
      type: 'testnet',
      status: 'active',
      rpcUrl: 'https://data-seed-prebsc-1-s1.binance.org:8545/',
      explorerUrl: 'https://testnet.bscscan.com',
      nativeCurrency: {
        name: 'BNB',
        symbol: 'tBNB',
        decimals: 18
      },
      blockTime: 3,
      gasPrice: 5,
      latency: 80,
      uptime: 99.8,
      blockHeight: 35000000,
      nodeCount: 100,
      isEnabled: true,
      lastSync: new Date().toISOString(),
      totalTransactions: 1000000,
      dailyTransactions: 5000
    }
    ];
  };

  const getDefaultTokens = (): Token[] => {
    return [
      {
        id: '1',
        name: 'Tether USD',
        symbol: 'USDT',
        address: '******************************************',
        networkId: '1',
        type: 'erc20',
        decimals: 18,
        totalSupply: 1000000000,
        marketCap: 1000000000,
        price: 1.00,
        volume24h: 10000000,
        status: 'active',
        isEnabled: true,
        contractVerified: true,
        lastUpdated: new Date().toISOString(),
        holders: 100000,
        transfers24h: 5000
      }
    ];
  };

  // تحديث البيانات عند تحميل المكون أو تغيير التبويب (من قاعدة البيانات أولاً)
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (activeTab === 'networks') {
          await fetchNetworks(false); // false = جلب من قاعدة البيانات أولاً
        } else {
          await fetchTokens(false); // false = جلب من قاعدة البيانات أولاً
        }
      } catch (error) {
        console.error('Error loading data:', error);
        setError('فشل في تحميل البيانات');
        // استخدام البيانات الافتراضية في حالة الخطأ
        if (activeTab === 'networks') {
          setNetworks(getDefaultNetworks());
        } else {
          setTokens(getDefaultTokens());
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [activeTab]);

  // وظائف إدارة الشبكات والعملات
  const handleNetworkToggle = async (networkId: string) => {
    try {
      const network = currentNetworks.find(n => n.id === networkId);
      if (!network) return;

      // تحديث حالة الشبكة في قاعدة البيانات
      const updatedNetwork: Network = {
        ...network,
        isEnabled: !network.isEnabled,
        status: (!network.isEnabled ? 'active' : 'inactive') as 'active' | 'inactive' | 'maintenance' | 'error' | 'syncing'
      };

      // تحديث الحالة المحلية
      if (hasPendingChanges && pendingNetworks.length > 0) {
        setPendingNetworks(pendingNetworks.map(n => n.id === networkId ? updatedNetwork : n));
      } else {
        setNetworks(networks.map(n => n.id === networkId ? updatedNetwork : n));
      }

      console.log(`🔄 Network ${network.name} ${updatedNetwork.isEnabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      console.error('Error toggling network:', error);
      setError('فشل في تحديث حالة الشبكة');
    }
  };

  const handleTokenToggle = async (tokenId: string) => {
    try {
      const token = currentTokens.find(t => t.id === tokenId);
      if (!token) return;

      // تحديث حالة العملة في قاعدة البيانات
      const updatedToken: Token = {
        ...token,
        isEnabled: !token.isEnabled,
        status: (!token.isEnabled ? 'active' : 'inactive') as 'active' | 'inactive' | 'error' | 'pending'
      };

      // تحديث الحالة المحلية
      if (hasPendingChanges && pendingTokens.length > 0) {
        setPendingTokens(pendingTokens.map(t => t.id === tokenId ? updatedToken : t));
      } else {
        setTokens(tokens.map(t => t.id === tokenId ? updatedToken : t));
      }

      console.log(`🔄 Token ${token.symbol} ${updatedToken.isEnabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      console.error('Error toggling token:', error);
      setError('فشل في تحديث حالة العملة');
    }
  };

  // وظيفة تحديث البيانات يدوياً من البلوك تشين (بدون حفظ)
  const refreshData = async () => {
    setIsRefreshing(true);
    setError(null);

    try {
      setLastRefresh(new Date());

      if (activeTab === 'networks') {
        // جلب البيانات الجديدة من البلوك تشين مباشرة
        const response = await fetch('/api/enhanced-contracts/networks.php?action=networks&from_blockchain=1');
        const data = await response.json();

        if (data.success && data.data && data.data.networks) {
          // تحويل البيانات للتأكد من وجود الحقول المطلوبة
          const processedNetworks = data.data.networks.map((network: any) => ({
            ...network,
            id: network.id || network.chainId || `network-${Date.now()}-${Math.random()}`,
            name: network.name || network.network_name || network.networkName || 'Unknown Network',
            chainId: parseInt(network.chainId || network.chain_id) || 1,
            type: network.type || (network.is_testnet ? 'testnet' : 'mainnet') || 'mainnet',
            status: network.status || (network.is_active ? 'active' : 'inactive') || 'active',
            rpcUrl: network.rpcUrl || network.rpc_url || '',
            explorerUrl: network.explorerUrl || network.explorer_url || '',
            nativeCurrency: network.nativeCurrency || {
              name: network.network_name || 'Unknown',
              symbol: network.network_symbol || network.symbol || 'ETH',
              decimals: 18
            },
            blockTime: network.blockTime || network.block_time_seconds || 3,
            gasPrice: parseFloat(network.gasPrice || network.gas_price_gwei) || 5,
            latency: network.latency || 100,
            uptime: network.uptime || 99.5,
            blockHeight: network.blockHeight || 0,
            nodeCount: network.nodeCount || 1,
            isEnabled: network.isEnabled !== undefined ? network.isEnabled : network.is_active !== undefined ? network.is_active : true,
            totalTransactions: network.totalTransactions || network.daily_transactions || 0,
            dailyTransactions: network.dailyTransactions || network.daily_transactions || 0,
            lastSync: network.lastSync || network.updated_at || network.created_at || new Date().toISOString()
          }));

          setPendingNetworks(processedNetworks);
          setHasPendingChanges(true);
          console.log('✅ تم جلب بيانات الشبكات الجديدة من البلوك تشين', processedNetworks);
        } else {
          throw new Error('فشل في جلب بيانات الشبكات من البلوك تشين');
        }
      } else {
        // جلب البيانات الجديدة من البلوك تشين مباشرة
        const response = await fetch('/api/enhanced-contracts/networks.php?action=tokens&from_blockchain=1');
        const data = await response.json();

        if (data.success && data.data && data.data.tokens) {
          // تحويل البيانات للتأكد من وجود الحقول المطلوبة
          const processedTokens = data.data.tokens.map((token: any) => ({
            ...token,
            id: token.id || token.address || `token-${Date.now()}-${Math.random()}`,
            name: token.name || token.token_name || 'Unknown Token',
            symbol: token.symbol || token.token_symbol || 'UNK',
            address: token.address || token.token_address || '',
            networkId: token.networkId || token.network_id || '1',
            decimals: token.decimals || 18,
            totalSupply: token.totalSupply || 0,
            marketCap: parseFloat(token.marketCap || token.market_cap) || 0,
            price: parseFloat(token.price || token.current_price_usd) || 1.0,
            volume24h: parseFloat(token.volume24h || token.volume_24h) || 0,
            status: token.status || (token.is_active ? 'active' : 'inactive') || 'active',
            isEnabled: token.isEnabled !== undefined ? token.isEnabled : token.is_active !== undefined ? token.is_active : true,
            contractVerified: token.contractVerified !== undefined ? token.contractVerified : true,
            type: token.type || 'erc20',
            holders: token.holders || token.total_offers || 0,
            transfers24h: token.transfers24h || token.total_trades || 0,
            lastUpdated: token.lastUpdated || token.updated_at || token.created_at || new Date().toISOString()
          }));

          setPendingTokens(processedTokens);
          setHasPendingChanges(true);
          console.log('✅ تم جلب بيانات العملات الجديدة من البلوك تشين', processedTokens);
        } else {
          throw new Error('فشل في جلب بيانات العملات من البلوك تشين');
        }
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
      setError('فشل في تحديث البيانات من البلوك تشين');
    } finally {
      setIsRefreshing(false);
    }
  };

  // وظيفة حفظ التغييرات المعلقة في قاعدة البيانات
  const saveChanges = async () => {
    setIsSaving(true);
    setError(null);

    try {
      if (activeTab === 'networks' && pendingNetworks.length > 0) {
        console.log('🔄 حفظ الشبكات:', pendingNetworks);

        // تحويل البيانات للتنسيق المطلوب لـ PHP API
        const networksForAPI = pendingNetworks.map(network => ({
          id: network.id,
          network_name: network.name,
          chain_id: network.chainId,
          rpc_url: network.rpcUrl,
          explorer_url: network.explorerUrl,
          network_symbol: network.nativeCurrency?.symbol || 'ETH',
          is_testnet: network.type === 'testnet',
          is_active: network.isEnabled,
          block_time_seconds: network.blockTime,
          gas_price_gwei: network.gasPrice,
          confirmation_blocks: 12, // قيمة افتراضية
          daily_transactions: network.dailyTransactions
        }));

        // حفظ الشبكات في قاعدة البيانات
        const syncResponse = await fetch('/api/enhanced-contracts/database-sync?action=sync-networks', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            networks: networksForAPI
          }),
        });

        console.log('📡 استجابة API:', syncResponse.status);

        if (!syncResponse.ok) {
          throw new Error(`HTTP error! status: ${syncResponse.status}`);
        }

        const syncResult = await syncResponse.json();
        console.log('📊 نتيجة المزامنة:', syncResult);

        if (syncResult.success) {
          // تحديث البيانات المعروضة
          setNetworks(pendingNetworks);
          setPendingNetworks([]);
          setHasPendingChanges(false);
          console.log('✅ تم حفظ بيانات الشبكات في قاعدة البيانات');
        } else {
          throw new Error(syncResult.error || syncResult.message || 'فشل في حفظ بيانات الشبكات');
        }
      } else if (activeTab === 'tokens' && pendingTokens.length > 0) {
        console.log('🔄 حفظ العملات:', pendingTokens);

        // تحويل البيانات للتنسيق المطلوب لـ PHP API
        const tokensForAPI = pendingTokens.map(token => ({
          id: token.id,
          token_name: token.name,
          token_symbol: token.symbol,
          token_address: token.address,
          network_id: token.networkId,
          decimals: token.decimals,
          is_stablecoin: token.symbol === 'USDT' || token.symbol === 'USDC' || token.symbol === 'DAI',
          is_active: token.isEnabled,
          coingecko_id: null, // قيمة افتراضية
          min_trade_amount: 1, // قيمة افتراضية
          max_trade_amount: 1000000, // قيمة افتراضية
          daily_volume_limit: 10000000, // قيمة افتراضية
          platform_fee_rate: 0.001, // قيمة افتراضية
          market_cap: token.marketCap,
          current_price_usd: token.price,
          volume_24h: token.volume24h,
          total_offers: token.holders,
          total_trades: token.transfers24h
        }));

        // حفظ العملات في قاعدة البيانات
        const syncResponse = await fetch('/api/enhanced-contracts/database-sync?action=sync-tokens', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            tokens: tokensForAPI
          }),
        });

        console.log('📡 استجابة API:', syncResponse.status);

        if (!syncResponse.ok) {
          throw new Error(`HTTP error! status: ${syncResponse.status}`);
        }

        const syncResult = await syncResponse.json();
        console.log('📊 نتيجة المزامنة:', syncResult);

        if (syncResult.success) {
          // تحديث البيانات المعروضة
          setTokens(pendingTokens);
          setPendingTokens([]);
          setHasPendingChanges(false);
          console.log('✅ تم حفظ بيانات العملات في قاعدة البيانات');
        } else {
          throw new Error(syncResult.error || syncResult.message || 'فشل في حفظ بيانات العملات');
        }
      }
    } catch (error: any) {
      console.error('❌ Error saving changes:', error);

      let errorMessage = 'فشل في حفظ التغييرات في قاعدة البيانات';

      if (error.message) {
        if (error.message.includes('HTTP error')) {
          errorMessage = 'خطأ في الاتصال بالخادم';
        } else if (error.message.includes('Failed to fetch')) {
          errorMessage = 'فشل في الاتصال بالخادم';
        } else {
          errorMessage = error.message;
        }
      }

      setError(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  // وظيفة إلغاء التغييرات المعلقة
  const discardChanges = () => {
    setPendingNetworks([]);
    setPendingTokens([]);
    setHasPendingChanges(false);
  };

  // استخدام البيانات المعلقة إذا وجدت، وإلا البيانات العادية
  const currentNetworks = hasPendingChanges && pendingNetworks.length > 0 ? pendingNetworks : networks;

  const filteredNetworks = currentNetworks.filter(network => {
    if (!network) return false;

    const matchesSearch = (network.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (network.nativeCurrency?.symbol || '').toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = selectedFilter === 'all' ||
                         (selectedFilter === 'active' && network.status === 'active') ||
                         (selectedFilter === 'inactive' && network.status === 'inactive') ||
                         (selectedFilter === 'mainnet' && network.type === 'mainnet') ||
                         (selectedFilter === 'testnet' && network.type === 'testnet');

    return matchesSearch && matchesFilter;
  });

  // استخدام البيانات المعلقة إذا وجدت، وإلا البيانات العادية
  const currentTokens = hasPendingChanges && pendingTokens.length > 0 ? pendingTokens : tokens;

  const filteredTokens = currentTokens.filter(token => {
    if (!token) return false;

    const matchesSearch = (token.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (token.symbol || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (token.address || '').toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = selectedFilter === 'all' ||
                         (selectedFilter === 'active' && token.status === 'active') ||
                         (selectedFilter === 'inactive' && token.status === 'inactive') ||
                         (selectedFilter === 'erc20' && token.type === 'erc20') ||
                         (selectedFilter === 'native' && token.type === 'native') ||
                         (selectedFilter === 'highVolume' && token.volume24h > 1000000000);

    return matchesSearch && matchesFilter;
  });

  const networkStats = {
    total: networks.length,
    active: networks.filter(n => n.status === 'active').length,
    inactive: networks.filter(n => n.status === 'inactive').length,
    maintenance: networks.filter(n => n.status === 'maintenance').length,
    avgUptime: networks.length > 0 ? networks.reduce((sum, n) => sum + (n.uptime || 0), 0) / networks.length : 0,
    totalTransactions: networks.reduce((sum, n) => sum + (n.dailyTransactions || 0), 0)
  };

  const tokenStats = {
    total: tokens.length,
    active: tokens.filter(t => t.status === 'active').length,
    inactive: tokens.filter(t => t.status === 'inactive').length,
    verified: tokens.filter(t => t.contractVerified).length,
    totalMarketCap: tokens.reduce((sum, t) => sum + (t.marketCap || 0), 0),
    totalVolume24h: tokens.reduce((sum, t) => sum + (t.volume24h || 0), 0)
  };

  const handleAction = async (itemId: string, action: string) => {
    console.log(`🔄 Action ${action} for item ${itemId}`);

    try {
      switch (action) {
        case 'enable':
        case 'disable':
          if (activeTab === 'networks') {
            await handleNetworkToggle(itemId);
          } else {
            await handleTokenToggle(itemId);
          }
          break;

        case 'configure':
          // فتح modal الإعدادات
          const network = currentNetworks.find(n => n.id === itemId);
          if (network) {
            setSelectedNetwork(network);
            setShowDetails(true);
          }
          break;

        case 'edit':
          // فتح modal التحرير
          const token = currentTokens.find(t => t.id === itemId);
          if (token) {
            setSelectedToken(token);
            setShowDetails(true);
          }
          break;

        case 'test':
          // اختبار الاتصال بالشبكة
          console.log('🧪 Testing network connection...');
          // سيتم تنفيذها لاحقاً
          break;

        case 'sync':
          // مزامنة البيانات من البلوك تشين
          console.log('🔄 Syncing data from blockchain...');
          if (activeTab === 'networks') {
            await fetchNetworks(true);
          } else {
            await fetchTokens(true);
          }
          break;

        case 'view':
          // عرض التفاصيل
          if (activeTab === 'networks') {
            const network = currentNetworks.find(n => n.id === itemId);
            if (network) {
              setSelectedNetwork(network);
              setShowDetails(true);
            }
          } else {
            const token = currentTokens.find(t => t.id === itemId);
            if (token) {
              setSelectedToken(token);
              setShowDetails(true);
            }
          }
          break;

        default:
          console.warn(`⚠️ Unknown action: ${action}`);
      }
    } catch (error) {
      console.error(`❌ Error executing action ${action}:`, error);
      setError(`فشل في تنفيذ العملية: ${action}`);
    }
  };

  const handleBulkAction = (action: string) => {
    console.log(`Bulk action ${action} for items:`, selectedItems);
    setShowBulkActions(false);
    setSelectedItems([]);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'gray';
      case 'maintenance': return 'yellow';
      case 'error': return 'red';
      case 'syncing': return 'blue';
      case 'pending': return 'orange';
      default: return 'gray';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'mainnet': return 'blue';
      case 'testnet': return 'purple';
      case 'devnet': return 'orange';
      case 'private': return 'gray';
      case 'erc20': return 'green';
      case 'native': return 'blue';
      default: return 'gray';
    }
  };

  const StatusBadge = ({ status }: { status: string }) => {
    const color = getStatusColor(status);
    const StatusIcon = status === 'active' ? CheckCircle : 
                      status === 'error' ? XCircle :
                      status === 'maintenance' ? Settings :
                      status === 'syncing' ? RefreshCw : Clock;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${color}-100 text-${color}-800 dark:bg-${color}-900/30 dark:text-${color}-400`}>
        <StatusIcon className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'} ${status === 'syncing' ? 'animate-spin' : ''}`} />
        {t(`networks.status.${status}`)}
      </span>
    );
  };

  const TypeBadge = ({ type }: { type: string }) => {
    const color = getTypeColor(type);
    
    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-${color}-100 text-${color}-800 dark:bg-${color}-900/30 dark:text-${color}-400`}>
        {t(`networks.${activeTab === 'networks' ? 'networkTypes' : 'tokenTypes'}.${type}`)}
      </span>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className={`flex flex-col sm:flex-row sm:items-center justify-between gap-4 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
        <div className={isRTL ? 'text-right' : 'text-left'}>
          <h3 className={`text-xl font-bold text-gray-900 dark:text-white flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Network className={`w-6 h-6 text-blue-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            {t('networks.title')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('networks.subtitle')}</p>
        </div>
        <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {/* زر تحديث من البلوك تشين */}
          <button
            onClick={refreshData}
            disabled={isRefreshing || isSaving}
            className="flex items-center px-3 py-2 text-sm font-medium text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/20 border border-blue-300 dark:border-blue-600 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'} ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'جاري التحديث...' : 'تحديث من البلوك تشين'}
          </button>

          {/* زر حفظ التغييرات */}
          {hasPendingChanges && (
            <button
              onClick={saveChanges}
              disabled={isSaving || isRefreshing}
              className="flex items-center px-3 py-2 text-sm font-medium text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-300 dark:border-green-600 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors disabled:opacity-50"
            >
              <Check className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {isSaving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
            </button>
          )}

          {/* زر إلغاء التغييرات */}
          {hasPendingChanges && (
            <button
              onClick={discardChanges}
              disabled={isSaving || isRefreshing}
              className="flex items-center px-3 py-2 text-sm font-medium text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/20 border border-red-300 dark:border-red-600 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors disabled:opacity-50"
            >
              <X className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              إلغاء التغييرات
            </button>
          )}

          <button
            onClick={() => handleBulkAction('export')}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            <Download className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.export')}
          </button>
          <button className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
            <Plus className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t(`networks.actions.add${activeTab === 'networks' ? 'Network' : 'Token'}`)}
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className={`flex space-x-8 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          <button
            onClick={() => setActiveTab('networks')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'networks'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Server className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('networks.supportedNetworks')} ({networkStats.total})
            </div>
          </button>
          <button
            onClick={() => setActiveTab('tokens')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'tokens'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Coins className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('networks.supportedTokens')} ({tokenStats.total})
            </div>
          </button>
        </nav>
      </div>

      {/* Wallet Status Indicator */}
      {isWalletConnected && currentNetwork && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3 mb-6">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <CheckCircle className={`w-5 h-5 text-green-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <span className="text-sm font-medium text-green-800 dark:text-green-200">
                متصل بـ {currentNetwork}
              </span>
              <span className="text-xs text-green-600 dark:text-green-300 block">
                العنوان: {walletAddress} | BSCScan Integration: ✅
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
            <span className="text-gray-600 dark:text-gray-400">
              {activeTab === 'networks' ? 'جاري تحميل الشبكات...' : 'جاري تحميل العملات...'}
            </span>
          </div>
        </div>
      )}

      {/* Pending Changes Notification */}
      {hasPendingChanges && (
        <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg flex items-start">
          <AlertTriangle className={`w-5 h-5 text-yellow-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
          <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
            <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              يوجد تغييرات معلقة من البلوك تشين
            </h4>
            <p className="text-sm text-yellow-600 dark:text-yellow-300 mt-1">
              تم جلب بيانات محدثة من البلوك تشين. اضغط "حفظ التغييرات" لتطبيقها في قاعدة البيانات.
            </p>
            <div className="flex items-center gap-2 mt-3">
              <button
                onClick={saveChanges}
                disabled={isSaving}
                className="text-sm bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded transition-colors disabled:opacity-50"
              >
                {isSaving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
              </button>
              <button
                onClick={discardChanges}
                disabled={isSaving}
                className="text-sm text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-200 underline"
              >
                إلغاء التغييرات
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && !isLoading && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <AlertTriangle className={`w-5 h-5 text-red-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <h4 className="text-sm font-medium text-red-800 dark:text-red-200">خطأ في تحميل البيانات</h4>
              <p className="text-sm text-red-600 dark:text-red-300 mt-1">{error}</p>
              <button
                onClick={refreshData}
                className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline mt-2"
              >
                إعادة المحاولة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Stats Cards */}
      {!isLoading && !error && activeTab === 'networks' ? (
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={isRTL ? 'text-right' : 'text-left'}>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('networks.totalNetworks')}</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatNumber(networkStats.total)}</p>
              </div>
              <Server className="w-8 h-8 text-gray-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={isRTL ? 'text-right' : 'text-left'}>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('networks.activeNetworks')}</p>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">{formatNumber(networkStats.active)}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={isRTL ? 'text-right' : 'text-left'}>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Inactive</p>
                <p className="text-2xl font-bold text-gray-600 dark:text-gray-400">{formatNumber(networkStats.inactive)}</p>
              </div>
              <XCircle className="w-8 h-8 text-gray-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={isRTL ? 'text-right' : 'text-left'}>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Maintenance</p>
                <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{formatNumber(networkStats.maintenance)}</p>
              </div>
              <Settings className="w-8 h-8 text-yellow-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={isRTL ? 'text-right' : 'text-left'}>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Uptime</p>
                <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{networkStats.avgUptime.toFixed(1)}%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={isRTL ? 'text-right' : 'text-left'}>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Daily Txns</p>
                <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">{formatNumber(networkStats.totalTransactions)}</p>
              </div>
              <Activity className="w-8 h-8 text-purple-500" />
            </div>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={isRTL ? 'text-right' : 'text-left'}>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('networks.totalTokens')}</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatNumber(tokenStats.total)}</p>
              </div>
              <Coins className="w-8 h-8 text-gray-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={isRTL ? 'text-right' : 'text-left'}>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('networks.activeTokens')}</p>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">{formatNumber(tokenStats.active)}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={isRTL ? 'text-right' : 'text-left'}>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Verified</p>
                <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{formatNumber(tokenStats.verified)}</p>
              </div>
              <Shield className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={isRTL ? 'text-right' : 'text-left'}>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Inactive</p>
                <p className="text-2xl font-bold text-gray-600 dark:text-gray-400">{formatNumber(tokenStats.inactive)}</p>
              </div>
              <XCircle className="w-8 h-8 text-gray-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={isRTL ? 'text-right' : 'text-left'}>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Market Cap</p>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">{formatCurrency(tokenStats.totalMarketCap)}</p>
              </div>
              <DollarSign className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={isRTL ? 'text-right' : 'text-left'}>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">24h Volume</p>
                <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">{formatCurrency(tokenStats.totalVolume24h)}</p>
              </div>
              <BarChart3 className="w-8 h-8 text-purple-500" />
            </div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div className={`flex flex-col lg:flex-row gap-4 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
        <div className="relative flex-1">
          <Search className={`absolute top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 ${isRTL ? 'right-3' : 'left-3'}`} />
          <input
            type="text"
            placeholder={t('common.actions.search')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full ${isRTL ? 'pr-10 pl-4 text-right' : 'pl-10 pr-4 text-left'} py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
          />
        </div>

        <div className="flex items-center gap-3">
          <select
            value={selectedFilter}
            onChange={(e) => setSelectedFilter(e.target.value)}
            className={`px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
          >
            <option value="all">{t('networks.filters.all')}</option>
            <option value="active">{t('networks.filters.active')}</option>
            <option value="inactive">{t('networks.filters.inactive')}</option>
            {activeTab === 'networks' ? (
              <>
                <option value="mainnet">{t('networks.filters.mainnet')}</option>
                <option value="testnet">{t('networks.filters.testnet')}</option>
              </>
            ) : (
              <>
                <option value="erc20">{t('networks.filters.erc20')}</option>
                <option value="native">{t('networks.filters.native')}</option>
                <option value="highVolume">{t('networks.filters.highVolume')}</option>
              </>
            )}
          </select>

          <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
            <Filter className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.filter')}
          </button>

          <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
            <RefreshCw className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.refresh')}
          </button>
        </div>
      </div>

      {/* Networks Table */}
      {activeTab === 'networks' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* إشعار البيانات المعلقة */}
          {hasPendingChanges && pendingNetworks.length > 0 && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-800 p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <RefreshCw className="w-4 h-4 text-blue-500 mr-2" />
                  <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    معاينة البيانات الجديدة من البلوك تشين ({pendingNetworks.length} شبكة)
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={saveChanges}
                    disabled={isSaving}
                    className="text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded transition-colors disabled:opacity-50"
                  >
                    {isSaving ? 'جاري الحفظ...' : 'حفظ'}
                  </button>
                  <button
                    onClick={discardChanges}
                    disabled={isSaving}
                    className="text-xs text-blue-600 hover:text-blue-800 underline"
                  >
                    إلغاء
                  </button>
                </div>
              </div>
            </div>
          )}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    <input
                      type="checkbox"
                      className="rounded"
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedItems(filteredNetworks.map(n => n.id));
                          setShowBulkActions(true);
                        } else {
                          setSelectedItems([]);
                          setShowBulkActions(false);
                        }
                      }}
                    />
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('networks.properties.networkName')} & {t('common.labels.type')}
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('networks.properties.chainId')} & RPC
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('networks.monitoring.uptime')} & {t('networks.monitoring.latency')}
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('common.labels.status')} & Health
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('networks.monitoring.transactions')}
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('networks.actions.configure')}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredNetworks.map((network) => (
                  <tr key={network.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <td className="py-4 px-4">
                      <input
                        type="checkbox"
                        className="rounded"
                        checked={selectedItems.includes(network.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedItems([...selectedItems, network.id]);
                            setShowBulkActions(true);
                          } else {
                            setSelectedItems(selectedItems.filter(id => id !== network.id));
                            if (selectedItems.length === 1) setShowBulkActions(false);
                          }
                        }}
                      />
                    </td>
                    <td className="py-4 px-4">
                      <div className={isRTL ? 'text-right' : 'text-left'}>
                        <div className={`font-medium text-gray-900 dark:text-white mb-1 flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                          {network.name}
                          {network.isEnabled && <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'mr-2' : 'ml-2'}`} />}
                        </div>
                        <div className="flex items-center gap-2 mb-1">
                          <TypeBadge type={network.type} />
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {network.nativeCurrency.symbol}
                          </span>
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-300">
                          {formatNumber(network.nodeCount || 0)} nodes
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className={isRTL ? 'text-right' : 'text-left'}>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          Chain ID: {network.chainId}
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-300 truncate max-w-32">
                          {network.rpcUrl}
                        </div>
                        <div className="text-xs text-blue-600 dark:text-blue-400">
                          Block: {formatNumber(network.blockHeight || 0)}
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className={isRTL ? 'text-right' : 'text-left'}>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {(network.uptime || 0).toFixed(1)}% uptime
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-300">
                          {network.latency || 0}ms latency
                        </div>
                        <div className="text-xs text-purple-600 dark:text-purple-400">
                          {network.blockTime || 0}s block time
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="space-y-1">
                        <StatusBadge status={network.status} />
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Gas: {network.gasPrice || 0} gwei
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {formatRelativeTime(network.lastSync || new Date())}
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className={isRTL ? 'text-right' : 'text-left'}>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {formatNumber(network.dailyTransactions)}/day
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-300">
                          Total: {formatNumber(network.totalTransactions)}
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <button
                          onClick={() => {
                            setSelectedNetwork(network);
                            setShowDetails(true);
                          }}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 p-1 rounded transition-colors"
                          title={t('networks.actions.viewDetails')}
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleAction(network.id, 'configure')}
                          className="text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 p-1 rounded transition-colors"
                          title={t('networks.actions.configure')}
                        >
                          <Settings className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleAction(network.id, 'test')}
                          className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 p-1 rounded transition-colors"
                          title={t('networks.actions.testConnection')}
                        >
                          <TestTube className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleAction(network.id, network.isEnabled ? 'disable' : 'enable')}
                          className={`p-1 rounded transition-colors ${
                            network.isEnabled
                              ? 'text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300'
                              : 'text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300'
                          }`}
                          title={network.isEnabled ? t('networks.actions.disableNetwork') : t('networks.actions.enableNetwork')}
                        >
                          {network.isEnabled ? <PowerOff className="w-4 h-4" /> : <Power className="w-4 h-4" />}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Empty State */}
          {filteredNetworks.length === 0 && (
            <div className="text-center py-12">
              <Server className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{t('common.messages.noData')}</h3>
              <p className="text-gray-600 dark:text-gray-400">{t('common.actions.search')}</p>
            </div>
          )}
        </div>
      )}

      {/* Tokens Table */}
      {!isLoading && !error && activeTab === 'tokens' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* إشعار البيانات المعلقة للعملات */}
          {hasPendingChanges && pendingTokens.length > 0 && (
            <div className="bg-green-50 dark:bg-green-900/20 border-b border-green-200 dark:border-green-800 p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <RefreshCw className="w-4 h-4 text-green-500 mr-2" />
                  <span className="text-sm font-medium text-green-800 dark:text-green-200">
                    معاينة البيانات الجديدة من البلوك تشين ({pendingTokens.length} عملة)
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={saveChanges}
                    disabled={isSaving}
                    className="text-xs bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded transition-colors disabled:opacity-50"
                  >
                    {isSaving ? 'جاري الحفظ...' : 'حفظ'}
                  </button>
                  <button
                    onClick={discardChanges}
                    disabled={isSaving}
                    className="text-xs text-green-600 hover:text-green-800 underline"
                  >
                    إلغاء
                  </button>
                </div>
              </div>
            </div>
          )}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    <input
                      type="checkbox"
                      className="rounded"
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedItems(filteredTokens.map(t => t.id));
                          setShowBulkActions(true);
                        } else {
                          setSelectedItems([]);
                          setShowBulkActions(false);
                        }
                      }}
                    />
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('networks.properties.tokenName')} & {t('common.labels.type')}
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('networks.properties.tokenAddress')} & Network
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('networks.properties.price')} & {t('networks.properties.marketCap')}
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('networks.properties.volume24h')} & Holders
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('common.labels.status')} & Verified
                  </th>
                  <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                    {t('networks.actions.configure')}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredTokens.map((token) => {
                  const network = networks.find(n => n.id === token.networkId);
                  return (
                    <tr key={token.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <td className="py-4 px-4">
                        <input
                          type="checkbox"
                          className="rounded"
                          checked={selectedItems.includes(token.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedItems([...selectedItems, token.id]);
                              setShowBulkActions(true);
                            } else {
                              setSelectedItems(selectedItems.filter(id => id !== token.id));
                              if (selectedItems.length === 1) setShowBulkActions(false);
                            }
                          }}
                        />
                      </td>
                      <td className="py-4 px-4">
                        <div className={isRTL ? 'text-right' : 'text-left'}>
                          <div className={`font-medium text-gray-900 dark:text-white mb-1 flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            {token.name} ({token.symbol})
                            {token.isEnabled && <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'mr-2' : 'ml-2'}`} />}
                          </div>
                          <div className="flex items-center gap-2 mb-1">
                            <TypeBadge type={token.type} />
                            {token.contractVerified && (
                              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                                <Shield className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                                Verified
                              </span>
                            )}
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-300">
                            {token.decimals} decimals
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={isRTL ? 'text-right' : 'text-left'}>
                          <div className="text-xs text-gray-600 dark:text-gray-300 font-mono truncate max-w-32">
                            {token.address}
                          </div>
                          <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                            {network?.name || 'Unknown Network'}
                          </div>
                          <button
                            onClick={() => navigator.clipboard.writeText(token.address)}
                            className="text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 mt-1 flex items-center"
                          >
                            <Copy className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                            Copy
                          </button>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={isRTL ? 'text-right' : 'text-left'}>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {formatCurrency(token.price || 0)}
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-300">
                            MCap: {formatCurrency(token.marketCap || 0)}
                          </div>
                          <div className="text-xs text-purple-600 dark:text-purple-400">
                            Supply: {formatNumber(token.totalSupply)}
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={isRTL ? 'text-right' : 'text-left'}>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {formatCurrency(token.volume24h || 0)}
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-300">
                            {formatNumber(token.holders || 0)} holders
                          </div>
                          <div className="text-xs text-green-600 dark:text-green-400">
                            {formatNumber(token.transfers24h || 0)} transfers
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="space-y-1">
                          <StatusBadge status={token.status} />
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {formatRelativeTime(token.lastUpdated || new Date())}
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <button
                            onClick={() => {
                              setSelectedToken(token);
                              setShowDetails(true);
                            }}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 p-1 rounded transition-colors"
                            title={t('networks.actions.viewDetails')}
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleAction(token.id, 'edit')}
                            className="text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 p-1 rounded transition-colors"
                            title={t('networks.actions.editToken')}
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleAction(token.id, 'sync')}
                            className="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 p-1 rounded transition-colors"
                            title={t('networks.actions.syncData')}
                          >
                            <RefreshCw className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleAction(token.id, token.isEnabled ? 'disable' : 'enable')}
                            className={`p-1 rounded transition-colors ${
                              token.isEnabled
                                ? 'text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300'
                                : 'text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300'
                            }`}
                            title={token.isEnabled ? t('networks.actions.disableToken') : t('networks.actions.enableToken')}
                          >
                            {token.isEnabled ? <PowerOff className="w-4 h-4" /> : <Power className="w-4 h-4" />}
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* Empty State */}
          {filteredTokens.length === 0 && (
            <div className="text-center py-12">
              <Coins className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{t('common.messages.noData')}</h3>
              <p className="text-gray-600 dark:text-gray-400">{t('common.actions.search')}</p>
            </div>
          )}
        </div>
      )}

      {/* Bulk Actions Panel */}
      {showBulkActions && selectedItems.length > 0 && (
        <div className={`fixed bottom-6 ${isRTL ? 'left-6' : 'right-6'} bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 z-50`}>
          <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {selectedItems.length} {t('common.actions.selected')}
            </span>
            <button
              onClick={() => handleBulkAction('enable')}
              className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors"
            >
              {t(`networks.actions.enable${activeTab === 'networks' ? 'Network' : 'Token'}`)}
            </button>
            <button
              onClick={() => handleBulkAction('disable')}
              className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors"
            >
              {t(`networks.actions.disable${activeTab === 'networks' ? 'Network' : 'Token'}`)}
            </button>
            <button
              onClick={() => handleBulkAction('sync')}
              className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors"
            >
              {t('networks.actions.syncData')}
            </button>
            <button
              onClick={() => {
                setSelectedItems([]);
                setShowBulkActions(false);
              }}
              className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition-colors"
            >
              {t('common.actions.cancel')}
            </button>
          </div>
        </div>
      )}

      {/* Network Details Modal */}
      {showDetails && selectedNetwork && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className={`flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t('networks.actions.viewDetails')}: {selectedNetwork.name}
              </h3>
              <button
                onClick={() => setShowDetails(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Network Information */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                    {t('networks.configuration.general')}
                  </h4>
                  <div className="space-y-3 text-sm">
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.properties.networkName')}:</span>
                      <span className="text-gray-900 dark:text-white font-semibold">{selectedNetwork.name}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.properties.chainId')}:</span>
                      <span className="text-gray-900 dark:text-white">{selectedNetwork.chainId}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.type')}:</span>
                      <TypeBadge type={selectedNetwork.type} />
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.status')}:</span>
                      <StatusBadge status={selectedNetwork.status} />
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.properties.nativeCurrency')}:</span>
                      <span className="text-gray-900 dark:text-white">{selectedNetwork.nativeCurrency.name} ({selectedNetwork.nativeCurrency.symbol})</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.properties.rpcUrl')}:</span>
                      <span className="text-gray-900 dark:text-white text-xs font-mono truncate max-w-48">{selectedNetwork.rpcUrl}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.properties.explorerUrl')}:</span>
                      <a href={selectedNetwork.explorerUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline text-xs">
                        <ExternalLink className="w-3 h-3 inline mr-1" />
                        Explorer
                      </a>
                    </div>
                  </div>
                </div>

                {/* Performance Metrics */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                    {t('networks.monitoring.title')}
                  </h4>
                  <div className="space-y-3 text-sm">
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.monitoring.uptime')}:</span>
                      <span className="text-gray-900 dark:text-white font-semibold">{(selectedNetwork.uptime || 0).toFixed(2)}%</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.monitoring.latency')}:</span>
                      <span className="text-gray-900 dark:text-white">{selectedNetwork.latency || 0}ms</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.monitoring.blockHeight')}:</span>
                      <span className="text-gray-900 dark:text-white">{formatNumber(selectedNetwork.blockHeight || 0)}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.properties.blockTime')}:</span>
                      <span className="text-gray-900 dark:text-white">{selectedNetwork.blockTime || 0}s</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.properties.gasPrice')}:</span>
                      <span className="text-gray-900 dark:text-white">{selectedNetwork.gasPrice || 0} gwei</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.monitoring.nodeCount')}:</span>
                      <span className="text-gray-900 dark:text-white">{formatNumber(selectedNetwork.nodeCount || 0)}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">Daily Transactions:</span>
                      <span className="text-gray-900 dark:text-white">{formatNumber(selectedNetwork.dailyTransactions)}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">Last Sync:</span>
                      <span className="text-gray-900 dark:text-white">{formatDate(selectedNetwork.lastSync)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className={`flex items-center gap-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <button
                  onClick={() => handleAction(selectedNetwork.id, 'configure')}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  {t('networks.actions.configure')}
                </button>
                <button
                  onClick={() => handleAction(selectedNetwork.id, 'test')}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                >
                  {t('networks.actions.testConnection')}
                </button>
                <button
                  onClick={() => handleAction(selectedNetwork.id, 'sync')}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  {t('networks.actions.syncData')}
                </button>
                <button
                  onClick={() => handleAction(selectedNetwork.id, selectedNetwork.isEnabled ? 'disable' : 'enable')}
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    selectedNetwork.isEnabled
                      ? 'bg-red-600 hover:bg-red-700 text-white'
                      : 'bg-green-600 hover:bg-green-700 text-white'
                  }`}
                >
                  {selectedNetwork.isEnabled ? t('networks.actions.disableNetwork') : t('networks.actions.enableNetwork')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Token Details Modal */}
      {showDetails && selectedToken && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className={`flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t('networks.actions.viewDetails')}: {selectedToken.name}
              </h3>
              <button
                onClick={() => setShowDetails(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Token Information */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                    Token Information
                  </h4>
                  <div className="space-y-3 text-sm">
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.properties.tokenName')}:</span>
                      <span className="text-gray-900 dark:text-white font-semibold">{selectedToken.name}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.properties.tokenSymbol')}:</span>
                      <span className="text-gray-900 dark:text-white">{selectedToken.symbol}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.type')}:</span>
                      <TypeBadge type={selectedToken.type} />
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.status')}:</span>
                      <StatusBadge status={selectedToken.status} />
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.properties.tokenAddress')}:</span>
                      <span className="text-gray-900 dark:text-white text-xs font-mono">{selectedToken.address}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.properties.decimals')}:</span>
                      <span className="text-gray-900 dark:text-white">{selectedToken.decimals}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">Contract Verified:</span>
                      <span className={`text-${selectedToken.contractVerified ? 'green' : 'red'}-600 dark:text-${selectedToken.contractVerified ? 'green' : 'red'}-400`}>
                        {selectedToken.contractVerified ? 'Yes' : 'No'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Market Data */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                    Market Data
                  </h4>
                  <div className="space-y-3 text-sm">
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.properties.price')}:</span>
                      <span className="text-gray-900 dark:text-white font-semibold">{formatCurrency(selectedToken.price || 0)}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.properties.marketCap')}:</span>
                      <span className="text-gray-900 dark:text-white">{formatCurrency(selectedToken.marketCap || 0)}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.properties.volume24h')}:</span>
                      <span className="text-gray-900 dark:text-white">{formatCurrency(selectedToken.volume24h || 0)}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('networks.properties.totalSupply')}:</span>
                      <span className="text-gray-900 dark:text-white">{formatNumber(selectedToken.totalSupply)}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">Holders:</span>
                      <span className="text-gray-900 dark:text-white">{formatNumber(selectedToken.holders || 0)}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">24h Transfers:</span>
                      <span className="text-gray-900 dark:text-white">{formatNumber(selectedToken.transfers24h || 0)}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">Last Updated:</span>
                      <span className="text-gray-900 dark:text-white">{formatDate(selectedToken.lastUpdated)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className={`flex items-center gap-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <button
                  onClick={() => handleAction(selectedToken.id, 'edit')}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  {t('networks.actions.editToken')}
                </button>
                <button
                  onClick={() => handleAction(selectedToken.id, 'sync')}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  {t('networks.actions.syncData')}
                </button>
                <button
                  onClick={() => handleAction(selectedToken.id, selectedToken.isEnabled ? 'disable' : 'enable')}
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    selectedToken.isEnabled
                      ? 'bg-red-600 hover:bg-red-700 text-white'
                      : 'bg-green-600 hover:bg-green-700 text-white'
                  }`}
                >
                  {selectedToken.isEnabled ? t('networks.actions.disableToken') : t('networks.actions.enableToken')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {!isLoading && !error && (
        (activeTab === 'networks' && networks.length === 0) ||
        (activeTab === 'tokens' && tokens.length === 0)
      ) && (
        <div className="text-center py-12">
          <div className="flex flex-col items-center">
            {activeTab === 'networks' ? (
              <Server className="w-12 h-12 text-gray-400 mb-4" />
            ) : (
              <Coins className="w-12 h-12 text-gray-400 mb-4" />
            )}
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {activeTab === 'networks' ? 'لا توجد شبكات' : 'لا توجد عملات'}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {activeTab === 'networks'
                ? 'لم يتم العثور على أي شبكات مدعومة. يرجى إضافة شبكة جديدة.'
                : 'لم يتم العثور على أي عملات مدعومة. يرجى إضافة عملة جديدة.'
              }
            </p>
            <button className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
              <Plus className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
              {activeTab === 'networks' ? 'إضافة شبكة' : 'إضافة عملة'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
