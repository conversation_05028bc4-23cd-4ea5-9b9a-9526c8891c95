<?php
/**
 * API endpoint لإدارة إعدادات الإشعارات
 * Notification Preferences Management API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // جلب إعدادات الإشعارات للمستخدم
        $userId = $_GET['user_id'] ?? null;
        
        if (!$userId) {
            throw new Exception('معرف المستخدم مطلوب');
        }
        
        // التحقق من وجود المستخدم
        $stmt = $connection->prepare("SELECT id FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$userId]);
        
        if (!$stmt->fetch()) {
            throw new Exception('المستخدم غير موجود');
        }
        
        // جلب الإعدادات من قاعدة البيانات
        $stmt = $connection->prepare("
            SELECT setting_value 
            FROM system_settings 
            WHERE setting_key = ? AND updated_by = ?
        ");
        $stmt->execute(['notification_preferences', $userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            $preferences = json_decode($result['setting_value'], true);
        } else {
            // إعدادات افتراضية
            $preferences = [
                'userId' => intval($userId),
                'browserNotifications' => true,
                'emailNotifications' => true,
                'tradeNotifications' => true,
                'securityNotifications' => true,
                'systemNotifications' => true,
                'contractNotifications' => true,
                'soundEnabled' => true,
                'quietHours' => [
                    'enabled' => false,
                    'startTime' => '22:00',
                    'endTime' => '08:00'
                ],
                'priorities' => [
                    'low' => true,
                    'medium' => true,
                    'high' => true,
                    'urgent' => true
                ],
                'languages' => ['ar', 'en']
            ];
        }
        
        echo json_encode([
            'success' => true,
            'data' => $preferences
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'PUT') {
        // حفظ أو تحديث إعدادات الإشعارات
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $userId = $input['userId'] ?? null;
        
        if (!$userId) {
            throw new Exception('معرف المستخدم مطلوب');
        }
        
        // التحقق من وجود المستخدم
        $stmt = $connection->prepare("SELECT id FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$userId]);
        
        if (!$stmt->fetch()) {
            throw new Exception('المستخدم غير موجود');
        }
        
        // التحقق من صحة البيانات
        $validPreferences = [
            'browserNotifications' => 'boolean',
            'emailNotifications' => 'boolean',
            'tradeNotifications' => 'boolean',
            'securityNotifications' => 'boolean',
            'systemNotifications' => 'boolean',
            'contractNotifications' => 'boolean',
            'soundEnabled' => 'boolean',
            'quietHours' => 'array',
            'priorities' => 'array',
            'languages' => 'array'
        ];
        
        $preferences = ['userId' => intval($userId)];
        
        foreach ($validPreferences as $key => $type) {
            if (isset($input[$key])) {
                switch ($type) {
                    case 'boolean':
                        $preferences[$key] = (bool)$input[$key];
                        break;
                    case 'array':
                        $preferences[$key] = is_array($input[$key]) ? $input[$key] : [];
                        break;
                    default:
                        $preferences[$key] = $input[$key];
                }
            }
        }
        
        // التحقق من صحة الساعات الهادئة
        if (isset($preferences['quietHours'])) {
            $quietHours = $preferences['quietHours'];
            if (!isset($quietHours['enabled']) || !isset($quietHours['startTime']) || !isset($quietHours['endTime'])) {
                $preferences['quietHours'] = [
                    'enabled' => false,
                    'startTime' => '22:00',
                    'endTime' => '08:00'
                ];
            }
        }
        
        // التحقق من صحة الأولويات
        if (isset($preferences['priorities'])) {
            $priorities = $preferences['priorities'];
            $validPriorities = ['low', 'medium', 'high', 'urgent'];
            $cleanPriorities = [];
            
            foreach ($validPriorities as $priority) {
                $cleanPriorities[$priority] = isset($priorities[$priority]) ? (bool)$priorities[$priority] : true;
            }
            
            $preferences['priorities'] = $cleanPriorities;
        }
        
        // حفظ الإعدادات في قاعدة البيانات
        $stmt = $connection->prepare("
            INSERT INTO system_settings (setting_key, setting_value, updated_by, created_at, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value),
                updated_by = VALUES(updated_by),
                updated_at = CURRENT_TIMESTAMP
        ");
        
        $stmt->execute([
            'notification_preferences',
            json_encode($preferences),
            $userId
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ إعدادات الإشعارات بنجاح',
            'data' => $preferences
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
}
?>
