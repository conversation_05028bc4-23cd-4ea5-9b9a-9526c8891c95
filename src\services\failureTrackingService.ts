/**
 * خدمة تتبع حالات الفشل المفصلة
 * Detailed Failure Tracking Service
 */

interface FailureLog {
  id: string;
  timestamp: string;
  networkCondition: string;
  requestType: 'create_offer' | 'join_trade' | 'confirm_payment' | 'complete_trade' | 'sync' | 'api_call';
  stage: 'frontend' | 'backend' | 'blockchain' | 'database';
  errorType: 'network_timeout' | 'packet_loss' | 'server_error' | 'blockchain_error' | 'validation_error';
  errorMessage: string;
  requestUrl: string;
  responseTime: number;
  retryAttempt: number;
  userAgent: string;
  userId?: string;
  tradeId?: string;
  additionalData?: any;
}

interface FailureStats {
  totalFailures: number;
  failuresByType: { [key: string]: number };
  failuresByStage: { [key: string]: number };
  failuresByNetwork: { [key: string]: number };
  averageResponseTime: number;
  mostCommonError: string;
  failureRate: number;
}

class FailureTrackingService {
  private failures: FailureLog[] = [];
  private maxLogSize = 1000; // الحد الأقصى للسجلات

  // تسجيل حالة فشل جديدة
  public logFailure(failure: Omit<FailureLog, 'id' | 'timestamp'>): void {
    const failureLog: FailureLog = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      ...failure
    };

    this.failures.push(failureLog);

    // الحفاظ على حجم السجل
    if (this.failures.length > this.maxLogSize) {
      this.failures = this.failures.slice(-this.maxLogSize);
    }

    // حفظ في localStorage
    this.saveToStorage();

    // إرسال للخادم إذا كان متاحاً
    this.sendToServer(failureLog);

    console.error('🚨 Failure logged:', failureLog);
  }

  // تحليل حالات الفشل
  public analyzeFailures(timeRange?: { start: Date; end: Date }): FailureStats {
    let filteredFailures = this.failures;

    if (timeRange) {
      filteredFailures = this.failures.filter(f => {
        const failureTime = new Date(f.timestamp);
        return failureTime >= timeRange.start && failureTime <= timeRange.end;
      });
    }

    const stats: FailureStats = {
      totalFailures: filteredFailures.length,
      failuresByType: {},
      failuresByStage: {},
      failuresByNetwork: {},
      averageResponseTime: 0,
      mostCommonError: '',
      failureRate: 0
    };

    if (filteredFailures.length === 0) return stats;

    // تجميع الإحصائيات
    let totalResponseTime = 0;
    const errorCounts: { [key: string]: number } = {};

    filteredFailures.forEach(failure => {
      // حسب النوع
      stats.failuresByType[failure.errorType] = (stats.failuresByType[failure.errorType] || 0) + 1;
      
      // حسب المرحلة
      stats.failuresByStage[failure.stage] = (stats.failuresByStage[failure.stage] || 0) + 1;
      
      // حسب حالة الشبكة
      stats.failuresByNetwork[failure.networkCondition] = (stats.failuresByNetwork[failure.networkCondition] || 0) + 1;
      
      // وقت الاستجابة
      totalResponseTime += failure.responseTime;
      
      // الأخطاء الشائعة
      errorCounts[failure.errorMessage] = (errorCounts[failure.errorMessage] || 0) + 1;
    });

    stats.averageResponseTime = totalResponseTime / filteredFailures.length;
    
    // أكثر خطأ شائع
    stats.mostCommonError = Object.entries(errorCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || '';

    return stats;
  }

  // الحصول على حالات الفشل الأخيرة
  public getRecentFailures(limit: number = 20): FailureLog[] {
    return this.failures
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  // البحث في حالات الفشل
  public searchFailures(criteria: {
    errorType?: string;
    stage?: string;
    networkCondition?: string;
    timeRange?: { start: Date; end: Date };
  }): FailureLog[] {
    return this.failures.filter(failure => {
      if (criteria.errorType && failure.errorType !== criteria.errorType) return false;
      if (criteria.stage && failure.stage !== criteria.stage) return false;
      if (criteria.networkCondition && failure.networkCondition !== criteria.networkCondition) return false;
      
      if (criteria.timeRange) {
        const failureTime = new Date(failure.timestamp);
        if (failureTime < criteria.timeRange.start || failureTime > criteria.timeRange.end) return false;
      }
      
      return true;
    });
  }

  // تصدير السجلات
  public exportFailures(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = ['ID', 'الوقت', 'حالة الشبكة', 'نوع الطلب', 'المرحلة', 'نوع الخطأ', 'رسالة الخطأ', 'وقت الاستجابة', 'محاولة إعادة'];
      const rows = this.failures.map(f => [
        f.id,
        f.timestamp,
        f.networkCondition,
        f.requestType,
        f.stage,
        f.errorType,
        f.errorMessage,
        f.responseTime.toString(),
        f.retryAttempt.toString()
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }
    
    return JSON.stringify(this.failures, null, 2);
  }

  // مسح السجلات
  public clearFailures(): void {
    this.failures = [];
    localStorage.removeItem('failure_logs');
  }

  // إنشاء تقرير مفصل
  public generateDetailedReport(): string {
    const stats = this.analyzeFailures();
    const recentFailures = this.getRecentFailures(10);
    
    let report = `# تقرير حالات الفشل المفصل\n\n`;
    report += `## الإحصائيات العامة\n`;
    report += `- إجمالي حالات الفشل: ${stats.totalFailures}\n`;
    report += `- متوسط وقت الاستجابة: ${stats.averageResponseTime.toFixed(0)}ms\n`;
    report += `- أكثر خطأ شائع: ${stats.mostCommonError}\n\n`;
    
    report += `## التوزيع حسب النوع\n`;
    Object.entries(stats.failuresByType).forEach(([type, count]) => {
      report += `- ${type}: ${count}\n`;
    });
    
    report += `\n## التوزيع حسب المرحلة\n`;
    Object.entries(stats.failuresByStage).forEach(([stage, count]) => {
      report += `- ${stage}: ${count}\n`;
    });
    
    report += `\n## التوزيع حسب حالة الشبكة\n`;
    Object.entries(stats.failuresByNetwork).forEach(([network, count]) => {
      report += `- ${network}: ${count}\n`;
    });
    
    report += `\n## آخر 10 حالات فشل\n`;
    recentFailures.forEach((failure, index) => {
      report += `${index + 1}. [${failure.timestamp}] ${failure.errorType} في ${failure.stage}: ${failure.errorMessage}\n`;
    });
    
    return report;
  }

  // دوال مساعدة
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private saveToStorage(): void {
    try {
      localStorage.setItem('failure_logs', JSON.stringify(this.failures));
    } catch (error) {
      console.error('Failed to save failure logs to storage:', error);
    }
  }

  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem('failure_logs');
      if (stored) {
        this.failures = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load failure logs from storage:', error);
    }
  }

  private async sendToServer(failure: FailureLog): Promise<void> {
    try {
      await fetch('/api/admin/log-failure.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(failure)
      });
    } catch (error) {
      // تجاهل أخطاء الإرسال للخادم لتجنب التكرار اللانهائي
    }
  }

  constructor() {
    this.loadFromStorage();
  }
}

// إنشاء مثيل واحد للخدمة
export const failureTrackingService = new FailureTrackingService();
export default failureTrackingService;
