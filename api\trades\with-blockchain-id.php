<?php
/**
 * API لجلب الصفقات المرتبطة بالعقد الذكي
 * Get trades linked to smart contract
 */

// تضمين إعدادات CORS
require_once __DIR__ . '/../cors.php';

// التحقق من طريقة الطلب
validateRequestMethod(['GET']);

try {
    // تضمين إعدادات قاعدة البيانات
    require_once __DIR__ . '/../config/database.php';
    
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // جلب الصفقات التي لها blockchain_trade_id
    $query = "
        SELECT 
            t.id,
            t.offer_id,
            t.seller_id,
            t.buyer_id,
            t.amount,
            t.price,
            t.currency,
            t.stablecoin,
            t.status,
            t.blockchain_trade_id,
            t.contract_status,
            t.seller_address,
            t.buyer_address,
            t.payment_method,
            t.payment_details,
            t.created_at,
            t.updated_at,
            t.completed_at,
            t.last_sync_at,
            seller.username as seller_username,
            seller.full_name as seller_name,
            seller.wallet_address as seller_wallet,
            buyer.username as buyer_username,
            buyer.full_name as buyer_name,
            buyer.wallet_address as buyer_wallet,
            o.offer_type,
            o.terms
        FROM trades t
        LEFT JOIN users seller ON t.seller_id = seller.id
        LEFT JOIN users buyer ON t.buyer_id = buyer.id
        LEFT JOIN offers o ON t.offer_id = o.id
        WHERE t.blockchain_trade_id IS NOT NULL
        ORDER BY t.created_at DESC
    ";
    
    $stmt = $connection->prepare($query);
    $stmt->execute();
    $trades = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تنسيق البيانات
    $formattedTrades = [];
    foreach ($trades as $trade) {
        $formattedTrades[] = [
            'id' => $trade['id'],
            'offer_id' => $trade['offer_id'],
            'seller_id' => $trade['seller_id'],
            'buyer_id' => $trade['buyer_id'],
            'amount' => (float) $trade['amount'],
            'price' => (float) $trade['price'],
            'currency' => $trade['currency'],
            'stablecoin' => $trade['stablecoin'],
            'status' => $trade['status'],
            'blockchain_trade_id' => (int) $trade['blockchain_trade_id'],
            'contract_status' => $trade['contract_status'],
            'seller_address' => $trade['seller_address'],
            'buyer_address' => $trade['buyer_address'],
            'payment_method' => $trade['payment_method'],
            'payment_details' => $trade['payment_details'] ? json_decode($trade['payment_details'], true) : null,
            'created_at' => $trade['created_at'],
            'updated_at' => $trade['updated_at'],
            'completed_at' => $trade['completed_at'],
            'last_sync_at' => $trade['last_sync_at'],
            'seller' => [
                'username' => $trade['seller_username'],
                'full_name' => $trade['seller_name'],
                'wallet_address' => $trade['seller_wallet']
            ],
            'buyer' => [
                'username' => $trade['buyer_username'],
                'full_name' => $trade['buyer_name'],
                'wallet_address' => $trade['buyer_wallet']
            ],
            'offer' => [
                'type' => $trade['offer_type'],
                'terms' => $trade['terms']
            ]
        ];
    }
    
    sendSuccessResponse([
        'data' => $formattedTrades,
        'count' => count($formattedTrades)
    ], 'تم جلب الصفقات المرتبطة بالعقد الذكي بنجاح');
    
} catch (Exception $e) {
    logError("Error fetching trades with blockchain ID: " . $e->getMessage());
    sendErrorResponse('خطأ في جلب الصفقات المرتبطة بالعقد الذكي: ' . $e->getMessage(), 500);
}
?>
