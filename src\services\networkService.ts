// خدمة إدارة الشبكات والتبديل بين التجريبية والحقيقية
import { ethers } from 'ethers';

export interface NetworkConfig {
  chainId: string;
  chainIdHex: string;
  chainIdNumber: number;
  name: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  rpcUrls: string[];
  blockExplorerUrls: string[];
  contracts: {
    escrow: string;
    usdt: string;
  };
  isTestnet: boolean;
}

// إعدادات الشبكات
export const NETWORKS: Record<string, NetworkConfig> = {
  BSC_TESTNET: {
    chainId: '0x61',
    chainIdHex: '0x61',
    chainIdNumber: 97,
    name: 'BSC Testnet',
    nativeCurrency: {
      name: 'Test BNB',
      symbol: 'tBNB',
      decimals: 18
    },
    rpcUrls: [
      'https://data-seed-prebsc-1-s1.binance.org:8545/',
      'https://data-seed-prebsc-2-s1.binance.org:8545/',
      'https://data-seed-prebsc-1-s2.binance.org:8545/'
    ],
    blockExplorerUrls: ['https://testnet.bscscan.com/'],
    contracts: {
      // العقود المحسنة الجديدة
      coreEscrow: process.env.NEXT_PUBLIC_CORE_ESCROW_ADDRESS || '0xAb25F1fFDB7fC4EA75abDcc662CeD61E4b20F7f2',
      reputationManager: process.env.NEXT_PUBLIC_REPUTATION_MANAGER_ADDRESS || '0x56A6914523413b0e7344f57466A6239fCC97b913',
      oracleManager: process.env.NEXT_PUBLIC_ORACLE_MANAGER_ADDRESS || '0xB70715392F62628Ccd1258AAF691384bE8C023b6',
      adminManager: process.env.NEXT_PUBLIC_ADMIN_MANAGER_ADDRESS || '0x5A9FD8082ADA38678721D59AAB4d4F76883c5575',
      escrowIntegrator: process.env.NEXT_PUBLIC_ESCROW_INTEGRATOR_ADDRESS || '0xc0Ec65ffA95aca485e4C2e9b35B20C85FA36F0e0',

      // العملات المستقرة المدعومة
      usdt: process.env.NEXT_PUBLIC_USDT_CONTRACT_ADDRESS || '0x7ef95a0FEE0Dd31b22626fA2e10Ee6A223F8a684',
      usdc: process.env.NEXT_PUBLIC_USDC_CONTRACT_ADDRESS || '0x8AC76a51cc950d9822D68b83fE1Ad97B32Cd580d',
      busd: process.env.NEXT_PUBLIC_BUSD_CONTRACT_ADDRESS || '0xeD24FC36d5Ee211Ea25A80239Fb8C4Cfd80f12Ee',
      dai: process.env.NEXT_PUBLIC_DAI_CONTRACT_ADDRESS || '0x1AF3F329e8BE154074D8769D1FFa4eE058B1DBc3',
      fdusd: process.env.NEXT_PUBLIC_FDUSD_CONTRACT_ADDRESS || '0xc5f0f7b66764F6ec8C8Dff7BA683102295E16409',
      tusd: process.env.NEXT_PUBLIC_TUSD_CONTRACT_ADDRESS || '0x14016E85a25aeb13065688cAFB43044C2ef86784',

      // العقد القديم (للتوافق مع النسخة السابقة)
      escrow: process.env.NEXT_PUBLIC_TESTNET_ESCROW_CONTRACT || '0xEcB2851e50e7CB5ea05c29b7e5b2221913bFce43'
    },
    isTestnet: true
  },
  BSC_MAINNET: {
    chainId: '0x38',
    chainIdHex: '0x38',
    chainIdNumber: 56,
    name: 'BSC Mainnet',
    nativeCurrency: {
      name: 'BNB',
      symbol: 'BNB',
      decimals: 18
    },
    rpcUrls: [
      'https://bsc-dataseed1.binance.org/',
      'https://bsc-dataseed2.binance.org/',
      'https://bsc-dataseed3.binance.org/'
    ],
    blockExplorerUrls: ['https://bscscan.com/'],
    contracts: {
      escrow: process.env.NEXT_PUBLIC_MAINNET_ESCROW_CONTRACT || '',
      usdt: process.env.NEXT_PUBLIC_MAINNET_USDT_CONTRACT || '******************************************'
    },
    isTestnet: false
  }
};

export class NetworkService {
  private currentNetwork: NetworkConfig | null = null;
  private provider: ethers.BrowserProvider | null = null;

  constructor() {
    this.initializeNetwork();
  }

  private async initializeNetwork() {
    if (typeof window !== 'undefined' && window.ethereum) {
      try {
        this.provider = new ethers.BrowserProvider(window.ethereum);
        await this.detectCurrentNetwork();
      } catch (error) {
        console.error('Network initialization error:', error);
      }
    }
  }

  // كشف الشبكة الحالية
  async detectCurrentNetwork(): Promise<NetworkConfig | null> {
    if (!this.provider) return null;

    try {
      const network = await this.provider.getNetwork();
      const chainId = `0x${network.chainId.toString(16)}`;
      
      // البحث عن الشبكة في القائمة
      for (const [key, config] of Object.entries(NETWORKS)) {
        if (config.chainIdHex === chainId) {
          this.currentNetwork = config;
          return config;
        }
      }

      console.warn('Unsupported network:', chainId);
      return null;
    } catch (error) {
      console.error('Network detection error:', error);
      return null;
    }
  }

  // التبديل إلى شبكة محددة
  async switchToNetwork(networkKey: string): Promise<boolean> {
    if (!window.ethereum) {
      throw new Error('MetaMask غير مثبت');
    }

    const network = NETWORKS[networkKey];
    if (!network) {
      throw new Error('شبكة غير مدعومة');
    }

    try {
      // محاولة التبديل إلى الشبكة
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: network.chainIdHex }],
      });

      this.currentNetwork = network;
      return true;
    } catch (switchError: any) {
      // إذا لم تكن الشبكة مضافة، أضفها
      if (switchError.code === 4902) {
        try {
          await window.ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [
              {
                chainId: network.chainIdHex,
                chainName: network.name,
                nativeCurrency: network.nativeCurrency,
                rpcUrls: network.rpcUrls,
                blockExplorerUrls: network.blockExplorerUrls,
              },
            ],
          });

          this.currentNetwork = network;
          return true;
        } catch (addError) {
          console.error('Network addition error:', addError);
          throw addError;
        }
      } else {
        console.error('Network switch error:', switchError);
        throw switchError;
      }
    }
  }

  // التبديل إلى الشبكة التجريبية
  async switchToTestnet(): Promise<boolean> {
    return this.switchToNetwork('BSC_TESTNET');
  }

  // التبديل إلى الشبكة الحقيقية
  async switchToMainnet(): Promise<boolean> {
    return this.switchToNetwork('BSC_MAINNET');
  }

  // الحصول على الشبكة الحالية
  getCurrentNetwork(): NetworkConfig | null {
    return this.currentNetwork;
  }

  // التحقق من كون الشبكة الحالية مدعومة
  isCurrentNetworkSupported(): boolean {
    return this.currentNetwork !== null;
  }

  // التحقق من كون الشبكة الحالية تجريبية
  isCurrentNetworkTestnet(): boolean {
    return this.currentNetwork?.isTestnet || false;
  }

  // الحصول على عناوين العقود للشبكة الحالية
  getCurrentContracts(): { escrow: string; usdt: string } | null {
    return this.currentNetwork?.contracts || null;
  }

  // الحصول على معلومات الشبكة بواسطة chainId
  getNetworkByChainId(chainId: string): NetworkConfig | null {
    for (const [key, config] of Object.entries(NETWORKS)) {
      if (config.chainIdHex === chainId) {
        return config;
      }
    }
    return null;
  }

  // الحصول على قائمة جميع الشبكات المدعومة
  getSupportedNetworks(): NetworkConfig[] {
    return Object.values(NETWORKS);
  }

  // الحصول على الشبكات التجريبية فقط
  getTestnetNetworks(): NetworkConfig[] {
    return Object.values(NETWORKS).filter(network => network.isTestnet);
  }

  // الحصول على الشبكات الحقيقية فقط
  getMainnetNetworks(): NetworkConfig[] {
    return Object.values(NETWORKS).filter(network => !network.isTestnet);
  }

  // مراقبة تغيير الشبكة
  onNetworkChange(callback: (network: NetworkConfig | null) => void) {
    if (window.ethereum) {
      window.ethereum.on('chainChanged', async (chainId: string) => {
        await this.detectCurrentNetwork();
        callback(this.currentNetwork);
      });
    }
  }

  // إزالة مراقب تغيير الشبكة
  removeNetworkChangeListener() {
    if (window.ethereum) {
      window.ethereum.removeAllListeners('chainChanged');
    }
  }
}

// إنشاء مثيل واحد من الخدمة
export const networkService = new NetworkService();
