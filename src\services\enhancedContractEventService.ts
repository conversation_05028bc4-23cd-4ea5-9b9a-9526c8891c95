// خدمة أحداث العقد الذكي المحسن
import { ethers } from 'ethers';
import { 
  getEnhancedContractAddresses, 
  getContractABI,
  ENHANCED_CONTRACT_ABIS 
} from '@/constants';

// أنواع الأحداث
export interface ContractEvent {
  id?: number;
  networkId: number;
  contractType: string;
  contractAddress: string;
  eventType: string;
  blockchainTradeId?: number;
  transactionHash: string;
  blockNumber: number;
  blockHash: string;
  logIndex: number;
  sellerAddress?: string;
  buyerAddress?: string;
  tokenAddress?: string;
  amount?: string;
  feeAmount?: string;
  pricePerToken?: string;
  currency?: string;
  eventData?: any;
  rawLogData?: string;
  gasUsed?: number;
  gasPrice?: number;
  effectiveGasPrice?: number;
  processed: boolean;
  processedAt?: Date;
  processingAttempts: number;
  errorMessage?: string;
  retryAfter?: Date;
  eventTimestamp: Date;
  createdAt: Date;
  updatedAt?: Date;
}

export interface EventFilter {
  networkId?: number;
  contractType?: string;
  eventType?: string;
  processed?: boolean;
  fromBlock?: number;
  toBlock?: number;
}

class EnhancedContractEventService {
  private provider: ethers.BrowserProvider | null = null;
  private contracts: Map<string, ethers.Contract> = new Map();
  private eventListeners: Map<string, any> = new Map();
  private isListening = false;
  private apiUrl: string;

  constructor() {
    this.apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '/api';
    this.initializeProvider();
  }

  /**
   * تهيئة المزود
   */
  private async initializeProvider() {
    if (typeof window !== 'undefined' && window.ethereum) {
      try {
        this.provider = new ethers.BrowserProvider(window.ethereum);
        await this.setupContracts();
        console.log('✅ تم تهيئة خدمة أحداث العقد المحسن بنجاح');
      } catch (error) {
        console.error('❌ خطأ في تهيئة مزود الأحداث:', error);
      }
    }
  }

  /**
   * إعداد العقود للاستماع للأحداث
   */
  private async setupContracts() {
    if (!this.provider) return;

    try {
      const network = await this.provider.getNetwork();
      const isTestnet = network.chainId === 97n;
      const contracts = getEnhancedContractAddresses(isTestnet);

      // إعداد عقد Core Escrow
      if (contracts.CORE_ESCROW && ethers.isAddress(contracts.CORE_ESCROW)) {
        const coreEscrowContract = new ethers.Contract(
          contracts.CORE_ESCROW,
          getContractABI('CORE_ESCROW'),
          this.provider
        );
        this.contracts.set('core_escrow', coreEscrowContract);
      }

      // إعداد عقد Reputation Manager
      if (contracts.REPUTATION_MANAGER && ethers.isAddress(contracts.REPUTATION_MANAGER)) {
        const reputationContract = new ethers.Contract(
          contracts.REPUTATION_MANAGER,
          getContractABI('REPUTATION_MANAGER'),
          this.provider
        );
        this.contracts.set('reputation_manager', reputationContract);
      }

      // إعداد عقد Oracle Manager
      if (contracts.ORACLE_MANAGER && ethers.isAddress(contracts.ORACLE_MANAGER)) {
        const oracleContract = new ethers.Contract(
          contracts.ORACLE_MANAGER,
          getContractABI('ORACLE_MANAGER'),
          this.provider
        );
        this.contracts.set('oracle_manager', oracleContract);
      }

      // إعداد عقد Admin Manager
      if (contracts.ADMIN_MANAGER && ethers.isAddress(contracts.ADMIN_MANAGER)) {
        const adminContract = new ethers.Contract(
          contracts.ADMIN_MANAGER,
          getContractABI('ADMIN_MANAGER'),
          this.provider
        );
        this.contracts.set('admin_manager', adminContract);
      }

      // إعداد عقد Escrow Integrator
      if (contracts.ESCROW_INTEGRATOR && ethers.isAddress(contracts.ESCROW_INTEGRATOR)) {
        const integratorContract = new ethers.Contract(
          contracts.ESCROW_INTEGRATOR,
          getContractABI('ESCROW_INTEGRATOR'),
          this.provider
        );
        this.contracts.set('escrow_integrator', integratorContract);
      }

      console.log(`✅ تم إعداد ${this.contracts.size} عقد للاستماع للأحداث`);
    } catch (error) {
      console.error('❌ خطأ في إعداد العقود للأحداث:', error);
    }
  }

  /**
   * بدء الاستماع للأحداث
   */
  async startListening(): Promise<void> {
    if (this.isListening || this.contracts.size === 0) {
      return;
    }

    try {
      // الاستماع لأحداث Core Escrow
      const coreEscrowContract = this.contracts.get('core_escrow');
      if (coreEscrowContract) {
        this.setupCoreEscrowListeners(coreEscrowContract);
      }

      // الاستماع لأحداث Reputation Manager
      const reputationContract = this.contracts.get('reputation_manager');
      if (reputationContract) {
        this.setupReputationListeners(reputationContract);
      }

      // الاستماع لأحداث Oracle Manager
      const oracleContract = this.contracts.get('oracle_manager');
      if (oracleContract) {
        this.setupOracleListeners(oracleContract);
      }

      // الاستماع لأحداث Admin Manager
      const adminContract = this.contracts.get('admin_manager');
      if (adminContract) {
        this.setupAdminListeners(adminContract);
      }

      this.isListening = true;
      console.log('✅ تم بدء الاستماع لأحداث العقود المحسنة');
    } catch (error) {
      console.error('❌ خطأ في بدء الاستماع للأحداث:', error);
    }
  }

  /**
   * إعداد مستمعي أحداث Core Escrow
   */
  private setupCoreEscrowListeners(contract: ethers.Contract) {
    // حدث إنشاء صفقة
    contract.on('TradeCreated', async (tradeId, seller, token, amount, event) => {
      await this.handleEvent({
        contractType: 'core_escrow',
        contractAddress: await contract.getAddress(),
        eventType: 'TradeCreated',
        blockchainTradeId: Number(tradeId),
        sellerAddress: seller,
        tokenAddress: token,
        amount: ethers.formatUnits(amount, 18),
        event
      });
    });

    // حدث الانضمام للصفقة
    contract.on('TradeJoined', async (tradeId, buyer, event) => {
      await this.handleEvent({
        contractType: 'core_escrow',
        contractAddress: await contract.getAddress(),
        eventType: 'TradeJoined',
        blockchainTradeId: Number(tradeId),
        buyerAddress: buyer,
        event
      });
    });

    // حدث إرسال الدفع
    contract.on('PaymentSent', async (tradeId, buyer, event) => {
      await this.handleEvent({
        contractType: 'core_escrow',
        contractAddress: await contract.getAddress(),
        eventType: 'PaymentSent',
        blockchainTradeId: Number(tradeId),
        buyerAddress: buyer,
        event
      });
    });

    // حدث تأكيد الدفع
    contract.on('PaymentConfirmed', async (tradeId, seller, event) => {
      await this.handleEvent({
        contractType: 'core_escrow',
        contractAddress: await contract.getAddress(),
        eventType: 'PaymentConfirmed',
        blockchainTradeId: Number(tradeId),
        sellerAddress: seller,
        event
      });
    });

    // حدث حل النزاع
    contract.on('DisputeResolved', async (tradeId, winner, resolution, event) => {
      await this.handleEvent({
        contractType: 'core_escrow',
        contractAddress: await contract.getAddress(),
        eventType: 'DisputeResolved',
        blockchainTradeId: Number(tradeId),
        eventData: { winner, resolution: Number(resolution) },
        event
      });
    });

    console.log('✅ تم إعداد مستمعي أحداث Core Escrow');
  }

  /**
   * إعداد مستمعي أحداث Reputation Manager
   */
  private setupReputationListeners(contract: ethers.Contract) {
    // حدث تحديث السمعة
    contract.on('ReputationUpdated', async (user, newScore, event) => {
      await this.handleEvent({
        contractType: 'reputation_manager',
        contractAddress: await contract.getAddress(),
        eventType: 'ReputationUpdated',
        sellerAddress: user, // أو buyerAddress حسب السياق
        eventData: { newScore: Number(newScore) },
        event
      });
    });

    console.log('✅ تم إعداد مستمعي أحداث Reputation Manager');
  }

  /**
   * إعداد مستمعي أحداث Oracle Manager
   */
  private setupOracleListeners(contract: ethers.Contract) {
    // حدث تحديث السعر
    contract.on('PriceUpdated', async (token, currency, newPrice, event) => {
      await this.handleEvent({
        contractType: 'oracle_manager',
        contractAddress: await contract.getAddress(),
        eventType: 'PriceUpdated',
        tokenAddress: token,
        currency,
        pricePerToken: ethers.formatUnits(newPrice, 18),
        event
      });
    });

    console.log('✅ تم إعداد مستمعي أحداث Oracle Manager');
  }

  /**
   * إعداد مستمعي أحداث Admin Manager
   */
  private setupAdminListeners(contract: ethers.Contract) {
    // حدث إضافة توكن
    contract.on('TokenAdded', async (token, supported, event) => {
      await this.handleEvent({
        contractType: 'admin_manager',
        contractAddress: await contract.getAddress(),
        eventType: 'TokenAdded',
        tokenAddress: token,
        eventData: { supported },
        event
      });
    });

    // حدث إزالة توكن
    contract.on('TokenRemoved', async (token, event) => {
      await this.handleEvent({
        contractType: 'admin_manager',
        contractAddress: await contract.getAddress(),
        eventType: 'TokenRemoved',
        tokenAddress: token,
        event
      });
    });

    console.log('✅ تم إعداد مستمعي أحداث Admin Manager');
  }

  /**
   * معالجة الحدث
   */
  private async handleEvent(eventData: any) {
    try {
      const event = eventData.event;
      const block = await event.getBlock();
      const transaction = await event.getTransaction();

      const contractEvent: ContractEvent = {
        networkId: this.getCurrentNetworkId(),
        contractType: eventData.contractType,
        contractAddress: eventData.contractAddress,
        eventType: eventData.eventType,
        blockchainTradeId: eventData.blockchainTradeId,
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
        blockHash: block.hash,
        logIndex: event.index,
        sellerAddress: eventData.sellerAddress,
        buyerAddress: eventData.buyerAddress,
        tokenAddress: eventData.tokenAddress,
        amount: eventData.amount,
        feeAmount: eventData.feeAmount,
        pricePerToken: eventData.pricePerToken,
        currency: eventData.currency,
        eventData: eventData.eventData,
        rawLogData: JSON.stringify(event.args),
        gasUsed: transaction.gasLimit ? Number(transaction.gasLimit) : undefined,
        gasPrice: transaction.gasPrice ? Number(transaction.gasPrice) : undefined,
        effectiveGasPrice: transaction.gasPrice ? Number(transaction.gasPrice) : undefined,
        processed: false,
        processingAttempts: 0,
        eventTimestamp: new Date(block.timestamp * 1000),
        createdAt: new Date()
      };

      // حفظ الحدث في قاعدة البيانات
      await this.saveEvent(contractEvent);

      // معالجة الحدث
      await this.processEvent(contractEvent);

      console.log(`✅ تم معالجة حدث ${eventData.eventType} للعقد ${eventData.contractType}`);
    } catch (error) {
      console.error('❌ خطأ في معالجة الحدث:', error);
    }
  }

  /**
   * حفظ الحدث في قاعدة البيانات
   */
  private async saveEvent(event: ContractEvent): Promise<void> {
    try {
      const response = await fetch(`${this.apiUrl}/contract-events.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(event)
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'فشل في حفظ الحدث');
      }
    } catch (error) {
      console.error('❌ خطأ في حفظ الحدث:', error);
      throw error;
    }
  }

  /**
   * معالجة الحدث
   */
  private async processEvent(event: ContractEvent): Promise<void> {
    try {
      // معالجة الحدث حسب نوعه
      switch (event.eventType) {
        case 'TradeCreated':
          await this.processTradeCreatedEvent(event);
          break;
        case 'TradeJoined':
          await this.processTradeJoinedEvent(event);
          break;
        case 'PaymentSent':
          await this.processPaymentSentEvent(event);
          break;
        case 'PaymentConfirmed':
          await this.processPaymentConfirmedEvent(event);
          break;
        case 'DisputeResolved':
          await this.processDisputeResolvedEvent(event);
          break;
        case 'ReputationUpdated':
          await this.processReputationUpdatedEvent(event);
          break;
        case 'PriceUpdated':
          await this.processPriceUpdatedEvent(event);
          break;
        default:
          console.log(`⚠️ نوع حدث غير معروف: ${event.eventType}`);
      }

      // تحديث حالة المعالجة
      await this.markEventAsProcessed(event);
    } catch (error) {
      console.error('❌ خطأ في معالجة الحدث:', error);
      await this.markEventAsFailed(event, error.message);
    }
  }

  /**
   * معالجة حدث إنشاء صفقة
   */
  private async processTradeCreatedEvent(event: ContractEvent): Promise<void> {
    // تحديث العرض في قاعدة البيانات
    const response = await fetch(`${this.apiUrl}/enhanced-contracts/sync.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'sync_offer',
        blockchain_trade_id: event.blockchainTradeId,
        transaction_hash: event.transactionHash,
        contract_status: 'created'
      })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'فشل في مزامنة العرض');
    }
  }

  /**
   * معالجة حدث الانضمام للصفقة
   */
  private async processTradeJoinedEvent(event: ContractEvent): Promise<void> {
    // تحديث الصفقة في قاعدة البيانات
    const response = await fetch(`${this.apiUrl}/enhanced-contracts/sync.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'sync_trade',
        blockchain_trade_id: event.blockchainTradeId,
        transaction_hash: event.transactionHash,
        contract_status: 'Joined',
        event_type: 'join'
      })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'فشل في مزامنة الصفقة');
    }
  }

  /**
   * معالجة حدث إرسال الدفع
   */
  private async processPaymentSentEvent(event: ContractEvent): Promise<void> {
    // تحديث حالة الصفقة
    const response = await fetch(`${this.apiUrl}/enhanced-contracts/sync.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'sync_trade',
        blockchain_trade_id: event.blockchainTradeId,
        transaction_hash: event.transactionHash,
        contract_status: 'PaymentSent',
        event_type: 'payment_sent'
      })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'فشل في تحديث حالة الدفع');
    }
  }

  /**
   * معالجة حدث تأكيد الدفع
   */
  private async processPaymentConfirmedEvent(event: ContractEvent): Promise<void> {
    // تحديث حالة الصفقة إلى مكتملة
    const response = await fetch(`${this.apiUrl}/enhanced-contracts/sync.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'sync_trade',
        blockchain_trade_id: event.blockchainTradeId,
        transaction_hash: event.transactionHash,
        contract_status: 'Completed',
        event_type: 'complete'
      })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'فشل في إكمال الصفقة');
    }
  }

  /**
   * معالجة حدث حل النزاع
   */
  private async processDisputeResolvedEvent(event: ContractEvent): Promise<void> {
    // تحديث حالة النزاع
    const response = await fetch(`${this.apiUrl}/enhanced-contracts/sync.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'sync_trade',
        blockchain_trade_id: event.blockchainTradeId,
        transaction_hash: event.transactionHash,
        contract_status: 'Resolved',
        event_type: 'resolve'
      })
    });

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'فشل في حل النزاع');
    }
  }

  /**
   * معالجة حدث تحديث السمعة
   */
  private async processReputationUpdatedEvent(event: ContractEvent): Promise<void> {
    // تحديث السمعة في قاعدة البيانات
    console.log('تحديث السمعة:', event);
  }

  /**
   * معالجة حدث تحديث السعر
   */
  private async processPriceUpdatedEvent(event: ContractEvent): Promise<void> {
    // تحديث السعر في قاعدة البيانات
    console.log('تحديث السعر:', event);
  }

  /**
   * تحديد الحدث كمعالج
   */
  private async markEventAsProcessed(event: ContractEvent): Promise<void> {
    const response = await fetch(`${this.apiUrl}/enhanced-contracts/sync.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'process_event',
        event_id: event.id
      })
    });

    const result = await response.json();
    if (!result.success) {
      console.error('فشل في تحديد الحدث كمعالج:', result.error);
    }
  }

  /**
   * تحديد الحدث كفاشل
   */
  private async markEventAsFailed(event: ContractEvent, errorMessage: string): Promise<void> {
    console.error(`فشل في معالجة الحدث ${event.eventType}:`, errorMessage);
  }

  /**
   * الحصول على معرف الشبكة الحالية
   */
  private getCurrentNetworkId(): number {
    // هذا مثال - يجب الحصول على معرف الشبكة الفعلي
    return 1; // BSC Testnet
  }

  /**
   * إيقاف الاستماع للأحداث
   */
  stopListening(): void {
    for (const [contractType, contract] of this.contracts.entries()) {
      contract.removeAllListeners();
      console.log(`✅ تم إيقاف الاستماع لأحداث ${contractType}`);
    }
    
    this.isListening = false;
    console.log('✅ تم إيقاف الاستماع لجميع أحداث العقود');
  }

  /**
   * التحقق من حالة الاستماع
   */
  isListeningToEvents(): boolean {
    return this.isListening;
  }
}

// إنشاء مثيل واحد من الخدمة
export const enhancedContractEventService = new EnhancedContractEventService();
export default enhancedContractEventService;
