/* تحسينات الهيدر للوحة الإدارة - محدث للتوافق مع الكود */

/* تحسينات الهيدر الأساسية */
.header-enhanced {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: sticky;
  top: 0;
  z-index: 50;
}

.backdrop-enhanced {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.border-enhanced {
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
}

.dark .border-enhanced {
  border-bottom: 1px solid rgba(75, 85, 99, 0.3);
}

.shadow-enhanced {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.transition-enhanced {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تخطيط الهيدر */
.layout-enhanced {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

@media (min-width: 1024px) {
  .layout-enhanced {
    height: 5rem;
  }
}

/* تحسينات الشعار */
.logo-enhanced {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo-enhanced:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 25px -5px rgba(239, 68, 68, 0.3);
}

/* تحسينات النص */
.text-enhanced {
  background: linear-gradient(135deg, #dc2626, #ea580c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.arabic-text-enhanced {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.heading-arabic {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

@media (min-width: 640px) {
  .heading-arabic {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

.body-arabic {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-weight: 500;
}

/* تحسينات عناصر التنقل */
.nav-item-enhanced {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid transparent;
}

.nav-item-enhanced:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* تحسينات الأزرار */
.button-enhanced {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid transparent;
  overflow: hidden;
}

.button-enhanced:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.button-enhanced:active {
  transform: translateY(0);
}

/* تحسينات القوائم المنسدلة */
.dropdown-enhanced {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(229, 231, 235, 0.3);
  animation: dropdownSlideIn 0.2s ease-out;
}

.dark .dropdown-enhanced {
  background: rgba(31, 41, 55, 0.98);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* تحسينات القائمة المحمولة */
.mobile-menu-enhanced {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  animation: mobileMenuSlideIn 0.3s ease-out;
}

.dark .mobile-menu-enhanced {
  background: rgba(17, 24, 39, 0.98);
}

@keyframes mobileMenuSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسينات التركيز */
.focus-enhanced:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.focus-enhanced:focus:not(:focus-visible) {
  outline: none;
}

/* تحسينات الاستجابة المحسنة */
@media (max-width: 640px) {
  .layout-enhanced {
    height: 3.5rem;
    padding: 0 1rem;
  }

  .heading-arabic {
    font-size: 1rem;
  }

  .button-enhanced {
    padding: 0.5rem;
  }
}

@media (min-width: 641px) and (max-width: 1023px) {
  .layout-enhanced {
    height: 4rem;
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .layout-enhanced {
    height: 5rem;
    padding: 0 2rem;
  }
}

/* تحسينات RTL */
.rtl .nav-item-enhanced {
  flex-direction: row-reverse;
}

.rtl .button-enhanced {
  flex-direction: row-reverse;
}

/* تحسينات الحركة */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* تحسينات الانتقالات المتقدمة */
.animate-in {
  animation-fill-mode: both;
}

.slide-in-from-top-2 {
  animation-name: slideInFromTop2;
}

@keyframes slideInFromTop2 {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسينات الطباعة */
@media print {
  .header-enhanced,
  .mobile-menu-enhanced,
  .dropdown-enhanced {
    display: none !important;
  }
}

/* تحسينات الأداء */
.header-enhanced * {
  will-change: transform;
}

.nav-item-enhanced,
.button-enhanced {
  contain: layout style paint;
}

/* تحسينات إضافية للاستجابة */
@media (max-width: 480px) {
  .layout-enhanced {
    height: 3rem;
    padding: 0 0.75rem;
  }

  .heading-arabic {
    font-size: 0.875rem;
  }

  .logo-enhanced {
    width: 1.75rem;
    height: 1.75rem;
  }

  .button-enhanced {
    padding: 0.375rem;
  }

  .nav-item-enhanced {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
  }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1280px) {
  .layout-enhanced {
    height: 5.5rem;
    padding: 0 2.5rem;
  }

  .nav-item-enhanced {
    padding: 0.75rem 1rem;
  }
}

/* تحسينات للشاشات فائقة العرض */
@media (min-width: 1536px) {
  .layout-enhanced {
    padding: 0 3rem;
  }
}

/* تحسينات إمكانية الوصول */
@media (prefers-reduced-motion: reduce) {
  .transition-enhanced,
  .nav-item-enhanced,
  .button-enhanced,
  .logo-enhanced {
    transition: none;
    animation: none;
  }

  .animate-pulse {
    animation: none;
  }
}

/* تحسينات للمس */
@media (hover: none) and (pointer: coarse) {
  .nav-item-enhanced,
  .button-enhanced {
    min-height: 44px;
    min-width: 44px;
  }

  .nav-item-enhanced:hover,
  .button-enhanced:hover {
    transform: none;
  }
}
