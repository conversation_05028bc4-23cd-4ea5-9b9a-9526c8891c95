/* تحسينات الهيدر للوحة الإدارة */

/* تحسينات الهيدر الأساسية */
.admin-header {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(229, 231, 235, 0.2);
  transition: all 0.3s ease;
}

.admin-header.scrolled {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* تحسينات التبويبات */
.admin-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0.75rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.admin-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  border: 1px solid transparent;
}

.admin-tab:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.admin-tab.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
}

.admin-tab.active:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
}

/* تحسينات الوضع المظلم */
.dark .admin-tabs {
  background: rgba(30, 41, 59, 0.95);
}

.dark .admin-tab {
  color: #cbd5e1;
}

.dark .admin-tab:hover {
  background: rgba(59, 130, 246, 0.2);
  color: #f1f5f9;
}

.dark .admin-tab.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

/* تحسينات RTL */
.rtl .admin-tabs {
  direction: rtl;
}

.rtl .admin-tab {
  flex-direction: row-reverse;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .admin-tabs {
    padding: 0.75rem;
    gap: 0.25rem;
  }
  
  .admin-tab {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
}

/* تحسينات الأيقونات */
.admin-tab svg {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

/* تحسينات الإشعارات */
.notification-badge {
  position: absolute;
  top: -0.25rem;
  right: -0.25rem;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  border: 2px solid white;
}

.dark .notification-badge {
  border-color: #1e293b;
}

/* تحسينات الحالة */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-indicator.online {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.status-indicator.offline {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-indicator.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

/* تحسينات الانتقالات */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* تحسينات التمرير */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* تحسينات التركيز */
.admin-tab:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.admin-tab:focus:not(:focus-visible) {
  outline: none;
}

/* تحسينات الطباعة */
@media print {
  .admin-header,
  .admin-tabs {
    display: none;
  }
}
