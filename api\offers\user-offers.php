<?php
/**
 * API جلب عروض المستخدم
 */

require_once __DIR__ . '/../cors.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/response.php';

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method !== 'GET') {
        sendErrorResponse('طريقة طلب غير مدعومة', 405);
    }
    
    $userId = $_GET['user_id'] ?? null;
    if (!$userId) {
        sendErrorResponse('معرف المستخدم مطلوب', 400);
    }
    
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // جلب عروض المستخدم
    getUserOffers($connection, $userId);
    
} catch (Exception $e) {
    logError("User Offers API error: " . $e->getMessage());
    sendErrorResponse('خطأ في الخادم: ' . $e->getMessage(), 500);
}

/**
 * جلب عروض المستخدم
 */
function getUserOffers($connection, $userId) {
    try {
        $stmt = $connection->prepare("
            SELECT 
                o.id,
                o.type,
                o.amount,
                o.price,
                o.currency,
                o.stablecoin,
                o.status,
                o.created_at,
                o.expires_at,
                o.is_active,
                o.is_free,
                o.payment_methods,
                o.trading_terms,
                COALESCE(ov.views_count, 0) as views,
                COALESCE(ot.trades_count, 0) as trades
            FROM offers o
            LEFT JOIN (
                SELECT offer_id, COUNT(*) as views_count 
                FROM offer_views 
                GROUP BY offer_id
            ) ov ON o.id = ov.offer_id
            LEFT JOIN (
                SELECT offer_id, COUNT(*) as trades_count 
                FROM trades 
                WHERE status IN ('completed', 'active')
                GROUP BY offer_id
            ) ot ON o.id = ot.offer_id
            WHERE o.user_id = ?
            ORDER BY o.created_at DESC
        ");
        
        $stmt->execute([$userId]);
        $offers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تنسيق البيانات
        foreach ($offers as &$offer) {
            $offer['id'] = intval($offer['id']);
            $offer['amount'] = floatval($offer['amount']);
            $offer['price'] = floatval($offer['price']);
            $offer['views'] = intval($offer['views']);
            $offer['trades'] = intval($offer['trades']);
            $offer['is_active'] = boolval($offer['is_active']);
            $offer['is_free'] = boolval($offer['is_free']);
            
            // تحديد حالة العرض
            $now = new DateTime();
            $expiresAt = new DateTime($offer['expires_at']);
            
            if (!$offer['is_active']) {
                $offer['status'] = 'inactive';
            } elseif ($expiresAt < $now) {
                $offer['status'] = 'expired';
            } else {
                $offer['status'] = 'active';
            }
            
            // تحويل طرق الدفع من JSON
            $offer['payment_methods'] = json_decode($offer['payment_methods'], true) ?: [];
            
            // تنسيق التواريخ
            $offer['created_at'] = date('Y-m-d H:i:s', strtotime($offer['created_at']));
            $offer['expires_at'] = date('Y-m-d H:i:s', strtotime($offer['expires_at']));
        }
        
        // حساب الإحصائيات
        $stats = calculateUserStats($offers);
        
        sendSuccessResponse([
            'offers' => $offers,
            'stats' => $stats,
            'total' => count($offers)
        ], 'تم جلب عروض المستخدم بنجاح');
        
    } catch (Exception $e) {
        throw new Exception('خطأ في جلب عروض المستخدم: ' . $e->getMessage());
    }
}

/**
 * حساب إحصائيات المستخدم
 */
function calculateUserStats($offers) {
    $stats = [
        'total_offers' => count($offers),
        'active_offers' => 0,
        'inactive_offers' => 0,
        'expired_offers' => 0,
        'completed_trades' => 0,
        'total_views' => 0,
        'total_trades' => 0,
        'success_rate' => 0,
        'average_views_per_offer' => 0
    ];
    
    if (empty($offers)) {
        return $stats;
    }
    
    foreach ($offers as $offer) {
        // عد العروض حسب الحالة
        switch ($offer['status']) {
            case 'active':
                $stats['active_offers']++;
                break;
            case 'inactive':
                $stats['inactive_offers']++;
                break;
            case 'expired':
                $stats['expired_offers']++;
                break;
        }
        
        // جمع المشاهدات والصفقات
        $stats['total_views'] += $offer['views'];
        $stats['total_trades'] += $offer['trades'];
        
        // عد الصفقات المكتملة
        if ($offer['trades'] > 0) {
            $stats['completed_trades'] += $offer['trades'];
        }
    }
    
    // حساب المعدلات
    if ($stats['total_offers'] > 0) {
        $stats['success_rate'] = round(($stats['completed_trades'] / $stats['total_offers']) * 100, 1);
        $stats['average_views_per_offer'] = round($stats['total_views'] / $stats['total_offers'], 1);
    }
    
    return $stats;
}

/**
 * تسجيل الأخطاء
 */
function logError($message) {
    error_log("[" . date('Y-m-d H:i:s') . "] User Offers API: " . $message);
}

?>
