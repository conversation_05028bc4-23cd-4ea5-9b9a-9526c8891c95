<?php
/**
 * API إدارة الاشتراكات
 * يتعامل مع عرض وإدارة خطط الاشتراك واشتراكات المستخدمين
 */

require_once __DIR__ . '/../cors.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/response.php';

try {
    // التحقق من طريقة الطلب
    $method = $_SERVER['REQUEST_METHOD'];
    
    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // تحديد اللغة
    $isArabic = isset($_GET['lang']) && $_GET['lang'] === 'ar';
    
    if ($method === 'GET') {
        $action = $_GET['action'] ?? 'plans';
        
        if ($action === 'plans') {
            // جلب جميع خطط الاشتراك النشطة
            getSubscriptionPlans($connection, $isArabic);
            
        } elseif ($action === 'user-subscription') {
            // جلب اشتراك المستخدم الحالي
            $userId = $_GET['user_id'] ?? null;
            if (!$userId) {
                sendErrorResponse('معرف المستخدم مطلوب', 400);
            }
            getUserSubscription($connection, $userId, $isArabic);
            
        } elseif ($action === 'usage-stats') {
            // جلب إحصائيات الاستخدام الشهري
            $userId = $_GET['user_id'] ?? null;
            if (!$userId) {
                sendErrorResponse('معرف المستخدم مطلوب', 400);
            }
            getUserUsageStats($connection, $userId, $isArabic);
            
        } else {
            sendErrorResponse('إجراء غير صحيح', 400);
        }
        
    } elseif ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
        
        if ($action === 'subscribe') {
            // اشتراك في خطة جديدة
            subscribeToplan($connection, $input, $isArabic);
            
        } elseif ($action === 'cancel') {
            // إلغاء الاشتراك
            cancelSubscription($connection, $input, $isArabic);
            
        } elseif ($action === 'upgrade') {
            // ترقية الخطة
            upgradeSubscription($connection, $input, $isArabic);
            
        } else {
            sendErrorResponse('إجراء غير صحيح', 400);
        }
        
    } else {
        sendErrorResponse('طريقة طلب غير مدعومة', 405);
    }
    
} catch (Exception $e) {
    logError("Subscriptions API error: " . $e->getMessage());
    sendErrorResponse('خطأ في الخادم: ' . $e->getMessage(), 500);
}

/**
 * جلب جميع خطط الاشتراك النشطة
 */
function getSubscriptionPlans($connection, $isArabic = false) {
    try {
        $stmt = $connection->prepare("
            SELECT id, name, name_ar, description, description_ar, 
                   monthly_free_offers, price_monthly, price_yearly, 
                   features, is_default, sort_order
            FROM subscription_plans 
            WHERE is_active = 1 
            ORDER BY sort_order ASC, price_monthly ASC
        ");
        $stmt->execute();
        $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // تنسيق البيانات
        foreach ($plans as &$plan) {
            $plan['features'] = json_decode($plan['features'], true) ?: [];
            $plan['name_display'] = $isArabic ? $plan['name_ar'] : $plan['name'];
            $plan['description_display'] = $isArabic ? $plan['description_ar'] : $plan['description'];
            $plan['price_monthly'] = floatval($plan['price_monthly']);
            $plan['price_yearly'] = floatval($plan['price_yearly']);
            $plan['monthly_free_offers'] = intval($plan['monthly_free_offers']);
            $plan['is_default'] = boolval($plan['is_default']);
        }
        
        sendSuccessResponse([
            'plans' => $plans,
            'total' => count($plans)
        ], 'تم جلب خطط الاشتراك بنجاح');
        
    } catch (Exception $e) {
        throw new Exception('خطأ في جلب خطط الاشتراك: ' . $e->getMessage());
    }
}

/**
 * جلب اشتراك المستخدم الحالي
 */
function getUserSubscription($connection, $userId, $isArabic = false) {
    try {
        $stmt = $connection->prepare("
            SELECT us.*, sp.name, sp.name_ar, sp.monthly_free_offers,
                   sp.price_monthly, sp.features
            FROM user_subscriptions us
            INNER JOIN subscription_plans sp ON us.plan_id = sp.id
            WHERE us.user_id = ? AND us.status = 'active' AND us.expires_at > NOW()
            ORDER BY us.created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$userId]);
        $subscription = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$subscription) {
            // إرجاع الخطة الافتراضية
            $stmt = $connection->prepare("
                SELECT id, name, name_ar, monthly_free_offers, price_monthly, features, is_default
                FROM subscription_plans 
                WHERE is_default = 1 AND is_active = 1
                LIMIT 1
            ");
            $stmt->execute();
            $defaultPlan = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($defaultPlan) {
                $subscription = [
                    'id' => null,
                    'user_id' => $userId,
                    'plan_id' => $defaultPlan['id'],
                    'status' => 'active',
                    'starts_at' => null,
                    'expires_at' => null,
                    'auto_renew' => false,
                    'name' => $defaultPlan['name'],
                    'name_ar' => $defaultPlan['name_ar'],
                    'monthly_free_offers' => $defaultPlan['monthly_free_offers'],
                    'price_monthly' => $defaultPlan['price_monthly'],
                    'features' => $defaultPlan['features'],
                    'is_default' => true
                ];
            }
        }
        
        if ($subscription) {
            $subscription['features'] = json_decode($subscription['features'], true) ?: [];
            $subscription['name_display'] = $isArabic ? $subscription['name_ar'] : $subscription['name'];
            $subscription['monthly_free_offers'] = intval($subscription['monthly_free_offers']);
            $subscription['price_monthly'] = floatval($subscription['price_monthly']);
        }
        
        sendSuccessResponse([
            'subscription' => $subscription
        ], 'تم جلب اشتراك المستخدم بنجاح');
        
    } catch (Exception $e) {
        throw new Exception('خطأ في جلب اشتراك المستخدم: ' . $e->getMessage());
    }
}

/**
 * جلب إحصائيات الاستخدام الشهري
 */
function getUserUsageStats($connection, $userId, $isArabic = false) {
    try {
        $currentMonth = date('Y-m');
        
        // جلب الاستخدام الشهري
        $stmt = $connection->prepare("
            SELECT * FROM user_monthly_limits
            WHERE user_id = ? AND month_year = ?
        ");
        $stmt->execute([$userId, $currentMonth]);
        $monthlyUsage = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$monthlyUsage) {
            $monthlyUsage = [
                'user_id' => $userId,
                'month_year' => $currentMonth,
                'free_offers_used' => 0,
                'subscription_plan_id' => null
            ];
        }
        
        // جلب خطة المستخدم
        require_once __DIR__ . '/../offers/check-free-offer-limits.php';
        $userPlan = getUserCurrentPlan($connection, $userId);
        
        $maxOffers = $userPlan ? $userPlan['monthly_free_offers'] : 3;
        $remainingOffers = max(0, $maxOffers - $monthlyUsage['free_offers_used']);
        
        // حساب تاريخ التجديد
        $resetDate = date('Y-m-01', strtotime('+1 month'));
        
        sendSuccessResponse([
            'usage' => [
                'used' => intval($monthlyUsage['free_offers_used']),
                'limit' => intval($maxOffers),
                'remaining' => intval($remainingOffers),
                'percentage' => $maxOffers > 0 ? round(($monthlyUsage['free_offers_used'] / $maxOffers) * 100, 1) : 0,
                'reset_date' => $resetDate,
                'current_month' => $currentMonth
            ],
            'plan' => $userPlan
        ], 'تم جلب إحصائيات الاستخدام بنجاح');
        
    } catch (Exception $e) {
        throw new Exception('خطأ في جلب إحصائيات الاستخدام: ' . $e->getMessage());
    }
}

/**
 * تسجيل الأخطاء
 */
function logError($message) {
    error_log("[" . date('Y-m-d H:i:s') . "] Subscriptions API: " . $message);
}

?>
