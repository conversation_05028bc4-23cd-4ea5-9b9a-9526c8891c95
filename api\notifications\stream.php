<?php
/**
 * API endpoint للإشعارات الفورية باستخدام Server-Sent Events
 * Real-time Notifications using Server-Sent Events
 */

// إعداد headers للـ SSE
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// تضمين الملفات المطلوبة
require_once __DIR__ . "/../../config/database.php";
require_once __DIR__ . "/../middleware/auth.php";

// إيقاف output buffering
if (ob_get_level()) {
    ob_end_clean();
}

// تعطيل timeout
set_time_limit(0);
ignore_user_abort(false);

/**
 * إرسال حدث SSE
 */
function sendSSEEvent($data, $event = 'message', $id = null, $retry = null) {
    if ($id !== null) {
        echo "id: $id\n";
    }
    
    if ($retry !== null) {
        echo "retry: $retry\n";
    }
    
    echo "event: $event\n";
    echo "data: " . json_encode($data) . "\n\n";
    
    // إجبار الإرسال
    if (ob_get_level()) {
        ob_flush();
    }
    flush();
}

/**
 * فحص الإشعارات الجديدة
 */
function checkNewNotifications($connection, $userId, $lastCheckTime) {
    $stmt = $connection->prepare("
        SELECT n.*, u.username as sender_username
        FROM notifications n
        LEFT JOIN users u ON n.user_id = u.id
        WHERE (n.target_user_id = ? OR n.target_user_id IS NULL) 
        AND n.created_at > ?
        ORDER BY n.created_at DESC
        LIMIT 10
    ");
    
    $stmt->execute([$userId, $lastCheckTime]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * فحص الرسائل الجديدة
 */
function checkNewMessages($connection, $userId, $lastCheckTime) {
    $stmt = $connection->prepare("
        SELECT m.*, t.id as trade_id, u.username as sender_username
        FROM messages m
        JOIN trades t ON m.trade_id = t.id
        JOIN users u ON m.sender_id = u.id
        WHERE (t.seller_id = ? OR t.buyer_id = ?) 
        AND m.sender_id != ?
        AND m.created_at > ?
        ORDER BY m.created_at DESC
        LIMIT 5
    ");
    
    $stmt->execute([$userId, $userId, $userId, $lastCheckTime]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * فحص تحديثات الصفقات
 */
function checkTradeUpdates($connection, $userId, $lastCheckTime) {
    $stmt = $connection->prepare("
        SELECT t.*, u.username as other_user
        FROM trades t
        JOIN users u ON (
            CASE 
                WHEN t.seller_id = ? THEN u.id = t.buyer_id
                ELSE u.id = t.seller_id
            END
        )
        WHERE (t.seller_id = ? OR t.buyer_id = ?) 
        AND t.updated_at > ?
        ORDER BY t.updated_at DESC
        LIMIT 5
    ");
    
    $stmt->execute([$userId, $userId, $userId, $lastCheckTime]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

try {
    // التحقق من معرف المستخدم
    $userId = $_GET['userId'] ?? null;
    
    if (!$userId) {
        sendSSEEvent(['error' => 'معرف المستخدم مطلوب'], 'error');
        exit;
    }
    
    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // التحقق من وجود المستخدم
    $userStmt = $connection->prepare("SELECT id, username FROM users WHERE id = ? AND is_active = 1");
    $userStmt->execute([$userId]);
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        sendSSEEvent(['error' => 'المستخدم غير موجود'], 'error');
        exit;
    }
    
    // إرسال رسالة الاتصال
    sendSSEEvent([
        'type' => 'connected',
        'message' => 'تم الاتصال بنجاح',
        'userId' => $userId,
        'username' => $user['username'],
        'timestamp' => date('Y-m-d H:i:s')
    ], 'connected');
    
    $lastCheckTime = date('Y-m-d H:i:s');
    $heartbeatInterval = 30; // 30 ثانية
    $lastHeartbeat = time();
    
    // حلقة الاستماع
    while (true) {
        // فحص إذا كان العميل ما زال متصلاً
        if (connection_aborted()) {
            break;
        }
        
        $currentTime = date('Y-m-d H:i:s');
        $hasNewData = false;
        
        // فحص الإشعارات الجديدة
        $newNotifications = checkNewNotifications($connection, $userId, $lastCheckTime);
        if (!empty($newNotifications)) {
            foreach ($newNotifications as $notification) {
                sendSSEEvent([
                    'type' => 'notification',
                    'id' => $notification['id'],
                    'title' => $notification['title'],
                    'message' => $notification['message'],
                    'notificationType' => $notification['type'],
                    'senderUsername' => $notification['sender_username'],
                    'data' => $notification['data'] ? json_decode($notification['data'], true) : null,
                    'timestamp' => $notification['created_at']
                ], 'notification', $notification['id']);
            }
            $hasNewData = true;
        }
        
        // فحص الرسائل الجديدة
        $newMessages = checkNewMessages($connection, $userId, $lastCheckTime);
        if (!empty($newMessages)) {
            foreach ($newMessages as $message) {
                sendSSEEvent([
                    'type' => 'message',
                    'id' => $message['id'],
                    'title' => 'رسالة جديدة',
                    'message' => 'لديك رسالة جديدة من ' . $message['sender_username'],
                    'tradeId' => $message['trade_id'],
                    'senderId' => $message['sender_id'],
                    'senderUsername' => $message['sender_username'],
                    'content' => $message['content'],
                    'messageType' => $message['message_type'],
                    'timestamp' => $message['created_at']
                ], 'message', $message['id']);
            }
            $hasNewData = true;
        }
        
        // فحص تحديثات الصفقات
        $tradeUpdates = checkTradeUpdates($connection, $userId, $lastCheckTime);
        if (!empty($tradeUpdates)) {
            foreach ($tradeUpdates as $trade) {
                $statusMessages = [
                    'pending' => 'في انتظار التأكيد',
                    'confirmed' => 'تم تأكيد الصفقة',
                    'payment_sent' => 'تم إرسال الدفع',
                    'payment_received' => 'تم استلام الدفع',
                    'completed' => 'تم إكمال الصفقة',
                    'cancelled' => 'تم إلغاء الصفقة',
                    'disputed' => 'الصفقة في نزاع'
                ];
                
                sendSSEEvent([
                    'type' => 'trade_update',
                    'id' => $trade['id'],
                    'title' => 'تحديث الصفقة #' . $trade['id'],
                    'message' => $statusMessages[$trade['status']] ?? 'تم تحديث الصفقة',
                    'tradeId' => $trade['id'],
                    'status' => $trade['status'],
                    'amount' => $trade['amount'],
                    'currency' => $trade['currency'],
                    'otherUser' => $trade['other_user'],
                    'timestamp' => $trade['updated_at']
                ], 'trade_update', $trade['id']);
            }
            $hasNewData = true;
        }
        
        // تحديث وقت آخر فحص
        if ($hasNewData) {
            $lastCheckTime = $currentTime;
        }
        
        // إرسال heartbeat كل 30 ثانية
        if (time() - $lastHeartbeat >= $heartbeatInterval) {
            sendSSEEvent([
                'type' => 'heartbeat',
                'timestamp' => date('Y-m-d H:i:s'),
                'serverTime' => time()
            ], 'heartbeat');
            $lastHeartbeat = time();
        }
        
        // انتظار ثانيتين قبل الفحص التالي
        sleep(2);
        
        // فحص الذاكرة والوقت
        if (memory_get_usage() > 50 * 1024 * 1024) { // 50MB
            sendSSEEvent([
                'type' => 'restart',
                'message' => 'إعادة تشغيل الاتصال لتحسين الأداء'
            ], 'restart');
            break;
        }
    }
    
} catch (Exception $e) {
    sendSSEEvent([
        'type' => 'error',
        'message' => 'خطأ في الخادم: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], 'error');
    
    error_log('SSE Error: ' . $e->getMessage());
} catch (PDOException $e) {
    sendSSEEvent([
        'type' => 'error',
        'message' => 'خطأ في قاعدة البيانات',
        'timestamp' => date('Y-m-d H:i:s')
    ], 'error');
    
    error_log('SSE Database Error: ' . $e->getMessage());
}

// إغلاق الاتصال
sendSSEEvent([
    'type' => 'disconnected',
    'message' => 'تم قطع الاتصال',
    'timestamp' => date('Y-m-d H:i:s')
], 'disconnected');
?>
