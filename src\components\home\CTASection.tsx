'use client';

import { useState, useEffect } from 'react';
import { 
  <PERSON>Right, 
  Star, 
  CheckCircle, 
  Gift,
  Zap,
  Shield,
  Users,
  TrendingUp,
  <PERSON><PERSON><PERSON>,
  Crown
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { motion, AnimatePresence } from 'framer-motion';

export default function CTASection() {
  const { t } = useTranslation();
  const [currentFeature, setCurrentFeature] = useState(0);

  const features = t('home.cta.features');

  // Auto-rotate features
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [features.length]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      rotate: [0, 5, -5, 0],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <section className="relative py-20 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 dark:from-black dark:via-gray-900 dark:to-gray-800 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        {/* Animated Gradient Orbs */}
        <motion.div 
          className="absolute top-20 left-20 w-96 h-96 bg-blue-500/20 rounded-full filter blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div 
          className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500/20 rounded-full filter blur-3xl"
          animate={{
            scale: [1.3, 1, 1.3],
            opacity: [0.2, 0.5, 0.2]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div 
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-full filter blur-3xl"
          animate={{
            rotate: [0, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        {/* Floating Elements */}
        <motion.div
          className="absolute top-32 right-32 w-16 h-16 bg-yellow-400/30 rounded-full flex items-center justify-center"
          variants={floatingVariants}
          animate="animate"
        >
          <Star className="w-8 h-8 text-yellow-400" />
        </motion.div>

        <motion.div
          className="absolute bottom-40 left-40 w-12 h-12 bg-green-400/30 rounded-full flex items-center justify-center"
          variants={floatingVariants}
          animate="animate"
          style={{ animationDelay: '1s' }}
        >
          <CheckCircle className="w-6 h-6 text-green-400" />
        </motion.div>

        <motion.div
          className="absolute top-1/2 right-20 w-8 h-8 bg-blue-400/40 rounded-full"
          variants={floatingVariants}
          animate="animate"
          style={{ animationDelay: '2s' }}
        />

        {/* Sparkle Effects */}
        {[...Array(20)].map((_, i) => {
          // Use deterministic positions based on index to avoid hydration mismatch
          const positions = [
            { top: 10, left: 20 }, { top: 25, left: 80 }, { top: 40, left: 15 }, { top: 60, left: 90 },
            { top: 75, left: 30 }, { top: 15, left: 70 }, { top: 85, left: 45 }, { top: 35, left: 85 },
            { top: 55, left: 25 }, { top: 70, left: 60 }, { top: 20, left: 40 }, { top: 90, left: 75 },
            { top: 45, left: 10 }, { top: 65, left: 95 }, { top: 30, left: 55 }, { top: 80, left: 35 },
            { top: 50, left: 65 }, { top: 95, left: 20 }, { top: 5, left: 50 }, { top: 75, left: 85 }
          ];
          const position = positions[i] || { top: 50, left: 50 };

          return (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full"
              style={{
                top: `${position.top}%`,
                left: `${position.left}%`,
              }}
              animate={{
                opacity: [0, 1, 0],
                scale: [0, 1, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.2
              }}
            />
          );
        })}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="text-center"
        >
          {/* Badge */}
          <motion.div
            className="inline-flex items-center bg-gradient-to-r from-yellow-400/20 to-orange-400/20 backdrop-blur-sm text-yellow-300 rounded-full px-6 py-3 mb-8 border border-yellow-400/30"
            variants={itemVariants}
          >
            <Crown className="w-5 h-5 ml-2" />
            <span className="text-sm font-medium">{t('home.cta.exclusiveBadge')}</span>
          </motion.div>

          {/* Main Title */}
          <motion.h2
            className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-white mb-6 leading-tight"
            variants={itemVariants}
          >
            <span className="block">{t('home.cta.title')}</span>
            <span className="block bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 bg-clip-text text-transparent">
              {t('home.cta.subtitle')}
            </span>
          </motion.h2>

          {/* Description */}
          <motion.p
            className="text-lg sm:text-xl lg:text-2xl text-blue-100 mb-12 max-w-4xl mx-auto leading-relaxed px-4"
            variants={itemVariants}
          >
            {t('home.cta.description')}
          </motion.p>

          {/* Rotating Features */}
          <motion.div 
            className="mb-12"
            variants={itemVariants}
          >
            <div className="max-w-2xl mx-auto">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentFeature}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                  className="flex items-center justify-center gap-3 p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20"
                >
                  <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-blue-400 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-5 h-5 text-white" />
                  </div>
                  <span className="text-white font-medium text-lg">
                    {features[currentFeature]}
                  </span>
                </motion.div>
              </AnimatePresence>
            </div>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div 
            className="flex flex-col sm:flex-row gap-6 justify-center mb-16"
            variants={itemVariants}
          >
            {/* Primary Button */}
            <motion.a 
              href="/register"
              className="group relative overflow-hidden"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="relative bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 px-6 sm:px-8 md:px-10 py-4 sm:py-5 rounded-2xl font-bold text-lg sm:text-xl shadow-2xl">
                <span className="relative z-10 flex items-center justify-center">
                  <Gift className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 group-hover:rotate-12 transition-transform" />
                  {t('home.cta.primaryButton')}
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-red-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl" />
                
                {/* Sparkle Effect */}
                <motion.div
                  className="absolute top-2 right-2 w-2 h-2 bg-white rounded-full"
                  animate={{
                    scale: [0, 1, 0],
                    opacity: [0, 1, 0]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: 0.5
                  }}
                />
              </div>
            </motion.a>

            {/* Secondary Button */}
            <motion.a 
              href="/offers"
              className="group relative overflow-hidden"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="relative border-2 border-white/30 text-white px-6 sm:px-8 md:px-10 py-4 sm:py-5 rounded-2xl font-bold text-lg sm:text-xl backdrop-blur-sm hover:bg-white/10 transition-all duration-300">
                <span className="flex items-center justify-center">
                  <ArrowRight className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 group-hover:translate-x-1 transition-transform" />
                  {t('home.cta.secondaryButton')}
                </span>
              </div>
            </motion.a>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            className="grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8 mb-16"
            variants={itemVariants}
          >
            <div className="text-center">
              <motion.div
                className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Shield className="w-8 h-8 text-white" />
              </motion.div>
              <h4 className="text-xl font-bold text-white mb-2">{t('home.cta.trustIndicators.secure.title')}</h4>
              <p className="text-blue-200 text-sm">{t('home.cta.trustIndicators.secure.description')}</p>
            </div>

            <div className="text-center">
              <motion.div
                className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg"
                whileHover={{ scale: 1.1, rotate: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Zap className="w-8 h-8 text-white" />
              </motion.div>
              <h4 className="text-xl font-bold text-white mb-2">{t('home.cta.trustIndicators.fast.title')}</h4>
              <p className="text-blue-200 text-sm">{t('home.cta.trustIndicators.fast.description')}</p>
            </div>

            <div className="text-center">
              <motion.div
                className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Users className="w-8 h-8 text-white" />
              </motion.div>
              <h4 className="text-xl font-bold text-white mb-2">{t('home.cta.trustIndicators.trusted.title')}</h4>
              <p className="text-blue-200 text-sm">{t('home.cta.trustIndicators.trusted.description')}</p>
            </div>

            <div className="text-center">
              <motion.div
                className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg"
                whileHover={{ scale: 1.1, rotate: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <TrendingUp className="w-8 h-8 text-white" />
              </motion.div>
              <h4 className="text-xl font-bold text-white mb-2">{t('home.cta.trustIndicators.profitable.title')}</h4>
              <p className="text-blue-200 text-sm">{t('home.cta.trustIndicators.profitable.description')}</p>
            </div>
          </motion.div>

          {/* Bottom Stats */}
          <motion.div
            className="flex flex-wrap justify-center gap-4 sm:gap-6 md:gap-8 text-center"
            variants={itemVariants}
          >
            <div>
              <div className="text-2xl sm:text-3xl font-bold text-white mb-1">50,000+</div>
              <div className="text-blue-200 text-xs sm:text-sm">{t('home.cta.bottomStats.successfulTrades')}</div>
            </div>
            <div className="hidden sm:block w-px h-12 bg-white/20" />
            <div>
              <div className="text-2xl sm:text-3xl font-bold text-white mb-1">12,500+</div>
              <div className="text-blue-200 text-xs sm:text-sm">{t('home.cta.bottomStats.satisfiedUsers')}</div>
            </div>
            <div className="hidden sm:block w-px h-12 bg-white/20" />
            <div>
              <div className="text-2xl sm:text-3xl font-bold text-white mb-1">4.9/5</div>
              <div className="text-blue-200 text-xs sm:text-sm">{t('home.cta.bottomStats.userRating')}</div>
            </div>
            <div className="hidden sm:block w-px h-12 bg-white/20" />
            <div>
              <div className="text-2xl sm:text-3xl font-bold text-white mb-1">99.8%</div>
              <div className="text-blue-200 text-xs sm:text-sm">{t('home.cta.bottomStats.successRate')}</div>
            </div>
          </motion.div>

          {/* Urgency Indicator */}
          <motion.div
            className="mt-12 inline-flex items-center gap-2 px-4 py-2 bg-red-500/20 border border-red-400/30 rounded-full text-red-300"
            variants={itemVariants}
            animate={{
              scale: [1, 1.05, 1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <Sparkles className="w-4 h-4" />
            <span className="text-sm font-medium">
              {t('home.cta.urgencyIndicator')}
            </span>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
