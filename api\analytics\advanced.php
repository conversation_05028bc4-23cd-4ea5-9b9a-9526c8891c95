<?php
/**
 * API endpoint للتحليلات المتقدمة
 * Advanced Analytics API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    // Get database connection
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // Get request parameters
    $timeRange = $_GET['timeRange'] ?? '24h';
    $metrics = $_GET['metrics'] ?? 'all';
    
    // Calculate time range
    $timeRanges = [
        '1h' => 'DATE_SUB(NOW(), INTERVAL 1 HOUR)',
        '24h' => 'DATE_SUB(NOW(), INTERVAL 24 HOUR)',
        '7d' => 'DATE_SUB(NOW(), INTERVAL 7 DAY)',
        '30d' => 'DATE_SUB(NOW(), INTERVAL 30 DAY)',
        '90d' => 'DATE_SUB(NOW(), INTERVAL 90 DAY)'
    ];
    
    $timeFilter = $timeRanges[$timeRange] ?? $timeRanges['24h'];
    
    // Initialize response data
    $analyticsData = [];
    
    // === مؤشرات الحجم والتداول ===
    if ($metrics === 'all' || $metrics === 'volume') {
        $volumeData = getVolumeMetrics($connection, $timeFilter, $timeRange);
        $analyticsData['volume'] = $volumeData;
    }
    
    // === مؤشرات المستخدمين ===
    if ($metrics === 'all' || $metrics === 'users') {
        $userData = getUserMetrics($connection, $timeFilter, $timeRange);
        $analyticsData['users'] = $userData;
    }
    
    // === مؤشرات الأداء ===
    if ($metrics === 'all' || $metrics === 'performance') {
        $performanceData = getPerformanceMetrics($connection, $timeFilter, $timeRange);
        $analyticsData['performance'] = $performanceData;
    }
    
    // === مؤشرات الجودة ===
    if ($metrics === 'all' || $metrics === 'quality') {
        $qualityData = getQualityMetrics($connection, $timeFilter, $timeRange);
        $analyticsData['quality'] = $qualityData;
    }
    
    // === التوزيع الجغرافي ===
    if ($metrics === 'all' || $metrics === 'geography') {
        $geographyData = getGeographyMetrics($connection, $timeFilter);
        $analyticsData['geography'] = $geographyData;
    }
    
    // === اتجاهات السوق ===
    if ($metrics === 'all' || $metrics === 'trends') {
        $trendsData = getTrendsData($connection, $timeFilter);
        $analyticsData['trends'] = $trendsData;
    }
    
    // Success response
    echo json_encode([
        'success' => true,
        'data' => $analyticsData,
        'timeRange' => $timeRange,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// === دوال مساعدة ===

/**
 * الحصول على مؤشرات الحجم والتداول
 */
function getVolumeMetrics($connection, $timeFilter, $timeRange) {
    $data = [
        'totalVolume' => 0,
        'totalVolumeUSD' => 0,
        'totalTrades' => 0,
        'volumeChange24h' => 0,
        'tradesChange24h' => 0,
        'averageTradeSize' => 0,
        'hourlyVolume' => [],
        'currencyBreakdown' => []
    ];
    
    try {
        // التحقق من وجود جدول التداولات
        $checkTable = $connection->prepare("SHOW TABLES LIKE 'trades'");
        $checkTable->execute();
        
        if ($checkTable->rowCount() > 0) {
            // إجمالي الحجم والصفقات
            $stmt = $connection->prepare("
                SELECT 
                    COUNT(*) as total_trades,
                    COALESCE(SUM(amount), 0) as total_volume,
                    COALESCE(AVG(amount), 0) as avg_trade_size
                FROM trades 
                WHERE created_at >= $timeFilter
                AND status = 'completed'
            ");
            $stmt->execute();
            $result = $stmt->fetch();
            
            $data['totalTrades'] = (int)$result['total_trades'];
            $data['totalVolume'] = (float)$result['total_volume'];
            $data['totalVolumeUSD'] = (float)$result['total_volume']; // افتراض أن العملة USDT
            $data['averageTradeSize'] = (float)$result['avg_trade_size'];
            
            // حساب التغيير خلال 24 ساعة
            $stmt24h = $connection->prepare("
                SELECT 
                    COUNT(*) as trades_24h,
                    COALESCE(SUM(amount), 0) as volume_24h
                FROM trades 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                AND created_at < DATE_SUB(NOW(), INTERVAL 48 HOUR)
                AND status = 'completed'
            ");
            $stmt24h->execute();
            $result24h = $stmt24h->fetch();
            
            $prevTrades = (int)$result24h['trades_24h'];
            $prevVolume = (float)$result24h['volume_24h'];
            
            $data['tradesChange24h'] = $prevTrades > 0 ? 
                (($data['totalTrades'] - $prevTrades) / $prevTrades) * 100 : 0;
            $data['volumeChange24h'] = $prevVolume > 0 ? 
                (($data['totalVolume'] - $prevVolume) / $prevVolume) * 100 : 0;
            
            // الحجم بالساعة (للرسم البياني)
            if ($timeRange === '24h' || $timeRange === '7d') {
                $stmt = $connection->prepare("
                    SELECT 
                        HOUR(created_at) as hour,
                        COALESCE(SUM(amount), 0) as volume
                    FROM trades 
                    WHERE created_at >= $timeFilter
                    AND status = 'completed'
                    GROUP BY HOUR(created_at)
                    ORDER BY hour
                ");
                $stmt->execute();
                $data['hourlyVolume'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
            
            // توزيع العملات
            $stmt = $connection->prepare("
                SELECT 
                    currency,
                    COUNT(*) as trades,
                    COALESCE(SUM(amount), 0) as volume
                FROM trades 
                WHERE created_at >= $timeFilter
                AND status = 'completed'
                GROUP BY currency
                ORDER BY volume DESC
                LIMIT 10
            ");
            $stmt->execute();
            $data['currencyBreakdown'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
    } catch (Exception $e) {
        error_log('Error in getVolumeMetrics: ' . $e->getMessage());
    }
    
    return $data;
}

/**
 * الحصول على مؤشرات المستخدمين
 */
function getUserMetrics($connection, $timeFilter, $timeRange) {
    $data = [
        'totalUsers' => 0,
        'activeUsers' => 0,
        'newUsers' => 0,
        'usersChange24h' => 0,
        'userRetention' => 0,
        'topTraders' => [],
        'userActivity' => []
    ];
    
    try {
        // إجمالي المستخدمين
        $stmt = $connection->prepare("SELECT COUNT(*) as total FROM users");
        $stmt->execute();
        $data['totalUsers'] = (int)$stmt->fetch()['total'];
        
        // المستخدمين النشطين (لديهم نشاط خلال الفترة)
        $stmt = $connection->prepare("
            SELECT COUNT(DISTINCT user_id) as active 
            FROM trades 
            WHERE created_at >= $timeFilter
        ");
        $stmt->execute();
        $data['activeUsers'] = (int)$stmt->fetch()['active'];
        
        // المستخدمين الجدد
        $stmt = $connection->prepare("
            SELECT COUNT(*) as new_users 
            FROM users 
            WHERE created_at >= $timeFilter
        ");
        $stmt->execute();
        $data['newUsers'] = (int)$stmt->fetch()['new_users'];
        
        // أهم المتداولين
        $stmt = $connection->prepare("
            SELECT 
                u.username,
                u.email,
                COUNT(t.id) as trades_count,
                COALESCE(SUM(t.amount), 0) as total_volume
            FROM users u
            LEFT JOIN trades t ON u.id = t.user_id
            WHERE t.created_at >= $timeFilter
            AND t.status = 'completed'
            GROUP BY u.id
            ORDER BY total_volume DESC
            LIMIT 10
        ");
        $stmt->execute();
        $data['topTraders'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log('Error in getUserMetrics: ' . $e->getMessage());
    }
    
    return $data;
}

/**
 * الحصول على مؤشرات الأداء
 */
function getPerformanceMetrics($connection, $timeFilter, $timeRange) {
    $data = [
        'averageResponseTime' => 0,
        'systemLoad' => 0,
        'memoryUsage' => 0,
        'databaseConnections' => 0,
        'cacheHitRate' => 0,
        'errorRate' => 0
    ];
    
    // محاكاة بيانات الأداء (في التطبيق الحقيقي، ستأتي من مراقبة النظام)
    $data['averageResponseTime'] = rand(50, 200); // milliseconds
    $data['systemLoad'] = rand(10, 80); // percentage
    $data['memoryUsage'] = rand(40, 85); // percentage
    $data['databaseConnections'] = rand(5, 50);
    $data['cacheHitRate'] = rand(80, 99); // percentage
    $data['errorRate'] = rand(0, 5); // percentage
    
    return $data;
}

/**
 * الحصول على مؤشرات الجودة
 */
function getQualityMetrics($connection, $timeFilter, $timeRange) {
    $data = [
        'completionRate' => 0,
        'averageTradeTime' => 0,
        'disputeRate' => 0,
        'userSatisfaction' => 0,
        'platformReliability' => 0
    ];
    
    try {
        // معدل الإكمال
        $stmt = $connection->prepare("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
            FROM trades 
            WHERE created_at >= $timeFilter
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        
        $total = (int)$result['total'];
        $completed = (int)$result['completed'];
        $data['completionRate'] = $total > 0 ? ($completed / $total) * 100 : 0;
        
        // متوسط وقت التداول (بالدقائق)
        $stmt = $connection->prepare("
            SELECT AVG(TIMESTAMPDIFF(MINUTE, created_at, updated_at)) as avg_time
            FROM trades 
            WHERE created_at >= $timeFilter
            AND status = 'completed'
            AND updated_at IS NOT NULL
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        $data['averageTradeTime'] = (float)($result['avg_time'] ?? 0);
        
        // معدل النزاعات
        $stmt = $connection->prepare("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'disputed' THEN 1 ELSE 0 END) as disputed
            FROM trades 
            WHERE created_at >= $timeFilter
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        
        $total = (int)$result['total'];
        $disputed = (int)$result['disputed'];
        $data['disputeRate'] = $total > 0 ? ($disputed / $total) * 100 : 0;
        
        // محاكاة مؤشرات أخرى
        $data['userSatisfaction'] = rand(85, 98); // percentage
        $data['platformReliability'] = rand(95, 99.9); // percentage
        
    } catch (Exception $e) {
        error_log('Error in getQualityMetrics: ' . $e->getMessage());
    }
    
    return $data;
}

/**
 * الحصول على التوزيع الجغرافي
 */
function getGeographyMetrics($connection, $timeFilter) {
    $data = [
        'topCountries' => [],
        'topCities' => [],
        'regionDistribution' => []
    ];
    
    // محاكاة بيانات جغرافية (في التطبيق الحقيقي، ستأتي من IP geolocation)
    $data['topCountries'] = [
        ['country' => 'السعودية', 'users' => rand(100, 500), 'volume' => rand(10000, 50000)],
        ['country' => 'الإمارات', 'users' => rand(50, 200), 'volume' => rand(5000, 25000)],
        ['country' => 'الكويت', 'users' => rand(30, 150), 'volume' => rand(3000, 15000)],
        ['country' => 'قطر', 'users' => rand(20, 100), 'volume' => rand(2000, 10000)],
        ['country' => 'البحرين', 'users' => rand(15, 80), 'volume' => rand(1500, 8000)]
    ];
    
    return $data;
}

/**
 * الحصول على بيانات الاتجاهات
 */
function getTrendsData($connection, $timeFilter) {
    $data = [
        'volumeTrend' => [],
        'userGrowth' => [],
        'marketSentiment' => 'positive',
        'predictions' => []
    ];
    
    // محاكاة بيانات الاتجاهات
    for ($i = 0; $i < 30; $i++) {
        $data['volumeTrend'][] = [
            'date' => date('Y-m-d', strtotime("-$i days")),
            'volume' => rand(1000, 10000)
        ];
        
        $data['userGrowth'][] = [
            'date' => date('Y-m-d', strtotime("-$i days")),
            'users' => rand(10, 100)
        ];
    }
    
    return $data;
}
?>
