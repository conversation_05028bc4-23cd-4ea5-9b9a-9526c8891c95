<?php
/**
 * API endpoint لمراقبة الأداء في الوقت الفعلي
 * Real-time Performance Monitoring API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    // Get database connection
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // Get request parameters
    $component = $_GET['component'] ?? 'all';
    $detailed = $_GET['detailed'] ?? 'false';
    
    // Initialize performance data
    $performanceData = [];
    
    // === مؤشرات النظام ===
    if ($component === 'all' || $component === 'system') {
        $performanceData['system'] = getSystemMetrics();
    }
    
    // === مؤشرات قاعدة البيانات ===
    if ($component === 'all' || $component === 'database') {
        $performanceData['database'] = getDatabaseMetrics($connection);
    }
    
    // === مؤشرات الشبكة ===
    if ($component === 'all' || $component === 'network') {
        $performanceData['network'] = getNetworkMetrics();
    }
    
    // === مؤشرات البلوك تشين ===
    if ($component === 'all' || $component === 'blockchain') {
        $performanceData['blockchain'] = getBlockchainMetrics();
    }
    
    // === مؤشرات التطبيق ===
    if ($component === 'all' || $component === 'application') {
        $performanceData['application'] = getApplicationMetrics($connection);
    }
    
    // === التنبيهات النشطة ===
    if ($component === 'all' || $component === 'alerts') {
        $performanceData['alerts'] = getActiveAlerts($performanceData);
    }
    
    // Success response
    echo json_encode([
        'success' => true,
        'data' => $performanceData,
        'timestamp' => date('Y-m-d H:i:s'),
        'server_time' => microtime(true)
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// === دوال مساعدة ===

/**
 * الحصول على مؤشرات النظام
 */
function getSystemMetrics() {
    $data = [
        'cpu' => [
            'usage' => 0,
            'temperature' => 0,
            'cores' => 0,
            'frequency' => 0,
            'load_average' => []
        ],
        'memory' => [
            'used' => 0,
            'total' => 0,
            'percentage' => 0,
            'available' => 0,
            'cached' => 0
        ],
        'disk' => [
            'used' => 0,
            'total' => 0,
            'percentage' => 0,
            'available' => 0
        ],
        'uptime' => 0
    ];
    
    try {
        // معلومات الذاكرة
        if (function_exists('memory_get_usage')) {
            $memoryUsage = memory_get_usage(true);
            $memoryLimit = ini_get('memory_limit');
            
            // تحويل memory_limit إلى bytes
            $memoryLimitBytes = convertToBytes($memoryLimit);
            
            $data['memory']['used'] = round($memoryUsage / 1024 / 1024, 2); // MB
            $data['memory']['total'] = round($memoryLimitBytes / 1024 / 1024, 2); // MB
            $data['memory']['percentage'] = $memoryLimitBytes > 0 ? 
                round(($memoryUsage / $memoryLimitBytes) * 100, 2) : 0;
            $data['memory']['available'] = $data['memory']['total'] - $data['memory']['used'];
        }
        
        // معلومات CPU (محاكاة)
        $data['cpu']['usage'] = rand(10, 80);
        $data['cpu']['temperature'] = rand(45, 75);
        $data['cpu']['cores'] = 4; // افتراضي
        $data['cpu']['frequency'] = 2.4;
        
        // Load average (إذا كان متاح)
        if (function_exists('sys_getloadavg')) {
            $data['cpu']['load_average'] = sys_getloadavg();
        } else {
            $data['cpu']['load_average'] = [0.5, 0.6, 0.7];
        }
        
        // معلومات القرص (محاكاة)
        $totalSpace = disk_total_space(__DIR__);
        $freeSpace = disk_free_space(__DIR__);
        
        if ($totalSpace && $freeSpace) {
            $usedSpace = $totalSpace - $freeSpace;
            $data['disk']['total'] = round($totalSpace / 1024 / 1024 / 1024, 2); // GB
            $data['disk']['used'] = round($usedSpace / 1024 / 1024 / 1024, 2); // GB
            $data['disk']['available'] = round($freeSpace / 1024 / 1024 / 1024, 2); // GB
            $data['disk']['percentage'] = round(($usedSpace / $totalSpace) * 100, 2);
        }
        
        // Uptime (محاكاة)
        $data['uptime'] = rand(3600, 86400 * 30); // seconds
        
    } catch (Exception $e) {
        error_log('Error in getSystemMetrics: ' . $e->getMessage());
    }
    
    return $data;
}

/**
 * الحصول على مؤشرات قاعدة البيانات
 */
function getDatabaseMetrics($connection) {
    $data = [
        'connections' => [
            'active' => 0,
            'max' => 0,
            'percentage' => 0
        ],
        'queries' => [
            'total' => 0,
            'slow' => 0,
            'average_time' => 0,
            'queries_per_second' => 0
        ],
        'cache' => [
            'hit_rate' => 0,
            'size' => 0,
            'usage' => 0
        ],
        'tables' => [
            'count' => 0,
            'size' => 0,
            'largest_table' => ''
        ]
    ];
    
    try {
        // عدد الاتصالات النشطة
        $stmt = $connection->prepare("SHOW STATUS LIKE 'Threads_connected'");
        $stmt->execute();
        $result = $stmt->fetch();
        $data['connections']['active'] = (int)($result['Value'] ?? 0);
        
        // الحد الأقصى للاتصالات
        $stmt = $connection->prepare("SHOW VARIABLES LIKE 'max_connections'");
        $stmt->execute();
        $result = $stmt->fetch();
        $data['connections']['max'] = (int)($result['Value'] ?? 100);
        
        $data['connections']['percentage'] = $data['connections']['max'] > 0 ? 
            round(($data['connections']['active'] / $data['connections']['max']) * 100, 2) : 0;
        
        // إحصائيات الاستعلامات
        $stmt = $connection->prepare("SHOW STATUS LIKE 'Questions'");
        $stmt->execute();
        $result = $stmt->fetch();
        $data['queries']['total'] = (int)($result['Value'] ?? 0);
        
        // Uptime للحصول على queries per second
        $stmt = $connection->prepare("SHOW STATUS LIKE 'Uptime'");
        $stmt->execute();
        $result = $stmt->fetch();
        $uptime = (int)($result['Value'] ?? 1);
        
        $data['queries']['queries_per_second'] = $uptime > 0 ? 
            round($data['queries']['total'] / $uptime, 2) : 0;
        
        // معدل إصابة الكاش
        $stmt = $connection->prepare("
            SHOW STATUS WHERE Variable_name IN (
                'Qcache_hits', 'Qcache_inserts', 'Qcache_not_cached'
            )
        ");
        $stmt->execute();
        $cacheStats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        $hits = (int)($cacheStats['Qcache_hits'] ?? 0);
        $inserts = (int)($cacheStats['Qcache_inserts'] ?? 0);
        $notCached = (int)($cacheStats['Qcache_not_cached'] ?? 0);
        
        $total = $hits + $inserts + $notCached;
        $data['cache']['hit_rate'] = $total > 0 ? round(($hits / $total) * 100, 2) : 0;
        
        // عدد الجداول
        $stmt = $connection->prepare("
            SELECT COUNT(*) as table_count 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
        ");
        $stmt->execute();
        $result = $stmt->fetch();
        $data['tables']['count'] = (int)($result['table_count'] ?? 0);
        
        // محاكاة بيانات أخرى
        $data['queries']['slow'] = rand(0, 10);
        $data['queries']['average_time'] = rand(50, 200); // milliseconds
        $data['cache']['size'] = rand(10, 100); // MB
        $data['cache']['usage'] = rand(30, 90); // percentage
        $data['tables']['size'] = rand(100, 1000); // MB
        $data['tables']['largest_table'] = 'trades';
        
    } catch (Exception $e) {
        error_log('Error in getDatabaseMetrics: ' . $e->getMessage());
    }
    
    return $data;
}

/**
 * الحصول على مؤشرات الشبكة
 */
function getNetworkMetrics() {
    $data = [
        'latency' => rand(10, 100), // milliseconds
        'throughput' => rand(50, 500), // Mbps
        'packet_loss' => rand(0, 5), // percentage
        'connections' => rand(100, 1000),
        'bandwidth_usage' => rand(20, 80), // percentage
        'response_time' => rand(100, 500) // milliseconds
    ];
    
    return $data;
}

/**
 * الحصول على مؤشرات البلوك تشين
 */
function getBlockchainMetrics() {
    $data = [
        'network' => [
            'name' => 'BSC Testnet',
            'chain_id' => 97,
            'block_height' => rand(34567890, 34567990),
            'block_time' => 3, // seconds
            'network_congestion' => rand(10, 80) // percentage
        ],
        'gas' => [
            'price' => rand(5, 25), // Gwei
            'limit' => 21000,
            'used' => rand(15000, 21000),
            'optimization' => rand(80, 95) // percentage
        ],
        'transactions' => [
            'pending' => rand(100, 1000),
            'confirmed' => rand(1000, 5000),
            'failed' => rand(10, 100),
            'success_rate' => rand(95, 99.5) // percentage
        ],
        'contract' => [
            'address' => '0xEcB2851e50e7CB5ea05c29b7e5b2221913bFce43',
            'interactions' => rand(1000, 5000),
            'last_interaction' => date('Y-m-d H:i:s', time() - rand(60, 3600))
        ]
    ];
    
    return $data;
}

/**
 * الحصول على مؤشرات التطبيق
 */
function getApplicationMetrics($connection) {
    $data = [
        'response_times' => [
            'api_average' => rand(100, 300), // milliseconds
            'database_average' => rand(50, 150), // milliseconds
            'page_load_average' => rand(500, 2000) // milliseconds
        ],
        'error_rates' => [
            'api_errors' => rand(0, 5), // percentage
            'database_errors' => rand(0, 2), // percentage
            'application_errors' => rand(0, 3) // percentage
        ],
        'cache' => [
            'redis_hit_rate' => rand(85, 99), // percentage
            'file_cache_hit_rate' => rand(80, 95), // percentage
            'memory_cache_usage' => rand(40, 80) // percentage
        ],
        'sessions' => [
            'active' => rand(50, 500),
            'total_today' => rand(1000, 5000),
            'average_duration' => rand(300, 1800) // seconds
        ]
    ];
    
    return $data;
}

/**
 * الحصول على التنبيهات النشطة
 */
function getActiveAlerts($performanceData) {
    $alerts = [];
    
    // فحص استخدام الذاكرة
    if (isset($performanceData['system']['memory']['percentage']) && 
        $performanceData['system']['memory']['percentage'] > 85) {
        $alerts[] = [
            'id' => 'memory_high_' . time(),
            'type' => 'memory',
            'severity' => $performanceData['system']['memory']['percentage'] > 95 ? 'critical' : 'high',
            'title' => 'استخدام مرتفع للذاكرة',
            'message' => 'استخدام الذاكرة وصل إلى ' . $performanceData['system']['memory']['percentage'] . '%',
            'timestamp' => date('Y-m-d H:i:s'),
            'value' => $performanceData['system']['memory']['percentage'],
            'threshold' => 85
        ];
    }
    
    // فحص استخدام CPU
    if (isset($performanceData['system']['cpu']['usage']) && 
        $performanceData['system']['cpu']['usage'] > 80) {
        $alerts[] = [
            'id' => 'cpu_high_' . time(),
            'type' => 'cpu',
            'severity' => $performanceData['system']['cpu']['usage'] > 90 ? 'critical' : 'high',
            'title' => 'استخدام مرتفع للمعالج',
            'message' => 'استخدام المعالج وصل إلى ' . $performanceData['system']['cpu']['usage'] . '%',
            'timestamp' => date('Y-m-d H:i:s'),
            'value' => $performanceData['system']['cpu']['usage'],
            'threshold' => 80
        ];
    }
    
    // فحص اتصالات قاعدة البيانات
    if (isset($performanceData['database']['connections']['percentage']) && 
        $performanceData['database']['connections']['percentage'] > 80) {
        $alerts[] = [
            'id' => 'db_connections_high_' . time(),
            'type' => 'database',
            'severity' => 'medium',
            'title' => 'اتصالات قاعدة البيانات مرتفعة',
            'message' => 'استخدام اتصالات قاعدة البيانات وصل إلى ' . $performanceData['database']['connections']['percentage'] . '%',
            'timestamp' => date('Y-m-d H:i:s'),
            'value' => $performanceData['database']['connections']['percentage'],
            'threshold' => 80
        ];
    }
    
    return $alerts;
}

/**
 * تحويل memory_limit إلى bytes
 */
function convertToBytes($value) {
    $value = trim($value);
    $last = strtolower($value[strlen($value)-1]);
    $value = (int)$value;
    
    switch($last) {
        case 'g':
            $value *= 1024;
        case 'm':
            $value *= 1024;
        case 'k':
            $value *= 1024;
    }
    
    return $value;
}
?>
