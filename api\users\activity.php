<?php
/**
 * API لجلب نشاط المستخدم
 * User Activity API
 */

// تمكين CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = new Database();
    $connection = $db->connect();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $userId = $_GET['user_id'] ?? null;
        $limit = intval($_GET['limit'] ?? 10);
        $offset = intval($_GET['offset'] ?? 0);
        
        if (!$userId) {
            throw new Exception('معرف المستخدم مطلوب');
        }
        
        // التحقق من وجود المستخدم
        $stmt = $connection->prepare("SELECT id FROM users WHERE id = ? AND is_active = 1");
        $stmt->execute([$userId]);
        
        if (!$stmt->fetch()) {
            throw new Exception('المستخدم غير موجود');
        }
        
        // جلب نشاط المستخدم من جداول مختلفة
        $activities = [];

        // جلب الصفقات الأخيرة
        try {
            $tradesStmt = $connection->prepare("
                SELECT
                    CONCAT('trade_', id) as id,
                    CASE
                        WHEN status = 'completed' THEN 'trade_completed'
                        WHEN status = 'cancelled' THEN 'trade_cancelled'
                        ELSE 'trade_created'
                    END as type,
                    'trade' as entity_type,
                    id as entity_id,
                    JSON_OBJECT('amount', total_value, 'currency', stablecoin) as data,
                    created_at,
                    status
                FROM trades
                WHERE seller_id = ? OR buyer_id = ?
                ORDER BY created_at DESC
                LIMIT ?
            ");

            $tradesStmt->execute([$userId, $userId, min($limit, 5)]);
            $tradeActivities = $tradesStmt->fetchAll(PDO::FETCH_ASSOC);
            $activities = array_merge($activities, $tradeActivities);
        } catch (Exception $e) {
            error_log('Error fetching trade activities: ' . $e->getMessage());
        }

        // جلب العروض الأخيرة
        try {
            $offersStmt = $connection->prepare("
                SELECT
                    CONCAT('offer_', id) as id,
                    'offer_created' as type,
                    'offer' as entity_type,
                    id as entity_id,
                    JSON_OBJECT('amount', amount, 'currency', currency) as data,
                    created_at,
                    'success' as status
                FROM offers
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT ?
            ");

            $offersStmt->execute([$userId, min($limit, 3)]);
            $offerActivities = $offersStmt->fetchAll(PDO::FETCH_ASSOC);
            $activities = array_merge($activities, $offerActivities);
        } catch (Exception $e) {
            error_log('Error fetching offer activities: ' . $e->getMessage());
        }

        // جلب معاملات المحفظة الأخيرة
        try {
            $walletStmt = $connection->prepare("
                SELECT
                    CONCAT('wallet_', id) as id,
                    CONCAT('wallet_', transaction_type) as type,
                    'wallet' as entity_type,
                    id as entity_id,
                    JSON_OBJECT('amount', amount, 'currency', currency) as data,
                    created_at,
                    status
                FROM wallet_transactions
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT ?
            ");

            $walletStmt->execute([$userId, min($limit, 3)]);
            $walletActivities = $walletStmt->fetchAll(PDO::FETCH_ASSOC);
            $activities = array_merge($activities, $walletActivities);
        } catch (Exception $e) {
            error_log('Error fetching wallet activities: ' . $e->getMessage());
        }

        // ترتيب الأنشطة حسب التاريخ
        usort($activities, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        // تحديد العدد المطلوب
        $activities = array_slice($activities, $offset, $limit);
        
        // تحويل البيانات إلى تنسيق مناسب للواجهة الأمامية
        $formattedActivities = [];
        foreach ($activities as $activity) {
            $description = '';
            $amount = null;
            $currency = null;

            // تحديد الوصف والمبلغ حسب نوع النشاط
            switch ($activity['type']) {
                case 'trade_created':
                    $description = 'تم إنشاء صفقة جديدة';
                    break;
                case 'trade_completed':
                    $description = 'تم إكمال صفقة بنجاح';
                    break;
                case 'trade_cancelled':
                    $description = 'تم إلغاء صفقة';
                    break;
                case 'offer_created':
                    $description = 'تم إنشاء عرض جديد';
                    break;
                case 'wallet_deposit':
                    $description = 'تم إيداع في المحفظة';
                    break;
                case 'wallet_withdrawal':
                    $description = 'تم سحب من المحفظة';
                    break;
                case 'wallet_trade':
                    $description = 'معاملة تداول';
                    break;
                case 'wallet_fee':
                    $description = 'رسوم معاملة';
                    break;
                case 'wallet_refund':
                    $description = 'استرداد مبلغ';
                    break;
                default:
                    $description = 'نشاط جديد';
            }

            // محاولة استخراج المبلغ من البيانات المخزنة
            if ($activity['data']) {
                $data = json_decode($activity['data'], true);
                if (isset($data['amount'])) {
                    $amount = floatval($data['amount']);
                    $currency = $data['currency'] ?? 'USDT';
                }
            }

            $formattedActivities[] = [
                'id' => $activity['id'],
                'type' => $activity['type'],
                'description' => $description,
                'amount' => $amount,
                'currency' => $currency,
                'created_at' => $activity['created_at'],
                'status' => $activity['status'] === 'completed' ? 'success' : ($activity['status'] === 'failed' ? 'error' : 'pending')
            ];
        }
        
        // إذا لم توجد أنشطة، إرجاع بيانات وهمية
        if (empty($formattedActivities)) {
            $formattedActivities = [
                [
                    'id' => 'welcome_1',
                    'type' => 'account_created',
                    'description' => 'مرحباً بك في منصة إيكاروس P2P',
                    'amount' => null,
                    'currency' => null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'status' => 'success'
                ],
                [
                    'id' => 'welcome_2',
                    'type' => 'profile_setup',
                    'description' => 'يمكنك الآن البدء في إنشاء العروض والتداول',
                    'amount' => null,
                    'currency' => null,
                    'created_at' => date('Y-m-d H:i:s', strtotime('-1 minute')),
                    'status' => 'success'
                ]
            ];
        }
        
        echo json_encode([
            'success' => true,
            'data' => $formattedActivities,
            'total' => count($formattedActivities)
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
    
    error_log('Database error in users/activity.php: ' . $e->getMessage());
}
?>
