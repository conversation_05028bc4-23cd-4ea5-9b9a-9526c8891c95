{"dashboard": {"title": "Administrative Dashboard", "subtitle": "Comprehensive management for Ikaros P2P platform", "welcome": "Welcome to the dashboard", "lastLogin": "Last login", "systemStatus": "System status", "quickActions": "Quick actions", "recentActivity": "Recent activity", "systemHealth": "System health", "platformOverview": "Platform overview"}, "navigation": {"overview": "Overview", "analytics": "Analytics", "trading": "Trading Interface", "users": "User Management", "trades": "Trade Management", "notifications": "Notifications", "contracts": "Smart Contracts", "events": "Blockchain Events", "admins": "Admin Management", "settings": "Settings", "testing": "Testing Tools", "monitoring": "System Monitoring", "networks": "Network Management", "tokens": "Token Management", "disputes": "Dispute Management", "database": "Database", "groups": {"core": "Core Management", "users": "User Management", "technical": "Networks & Technology", "security": "Monitoring & Security"}}, "stats": {"totalUsers": "Total Users", "activeUsers": "Active Users", "totalTrades": "Total Trades", "activeTrades": "Active Trades", "completedTrades": "Completed Trades", "pendingDisputes": "Pending Disputes", "monthlyVolume": "Monthly Volume", "monthlyFees": "Monthly Fees", "platformRevenue": "Platform Revenue", "dailyVolume": "Daily Volume", "averageTradeTime": "Average Trade Time", "successRate": "Success Rate", "userGrowth": "User Growth", "networkHealth": "Network Health"}, "overview": {"title": "System Overview", "recentActivity": "Recent Activity", "systemAlerts": "System Alerts", "quickActions": "Quick Actions", "tradingVolume": "Trading Volume", "platformFees": "Platform Fees", "total": "Total", "thisMonth": "This Month", "thisWeek": "This Week", "today": "Today", "growth": "Growth", "change": "Change"}, "users": {"title": "User Management", "subtitle": "Comprehensive management of user accounts and identity verification", "searchPlaceholder": "Search users...", "totalUsers": "Total Users", "verifiedUsers": "Verified Users", "suspendedUsers": "Suspended Users", "newUsersToday": "New Users Today", "onlineUsers": "Online Users", "premiumUsers": "Premium Users", "actions": {"view": "View", "edit": "Edit", "suspend": "Suspend", "activate": "Activate", "verify": "Verify", "delete": "Delete", "sendMessage": "Send Message", "viewTrades": "View Trades", "viewProfile": "View Profile", "editProfile": "Edit Profile"}, "status": {"verified": "Verified", "pending": "Pending", "suspended": "Suspended", "premium": "Premium"}, "filters": {"all": "All", "active": "Active", "verified": "Verified", "unverified": "Unverified", "suspended": "Suspended", "vipUsers": "VIP Users", "highVolume": "High Volume", "kycPending": "KYC Pending", "kycApproved": "KYC Approved"}, "activity": {"lastActivity": "Last Activity"}, "profile": {"title": "Profile", "personalInfo": "Personal Information", "tradingInfo": "Trading Information"}, "kyc": {"actions": {"approve": "Approve", "reject": "Reject"}}, "bulkActions": {"verifySelected": "Verify Selected", "suspendSelected": "Suspend Selected", "sendMessageToSelected": "Send Message to Selected"}}, "trades": {"title": "Trade Management", "subtitle": "Comprehensive system for monitoring and managing all trading activities", "activeTrades": "Active Trades", "completedTrades": "Completed Trades", "disputedTrades": "Disputed Trades", "totalTrades": "Total Trades", "totalVolume": "Total Volume", "successRate": "Success Rate", "actions": {"view": "View Details", "monitor": "Monitor", "resolve": "Resolve Dispute", "complete": "Complete", "pause": "Pause", "freeze": "Freeze", "exportData": "Export Data", "sendMessage": "Send Message"}, "filters": {"all": "All", "active": "Active", "completed": "Completed", "disputed": "Disputed", "cancelled": "Cancelled", "highValue": "High Value", "highRisk": "High Risk", "todayOnly": "Today Only"}, "details": {"tradeId": "Trade ID", "amount": "Amount", "rate": "Rate", "fee": "Fee", "total": "Total", "method": "Method", "duration": "Duration", "location": "Location", "notes": "Notes"}, "participants": {"buyer": "Buyer", "seller": "<PERSON><PERSON>"}, "timeline": {"created": "Created", "completed": "Completed"}, "analytics": {"title": "Trading Analytics"}}, "disputes": {"title": "Dispute Management", "subtitle": "Comprehensive system for managing and tracking trade disputes", "openDisputes": "Open Disputes", "resolvedDisputes": "Resolved Disputes", "pendingReview": "Pending Review", "escalatedDisputes": "Escalated Disputes", "totalDisputes": "Total Disputes", "disputesThisMonth": "Disputes This Month", "actions": {"review": "Review", "resolve": "Resolve", "escalate": "Escalate", "assign": "Assign", "viewDetails": "View Details", "sendMessage": "Send Message"}, "filters": {"all": "All", "open": "Open", "resolved": "Resolved", "escalated": "Escalated", "inProgress": "In Progress", "overdue": "Overdue", "highPriority": "High Priority"}, "priority": {"high": "High"}, "participants": {"complainant": "Complain<PERSON>", "respondent": "Respondent"}}, "notifications": {"title": "Notification Management", "subtitle": "Comprehensive system for managing notifications and alerts", "allNotifications": "All Notifications", "unreadNotifications": "Unread Notifications", "systemAlerts": "System Alerts", "tradeAlerts": "Trade Alerts", "disputeAlerts": "<PERSON><PERSON><PERSON>s", "securityAlerts": "Security Alerts", "actions": {"markAsRead": "<PERSON> <PERSON>", "markAllAsRead": "<PERSON> as <PERSON>", "deleteNotification": "Delete Notification", "viewDetails": "View Details", "dismiss": "<PERSON><PERSON><PERSON>", "acknowledge": "Acknowledge", "createAlert": "Create <PERSON><PERSON>", "editAlert": "<PERSON>", "testAlert": "Test Alert"}, "filters": {"all": "All", "unread": "Unread", "today": "Today", "critical": "Critical", "system": "System", "disputes": "Disputes", "trades": "Trading", "users": "Users", "security": "Security"}, "priority": {"high": "High Priority"}, "channels": {"email": "Email", "sms": "SMS", "push": "Push Notifications", "inApp": "In-App"}, "templates": {"disputeCreated": "New dispute created", "securityBreach": "Security breach"}, "settings": {"title": "Notification Settings", "preferences": "Preferences", "templates": "Templates"}, "stats": {"totalNotifications": "Total Notifications", "unreadCount": "Unread Count", "todayCount": "Today Count", "criticalCount": "Critical Count", "averageResponseTime": "Average Response Time", "acknowledgedRate": "Acknowledged Rate"}}, "contracts": {"title": "Smart Contract Management", "subtitle": "Comprehensive management of smart contracts and blockchain verification", "deployment": "Contract Deployment", "monitoring": "Contract Monitoring", "configuration": "Contract Configuration", "verification": "Contract Verification", "analysis": "Contract Analysis", "autoPopulation": "Auto Population", "contractAddress": "Contract Address", "contractAddressPlaceholder": "Enter smart contract address (0x...)", "selectNetwork": "Select Network", "verifyContract": "Verify Contract", "analyzeContract": "Analyze Contract", "saveToDatabase": "Save to Database", "contractInfo": "Contract Information", "contractType": "Contract Type", "securityAnalysis": "Security Analysis", "supportedTokens": "Supported <PERSON><PERSON>s", "contractStats": "Contract Statistics", "recommendations": "Recommendations", "status": {"active": "Active", "inactive": "Inactive", "upgrading": "Upgrading", "error": "Error", "deploying": "Deploying", "verified": "Verified", "unverified": "Unverified"}, "types": {"coreEscrow": "Core Escrow Contract", "reputationManager": "Reputation Manager", "oracleManager": "Oracle Manager", "adminManager": "Admin Manager", "escrowIntegrator": "Escrow Integrator", "erc20_token": "ERC20 Token", "nft": "Non-Fungible Token", "escrow": "Escrow Contract", "defi": "De<PERSON>i Contract", "proxy": "Proxy Contract", "unknown": "Unknown", "token": "Token Contract"}, "actions": {"deploy": "Deploy", "configure": "Configure", "monitor": "Monitor", "interact": "Interact", "verify": "Verify", "analyze": "Analyze", "save": "Save", "refresh": "Refresh", "view": "View", "edit": "Edit", "delete": "Delete"}, "fields": {"name": "Name", "address": "Address", "version": "Version", "network": "Network", "deployedAt": "Deployed At", "lastUpdated": "Last Updated", "gasUsed": "Gas Used", "transactionCount": "Transaction Count", "balance": "Balance", "verified": "Verified", "sourceCode": "Source Code", "abi": "Application Binary Interface", "compilerVersion": "Compiler Version", "optimization": "Optimization", "securityScore": "Security Score"}, "messages": {"verificationSuccess": "Contract verified successfully", "verificationFailed": "Contract verification failed", "analysisSuccess": "Contract analyzed successfully", "analysisFailed": "Contract analysis failed", "saveSuccess": "Data saved successfully", "saveFailed": "Failed to save data", "invalidAddress": "Invalid contract address", "contractNotFound": "Contract not found on blockchain", "networkNotSupported": "Network not supported", "loading": "Loading...", "verifying": "Verifying...", "analyzing": "Analyzing...", "saving": "Saving..."}, "security": {"verified": "Verified", "unverified": "Unverified", "hasProxy": "Has Proxy", "optimized": "Optimized", "securityIssues": "Security Issues", "securityScore": "Security Score", "lowSecurity": "Low Security", "mediumSecurity": "Medium Security", "highSecurity": "High Security"}}, "networks": {"supportedNetworks": "Supported Networks", "management": "Network Management", "addNetwork": "Add Network", "editNetwork": "Edit Network", "managementComingSoon": "Network management coming soon...", "noNetworks": "No networks found", "networkName": "Network Name", "networkSymbol": "Network Symbol", "chainId": "Chain ID", "rpcUrl": "RPC URL", "explorerUrl": "Explorer URL", "gasPrice": "Gas Price", "blockTime": "Block Time", "confirmationBlocks": "Confirmation Blocks", "isTestnet": "Is Testnet", "isActive": "Is Active", "addSuccess": "Network added successfully", "addFailed": "Failed to add network", "updateSuccess": "Network updated successfully", "updateFailed": "Failed to update network", "deleteSuccess": "Network deleted successfully", "deleteFailed": "Failed to delete network", "confirmDelete": "Are you sure you want to delete this network?", "title": "Network Management", "subtitle": "Comprehensive management of supported networks and tokens", "totalNetworks": "Total Networks", "totalTokens": "Total Tokens", "activeNetworks": "Active Networks", "activeTokens": "Active Tokens", "supportedTokens": "Supported <PERSON><PERSON>s", "properties": {"networkName": "Network Name", "chainId": "Chain ID", "rpcUrl": "RPC URL", "explorerUrl": "Explorer URL", "nativeCurrency": "Native Currency", "blockTime": "Block Time", "gasPrice": "Gas Price", "tokenAddress": "Token Address", "tokenSymbol": "Token Symbol", "tokenName": "Token Name", "decimals": "Decimals", "totalSupply": "Total Supply", "marketCap": "Market Cap", "price": "Price", "volume24h": "24h Volume", "name": "Name"}, "monitoring": {"title": "Network Monitoring", "uptime": "Uptime", "latency": "Latency", "blockHeight": "Block Height", "transactions": "Transactions", "nodeCount": "Node Count"}, "configuration": {"general": "General"}, "status": {"active": "Active", "inactive": "Inactive", "maintenance": "Maintenance", "error": "Error", "syncing": "Syncing", "pending": "Pending"}, "actions": {"testConnection": "Test Connection", "syncData": "Sync Data", "enableNetwork": "Enable Network", "disableNetwork": "Disable Network", "enableToken": "Enable Token", "disableToken": "Disable Token", "editToken": "<PERSON>", "viewDetails": "View Details", "configure": "Configure"}, "filters": {"all": "All", "active": "Active", "inactive": "Inactive", "mainnet": "Mainnet", "testnet": "Testnet", "native": "Native", "erc20": "ERC20", "highVolume": "High Volume"}}, "tokens": {"management": "Token Management", "addToken": "Add <PERSON>", "editToken": "<PERSON>", "managementComingSoon": "Token management coming soon...", "noTokens": "No tokens found", "selectNetwork": "Select Network", "contractAddress": "Contract Address", "tokenSymbol": "Token Symbol", "tokenName": "Token Name", "decimals": "Decimals", "minAmount": "<PERSON>", "maxAmount": "<PERSON>", "platformFee": "Platform Fee", "isStablecoin": "Is Stablecoin", "isActive": "Is Active", "addSuccess": "<PERSON><PERSON> added successfully", "addFailed": "Failed to add token", "updateSuccess": "Token updated successfully", "updateFailed": "Failed to update token", "deleteSuccess": "Token deleted successfully", "deleteFailed": "Failed to delete token", "confirmDelete": "Are you sure you want to delete this token?", "contractValidated": "Contract validated successfully", "contractValidationFailed": "Contract validation failed", "addressAndNetworkRequired": "Contract address and network are required"}, "common": {"loading": "Loading...", "noData": "No data available", "all": "All", "active": "Active", "inactive": "Inactive", "free": "Free", "popular": "Popular", "unlimited": "Unlimited", "month": "Month", "user": "User", "users": "Users", "offers": "Offers", "payment": "Payment", "revenue": "Revenue", "usage": "Usage", "selected": "Selected", "results": "Results", "overview": "Overview", "thisMonth": "This Month", "labels": {"available": "Available", "status": "Status", "total": "Total", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "date": "Date", "time": "Time", "amount": "Amount", "type": "Type", "description": "Description", "id": "ID", "created": "Created", "updated": "Updated", "active": "Active", "verified": "Verified", "balance": "Balance", "rating": "Rating", "transactions": "Transactions", "information": "Information"}, "actions": {"add": "Add", "edit": "Edit", "delete": "Delete", "view": "View", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "refresh": "Refresh", "search": "Search", "filter": "Filter", "export": "Export", "settings": "Settings", "close": "Close", "ok": "OK", "actions": "Actions", "title": "Title", "selected": "Selected", "clear": "Clear", "update": "Update", "saving": "Saving..."}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled", "suspended": "Suspended", "loading": "Loading..."}, "messages": {"noData": "No data available"}}, "monitoring": {"title": "System Monitoring and Health", "subtitle": "Comprehensive monitoring of system performance and services", "systemHealth": "System Health", "overview": "Overview", "resources": "Resources", "services": "Services", "logs": "Logs", "alerts": "<PERSON><PERSON><PERSON>", "diagnostics": "Diagnostics", "metrics": {"uptime": "Uptime", "responseTime": "Response Time", "gasUsed": "Gas Used"}, "actions": {"refresh": "Refresh", "diagnose": "Diagnose", "restart": "<PERSON><PERSON>", "stop": "Stop"}}, "analytics": {"title": "Analytics and Reports", "subtitle": "Comprehensive analysis of platform and user performance", "realTimeData": "Real-time Data", "comparison": "Comparisons", "insights": "Analytical Insights", "charts": {"tradingVolume": "Trading Volume", "userGrowth": "User Growth", "revenueAnalysis": "Revenue Analysis", "networkActivity": "Network Activity", "conversionRates": "Conversion Rates", "userRetention": "User Retention", "geographicDistribution": "Geographic Distribution"}, "periods": {"yesterday": "Yesterday", "last30Days": "Last 30 Days"}, "filters": {"dateRange": "Date Range"}}, "settings": {"title": "System Settings", "subtitle": "Comprehensive configuration of platform and system settings", "categories": {"platformSettings": "Platform Settings", "securitySettings": "Security Settings", "feeManagement": "Fee Management", "apiConfiguration": "API Configuration", "blockchainSettings": "Blockchain Settings", "backupSettings": "Backup Settings", "maintenanceMode": "Maintenance Mode"}, "platform": {"siteName": "Site Name", "siteDescription": "Site Description", "defaultLanguage": "Default Language", "defaultCurrency": "<PERSON><PERSON><PERSON>", "registrationEnabled": "Registration Enabled", "kycRequired": "KYC Required", "maxUsersPerDay": "Max Users Per Day", "sessionTimeout": "Session Timeout", "maxLoginAttempts": "<PERSON> Login Attempts"}, "security": {"twoFactorAuth": "Two-Factor Authentication"}, "fees": {"tradingFee": "Trading Fee", "withdrawalFee": "<PERSON><PERSON><PERSON>", "minimumTradeAmount": "Minimum Trade Amount", "maximumTradeAmount": "Maximum Trade Amount"}, "api": {"apiKey": "API Key", "rateLimits": "Rate Limits", "webhookUrls": "Webhook URLs"}, "blockchain": {"confirmations": "Confirmations"}, "status": {"enabled": "Enabled", "disabled": "Disabled"}, "actions": {"save": "Save", "test": "Test", "reset": "Reset", "backup": "Backup", "restore": "Rest<PERSON>", "export": "Export"}}, "subscriptions": {"title": "Subscription Management", "subtitle": "Comprehensive system for managing packages and paid subscriptions", "plans": {"title": "Subscription Plans", "subtitle": "Manage subscription plans and pricing", "free": {"features": {"offers": "3 offers per month", "support": "Basic support", "commission": "0.3% commission"}}, "basic": {"features": {"analytics": "Basic analytics"}}, "pro": {"features": {"api": "API access"}}, "enterprise": {"features": {"whitelabel": "Custom branding"}}}, "management": {"title": "Subscription Management", "activeSubscriptions": "Active Subscriptions", "createPlan": "Create Plan", "editPlan": "Edit Plan"}, "payments": {"title": "Payment Management", "methods": "Payment Methods", "transactions": "Transactions", "gateways": {"stripe": "Stripe", "paypal": "PayPal", "crypto": "Cryptocurrencies", "bank": "Bank Transfer"}, "status": {"active": "Active", "pending": "Pending", "cancelled": "Cancelled", "expired": "Expired"}}, "verification": {"title": "Verification Requirements", "subtitle": "Verification settings for free plan", "requirements": {"title": "Verification Requirements", "email": "Email verification", "password": "Password security", "wallet": "Wallet verification"}, "emailVerification": "Email Verification", "passwordSecurity": "Password Security", "walletVerification": "Wallet Verification", "antiSpam": {"title": "Anti-Spam System", "duplicateDetection": "Duplicate Detection", "ipTracking": "IP Tracking", "deviceFingerprint": "Device Fingerprint", "behaviorAnalysis": "Behavior Analysis", "riskScoring": "Risk Scoring"}}, "analytics": {"title": "Subscription Analytics", "revenue": {"title": "Revenue Analysis", "monthly": "Monthly Revenue", "yearly": "Yearly Revenue", "byPlan": "Revenue by Plan"}, "users": {"title": "User Analysis", "conversion": "Conversion Rate", "churn": "Churn Rate"}, "performance": {"title": "Plan Performance"}}, "settings": {"title": "Subscription Settings", "pricing": "Pricing", "features": "Features", "notifications": "Notifications"}, "stats": {"totalRevenue": "Total Revenue", "activeSubscriptions": "Active Subscriptions", "freeUsers": "Free Users", "paidUsers": "Paid Users", "conversionRate": "Conversion Rate", "churnRate": "Churn Rate", "averageRevenue": "Average Revenue"}, "actions": {"upgrade": "Upgrade"}}, "testing": {"title": "Testing and Simulation Tools", "subtitle": "Advanced tools for testing and simulating the system", "categories": {"tradeSimulation": "Trade Simulation", "networkTesting": "Network Testing", "contractTesting": "Contract Testing", "loadTesting": "Load Testing", "securityTesting": "Security Testing", "performanceTesting": "Performance Testing", "apiTesting": "API Testing", "systemValidation": "System Validation"}, "actions": {"runTest": "Run Test", "pauseTest": "Pause Test", "exportReport": "Export Report"}}, "auth": {"title": "Authentication and Security", "subtitle": "Comprehensive management of system security and advanced authentication", "twoFactor": {"title": "Two-Factor Authentication", "subtitle": "Enhanced security with 2FA", "setup": "Setup 2FA", "enable": "Enable 2FA", "disable": "Disable 2FA", "appInstructions": "Scan QR code with authenticator app", "backupCodes": "Backup Codes", "generateBackupCodes": "Generate Backup Codes", "downloadBackupCodes": "Download Backup Codes"}, "security": {"title": "Security Settings", "passwordPolicy": {"title": "Password Policy", "minLength": "Minimum Length", "requireUppercase": "Require Uppercase", "requireLowercase": "Require Lowercase", "requireNumbers": "Require Numbers", "requireSymbols": "Require Symbols"}, "loginSecurity": {"title": "Login Security", "maxAttempts": "<PERSON> Login Attempts", "lockoutDuration": "Lockout Duration", "sessionTimeout": "Session Timeout", "requireTwoFactor": "Require Two-Factor"}}, "sessions": {"title": "Active Sessions", "activeSessions": "Active Sessions", "terminateAllSessions": "Terminate All Sessions"}, "logs": {"title": "Security Logs", "action": "Action", "details": "Details", "timestamp": "Timestamp", "failedLogins": "Failed Login Attempts", "logDetails": "Log Details", "exportSecurityReport": "Export Security Report"}, "alerts": {"title": "Security Alerts", "subtitle": "Monitor and respond to security events", "actions": {"dismiss": "<PERSON><PERSON><PERSON>", "investigate": "Investigate", "resolve": "Resolve"}}, "roles": {"title": "Role Management", "createRole": "Create Role", "editRole": "Edit Role"}}, "adminDashboard": {"connectWallet": "Connect Wallet", "walletNotConnected": "Wallet Not Connected", "walletNotConnectedDesc": "Please connect your wallet to access admin functions", "stats": {"totalUsers": "Total Users", "activeUsers": "Active Users", "totalTrades": "Total Trades", "activeTrades": "Active Trades", "pendingDisputes": "Pending Disputes", "monthlyFees": "Monthly Fees"}, "status": {"active": "Active", "suspended": "Suspended", "pending": "Pending"}, "messages": {"walletConnectFailed": "Failed to connect wallet", "seller": "<PERSON><PERSON>", "buyer": "Buyer", "resolvingDispute": "Resolving dispute...", "disputeResolveError": "Error resolving dispute", "tradeActionSuccess": "Action executed successfully"}, "actions": {"complete": "Complete", "cancel": "Cancel"}, "trading": {"title": "Trading Interface"}}, "comments": {"authCheck": "Authorization check error"}, "admin": {"messages": {"loginSuccess": "Login successful"}}, "validation": {"required": "This field is required"}, "a": "A"}