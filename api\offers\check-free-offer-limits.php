<?php
/**
 * التحقق من حدود العروض المجانية
 * Check Free Offer Limits API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: http://localhost:3000');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../../config/database.php';

// تجاهل المصادقة للاختبار
$userId = $_GET['user_id'] ?? 1;

$method = $_SERVER['REQUEST_METHOD'];

try {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->connect();
    
    if ($method === 'GET') {
        checkFreeOfferLimits($connection, $userId);
    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    error_log("Check free offer limits API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'خطأ في الخادم: ' . $e->getMessage()]);
}

/**
 * إرسال استجابة نجاح
 */
function sendSuccessResponse($data, $message = '') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data
    ]);
}

/**
 * إرسال استجابة خطأ
 */
function sendErrorResponse($message, $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'error' => $message
    ]);
}

/**
 * التحقق من حدود العروض المجانية للمستخدم
 */
function checkFreeOfferLimits($connection, $userId) {
    try {
        // تحديد اللغة
        $lang = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? 'ar';
        $isArabic = strpos($lang, 'ar') !== false;

        // رسائل الترجمة
        $messages = [
            'ar' => [
                'free_offers_disabled' => 'العروض المجانية غير مفعلة حالياً',
                'free_offers_not_available' => 'العروض المجانية غير متاحة',
                'verification_required' => 'يجب التحقق من الهوية أولاً لإنشاء عروض مجانية',
                'verification_needed' => 'التحقق من الهوية مطلوب',
                'limit_reached' => 'لقد وصلت للحد الأقصى من العروض المجانية',
                'max_limit_reached' => 'تم الوصول للحد الأقصى',
                'monthly_limit_reached' => 'تم الوصول للحد الشهري للعروض المجانية',
                'cooldown_required' => 'يجب الانتظار {minutes} دقيقة قبل إنشاء عرض مجاني آخر',
                'cooldown_period' => 'فترة انتظار مطلوبة',
                'can_create_offer' => 'يمكن إنشاء عرض مجاني'
            ],
            'en' => [
                'free_offers_disabled' => 'Free offers are currently disabled',
                'free_offers_not_available' => 'Free offers are not available',
                'verification_required' => 'Identity verification required to create free offers',
                'verification_needed' => 'Verification required',
                'limit_reached' => 'You have reached the maximum number of free offers',
                'max_limit_reached' => 'Maximum limit reached',
                'monthly_limit_reached' => 'Monthly free offer limit reached',
                'cooldown_required' => 'Please wait {minutes} minutes before creating another free offer',
                'cooldown_period' => 'Cooldown period required',
                'can_create_offer' => 'Can create free offer'
            ]
        ];

        $msg = $messages[$isArabic ? 'ar' : 'en'];

        // جلب إعدادات العروض المجانية
        $settings = getFreeOffersSettings($connection);

        if (!$settings['freeOffersEnabled']['value']) {
            sendSuccessResponse([
                'canCreateFreeOffer' => false,
                'reason' => $msg['free_offers_disabled'],
                'limits' => $settings
            ], $msg['free_offers_not_available']);
            return;
        }
        
        // التحقق من التحقق من الهوية إذا كان مطلوباً
        if ($settings['freeOfferRequireVerification']['value']) {
            $stmt = $connection->prepare("SELECT is_verified FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $userInfo = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$userInfo || !$userInfo['is_verified']) {
                sendSuccessResponse([
                    'canCreateFreeOffer' => false,
                    'reason' => $msg['verification_required'],
                    'requiresVerification' => true,
                    'limits' => $settings
                ], $msg['verification_needed']);
                return;
            }
        }
        
        // التحقق من الحدود الشهرية للعروض المجانية
        $currentMonth = date('Y-m');

        // جلب أو إنشاء سجل الاستخدام الشهري
        $monthlyUsage = getOrCreateMonthlyUsage($connection, $userId, $currentMonth);

        // جلب خطة المستخدم الحالية
        $userPlan = getUserCurrentPlan($connection, $userId);
        $maxAllowed = $userPlan['monthly_free_offers'] ?? $settings['maxFreeOffersPerUser']['value'];

        if ($monthlyUsage['free_offers_used'] >= $maxAllowed) {
            sendSuccessResponse([
                'canCreateFreeOffer' => false,
                'reason' => $msg['monthly_limit_reached'] . " ({$maxAllowed})",
                'currentCount' => $monthlyUsage['free_offers_used'],
                'maxAllowed' => $maxAllowed,
                'monthlyUsage' => $monthlyUsage,
                'userPlan' => $userPlan,
                'limits' => $settings,
                'resetDate' => date('Y-m-01', strtotime('+1 month'))
            ], $msg['monthly_limit_reached']);
            return;
        }
        
        // التحقق من فترة الانتظار
        $cooldownPeriod = $settings['freeOfferCooldownPeriod']['value'];
        if ($cooldownPeriod > 0) {
            $stmt = $connection->prepare("
                SELECT created_at 
                FROM offers 
                WHERE user_id = ? AND is_free = 1 
                ORDER BY created_at DESC 
                LIMIT 1
            ");
            $stmt->execute([$userId]);
            $lastOffer = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($lastOffer) {
                $lastOfferTime = strtotime($lastOffer['created_at']);
                $cooldownEnd = $lastOfferTime + $cooldownPeriod;
                $now = time();
                
                if ($now < $cooldownEnd) {
                    $remainingTime = $cooldownEnd - $now;
                    $remainingMinutes = ceil($remainingTime / 60);

                    $cooldownMessage = str_replace('{minutes}', $remainingMinutes, $msg['cooldown_required']);

                    sendSuccessResponse([
                        'canCreateFreeOffer' => false,
                        'reason' => $cooldownMessage,
                        'remainingCooldown' => $remainingTime,
                        'cooldownEndTime' => date('Y-m-d H:i:s', $cooldownEnd),
                        'limits' => $settings
                    ], $msg['cooldown_period']);
                    return;
                }
            }
        }
        
        // جلب العروض المجانية المنتهية الصلاحية للتنظيف
        cleanupExpiredFreeOffers($connection, $userId);
        
        // المستخدم يمكنه إنشاء عرض مجاني
        sendSuccessResponse([
            'canCreateFreeOffer' => true,
            'currentCount' => $monthlyUsage['free_offers_used'],
            'maxAllowed' => $maxAllowed,
            'remainingSlots' => $maxAllowed - $monthlyUsage['free_offers_used'],
            'monthlyUsage' => $monthlyUsage,
            'userPlan' => $userPlan,
            'limits' => $settings,
            'maxAmount' => $settings['maxFreeOfferAmount']['value'],
            'minAmount' => $settings['freeOfferMinAmount']['value'],
            'timeLimit' => $settings['freeOfferTimeLimit']['value'],
            'resetDate' => date('Y-m-01', strtotime('+1 month'))
        ], $msg['can_create_offer']);
        
    } catch (Exception $e) {
        throw new Exception('خطأ في التحقق من حدود العروض المجانية: ' . $e->getMessage());
    }
}

/**
 * جلب إعدادات العروض المجانية
 */
function getFreeOffersSettings($connection) {
    try {
        $stmt = $connection->prepare("
            SELECT setting_key, setting_value, setting_type 
            FROM platform_settings 
            WHERE setting_key IN (
                'freeOffersEnabled', 'maxFreeOffersPerUser', 'maxFreeOfferAmount',
                'freeOfferTimeLimit', 'freeOfferRequireVerification', 
                'freeOfferMinAmount', 'freeOfferCooldownPeriod'
            )
        ");
        $stmt->execute();
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $formattedSettings = [];
        foreach ($settings as $setting) {
            $value = $setting['setting_value'];
            
            switch ($setting['setting_type']) {
                case 'boolean':
                    $value = (bool)$value;
                    break;
                case 'integer':
                    $value = (int)$value;
                    break;
                case 'float':
                    $value = (float)$value;
                    break;
            }
            
            $formattedSettings[$setting['setting_key']] = [
                'value' => $value,
                'type' => $setting['setting_type']
            ];
        }
        
        // إضافة الإعدادات الافتراضية إذا لم تكن موجودة
        $defaults = [
            'freeOffersEnabled' => ['value' => true, 'type' => 'boolean'],
            'maxFreeOffersPerUser' => ['value' => 3, 'type' => 'integer'],
            'maxFreeOfferAmount' => ['value' => 1000, 'type' => 'integer'],
            'freeOfferTimeLimit' => ['value' => 86400, 'type' => 'integer'],
            'freeOfferRequireVerification' => ['value' => false, 'type' => 'boolean'],
            'freeOfferMinAmount' => ['value' => 10, 'type' => 'integer'],
            'freeOfferCooldownPeriod' => ['value' => 3600, 'type' => 'integer']
        ];
        
        foreach ($defaults as $key => $default) {
            if (!isset($formattedSettings[$key])) {
                $formattedSettings[$key] = $default;
            }
        }
        
        return $formattedSettings;
        
    } catch (Exception $e) {
        throw new Exception('خطأ في جلب إعدادات العروض المجانية: ' . $e->getMessage());
    }
}

/**
 * تنظيف العروض المجانية المنتهية الصلاحية
 */
function cleanupExpiredFreeOffers($connection, $userId) {
    try {
        // تعطيل العروض المجانية المنتهية الصلاحية
        $stmt = $connection->prepare("
            UPDATE user_free_offers 
            SET is_active = 0 
            WHERE user_id = ? AND expires_at < NOW() AND is_active = 1
        ");
        $stmt->execute([$userId]);
        
        // تعطيل العروض المرتبطة
        $stmt = $connection->prepare("
            UPDATE offers o
            INNER JOIN user_free_offers ufo ON o.id = ufo.offer_id
            SET o.is_active = 0
            WHERE ufo.user_id = ? AND ufo.expires_at < NOW() AND ufo.is_active = 0 AND o.is_free = 1
        ");
        $stmt->execute([$userId]);
        
    } catch (Exception $e) {
        logError("Error cleaning up expired free offers: " . $e->getMessage());
    }
}

/**
 * جلب أو إنشاء سجل الاستخدام الشهري
 */
function getOrCreateMonthlyUsage($connection, $userId, $yearMonth) {
    try {
        // محاولة جلب السجل الموجود
        $stmt = $connection->prepare("
            SELECT * FROM user_monthly_limits
            WHERE user_id = ? AND month_year = ?
        ");
        $stmt->execute([$userId, $yearMonth]);
        $usage = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($usage) {
            return $usage;
        }

        // إنشاء سجل جديد إذا لم يكن موجوداً
        $stmt = $connection->prepare("
            INSERT INTO user_monthly_limits (user_id, month_year, free_offers_used, subscription_plan_id)
            VALUES (?, ?, 0, ?)
        ");

        // جلب خطة المستخدم الحالية
        $userPlan = getUserCurrentPlan($connection, $userId);
        $planId = $userPlan ? $userPlan['id'] : null;

        $stmt->execute([$userId, $yearMonth, $planId]);

        // إرجاع السجل الجديد
        return [
            'id' => $connection->lastInsertId(),
            'user_id' => $userId,
            'month_year' => $yearMonth,
            'free_offers_used' => 0,
            'subscription_plan_id' => $planId,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

    } catch (Exception $e) {
        logError("Error getting/creating monthly usage: " . $e->getMessage());
        return [
            'user_id' => $userId,
            'month_year' => $yearMonth,
            'free_offers_used' => 0,
            'subscription_plan_id' => null
        ];
    }
}

/**
 * جلب خطة المستخدم الحالية
 */
function getUserCurrentPlan($connection, $userId) {
    try {
        $stmt = $connection->prepare("
            SELECT sp.*, us.status, us.expires_at
            FROM subscription_plans sp
            INNER JOIN user_subscriptions us ON sp.id = us.plan_id
            WHERE us.user_id = ? AND us.status = 'active' AND us.expires_at > NOW()
            ORDER BY us.created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$userId]);
        $plan = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($plan) {
            return $plan;
        }

        // إرجاع الخطة الافتراضية (المجانية)
        $stmt = $connection->prepare("
            SELECT * FROM subscription_plans
            WHERE is_default = 1 AND is_active = 1
            LIMIT 1
        ");
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        logError("Error getting user current plan: " . $e->getMessage());
        return null;
    }
}

/**
 * تحديث عداد الاستخدام الشهري
 */
function incrementMonthlyUsage($connection, $userId, $yearMonth = null) {
    try {
        if (!$yearMonth) {
            $yearMonth = date('Y-m');
        }

        $stmt = $connection->prepare("
            UPDATE user_monthly_limits
            SET free_offers_used = free_offers_used + 1, updated_at = NOW()
            WHERE user_id = ? AND month_year = ?
        ");
        $stmt->execute([$userId, $yearMonth]);

        // إذا لم يتم تحديث أي سجل، أنشئ سجل جديد
        if ($stmt->rowCount() === 0) {
            getOrCreateMonthlyUsage($connection, $userId, $yearMonth);
            incrementMonthlyUsage($connection, $userId, $yearMonth);
        }

        return true;

    } catch (Exception $e) {
        logError("Error incrementing monthly usage: " . $e->getMessage());
        return false;
    }
}

/**
 * تسجيل الأخطاء
 */
function logError($message) {
    error_log("[" . date('Y-m-d H:i:s') . "] Check Free Offer Limits API: " . $message);
}
?>
