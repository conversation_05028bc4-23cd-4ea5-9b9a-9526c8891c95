<?php
/**
 * نظام تحديد معدل الطلبات (Rate Limiting)
 * Rate Limiting System
 */

class RateLimiter {
    private $connection;
    private $defaultLimits = [
        'login' => ['requests' => 5, 'window' => 900], // 5 محاولات كل 15 دقيقة
        'register' => ['requests' => 3, 'window' => 3600], // 3 محاولات كل ساعة
        'api_general' => ['requests' => 100, 'window' => 3600], // 100 طلب كل ساعة
        'api_heavy' => ['requests' => 20, 'window' => 3600], // 20 طلب كل ساعة للعمليات الثقيلة
        'password_reset' => ['requests' => 3, 'window' => 3600], // 3 محاولات كل ساعة
        'trade_creation' => ['requests' => 10, 'window' => 600], // 10 صفقات كل 10 دقائق
        'message_send' => ['requests' => 30, 'window' => 300] // 30 رسالة كل 5 دقائق
    ];
    
    public function __construct($connection) {
        $this->connection = $connection;
        $this->createRateLimitTable();
    }
    
    /**
     * إنشاء جدول تتبع معدل الطلبات
     */
    private function createRateLimitTable() {
        try {
            $this->connection->exec("
                CREATE TABLE IF NOT EXISTS rate_limits (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    identifier VARCHAR(255) NOT NULL,
                    action_type VARCHAR(50) NOT NULL,
                    request_count INT DEFAULT 1,
                    window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_request TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    is_blocked BOOLEAN DEFAULT FALSE,
                    block_until TIMESTAMP NULL,
                    
                    INDEX idx_identifier_action (identifier, action_type),
                    INDEX idx_window_start (window_start),
                    INDEX idx_block_until (block_until)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
        } catch (PDOException $e) {
            error_log('Error creating rate_limits table: ' . $e->getMessage());
        }
    }
    
    /**
     * فحص معدل الطلبات
     */
    public function checkLimit($identifier, $actionType = 'api_general', $customLimits = null) {
        $limits = $customLimits ?? $this->defaultLimits[$actionType] ?? $this->defaultLimits['api_general'];
        $maxRequests = $limits['requests'];
        $windowSeconds = $limits['window'];
        
        // تنظيف السجلات القديمة
        $this->cleanupOldRecords();
        
        // فحص إذا كان المستخدم محظور
        if ($this->isBlocked($identifier, $actionType)) {
            throw new Exception('تم حظرك مؤقتاً بسبب تجاوز حد الطلبات المسموح. حاول مرة أخرى لاحقاً.');
        }
        
        $windowStart = date('Y-m-d H:i:s', time() - $windowSeconds);
        
        // البحث عن سجل موجود في النافزة الزمنية الحالية
        $stmt = $this->connection->prepare("
            SELECT id, request_count, window_start 
            FROM rate_limits 
            WHERE identifier = ? AND action_type = ? AND window_start >= ?
            ORDER BY window_start DESC 
            LIMIT 1
        ");
        
        $stmt->execute([$identifier, $actionType, $windowStart]);
        $record = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($record) {
            // تحديث السجل الموجود
            $newCount = $record['request_count'] + 1;
            
            if ($newCount > $maxRequests) {
                // تجاوز الحد المسموح
                $this->blockUser($identifier, $actionType, $windowSeconds);
                throw new Exception("تجاوزت الحد المسموح من الطلبات ($maxRequests طلب كل " . ($windowSeconds/60) . " دقيقة). حاول مرة أخرى لاحقاً.");
            }
            
            $updateStmt = $this->connection->prepare("
                UPDATE rate_limits 
                SET request_count = ?, last_request = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");
            $updateStmt->execute([$newCount, $record['id']]);
            
        } else {
            // إنشاء سجل جديد
            $insertStmt = $this->connection->prepare("
                INSERT INTO rate_limits (identifier, action_type, request_count, window_start) 
                VALUES (?, ?, 1, CURRENT_TIMESTAMP)
            ");
            $insertStmt->execute([$identifier, $actionType]);
        }
        
        return true;
    }
    
    /**
     * فحص إذا كان المستخدم محظور
     */
    private function isBlocked($identifier, $actionType) {
        $stmt = $this->connection->prepare("
            SELECT block_until 
            FROM rate_limits 
            WHERE identifier = ? AND action_type = ? AND is_blocked = 1 AND block_until > CURRENT_TIMESTAMP
        ");
        
        $stmt->execute([$identifier, $actionType]);
        return $stmt->fetch() !== false;
    }
    
    /**
     * حظر المستخدم مؤقتاً
     */
    private function blockUser($identifier, $actionType, $blockDuration) {
        $blockUntil = date('Y-m-d H:i:s', time() + $blockDuration * 2); // ضعف مدة النافزة
        
        $stmt = $this->connection->prepare("
            UPDATE rate_limits 
            SET is_blocked = 1, block_until = ? 
            WHERE identifier = ? AND action_type = ?
        ");
        
        $stmt->execute([$blockUntil, $identifier, $actionType]);
        
        // تسجيل محاولة الحظر في سجلات الأمان
        $this->logSecurityEvent($identifier, $actionType, 'rate_limit_exceeded');
    }
    
    /**
     * تنظيف السجلات القديمة
     */
    private function cleanupOldRecords() {
        // حذف السجلات الأقدم من 24 ساعة
        $stmt = $this->connection->prepare("
            DELETE FROM rate_limits 
            WHERE window_start < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 24 HOUR)
        ");
        $stmt->execute();
        
        // إلغاء حظر المستخدمين المنتهية مدة حظرهم
        $unblockStmt = $this->connection->prepare("
            UPDATE rate_limits 
            SET is_blocked = 0, block_until = NULL 
            WHERE is_blocked = 1 AND block_until <= CURRENT_TIMESTAMP
        ");
        $unblockStmt->execute();
    }
    
    /**
     * الحصول على معلومات الحد الحالي للمستخدم
     */
    public function getLimitInfo($identifier, $actionType = 'api_general') {
        $limits = $this->defaultLimits[$actionType] ?? $this->defaultLimits['api_general'];
        $windowStart = date('Y-m-d H:i:s', time() - $limits['window']);
        
        $stmt = $this->connection->prepare("
            SELECT request_count, window_start, is_blocked, block_until
            FROM rate_limits 
            WHERE identifier = ? AND action_type = ? AND window_start >= ?
            ORDER BY window_start DESC 
            LIMIT 1
        ");
        
        $stmt->execute([$identifier, $actionType, $windowStart]);
        $record = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return [
            'current_requests' => $record['request_count'] ?? 0,
            'max_requests' => $limits['requests'],
            'window_seconds' => $limits['window'],
            'remaining_requests' => max(0, $limits['requests'] - ($record['request_count'] ?? 0)),
            'is_blocked' => (bool)($record['is_blocked'] ?? false),
            'block_until' => $record['block_until'] ?? null,
            'reset_time' => $record ? date('Y-m-d H:i:s', strtotime($record['window_start']) + $limits['window']) : null
        ];
    }
    
    /**
     * تسجيل حدث أمني
     */
    private function logSecurityEvent($identifier, $actionType, $eventType) {
        try {
            // محاولة استخراج user_id من identifier إذا كان رقماً
            $userId = is_numeric($identifier) ? $identifier : null;
            
            if ($userId) {
                $stmt = $this->connection->prepare("
                    INSERT INTO security_logs (
                        user_id, log_type, action_description, ip_address, 
                        user_agent, risk_level, additional_data, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ");
                
                $stmt->execute([
                    $userId,
                    'rate_limit_exceeded',
                    "تجاوز حد الطلبات المسموح للعملية: $actionType",
                    $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                    $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                    8, // مستوى مخاطر عالي
                    json_encode(['action_type' => $actionType, 'identifier' => $identifier])
                ]);
            }
        } catch (Exception $e) {
            error_log('Error logging security event: ' . $e->getMessage());
        }
    }
    
    /**
     * إعادة تعيين حدود مستخدم معين (للمدراء)
     */
    public function resetUserLimits($identifier, $actionType = null) {
        if ($actionType) {
            $stmt = $this->connection->prepare("
                DELETE FROM rate_limits 
                WHERE identifier = ? AND action_type = ?
            ");
            $stmt->execute([$identifier, $actionType]);
        } else {
            $stmt = $this->connection->prepare("
                DELETE FROM rate_limits 
                WHERE identifier = ?
            ");
            $stmt->execute([$identifier]);
        }
        
        return true;
    }
    
    /**
     * الحصول على إحصائيات Rate Limiting
     */
    public function getStats($hours = 24) {
        $stmt = $this->connection->prepare("
            SELECT 
                action_type,
                COUNT(*) as total_requests,
                COUNT(DISTINCT identifier) as unique_users,
                SUM(CASE WHEN is_blocked = 1 THEN 1 ELSE 0 END) as blocked_requests,
                AVG(request_count) as avg_requests_per_user
            FROM rate_limits 
            WHERE window_start >= DATE_SUB(CURRENT_TIMESTAMP, INTERVAL ? HOUR)
            GROUP BY action_type
            ORDER BY total_requests DESC
        ");
        
        $stmt->execute([$hours]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}

/**
 * دالة مساعدة لاستخدام Rate Limiter
 */
function checkRateLimit($connection, $identifier, $actionType = 'api_general', $customLimits = null) {
    $rateLimiter = new RateLimiter($connection);
    return $rateLimiter->checkLimit($identifier, $actionType, $customLimits);
}

/**
 * دالة للحصول على معرف فريد للمستخدم
 */
function getRateLimitIdentifier($userId = null) {
    if ($userId) {
        return "user_$userId";
    }
    
    // استخدام IP address كبديل
    $ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    return "ip_$ip";
}
?>
