<?php
/**
 * مساعد الاستجابات الموحدة
 * Unified Response Helper
 */

class ResponseHelper {
    
    /**
     * إرسال استجابة نجاح موحدة
     * Send unified success response
     */
    public static function success($data = null, $message = null, $messageEn = null, $meta = null) {
        $response = [
            'success' => true,
            'timestamp' => date('Y-m-d H:i:s'),
            'status_code' => http_response_code() ?: 200
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        if ($message !== null) {
            $response['message'] = $message;
        }
        
        if ($messageEn !== null) {
            $response['message_en'] = $messageEn;
        }
        
        if ($meta !== null) {
            $response['meta'] = $meta;
        }
        
        self::sendJson($response);
    }
    
    /**
     * إرسال استجابة خطأ موحدة
     * Send unified error response
     */
    public static function error($error, $errorEn = null, $code = 400, $details = null) {
        http_response_code($code);
        
        $response = [
            'success' => false,
            'error' => $error,
            'timestamp' => date('Y-m-d H:i:s'),
            'status_code' => $code
        ];
        
        if ($errorEn !== null) {
            $response['error_en'] = $errorEn;
        }
        
        if ($details !== null) {
            $response['details'] = $details;
        }
        
        self::sendJson($response);
    }
    
    /**
     * إرسال استجابة مع بيانات مقسمة (pagination)
     * Send paginated response
     */
    public static function paginated($data, $pagination, $message = null, $messageEn = null) {
        $response = [
            'success' => true,
            'data' => $data,
            'pagination' => [
                'current_page' => (int)$pagination['current_page'],
                'per_page' => (int)$pagination['per_page'],
                'total' => (int)$pagination['total'],
                'total_pages' => (int)ceil($pagination['total'] / $pagination['per_page']),
                'has_next' => $pagination['current_page'] < ceil($pagination['total'] / $pagination['per_page']),
                'has_prev' => $pagination['current_page'] > 1
            ],
            'timestamp' => date('Y-m-d H:i:s'),
            'status_code' => http_response_code() ?: 200
        ];
        
        if ($message !== null) {
            $response['message'] = $message;
        }
        
        if ($messageEn !== null) {
            $response['message_en'] = $messageEn;
        }
        
        self::sendJson($response);
    }
    
    /**
     * إرسال استجابة مصادقة
     * Send authentication response
     */
    public static function auth($user, $token = null, $expiresAt = null, $permissions = null) {
        $response = [
            'success' => true,
            'user' => $user,
            'timestamp' => date('Y-m-d H:i:s'),
            'status_code' => 200
        ];
        
        if ($token !== null) {
            $response['token'] = $token;
        }
        
        if ($expiresAt !== null) {
            $response['expires_at'] = $expiresAt;
        }
        
        if ($permissions !== null) {
            $response['permissions'] = $permissions;
        }
        
        self::sendJson($response);
    }
    
    /**
     * إرسال استجابة مصادقة إدارية
     * Send admin authentication response
     */
    public static function adminAuth($admin, $token = null, $expiresAt = null, $permissions = null) {
        $response = [
            'success' => true,
            'admin' => $admin,
            'timestamp' => date('Y-m-d H:i:s'),
            'status_code' => 200
        ];
        
        if ($token !== null) {
            $response['token'] = $token;
        }
        
        if ($expiresAt !== null) {
            $response['expires_at'] = $expiresAt;
        }
        
        if ($permissions !== null) {
            $response['permissions'] = $permissions;
        }
        
        self::sendJson($response);
    }
    
    /**
     * إرسال استجابة تحقق
     * Send validation response
     */
    public static function validation($isValid, $message = null, $messageEn = null, $errors = null) {
        $response = [
            'success' => true,
            'valid' => $isValid,
            'timestamp' => date('Y-m-d H:i:s'),
            'status_code' => 200
        ];
        
        if ($message !== null) {
            $response['message'] = $message;
        }
        
        if ($messageEn !== null) {
            $response['message_en'] = $messageEn;
        }
        
        if ($errors !== null) {
            $response['errors'] = $errors;
        }
        
        self::sendJson($response);
    }
    
    /**
     * إرسال استجابة غير مصرح
     * Send unauthorized response
     */
    public static function unauthorized($message = 'غير مصرح بالوصول', $messageEn = 'Unauthorized access') {
        self::error($message, $messageEn, 401);
    }
    
    /**
     * إرسال استجابة ممنوع
     * Send forbidden response
     */
    public static function forbidden($message = 'ممنوع الوصول', $messageEn = 'Forbidden access') {
        self::error($message, $messageEn, 403);
    }
    
    /**
     * إرسال استجابة غير موجود
     * Send not found response
     */
    public static function notFound($message = 'غير موجود', $messageEn = 'Not found') {
        self::error($message, $messageEn, 404);
    }
    
    /**
     * إرسال استجابة خطأ خادم
     * Send server error response
     */
    public static function serverError($message = 'خطأ في الخادم', $messageEn = 'Server error') {
        self::error($message, $messageEn, 500);
    }
    
    /**
     * إرسال استجابة للعقود المحسنة
     * Send enhanced contracts response
     */
    public static function enhancedContracts($data, $contractType = null, $networkId = null, $message = null, $messageEn = null) {
        $response = [
            'success' => true,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s'),
            'status_code' => http_response_code() ?: 200,
            'api_version' => '2.0.0'
        ];

        if ($contractType !== null) {
            $response['contract_type'] = $contractType;
        }

        if ($networkId !== null) {
            $response['network_id'] = $networkId;
        }

        if ($message !== null) {
            $response['message'] = $message;
        }

        if ($messageEn !== null) {
            $response['message_en'] = $messageEn;
        }

        self::sendJson($response);
    }

    /**
     * إرسال استجابة أحداث العقد
     * Send contract events response
     */
    public static function contractEvents($events, $pagination = null, $filters = null) {
        $response = [
            'success' => true,
            'events' => $events,
            'timestamp' => date('Y-m-d H:i:s'),
            'status_code' => 200,
            'api_version' => '2.0.0'
        ];

        if ($pagination !== null) {
            $response['pagination'] = $pagination;
        }

        if ($filters !== null) {
            $response['filters'] = $filters;
        }

        self::sendJson($response);
    }

    /**
     * إرسال استجابة JSON
     * Send JSON response
     */
    private static function sendJson($data) {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit();
    }
    
    /**
     * التحقق من طريقة الطلب
     * Validate request method
     */
    public static function validateMethod($allowedMethods) {
        $method = $_SERVER['REQUEST_METHOD'];
        
        if (!in_array($method, $allowedMethods)) {
            self::error(
                'طريقة الطلب غير مدعومة',
                'Method not allowed',
                405,
                ['allowed_methods' => $allowedMethods, 'current_method' => $method]
            );
        }
    }
    
    /**
     * التحقق من وجود البيانات المطلوبة
     * Validate required fields
     */
    public static function validateRequired($data, $requiredFields) {
        $missing = [];

        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $missing[] = $field;
            }
        }

        if (!empty($missing)) {
            self::error(
                'حقول مطلوبة مفقودة',
                'Missing required fields',
                400,
                ['missing_fields' => $missing]
            );
        }
    }

    /**
     * إرسال استجابة مزامنة العقود
     * Send contract sync response
     */
    public static function contractSync($syncData, $status = 'success', $message = null, $messageEn = null) {
        $response = [
            'success' => $status === 'success',
            'sync_status' => $status,
            'data' => $syncData,
            'timestamp' => date('Y-m-d H:i:s'),
            'status_code' => http_response_code() ?: 200,
            'api_version' => '2.0.0'
        ];

        if ($message !== null) {
            $response['message'] = $message;
        }

        if ($messageEn !== null) {
            $response['message_en'] = $messageEn;
        }

        self::sendJson($response);
    }

    /**
     * إرسال استجابة إحصائيات متقدمة
     * Send advanced statistics response
     */
    public static function advancedStats($stats, $period = null, $filters = null, $message = null, $messageEn = null) {
        $response = [
            'success' => true,
            'statistics' => $stats,
            'timestamp' => date('Y-m-d H:i:s'),
            'status_code' => 200,
            'api_version' => '2.0.0'
        ];

        if ($period !== null) {
            $response['period'] = $period;
        }

        if ($filters !== null) {
            $response['filters'] = $filters;
        }

        if ($message !== null) {
            $response['message'] = $message;
        }

        if ($messageEn !== null) {
            $response['message_en'] = $messageEn;
        }

        self::sendJson($response);
    }

    /**
     * إرسال استجابة العمليات المجمعة
     * Send batch operations response
     */
    public static function batchOperation($results, $summary = null, $message = null, $messageEn = null) {
        $successCount = count(array_filter($results, function($r) { return $r['success'] ?? false; }));
        $failureCount = count($results) - $successCount;

        $response = [
            'success' => $failureCount === 0,
            'batch_results' => $results,
            'summary' => $summary ?? [
                'total_operations' => count($results),
                'successful' => $successCount,
                'failed' => $failureCount,
                'success_rate' => count($results) > 0 ? round(($successCount / count($results)) * 100, 2) : 0
            ],
            'timestamp' => date('Y-m-d H:i:s'),
            'status_code' => $failureCount === 0 ? 200 : 207, // 207 Multi-Status
            'api_version' => '2.0.0'
        ];

        if ($message !== null) {
            $response['message'] = $message;
        }

        if ($messageEn !== null) {
            $response['message_en'] = $messageEn;
        }

        http_response_code($response['status_code']);
        self::sendJson($response);
    }

    /**
     * إرسال استجابة تحديث الوقت الفعلي
     * Send real-time update response
     */
    public static function realTimeUpdate($updateType, $data, $targetUsers = null, $message = null, $messageEn = null) {
        $response = [
            'success' => true,
            'update_type' => $updateType,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s'),
            'status_code' => 200,
            'api_version' => '2.0.0'
        ];

        if ($targetUsers !== null) {
            $response['target_users'] = $targetUsers;
        }

        if ($message !== null) {
            $response['message'] = $message;
        }

        if ($messageEn !== null) {
            $response['message_en'] = $messageEn;
        }

        self::sendJson($response);
    }
}
?>
