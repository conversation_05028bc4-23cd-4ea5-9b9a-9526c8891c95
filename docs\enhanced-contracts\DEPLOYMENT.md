# دليل النشر للعقود المحسنة

## نظرة عامة

هذا الدليل يوضح كيفية نشر وتكوين العقود الذكية المحسنة لمنصة iKAROS P2P.

## المتطلبات

### البرمجيات المطلوبة

- **Node.js**: v18.0.0 أو أحدث
- **npm**: v8.0.0 أو أحدث
- **PHP**: v8.0 أو أحدث
- **MySQL**: v8.0 أو أحدث
- **Apache/Nginx**: خادم ويب
- **Git**: لإدارة الإصدارات

### أدوات Blockchain

- **Hardhat**: لتطوير ونشر العقود
- **MetaMask**: للتفاعل مع العقود
- **BSC Testnet/Mainnet**: الشبكات المدعومة

### متغيرات البيئة

إنشاء ملف `.env` في جذر المشروع:

```env
# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_PORT=3306
DB_NAME=ikaros_p2p_enhanced
DB_USER=root
DB_PASSWORD=

# إعدادات Blockchain
NEXT_PUBLIC_CHAIN_ID=97
NEXT_PUBLIC_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/
NEXT_PUBLIC_EXPLORER_URL=https://testnet.bscscan.com/

# عناوين العقود المحسنة (BSC Testnet)
NEXT_PUBLIC_CORE_ESCROW_ADDRESS=******************************************
NEXT_PUBLIC_REPUTATION_MANAGER_ADDRESS=******************************************
NEXT_PUBLIC_ORACLE_MANAGER_ADDRESS=0xB70715392F62628Ccd1258AAF691384bE8C023b6
NEXT_PUBLIC_ADMIN_MANAGER_ADDRESS=0x5A9FD8082ADA38678721D59AAB4d4F76883c5575
NEXT_PUBLIC_ESCROW_INTEGRATOR_ADDRESS=0xc0Ec65ffA95aca485e4C2e9b35B20C85FA36F0e0

# مفاتيح خاصة (للنشر فقط - لا تشاركها)
DEPLOYER_PRIVATE_KEY=your_private_key_here
ADMIN_PRIVATE_KEY=your_admin_private_key_here

# إعدادات API
NEXT_PUBLIC_API_BASE_URL=http://localhost/api
API_SECRET_KEY=your_secret_key_here

# إعدادات الأمان
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here
```

## خطوات النشر

### 1. إعداد البيئة

#### تثبيت التبعيات

```bash
# تثبيت تبعيات Node.js
npm install

# تثبيت تبعيات Hardhat
npm install --save-dev hardhat @nomiclabs/hardhat-ethers ethers
```

#### إعداد قاعدة البيانات

```bash
# إنشاء قاعدة البيانات
mysql -u root -p -e "CREATE DATABASE ikaros_p2p_enhanced CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# استيراد الهيكل
mysql -u root -p ikaros_p2p_enhanced < database/schema.sql

# استيراد البيانات الأولية
mysql -u root -p ikaros_p2p_enhanced < database/initial_data.sql
```

### 2. نشر العقود الذكية

#### إعداد Hardhat

إنشاء ملف `hardhat.config.js`:

```javascript
require("@nomiclabs/hardhat-ethers");
require("dotenv").config();

module.exports = {
  solidity: {
    version: "0.8.19",
    settings: {
      optimizer: {
        enabled: true,
        runs: 200
      }
    }
  },
  networks: {
    bscTestnet: {
      url: "https://data-seed-prebsc-1-s1.binance.org:8545/",
      chainId: 97,
      accounts: [process.env.DEPLOYER_PRIVATE_KEY]
    },
    bscMainnet: {
      url: "https://bsc-dataseed1.binance.org/",
      chainId: 56,
      accounts: [process.env.DEPLOYER_PRIVATE_KEY]
    }
  }
};
```

#### نشر العقود على BSC Testnet

```bash
# نشر جميع العقود
npx hardhat run scripts/deploy-enhanced-contracts.js --network bscTestnet

# التحقق من العقود
npx hardhat verify --network bscTestnet ******************************************
```

#### سكريبت النشر

إنشاء ملف `scripts/deploy-enhanced-contracts.js`:

```javascript
const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 بدء نشر العقود المحسنة...");

  // نشر Core Escrow
  const CoreEscrow = await ethers.getContractFactory("CoreEscrow");
  const coreEscrow = await CoreEscrow.deploy();
  await coreEscrow.deployed();
  console.log("✅ Core Escrow deployed to:", coreEscrow.address);

  // نشر Reputation Manager
  const ReputationManager = await ethers.getContractFactory("ReputationManager");
  const reputationManager = await ReputationManager.deploy();
  await reputationManager.deployed();
  console.log("✅ Reputation Manager deployed to:", reputationManager.address);

  // نشر Oracle Manager
  const OracleManager = await ethers.getContractFactory("OracleManager");
  const oracleManager = await OracleManager.deploy();
  await oracleManager.deployed();
  console.log("✅ Oracle Manager deployed to:", oracleManager.address);

  // نشر Admin Manager
  const AdminManager = await ethers.getContractFactory("AdminManager");
  const adminManager = await AdminManager.deploy();
  await adminManager.deployed();
  console.log("✅ Admin Manager deployed to:", adminManager.address);

  // نشر Escrow Integrator
  const EscrowIntegrator = await ethers.getContractFactory("EscrowIntegrator");
  const escrowIntegrator = await EscrowIntegrator.deploy(
    coreEscrow.address,
    reputationManager.address,
    oracleManager.address,
    adminManager.address
  );
  await escrowIntegrator.deployed();
  console.log("✅ Escrow Integrator deployed to:", escrowIntegrator.address);

  // تحديث قاعدة البيانات بعناوين العقود
  console.log("\n📝 عناوين العقود للتحديث في قاعدة البيانات:");
  console.log(`Core Escrow: ${coreEscrow.address}`);
  console.log(`Reputation Manager: ${reputationManager.address}`);
  console.log(`Oracle Manager: ${oracleManager.address}`);
  console.log(`Admin Manager: ${adminManager.address}`);
  console.log(`Escrow Integrator: ${escrowIntegrator.address}`);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
```

### 3. تكوين الخادم

#### Apache Configuration

إنشاء ملف `.htaccess`:

```apache
RewriteEngine On

# إعادة توجيه APIs
RewriteRule ^api/enhanced-contracts/(.*)$ api/enhanced-contracts/$1 [L]

# إعدادات CORS
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"

# إعدادات الأمان
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

#### Nginx Configuration

```nginx
server {
    listen 80;
    server_name localhost;
    root /var/www/ikaros-p2p;
    index index.php index.html;

    # إعدادات PHP
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # APIs للعقود المحسنة
    location /api/enhanced-contracts/ {
        try_files $uri $uri/ /api/enhanced-contracts/index.php?$query_string;
    }

    # إعدادات الأمان
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # إعدادات CORS
    add_header Access-Control-Allow-Origin "*";
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
    add_header Access-Control-Allow-Headers "Content-Type, Authorization";
}
```

### 4. تحديث قاعدة البيانات

#### إدراج عناوين العقود

```sql
-- تحديث عناوين العقود في قاعدة البيانات
UPDATE enhanced_contracts SET 
    contract_address = '******************************************',
    is_active = TRUE,
    deployed_at = NOW()
WHERE contract_type = 'core_escrow' AND network_id = 1;

UPDATE enhanced_contracts SET 
    contract_address = '******************************************',
    is_active = TRUE,
    deployed_at = NOW()
WHERE contract_type = 'reputation_manager' AND network_id = 1;

-- إضافة المزيد حسب الحاجة...
```

### 5. اختبار النشر

#### اختبار العقود الذكية

```bash
# تشغيل اختبارات العقود
npx hardhat test

# اختبار التفاعل مع العقود المنشورة
npx hardhat run scripts/test-deployed-contracts.js --network bscTestnet
```

#### اختبار APIs

```bash
# اختبار APIs
curl -X GET "http://localhost/api/enhanced-contracts/networks.php?action=networks"

# اختبار إنشاء عرض
curl -X POST "http://localhost/api/enhanced-contracts/offers.php" \
  -H "Content-Type: application/json" \
  -d '{"userId":1,"offerType":"sell","networkId":1,"tokenId":1,"amount":"100","price":"3.75","currency":"SAR","paymentMethods":["bank_transfer"]}'
```

### 6. مراقبة النظام

#### إعداد السجلات

```php
// في ملف config/logging.php
return [
    'default' => 'daily',
    'channels' => [
        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/enhanced-contracts.log'),
            'level' => 'debug',
            'days' => 14,
        ],
        'blockchain' => [
            'driver' => 'daily',
            'path' => storage_path('logs/blockchain.log'),
            'level' => 'info',
            'days' => 30,
        ]
    ]
];
```

#### مراقبة الأداء

```bash
# مراقبة استخدام الذاكرة
top -p $(pgrep php)

# مراقبة قاعدة البيانات
mysqladmin -u root -p processlist

# مراقبة السجلات
tail -f /var/log/apache2/error.log
tail -f storage/logs/enhanced-contracts.log
```

## النشر على الإنتاج

### 1. إعدادات الأمان

```env
# متغيرات الإنتاج
NODE_ENV=production
APP_DEBUG=false

# استخدام HTTPS
NEXT_PUBLIC_API_BASE_URL=https://yourdomain.com/api

# شبكة الإنتاج
NEXT_PUBLIC_CHAIN_ID=56
NEXT_PUBLIC_RPC_URL=https://bsc-dataseed1.binance.org/

# عناوين العقود على الشبكة الرئيسية
NEXT_PUBLIC_CORE_ESCROW_ADDRESS=0x...
```

### 2. تحسين الأداء

```bash
# ضغط الأصول
npm run build

# تحسين قاعدة البيانات
mysql -u root -p -e "OPTIMIZE TABLE offers, trades, enhanced_contract_events;"

# تفعيل التخزين المؤقت
# في ملف .htaccess
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
```

### 3. النسخ الاحتياطي

```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u root -p ikaros_p2p_enhanced > backup_$(date +%Y%m%d_%H%M%S).sql

# نسخ احتياطي للملفات
tar -czf backup_files_$(date +%Y%m%d_%H%M%S).tar.gz /var/www/ikaros-p2p
```

## استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في الاتصال بقاعدة البيانات**
   ```bash
   # التحقق من حالة MySQL
   systemctl status mysql
   
   # إعادة تشغيل MySQL
   systemctl restart mysql
   ```

2. **خطأ في العقود الذكية**
   ```bash
   # التحقق من رصيد الغاز
   # التحقق من صحة عناوين العقود
   # مراجعة سجلات المعاملات
   ```

3. **مشاكل في الأداء**
   ```bash
   # تحسين استعلامات قاعدة البيانات
   # زيادة ذاكرة PHP
   # تفعيل التخزين المؤقت
   ```

### السجلات المفيدة

```bash
# سجلات Apache
tail -f /var/log/apache2/error.log

# سجلات PHP
tail -f /var/log/php/error.log

# سجلات التطبيق
tail -f storage/logs/enhanced-contracts.log

# سجلات قاعدة البيانات
tail -f /var/log/mysql/error.log
```

## الصيانة

### تحديثات دورية

```bash
# تحديث التبعيات
npm update

# تحديث قاعدة البيانات
mysql -u root -p ikaros_p2p_enhanced < database/migrations/latest.sql

# تنظيف السجلات القديمة
find storage/logs -name "*.log" -mtime +30 -delete
```

### مراقبة الأمان

```bash
# فحص الثغرات الأمنية
npm audit

# تحديث العقود الذكية (إذا لزم الأمر)
npx hardhat run scripts/upgrade-contracts.js --network bscMainnet
```
