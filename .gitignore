# ملفات الإعدادات الحساسة
.env
.env.local
.env.production
.env.development
config/config.php

# ملفات التحميل والمؤقتة
public/uploads/*
!public/uploads/.gitkeep
logs/*
!logs/.gitkeep
temp/*
!temp/.gitkeep

# ملفات قاعدة البيانات
*.sql.backup
database/backups/*
!database/backups/.gitkeep

# ملفات Node.js
node_modules/
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# ملفات Next.js
/.next/
/out/
/build
.vercel

# ملفات TypeScript
*.tsbuildinfo
next-env.d.ts

# ملفات IDE
.vscode/
.idea/
*.swp
*.swo

# ملفات النظام
.DS_Store
*.pem
Thumbs.db

# ملفات التطوير والاختبار
/coverage
test_*.php
*_test.php
debug_*.php
src/tests/
tests/

# ملفات مؤقتة ومجلدات فارغة
temp/
uploads/
scripts/
apiconfig/

# ملفات التنصيب (بعد اكتمال التنصيب)
# install/

# ملفات العقود الذكية المؤقتة
contracts/cache/
contracts/artifacts/
contracts/node_modules/

# ملفات التقارير والوثائق التجريبية
*_REPORT.md
*_GUIDE.md
audit_*.md

# ملفات مؤقتة أخرى
*.tmp
*.temp
*.log
*.backup
*.tsbuildinfo
next-env.d.ts
