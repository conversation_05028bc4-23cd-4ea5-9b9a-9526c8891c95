# 📋 تعليمات تصميم صفحة إنشاء العرض

## 🎯 نظرة عامة

هذا المستند يحتوي على التعليمات المفصلة لتصميم وتطوير صفحة إنشاء العرض في منصة Ikaros P2P، مع التركيز على التصميم المتجاوب والتكامل مع النظام الحالي.

## 🗄️ تحليل قاعدة البيانات

### جدول `offers` - الحقول الأساسية:
```sql
- id: INT PRIMARY KEY AUTO_INCREMENT
- user_id: INT NOT NULL
- offer_type: ENUM('buy', 'sell') NOT NULL
- amount: DECIMAL(20, 8) NOT NULL
- min_amount: DECIMAL(20, 8)
- max_amount: DECIMAL(20, 8)
- price: DECIMAL(10, 4) NOT NULL
- currency: VARCHAR(3) NOT NULL
- stablecoin: VARCHAR(10) DEFAULT 'USDT'
- payment_methods: JSON
- terms: TEXT
- auto_reply: TEXT
- is_active: BOOLEAN DEFAULT TRUE
- is_premium: BOOLEAN DEFAULT FALSE
- views_count: INT DEFAULT 0
- time_limit: INT DEFAULT 1800
```

### حقول التكامل مع العقد الذكي:
```sql
- blockchain_trade_id: INT UNIQUE NULL
- transaction_hash: VARCHAR(66) NULL
- contract_status: ENUM('pending', 'created', 'joined', 'payment_sent', 'completed', 'cancelled', 'disputed')
- escrow_amount: DECIMAL(20, 8) NULL
- platform_fee: DECIMAL(20, 8) NULL
- net_amount: DECIMAL(20, 8) NULL
```

## 🎨 مبادئ التصميم

### 1. التصميم المتجاوب (مثل صفحة العروض الحالية)

#### Grid System:
```tsx
// Mobile: عمود واحد
className="grid grid-cols-1"

// Tablet: عمودين
className="grid grid-cols-1 md:grid-cols-2"

// Desktop: ثلاثة أعمدة
className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3"

// المسافات المتجاوبة
className="gap-4 md:gap-6"
```

#### Typography المتجاوبة:
```tsx
// العناوين
className="text-lg md:text-xl lg:text-2xl"

// النصوص العادية
className="text-sm md:text-base"

// النصوص الصغيرة
className="text-xs md:text-sm"
```

#### Padding والمسافات:
```tsx
// الحشو المتدرج
className="p-3 md:p-4 lg:p-6"

// الهوامش المتدرجة
className="mb-4 md:mb-6 lg:mb-8"
```

### 2. نظام الألوان والثيمات

#### الألوان الأساسية:
```tsx
// Primary - الأزرار الرئيسية
className="bg-blue-600 hover:bg-blue-700"

// Success - عروض الشراء
className="bg-green-500 text-green-700"

// Danger - عروض البيع
className="bg-red-500 text-red-700"

// Warning - التحذيرات
className="bg-yellow-500 text-yellow-700"
```

#### دعم الوضع المظلم:
```tsx
// الخلفيات
className="bg-white dark:bg-gray-800"

// النصوص
className="text-gray-900 dark:text-white"

// الحدود
className="border-gray-200 dark:border-gray-700"
```

### 3. المكونات التفاعلية

#### حالات التحميل:
```tsx
// مؤشر التحميل
{loading && <Loader2 className="w-4 h-4 animate-spin" />}

// الأزرار المعطلة
className="disabled:opacity-50 disabled:cursor-not-allowed"
```

#### التأثيرات التفاعلية:
```tsx
// Hover Effects
className="hover:bg-gray-50 dark:hover:bg-gray-700"

// Focus States
className="focus:ring-2 focus:ring-blue-500 focus:border-transparent"

// Transitions
className="transition-all duration-200"
```

## 🏗️ هيكل الصفحة المقترح

### المرحلة 1: معلومات العرض الأساسية

```tsx
// نوع العرض (شراء/بيع)
<OfferTypeSelector 
  value={formData.offer_type}
  onChange={(type) => handleInputChange('offer_type', type)}
/>

// العملات والكميات
<CurrencyAmountSection 
  currency={formData.currency}
  stablecoin={formData.stablecoin}
  amount={formData.amount}
  minAmount={formData.min_amount}
  maxAmount={formData.max_amount}
/>

// السعر مع اقتراحات ذكية
<SmartPricingSection 
  price={formData.price}
  marketPrice={marketPrice}
  offerType={formData.offer_type}
/>

// المعاينة المباشرة للحسابات
<LiveCalculator 
  amount={formData.amount}
  price={formData.price}
  currency={formData.currency}
/>
```

### المرحلة 2: طرق الدفع والشروط

```tsx
// اختيار طرق الدفع المتعددة
<PaymentMethodsSelector 
  selected={formData.payment_methods}
  onChange={(methods) => handleInputChange('payment_methods', methods)}
/>

// شروط التداول مع قوالب جاهزة
<TermsEditor 
  value={formData.terms}
  onChange={(terms) => handleInputChange('terms', terms)}
  templates={termsTemplates}
/>

// الحد الزمني للدفع
<TimeLimitSelector 
  value={formData.time_limit}
  onChange={(limit) => handleInputChange('time_limit', limit)}
/>

// الموقع الجغرافي (اختياري)
<LocationSelector 
  value={formData.location}
  onChange={(location) => handleInputChange('location', location)}
/>
```

### المرحلة 3: إعدادات متقدمة

```tsx
// الرد التلقائي
<AutoReplySettings 
  enabled={formData.auto_reply}
  message={formData.auto_reply_message}
  onChange={(enabled, message) => {
    handleInputChange('auto_reply', enabled);
    handleInputChange('auto_reply_message', message);
  }}
/>

// العرض المميز
<PremiumOfferToggle 
  enabled={formData.is_premium}
  onChange={(premium) => handleInputChange('is_premium', premium)}
/>

// معاينة العرض النهائية
<OfferPreview 
  data={formData}
  calculatedTotal={calculateTotal()}
/>

// أزرار الحفظ والنشر
<ActionButtons 
  onSave={handleSave}
  onPublish={handlePublish}
  loading={isCreating}
/>
```

## 🎯 الميزات الذكية المطلوبة

### 1. التسعير الذكي

```tsx
// Hook لجلب أسعار السوق
const useMarketPrice = (stablecoin: string) => {
  const [marketPrice, setMarketPrice] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    // جلب السعر من API
    fetchMarketPrice(stablecoin);
  }, [stablecoin]);
  
  return { marketPrice, isLoading };
};

// حاسبة الهامش التلقائية
const calculateSuggestedPrice = (marketPrice: number, offerType: 'buy' | 'sell') => {
  const margin = offerType === 'buy' ? 0.98 : 1.02; // 2% هامش
  return (marketPrice * margin).toFixed(2);
};

// تنبيهات الأسعار غير المعقولة
const validatePrice = (price: number, marketPrice: number) => {
  const deviation = Math.abs(price - marketPrice) / marketPrice;
  if (deviation > 0.1) { // أكثر من 10% انحراف
    return {
      isValid: false,
      message: 'السعر يختلف كثيراً عن سعر السوق'
    };
  }
  return { isValid: true };
};
```

### 2. تجربة المستخدم المحسنة

```tsx
// الحفظ التلقائي
const useAutoSave = (formData: CreateOfferFormData) => {
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      localStorage.setItem('draft_offer', JSON.stringify(formData));
    }, 2000); // حفظ كل ثانيتين
    
    return () => clearTimeout(timeoutId);
  }, [formData]);
};

// التحقق الفوري من البيانات
const useRealTimeValidation = (formData: CreateOfferFormData) => {
  const [errors, setErrors] = useState<ValidationErrors>({});
  
  useEffect(() => {
    const newErrors = validateFormData(formData);
    setErrors(newErrors);
  }, [formData]);
  
  return errors;
};

// ردود فعل بصرية فورية
const VisualFeedback = ({ isValid, children }: { isValid: boolean, children: React.ReactNode }) => (
  <div className={`transition-all duration-200 ${
    isValid 
      ? 'border-green-500 bg-green-50 dark:bg-green-900/20' 
      : 'border-red-500 bg-red-50 dark:bg-red-900/20'
  }`}>
    {children}
  </div>
);
```

### 3. الأمان والتحقق

```tsx
// التحقق من تسجيل الدخول
const RequireAuth = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated } = useAuth();
  
  if (!isAuthenticated) {
    return <LoginPrompt />;
  }
  
  return <>{children}</>;
};

// تقييم المخاطر التلقائي
const assessRisk = (formData: CreateOfferFormData) => {
  let riskScore = 0;
  
  // عوامل المخاطر
  if (parseFloat(formData.amount) > 10000) riskScore += 2;
  if (formData.payment_methods.includes('cash')) riskScore += 1;
  if (!formData.terms.trim()) riskScore += 1;
  
  return {
    score: riskScore,
    level: riskScore > 3 ? 'high' : riskScore > 1 ? 'medium' : 'low'
  };
};

// التحقق من الامتثال للشروط
const validateCompliance = (formData: CreateOfferFormData) => {
  const issues = [];
  
  if (!formData.terms.includes('KYC')) {
    issues.push('يجب ذكر متطلبات التحقق من الهوية');
  }
  
  if (parseFloat(formData.amount) > 50000 && !formData.terms.includes('AML')) {
    issues.push('يجب ذكر سياسات مكافحة غسيل الأموال للمبالغ الكبيرة');
  }
  
  return issues;
};
```

## 🔧 التكامل التقني

### 1. APIs المطلوبة

```typescript
// إنشاء عرض جديد
POST /api/offers/create.php
Content-Type: application/json
Authorization: Bearer {token}

{
  "offer_type": "sell",
  "amount": "1000.00",
  "price": "3.75",
  "currency": "SAR",
  "stablecoin": "USDT",
  "payment_methods": ["bank_transfer", "stc_pay"],
  "terms": "شروط التداول...",
  "time_limit": 1800,
  "is_premium": false
}

// جلب أسعار السوق
GET /api/market/prices.php?stablecoin=USDT

// طرق الدفع المتاحة
GET /api/payment-methods.php

// التحقق من صحة البيانات
POST /api/offers/validate.php
```

### 2. State Management

```typescript
interface CreateOfferFormData {
  offer_type: 'buy' | 'sell';
  amount: string;
  min_amount: string;
  max_amount: string;
  price: string;
  currency: string;
  stablecoin: string;
  payment_methods: string[];
  terms: string;
  time_limit: number;
  auto_reply: boolean;
  auto_reply_message: string;
  location: string;
  is_premium: boolean;
  margin_type: 'fixed' | 'percentage';
  margin_value: string;
}

interface ValidationState {
  errors: Record<string, string>;
  warnings: Record<string, string>;
  isValid: boolean;
}

interface OfferPreview {
  calculatedTotal: string;
  estimatedFees: string;
  netAmount: string;
  riskAssessment: RiskAssessment;
}

interface SaveState {
  isDraft: boolean;
  isPublished: boolean;
  lastSaved: Date | null;
  autoSaveEnabled: boolean;
}
```

### 3. Hooks المخصصة

```typescript
// جلب أسعار السوق
export const useMarketPrice = (stablecoin: string) => {
  // Implementation...
};

// التحقق من صحة العرض
export const useOfferValidation = (formData: CreateOfferFormData) => {
  // Implementation...
};

// الحفظ التلقائي
export const useAutoSave = (formData: CreateOfferFormData, enabled: boolean = true) => {
  // Implementation...
};

// معاينة العرض
export const useOfferPreview = (formData: CreateOfferFormData) => {
  // Implementation...
};
```

## 📱 التصميم المتجاوب المفصل

### Mobile First (320px+):
```scss
// نموذج من عمود واحد
.form-container {
  @apply grid grid-cols-1 gap-4 p-4;
}

// أزرار بعرض كامل
.mobile-button {
  @apply w-full py-3 text-base;
}

// نص أكبر للقراءة السهلة
.mobile-text {
  @apply text-base leading-relaxed;
}
```

### Tablet (768px+):
```scss
// نموذج من عمودين
.form-container {
  @apply md:grid-cols-2 md:gap-6 md:p-6;
}

// شريط جانبي للمعاينة
.tablet-layout {
  @apply md:flex md:space-x-6;
}

.form-section {
  @apply md:flex-1;
}

.preview-section {
  @apply md:w-80 md:sticky md:top-8;
}
```

### Desktop (1024px+):
```scss
// تخطيط ثلاثة أعمدة
.desktop-layout {
  @apply lg:grid lg:grid-cols-3 lg:gap-8 lg:p-8;
}

// معاينة مباشرة
.live-preview {
  @apply lg:col-span-1 lg:sticky lg:top-8;
}

// اختصارات لوحة المفاتيح
.keyboard-shortcuts {
  @apply lg:block hidden;
}
```

## 🎨 نمط التصميم المتسق

### مثل صفحة العروض الحالية:

```tsx
// Card-based Layout
const OfferCard = ({ children }: { children: React.ReactNode }) => (
  <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    {children}
  </div>
);

// Consistent Spacing
const spacing = {
  xs: 'space-y-2',
  sm: 'space-y-4',
  md: 'space-y-6',
  lg: 'space-y-8',
  xl: 'space-y-12'
};

// Icon Integration
const IconWithText = ({ icon: Icon, children }: { icon: LucideIcon, children: React.ReactNode }) => (
  <div className="flex items-center space-x-2 space-x-reverse">
    <Icon className="w-5 h-5 text-gray-500" />
    <span>{children}</span>
  </div>
);

// Color Coding
const getOfferTypeColor = (type: 'buy' | 'sell') => ({
  buy: 'text-green-600 bg-green-50 dark:bg-green-900/20',
  sell: 'text-red-600 bg-red-50 dark:bg-red-900/20'
}[type]);

// Shadow System
const shadows = {
  sm: 'shadow-sm',
  md: 'shadow-md',
  lg: 'shadow-lg',
  xl: 'shadow-xl'
};

// Border Radius
const borderRadius = {
  md: 'rounded-md',
  lg: 'rounded-lg',
  xl: 'rounded-xl',
  full: 'rounded-full'
};
```

## 🚀 خطة التنفيذ المقترحة

### الأسبوع 1: الهيكل الأساسي
- [ ] إنشاء مكونات النموذج الأساسية
- [ ] تطبيق التصميم المتجاوب
- [ ] إضافة التحقق من صحة البيانات
- [ ] إنشاء نظام الخطوات (Steps)

### الأسبوع 2: الميزات الذكية
- [ ] تكامل أسعار السوق
- [ ] إضافة التسعير الذكي
- [ ] تطوير المعاينة المباشرة
- [ ] إضافة حاسبة الهامش

### الأسبوع 3: التحسينات
- [ ] إضافة الحفظ التلقائي
- [ ] تحسين تجربة المستخدم
- [ ] اختبار شامل للاستجابة
- [ ] إضافة الرسوم المتحركة

### الأسبوع 4: التكامل والاختبار
- [ ] ربط APIs
- [ ] اختبار الأمان
- [ ] تحسين الأداء
- [ ] اختبار المستخدم النهائي

## 📝 ملاحظات إضافية

### الأولويات:
1. **الأمان أولاً**: التحقق من المصادقة والتحقق من صحة البيانات
2. **تجربة المستخدم**: سهولة الاستخدام والوضوح
3. **الاستجابة**: يعمل بشكل مثالي على جميع الأجهزة
4. **الأداء**: تحميل سريع وتفاعل سلس

### اعتبارات خاصة:
- دعم اللغة العربية والإنجليزية
- التكامل مع العقود الذكية
- معالجة الأخطاء الشاملة
- إمكانية الوصول (Accessibility)

## 🔍 أمثلة عملية للكود

### مثال: مكون اختيار نوع العرض

```tsx
const OfferTypeSelector = ({ value, onChange }: {
  value: 'buy' | 'sell';
  onChange: (type: 'buy' | 'sell') => void;
}) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      <button
        onClick={() => onChange('buy')}
        className={`p-4 border-2 rounded-lg text-center transition-colors ${
          value === 'buy'
            ? 'border-green-500 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
        }`}
      >
        <TrendingUp className="w-6 h-6 mx-auto mb-2" />
        <div className="font-medium">{t('buy')}</div>
        <div className="text-xs text-gray-500">{t('buyDescription')}</div>
      </button>

      <button
        onClick={() => onChange('sell')}
        className={`p-4 border-2 rounded-lg text-center transition-colors ${
          value === 'sell'
            ? 'border-red-500 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
        }`}
      >
        <TrendingDown className="w-6 h-6 mx-auto mb-2" />
        <div className="font-medium">{t('sell')}</div>
        <div className="text-xs text-gray-500">{t('sellDescription')}</div>
      </button>
    </div>
  );
};
```

### مثال: مكون التسعير الذكي

```tsx
const SmartPricingSection = ({
  price,
  marketPrice,
  offerType,
  onChange
}: {
  price: string;
  marketPrice: number | null;
  offerType: 'buy' | 'sell';
  onChange: (price: string) => void;
}) => {
  const suggestedPrice = marketPrice ?
    (offerType === 'buy' ? marketPrice * 0.98 : marketPrice * 1.02).toFixed(2) : null;

  const priceDeviation = marketPrice && price ?
    Math.abs(parseFloat(price) - marketPrice) / marketPrice : 0;

  return (
    <div className="space-y-4">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {t('price')} ({t('currency.SAR')})
      </label>

      <div className="relative">
        <input
          type="number"
          value={price}
          onChange={(e) => onChange(e.target.value)}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all ${
            priceDeviation > 0.1
              ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
              : 'border-gray-300 dark:border-gray-600'
          }`}
          placeholder="0.00"
        />

        {suggestedPrice && (
          <button
            onClick={() => onChange(suggestedPrice)}
            className="absolute left-2 top-1/2 transform -translate-y-1/2 text-xs text-blue-600 hover:text-blue-700 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded"
          >
            {t('suggested')}: {suggestedPrice}
          </button>
        )}
      </div>

      {marketPrice && (
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">{t('marketPrice')}: {marketPrice.toFixed(2)}</span>
          {priceDeviation > 0.1 && (
            <span className="text-red-500 flex items-center">
              <AlertTriangle className="w-4 h-4 mr-1" />
              {t('priceDeviationWarning')}
            </span>
          )}
        </div>
      )}
    </div>
  );
};
```

### مثال: مكون المعاينة المباشرة

```tsx
const LiveCalculator = ({
  amount,
  price,
  currency,
  stablecoin
}: {
  amount: string;
  price: string;
  currency: string;
  stablecoin: string;
}) => {
  const total = useMemo(() => {
    const amt = parseFloat(amount) || 0;
    const prc = parseFloat(price) || 0;
    return (amt * prc).toFixed(2);
  }, [amount, price]);

  const platformFee = useMemo(() => {
    return (parseFloat(total) * 0.01).toFixed(2); // 1% رسوم
  }, [total]);

  const netAmount = useMemo(() => {
    return (parseFloat(total) - parseFloat(platformFee)).toFixed(2);
  }, [total, platformFee]);

  return (
    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3">
      <h3 className="font-medium text-gray-900 dark:text-white flex items-center">
        <Calculator className="w-4 h-4 mr-2" />
        {t('calculation')}
      </h3>

      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600 dark:text-gray-400">
            {amount} {stablecoin} × {price} {currency}
          </span>
          <span className="font-medium">{total} {currency}</span>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-600 dark:text-gray-400">{t('platformFee')} (1%)</span>
          <span className="text-red-600">-{platformFee} {currency}</span>
        </div>

        <div className="border-t border-gray-200 dark:border-gray-600 pt-2">
          <div className="flex justify-between font-semibold">
            <span className="text-gray-900 dark:text-white">{t('netAmount')}</span>
            <span className="text-green-600">{netAmount} {currency}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
```

## 🎨 ملفات الترجمة المطلوبة

### `public/locales/ar/create-offer.json`:
```json
{
  "title": "إنشاء عرض جديد",
  "subtitle": "قم بإنشاء عرض شراء أو بيع للعملات المستقرة",
  "steps": {
    "basic": "المعلومات الأساسية",
    "payment": "طرق الدفع والشروط",
    "advanced": "الإعدادات المتقدمة"
  },
  "offerType": {
    "buy": "شراء",
    "sell": "بيع",
    "buyDescription": "أريد شراء عملة مستقرة",
    "sellDescription": "أريد بيع عملة مستقرة"
  },
  "fields": {
    "amount": "الكمية",
    "minAmount": "الحد الأدنى",
    "maxAmount": "الحد الأقصى",
    "price": "السعر",
    "currency": "العملة",
    "stablecoin": "العملة المستقرة",
    "paymentMethods": "طرق الدفع",
    "terms": "شروط التداول",
    "timeLimit": "الحد الزمني",
    "location": "الموقع",
    "autoReply": "الرد التلقائي",
    "premiumOffer": "عرض مميز"
  },
  "validation": {
    "required": "هذا الحقل مطلوب",
    "invalidAmount": "الكمية غير صحيحة",
    "invalidPrice": "السعر غير صحيح",
    "priceDeviation": "السعر يختلف كثيراً عن سعر السوق",
    "selectPaymentMethod": "يجب اختيار طريقة دفع واحدة على الأقل",
    "termsRequired": "شروط التداول مطلوبة"
  },
  "actions": {
    "previous": "السابق",
    "next": "التالي",
    "save": "حفظ",
    "publish": "نشر العرض",
    "preview": "معاينة"
  }
}
```

## 🔧 متطلبات تقنية إضافية

### Environment Variables:
```env
# API URLs
NEXT_PUBLIC_API_BASE_URL=http://localhost/ikaros-p2p/api
NEXT_PUBLIC_MARKET_API_URL=https://api.coingecko.com/api/v3

# Feature Flags
NEXT_PUBLIC_ENABLE_AUTO_SAVE=true
NEXT_PUBLIC_ENABLE_SMART_PRICING=true
NEXT_PUBLIC_ENABLE_RISK_ASSESSMENT=true

# Limits
NEXT_PUBLIC_MAX_OFFER_AMOUNT=100000
NEXT_PUBLIC_MIN_OFFER_AMOUNT=10
NEXT_PUBLIC_MAX_TIME_LIMIT=86400
```

### Package Dependencies:
```json
{
  "dependencies": {
    "react-hook-form": "^7.45.0",
    "yup": "^1.2.0",
    "@hookform/resolvers": "^3.1.0",
    "react-select": "^5.7.0",
    "react-textarea-autosize": "^8.4.1",
    "react-number-format": "^5.2.2"
  }
}
```

---

**تاريخ الإنشاء**: 2025-01-27
**الإصدار**: 1.0
**المطور**: Augment Agent
**المراجعة التالية**: عند بدء التطوير

## 📞 للاستفسارات والدعم

هذا المستند يحتوي على جميع المتطلبات والتعليمات اللازمة لتطوير صفحة إنشاء العرض.
في حالة الحاجة لتوضيحات إضافية أو تعديلات، يرجى مراجعة فريق التطوير.
