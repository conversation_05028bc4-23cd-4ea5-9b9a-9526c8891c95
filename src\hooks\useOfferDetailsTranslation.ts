import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';

export const useOfferDetailsTranslation = () => {
  const { t, i18n } = useTranslation('offer-details');
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const loadTranslations = async () => {
      try {
        if (!i18n.hasResourceBundle(i18n.language, 'offer-details')) {
          // تحميل ترجمات تفاصيل العرض
          const currentLang = i18n.language;
          const offerDetailsTranslations = await import(`../../public/locales/${currentLang}/offer-details.json`);
          
          i18n.addResourceBundle(currentLang, 'offer-details', offerDetailsTranslations.default, true, true);
        }
        setIsLoaded(true);
      } catch (error) {
        console.error('Error loading offer-details translations:', error);
        // fallback للإنجليزية
        try {
          const fallbackTranslations = await import(`../../public/locales/en/offer-details.json`);
          i18n.addResourceBundle('en', 'offer-details', fallbackTranslations.default, true, true);
          setIsLoaded(true);
        } catch (fallbackError) {
          console.error('Error loading fallback offer-details translations:', fallbackError);
          setIsLoaded(true); // تحميل حتى لو فشل
        }
      }
    };

    loadTranslations();
  }, [i18n]);

  return { t, isLoaded };
};
