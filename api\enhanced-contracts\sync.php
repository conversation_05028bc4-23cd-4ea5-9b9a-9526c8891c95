<?php
/**
 * API مزامنة العقد الذكي المحسن
 * Enhanced Smart Contract Sync API
 */

require_once __DIR__ . '/../cors.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/response.php';

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetSyncStatus();
            break;
        case 'POST':
            handleSyncRequest();
            break;
        case 'PUT':
            handleUpdateSyncStatus();
            break;
        default:
            sendErrorResponse('طريقة طلب غير مدعومة', 405);
    }
    
} catch (Exception $e) {
    logError("Enhanced Contract Sync API error: " . $e->getMessage());
    sendErrorResponse('خطأ في الخادم: ' . $e->getMessage(), 500);
}

/**
 * جلب حالة المزامنة
 */
function handleGetSyncStatus() {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    try {
        $action = $_GET['action'] ?? 'status';
        
        switch ($action) {
            case 'status':
                getSyncStatus($connection);
                break;
            case 'events':
                getContractEvents($connection);
                break;
            case 'failed':
                getFailedSyncs($connection);
                break;
            default:
                sendErrorResponse('إجراء غير مدعوم', 400);
        }
        
    } catch (Exception $e) {
        sendErrorResponse('خطأ في جلب حالة المزامنة: ' . $e->getMessage(), 500);
    }
}

/**
 * جلب حالة المزامنة العامة
 */
function getSyncStatus($connection) {
    // إحصائيات العروض
    $offersStmt = $connection->prepare("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN sync_status = 'synced' THEN 1 ELSE 0 END) as synced,
            SUM(CASE WHEN sync_status = 'pending' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN sync_status = 'failed' THEN 1 ELSE 0 END) as failed,
            SUM(CASE WHEN sync_status = 'conflict' THEN 1 ELSE 0 END) as conflicts
        FROM offers
    ");
    $offersStmt->execute();
    $offersStats = $offersStmt->fetch(PDO::FETCH_ASSOC);
    
    // إحصائيات الصفقات
    $tradesStmt = $connection->prepare("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN sync_status = 'synced' THEN 1 ELSE 0 END) as synced,
            SUM(CASE WHEN sync_status = 'pending' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN sync_status = 'failed' THEN 1 ELSE 0 END) as failed,
            SUM(CASE WHEN sync_status = 'conflict' THEN 1 ELSE 0 END) as conflicts
        FROM trades
    ");
    $tradesStmt->execute();
    $tradesStats = $tradesStmt->fetch(PDO::FETCH_ASSOC);
    
    // إحصائيات الأحداث
    $eventsStmt = $connection->prepare("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN processed = 1 THEN 1 ELSE 0 END) as processed,
            SUM(CASE WHEN processed = 0 THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN processing_attempts > 3 THEN 1 ELSE 0 END) as failed
        FROM enhanced_contract_events
    ");
    $eventsStmt->execute();
    $eventsStats = $eventsStmt->fetch(PDO::FETCH_ASSOC);
    
    // آخر الأحداث المعالجة
    $lastEventsStmt = $connection->prepare("
        SELECT 
            event_type,
            contract_type,
            transaction_hash,
            block_number,
            processed,
            event_timestamp,
            created_at
        FROM enhanced_contract_events
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $lastEventsStmt->execute();
    $lastEvents = $lastEventsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تحويل القيم المنطقية
    foreach ($lastEvents as &$event) {
        $event['processed'] = (bool)$event['processed'];
    }
    
    sendSuccessResponse([
        'sync_status' => [
            'offers' => [
                'total' => (int)$offersStats['total'],
                'synced' => (int)$offersStats['synced'],
                'pending' => (int)$offersStats['pending'],
                'failed' => (int)$offersStats['failed'],
                'conflicts' => (int)$offersStats['conflicts'],
                'sync_rate' => $offersStats['total'] > 0 ? round(($offersStats['synced'] / $offersStats['total']) * 100, 2) : 0
            ],
            'trades' => [
                'total' => (int)$tradesStats['total'],
                'synced' => (int)$tradesStats['synced'],
                'pending' => (int)$tradesStats['pending'],
                'failed' => (int)$tradesStats['failed'],
                'conflicts' => (int)$tradesStats['conflicts'],
                'sync_rate' => $tradesStats['total'] > 0 ? round(($tradesStats['synced'] / $tradesStats['total']) * 100, 2) : 0
            ],
            'events' => [
                'total' => (int)$eventsStats['total'],
                'processed' => (int)$eventsStats['processed'],
                'pending' => (int)$eventsStats['pending'],
                'failed' => (int)$eventsStats['failed'],
                'processing_rate' => $eventsStats['total'] > 0 ? round(($eventsStats['processed'] / $eventsStats['total']) * 100, 2) : 0
            ]
        ],
        'last_events' => $lastEvents,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * جلب أحداث العقد الذكي
 */
function getContractEvents($connection) {
    $networkId = $_GET['network_id'] ?? null;
    $contractType = $_GET['contract_type'] ?? null;
    $eventType = $_GET['event_type'] ?? null;
    $processed = $_GET['processed'] ?? null;
    
    // معاملات الترقيم
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(100, max(1, intval($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    // بناء شروط البحث
    $whereConditions = [];
    $params = [];
    
    if ($networkId) {
        $whereConditions[] = "e.network_id = ?";
        $params[] = $networkId;
    }
    
    if ($contractType) {
        $whereConditions[] = "e.contract_type = ?";
        $params[] = $contractType;
    }
    
    if ($eventType) {
        $whereConditions[] = "e.event_type = ?";
        $params[] = $eventType;
    }
    
    if ($processed !== null) {
        $whereConditions[] = "e.processed = ?";
        $params[] = $processed === '1' ? 1 : 0;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // استعلام العد الإجمالي
    $countQuery = "SELECT COUNT(*) as total FROM enhanced_contract_events e $whereClause";
    $countStmt = $connection->prepare($countQuery);
    $countStmt->execute($params);
    $totalEvents = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // استعلام البيانات
    $dataQuery = "
        SELECT 
            e.*,
            n.network_name,
            n.network_symbol
        FROM enhanced_contract_events e
        LEFT JOIN supported_networks n ON e.network_id = n.id
        $whereClause
        ORDER BY e.created_at DESC
        LIMIT ? OFFSET ?
    ";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $dataStmt = $connection->prepare($dataQuery);
    $dataStmt->execute($params);
    $events = $dataStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // تحويل البيانات
    foreach ($events as &$event) {
        $event['processed'] = (bool)$event['processed'];
        $event['event_data'] = $event['event_data'] ? json_decode($event['event_data'], true) : null;
        $event['amount'] = $event['amount'] ? (float)$event['amount'] : null;
        $event['fee_amount'] = $event['fee_amount'] ? (float)$event['fee_amount'] : null;
        $event['price_per_token'] = $event['price_per_token'] ? (float)$event['price_per_token'] : null;
    }
    
    // حساب معلومات الترقيم
    $totalPages = ceil($totalEvents / $limit);
    
    sendSuccessResponse([
        'events' => $events,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_events' => (int)$totalEvents,
            'per_page' => $limit
        ]
    ]);
}

/**
 * جلب المزامنات الفاشلة
 */
function getFailedSyncs($connection) {
    // العروض الفاشلة
    $failedOffersStmt = $connection->prepare("
        SELECT 
            'offer' as entity_type,
            id as entity_id,
            sync_status,
            sync_attempts,
            last_sync_error,
            last_sync_at,
            created_at
        FROM offers 
        WHERE sync_status IN ('failed', 'conflict')
        ORDER BY last_sync_at DESC
        LIMIT 20
    ");
    $failedOffersStmt->execute();
    $failedOffers = $failedOffersStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // الصفقات الفاشلة
    $failedTradesStmt = $connection->prepare("
        SELECT 
            'trade' as entity_type,
            id as entity_id,
            sync_status,
            sync_attempts,
            last_sync_error,
            last_sync_at,
            created_at
        FROM trades 
        WHERE sync_status IN ('failed', 'conflict')
        ORDER BY last_sync_at DESC
        LIMIT 20
    ");
    $failedTradesStmt->execute();
    $failedTrades = $failedTradesStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // الأحداث الفاشلة
    $failedEventsStmt = $connection->prepare("
        SELECT 
            'event' as entity_type,
            id as entity_id,
            'failed' as sync_status,
            processing_attempts as sync_attempts,
            error_message as last_sync_error,
            processed_at as last_sync_at,
            created_at
        FROM enhanced_contract_events 
        WHERE processed = 0 AND processing_attempts > 3
        ORDER BY created_at DESC
        LIMIT 20
    ");
    $failedEventsStmt->execute();
    $failedEvents = $failedEventsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // دمج النتائج
    $allFailed = array_merge($failedOffers, $failedTrades, $failedEvents);
    
    // ترتيب حسب التاريخ
    usort($allFailed, function($a, $b) {
        return strtotime($b['last_sync_at'] ?? $b['created_at']) - strtotime($a['last_sync_at'] ?? $a['created_at']);
    });
    
    sendSuccessResponse([
        'failed_syncs' => array_slice($allFailed, 0, 50),
        'summary' => [
            'failed_offers' => count($failedOffers),
            'failed_trades' => count($failedTrades),
            'failed_events' => count($failedEvents),
            'total_failed' => count($allFailed)
        ]
    ]);
}

/**
 * معالجة طلبات المزامنة
 */
function handleSyncRequest() {
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            sendErrorResponse('بيانات غير صحيحة', 400);
            return;
        }
        
        $action = $input['action'] ?? null;
        
        switch ($action) {
            case 'sync_offer':
                syncOffer($connection, $input);
                break;
            case 'sync_trade':
                syncTrade($connection, $input);
                break;
            case 'process_event':
                processEvent($connection, $input);
                break;
            case 'retry_failed':
                retryFailedSyncs($connection, $input);
                break;
            default:
                sendErrorResponse('إجراء غير مدعوم', 400);
        }
        
    } catch (Exception $e) {
        sendErrorResponse('خطأ في معالجة طلب المزامنة: ' . $e->getMessage(), 500);
    }
}

/**
 * مزامنة عرض مع العقد الذكي
 */
function syncOffer($connection, $input) {
    $offerId = $input['offer_id'] ?? null;
    $blockchainTradeId = $input['blockchain_trade_id'] ?? null;
    $transactionHash = $input['transaction_hash'] ?? null;
    $contractStatus = $input['contract_status'] ?? 'created';
    
    if (!$offerId || !$blockchainTradeId || !$transactionHash) {
        sendErrorResponse('معرف العرض ومعرف البلوك تشين وهاش المعاملة مطلوبة', 400);
        return;
    }
    
    try {
        $stmt = $connection->prepare("
            UPDATE offers SET 
                blockchain_trade_id = ?,
                transaction_hash = ?,
                contract_status = ?,
                sync_status = 'synced',
                last_sync_at = CURRENT_TIMESTAMP,
                contract_created_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        
        $stmt->execute([$blockchainTradeId, $transactionHash, $contractStatus, $offerId]);
        
        if ($stmt->rowCount() > 0) {
            sendSuccessResponse([
                'message' => 'تم مزامنة العرض بنجاح',
                'offer_id' => $offerId,
                'blockchain_trade_id' => $blockchainTradeId
            ]);
        } else {
            sendErrorResponse('العرض غير موجود', 404);
        }
        
    } catch (Exception $e) {
        sendErrorResponse('خطأ في مزامنة العرض: ' . $e->getMessage(), 500);
    }
}

/**
 * مزامنة صفقة مع العقد الذكي
 */
function syncTrade($connection, $input) {
    $tradeId = $input['trade_id'] ?? null;
    $blockchainTradeId = $input['blockchain_trade_id'] ?? null;
    $transactionHash = $input['transaction_hash'] ?? null;
    $contractStatus = $input['contract_status'] ?? 'Created';
    $eventType = $input['event_type'] ?? 'join';
    
    if (!$tradeId || !$transactionHash) {
        sendErrorResponse('معرف الصفقة وهاش المعاملة مطلوبان', 400);
        return;
    }
    
    try {
        // تحديد الحقول حسب نوع الحدث
        $updateFields = [
            'sync_status = "synced"',
            'last_sync_at = CURRENT_TIMESTAMP'
        ];
        $params = [];
        
        if ($blockchainTradeId) {
            $updateFields[] = 'blockchain_trade_id = ?';
            $params[] = $blockchainTradeId;
        }
        
        $updateFields[] = 'contract_status = ?';
        $params[] = $contractStatus;
        
        switch ($eventType) {
            case 'join':
                $updateFields[] = 'join_transaction_hash = ?';
                $updateFields[] = 'contract_joined_at = CURRENT_TIMESTAMP';
                $params[] = $transactionHash;
                break;
            case 'payment_sent':
                $updateFields[] = 'payment_sent_transaction_hash = ?';
                $updateFields[] = 'contract_payment_sent_at = CURRENT_TIMESTAMP';
                $params[] = $transactionHash;
                break;
            case 'payment_confirmed':
                $updateFields[] = 'payment_confirmed_transaction_hash = ?';
                $updateFields[] = 'contract_payment_confirmed_at = CURRENT_TIMESTAMP';
                $params[] = $transactionHash;
                break;
            case 'complete':
                $updateFields[] = 'complete_transaction_hash = ?';
                $updateFields[] = 'contract_completed_at = CURRENT_TIMESTAMP';
                $params[] = $transactionHash;
                break;
            case 'dispute':
                $updateFields[] = 'dispute_transaction_hash = ?';
                $updateFields[] = 'contract_disputed_at = CURRENT_TIMESTAMP';
                $params[] = $transactionHash;
                break;
        }
        
        $params[] = $tradeId;
        
        $stmt = $connection->prepare("
            UPDATE trades SET " . implode(', ', $updateFields) . " WHERE id = ?
        ");
        
        $stmt->execute($params);
        
        if ($stmt->rowCount() > 0) {
            sendSuccessResponse([
                'message' => 'تم مزامنة الصفقة بنجاح',
                'trade_id' => $tradeId,
                'event_type' => $eventType
            ]);
        } else {
            sendErrorResponse('الصفقة غير موجودة', 404);
        }
        
    } catch (Exception $e) {
        sendErrorResponse('خطأ في مزامنة الصفقة: ' . $e->getMessage(), 500);
    }
}

/**
 * معالجة حدث من العقد الذكي
 */
function processEvent($connection, $input) {
    $eventId = $input['event_id'] ?? null;
    
    if (!$eventId) {
        sendErrorResponse('معرف الحدث مطلوب', 400);
        return;
    }
    
    try {
        $stmt = $connection->prepare("
            UPDATE enhanced_contract_events SET 
                processed = 1,
                processed_at = CURRENT_TIMESTAMP,
                processing_attempts = processing_attempts + 1
            WHERE id = ?
        ");
        
        $stmt->execute([$eventId]);
        
        if ($stmt->rowCount() > 0) {
            sendSuccessResponse([
                'message' => 'تم معالجة الحدث بنجاح',
                'event_id' => $eventId
            ]);
        } else {
            sendErrorResponse('الحدث غير موجود', 404);
        }
        
    } catch (Exception $e) {
        sendErrorResponse('خطأ في معالجة الحدث: ' . $e->getMessage(), 500);
    }
}

/**
 * إعادة محاولة المزامنات الفاشلة
 */
function retryFailedSyncs($connection, $input) {
    $entityType = $input['entity_type'] ?? null;
    $entityId = $input['entity_id'] ?? null;
    
    if (!$entityType || !$entityId) {
        sendErrorResponse('نوع الكيان ومعرفه مطلوبان', 400);
        return;
    }
    
    try {
        if ($entityType === 'offer') {
            $stmt = $connection->prepare("
                UPDATE offers SET 
                    sync_status = 'pending',
                    sync_attempts = 0,
                    last_sync_error = NULL
                WHERE id = ?
            ");
        } elseif ($entityType === 'trade') {
            $stmt = $connection->prepare("
                UPDATE trades SET 
                    sync_status = 'pending',
                    sync_attempts = 0,
                    last_sync_error = NULL
                WHERE id = ?
            ");
        } elseif ($entityType === 'event') {
            $stmt = $connection->prepare("
                UPDATE enhanced_contract_events SET 
                    processed = 0,
                    processing_attempts = 0,
                    error_message = NULL,
                    retry_after = NULL
                WHERE id = ?
            ");
        } else {
            sendErrorResponse('نوع كيان غير مدعوم', 400);
            return;
        }
        
        $stmt->execute([$entityId]);
        
        sendSuccessResponse([
            'message' => 'تم إعادة تعيين حالة المزامنة بنجاح',
            'entity_type' => $entityType,
            'entity_id' => $entityId
        ]);
        
    } catch (Exception $e) {
        sendErrorResponse('خطأ في إعادة المحاولة: ' . $e->getMessage(), 500);
    }
}

/**
 * تحديث حالة المزامنة
 */
function handleUpdateSyncStatus() {
    // سيتم تنفيذها لاحقاً
    sendErrorResponse('غير مدعوم حالياً', 501);
}

?>
