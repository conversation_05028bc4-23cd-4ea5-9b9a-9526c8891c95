<?php
/**
 * API endpoint لإدارة الصفقات
 * Trades Management API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";

// Include database configuration
require_once __DIR__ . '/../../config/database.php';

try {
    $db = new Database();
    $connection = $db->getConnection();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // جلب الصفقات
        $userId = $_GET['user_id'] ?? null;
        $tradeId = $_GET['trade_id'] ?? null;
        
        if ($tradeId) {
            // جلب صفقة محددة
            $stmt = $connection->prepare("
                SELECT
                    t.*,
                    o.offer_type,
                    o.currency,
                    'USDT' as stablecoin,
                    o.payment_methods,
                    o.terms,
                    seller.username as seller_username,
                    seller.full_name as seller_name,
                    seller.rating as seller_rating,
                    seller.total_trades as seller_trades,
                    seller.is_verified as seller_verified,
                    buyer.username as buyer_username,
                    buyer.full_name as buyer_name,
                    buyer.rating as buyer_rating,
                    buyer.total_trades as buyer_trades,
                    buyer.is_verified as buyer_verified
                FROM trades t
                JOIN offers o ON t.offer_id = o.id
                JOIN users seller ON t.seller_id = seller.id
                JOIN users buyer ON t.buyer_id = buyer.id
                WHERE t.id = ?
            ");
            
            $stmt->execute([$tradeId]);
            $trade = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$trade) {
                throw new Exception('الصفقة غير موجودة');
            }
            
            // تنسيق البيانات
            $trade['payment_methods'] = json_decode($trade['payment_methods'], true) ?: [];
            $trade['seller'] = [
                'id' => $trade['seller_id'],
                'username' => $trade['seller_username'],
                'full_name' => $trade['seller_name'],
                'rating' => floatval($trade['seller_rating']),
                'total_trades' => intval($trade['seller_trades']),
                'is_verified' => (bool)$trade['seller_verified']
            ];
            $trade['buyer'] = [
                'id' => $trade['buyer_id'],
                'username' => $trade['buyer_username'],
                'full_name' => $trade['buyer_name'],
                'rating' => floatval($trade['buyer_rating']),
                'total_trades' => intval($trade['buyer_trades']),
                'is_verified' => (bool)$trade['buyer_verified']
            ];
            
            // إزالة البيانات المكررة
            unset($trade['seller_username'], $trade['seller_name'], $trade['seller_rating'], 
                  $trade['seller_trades'], $trade['seller_verified'],
                  $trade['buyer_username'], $trade['buyer_name'], $trade['buyer_rating'], 
                  $trade['buyer_trades'], $trade['buyer_verified']);
            
            echo json_encode([
                'success' => true,
                'data' => $trade
            ]);
            
        } else {
            // جلب قائمة الصفقات
            $getAllTrades = $_GET['all'] ?? false;

            if (!$userId && !$getAllTrades) {
                throw new Exception('معرف المستخدم مطلوب أو استخدم all=true لجلب جميع الصفقات');
            }
            
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(50, max(10, intval($_GET['limit'] ?? 20)));
            $offset = ($page - 1) * $limit;
            
            $status = $_GET['status'] ?? 'all';
            
            // بناء الاستعلام
            $whereConditions = [];
            $params = [];

            if ($getAllTrades) {
                // جلب جميع الصفقات (للإدارة)
                $whereConditions[] = '1=1'; // شرط دائماً صحيح
            } else {
                // جلب صفقات المستخدم فقط
                $whereConditions[] = '(t.seller_id = ? OR t.buyer_id = ?)';
                $params = [$userId, $userId];
            }

            if ($status !== 'all') {
                $whereConditions[] = 't.status = ?';
                $params[] = $status;
            }

            $whereClause = implode(' AND ', $whereConditions);
            
            // الحصول على العدد الإجمالي
            $countStmt = $connection->prepare("
                SELECT COUNT(*) as total
                FROM trades t
                WHERE $whereClause
            ");
            $countStmt->execute($params);
            $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // جلب الصفقات
            $stmt = $connection->prepare("
                SELECT 
                    t.*,
                    o.offer_type,
                    o.currency,
                    'USDT' as stablecoin,
                    seller.username as seller_username,
                    seller.full_name as seller_name,
                    seller.rating as seller_rating,
                    buyer.username as buyer_username,
                    buyer.full_name as buyer_name,
                    buyer.rating as buyer_rating
                FROM trades t
                JOIN offers o ON t.offer_id = o.id
                JOIN users seller ON t.seller_id = seller.id
                JOIN users buyer ON t.buyer_id = buyer.id
                WHERE $whereClause
                ORDER BY t.created_at DESC
                LIMIT ? OFFSET ?
            ");
            
            $params[] = $limit;
            $params[] = $offset;
            $stmt->execute($params);
            $trades = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // تنسيق البيانات
            foreach ($trades as &$trade) {
                $trade['seller'] = [
                    'id' => $trade['seller_id'],
                    'username' => $trade['seller_username'],
                    'full_name' => $trade['seller_name'],
                    'rating' => floatval($trade['seller_rating'])
                ];
                $trade['buyer'] = [
                    'id' => $trade['buyer_id'],
                    'username' => $trade['buyer_username'],
                    'full_name' => $trade['buyer_name'],
                    'rating' => floatval($trade['buyer_rating'])
                ];
                
                // إزالة البيانات المكررة
                unset($trade['seller_username'], $trade['seller_name'], $trade['seller_rating'],
                      $trade['buyer_username'], $trade['buyer_name'], $trade['buyer_rating']);
            }
            
            echo json_encode([
                'success' => true,
                'data' => $trades,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => intval($totalCount),
                    'total_pages' => ceil($totalCount / $limit)
                ]
            ]);
        }
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // إنشاء صفقة جديدة (الانضمام لعرض)
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('بيانات غير صحيحة');
        }
        
        $requiredFields = ['offer_id', 'buyer_id', 'amount'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                throw new Exception("الحقل $field مطلوب");
            }
        }
        
        // التحقق من العرض
        $stmt = $connection->prepare("
            SELECT o.*, u.id as seller_id 
            FROM offers o 
            JOIN users u ON o.user_id = u.id 
            WHERE o.id = ? AND o.is_active = 1
        ");
        $stmt->execute([$input['offer_id']]);
        $offer = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!offer) {
            throw new Exception('العرض غير موجود أو غير نشط');
        }
        
        // التحقق من أن المشتري ليس البائع
        if ($offer['seller_id'] == $input['buyer_id']) {
            throw new Exception('لا يمكنك شراء عرضك الخاص');
        }
        
        // إنشاء الصفقة مع دعم العقد الذكي
        $stmt = $connection->prepare("
            INSERT INTO trades (
                offer_id, seller_id, buyer_id, amount, price, currency,
                stablecoin, total_value, platform_fee, net_amount,
                payment_method, selected_payment_methods, status,
                blockchain_trade_id, contract_status, transaction_hash,
                sync_status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ");

        $amount = floatval($input['amount']);
        $price = floatval($offer['price']);
        $totalValue = $amount * $price;
        $platformFee = $totalValue * 0.015; // 1.5%
        $netAmount = $amount - ($amount * 0.015);

        // تحضير طرق الدفع المختارة
        $selectedPaymentMethods = [];
        if (isset($input['payment_method'])) {
            $selectedPaymentMethods = [$input['payment_method']];
        }

        // معلومات العقد الذكي
        $blockchainTradeId = $input['blockchain_trade_id'] ?? null;
        $contractStatus = $input['contract_status'] ?? 'pending';
        $transactionHash = $input['transaction_hash'] ?? null;
        $syncStatus = $blockchainTradeId ? 'synced' : 'pending';

        $stmt->execute([
            $input['offer_id'],
            $offer['seller_id'],
            $input['buyer_id'],
            $amount,
            $price,
            $offer['currency'],
            $offer['stablecoin'],
            $totalValue,
            $platformFee,
            $netAmount,
            $input['payment_method'] ?? null,
            json_encode($selectedPaymentMethods),
            'created',
            $blockchainTradeId,
            $contractStatus,
            $transactionHash,
            $syncStatus
        ]);

        $tradeId = $connection->lastInsertId();

        // إذا تم توفير معرف العقد الذكي، قم بتحديث معلومات إضافية
        if ($blockchainTradeId) {
            $stmt = $connection->prepare("
                UPDATE trades
                SET contract_created_at = CURRENT_TIMESTAMP,
                    last_sync_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $stmt->execute([$tradeId]);
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إنشاء الصفقة بنجاح',
            'data' => ['id' => $tradeId]
        ]);
        
    } else {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات'
    ]);
    
    error_log('Database error in trades/index.php: ' . $e->getMessage());
}
?>
