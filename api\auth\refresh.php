<?php
/**
 * API endpoint لتجديد رمز المصادقة
 * Refresh Token API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";

// التحقق من طريقة الطلب
validateRequestMethod(['POST']);

// تحميل متغيرات البيئة
if (file_exists(__DIR__ . '/../../.env')) {
    $lines = file(__DIR__ . '/../../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

// مفتاح JWT من متغيرات البيئة
$jwtSecret = $_ENV['JWT_SECRET'] ?? 'default-secret-key';

/**
 * إنشاء JWT token
 */
function createJWT($payload, $secret) {
    $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
    $payload = json_encode($payload);
    
    $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
    $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
    
    $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $secret, true);
    $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
    
    return $base64Header . "." . $base64Payload . "." . $base64Signature;
}

/**
 * التحقق من صحة JWT token
 */
function verifyJWT($token, $secret) {
    $parts = explode('.', $token);
    if (count($parts) !== 3) {
        return false;
    }
    
    list($header, $payload, $signature) = $parts;
    
    $validSignature = hash_hmac('sha256', $header . "." . $payload, $secret, true);
    $validSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($validSignature));
    
    if (!hash_equals($signature, $validSignature)) {
        return false;
    }
    
    $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $payload)), true);
    
    // التحقق من انتهاء صلاحية التوكن
    if (isset($payload['exp']) && $payload['exp'] < time()) {
        return false;
    }
    
    return $payload;
}

try {
    // الحصول على البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendErrorResponse('بيانات غير صحيحة');
    }
    
    $refreshToken = $input['refreshToken'] ?? '';
    
    if (empty($refreshToken)) {
        sendErrorResponse('رمز التجديد مطلوب');
    }
    
    // التحقق من صحة رمز التجديد
    $tokenPayload = verifyJWT($refreshToken, $jwtSecret);
    
    if (!$tokenPayload) {
        sendErrorResponse('رمز التجديد غير صحيح أو منتهي الصلاحية');
    }
    
    // التحقق من أن هذا رمز تجديد وليس رمز وصول
    if (!isset($tokenPayload['type']) || $tokenPayload['type'] !== 'refresh') {
        sendErrorResponse('نوع الرمز غير صحيح');
    }
    
    $userId = $tokenPayload['user_id'];
    
    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // التحقق من وجود المستخدم وحالته
    $stmt = $connection->prepare("
        SELECT id, wallet_address, username, email, full_name,
               is_verified, is_admin, is_active, rating, total_trades,
               completed_trades, total_volume, created_at, last_login
        FROM users
        WHERE id = ? AND is_active = 1
    ");
    
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        sendErrorResponse('المستخدم غير موجود أو غير نشط');
    }
    
    // إنشاء رموز جديدة
    $currentTime = time();
    $accessTokenExpiry = $currentTime + (15 * 60); // 15 دقيقة
    $refreshTokenExpiry = $currentTime + (7 * 24 * 60 * 60); // 7 أيام
    
    // رمز الوصول الجديد
    $accessTokenPayload = [
        'user_id' => $user['id'],
        'username' => $user['username'],
        'email' => $user['email'],
        'is_admin' => $user['is_admin'],
        'is_verified' => $user['is_verified'],
        'type' => 'access',
        'iat' => $currentTime,
        'exp' => $accessTokenExpiry
    ];
    
    // رمز التجديد الجديد
    $refreshTokenPayload = [
        'user_id' => $user['id'],
        'type' => 'refresh',
        'iat' => $currentTime,
        'exp' => $refreshTokenExpiry
    ];
    
    $newAccessToken = createJWT($accessTokenPayload, $jwtSecret);
    $newRefreshToken = createJWT($refreshTokenPayload, $jwtSecret);
    
    // تسجيل نشاط تجديد الرمز
    try {
        $checkTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
        $checkTable->execute();
        
        if ($checkTable->rowCount() > 0) {
            $checkColumns = $connection->prepare("SHOW COLUMNS FROM activity_logs LIKE 'entity_type'");
            $checkColumns->execute();
            
            if ($checkColumns->rowCount() > 0) {
                $logStmt = $connection->prepare("
                    INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data)
                    VALUES (?, 'token_refresh', 'user', ?, ?, ?, ?)
                ");
                
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                $refreshData = json_encode([
                    'refresh_time' => date('Y-m-d H:i:s'),
                    'access_token_expiry' => date('Y-m-d H:i:s', $accessTokenExpiry),
                    'refresh_token_expiry' => date('Y-m-d H:i:s', $refreshTokenExpiry)
                ]);
                
                $logStmt->execute([$user['id'], $user['id'], $ipAddress, $userAgent, $refreshData]);
            }
        }
    } catch (Exception $logError) {
        // تجاهل أخطاء تسجيل النشاط
        error_log('Activity log error in refresh: ' . $logError->getMessage());
    }
    
    // إرجاع الرموز الجديدة
    sendSuccessResponse([
        'accessToken' => $newAccessToken,
        'refreshToken' => $newRefreshToken,
        'expiresIn' => 900, // 15 دقيقة بالثواني
        'tokenType' => 'Bearer',
        'user' => [
            'id' => $user['id'],
            'walletAddress' => $user['wallet_address'],
            'username' => $user['username'],
            'email' => $user['email'],
            'fullName' => $user['full_name'],
            'isVerified' => (bool)$user['is_verified'],
            'isAdmin' => (bool)$user['is_admin'],
            'rating' => (float)$user['rating'],
            'totalTrades' => (int)$user['total_trades'],
            'completedTrades' => (int)$user['completed_trades'],
            'totalVolume' => (float)$user['total_volume'],
            'joinDate' => $user['created_at'],
            'lastLogin' => $user['last_login']
        ]
    ], 'تم تجديد الرمز بنجاح');
    
} catch (Exception $e) {
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    error_log('Database error in refresh.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}
?>
