/**
 * خدمة إدارة التداولات المتقدمة للمدراء
 * Advanced Trade Management Service for Admins
 * 
 * يوفر وظائف شاملة للتعامل مع APIs إدارة التداولات
 * Provides comprehensive functions for interacting with trade management APIs
 */

import { API_BASE_URL } from '@/constants';

// أنواع البيانات
export interface TradeFilters {
  status?: string | string[];
  date_from?: string;
  date_to?: string;
  user_id?: number;
  amount_min?: number;
  amount_max?: number;
  currency?: string;
  search?: string;
}

export interface Pagination {
  page: number;
  limit: number;
}

export interface PaginationResponse {
  current_page: number;
  per_page: number;
  total: number;
  total_pages: number;
}

export interface TradeUser {
  id: number;
  username: string;
  wallet_address: string;
  rating: number;
  total_trades: number;
}

export interface AdminTrade {
  id: number;
  type: 'buy' | 'sell';
  amount: number;
  price: number;
  currency: string;
  status: 'created' | 'joined' | 'payment_sent' | 'payment_received' | 'completed' | 'cancelled' | 'disputed';
  seller_id: number;
  buyer_id: number;
  payment_method: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  dispute_created_at?: string;
  dispute_resolved_at?: string;
  dispute_reason?: string;
  dispute_resolution?: string;
  dispute_resolved_by?: number;
  
  // معلومات المستخدمين
  seller_username: string;
  seller_wallet: string;
  seller_rating: number;
  seller_total_trades: number;
  buyer_username: string;
  buyer_wallet: string;
  buyer_rating: number;
  buyer_total_trades: number;
  
  // إحصائيات إضافية
  notes_count: number;
  resolutions_count: number;
}

export interface TradeListResponse {
  trades: AdminTrade[];
  pagination: PaginationResponse;
}

export interface TradeStatistics {
  total_trades: number;
  completed_trades: number;
  disputed_trades: number;
  cancelled_trades: number;
  active_trades: number;
  total_volume: number;
  avg_completion_time: number;
  dispute_rate: number;
  completion_rate: number;
}

export interface TradeNote {
  id: number;
  trade_id: number;
  admin_id: number;
  note: string;
  is_internal: boolean;
  visibility: 'internal' | 'public' | 'parties_only';
  created_at: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  error_en?: string;
  message?: string;
  message_en?: string;
  pagination?: PaginationResponse;
}

class TradesAdminService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/admin`;
  }

  /**
   * الحصول على قائمة التداولات مع فلترة متقدمة
   * Get trades list with advanced filtering
   */
  async fetchTrades(
    filters: TradeFilters = {}, 
    pagination: Pagination = { page: 1, limit: 50 }
  ): Promise<TradeListResponse> {
    try {
      const params = new URLSearchParams();
      
      // إضافة معاملات الفلترة
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(`${key}[]`, v.toString()));
          } else {
            params.append(key, value.toString());
          }
        }
      });
      
      // إضافة معاملات الترقيم
      params.append('page', pagination.page.toString());
      params.append('limit', pagination.limit.toString());
      
      const response = await fetch(`${this.baseUrl}/trades-management.php?${params}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const result: ApiResponse<AdminTrade[]> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في جلب التداولات');
      }
      
      return {
        trades: result.data || [],
        pagination: result.pagination || {
          current_page: 1,
          per_page: 50,
          total: 0,
          total_pages: 0
        }
      };
    } catch (error) {
      console.error('Error fetching trades:', error);
      throw error;
    }
  }

  /**
   * تحديث حالة التداول
   * Update trade status
   */
  async updateTradeStatus(
    tradeId: number, 
    newStatus: string, 
    adminNotes?: string
  ): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/trades-management.php`, {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          trade_id: tradeId,
          new_status: newStatus,
          admin_notes: adminNotes
        }),
      });
      
      const result: ApiResponse<any> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في تحديث حالة التداول');
      }
    } catch (error) {
      console.error('Error updating trade status:', error);
      throw error;
    }
  }

  /**
   * إضافة ملاحظة إدارية
   * Add administrative note
   */
  async addTradeNote(
    tradeId: number,
    note: string,
    isInternal: boolean = true,
    visibility: 'internal' | 'public' | 'parties_only' = 'internal'
  ): Promise<number> {
    try {
      const response = await fetch(`${this.baseUrl}/trades-management.php`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          trade_id: tradeId,
          note,
          is_internal: isInternal,
          visibility
        }),
      });
      
      const result: ApiResponse<{ note_id: number }> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في إضافة الملاحظة');
      }
      
      return result.data?.note_id || 0;
    } catch (error) {
      console.error('Error adding trade note:', error);
      throw error;
    }
  }

  /**
   * الحصول على إحصائيات التداولات
   * Get trade statistics
   */
  async getTradeStatistics(dateRange?: { start: string; end: string }): Promise<TradeStatistics> {
    try {
      const params = new URLSearchParams();
      params.append('action', 'statistics');
      
      if (dateRange) {
        params.append('date_from', dateRange.start);
        params.append('date_to', dateRange.end);
      }
      
      const response = await fetch(`${this.baseUrl}/trades-management.php?${params}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const result: ApiResponse<TradeStatistics> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في جلب الإحصائيات');
      }
      
      return result.data || {
        total_trades: 0,
        completed_trades: 0,
        disputed_trades: 0,
        cancelled_trades: 0,
        active_trades: 0,
        total_volume: 0,
        avg_completion_time: 0,
        dispute_rate: 0,
        completion_rate: 0
      };
    } catch (error) {
      console.error('Error fetching trade statistics:', error);
      throw error;
    }
  }

  /**
   * تصدير بيانات التداولات
   * Export trades data
   */
  async exportTrades(
    filters: TradeFilters = {},
    format: 'csv' | 'excel' = 'csv'
  ): Promise<Blob> {
    try {
      const params = new URLSearchParams();
      params.append('action', 'export');
      params.append('format', format);
      
      // إضافة معاملات الفلترة
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(`${key}[]`, v.toString()));
          } else {
            params.append(key, value.toString());
          }
        }
      });
      
      const response = await fetch(`${this.baseUrl}/trades-management.php?${params}`, {
        method: 'GET',
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('فشل في تصدير البيانات');
      }
      
      return await response.blob();
    } catch (error) {
      console.error('Error exporting trades:', error);
      throw error;
    }
  }

  /**
   * مزامنة التداول مع العقد الذكي
   * Sync trade with smart contract
   */
  async syncWithSmartContract(tradeId: number): Promise<any> {
    try {
      console.log(`Syncing trade ${tradeId} with smart contract...`);

      const response = await fetch(`${this.baseUrl}/sync-contract.php`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ trade_id: tradeId }),
      });

      const result: ApiResponse<any> = await response.json();

      if (!result.success) {
        throw new Error(result.error || result.error_en || 'فشل في مزامنة العقد الذكي');
      }

      return result.data;
    } catch (error) {
      console.error('Error syncing with smart contract:', error);
      throw error;
    }
  }

  /**
   * الحصول على حالات التداول المتاحة
   * Get available trade statuses
   */
  getAvailableStatuses(): Array<{ value: string; label_ar: string; label_en: string }> {
    return [
      { value: 'created', label_ar: 'تم الإنشاء', label_en: 'Created' },
      { value: 'joined', label_ar: 'تم الانضمام', label_en: 'Joined' },
      { value: 'payment_sent', label_ar: 'تم إرسال الدفع', label_en: 'Payment Sent' },
      { value: 'payment_received', label_ar: 'تم استلام الدفع', label_en: 'Payment Received' },
      { value: 'completed', label_ar: 'مكتمل', label_en: 'Completed' },
      { value: 'cancelled', label_ar: 'ملغي', label_en: 'Cancelled' },
      { value: 'disputed', label_ar: 'متنازع عليه', label_en: 'Disputed' }
    ];
  }

  /**
   * الحصول على العملات المتاحة
   * Get available currencies
   */
  getAvailableCurrencies(): Array<{ value: string; label: string }> {
    return [
      { value: 'USDT', label: 'USDT' },
      { value: 'SAR', label: 'ريال سعودي' },
      { value: 'AED', label: 'درهم إماراتي' },
      { value: 'KWD', label: 'دينار كويتي' },
      { value: 'QAR', label: 'ريال قطري' },
      { value: 'BHD', label: 'دينار بحريني' },
      { value: 'USD', label: 'دولار أمريكي' },
      { value: 'EUR', label: 'يورو' }
    ];
  }
}

export const tradesAdminService = new TradesAdminService();
export default tradesAdminService;
