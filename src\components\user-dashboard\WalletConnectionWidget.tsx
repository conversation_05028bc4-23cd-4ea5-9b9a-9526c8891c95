'use client';

import { useState, useEffect } from 'react';
import { 
  Wallet, 
  ExternalLink, 
  Copy, 
  RefreshCw, 
  AlertCircle, 
  CheckCircle,
  Zap,
  Shield,
  TrendingUp,
  Eye,
  EyeOff
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { walletService } from '@/services/walletService';
import { notificationService } from '@/services/notificationService';
import { detectMetaMask, detectTrustWallet } from '@/utils/walletDetection';

interface WalletInfo {
  address: string;
  balance: string;
  network: string;
  isConnected: boolean;
  chainId?: string;
}

interface WalletConnectionWidgetProps {
  showBalance?: boolean;
  showNetwork?: boolean;
  showActions?: boolean;
  compact?: boolean;
  className?: string;
}

export default function WalletConnectionWidget({
  showBalance = true,
  showNetwork = true,
  showActions = true,
  compact = false,
  className = ''
}: WalletConnectionWidgetProps) {
  const { t, formatCurrency } = useUserDashboardTranslation();
  const { user } = useAuth();

  const [walletInfo, setWalletInfo] = useState<WalletInfo | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showWalletOptions, setShowWalletOptions] = useState(false);
  const [error, setError] = useState('');
  const [showBalanceVisible, setShowBalanceVisible] = useState(true);

  // تحميل معلومات المحفظة عند التحميل
  useEffect(() => {
    loadWalletInfo();
  }, []);

  // تحميل معلومات المحفظة
  const loadWalletInfo = async () => {
    try {
      const info = await walletService.getWalletInfo();
      setWalletInfo(info);
      setError('');
    } catch (error: any) {
      console.error('خطأ في تحميل معلومات المحفظة:', error);
      setError(error.message || 'خطأ في تحميل معلومات المحفظة');
    }
  };

  // ربط المحفظة
  const handleConnectWallet = async (walletType: 'metamask' | 'trustwallet') => {
    setIsConnecting(true);
    setError('');

    try {
      // التحقق من توفر المحفظة
      const walletStatus = walletType === 'metamask' ? detectMetaMask() : detectTrustWallet();
      
      if (!walletStatus.isInstalled) {
        throw new Error(`${walletType === 'metamask' ? 'MetaMask' : 'Trust Wallet'} غير مثبت`);
      }

      if (!walletStatus.isAvailable) {
        throw new Error(`${walletType === 'metamask' ? 'MetaMask' : 'Trust Wallet'} غير متاح`);
      }

      // الاتصال الفعلي
      const info = await walletService.connectWallet(walletType);
      setWalletInfo(info);
      setShowWalletOptions(false);
      
      notificationService.success('تم ربط المحفظة بنجاح');
    } catch (error: any) {
      const errorMessage = error.message || 'فشل في ربط المحفظة';
      setError(errorMessage);
      notificationService.error(errorMessage);
    } finally {
      setIsConnecting(false);
    }
  };

  // قطع الاتصال
  const handleDisconnect = () => {
    try {
      walletService.disconnectWallet();
      setWalletInfo(null);
      setError('');
      notificationService.success('تم قطع اتصال المحفظة');
    } catch (error: any) {
      const errorMessage = error.message || 'فشل في قطع الاتصال';
      setError(errorMessage);
      notificationService.error(errorMessage);
    }
  };

  // تحديث المعلومات
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await loadWalletInfo();
      notificationService.success('تم تحديث معلومات المحفظة');
    } catch (error: any) {
      setError(error.message || 'فشل في تحديث المعلومات');
    } finally {
      setIsRefreshing(false);
    }
  };

  // نسخ العنوان
  const handleCopyAddress = () => {
    if (walletInfo?.address) {
      navigator.clipboard.writeText(walletInfo.address);
      notificationService.success('تم نسخ عنوان المحفظة');
    }
  };

  // فتح في المستكشف
  const openInExplorer = () => {
    if (walletInfo?.address) {
      const explorerUrl = walletInfo.network?.includes('Testnet')
        ? `https://testnet.bscscan.com/address/${walletInfo.address}`
        : `https://bscscan.com/address/${walletInfo.address}`;
      window.open(explorerUrl, '_blank');
    }
  };

  // تنسيق العنوان
  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  // إذا لم تكن المحفظة متصلة
  if (!walletInfo?.isConnected) {
    if (compact) {
      return (
        <div className={`bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center">
              <Wallet className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                ربط المحفظة
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                اربط محفظتك للبدء
              </p>
            </div>
            <button
              onClick={() => handleConnectWallet('metamask')}
              disabled={isConnecting}
              className="flex items-center space-x-2 rtl:space-x-reverse bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg transition-colors disabled:opacity-50 text-sm"
            >
              <span>ربط</span>
              {isConnecting && <RefreshCw className="w-3 h-3 animate-spin" />}
            </button>
          </div>
          {error && (
            <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <span className="text-xs text-red-600 dark:text-red-400">{error}</span>
            </div>
          )}
        </div>
      );
    }

    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
            <Wallet className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          </div>

          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            ربط المحفظة
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            اربط محفظتك الرقمية للبدء في التداول
          </p>

          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
                <span className="text-sm text-red-600 dark:text-red-400">{error}</span>
              </div>
            </div>
          )}

          <div className="space-y-3">
            <button
              onClick={() => handleConnectWallet('metamask')}
              disabled={isConnecting}
              className="w-full flex items-center justify-center space-x-3 rtl:space-x-reverse bg-orange-500 hover:bg-orange-600 text-white px-4 py-3 rounded-lg transition-colors disabled:opacity-50"
            >
              <span className="text-lg">🦊</span>
              <span>ربط MetaMask</span>
              {isConnecting && <RefreshCw className="w-4 h-4 animate-spin" />}
            </button>

            <button
              onClick={() => handleConnectWallet('trustwallet')}
              disabled={isConnecting}
              className="w-full flex items-center justify-center space-x-3 rtl:space-x-reverse bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg transition-colors disabled:opacity-50"
            >
              <span className="text-lg">🛡️</span>
              <span>ربط Trust Wallet</span>
              {isConnecting && <RefreshCw className="w-4 h-4 animate-spin" />}
            </button>
          </div>

          <p className="text-xs text-gray-500 dark:text-gray-400 mt-4">
            محفظتك آمنة ومحمية بالكامل
          </p>
        </div>
      </div>
    );
  }

  // إذا كانت المحفظة متصلة
  if (compact) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className="w-8 h-8 bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 rounded-full flex items-center justify-center">
            <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {showBalanceVisible ? `${parseFloat(walletInfo.balance).toFixed(2)} BNB` : '••••••••'}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
              {formatAddress(walletInfo.address)}
            </p>
          </div>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <button
              onClick={() => setShowBalanceVisible(!showBalanceVisible)}
              className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              {showBalanceVisible ? <Eye className="w-3 h-3" /> : <EyeOff className="w-3 h-3" />}
            </button>
            <button
              onClick={handleCopyAddress}
              className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <Copy className="w-3 h-3" />
            </button>
          </div>
        </div>
        {error && (
          <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <span className="text-xs text-red-600 dark:text-red-400">{error}</span>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className="w-10 h-10 bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 rounded-full flex items-center justify-center">
            <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              المحفظة متصلة
            </h3>
            {showNetwork && (
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {walletInfo.network}
              </p>
            )}
          </div>
        </div>

        {showActions && (
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              title="تحديث"
            >
              <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </button>
          </div>
        )}
      </div>

      {/* معلومات المحفظة */}
      <div className="space-y-4">
        {/* العنوان */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            عنوان المحفظة
          </label>
          <div className="flex items-center space-x-2 rtl:space-x-reverse bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <code className="flex-1 text-sm font-mono text-gray-900 dark:text-white">
              {formatAddress(walletInfo.address)}
            </code>
            <button
              onClick={handleCopyAddress}
              className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              title="نسخ العنوان"
            >
              <Copy className="w-4 h-4" />
            </button>
            <button
              onClick={openInExplorer}
              className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              title="عرض في المستكشف"
            >
              <ExternalLink className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* الرصيد */}
        {showBalance && (
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                الرصيد
              </label>
              <button
                onClick={() => setShowBalanceVisible(!showBalanceVisible)}
                className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                {showBalanceVisible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
              </button>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
              <div className="text-lg font-semibold text-gray-900 dark:text-white">
                {showBalanceVisible ? `${parseFloat(walletInfo.balance).toFixed(4)} BNB` : '••••••••'}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                ≈ {formatCurrency(parseFloat(walletInfo.balance) * 300, 'USD')} {/* سعر تقريبي */}
              </div>
            </div>
          </div>
        )}

        {/* الإجراءات */}
        {showActions && (
          <div className="flex space-x-3 rtl:space-x-reverse pt-2">
            <button
              onClick={handleDisconnect}
              className="flex-1 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400 px-4 py-2 rounded-lg transition-colors text-sm font-medium"
            >
              قطع الاتصال
            </button>
          </div>
        )}
      </div>

      {error && (
        <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
            <span className="text-sm text-red-600 dark:text-red-400">{error}</span>
          </div>
        </div>
      )}
    </div>
  );
}
