<?php
/**
 * API إدارة النزاعات من العقد الذكي للأدمن
 * Smart Contract Disputes Management API for Admin
 * 
 * يوفر وظائف لحل النزاعات مباشرة من العقد الذكي
 * Provides functions for resolving disputes directly from smart contract
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../includes/admin-auth.php';

/**
 * حل النزاع من خلال العقد الذكي
 */
function resolveDisputeOnContract($connection, $data) {
    try {
        // التحقق من وجود الصفقة
        $stmt = $connection->prepare("
            SELECT 
                t.*,
                u1.username as seller_username,
                u2.username as buyer_username
            FROM trades t
            LEFT JOIN users u1 ON t.seller_id = u1.id
            LEFT JOIN users u2 ON t.buyer_id = u2.id
            WHERE t.id = ? AND t.status = 'disputed'
        ");
        $stmt->execute([$data['trade_id']]);
        $trade = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$trade) {
            throw new Exception('الصفقة غير موجودة أو ليست في حالة نزاع');
        }
        
        // تحديث حالة الصفقة في قاعدة البيانات
        $stmt = $connection->prepare("
            UPDATE trades 
            SET 
                status = 'completed',
                resolved_by_admin_id = ?,
                resolution_reason = ?,
                resolution_type = ?,
                admin_notes = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        
        $resolutionType = $data['favor_buyer'] ? 'buyer_favor' : 'seller_favor';
        
        $stmt->execute([
            $data['admin_id'],
            $data['reason'],
            $resolutionType,
            $data['admin_notes'] ?? null,
            $data['trade_id']
        ]);
        
        // إدراج سجل في جدول dispute_resolutions
        $stmt = $connection->prepare("
            INSERT INTO dispute_resolutions 
            (trade_id, admin_id, resolution_type, reason, admin_notes, transaction_hash, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ");
        
        $stmt->execute([
            $data['trade_id'],
            $data['admin_id'],
            $resolutionType,
            $data['reason'],
            $data['admin_notes'] ?? null,
            $data['transaction_hash']
        ]);
        
        // تسجيل النشاط
        $stmt = $connection->prepare("
            INSERT INTO admin_activity_logs 
            (admin_id, action_type, target_type, target_id, data, created_at) 
            VALUES (?, 'dispute_resolution_contract', 'trade', ?, ?, CURRENT_TIMESTAMP)
        ");
        
        $activityData = json_encode([
            'resolution_type' => $resolutionType,
            'transaction_hash' => $data['transaction_hash'],
            'reason' => $data['reason'],
            'trade_amount' => $trade['amount'],
            'seller_username' => $trade['seller_username'],
            'buyer_username' => $trade['buyer_username']
        ]);
        
        $stmt->execute([
            $data['admin_id'],
            $data['trade_id'],
            $activityData
        ]);
        
        return [
            'trade_id' => $data['trade_id'],
            'resolution_type' => $resolutionType,
            'transaction_hash' => $data['transaction_hash'],
            'trade_details' => $trade
        ];
        
    } catch (Exception $e) {
        throw new Exception('خطأ في حل النزاع: ' . $e->getMessage());
    }
}

/**
 * تنفيذ النزاع التلقائي
 */
function executeAutoDispute($connection, $data) {
    try {
        // التحقق من وجود الصفقة وأهليتها للنزاع التلقائي
        $stmt = $connection->prepare("
            SELECT 
                t.*,
                u1.username as seller_username,
                u2.username as buyer_username,
                TIMESTAMPDIFF(HOUR, t.updated_at, NOW()) as hours_since_update
            FROM trades t
            LEFT JOIN users u1 ON t.seller_id = u1.id
            LEFT JOIN users u2 ON t.buyer_id = u2.id
            WHERE t.id = ? 
            AND t.status IN ('payment_sent', 'payment_confirmed')
            AND t.blockchain_trade_id IS NOT NULL
            AND TIMESTAMPDIFF(HOUR, t.updated_at, NOW()) >= 24
        ");
        $stmt->execute([$data['trade_id']]);
        $trade = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$trade) {
            throw new Exception('الصفقة غير مؤهلة للنزاع التلقائي');
        }
        
        // تحديث حالة الصفقة إلى نزاع
        $stmt = $connection->prepare("
            UPDATE trades 
            SET 
                status = 'disputed',
                dispute_reason = 'Auto dispute triggered by admin after 24 hours timeout',
                dispute_initiated_by = 'admin',
                dispute_initiated_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        
        $stmt->execute([$data['trade_id']]);
        
        // تسجيل النشاط
        $stmt = $connection->prepare("
            INSERT INTO admin_activity_logs 
            (admin_id, action_type, target_type, target_id, data, created_at) 
            VALUES (?, 'auto_dispute_triggered', 'trade', ?, ?, CURRENT_TIMESTAMP)
        ");
        
        $activityData = json_encode([
            'transaction_hash' => $data['transaction_hash'],
            'hours_since_update' => $trade['hours_since_update'],
            'trade_amount' => $trade['amount'],
            'seller_username' => $trade['seller_username'],
            'buyer_username' => $trade['buyer_username'],
            'auto_triggered' => true
        ]);
        
        $stmt->execute([
            $data['admin_id'],
            $data['trade_id'],
            $activityData
        ]);
        
        return [
            'trade_id' => $data['trade_id'],
            'transaction_hash' => $data['transaction_hash'],
            'hours_since_update' => $trade['hours_since_update'],
            'trade_details' => $trade
        ];
        
    } catch (Exception $e) {
        throw new Exception('خطأ في تنفيذ النزاع التلقائي: ' . $e->getMessage());
    }
}

/**
 * الحصول على الصفقات المؤهلة للنزاع التلقائي
 */
function getEligibleAutoDisputeTrades($connection, $limit = 50) {
    try {
        $stmt = $connection->prepare("
            SELECT 
                t.id,
                t.blockchain_trade_id,
                t.seller_id,
                t.buyer_id,
                t.amount,
                t.status,
                t.created_at,
                t.updated_at,
                TIMESTAMPDIFF(HOUR, t.updated_at, NOW()) as hours_since_update,
                u1.username as seller_username,
                u2.username as buyer_username
            FROM trades t
            LEFT JOIN users u1 ON t.seller_id = u1.id
            LEFT JOIN users u2 ON t.buyer_id = u2.id
            WHERE t.status IN ('payment_sent', 'payment_confirmed')
            AND t.blockchain_trade_id IS NOT NULL
            AND TIMESTAMPDIFF(HOUR, t.updated_at, NOW()) >= 24
            ORDER BY t.updated_at ASC
            LIMIT ?
        ");
        
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        throw new Exception('خطأ في جلب الصفقات المؤهلة للنزاع التلقائي: ' . $e->getMessage());
    }
}

// معالجة الطلبات
try {
    session_start();
    $admin_id = validateAdminSession();

    // Get database connection
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();

    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';
    
    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'eligible_trades':
                    $limit = (int)($_GET['limit'] ?? 50);
                    $trades = getEligibleAutoDisputeTrades($connection, $limit);
                    echo json_encode([
                        'success' => true,
                        'data' => $trades
                    ], JSON_UNESCAPED_UNICODE);
                    break;
                    
                default:
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'error' => 'نوع إجراء غير مدعوم',
                        'error_en' => 'Unsupported action type'
                    ], JSON_UNESCAPED_UNICODE);
                    break;
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['action'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'نوع الإجراء مطلوب',
                    'error_en' => 'Action type is required'
                ], JSON_UNESCAPED_UNICODE);
                break;
            }
            
            $input['admin_id'] = $admin_id; // إضافة معرف الأدمن
            
            switch ($input['action']) {
                case 'resolve_dispute':
                    $result = resolveDisputeOnContract($connection, $input);
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم حل النزاع بنجاح من خلال العقد الذكي',
                        'message_en' => 'Dispute resolved successfully through smart contract',
                        'data' => $result
                    ], JSON_UNESCAPED_UNICODE);
                    break;
                    
                case 'auto_dispute':
                    $result = executeAutoDispute($connection, $input);
                    echo json_encode([
                        'success' => true,
                        'message' => 'تم تنفيذ النزاع التلقائي بنجاح',
                        'message_en' => 'Auto dispute executed successfully',
                        'data' => $result
                    ], JSON_UNESCAPED_UNICODE);
                    break;
                    
                default:
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'error' => 'نوع إجراء غير مدعوم',
                        'error_en' => 'Unsupported action type'
                    ], JSON_UNESCAPED_UNICODE);
                    break;
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'error' => 'Method not allowed'
            ]);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
    
    error_log('Error in contract-disputes.php: ' . $e->getMessage());
}
?>
