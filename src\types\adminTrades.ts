/**
 * أنواع البيانات لإدارة التداولات المتقدمة
 * Advanced Trade Management Data Types
 */

// أنواع البيانات الأساسية
export type TradeStatus = 
  | 'created' 
  | 'joined' 
  | 'payment_sent' 
  | 'payment_received' 
  | 'completed' 
  | 'cancelled' 
  | 'disputed';

export type TradeType = 'buy' | 'sell';

export type Currency = 'USDT' | 'SAR' | 'AED' | 'KWD' | 'QAR' | 'BHD' | 'USD' | 'EUR';

// واجهات المستخدمين
export interface TradeUser {
  id: number;
  username: string;
  wallet_address: string;
  email?: string;
  phone?: string;
  rating: number;
  total_trades: number;
  completed_trades: number;
  is_verified: boolean;
  is_online: boolean;
}

// واجهة الصفقة المتقدمة للمدراء
export interface AdminTrade {
  id: number;
  type: TradeType;
  amount: number;
  price: number;
  total_amount: number;
  currency: Currency;
  status: TradeStatus;
  contract_status?: string;
  
  // معرفات المستخدمين
  seller_id: number;
  buyer_id: number;
  
  // تفاصيل الدفع
  payment_method: string;
  payment_details?: any;
  selected_payment_methods?: string[];
  
  // حالات التأكيد
  seller_confirmed: boolean;
  buyer_confirmed: boolean;
  
  // تفاصيل النزاع
  dispute_reason?: string;
  dispute_created_at?: string;
  dispute_resolved_at?: string;
  dispute_resolved_by?: number;
  dispute_resolution?: string;
  
  // التواريخ
  created_at: string;
  updated_at: string;
  completed_at?: string;
  
  // معلومات المستخدمين (من الـ JOIN)
  seller_username: string;
  seller_wallet: string;
  seller_rating: number;
  seller_total_trades: number;
  buyer_username: string;
  buyer_wallet: string;
  buyer_rating: number;
  buyer_total_trades: number;
  
  // إحصائيات إضافية
  notes_count: number;
  resolutions_count: number;
  message_count?: number;
  
  // معلومات العقد الذكي
  blockchain_trade_id?: number;
  transaction_hash?: string;
  escrow_amount?: number;
  platform_fee?: number;
  net_amount?: number;
  last_sync_at?: string;
}

// فلاتر البحث والتصفية
export interface TradeFilters {
  status?: TradeStatus | TradeStatus[];
  type?: TradeType;
  currency?: Currency;
  date_from?: string;
  date_to?: string;
  amount_min?: number;
  amount_max?: number;
  user_id?: number;
  seller_id?: number;
  buyer_id?: number;
  search_term?: string;
  payment_method?: string;
  has_dispute?: boolean;
  is_verified_users_only?: boolean;
}

// معاملات الترقيم
export interface Pagination {
  page: number;
  limit: number;
}

export interface PaginationResponse {
  current_page: number;
  per_page: number;
  total: number;
  total_pages: number;
  has_next_page: boolean;
  has_prev_page: boolean;
}

// استجابة قائمة التداولات
export interface TradeListResponse {
  trades: AdminTrade[];
  pagination: PaginationResponse;
  filters_applied: TradeFilters;
  total_stats: {
    total_volume: number;
    avg_trade_size: number;
    completion_rate: number;
    dispute_rate: number;
  };
}

// إحصائيات التداولات
export interface TradeStatistics {
  // الإحصائيات الأساسية
  total_trades: number;
  completed_trades: number;
  disputed_trades: number;
  cancelled_trades: number;
  active_trades: number;
  
  // الحجم والقيم
  total_volume: number;
  avg_trade_size: number;
  largest_trade: number;
  smallest_trade: number;
  
  // الأوقات
  avg_completion_time: number; // بالدقائق
  fastest_completion: number;
  slowest_completion: number;
  
  // النسب المئوية
  completion_rate: number;
  dispute_rate: number;
  cancellation_rate: number;
  
  // إحصائيات يومية/أسبوعية
  daily_volume?: Array<{
    date: string;
    volume: number;
    trade_count: number;
  }>;
  
  // إحصائيات حسب العملة
  currency_breakdown?: Array<{
    currency: Currency;
    trade_count: number;
    total_volume: number;
    avg_amount: number;
  }>;
  
  // إحصائيات حسب الحالة
  status_breakdown?: Array<{
    status: TradeStatus;
    count: number;
    percentage: number;
  }>;
}

// ملاحظة إدارية
export interface TradeNote {
  id: number;
  trade_id: number;
  admin_id: number;
  admin_username: string;
  note: string;
  is_internal: boolean;
  visibility: 'internal' | 'public' | 'parties_only';
  created_at: string;
  updated_at?: string;
}

// إجراء إداري على الصفقة
export interface TradeAction {
  type: 'status_update' | 'add_note' | 'resolve_dispute' | 'cancel' | 'force_complete';
  trade_id: number;
  admin_id: number;
  details: any;
  reason?: string;
  notes?: string;
}

// تحديث حالة الصفقة
export interface TradeStatusUpdate {
  trade_id: number;
  old_status: TradeStatus;
  new_status: TradeStatus;
  admin_notes?: string;
  reason?: string;
  notify_users?: boolean;
}

// خيارات التصدير
export interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  filters?: TradeFilters;
  date_range?: {
    start: string;
    end: string;
  };
  include_user_details?: boolean;
  include_payment_details?: boolean;
  include_dispute_details?: boolean;
  include_notes?: boolean;
}

// نتيجة التصدير
export interface ExportResult {
  success: boolean;
  file_url?: string;
  file_name?: string;
  file_size?: number;
  download_expires_at?: string;
  error?: string;
}

// حالات التحميل والأخطاء
export interface LoadingState {
  isLoading: boolean;
  isRefreshing: boolean;
  isExporting: boolean;
  error?: string;
  lastUpdated?: string;
}

// إعدادات العرض
export interface DisplaySettings {
  items_per_page: number;
  show_user_details: boolean;
  show_payment_details: boolean;
  show_dispute_details: boolean;
  auto_refresh_interval: number; // بالثواني
  default_filters: TradeFilters;
  column_visibility: {
    [key: string]: boolean;
  };
}

// خيارات الفرز
export interface SortOptions {
  field: keyof AdminTrade;
  direction: 'asc' | 'desc';
}

// استجابة API عامة
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  error_en?: string;
  message?: string;
  message_en?: string;
  pagination?: PaginationResponse;
  timestamp?: string;
}

// حالة مكون إدارة التداولات
export interface TradeManagementState {
  trades: AdminTrade[];
  selectedTrades: number[];
  filters: TradeFilters;
  pagination: Pagination;
  paginationResponse?: PaginationResponse;
  statistics?: TradeStatistics;
  loading: LoadingState;
  displaySettings: DisplaySettings;
  sortOptions: SortOptions;
  selectedTrade?: AdminTrade;
  showFilters: boolean;
  showExportModal: boolean;
  showBulkActions: boolean;
}

// أحداث مكون إدارة التداولات
export interface TradeManagementEvents {
  onTradeSelect: (trade: AdminTrade) => void;
  onTradeUpdate: (tradeId: number, updates: Partial<AdminTrade>) => void;
  onStatusChange: (tradeId: number, newStatus: TradeStatus, notes?: string) => void;
  onAddNote: (tradeId: number, note: string, visibility: 'internal' | 'public' | 'parties_only') => void;
  onExport: (options: ExportOptions) => void;
  onRefresh: () => void;
  onFiltersChange: (filters: TradeFilters) => void;
  onPaginationChange: (pagination: Pagination) => void;
  onSortChange: (sort: SortOptions) => void;
  onBulkAction: (action: string, tradeIds: number[]) => void;
}
