<?php
/**
 * API إدارة التداولات المتقدمة للمدراء
 * Advanced Trade Management API for Admins
 * 
 * يوفر وظائف شاملة لإدارة التداولات والنزاعات
 * Provides comprehensive functions for trade and dispute management
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../../config/database.php';

// التحقق من صحة الجلسة والصلاحيات الإدارية
function validateAdminSession() {
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
        http_response_code(403);
        echo json_encode([
            'success' => false,
            'error' => 'غير مصرح لك بالوصول إلى هذا المورد',
            'error_en' => 'Access denied. Admin privileges required.'
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
    return $_SESSION['user_id'];
}

// تسجيل نشاط المدير
function logAdminActivity($connection, $admin_id, $action_type, $target_type, $target_id, $details = null) {
    try {
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        $stmt = $connection->prepare("
            INSERT INTO admin_activity_logs 
            (admin_id, action_type, target_type, target_id, details, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $details_json = $details ? json_encode($details, JSON_UNESCAPED_UNICODE) : null;
        $stmt->execute([$admin_id, $action_type, $target_type, $target_id, $details_json, $ip_address, $user_agent]);
    } catch (Exception $e) {
        error_log("Failed to log admin activity: " . $e->getMessage());
    }
}

// الحصول على التداولات مع فلترة متقدمة
function getTradesWithFilters($connection, $filters = [], $pagination = []) {
    $where_conditions = [];
    $params = [];
    
    // بناء شروط الفلترة
    if (!empty($filters['status'])) {
        if (is_array($filters['status'])) {
            $placeholders = str_repeat('?,', count($filters['status']) - 1) . '?';
            $where_conditions[] = "t.status IN ($placeholders)";
            $params = array_merge($params, $filters['status']);
        } else {
            $where_conditions[] = "t.status = ?";
            $params[] = $filters['status'];
        }
    }
    
    if (!empty($filters['date_from'])) {
        $where_conditions[] = "t.created_at >= ?";
        $params[] = $filters['date_from'];
    }
    
    if (!empty($filters['date_to'])) {
        $where_conditions[] = "t.created_at <= ?";
        $params[] = $filters['date_to'] . ' 23:59:59';
    }
    
    if (!empty($filters['user_id'])) {
        $where_conditions[] = "(t.seller_id = ? OR t.buyer_id = ?)";
        $params[] = $filters['user_id'];
        $params[] = $filters['user_id'];
    }
    
    if (!empty($filters['amount_min'])) {
        $where_conditions[] = "t.amount >= ?";
        $params[] = $filters['amount_min'];
    }
    
    if (!empty($filters['amount_max'])) {
        $where_conditions[] = "t.amount <= ?";
        $params[] = $filters['amount_max'];
    }
    
    if (!empty($filters['currency'])) {
        $where_conditions[] = "t.currency = ?";
        $params[] = $filters['currency'];
    }
    
    if (!empty($filters['search_term'])) {
        $where_conditions[] = "(t.id LIKE ? OR seller.username LIKE ? OR buyer.username LIKE ? OR seller.wallet_address LIKE ? OR buyer.wallet_address LIKE ?)";
        $search_term = '%' . $filters['search_term'] . '%';
        $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term, $search_term]);
    }
    
    // بناء الاستعلام الأساسي
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // حساب العدد الإجمالي
    $count_query = "
        SELECT COUNT(*) as total
        FROM trades t
        LEFT JOIN users seller ON t.seller_id = seller.id
        LEFT JOIN users buyer ON t.buyer_id = buyer.id
        $where_clause
    ";
    
    $count_stmt = $connection->prepare($count_query);
    $count_stmt->execute($params);
    $total_count = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // إعداد الترقيم
    $page = $pagination['page'] ?? 1;
    $limit = $pagination['limit'] ?? 50;
    $offset = ($page - 1) * $limit;
    
    // الاستعلام الرئيسي
    $query = "
        SELECT 
            t.*,
            seller.username as seller_username,
            seller.wallet_address as seller_wallet,
            seller.rating as seller_rating,
            seller.total_trades as seller_total_trades,
            buyer.username as buyer_username,
            buyer.wallet_address as buyer_wallet,
            buyer.rating as buyer_rating,
            buyer.total_trades as buyer_total_trades,
            (SELECT COUNT(*) FROM admin_trade_notes WHERE trade_id = t.id) as notes_count,
            (SELECT COUNT(*) FROM dispute_resolutions WHERE trade_id = t.id) as resolutions_count
        FROM trades t
        LEFT JOIN users seller ON t.seller_id = seller.id
        LEFT JOIN users buyer ON t.buyer_id = buyer.id
        $where_clause
        ORDER BY t.created_at DESC
        LIMIT ? OFFSET ?
    ";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $connection->prepare($query);
    $stmt->execute($params);
    $trades = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'trades' => $trades,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $limit,
            'total' => $total_count,
            'total_pages' => ceil($total_count / $limit)
        ]
    ];
}

// تحديث حالة التداول
function updateTradeStatus($connection, $trade_id, $new_status, $admin_id, $admin_notes = null) {
    try {
        $connection->beginTransaction();
        
        // الحصول على الحالة الحالية
        $stmt = $connection->prepare("SELECT status FROM trades WHERE id = ?");
        $stmt->execute([$trade_id]);
        $current_trade = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$current_trade) {
            throw new Exception('الصفقة غير موجودة');
        }
        
        $old_status = $current_trade['status'];
        
        // تحديث حالة الصفقة
        $stmt = $connection->prepare("
            UPDATE trades 
            SET status = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        ");
        $stmt->execute([$new_status, $trade_id]);
        
        // إضافة ملاحظة إدارية إذا تم توفيرها
        if ($admin_notes) {
            $stmt = $connection->prepare("
                INSERT INTO admin_trade_notes (trade_id, admin_id, note, is_internal, visibility) 
                VALUES (?, ?, ?, TRUE, 'internal')
            ");
            $stmt->execute([$trade_id, $admin_id, $admin_notes]);
        }
        
        // تسجيل النشاط
        logAdminActivity($connection, $admin_id, 'trade_status_update', 'trade', $trade_id, [
            'old_status' => $old_status,
            'new_status' => $new_status,
            'admin_notes' => $admin_notes
        ]);
        
        $connection->commit();
        return true;
        
    } catch (Exception $e) {
        $connection->rollBack();
        throw $e;
    }
}

// إضافة ملاحظة إدارية
function addTradeNote($connection, $trade_id, $admin_id, $note, $is_internal = true, $visibility = 'internal') {
    try {
        $stmt = $connection->prepare("
            INSERT INTO admin_trade_notes (trade_id, admin_id, note, is_internal, visibility) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$trade_id, $admin_id, $note, $is_internal, $visibility]);
        
        // تسجيل النشاط
        logAdminActivity($connection, $admin_id, 'trade_note_added', 'trade', $trade_id, [
            'note_type' => $visibility,
            'note_length' => strlen($note),
            'is_internal' => $is_internal
        ]);
        
        return $connection->lastInsertId();
        
    } catch (Exception $e) {
        throw $e;
    }
}

// الحصول على إحصائيات التداولات
function getTradeStatistics($connection, $date_range = null) {
    $where_clause = '';
    $params = [];
    
    if ($date_range) {
        $where_clause = "WHERE created_at BETWEEN ? AND ?";
        $params = [$date_range['start'], $date_range['end']];
    }
    
    $query = "
        SELECT 
            COUNT(*) as total_trades,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_trades,
            COUNT(CASE WHEN status = 'disputed' THEN 1 END) as disputed_trades,
            COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_trades,
            COUNT(CASE WHEN status IN ('created', 'joined', 'payment_sent', 'payment_received') THEN 1 END) as active_trades,
            SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_volume,
            AVG(CASE WHEN status = 'completed' AND completed_at IS NOT NULL 
                THEN TIMESTAMPDIFF(MINUTE, created_at, completed_at) END) as avg_completion_time,
            (COUNT(CASE WHEN status = 'disputed' THEN 1 END) * 100.0 / COUNT(*)) as dispute_rate,
            (COUNT(CASE WHEN status = 'completed' THEN 1 END) * 100.0 / COUNT(*)) as completion_rate
        FROM trades 
        $where_clause
    ";
    
    $stmt = $connection->prepare($query);
    $stmt->execute($params);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// معالجة الطلبات
try {
    session_start();
    $admin_id = validateAdminSession();

    // Get database connection
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();

    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['action']) && $_GET['action'] === 'export') {
                // تصدير البيانات - سيتم تنفيذه لاحقاً
                echo json_encode([
                    'success' => false,
                    'error' => 'وظيفة التصدير قيد التطوير',
                    'error_en' => 'Export functionality under development'
                ], JSON_UNESCAPED_UNICODE);
                break;
            }
            
            if (isset($_GET['action']) && $_GET['action'] === 'statistics') {
                $date_range = null;
                if (isset($_GET['date_from']) && isset($_GET['date_to'])) {
                    $date_range = [
                        'start' => $_GET['date_from'],
                        'end' => $_GET['date_to']
                    ];
                }
                
                $stats = getTradeStatistics($connection, $date_range);
                echo json_encode([
                    'success' => true,
                    'data' => $stats
                ], JSON_UNESCAPED_UNICODE);
                break;
            }
            
            // الحصول على قائمة التداولات مع الفلترة
            $filters = [
                'status' => $_GET['status'] ?? null,
                'date_from' => $_GET['date_from'] ?? null,
                'date_to' => $_GET['date_to'] ?? null,
                'user_id' => $_GET['user_id'] ?? null,
                'amount_min' => $_GET['amount_min'] ?? null,
                'amount_max' => $_GET['amount_max'] ?? null,
                'currency' => $_GET['currency'] ?? null,
                'search_term' => $_GET['search'] ?? null
            ];
            
            $pagination = [
                'page' => (int)($_GET['page'] ?? 1),
                'limit' => (int)($_GET['limit'] ?? 50)
            ];
            
            $result = getTradesWithFilters($connection, $filters, $pagination);
            
            echo json_encode([
                'success' => true,
                'data' => $result['trades'],
                'pagination' => $result['pagination']
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'PUT':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['trade_id']) || !isset($input['new_status'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'معرف الصفقة والحالة الجديدة مطلوبان',
                    'error_en' => 'Trade ID and new status are required'
                ], JSON_UNESCAPED_UNICODE);
                break;
            }
            
            updateTradeStatus(
                $connection, 
                $input['trade_id'], 
                $input['new_status'], 
                $admin_id, 
                $input['admin_notes'] ?? null
            );
            
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث حالة الصفقة بنجاح',
                'message_en' => 'Trade status updated successfully'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['trade_id']) || !isset($input['note'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'معرف الصفقة والملاحظة مطلوبان',
                    'error_en' => 'Trade ID and note are required'
                ], JSON_UNESCAPED_UNICODE);
                break;
            }
            
            $note_id = addTradeNote(
                $connection,
                $input['trade_id'],
                $admin_id,
                $input['note'],
                $input['is_internal'] ?? true,
                $input['visibility'] ?? 'internal'
            );
            
            echo json_encode([
                'success' => true,
                'data' => ['note_id' => $note_id],
                'message' => 'تم إضافة الملاحظة بنجاح',
                'message_en' => 'Note added successfully'
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'error' => 'طريقة الطلب غير مدعومة',
                'error_en' => 'Method not allowed'
            ], JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في الخادم: ' . $e->getMessage(),
        'error_en' => 'Server error: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
