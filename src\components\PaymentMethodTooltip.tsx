'use client';

import React, { useState } from 'react';
import { Info, X } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

interface PaymentMethodTooltipProps {
  methodId: string;
  methodName: string;
  icon: string;
  className?: string;
}

export default function PaymentMethodTooltip({
  methodId,
  methodName,
  icon,
  className = ''
}: PaymentMethodTooltipProps) {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const getMethodDescription = (id: string): string => {
    return t(`paymentMethods.description.${id}`) || 'وصف غير متوفر';
  };

  const getMethodFeatures = (id: string): string[] => {
    const features: { [key: string]: string[] } = {
      bankTransfer: [
        'آمن ومضمون',
        'يحتاج وقت أطول (1-3 أيام)',
        'رسوم منخفضة',
        'متوفر في جميع البنوك'
      ],
      quickTransfer: [
        'سريع (دقائق قليلة)',
        'رسوم متوسطة',
        'متوفر 24/7',
        'يحتاج تطبيق البنك'
      ],
      instantTransfer: [
        'فوري (ثوانٍ)',
        'رسوم أعلى',
        'متوفر دائماً',
        'تقنية حديثة'
      ],
      cash: [
        'لقاء مباشر',
        'بدون رسوم',
        'آمن في أماكن عامة',
        'تحقق من الهوية'
      ],
      paypal: [
        'عالمي ومقبول',
        'حماية المشتري',
        'رسوم معقولة',
        'سهل الاستخدام'
      ],
      westernUnion: [
        'تحويل دولي',
        'شبكة واسعة',
        'رسوم ثابتة',
        'موثوق عالمياً'
      ],
      skrill: [
        'محفظة أوروبية',
        'رسوم منخفضة',
        'سريع وآمن',
        'دعم عملات متعددة'
      ],
      wise: [
        'أسعار صرف حقيقية',
        'رسوم شفافة',
        'تحويل دولي',
        'سريع وموثوق'
      ]
    };

    return features[id] || ['ميزات غير محددة'];
  };

  return (
    <div className={`relative ${className}`}>
      {/* زر المعلومات */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
      >
        <Info className="w-4 h-4 ltr:mr-1 rtl:ml-1" />
        <span className="text-sm">معلومات</span>
      </button>

      {/* النافذة المنبثقة */}
      {isOpen && (
        <>
          {/* خلفية شفافة */}
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setIsOpen(false)}
          />
          
          {/* المحتوى */}
          <div className="absolute top-full ltr:left-0 rtl:right-0 mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-xl z-50 p-6">
            {/* الرأس */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <span className="text-2xl">{icon}</span>
                <h3 className="font-bold text-gray-900 dark:text-white text-lg">
                  {methodName}
                </h3>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* الوصف */}
            <div className="mb-4">
              <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                {getMethodDescription(methodId)}
              </p>
            </div>

            {/* الميزات */}
            <div className="mb-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                الميزات الرئيسية:
              </h4>
              <ul className="space-y-1">
                {getMethodFeatures(methodId).map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <span className="w-2 h-2 bg-blue-500 rounded-full ltr:mr-2 rtl:ml-2 flex-shrink-0"></span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            {/* تحذيرات أو نصائح */}
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
              <h5 className="font-medium text-yellow-800 dark:text-yellow-300 mb-1 text-sm">
                💡 نصيحة:
              </h5>
              <p className="text-yellow-700 dark:text-yellow-400 text-xs">
                {methodId === 'cash' && 'التقِ في مكان عام وآمن دائماً'}
                {methodId === 'bankTransfer' && 'تأكد من صحة بيانات الحساب قبل التحويل'}
                {methodId === 'paypal' && 'استخدم خدمة الأصدقاء والعائلة لتوفير الرسوم'}
                {methodId === 'westernUnion' && 'احتفظ برقم التحويل حتى تأكيد الاستلام'}
                {!['cash', 'bankTransfer', 'paypal', 'westernUnion'].includes(methodId) && 
                 'تأكد من صحة بيانات الحساب قبل إرسال الأموال'}
              </p>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

// مكون مبسط لعرض معلومات سريعة
export function PaymentMethodBadge({ 
  methodId, 
  methodName, 
  icon 
}: { 
  methodId: string; 
  methodName: string; 
  icon: string; 
}) {
  const { t } = useTranslation();

  const getMethodType = (id: string): { type: string; color: string } => {
    const types: { [key: string]: { type: string; color: string } } = {
      bankTransfer: { type: 'بنكي', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' },
      quickTransfer: { type: 'سريع', color: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' },
      instantTransfer: { type: 'فوري', color: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300' },
      cash: { type: 'نقدي', color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' },
      paypal: { type: 'رقمي', color: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300' },
      westernUnion: { type: 'دولي', color: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' }
    };

    return types[id] || { type: 'رقمي', color: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300' };
  };

  const methodType = getMethodType(methodId);

  return (
    <div className="inline-flex items-center bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-2 shadow-sm">
      <span className="text-lg ltr:mr-2 rtl:ml-2">{icon}</span>
      <div className="flex flex-col">
        <span className="font-medium text-gray-900 dark:text-white text-sm">
          {methodName}
        </span>
        <span className={`text-xs px-2 py-0.5 rounded-full ${methodType.color}`}>
          {methodType.type}
        </span>
      </div>
    </div>
  );
}
