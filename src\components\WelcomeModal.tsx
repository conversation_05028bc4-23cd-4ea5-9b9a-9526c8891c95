'use client';

import { useState, useEffect } from 'react';
import { X, CheckCircle, Wallet, Star, TrendingUp, ArrowRight } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuth } from '@/contexts/AuthContext';

interface WelcomeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function WelcomeModal({ isOpen, onClose }: WelcomeModalProps) {
  const { t } = useTranslation();
  const { user, isWalletConnected, connectWallet } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: t('welcome.steps.welcome.title'),
      description: t('welcome.steps.welcome.description'),
      icon: CheckCircle,
      color: 'text-green-500',
      bgColor: 'bg-green-100 dark:bg-green-900/30'
    },
    {
      title: t('welcome.steps.wallet.title'),
      description: t('welcome.steps.wallet.description'),
      icon: Wallet,
      color: 'text-blue-500',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30'
    },
    {
      title: t('welcome.steps.explore.title'),
      description: t('welcome.steps.explore.description'),
      icon: TrendingUp,
      color: 'text-purple-500',
      bgColor: 'bg-purple-100 dark:bg-purple-900/30'
    }
  ];

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onClose();
    }
  };

  const handleConnectWallet = async () => {
    try {
      await connectWallet();
      handleNext();
    } catch (error) {
      console.error('Error connecting wallet:', error);
    }
  };

  if (!isOpen) return null;

  const currentStepData = steps[currentStep];
  const Icon = currentStepData.icon;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden animate-in zoom-in-95 duration-300">
        {/* Header */}
        <div className="relative bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 p-6 text-white">
          <button
            onClick={onClose}
            className="absolute top-4 ltr:right-4 rtl:left-4 p-2 hover:bg-white/20 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
          
          <div className="text-center">
            <div className={`w-16 h-16 ${currentStepData.bgColor} rounded-full flex items-center justify-center mx-auto mb-4`}>
              <Icon className={`w-8 h-8 ${currentStepData.color}`} />
            </div>
            <h2 className="text-2xl font-bold mb-2">
              {currentStepData.title}
            </h2>
            <p className="text-blue-100 text-sm">
              {t('welcome.greeting', { name: user?.fullName || user?.username || t('header.user') })}
            </p>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="text-center mb-6">
            <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
              {currentStepData.description}
            </p>
          </div>

          {/* Step-specific content */}
          {currentStep === 0 && (
            <div className="space-y-4">
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-sm text-green-700 dark:text-green-300">
                    {t('welcome.benefits.secure')}
                  </span>
                </div>
              </div>
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <Star className="w-5 h-5 text-blue-500" />
                  <span className="text-sm text-blue-700 dark:text-blue-300">
                    {t('welcome.benefits.features')}
                  </span>
                </div>
              </div>
              <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <TrendingUp className="w-5 h-5 text-purple-500" />
                  <span className="text-sm text-purple-700 dark:text-purple-300">
                    {t('welcome.benefits.trading')}
                  </span>
                </div>
              </div>
            </div>
          )}

          {currentStep === 1 && (
            <div className="space-y-4">
              {isWalletConnected ? (
                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
                  <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-2" />
                  <p className="text-green-700 dark:text-green-300 font-medium">
                    {t('welcome.wallet.connected')}
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 text-center">
                    <Wallet className="w-8 h-8 text-orange-500 mx-auto mb-2" />
                    <p className="text-orange-700 dark:text-orange-300 text-sm">
                      {t('welcome.wallet.notConnected')}
                    </p>
                  </div>
                  <button
                    onClick={handleConnectWallet}
                    className="w-full btn btn-primary"
                  >
                    <Wallet className="w-4 h-4 ltr:mr-2 rtl:ml-2" />
                    {t('wallet.connect.button')}
                  </button>
                </div>
              )}
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                <a
                  href="/offers"
                  className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors group"
                >
                  <TrendingUp className="w-6 h-6 text-blue-500 mx-auto mb-2 group-hover:scale-110 transition-transform" />
                  <p className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                    {t('welcome.quickActions.browseOffers')}
                  </p>
                </a>
                <a
                  href="/create-offer"
                  className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors group"
                >
                  <Star className="w-6 h-6 text-green-500 mx-auto mb-2 group-hover:scale-110 transition-transform" />
                  <p className="text-sm text-green-700 dark:text-green-300 font-medium">
                    {t('welcome.quickActions.createOffer')}
                  </p>
                </a>
              </div>
            </div>
          )}

          {/* Progress indicator */}
          <div className="flex justify-center space-x-2 rtl:space-x-reverse mt-6 mb-4">
            {steps.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentStep
                    ? 'bg-blue-500'
                    : index < currentStep
                    ? 'bg-green-500'
                    : 'bg-gray-300 dark:bg-gray-600'
                }`}
              />
            ))}
          </div>

          {/* Actions */}
          <div className="flex justify-between items-center">
            <button
              onClick={onClose}
              className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 text-sm"
            >
              {t('welcome.skip')}
            </button>
            
            <button
              onClick={currentStep === 1 && !isWalletConnected ? handleConnectWallet : handleNext}
              className="btn btn-primary flex items-center space-x-2 rtl:space-x-reverse"
            >
              <span>
                {currentStep === steps.length - 1 
                  ? t('welcome.getStarted') 
                  : currentStep === 1 && !isWalletConnected
                  ? t('wallet.connect.button')
                  : t('welcome.next')
                }
              </span>
              <ArrowRight className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
