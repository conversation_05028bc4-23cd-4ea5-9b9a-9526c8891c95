import { useState, useEffect, useCallback } from 'react';

interface MarketPriceData {
  price: number;
  currency: string;
  stablecoin: string;
  timestamp: number;
  source: string;
  variation?: number;
}

interface UseMarketPriceOptions {
  currency: string;
  stablecoin: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseMarketPriceReturn {
  price: number | null;
  loading: boolean;
  error: string | null;
  data: MarketPriceData | null;
  refresh: () => Promise<void>;
  lastUpdated: Date | null;
}

// أسعار افتراضية للعملات
const DEFAULT_PRICES: Record<string, number> = {
  'SAR': 3.75,
  'AED': 3.67,
  'USD': 1.0,
  'EUR': 0.92,
  'KWD': 0.31,
  'QAR': 3.64,
  'BHD': 0.38,
  'OMR': 0.38,
  'JOD': 0.71,
  'EGP': 31.0,
  'MAD': 10.2,
  'LBP': 15000
};

export function useMarketPrice({
  currency,
  stablecoin,
  autoRefresh = false,
  refreshInterval = 60000 // دقيقة واحدة
}: UseMarketPriceOptions): UseMarketPriceReturn {
  const [price, setPrice] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<MarketPriceData | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchPrice = useCallback(async () => {
    if (!currency || !stablecoin) return;

    setLoading(true);
    setError(null);

    let controller: AbortController | null = null;
    let timeoutId: NodeJS.Timeout | null = null;

    try {
      controller = new AbortController();
      timeoutId = setTimeout(() => {
        if (controller && !controller.signal.aborted) {
          controller.abort('Request timeout');
        }
      }, 10000); // 10 ثوان timeout

      const response = await fetch(
        `/api/market/price.php?currency=${currency}&stablecoin=${stablecoin}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          signal: controller.signal,
        }
      );

      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success && result.data?.price) {
        const marketData: MarketPriceData = {
          price: result.data.price,
          currency: result.data.currency,
          stablecoin: result.data.stablecoin,
          timestamp: result.data.timestamp,
          source: result.data.source,
          variation: result.data.variation
        };

        setData(marketData);
        setPrice(result.data.price);
        setLastUpdated(new Date());
        setError(null);
      } else {
        throw new Error(result.error || 'فشل في جلب سعر السوق');
      }
    } catch (err: any) {
      // تنظيف timeout في حالة الخطأ
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      console.warn('Market price fetch failed, using fallback:', err.message);

      let errorMessage = 'خطأ في جلب سعر السوق';

      if (err.name === 'AbortError') {
        errorMessage = 'انتهت مهلة الطلب';
      } else if (err.name === 'TypeError' && err.message.includes('Failed to fetch')) {
        errorMessage = 'خطأ في الاتصال بالخادم';
      } else if (err.message) {
        errorMessage = err.message;
      }

      // لا نعرض خطأ للمستخدم، فقط نستخدم السعر الافتراضي
      setError(null);

      // استخدام سعر افتراضي
      const defaultPrice = DEFAULT_PRICES[currency] || 1.0;
      const fallbackData: MarketPriceData = {
        price: defaultPrice,
        currency,
        stablecoin,
        timestamp: Date.now(),
        source: 'fallback'
      };

      setData(fallbackData);
      setPrice(defaultPrice);
      setLastUpdated(new Date());
    } finally {
      setLoading(false);
    }
  }, [currency, stablecoin]);

  // جلب السعر عند تغيير العملة
  useEffect(() => {
    if (currency && stablecoin) {
      fetchPrice();
    }
  }, [currency, stablecoin, fetchPrice]);

  // التحديث التلقائي
  useEffect(() => {
    if (!autoRefresh || !currency || !stablecoin) return;

    const interval = setInterval(() => {
      fetchPrice();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchPrice, currency, stablecoin]);

  return {
    price,
    loading,
    error,
    data,
    refresh: fetchPrice,
    lastUpdated
  };
}

// Hook مبسط للحصول على سعر فقط
export function useSimpleMarketPrice(currency: string, stablecoin: string = 'USDT'): number {
  const { price } = useMarketPrice({ currency, stablecoin });
  return price || DEFAULT_PRICES[currency] || 1.0;
}

// Hook للحصول على أسعار متعددة
export function useMultipleMarketPrices(
  currencies: string[],
  stablecoin: string = 'USDT'
): Record<string, number> {
  const [prices, setPrices] = useState<Record<string, number>>({});

  useEffect(() => {
    const fetchPrices = async () => {
      const newPrices: Record<string, number> = {};

      for (const currency of currencies) {
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => {
            if (!controller.signal.aborted) {
              controller.abort('Request timeout');
            }
          }, 5000); // 5 ثوان timeout للأسعار المتعددة

          const response = await fetch(
            `/api/market/price.php?currency=${currency}&stablecoin=${stablecoin}`,
            {
              signal: controller.signal,
            }
          );

          clearTimeout(timeoutId);

          if (response.ok) {
            const result = await response.json();
            if (result.success && result.data?.price) {
              newPrices[currency] = result.data.price;
            } else {
              newPrices[currency] = DEFAULT_PRICES[currency] || 1.0;
            }
          } else {
            newPrices[currency] = DEFAULT_PRICES[currency] || 1.0;
          }
        } catch (error: any) {
          console.warn(`Price fetch failed for ${currency}, using fallback:`, error.message);
          newPrices[currency] = DEFAULT_PRICES[currency] || 1.0;
        }
      }

      setPrices(newPrices);
    };

    if (currencies.length > 0) {
      fetchPrices();
    }
  }, [currencies, stablecoin]);

  return prices;
}

// دالة مساعدة لتنسيق السعر
export function formatMarketPrice(
  price: number,
  currency: string,
  options?: {
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
  }
): string {
  const { minimumFractionDigits = 2, maximumFractionDigits = 4 } = options || {};
  
  return new Intl.NumberFormat('ar-SA', {
    style: 'decimal',
    minimumFractionDigits,
    maximumFractionDigits,
  }).format(price) + ` ${currency}`;
}

export default useMarketPrice;
