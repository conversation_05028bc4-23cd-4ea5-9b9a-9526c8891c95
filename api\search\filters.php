<?php
/**
 * API endpoint للحصول على مرشحات البحث المتاحة
 * Search Filters API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";
require_once __DIR__ . "/../middleware/auth.php";

// التحقق من طريقة الطلب
validateRequestMethod(['GET']);

/**
 * الحصول على قائمة العملات المتاحة
 */
function getAvailableCurrencies($connection) {
    $stmt = $connection->prepare("
        SELECT DISTINCT currency, COUNT(*) as count
        FROM offers 
        WHERE status = 'active' AND currency IS NOT NULL
        GROUP BY currency
        ORDER BY count DESC, currency ASC
    ");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * الحصول على طرق الدفع المتاحة
 */
function getAvailablePaymentMethods($connection) {
    $stmt = $connection->prepare("
        SELECT DISTINCT payment_method, COUNT(*) as count
        FROM offers 
        WHERE status = 'active' AND payment_method IS NOT NULL
        GROUP BY payment_method
        ORDER BY count DESC, payment_method ASC
    ");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * الحصول على نطاقات الأسعار والمبالغ
 */
function getPriceRanges($connection) {
    $stmt = $connection->prepare("
        SELECT 
            MIN(amount) as min_amount,
            MAX(amount) as max_amount,
            AVG(amount) as avg_amount,
            MIN(price) as min_price,
            MAX(price) as max_price,
            AVG(price) as avg_price,
            COUNT(*) as total_offers
        FROM offers 
        WHERE status = 'active'
    ");
    $stmt->execute();
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * الحصول على إحصائيات المستخدمين
 */
function getUserStats($connection) {
    $stmt = $connection->prepare("
        SELECT 
            COUNT(DISTINCT u.id) as total_users,
            AVG(u.rating) as avg_rating,
            MIN(u.rating) as min_rating,
            MAX(u.rating) as max_rating,
            COUNT(DISTINCT CASE WHEN u.rating >= 4.0 THEN u.id END) as high_rated_users,
            COUNT(DISTINCT CASE WHEN u.total_trades >= 10 THEN u.id END) as experienced_users
        FROM users u
        JOIN offers o ON u.id = o.user_id
        WHERE u.is_active = 1 AND o.status = 'active'
    ");
    $stmt->execute();
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * الحصول على الفئات الشائعة
 */
function getPopularCategories($connection) {
    // يمكن إضافة جدول categories في المستقبل
    return [
        ['name' => 'عملات رقمية', 'count' => 0, 'slug' => 'crypto'],
        ['name' => 'عملات ورقية', 'count' => 0, 'slug' => 'fiat'],
        ['name' => 'تحويلات بنكية', 'count' => 0, 'slug' => 'bank'],
        ['name' => 'محافظ إلكترونية', 'count' => 0, 'slug' => 'wallet']
    ];
}

/**
 * الحصول على المرشحات المحفوظة للمستخدم
 */
function getSavedFilters($connection, $userId) {
    $stmt = $connection->prepare("
        SELECT id, name, filters, created_at, last_used
        FROM saved_search_filters 
        WHERE user_id = ?
        ORDER BY last_used DESC, created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$userId]);
    $filters = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return array_map(function($filter) {
        return [
            'id' => (int)$filter['id'],
            'name' => $filter['name'],
            'filters' => json_decode($filter['filters'], true),
            'createdAt' => $filter['created_at'],
            'lastUsed' => $filter['last_used']
        ];
    }, $filters);
}

try {
    // التحقق من المصادقة
    $auth = requireAuth();
    $userId = $auth->getCurrentUserId();
    $isAdmin = $auth->isAdmin();
    
    if (!$userId) {
        sendErrorResponse('المصادقة مطلوبة', 401);
    }
    
    // الحصول على اتصال قاعدة البيانات
    $db = DatabaseManager::getInstance()->getDatabase();
    $connection = $db->getConnection();
    
    // جمع جميع المرشحات المتاحة
    $currencies = getAvailableCurrencies($connection);
    $paymentMethods = getAvailablePaymentMethods($connection);
    $priceRanges = getPriceRanges($connection);
    $userStats = getUserStats($connection);
    $categories = getPopularCategories($connection);
    $savedFilters = getSavedFilters($connection, $userId);
    
    // مرشحات ثابتة
    $offerTypes = [
        ['value' => 'buy', 'label' => 'شراء', 'label_en' => 'Buy'],
        ['value' => 'sell', 'label' => 'بيع', 'label_en' => 'Sell']
    ];
    
    $statusOptions = [
        ['value' => 'active', 'label' => 'نشط', 'label_en' => 'Active'],
        ['value' => 'completed', 'label' => 'مكتمل', 'label_en' => 'Completed'],
        ['value' => 'cancelled', 'label' => 'ملغي', 'label_en' => 'Cancelled']
    ];
    
    $ratingRanges = [
        ['min' => 0, 'max' => 1, 'label' => '0-1 نجمة'],
        ['min' => 1, 'max' => 2, 'label' => '1-2 نجمة'],
        ['min' => 2, 'max' => 3, 'label' => '2-3 نجمة'],
        ['min' => 3, 'max' => 4, 'label' => '3-4 نجمة'],
        ['min' => 4, 'max' => 5, 'label' => '4-5 نجمة']
    ];
    
    $dateRanges = [
        ['value' => 'today', 'label' => 'اليوم', 'label_en' => 'Today'],
        ['value' => 'week', 'label' => 'هذا الأسبوع', 'label_en' => 'This Week'],
        ['value' => 'month', 'label' => 'هذا الشهر', 'label_en' => 'This Month'],
        ['value' => 'quarter', 'label' => 'هذا الربع', 'label_en' => 'This Quarter'],
        ['value' => 'year', 'label' => 'هذا العام', 'label_en' => 'This Year']
    ];
    
    // تنسيق العملات
    $formattedCurrencies = array_map(function($currency) {
        return [
            'value' => $currency['currency'],
            'label' => strtoupper($currency['currency']),
            'count' => (int)$currency['count']
        ];
    }, $currencies);
    
    // تنسيق طرق الدفع
    $formattedPaymentMethods = array_map(function($method) {
        return [
            'value' => $method['payment_method'],
            'label' => $method['payment_method'],
            'count' => (int)$method['count']
        ];
    }, $paymentMethods);
    
    // نطاقات الأسعار المقترحة
    $suggestedAmountRanges = [];
    $suggestedPriceRanges = [];
    
    if ($priceRanges['max_amount'] > 0) {
        $maxAmount = (float)$priceRanges['max_amount'];
        $step = $maxAmount / 5;
        
        for ($i = 0; $i < 5; $i++) {
            $min = $i * $step;
            $max = ($i + 1) * $step;
            $suggestedAmountRanges[] = [
                'min' => round($min, 2),
                'max' => round($max, 2),
                'label' => number_format($min, 2) . ' - ' . number_format($max, 2)
            ];
        }
    }
    
    if ($priceRanges['max_price'] > 0) {
        $maxPrice = (float)$priceRanges['max_price'];
        $step = $maxPrice / 5;
        
        for ($i = 0; $i < 5; $i++) {
            $min = $i * $step;
            $max = ($i + 1) * $step;
            $suggestedPriceRanges[] = [
                'min' => round($min, 2),
                'max' => round($max, 2),
                'label' => number_format($min, 2) . ' - ' . number_format($max, 2)
            ];
        }
    }
    
    sendSuccessResponse([
        'filters' => [
            'offerTypes' => $offerTypes,
            'currencies' => $formattedCurrencies,
            'paymentMethods' => $formattedPaymentMethods,
            'statusOptions' => $statusOptions,
            'ratingRanges' => $ratingRanges,
            'dateRanges' => $dateRanges,
            'categories' => $categories
        ],
        'ranges' => [
            'amount' => [
                'min' => (float)($priceRanges['min_amount'] ?? 0),
                'max' => (float)($priceRanges['max_amount'] ?? 0),
                'avg' => (float)($priceRanges['avg_amount'] ?? 0),
                'suggested' => $suggestedAmountRanges
            ],
            'price' => [
                'min' => (float)($priceRanges['min_price'] ?? 0),
                'max' => (float)($priceRanges['max_price'] ?? 0),
                'avg' => (float)($priceRanges['avg_price'] ?? 0),
                'suggested' => $suggestedPriceRanges
            ],
            'rating' => [
                'min' => (float)($userStats['min_rating'] ?? 0),
                'max' => (float)($userStats['max_rating'] ?? 5),
                'avg' => (float)($userStats['avg_rating'] ?? 0)
            ]
        ],
        'stats' => [
            'totalOffers' => (int)($priceRanges['total_offers'] ?? 0),
            'totalUsers' => (int)($userStats['total_users'] ?? 0),
            'highRatedUsers' => (int)($userStats['high_rated_users'] ?? 0),
            'experiencedUsers' => (int)($userStats['experienced_users'] ?? 0)
        ],
        'savedFilters' => $savedFilters,
        'userRole' => $isAdmin ? 'admin' : 'user'
    ], 'تم جلب مرشحات البحث بنجاح');
    
} catch (Exception $e) {
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    error_log('Database error in filters.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}
?>
