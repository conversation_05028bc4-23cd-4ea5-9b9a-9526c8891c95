<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'ikaros_p2p';
$username = 'root';
$password = '';

try {
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];

    $pdo = new PDO($dsn, $username, $password, $options);
    
    // الحصول على معاملات الفلترة
    $offer_type = $_GET['type'] ?? '';
    $currency = $_GET['currency'] ?? '';
    $stablecoin = $_GET['stablecoin'] ?? '';
    $payment_method = $_GET['payment_method'] ?? '';
    $verified_only = isset($_GET['verified_only']) && $_GET['verified_only'] === '1';
    $premium_only = isset($_GET['premium_only']) && $_GET['premium_only'] === '1';
    
    // بناء الاستعلام الأساسي
    $where_conditions = ['o.is_active = 1'];
    $params = [];
    
    // تطبيق الفلاتر
    if (!empty($offer_type) && $offer_type !== 'all') {
        $where_conditions[] = 'o.offer_type = :offer_type';
        $params[':offer_type'] = $offer_type;
    }
    
    if (!empty($currency) && $currency !== 'all') {
        $where_conditions[] = 'o.currency = :currency';
        $params[':currency'] = $currency;
    }
    
    if (!empty($stablecoin) && $stablecoin !== 'all') {
        $where_conditions[] = 'o.stablecoin = :stablecoin';
        $params[':stablecoin'] = $stablecoin;
    }
    
    if (!empty($payment_method)) {
        $where_conditions[] = 'JSON_CONTAINS(o.payment_methods, :payment_method)';
        $params[':payment_method'] = json_encode($payment_method);
    }
    
    if ($verified_only) {
        $where_conditions[] = 'u.is_verified = 1';
    }
    
    if ($premium_only) {
        $where_conditions[] = 'o.is_premium = 1';
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // استعلام الإحصائيات
    $stats_query = "
        SELECT 
            COUNT(*) as total_offers,
            COUNT(CASE WHEN o.offer_type = 'buy' THEN 1 END) as buy_offers,
            COUNT(CASE WHEN o.offer_type = 'sell' THEN 1 END) as sell_offers,
            AVG(o.price) as avg_price,
            SUM(o.amount) as total_volume,
            COUNT(CASE WHEN o.is_premium = 1 THEN 1 END) as premium_offers,
            COUNT(CASE WHEN u.is_verified = 1 THEN 1 END) as verified_traders
        FROM offers o 
        LEFT JOIN users u ON o.user_id = u.id 
        WHERE $where_clause
    ";
    
    $stmt = $pdo->prepare($stats_query);
    $stmt->execute($params);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // تحويل القيم إلى أرقام
    $stats['total_offers'] = (int)$stats['total_offers'];
    $stats['buy_offers'] = (int)$stats['buy_offers'];
    $stats['sell_offers'] = (int)$stats['sell_offers'];
    $stats['avg_price'] = (float)$stats['avg_price'];
    $stats['total_volume'] = (float)$stats['total_volume'];
    $stats['premium_offers'] = (int)$stats['premium_offers'];
    $stats['verified_traders'] = (int)$stats['verified_traders'];
    
    // إضافة إحصائيات إضافية
    $stats['active_filters'] = count(array_filter([
        $offer_type && $offer_type !== 'all',
        $currency && $currency !== 'all', 
        $stablecoin && $stablecoin !== 'all',
        $payment_method,
        $verified_only,
        $premium_only
    ]));
    
    echo json_encode([
        'success' => true,
        'data' => $stats
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في الخادم: ' . $e->getMessage()
    ]);
}
?>
