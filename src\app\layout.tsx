import type { Metada<PERSON> } from "next";
import { Cairo } from "next/font/google";
import "./globals.css";
import ToastProvider from "@/components/ToastProvider";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { AuthProvider } from "@/contexts/AuthContext";

// تحسين تحميل الخط لسرعة أكبر
const cairo = Cairo({
  variable: "--font-cairo",
  subsets: ["arabic"],
  weight: ["400", "500", "600", "700"],
  display: "swap",
  fallback: ["system-ui", "sans-serif"],
  preload: true,
  adjustFontFallback: false, // تحسين الأداء
});

export const metadata: Metadata = {
  title: "إيكاروس P2P - منصة تبادل العملات الرقمية",
  description: "منصة آمنة وموثوقة لتبادل عملة USDT بين الأفراد مباشرة مع ضمان العقود الذكية",
  keywords: "USDT, تبادل عملات رقمية, P2P, عقود ذكية, BSC",
  authors: [{ name: "فريق إيكاروس" }],
  robots: "index, follow",
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body
        className={`${cairo.variable} font-arabic antialiased bg-background text-foreground transition-colors duration-300`}
      >
        <ThemeProvider>
          <LanguageProvider>
            <AuthProvider>
              {children}
              <ToastProvider />
            </AuthProvider>
          </LanguageProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
