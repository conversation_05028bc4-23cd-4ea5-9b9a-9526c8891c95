'use client';

import React, { useState, useEffect } from 'react';
import { networkService, NetworkConfig } from '@/services/networkService';
import { contractService } from '@/services/contractService';
import { notificationService } from '@/services/notificationService';
import { useTranslation } from '@/hooks/useTranslation';
import { Settings, Wifi, WifiOff, AlertTriangle } from 'lucide-react';

interface NetworkSelectorProps {
  className?: string;
  showLabel?: boolean;
}

export default function NetworkSelector({ className = '', showLabel = true }: NetworkSelectorProps) {
  const { t } = useTranslation();
  const [currentNetwork, setCurrentNetwork] = useState<NetworkConfig | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);

  useEffect(() => {
    initializeNetwork();
    setupNetworkListener();

    return () => {
      networkService.removeNetworkChangeListener();
    };
  }, []);

  const initializeNetwork = async () => {
    try {
      const network = await networkService.detectCurrentNetwork();
      setCurrentNetwork(network);
      setIsConnected(network !== null);
    } catch (error) {
      console.error('Network initialization error:', error);
      setIsConnected(false);
    }
  };

  const setupNetworkListener = () => {
    networkService.onNetworkChange(async (network) => {
      setCurrentNetwork(network);
      setIsConnected(network !== null);
      
      if (network) {
        // إعادة تهيئة العقود عند تغيير الشبكة
        await contractService.reinitializeContracts();
        notificationService.success(
          t('network.switchedTo', { networkName: network.name })
        );
      } else {
        notificationService.error(t('network.unsupported'));
      }
    });
  };

  const handleNetworkSwitch = async (networkKey: string) => {
    setIsLoading(true);
    setShowDropdown(false);

    try {
      const success = await networkService.switchToNetwork(networkKey);
      if (success) {
        const network = networkService.getCurrentNetwork();
        setCurrentNetwork(network);
        setIsConnected(true);
        notificationService.success(
          t('network.switchedTo', { networkName: network?.name || 'Unknown' })
        );
      }
    } catch (error: any) {
      console.error('Network switch error:', error);
      notificationService.error(
        error.message || t('network.switchFailed')
      );
    } finally {
      setIsLoading(false);
    }
  };

  const getNetworkIcon = () => {
    if (!isConnected) return <WifiOff className="w-4 h-4 text-red-500" />;
    if (currentNetwork?.isTestnet) return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    return <Wifi className="w-4 h-4 text-green-500" />;
  };

  const getNetworkStatus = () => {
    if (!isConnected) return t('network.disconnected');
    if (currentNetwork?.isTestnet) return t('network.testnet');
    return t('network.mainnet');
  };

  const supportedNetworks = networkService.getSupportedNetworks();

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        disabled={isLoading}
        className="flex items-center gap-2 px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
      >
        {getNetworkIcon()}
        
        {showLabel && (
          <div className="flex flex-col items-start">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {currentNetwork?.name || t('network.disconnected')}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {getNetworkStatus()}
            </span>
          </div>
        )}
        
        <Settings className={`w-4 h-4 text-gray-500 transition-transform ${showDropdown ? 'rotate-180' : ''}`} />
      </button>

      {showDropdown && (
        <div className="absolute top-full ltr:left-0 rtl:right-0 mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50">
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">
              {t('network.selectNetwork')}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {t('network.selectNetworkDescription')}
            </p>
          </div>

          <div className="p-2">
            {supportedNetworks.map((network) => (
              <button
                key={network.chainIdHex}
                onClick={() => handleNetworkSwitch(
                  network.isTestnet ? 'BSC_TESTNET' : 'BSC_MAINNET'
                )}
                disabled={isLoading}
                className={`w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 ${
                  currentNetwork?.chainIdHex === network.chainIdHex
                    ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                    : ''
                }`}
              >
                <div className="flex-shrink-0">
                  {network.isTestnet ? (
                    <AlertTriangle className="w-5 h-5 text-yellow-500" />
                  ) : (
                    <Wifi className="w-5 h-5 text-green-500" />
                  )}
                </div>
                
                <div className="flex-1 text-left">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {network.name}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {network.isTestnet ? t('network.testnetDescription') : t('network.mainnetDescription')}
                  </div>
                </div>

                {currentNetwork?.chainIdHex === network.chainIdHex && (
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                )}
              </button>
            ))}
          </div>

          <div className="p-3 border-t border-gray-200 dark:border-gray-700">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-2 mb-1">
                <AlertTriangle className="w-3 h-3 text-yellow-500" />
                <span>{t('network.testnetNote')}</span>
              </div>
              <div className="flex items-center gap-2">
                <Wifi className="w-3 h-3 text-green-500" />
                <span>{t('network.mainnetNote')}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* خلفية شفافة لإغلاق القائمة */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  );
}
