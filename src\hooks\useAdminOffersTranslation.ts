import { useState, useEffect } from 'react';

interface AdminOffersTranslations {
  title: string;
  subtitle: string;
  tabs: {
    settings: string;
    plans: string;
    statistics: string;
    users: string;
  };
  settings: {
    title: string;
    monthly_free_offers: string;
    monthly_free_offers_desc: string;
    max_offer_amount: string;
    max_offer_amount_desc: string;
    min_offer_amount: string;
    min_offer_amount_desc: string;
    offer_time_limit: string;
    offer_time_limit_desc: string;
    require_verification: string;
    require_verification_desc: string;
    subscription_system: string;
    subscription_system_desc: string;
  };
  plans: {
    title: string;
    create_plan: string;
    edit_plan: string;
    plan_name: string;
    plan_name_ar: string;
    description: string;
    description_ar: string;
    monthly_offers: string;
    price_monthly: string;
    price_yearly: string;
    features: string;
    is_active: string;
    is_default: string;
    sort_order: string;
    subscribers: string;
    revenue: string;
  };
  statistics: {
    title: string;
    total_offers: string;
    active_offers: string;
    free_offers: string;
    premium_offers: string;
    monthly_offers_created: string;
    average_offers_per_user: string;
    subscription_revenue: string;
    conversion_rate: string;
    top_currencies: string;
    offer_trends: string;
  };
  users: {
    title: string;
    search_user: string;
    user_id: string;
    username: string;
    current_plan: string;
    offers_used: string;
    offers_limit: string;
    subscription_status: string;
    last_offer: string;
    actions: string;
    reset_limits: string;
    change_plan: string;
    view_offers: string;
  };
  actions: {
    save_settings: string;
    reset_defaults: string;
    export_data: string;
    import_data: string;
    bulk_actions: string;
    apply: string;
    cancel: string;
    confirm: string;
  };
  messages: {
    settings_saved: string;
    plan_created: string;
    plan_updated: string;
    plan_deleted: string;
    limits_reset: string;
    plan_changed: string;
    export_completed: string;
    import_completed: string;
  };
  validation: {
    required_field: string;
    invalid_number: string;
    invalid_price: string;
    plan_name_exists: string;
    min_greater_than_max: string;
  };
}

export const useAdminOffersTranslation = () => {
  const [translations, setTranslations] = useState<AdminOffersTranslations | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadTranslations = async () => {
      try {
        // استخدام اللغة العربية كافتراضي
        const lang = 'ar';
        const response = await fetch(`/locales/${lang}/admin-offers.json`);
        const data = await response.json();
        setTranslations(data);
      } catch (error) {
        console.error('Error loading admin offers translations:', error);
        // Fallback to English if Arabic fails
        try {
          const response = await fetch('/locales/en/admin-offers.json');
          const data = await response.json();
          setTranslations(data);
        } catch (fallbackError) {
          console.error('Error loading fallback translations:', fallbackError);
        }
      } finally {
        setLoading(false);
      }
    };

    loadTranslations();
  }, []);

  const t = (key: string): string => {
    if (!translations || loading) return key;
    
    const keys = key.split('.');
    let value: any = translations;
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return key; // Return key if translation not found
      }
    }
    
    return typeof value === 'string' ? value : key;
  };

  return t;
};
