import { PAYMENT_METHODS } from '@/constants';

// دالة للحصول على ترجمة طريقة الدفع
export const getPaymentMethodTranslation = (methodId: string, t: (key: string) => string): string => {
  const method = PAYMENT_METHODS.find(pm => pm.id === methodId);
  if (!method) return methodId;
  
  // إذا كان nameAr يحتوي على مفتاح ترجمة، استخدم t()
  if (method.nameAr.startsWith('paymentMethods.')) {
    return t(method.nameAr);
  }
  
  // وإلا أرجع النص مباشرة (للتوافق مع النصوص القديمة)
  return method.nameAr;
};

// دالة للحصول على جميع طرق الدفع مترجمة
export const getTranslatedPaymentMethods = (t: (key: string) => string) => {
  return PAYMENT_METHODS.map(method => ({
    ...method,
    translatedName: getPaymentMethodTranslation(method.id, t)
  }));
};

// دالة للحصول على أيقونة طريقة الدفع
export const getPaymentMethodIcon = (methodId: string): string => {
  const method = PAYMENT_METHODS.find(pm => pm.id === methodId);
  return method?.icon || '💳';
};
