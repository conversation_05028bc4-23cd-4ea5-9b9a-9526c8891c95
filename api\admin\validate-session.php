<?php
/**
 * API endpoint للتحقق من صحة جلسة الإدارة
 * Admin Session Validation API Endpoint
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Method not allowed'
    ]);
    exit();
}

// Include required files
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../middleware/admin_auth.php';
require_once __DIR__ . '/../helpers/ResponseHelper.php';

try {
    // Validate request method
    ResponseHelper::validateMethod(['POST']);

    // التحقق من المصادقة الإدارية
    $authResult = checkAdminAuth();

    if (!$authResult['success']) {
        ResponseHelper::validation(
            false,
            $authResult['error'],
            $authResult['error_en'] ?? 'Authentication failed'
        );
    }

    // إذا وصلنا هنا، فالجلسة صحيحة
    ResponseHelper::validation(
        true,
        'جلسة صحيحة',
        'Valid session',
        [
            'admin_id' => $authResult['admin_id'],
            'admin_role' => $authResult['admin_role'],
            'username' => $authResult['admin_username'],
            'permissions' => $authResult['permissions'] ?? []
        ]
    );
    
} catch (Exception $e) {
    error_log('Session validation error: ' . $e->getMessage());
    ResponseHelper::validation(false, 'خطأ في النظام', 'System error');
} catch (PDOException $e) {
    error_log('Database error in validate-session.php: ' . $e->getMessage());
    ResponseHelper::validation(false, 'خطأ في قاعدة البيانات', 'Database error');
}
?>
