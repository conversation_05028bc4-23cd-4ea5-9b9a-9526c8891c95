'use client';

import { ReactNode } from 'react';

interface ResponsiveCardProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  icon?: ReactNode;
  actions?: ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  border?: boolean;
  hover?: boolean;
  gradient?: boolean;
  mobileFullWidth?: boolean;
}

export default function ResponsiveCard({
  children,
  title,
  subtitle,
  icon,
  actions,
  className = '',
  padding = 'md',
  shadow = 'sm',
  border = true,
  hover = false,
  gradient = false,
  mobileFullWidth = true
}: ResponsiveCardProps) {
  const paddingClasses = {
    none: '',
    sm: 'p-3 sm:p-4',
    md: 'p-4 sm:p-6',
    lg: 'p-6 sm:p-8'
  };

  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg'
  };

  const baseClasses = `
    bg-white dark:bg-gray-800 
    rounded-lg sm:rounded-xl 
    ${border ? 'border border-gray-200 dark:border-gray-700' : ''}
    ${shadowClasses[shadow]}
    ${hover ? 'hover:shadow-md dark:hover:shadow-lg transition-all duration-200 hover:-translate-y-1' : ''}
    ${gradient ? 'bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900' : ''}
    ${mobileFullWidth ? 'w-full' : ''}
    ${paddingClasses[padding]}
    ${className}
  `;

  return (
    <div className={baseClasses}>
      {/* رأس البطاقة */}
      {(title || subtitle || icon || actions) && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6">
          <div className="flex items-center space-x-3 rtl:space-x-reverse mb-3 sm:mb-0">
            {icon && (
              <div className="flex-shrink-0">
                {icon}
              </div>
            )}
            <div className="min-w-0 flex-1">
              {title && (
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white truncate">
                  {title}
                </h3>
              )}
              {subtitle && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 truncate">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
          
          {actions && (
            <div className="flex-shrink-0">
              {actions}
            </div>
          )}
        </div>
      )}

      {/* محتوى البطاقة */}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
}

// مكون بطاقة الإحصائيات المتجاوبة
interface StatCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period?: string;
  };
  icon?: ReactNode;
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray';
  loading?: boolean;
  className?: string;
}

export function StatCard({
  title,
  value,
  change,
  icon,
  color = 'blue',
  loading = false,
  className = ''
}: StatCardProps) {
  const colorClasses = {
    blue: 'from-blue-500 to-blue-600 text-blue-100',
    green: 'from-green-500 to-green-600 text-green-100',
    yellow: 'from-yellow-500 to-yellow-600 text-yellow-100',
    red: 'from-red-500 to-red-600 text-red-100',
    purple: 'from-purple-500 to-purple-600 text-purple-100',
    gray: 'from-gray-500 to-gray-600 text-gray-100'
  };

  const iconColorClasses = {
    blue: 'text-blue-200',
    green: 'text-green-200',
    yellow: 'text-yellow-200',
    red: 'text-red-200',
    purple: 'text-purple-200',
    gray: 'text-gray-200'
  };

  if (loading) {
    return (
      <div className={`bg-gradient-to-r ${colorClasses[color]} rounded-lg sm:rounded-xl p-4 sm:p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-4 bg-white/20 rounded w-24"></div>
              <div className="h-8 bg-white/20 rounded w-16"></div>
            </div>
            <div className="w-8 h-8 bg-white/20 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-gradient-to-r ${colorClasses[color]} rounded-lg sm:rounded-xl p-4 sm:p-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="min-w-0 flex-1">
          <p className="text-sm font-medium opacity-90 truncate">
            {title}
          </p>
          <p className="text-2xl sm:text-3xl font-bold mt-1 truncate">
            {typeof value === 'number' ? value.toLocaleString() : value}
          </p>
          
          {change && (
            <div className="flex items-center mt-2">
              <span className={`text-xs font-medium ${
                change.type === 'increase' ? 'text-green-200' : 'text-red-200'
              }`}>
                {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
              </span>
              {change.period && (
                <span className="text-xs opacity-75 mr-2">
                  {change.period}
                </span>
              )}
            </div>
          )}
        </div>
        
        {icon && (
          <div className={`w-8 h-8 sm:w-10 sm:h-10 ${iconColorClasses[color]} flex-shrink-0`}>
            {icon}
          </div>
        )}
      </div>
    </div>
  );
}

// مكون بطاقة المعلومات المتجاوبة
interface InfoCardProps {
  items: Array<{
    label: string;
    value: ReactNode;
    highlight?: boolean;
  }>;
  title?: string;
  className?: string;
}

export function InfoCard({ items, title, className = '' }: InfoCardProps) {
  return (
    <ResponsiveCard title={title} className={className}>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
        {items.map((item, index) => (
          <div key={index} className={`p-3 rounded-lg ${
            item.highlight 
              ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' 
              : 'bg-gray-50 dark:bg-gray-700'
          }`}>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1 sm:mb-0">
                {item.label}:
              </span>
              <span className={`text-sm font-semibold ${
                item.highlight 
                  ? 'text-blue-900 dark:text-blue-100' 
                  : 'text-gray-900 dark:text-white'
              }`}>
                {item.value}
              </span>
            </div>
          </div>
        ))}
      </div>
    </ResponsiveCard>
  );
}
