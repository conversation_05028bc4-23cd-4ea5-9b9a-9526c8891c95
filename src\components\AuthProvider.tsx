'use client';

import { ReactNode } from 'react';
import { AuthProvider as AuthContextProvider } from '@/contexts/AuthContext';

interface AuthProviderWrapperProps {
  children: ReactNode;
}

/**
 * مكون AuthProvider wrapper بسيط
 * يستخدم فقط لتغليف AuthContext
 */
export default function AuthProviderWrapper({ children }: AuthProviderWrapperProps) {
  return (
    <AuthContextProvider>
      {children}
    </AuthContextProvider>
  );
}
