'use client';

import { useState, useEffect } from 'react';
import {
  Bell,
  Check,
  Trash2,
  <PERSON><PERSON><PERSON>,
  AlertCircle,
  CheckCircle,
  Info,
  Star
} from 'lucide-react';
import { useUserDashboardTranslation } from '@/hooks/user-dashboard/useUserDashboardTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet, handleApiError } from '@/utils/apiClient';

interface Notification {
  id: string;
  type: 'trade' | 'offer' | 'system' | 'security' | 'payment';
  title: string;
  message: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  actionUrl?: string;
}

export default function UserNotificationsTab() {
  const { formatDate } = useUserDashboardTranslation();
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | Notification['type']>('all');
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);

  // جلب الإشعارات من API
  useEffect(() => {
    const fetchNotifications = async () => {
      if (!user?.id) {
        setError('معرف المستخدم غير متاح');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // استدعاء API الحقيقي لجلب إشعارات المستخدم
        const response = await apiGet(`notifications/index.php?user_id=${user.id}&limit=50`);

        if (!response.success) {
          throw new Error(response.message || 'فشل في جلب الإشعارات');
        }

        // تحويل البيانات من API إلى تنسيق Notification
        const apiNotifications = response.data || [];
        const formattedNotifications: Notification[] = apiNotifications.map((notification: any) => ({
          id: notification.id.toString(),
          type: notification.type as Notification['type'],
          title: notification.title || 'إشعار',
          message: notification.message || '',
          isRead: Boolean(notification.is_read),
          priority: notification.priority as Notification['priority'] || 'medium',
          createdAt: notification.created_at || new Date().toISOString(),
          actionUrl: notification.action_url || undefined
        }));

        setNotifications(formattedNotifications);
      } catch (error) {
        console.error('Error fetching notifications:', error);
        const errorMessage = handleApiError(error);
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchNotifications();
  }, [user?.id]);

  // البيانات الوهمية للعرض في حالة عدم وجود بيانات حقيقية
  const mockNotifications: Notification[] = [
    {
      id: '1',
      type: 'trade',
      title: 'صفقة جديدة',
      message: 'تم إنشاء صفقة جديدة برقم #12345',
      isRead: false,
      priority: 'high',
      createdAt: '2024-01-15T10:30:00Z',
      actionUrl: '/trades/12345'
    },
    {
      id: '2',
      type: 'payment',
      title: 'تم استلام الدفع',
      message: 'تم تأكيد استلام الدفع للصفقة #12344',
      isRead: true,
      priority: 'medium',
      createdAt: '2024-01-14T15:45:00Z'
    },
    {
      id: '3',
      type: 'system',
      title: 'تحديث النظام',
      message: 'تم تحديث النظام بميزات جديدة',
      isRead: false,
      priority: 'low',
      createdAt: '2024-01-13T09:20:00Z'
    }
  ];

  // استخدام البيانات الحقيقية إذا كانت متوفرة، وإلا استخدام البيانات الوهمية
  const displayNotifications = notifications.length > 0 ? notifications : mockNotifications;

  const filteredNotifications = displayNotifications.filter(notification => {
    const matchesType = filter === 'all' || notification.type === filter;
    const matchesRead = !showUnreadOnly || !notification.isRead;
    return matchesType && matchesRead;
  });

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === id ? { ...notif, isRead: true } : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notif => ({ ...notif, isRead: true }))
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const getTypeIcon = (type: Notification['type']) => {
    switch (type) {
      case 'trade':
        return <Star className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
      case 'payment':
        return <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />;
      case 'security':
        return <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />;
      case 'system':
        return <Info className="w-5 h-5 text-purple-600 dark:text-purple-400" />;
      default:
        return <Bell className="w-5 h-5 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'urgent':
        return 'border-r-red-500 bg-red-50 dark:bg-red-900/10';
      case 'high':
        return 'border-r-orange-500 bg-orange-50 dark:bg-orange-900/10';
      case 'medium':
        return 'border-r-yellow-500 bg-yellow-50 dark:bg-yellow-900/10';
      default:
        return 'border-r-gray-300 bg-white dark:bg-gray-800';
    }
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  return (
    <div className="space-y-6">
      {/* رأس القسم */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <Bell className="w-6 h-6 ml-3 text-blue-600" />
            الإشعارات
            {unreadCount > 0 && (
              <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full mr-2">
                {unreadCount}
              </span>
            )}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            تتبع جميع الإشعارات والتحديثات
          </p>
        </div>
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <button
            onClick={markAllAsRead}
            className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
          >
            <Check className="w-4 h-4" />
            <span>قراءة الكل</span>
          </button>
          <button className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
            <Settings className="w-4 h-4" />
            <span>الإعدادات</span>
          </button>
        </div>
      </div>

      {/* حالة التحميل */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="mr-3 text-gray-600 dark:text-gray-400">جاري تحميل الإشعارات...</span>
        </div>
      )}

      {/* حالة الخطأ */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 ml-3" />
            <span className="text-red-800 dark:text-red-200">{error}</span>
          </div>
        </div>
      )}

      {/* الفلاتر */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex flex-wrap items-center gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              نوع الإشعار
            </label>
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as typeof filter)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">جميع الأنواع</option>
              <option value="trade">التداول</option>
              <option value="payment">المدفوعات</option>
              <option value="security">الأمان</option>
              <option value="system">النظام</option>
              <option value="offer">العروض</option>
            </select>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="unreadOnly"
              checked={showUnreadOnly}
              onChange={(e) => setShowUnreadOnly(e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="unreadOnly" className="mr-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              غير المقروءة فقط
            </label>
          </div>
        </div>
      </div>

      {/* قائمة الإشعارات */}
      <div className="space-y-3">
        {filteredNotifications.map((notification) => (
          <div
            key={notification.id}
            className={`border-r-4 rounded-lg p-4 shadow-sm transition-all hover:shadow-md ${
              getPriorityColor(notification.priority)
            } ${
              !notification.isRead ? 'border-l-4 border-l-blue-500' : ''
            }`}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3 rtl:space-x-reverse flex-1">
                <div className="flex-shrink-0 mt-1">
                  {getTypeIcon(notification.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
                    <h3 className={`text-sm font-medium ${
                      !notification.isRead
                        ? 'text-gray-900 dark:text-white'
                        : 'text-gray-600 dark:text-gray-400'
                    }`}>
                      {notification.title}
                    </h3>
                    {!notification.isRead && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    )}
                  </div>
                  <p className={`text-sm ${
                    !notification.isRead
                      ? 'text-gray-700 dark:text-gray-300'
                      : 'text-gray-500 dark:text-gray-500'
                  }`}>
                    {notification.message}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    {formatDate(new Date(notification.createdAt), {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                {!notification.isRead && (
                  <button
                    onClick={() => markAsRead(notification.id)}
                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                    title="تحديد كمقروء"
                  >
                    <Check className="w-4 h-4" />
                  </button>
                )}
                <button
                  onClick={() => deleteNotification(notification.id)}
                  className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                  title="حذف"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredNotifications.length === 0 && (
        <div className="text-center py-12">
          <Bell className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            لا توجد إشعارات
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {showUnreadOnly || filter !== 'all'
              ? 'لا توجد إشعارات تطابق الفلاتر المحددة'
              : 'لا توجد إشعارات حالياً'
            }
          </p>
        </div>
      )}
    </div>
  );
}
