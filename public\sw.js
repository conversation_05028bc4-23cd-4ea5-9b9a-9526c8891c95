// Service Worker for Ikaros P2P PWA
// إيكاروس P2P - Service Worker

const CACHE_NAME = 'ikaros-p2p-v1.0.0';
const OFFLINE_URL = '/offline';

// الملفات الأساسية للتخزين المؤقت
const CORE_ASSETS = [
  '/',
  '/offline',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
];

// الملفات الثابتة للتخزين المؤقت
const STATIC_ASSETS = [
  '/css/globals.css',
  '/js/app.js',
  '/fonts/inter.woff2',
  '/images/logo.png'
];

// استراتيجيات التخزين المؤقت
const CACHE_STRATEGIES = {
  CACHE_FIRST: 'cache-first',
  NETWORK_FIRST: 'network-first',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
  NETWORK_ONLY: 'network-only',
  CACHE_ONLY: 'cache-only'
};

// قواعد التخزين المؤقت حسب نوع الطلب
const CACHE_RULES = [
  {
    pattern: /^https:\/\/fonts\.googleapis\.com/,
    strategy: CACHE_STRATEGIES.STALE_WHILE_REVALIDATE,
    cacheName: 'google-fonts-stylesheets'
  },
  {
    pattern: /^https:\/\/fonts\.gstatic\.com/,
    strategy: CACHE_STRATEGIES.CACHE_FIRST,
    cacheName: 'google-fonts-webfonts',
    maxAge: 60 * 60 * 24 * 365 // سنة واحدة
  },
  {
    pattern: /\.(?:png|jpg|jpeg|svg|gif|webp)$/,
    strategy: CACHE_STRATEGIES.CACHE_FIRST,
    cacheName: 'images',
    maxAge: 60 * 60 * 24 * 30 // شهر واحد
  },
  {
    pattern: /\.(?:js|css)$/,
    strategy: CACHE_STRATEGIES.STALE_WHILE_REVALIDATE,
    cacheName: 'static-resources'
  },
  {
    pattern: /\/api\//,
    strategy: CACHE_STRATEGIES.NETWORK_FIRST,
    cacheName: 'api-cache',
    maxAge: 60 * 5 // 5 دقائق
  }
];

// تثبيت Service Worker
self.addEventListener('install', event => {
  console.log('🔧 Service Worker: Installing...');
  
  event.waitUntil(
    (async () => {
      try {
        const cache = await caches.open(CACHE_NAME);
        
        // تخزين الملفات الأساسية
        await cache.addAll(CORE_ASSETS);
        
        // تخزين الملفات الثابتة (مع تجاهل الأخطاء)
        await Promise.allSettled(
          STATIC_ASSETS.map(url => 
            cache.add(url).catch(err => 
              console.warn(`Failed to cache ${url}:`, err)
            )
          )
        );
        
        console.log('✅ Service Worker: Core assets cached');
        
        // تفعيل Service Worker فوراً
        self.skipWaiting();
      } catch (error) {
        console.error('❌ Service Worker: Installation failed:', error);
      }
    })()
  );
});

// تفعيل Service Worker
self.addEventListener('activate', event => {
  console.log('🚀 Service Worker: Activating...');
  
  event.waitUntil(
    (async () => {
      try {
        // حذف التخزين المؤقت القديم
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames
            .filter(cacheName => cacheName !== CACHE_NAME)
            .map(cacheName => {
              console.log(`🗑️ Deleting old cache: ${cacheName}`);
              return caches.delete(cacheName);
            })
        );
        
        // السيطرة على جميع العملاء
        await self.clients.claim();
        
        console.log('✅ Service Worker: Activated successfully');
      } catch (error) {
        console.error('❌ Service Worker: Activation failed:', error);
      }
    })()
  );
});

// اعتراض طلبات الشبكة
self.addEventListener('fetch', event => {
  // تجاهل الطلبات غير HTTP/HTTPS
  if (!event.request.url.startsWith('http')) {
    return;
  }
  
  // تجاهل طلبات POST/PUT/DELETE للـ API
  if (event.request.method !== 'GET') {
    return;
  }
  
  event.respondWith(handleFetch(event.request));
});

// معالجة طلبات الشبكة
async function handleFetch(request) {
  const url = new URL(request.url);
  
  try {
    // العثور على قاعدة التخزين المؤقت المناسبة
    const rule = CACHE_RULES.find(rule => rule.pattern.test(request.url));
    
    if (rule) {
      return await applyCacheStrategy(request, rule);
    }
    
    // الاستراتيجية الافتراضية للصفحات
    if (request.mode === 'navigate') {
      return await handleNavigation(request);
    }
    
    // محاولة الشبكة أولاً للطلبات الأخرى
    return await fetch(request);
    
  } catch (error) {
    console.error('Fetch failed:', error);
    
    // إرجاع صفحة offline للتنقل
    if (request.mode === 'navigate') {
      const cache = await caches.open(CACHE_NAME);
      return await cache.match(OFFLINE_URL) || new Response('Offline');
    }
    
    // إرجاع استجابة فارغة للموارد الأخرى
    return new Response('', { status: 408, statusText: 'Request Timeout' });
  }
}

// تطبيق استراتيجية التخزين المؤقت
async function applyCacheStrategy(request, rule) {
  const cache = await caches.open(rule.cacheName || CACHE_NAME);
  
  switch (rule.strategy) {
    case CACHE_STRATEGIES.CACHE_FIRST:
      return await cacheFirst(request, cache, rule);
      
    case CACHE_STRATEGIES.NETWORK_FIRST:
      return await networkFirst(request, cache, rule);
      
    case CACHE_STRATEGIES.STALE_WHILE_REVALIDATE:
      return await staleWhileRevalidate(request, cache, rule);
      
    case CACHE_STRATEGIES.NETWORK_ONLY:
      return await fetch(request);
      
    case CACHE_STRATEGIES.CACHE_ONLY:
      return await cache.match(request);
      
    default:
      return await networkFirst(request, cache, rule);
  }
}

// استراتيجية Cache First
async function cacheFirst(request, cache, rule) {
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse && !isExpired(cachedResponse, rule.maxAge)) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    return cachedResponse || new Response('', { status: 408 });
  }
}

// استراتيجية Network First
async function networkFirst(request, cache, rule) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    const cachedResponse = await cache.match(request);
    if (cachedResponse && !isExpired(cachedResponse, rule.maxAge)) {
      return cachedResponse;
    }
    throw error;
  }
}

// استراتيجية Stale While Revalidate
async function staleWhileRevalidate(request, cache, rule) {
  const cachedResponse = await cache.match(request);
  
  // تحديث التخزين المؤقت في الخلفية
  const fetchPromise = fetch(request).then(networkResponse => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  }).catch(() => {});
  
  // إرجاع النسخة المخزنة إذا كانت متوفرة
  if (cachedResponse && !isExpired(cachedResponse, rule.maxAge)) {
    return cachedResponse;
  }
  
  // وإلا انتظار الشبكة
  return await fetchPromise;
}

// معالجة التنقل
async function handleNavigation(request) {
  try {
    // محاولة الشبكة أولاً
    const networkResponse = await fetch(request);
    
    // تخزين الصفحة إذا نجحت
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    // البحث في التخزين المؤقت
    const cache = await caches.open(CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // إرجاع صفحة offline
    return await cache.match(OFFLINE_URL) || new Response('Offline');
  }
}

// فحص انتهاء صلاحية التخزين المؤقت
function isExpired(response, maxAge) {
  if (!maxAge) return false;
  
  const dateHeader = response.headers.get('date');
  if (!dateHeader) return false;
  
  const date = new Date(dateHeader);
  const now = new Date();
  
  return (now.getTime() - date.getTime()) > (maxAge * 1000);
}

// معالجة رسائل من العميل
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});

// معالجة إشعارات Push
self.addEventListener('push', event => {
  console.log('📱 Push notification received:', event);
  
  const options = {
    body: 'لديك إشعار جديد من منصة إيكاروس P2P',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'عرض التفاصيل',
        icon: '/icons/checkmark.png'
      },
      {
        action: 'close',
        title: 'إغلاق',
        icon: '/icons/xmark.png'
      }
    ]
  };
  
  if (event.data) {
    const data = event.data.json();
    options.body = data.body || options.body;
    options.data = { ...options.data, ...data };
  }
  
  event.waitUntil(
    self.registration.showNotification('إيكاروس P2P', options)
  );
});

// معالجة النقر على الإشعارات
self.addEventListener('notificationclick', event => {
  console.log('🔔 Notification click received:', event);
  
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/')
    );
  } else if (event.action === 'close') {
    // لا حاجة لفعل شيء
  } else {
    // النقر على الإشعار نفسه
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

console.log('🎯 Service Worker: Loaded successfully');
