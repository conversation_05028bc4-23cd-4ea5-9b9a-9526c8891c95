import { TRADE_STATUS_KEYS, TRADE_STATUS_COLORS } from '@/constants';

// دالة للحصول على ترجمة حالة الصفقة
export const getTradeStatusTranslation = (status: string, t: (key: string) => string): string => {
  const statusKey = TRADE_STATUS_KEYS[status as keyof typeof TRADE_STATUS_KEYS];
  if (!statusKey) return status;
  
  return t(`trade.status.${statusKey}`);
};

// دالة للحصول على لون حالة الصفقة
export const getTradeStatusColor = (status: string): string => {
  return TRADE_STATUS_COLORS[status as keyof typeof TRADE_STATUS_COLORS] || 'bg-gray-100 text-gray-800';
};

// دالة للحصول على جميع حالات الصفقات مترجمة
export const getTranslatedTradeStatuses = (t: (key: string) => string) => {
  return Object.entries(TRADE_STATUS_KEYS).map(([key, value]) => ({
    key,
    value,
    translation: t(`trade.status.${value}`),
    color: getTradeStatusColor(key)
  }));
};
