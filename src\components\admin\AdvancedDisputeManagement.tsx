'use client';

import React, { useState } from 'react';
import {
  G<PERSON>l,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  MessageSquare,
  FileText,
  Calendar,
  TrendingUp,
  Users,
  DollarSign,
  Activity,
  Settings,
  RefreshCw,
  Upload,
  X,
  Check,
  ArrowUp,
  ArrowDown,
  Flag,
  Shield,
  Scale,
  Phone,
  Mail,
  Paperclip,
  Camera,
  Video,
  Star,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';

interface Dispute {
  id: string;
  tradeId: string;
  title: string;
  description: string;
  type: 'paymentIssue' | 'deliveryIssue' | 'qualityIssue' | 'fraudulent' | 'communication' | 'technical' | 'policy' | 'other';
  status: 'open' | 'inReview' | 'resolved' | 'closed' | 'escalated' | 'pending' | 'investigating' | 'awaitingEvidence' | 'awaitingDecision';
  priority: 'low' | 'medium' | 'high' | 'critical' | 'urgent';
  complainant: {
    id: string;
    name: string;
    email: string;
    rating: number;
  };
  respondent: {
    id: string;
    name: string;
    email: string;
    rating: number;
  };
  assignedTo?: string;
  createdAt: string;
  updatedAt: string;
  dueDate?: string;
  amount: number;
  currency: string;
  evidenceCount: number;
  messagesCount: number;
  resolutionType?: string;
  outcome?: string;
}

interface AdvancedDisputeManagementProps {
  className?: string;
}

export default function AdvancedDisputeManagement({ className = '' }: AdvancedDisputeManagementProps) {
  const { t, language, isRTL, getDirectionClasses, formatNumber, formatCurrency, formatDate, formatRelativeTime } = useAdminTranslation();
  const dirClasses = getDirectionClasses();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedDisputes, setSelectedDisputes] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [selectedDispute, setSelectedDispute] = useState<Dispute | null>(null);
  const [showDisputeDetails, setShowDisputeDetails] = useState(false);

  // Mock data - replace with real data
  const disputes: Dispute[] = [
    {
      id: '1',
      tradeId: 'TRD-001',
      title: 'مشكلة في الدفع - لم يتم استلام المبلغ',
      description: 'قام المشتري بإرسال الدفع ولكن لم يتم استلامه من قبل البائع',
      type: 'paymentIssue',
      status: 'open',
      priority: 'high',
      complainant: {
        id: 'user1',
        name: 'أحمد محمد',
        email: '<EMAIL>',
        rating: 4.8
      },
      respondent: {
        id: 'user2',
        name: 'فاطمة علي',
        email: '<EMAIL>',
        rating: 4.5
      },
      assignedTo: 'admin1',
      createdAt: '2024-01-20T10:00:00Z',
      updatedAt: '2024-01-20T15:30:00Z',
      dueDate: '2024-01-22T10:00:00Z',
      amount: 1500,
      currency: 'USD',
      evidenceCount: 3,
      messagesCount: 8
    },
    {
      id: '2',
      tradeId: 'TRD-002',
      title: 'مشكلة في جودة المنتج',
      description: 'المنتج المستلم لا يطابق الوصف المذكور في الإعلان',
      type: 'qualityIssue',
      status: 'inReview',
      priority: 'medium',
      complainant: {
        id: 'user3',
        name: 'محمد عبدالله',
        email: '<EMAIL>',
        rating: 4.9
      },
      respondent: {
        id: 'user4',
        name: 'سارة أحمد',
        email: '<EMAIL>',
        rating: 4.2
      },
      createdAt: '2024-01-19T14:00:00Z',
      updatedAt: '2024-01-20T09:15:00Z',
      amount: 800,
      currency: 'USD',
      evidenceCount: 5,
      messagesCount: 12
    },
    {
      id: '3',
      tradeId: 'TRD-003',
      title: 'اشتباه في نشاط احتيالي',
      description: 'سلوك مشبوه من البائع وعدم الرد على الرسائل',
      type: 'fraudulent',
      status: 'escalated',
      priority: 'critical',
      complainant: {
        id: 'user5',
        name: 'خالد يوسف',
        email: '<EMAIL>',
        rating: 4.6
      },
      respondent: {
        id: 'user6',
        name: 'نور الدين',
        email: '<EMAIL>',
        rating: 3.8
      },
      assignedTo: 'admin2',
      createdAt: '2024-01-18T11:30:00Z',
      updatedAt: '2024-01-20T16:45:00Z',
      dueDate: '2024-01-21T11:30:00Z',
      amount: 2500,
      currency: 'USD',
      evidenceCount: 7,
      messagesCount: 15
    }
  ];

  const filteredDisputes = disputes.filter(dispute => {
    const matchesSearch = dispute.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dispute.tradeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dispute.complainant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dispute.respondent.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = selectedFilter === 'all' ||
                         (selectedFilter === 'open' && dispute.status === 'open') ||
                         (selectedFilter === 'inProgress' && ['inReview', 'investigating', 'awaitingEvidence'].includes(dispute.status)) ||
                         (selectedFilter === 'resolved' && dispute.status === 'resolved') ||
                         (selectedFilter === 'escalated' && dispute.status === 'escalated') ||
                         (selectedFilter === 'highPriority' && ['high', 'critical', 'urgent'].includes(dispute.priority)) ||
                         (selectedFilter === 'overdue' && dispute.dueDate && new Date(dispute.dueDate) < new Date());
    
    return matchesSearch && matchesFilter;
  });

  const stats = {
    total: disputes.length,
    open: disputes.filter(d => d.status === 'open').length,
    inReview: disputes.filter(d => d.status === 'inReview').length,
    resolved: disputes.filter(d => d.status === 'resolved').length,
    escalated: disputes.filter(d => d.status === 'escalated').length,
    highPriority: disputes.filter(d => ['high', 'critical', 'urgent'].includes(d.priority)).length,
    overdue: disputes.filter(d => d.dueDate && new Date(d.dueDate) < new Date()).length,
    thisMonth: disputes.filter(d => {
      const disputeDate = new Date(d.createdAt);
      const now = new Date();
      return disputeDate.getMonth() === now.getMonth() && disputeDate.getFullYear() === now.getFullYear();
    }).length
  };

  const handleDisputeAction = (disputeId: string, action: string) => {
    console.log(`Action ${action} for dispute ${disputeId}`);
    // Implement dispute actions
  };

  const handleBulkAction = (action: string) => {
    console.log(`Bulk action ${action} for disputes:`, selectedDisputes);
    setShowBulkActions(false);
    setSelectedDisputes([]);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'blue';
      case 'inReview': return 'yellow';
      case 'resolved': return 'green';
      case 'closed': return 'gray';
      case 'escalated': return 'red';
      case 'investigating': return 'purple';
      case 'awaitingEvidence': return 'orange';
      case 'awaitingDecision': return 'indigo';
      default: return 'gray';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'green';
      case 'medium': return 'yellow';
      case 'high': return 'orange';
      case 'critical': return 'red';
      case 'urgent': return 'purple';
      default: return 'gray';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'paymentIssue': return DollarSign;
      case 'deliveryIssue': return Clock;
      case 'qualityIssue': return Star;
      case 'fraudulent': return Shield;
      case 'communication': return MessageSquare;
      case 'technical': return Settings;
      case 'policy': return FileText;
      default: return AlertTriangle;
    }
  };

  const StatusBadge = ({ status }: { status: string }) => {
    const color = getStatusColor(status);
    const StatusIcon = status === 'resolved' ? CheckCircle : 
                      status === 'escalated' ? AlertTriangle :
                      status === 'closed' ? XCircle : Clock;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${color}-100 text-${color}-800 dark:bg-${color}-900/30 dark:text-${color}-400`}>
        <StatusIcon className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
        {t(`disputes.status.${status}`)}
      </span>
    );
  };

  const PriorityBadge = ({ priority }: { priority: string }) => {
    const color = getPriorityColor(priority);
    const PriorityIcon = priority === 'critical' || priority === 'urgent' ? Flag : 
                        priority === 'high' ? ArrowUp :
                        priority === 'medium' ? ArrowDown : Flag;
    
    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-${color}-100 text-${color}-800 dark:bg-${color}-900/30 dark:text-${color}-400`}>
        <PriorityIcon className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
        {t(`disputes.priority.${priority}`)}
      </span>
    );
  };

  const TypeBadge = ({ type }: { type: string }) => {
    const TypeIcon = getTypeIcon(type);
    
    return (
      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
        <TypeIcon className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
        {t(`disputes.types.${type}`)}
      </span>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className={`flex flex-col sm:flex-row sm:items-center justify-between gap-4 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
        <div className={isRTL ? 'text-right' : 'text-left'}>
          <h3 className={`text-xl font-bold text-gray-900 dark:text-white flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Gavel className={`w-6 h-6 text-red-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            {t('disputes.title')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('disputes.subtitle')}</p>
        </div>
        <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button 
            onClick={() => handleBulkAction('export')}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            <Download className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.export')}
          </button>
          <button className="flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors">
            <Plus className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('disputes.actions.escalate')}
          </button>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-8 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('disputes.totalDisputes')}</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatNumber(stats.total)}</p>
            </div>
            <Gavel className="w-8 h-8 text-gray-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('disputes.openDisputes')}</p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{formatNumber(stats.open)}</p>
            </div>
            <Clock className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('disputes.pendingReview')}</p>
              <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{formatNumber(stats.inReview)}</p>
            </div>
            <Eye className="w-8 h-8 text-yellow-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('disputes.resolvedDisputes')}</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">{formatNumber(stats.resolved)}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('disputes.escalatedDisputes')}</p>
              <p className="text-2xl font-bold text-red-600 dark:text-red-400">{formatNumber(stats.escalated)}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">High Priority</p>
              <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">{formatNumber(stats.highPriority)}</p>
            </div>
            <Flag className="w-8 h-8 text-orange-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Overdue</p>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">{formatNumber(stats.overdue)}</p>
            </div>
            <Calendar className="w-8 h-8 text-purple-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('disputes.disputesThisMonth')}</p>
              <p className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">{formatNumber(stats.thisMonth)}</p>
            </div>
            <TrendingUp className="w-8 h-8 text-indigo-500" />
          </div>
        </div>
      </div>

      {/* Advanced Search and Filters */}
      <div className={`flex flex-col lg:flex-row gap-4 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
        <div className="relative flex-1">
          <Search className={`absolute top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 ${isRTL ? 'right-3' : 'left-3'}`} />
          <input
            type="text"
            placeholder={t('common.actions.search')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full ${isRTL ? 'pr-10 pl-4 text-right' : 'pl-10 pr-4 text-left'} py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-red-500 focus:border-transparent`}
          />
        </div>
        
        <div className="flex items-center gap-3">
          <select
            value={selectedFilter}
            onChange={(e) => setSelectedFilter(e.target.value)}
            className={`px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
          >
            <option value="all">{t('disputes.filters.all')}</option>
            <option value="open">{t('disputes.filters.open')}</option>
            <option value="inProgress">{t('disputes.filters.inProgress')}</option>
            <option value="resolved">{t('disputes.filters.resolved')}</option>
            <option value="escalated">{t('disputes.filters.escalated')}</option>
            <option value="highPriority">{t('disputes.filters.highPriority')}</option>
            <option value="overdue">{t('disputes.filters.overdue')}</option>
          </select>
          
          <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
            <Filter className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.filter')}
          </button>
          
          <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
            <RefreshCw className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.refresh')}
          </button>
        </div>
      </div>

      {/* Advanced Disputes Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  <input
                    type="checkbox"
                    className="rounded"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedDisputes(filteredDisputes.map(d => d.id));
                        setShowBulkActions(true);
                      } else {
                        setSelectedDisputes([]);
                        setShowBulkActions(false);
                      }
                    }}
                  />
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('disputes.title')} & {t('common.labels.type')}
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('disputes.participants.complainant')} / {t('disputes.participants.respondent')}
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('common.labels.amount')}
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('common.labels.status')} & {t('disputes.priority.high')}
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('common.labels.created')} / {t('common.labels.updated')}
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('disputes.actions.review')}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredDisputes.map((dispute) => (
                <tr key={dispute.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <td className="py-4 px-4">
                    <input
                      type="checkbox"
                      className="rounded"
                      checked={selectedDisputes.includes(dispute.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedDisputes([...selectedDisputes, dispute.id]);
                          setShowBulkActions(true);
                        } else {
                          setSelectedDisputes(selectedDisputes.filter(id => id !== dispute.id));
                          if (selectedDisputes.length === 1) setShowBulkActions(false);
                        }
                      }}
                    />
                  </td>
                  <td className="py-4 px-4">
                    <div className={isRTL ? 'text-right' : 'text-left'}>
                      <div className="font-medium text-gray-900 dark:text-white mb-1">
                        {dispute.title}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                        {t('common.labels.id')}: {dispute.tradeId}
                      </div>
                      <div className="flex items-center gap-2">
                        <TypeBadge type={dispute.type} />
                        {dispute.evidenceCount > 0 && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                            <Paperclip className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                            {dispute.evidenceCount}
                          </span>
                        )}
                        {dispute.messagesCount > 0 && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                            <MessageSquare className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                            {dispute.messagesCount}
                          </span>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className={`space-y-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className={`w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center ${isRTL ? 'ml-2' : 'mr-2'}`}>
                          <span className="text-blue-700 dark:text-blue-400 font-semibold text-xs">
                            {dispute.complainant.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {dispute.complainant.name}
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-300">
                            ⭐ {dispute.complainant.rating}/5
                          </div>
                        </div>
                      </div>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className={`w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center ${isRTL ? 'ml-2' : 'mr-2'}`}>
                          <span className="text-purple-700 dark:text-purple-400 font-semibold text-xs">
                            {dispute.respondent.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {dispute.respondent.name}
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-300">
                            ⭐ {dispute.respondent.rating}/5
                          </div>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className={isRTL ? 'text-right' : 'text-left'}>
                      <div className="text-lg font-bold text-gray-900 dark:text-white">
                        {formatCurrency(dispute.amount, dispute.currency)}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        {dispute.currency}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className="space-y-2">
                      <StatusBadge status={dispute.status} />
                      <PriorityBadge priority={dispute.priority} />
                      {dispute.dueDate && new Date(dispute.dueDate) < new Date() && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">
                          <Clock className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                          Overdue
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="py-4 px-4 text-sm text-gray-600 dark:text-gray-300">
                    <div className={isRTL ? 'text-right' : 'text-left'}>
                      <div>{formatDate(dispute.createdAt)}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {formatRelativeTime(dispute.updatedAt)}
                      </div>
                      {dispute.dueDate && (
                        <div className="text-xs text-orange-600 dark:text-orange-400">
                          Due: {formatDate(dispute.dueDate)}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <button
                        onClick={() => {
                          setSelectedDispute(dispute);
                          setShowDisputeDetails(true);
                        }}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 p-1 rounded transition-colors"
                        title={t('disputes.actions.viewDetails')}
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDisputeAction(dispute.id, 'review')}
                        className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 p-1 rounded transition-colors"
                        title={t('disputes.actions.review')}
                      >
                        <Scale className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDisputeAction(dispute.id, 'message')}
                        className="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 p-1 rounded transition-colors"
                        title={t('disputes.actions.sendMessage')}
                      >
                        <MessageSquare className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDisputeAction(dispute.id, 'escalate')}
                        className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 p-1 rounded transition-colors"
                        title={t('disputes.actions.escalate')}
                      >
                        <AlertTriangle className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Empty State */}
        {filteredDisputes.length === 0 && (
          <div className="text-center py-12">
            <Gavel className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{t('common.messages.noData')}</h3>
            <p className="text-gray-600 dark:text-gray-400">{t('common.actions.search')}</p>
          </div>
        )}
      </div>

      {/* Bulk Actions Panel */}
      {showBulkActions && selectedDisputes.length > 0 && (
        <div className={`fixed bottom-6 ${isRTL ? 'left-6' : 'right-6'} bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 z-50`}>
          <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {selectedDisputes.length} {t('common.actions.selected')}
            </span>
            <button
              onClick={() => handleBulkAction('assign')}
              className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors"
            >
              {t('disputes.actions.assign')}
            </button>
            <button
              onClick={() => handleBulkAction('escalate')}
              className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors"
            >
              {t('disputes.actions.escalate')}
            </button>
            <button
              onClick={() => handleBulkAction('resolve')}
              className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors"
            >
              {t('disputes.actions.resolve')}
            </button>
            <button
              onClick={() => {
                setSelectedDisputes([]);
                setShowBulkActions(false);
              }}
              className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition-colors"
            >
              {t('common.actions.cancel')}
            </button>
          </div>
        </div>
      )}

      {/* Dispute Details Modal */}
      {showDisputeDetails && selectedDispute && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className={`flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t('disputes.actions.viewDetails')}: {selectedDispute.tradeId}
              </h3>
              <button
                onClick={() => setShowDisputeDetails(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Dispute Information */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                    {t('disputes.title')} {t('common.labels.information')}
                  </h4>
                  <div className="space-y-3 text-sm">
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.type')}:</span>
                      <TypeBadge type={selectedDispute.type} />
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.status')}:</span>
                      <StatusBadge status={selectedDispute.status} />
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('disputes.priority.high')}:</span>
                      <PriorityBadge priority={selectedDispute.priority} />
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.amount')}:</span>
                      <span className="text-gray-900 dark:text-white font-semibold">
                        {formatCurrency(selectedDispute.amount, selectedDispute.currency)}
                      </span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.created')}:</span>
                      <span className="text-gray-900 dark:text-white">{formatDate(selectedDispute.createdAt)}</span>
                    </div>
                    {selectedDispute.dueDate && (
                      <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className="text-gray-600 dark:text-gray-400">Due Date:</span>
                        <span className="text-gray-900 dark:text-white">{formatDate(selectedDispute.dueDate)}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Participants Information */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                    {t('disputes.participants.complainant')} & {t('disputes.participants.respondent')}
                  </h4>
                  <div className="space-y-4">
                    <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">
                        {t('disputes.participants.complainant')}
                      </div>
                      <div className="text-sm text-gray-700 dark:text-gray-300">
                        <div>{selectedDispute.complainant.name}</div>
                        <div>{selectedDispute.complainant.email}</div>
                        <div>⭐ {selectedDispute.complainant.rating}/5</div>
                      </div>
                    </div>
                    <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <div className="text-sm font-medium text-purple-800 dark:text-purple-300 mb-2">
                        {t('disputes.participants.respondent')}
                      </div>
                      <div className="text-sm text-gray-700 dark:text-gray-300">
                        <div>{selectedDispute.respondent.name}</div>
                        <div>{selectedDispute.respondent.email}</div>
                        <div>⭐ {selectedDispute.respondent.rating}/5</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div className="mt-6">
                <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                  {t('common.labels.description')}
                </h4>
                <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg text-sm text-gray-700 dark:text-gray-300">
                  {selectedDispute.description}
                </div>
              </div>

              {/* Action Buttons */}
              <div className={`flex items-center gap-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <button
                  onClick={() => handleDisputeAction(selectedDispute.id, 'review')}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  {t('disputes.actions.review')}
                </button>
                <button
                  onClick={() => handleDisputeAction(selectedDispute.id, 'resolve')}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                >
                  {t('disputes.actions.resolve')}
                </button>
                <button
                  onClick={() => handleDisputeAction(selectedDispute.id, 'escalate')}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                >
                  {t('disputes.actions.escalate')}
                </button>
                <button
                  onClick={() => handleDisputeAction(selectedDispute.id, 'message')}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  {t('disputes.actions.sendMessage')}
                </button>
                <button
                  onClick={() => handleDisputeAction(selectedDispute.id, 'assign')}
                  className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors"
                >
                  {t('disputes.actions.assign')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
