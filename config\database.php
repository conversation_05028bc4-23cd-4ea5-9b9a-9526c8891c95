<?php
/**
 * إعدادات قاعدة البيانات لمنصة إيكاروس P2P
 * Database Configuration for Ikaros P2P Platform
 */

class Database {
    private $host;
    private $port;
    private $database;
    private $username;
    private $password;
    private $charset;
    private $connection;
    
    public function __construct() {
        // قراءة الإعدادات من ملف التكوين أو متغيرات البيئة
        // إعدادات XAMPP الافتراضية
        $this->host = $_ENV['DB_HOST'] ?? '127.0.0.1';
        $this->port = $_ENV['DB_PORT'] ?? '3306';
        $this->database = $_ENV['DB_DATABASE'] ?? 'ikaros_p2p';
        $this->username = $_ENV['DB_USERNAME'] ?? 'root';
        $this->password = $_ENV['DB_PASSWORD'] ?? '';
        $this->charset = 'utf8mb4';
    }
    
    /**
     * إنشاء اتصال بقاعدة البيانات
     */
    public function connect() {
        try {
            $dsn = "mysql:host={$this->host};port={$this->port};dbname={$this->database};charset={$this->charset}";
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset} COLLATE utf8mb4_unicode_ci"
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
            return $this->connection;
        } catch (PDOException $e) {
            throw new Exception("فشل الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على الاتصال الحالي
     */
    public function getConnection() {
        if (!$this->connection) {
            $this->connect();
        }
        return $this->connection;
    }
    
    /**
     * إغلاق الاتصال
     */
    public function disconnect() {
        $this->connection = null;
    }
    
    /**
     * تنفيذ استعلام SELECT
     */
    public function select($query, $params = []) {
        try {
            $stmt = $this->getConnection()->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            throw new Exception("خطأ في تنفيذ استعلام SELECT: " . $e->getMessage());
        }
    }
    
    /**
     * تنفيذ استعلام INSERT
     */
    public function insert($query, $params = []) {
        try {
            $stmt = $this->getConnection()->prepare($query);
            $stmt->execute($params);
            return $this->getConnection()->lastInsertId();
        } catch (PDOException $e) {
            throw new Exception("خطأ في تنفيذ استعلام INSERT: " . $e->getMessage());
        }
    }
    
    /**
     * تنفيذ استعلام UPDATE
     */
    public function update($query, $params = []) {
        try {
            $stmt = $this->getConnection()->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            throw new Exception("خطأ في تنفيذ استعلام UPDATE: " . $e->getMessage());
        }
    }
    
    /**
     * تنفيذ استعلام DELETE
     */
    public function delete($query, $params = []) {
        try {
            $stmt = $this->getConnection()->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            throw new Exception("خطأ في تنفيذ استعلام DELETE: " . $e->getMessage());
        }
    }
    
    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->getConnection()->beginTransaction();
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->getConnection()->commit();
    }
    
    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->getConnection()->rollback();
    }
    
    /**
     * اختبار الاتصال
     */
    public function testConnection() {
        try {
            $this->connect();
            $stmt = $this->connection->query("SELECT 1");
            return $stmt !== false;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * الحصول على معلومات قاعدة البيانات
     */
    public function getDatabaseInfo() {
        try {
            $info = [];
            
            // معلومات الخادم
            $stmt = $this->getConnection()->query("SELECT VERSION() as version");
            $result = $stmt->fetch();
            $info['mysql_version'] = $result['version'];
            
            // معلومات قاعدة البيانات
            $stmt = $this->getConnection()->query("SELECT DATABASE() as database_name");
            $result = $stmt->fetch();
            $info['database_name'] = $result['database_name'];
            
            // عدد الجداول
            $stmt = $this->getConnection()->query("SHOW TABLES");
            $info['tables_count'] = $stmt->rowCount();
            
            // حجم قاعدة البيانات
            $stmt = $this->getConnection()->query("
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            ");
            $result = $stmt->fetch();
            $info['database_size_mb'] = $result['size_mb'];
            
            return $info;
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * الحصول على قائمة الجداول
     */
    public function getTables() {
        try {
            $stmt = $this->getConnection()->query("SHOW TABLES");
            $tables = [];
            while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
                $tables[] = $row[0];
            }
            return $tables;
        } catch (Exception $e) {
            throw new Exception("خطأ في جلب قائمة الجداول: " . $e->getMessage());
        }
    }
    
    /**
     * تحسين قاعدة البيانات
     */
    public function optimizeDatabase() {
        try {
            $tables = $this->getTables();
            $optimized = [];
            
            foreach ($tables as $table) {
                $stmt = $this->getConnection()->query("OPTIMIZE TABLE `$table`");
                $result = $stmt->fetch();
                $optimized[$table] = $result['Msg_text'] ?? 'تم التحسين';
            }
            
            return $optimized;
        } catch (Exception $e) {
            throw new Exception("خطأ في تحسين قاعدة البيانات: " . $e->getMessage());
        }
    }
    
    /**
     * إنشاء نسخة احتياطية من قاعدة البيانات
     */
    public function backup($filename = null) {
        try {
            if (!$filename) {
                $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
            }
            
            $backupPath = __DIR__ . '/../backups/';
            if (!is_dir($backupPath)) {
                mkdir($backupPath, 0755, true);
            }
            
            $command = sprintf(
                'mysqldump --host=%s --port=%s --user=%s --password=%s %s > %s',
                escapeshellarg($this->host),
                escapeshellarg($this->port),
                escapeshellarg($this->username),
                escapeshellarg($this->password),
                escapeshellarg($this->database),
                escapeshellarg($backupPath . $filename)
            );
            
            exec($command, $output, $returnCode);
            
            if ($returnCode === 0) {
                return $backupPath . $filename;
            } else {
                throw new Exception("فشل في إنشاء النسخة الاحتياطية");
            }
        } catch (Exception $e) {
            throw new Exception("خطأ في إنشاء النسخة الاحتياطية: " . $e->getMessage());
        }
    }
}

// إنشاء مثيل واحد من قاعدة البيانات (Singleton)
class DatabaseManager {
    private static $instance = null;
    private $database;
    
    private function __construct() {
        $this->database = new Database();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new DatabaseManager();
        }
        return self::$instance;
    }
    
    public function getDatabase() {
        return $this->database;
    }
}

// دالة مساعدة للوصول السريع لقاعدة البيانات
function db() {
    return DatabaseManager::getInstance()->getDatabase();
}

// دالة لتحميل متغيرات البيئة من ملف .env
function loadEnv($path = __DIR__ . '/../.env') {
    if (!file_exists($path)) {
        return;
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        
        if (!array_key_exists($name, $_ENV)) {
            $_ENV[$name] = $value;
        }
    }
}

// تحميل متغيرات البيئة
loadEnv();
?>
