'use client';

import React, { useState, useEffect } from 'react';
import {
  Bell,
  BellOff,
  Volume2,
  VolumeX,
  Clock,
  Shield,
  DollarSign,
  Settings,
  Mail,
  Smartphone,
  AlertTriangle,
  CheckCircle,
  Save,
  RotateCcw
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { smartNotificationService, NotificationPreferences } from '@/services/smartNotificationService';
import { notificationService } from '@/services/notificationService';

interface NotificationSettingsProps {
  onClose?: () => void;
  className?: string;
}

export default function NotificationSettings({ onClose, className = '' }: NotificationSettingsProps) {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    loadPreferences();
  }, [user]);

  /**
   * تحميل إعدادات المستخدم
   */
  const loadPreferences = async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      
      // محاولة تحميل من localStorage أولاً
      const savedPrefs = localStorage.getItem('notificationPreferences');
      if (savedPrefs) {
        const prefs = JSON.parse(savedPrefs);
        if (prefs.userId === user.id) {
          setPreferences(prefs);
          setIsLoading(false);
          return;
        }
      }

      // إنشاء إعدادات افتراضية
      const defaultPrefs: NotificationPreferences = {
        userId: parseInt(user.id.toString()),
        browserNotifications: true,
        emailNotifications: true,
        tradeNotifications: true,
        securityNotifications: true,
        systemNotifications: true,
        contractNotifications: true,
        soundEnabled: true,
        quietHours: {
          enabled: false,
          startTime: '22:00',
          endTime: '08:00'
        },
        priorities: {
          low: true,
          medium: true,
          high: true,
          urgent: true
        },
        languages: ['ar', 'en']
      };

      setPreferences(defaultPrefs);
    } catch (error) {
      console.error('خطأ في تحميل إعدادات الإشعارات:', error);
      notificationService.error('فشل في تحميل إعدادات الإشعارات');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * حفظ الإعدادات
   */
  const savePreferences = async () => {
    if (!preferences || !user?.id) return;

    try {
      setIsSaving(true);
      
      await smartNotificationService.updateUserPreferences(parseInt(user.id.toString()), preferences);
      
      setHasChanges(false);
      notificationService.success('تم حفظ إعدادات الإشعارات بنجاح');
      
      if (onClose) {
        onClose();
      }
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error);
      notificationService.error('فشل في حفظ إعدادات الإشعارات');
    } finally {
      setIsSaving(false);
    }
  };

  /**
   * إعادة تعيين الإعدادات
   */
  const resetPreferences = () => {
    if (!user?.id) return;

    const defaultPrefs: NotificationPreferences = {
      userId: parseInt(user.id.toString()),
      browserNotifications: true,
      emailNotifications: true,
      tradeNotifications: true,
      securityNotifications: true,
      systemNotifications: true,
      contractNotifications: true,
      soundEnabled: true,
      quietHours: {
        enabled: false,
        startTime: '22:00',
        endTime: '08:00'
      },
      priorities: {
        low: true,
        medium: true,
        high: true,
        urgent: true
      },
      languages: ['ar', 'en']
    };

    setPreferences(defaultPrefs);
    setHasChanges(true);
  };

  /**
   * تحديث إعداد
   */
  const updatePreference = (key: keyof NotificationPreferences, value: any) => {
    if (!preferences) return;

    setPreferences(prev => ({
      ...prev!,
      [key]: value
    }));
    setHasChanges(true);
  };

  /**
   * تحديث إعداد متداخل
   */
  const updateNestedPreference = (parentKey: keyof NotificationPreferences, childKey: string, value: any) => {
    if (!preferences) return;

    setPreferences(prev => ({
      ...prev!,
      [parentKey]: {
        ...(prev![parentKey] as any),
        [childKey]: value
      }
    }));
    setHasChanges(true);
  };

  /**
   * اختبار الإشعارات
   */
  const testNotification = (type: 'browser' | 'toast') => {
    if (type === 'browser') {
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('إشعار تجريبي', {
          body: 'هذا إشعار تجريبي من منصة إيكاروس P2P',
          icon: '/favicon.ico'
        });
      } else {
        notificationService.warning('إشعارات المتصفح غير مفعلة');
      }
    } else {
      notificationService.info('هذا إشعار تجريبي من منصة إيكاروس P2P', {
        icon: '🧪',
        duration: 3000
      });
    }
  };

  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="space-y-3">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!preferences) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <div className="text-center text-gray-500 dark:text-gray-400">
          فشل في تحميل إعدادات الإشعارات
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
              preferences.browserNotifications || preferences.emailNotifications
                ? 'bg-blue-100 dark:bg-blue-900/30'
                : 'bg-gray-100 dark:bg-gray-700'
            }`}>
              {preferences.browserNotifications || preferences.emailNotifications ? (
                <Bell className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              ) : (
                <BellOff className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              )}
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                إعدادات الإشعارات
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                تخصيص تفضيلات الإشعارات والتنبيهات
                {!preferences.browserNotifications && !preferences.emailNotifications && (
                  <span className="text-orange-500 dark:text-orange-400"> - الإشعارات معطلة</span>
                )}
              </p>
            </div>
          </div>
          
          {hasChanges && (
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <button
                onClick={resetPreferences}
                className="btn btn-secondary btn-sm"
                disabled={isSaving}
              >
                <RotateCcw className="w-4 h-4 ml-1 rtl:ml-1 ltr:mr-1" />
                إعادة تعيين
              </button>
              <button
                onClick={savePreferences}
                className="btn btn-primary btn-sm"
                disabled={isSaving}
              >
                {isSaving ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-1 rtl:ml-1 ltr:mr-1"></div>
                ) : (
                  <Save className="w-4 h-4 ml-1 rtl:ml-1 ltr:mr-1" />
                )}
                حفظ
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* إعدادات عامة */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <Settings className="w-5 h-5 ml-2 rtl:ml-2 ltr:mr-2" />
            الإعدادات العامة
          </h3>
          
          <div className="space-y-4">
            {/* إشعارات المتصفح */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                {preferences.browserNotifications ? (
                  <Smartphone className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                ) : (
                  <BellOff className="w-5 h-5 text-gray-400 dark:text-gray-500" />
                )}
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">إشعارات المتصفح</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    عرض الإشعارات في المتصفح
                    {!preferences.browserNotifications && (
                      <span className="text-red-500 dark:text-red-400"> (معطل)</span>
                    )}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <button
                  onClick={() => testNotification('browser')}
                  className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
                >
                  اختبار
                </button>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.browserNotifications}
                    onChange={(e) => updatePreference('browserNotifications', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>

            {/* إشعارات البريد الإلكتروني */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                {preferences.emailNotifications ? (
                  <Mail className="w-5 h-5 text-green-600 dark:text-green-400" />
                ) : (
                  <BellOff className="w-5 h-5 text-gray-400 dark:text-gray-500" />
                )}
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">إشعارات البريد الإلكتروني</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    إرسال الإشعارات عبر البريد الإلكتروني
                    {!preferences.emailNotifications && (
                      <span className="text-red-500 dark:text-red-400"> (معطل)</span>
                    )}
                  </p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.emailNotifications}
                  onChange={(e) => updatePreference('emailNotifications', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {/* الأصوات */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                {preferences.soundEnabled ? (
                  <Volume2 className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                ) : (
                  <VolumeX className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                )}
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">الأصوات</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">تشغيل أصوات الإشعارات</p>
                </div>
              </div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <button
                  onClick={() => testNotification('toast')}
                  className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
                >
                  اختبار
                </button>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.soundEnabled}
                    onChange={(e) => updatePreference('soundEnabled', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* أنواع الإشعارات */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <Bell className="w-5 h-5 ml-2 rtl:ml-2 ltr:mr-2" />
            أنواع الإشعارات
          </h3>

          <div className="grid md:grid-cols-2 gap-4">
            {/* إشعارات التداول */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <DollarSign className="w-5 h-5 text-green-600 dark:text-green-400" />
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">إشعارات التداول</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">العروض والصفقات</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.tradeNotifications}
                  onChange={(e) => updatePreference('tradeNotifications', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {/* إشعارات الأمان */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Shield className="w-5 h-5 text-red-600 dark:text-red-400" />
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">إشعارات الأمان</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">تسجيل الدخول والأمان</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.securityNotifications}
                  onChange={(e) => updatePreference('securityNotifications', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {/* إشعارات النظام */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Settings className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">إشعارات النظام</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">التحديثات والصيانة</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.systemNotifications}
                  onChange={(e) => updatePreference('systemNotifications', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {/* إشعارات العقد الذكي */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <AlertTriangle className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">إشعارات العقد الذكي</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">أحداث البلوك تشين</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.contractNotifications}
                  onChange={(e) => updatePreference('contractNotifications', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>

        {/* أولويات الإشعارات */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <AlertTriangle className="w-5 h-5 ml-2 rtl:ml-2 ltr:mr-2" />
            أولويات الإشعارات
          </h3>

          <div className="grid md:grid-cols-2 gap-4">
            {/* منخفضة */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">منخفضة</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">إشعارات عامة</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.priorities.low}
                  onChange={(e) => updateNestedPreference('priorities', 'low', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {/* متوسطة */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">متوسطة</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">إشعارات مهمة</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.priorities.medium}
                  onChange={(e) => updateNestedPreference('priorities', 'medium', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {/* عالية */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">عالية</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">إشعارات عاجلة</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.priorities.high}
                  onChange={(e) => updateNestedPreference('priorities', 'high', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {/* حرجة */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">حرجة</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">إشعارات طارئة</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.priorities.urgent}
                  onChange={(e) => updateNestedPreference('priorities', 'urgent', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>

        {/* الساعات الهادئة */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            {preferences.quietHours.enabled ? (
              <BellOff className="w-5 h-5 ml-2 rtl:ml-2 ltr:mr-2 text-orange-500 dark:text-orange-400" />
            ) : (
              <Clock className="w-5 h-5 ml-2 rtl:ml-2 ltr:mr-2" />
            )}
            الساعات الهادئة
            {preferences.quietHours.enabled && (
              <span className="mr-2 rtl:mr-2 ltr:ml-2 text-sm text-orange-500 dark:text-orange-400">
                (مفعل)
              </span>
            )}
          </h3>

          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div>
                <p className="font-medium text-gray-900 dark:text-white">تفعيل الساعات الهادئة</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  إيقاف الإشعارات غير الحرجة خلال فترات محددة
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences.quietHours.enabled}
                  onChange={(e) => updateNestedPreference('quietHours', 'enabled', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {preferences.quietHours.enabled && (
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    من الساعة
                  </label>
                  <input
                    type="time"
                    value={preferences.quietHours.startTime}
                    onChange={(e) => updateNestedPreference('quietHours', 'startTime', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    إلى الساعة
                  </label>
                  <input
                    type="time"
                    value={preferences.quietHours.endTime}
                    onChange={(e) => updateNestedPreference('quietHours', 'endTime', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* حالة الإشعارات */}
        <div className={`border rounded-lg p-4 ${
          !preferences.browserNotifications && !preferences.emailNotifications
            ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
            : 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
        }`}>
          <div className="flex items-start space-x-3 rtl:space-x-reverse">
            {!preferences.browserNotifications && !preferences.emailNotifications ? (
              <BellOff className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5" />
            ) : (
              <CheckCircle className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            )}
            <div>
              <p className={`text-sm font-medium ${
                !preferences.browserNotifications && !preferences.emailNotifications
                  ? 'text-red-900 dark:text-red-100'
                  : 'text-blue-900 dark:text-blue-100'
              }`}>
                {!preferences.browserNotifications && !preferences.emailNotifications
                  ? 'تحذير: الإشعارات معطلة'
                  : 'ملاحظة مهمة'
                }
              </p>
              <p className={`text-sm mt-1 ${
                !preferences.browserNotifications && !preferences.emailNotifications
                  ? 'text-red-700 dark:text-red-200'
                  : 'text-blue-700 dark:text-blue-200'
              }`}>
                {!preferences.browserNotifications && !preferences.emailNotifications ? (
                  'جميع الإشعارات معطلة حالياً. قد تفوتك تحديثات مهمة حول صفقاتك. الإشعارات الحرجة (مثل النزاعات والأمان) ستظهر دائماً.'
                ) : (
                  'الإشعارات الحرجة (مثل النزاعات والأمان) ستظهر دائماً بغض النظر عن الإعدادات. يمكنك اختبار الإشعارات باستخدام أزرار "اختبار" بجانب كل إعداد.'
                )}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
