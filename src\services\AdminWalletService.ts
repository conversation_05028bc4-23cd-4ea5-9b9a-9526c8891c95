/**
 * خدمة محفظة المدراء المنفصلة والآمنة
 * Secure Admin Wallet Service
 */

import { ethers } from 'ethers';

export interface AdminWalletInfo {
  address: string;
  balance: string;
  network: string;
  chainId: string;
  isConnected: boolean;
  adminRole?: string;
  sessionId: string;
  sessionExpiry: number;
}

export interface AdminWalletError {
  type: 'WALLET_NOT_FOUND' | 'USER_REJECTED' | 'NETWORK_ERROR' | 'UNAUTHORIZED' | 'SESSION_EXPIRED';
  message: string;
  code?: string;
}

class AdminWalletService {
  private provider: ethers.BrowserProvider | null = null;
  private signer: ethers.Signer | null = null;
  private currentAccount: string | null = null;
  private currentChainId: string | null = null;
  private sessionId: string | null = null;
  private sessionExpiry: number | null = null;
  private encryptionKey: string | null = null;

  // إعدادات الأمان للمدراء
  private readonly ADMIN_SESSION_TIMEOUT = 4 * 60 * 60 * 1000; // 4 ساعات
  private readonly ADMIN_STORAGE_KEY = 'admin_wallet_session';
  private readonly ADMIN_ENCRYPTION_KEY = 'admin_wallet_encryption';

  constructor() {
    // التأكد من أننا في بيئة المتصفح
    if (typeof window !== 'undefined') {
      this.initializeEncryption();
      this.checkExistingSession();
    }
  }

  /**
   * تهيئة مفتاح التشفير
   */
  private initializeEncryption(): void {
    // التأكد من أننا في بيئة المتصفح
    if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
      return;
    }

    let key = sessionStorage.getItem(this.ADMIN_ENCRYPTION_KEY);
    if (!key) {
      key = this.generateEncryptionKey();
      sessionStorage.setItem(this.ADMIN_ENCRYPTION_KEY, key);
    }
    this.encryptionKey = key;
  }

  /**
   * توليد مفتاح تشفير عشوائي
   */
  private generateEncryptionKey(): string {
    // التأكد من أننا في بيئة المتصفح
    if (typeof window === 'undefined' || typeof crypto === 'undefined') {
      // fallback للخادم
      return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }

    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * تشفير البيانات
   */
  private encrypt(data: string): string {
    if (!this.encryptionKey) return data;
    
    try {
      // تشفير بسيط باستخدام XOR (في الإنتاج، استخدم AES)
      const key = this.encryptionKey;
      let encrypted = '';
      for (let i = 0; i < data.length; i++) {
        encrypted += String.fromCharCode(
          data.charCodeAt(i) ^ key.charCodeAt(i % key.length)
        );
      }
      return btoa(encrypted);
    } catch (error) {
      console.error('Encryption error:', error);
      return data;
    }
  }

  /**
   * فك التشفير
   */
  private decrypt(encryptedData: string): string {
    if (!this.encryptionKey) return encryptedData;
    
    try {
      const key = this.encryptionKey;
      const data = atob(encryptedData);
      let decrypted = '';
      for (let i = 0; i < data.length; i++) {
        decrypted += String.fromCharCode(
          data.charCodeAt(i) ^ key.charCodeAt(i % key.length)
        );
      }
      return decrypted;
    } catch (error) {
      console.error('Decryption error:', error);
      return encryptedData;
    }
  }

  /**
   * التحقق من وجود جلسة سابقة
   */
  private checkExistingSession(): void {
    // التأكد من أننا في بيئة المتصفح
    if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
      return;
    }

    try {
      const encryptedSession = sessionStorage.getItem(this.ADMIN_STORAGE_KEY);
      if (!encryptedSession) return;

      const sessionData = JSON.parse(this.decrypt(encryptedSession));
      
      // التحقق من انتهاء الجلسة
      if (Date.now() > sessionData.expiresAt) {
        this.clearSession();
        return;
      }

      this.currentAccount = sessionData.address;
      this.currentChainId = sessionData.chainId;
      this.sessionId = sessionData.sessionId;
      this.sessionExpiry = sessionData.expiresAt;

      // محاولة استعادة الاتصال
      this.restoreConnection().catch(console.error);
    } catch (error) {
      console.error('Error checking existing session:', error);
      this.clearSession();
    }
  }

  /**
   * استعادة الاتصال
   */
  private async restoreConnection(): Promise<void> {
    if (typeof window === 'undefined' || !window.ethereum) return;

    try {
      this.provider = new ethers.BrowserProvider(window.ethereum);
      this.signer = await this.provider.getSigner();
      
      const address = await this.signer.getAddress();
      if (address.toLowerCase() !== this.currentAccount?.toLowerCase()) {
        throw new Error('Account mismatch');
      }

      const network = await this.provider.getNetwork();
      this.currentChainId = `0x${network.chainId.toString(16)}`;
    } catch (error) {
      console.error('Failed to restore connection:', error);
      this.clearSession();
    }
  }

  /**
   * الاتصال بمحفظة المدير الافتراضية للاختبار
   */
  async connectDefaultAdminWallet(): Promise<AdminWalletInfo> {
    const defaultAdminAddress = '******************************************';

    // التحقق من صلاحيات الإدارة
    const isAdmin = await this.verifyAdminPermissions(defaultAdminAddress);
    if (!isAdmin.isAdmin) {
      throw new Error('المدير الافتراضي غير موجود في قاعدة البيانات');
    }

    // محاكاة معلومات المحفظة
    this.currentAccount = defaultAdminAddress;
    this.currentChainId = '0x61'; // BSC Testnet

    // إنشاء جلسة آمنة
    this.sessionId = this.generateSessionId();
    this.sessionExpiry = Date.now() + this.ADMIN_SESSION_TIMEOUT;

    const walletInfo: AdminWalletInfo = {
      address: defaultAdminAddress,
      balance: '1.0', // رصيد وهمي
      chainId: this.currentChainId,
      network: 'BSC Testnet',
      isConnected: true,
      adminRole: isAdmin.adminRole || 'super_admin',
      sessionId: this.sessionId,
      sessionExpiry: this.sessionExpiry
    };

    // تسجيل النشاط
    this.logAdminActivity('admin_wallet_connected', {
      address: defaultAdminAddress,
      method: 'default_admin',
      timestamp: new Date().toISOString()
    });

    return walletInfo;
  }

  /**
   * الاتصال بمحفظة المدير
   */
  async connectAdminWallet(): Promise<AdminWalletInfo> {
    if (typeof window === 'undefined' || !window.ethereum) {
      throw new Error('محفظة غير متوفرة');
    }

    try {
      // طلب الاتصال
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      });

      if (!accounts || accounts.length === 0) {
        throw new Error('لم يتم اختيار أي حساب');
      }

      // إعداد المزود
      this.provider = new ethers.BrowserProvider(window.ethereum);
      this.signer = await this.provider.getSigner();
      this.currentAccount = await this.signer.getAddress();

      // التحقق من صلاحيات الإدارة
      const isAdmin = await this.verifyAdminPermissions(this.currentAccount);
      if (!isAdmin.isAdmin) {
        throw new Error('هذه المحفظة لا تملك صلاحيات إدارية');
      }

      // الحصول على معلومات الشبكة
      const network = await this.provider.getNetwork();
      this.currentChainId = `0x${network.chainId.toString(16)}`;

      // الحصول على الرصيد
      const balance = await this.provider.getBalance(this.currentAccount);
      const balanceInEth = ethers.formatEther(balance);

      // إنشاء جلسة آمنة
      this.sessionId = this.generateSessionId();
      this.sessionExpiry = Date.now() + this.ADMIN_SESSION_TIMEOUT;

      const walletInfo: AdminWalletInfo = {
        address: this.currentAccount,
        balance: balanceInEth,
        network: network.name,
        chainId: this.currentChainId,
        isConnected: true,
        adminRole: isAdmin.adminRole,
        sessionId: this.sessionId,
        sessionExpiry: this.sessionExpiry
      };

      // حفظ الجلسة مشفرة
      this.saveSession(walletInfo);

      // تسجيل النشاط
      this.logAdminActivity('admin_wallet_connected', {
        address: this.currentAccount,
        network: network.name,
        timestamp: new Date().toISOString()
      });

      return walletInfo;
    } catch (error: any) {
      console.error('Admin wallet connection error:', error);
      throw this.handleWalletError(error);
    }
  }

  /**
   * التحقق من صلاحيات الإدارة
   */
  private async verifyAdminPermissions(address: string): Promise<{isAdmin: boolean, adminRole?: string}> {
    try {
      const response = await fetch('/api/admin/check-permissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ walletAddress: address })
      });

      const data = await response.json();
      return {
        isAdmin: data.isAdmin || false,
        adminRole: data.adminData?.adminRole
      };
    } catch (error) {
      console.error('Error verifying admin permissions:', error);
      return { isAdmin: false };
    }
  }

  /**
   * توليد معرف جلسة فريد
   */
  private generateSessionId(): string {
    return `admin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * حفظ الجلسة مشفرة
   */
  private saveSession(walletInfo: AdminWalletInfo): void {
    // التأكد من أننا في بيئة المتصفح
    if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
      return;
    }

    const sessionData = {
      address: walletInfo.address,
      chainId: walletInfo.chainId,
      sessionId: walletInfo.sessionId,
      expiresAt: this.sessionExpiry,
      adminRole: walletInfo.adminRole
    };

    const encryptedData = this.encrypt(JSON.stringify(sessionData));
    sessionStorage.setItem(this.ADMIN_STORAGE_KEY, encryptedData);
  }

  /**
   * مسح الجلسة
   */
  private clearSession(): void {
    // التأكد من أننا في بيئة المتصفح
    if (typeof window !== 'undefined' && typeof sessionStorage !== 'undefined') {
      sessionStorage.removeItem(this.ADMIN_STORAGE_KEY);
      sessionStorage.removeItem(this.ADMIN_ENCRYPTION_KEY);
    }

    this.provider = null;
    this.signer = null;
    this.currentAccount = null;
    this.currentChainId = null;
    this.sessionId = null;
    this.sessionExpiry = null;
    this.encryptionKey = null;
  }

  /**
   * قطع الاتصال
   */
  disconnectAdminWallet(): void {
    if (this.currentAccount) {
      this.logAdminActivity('admin_wallet_disconnected', {
        address: this.currentAccount,
        timestamp: new Date().toISOString()
      });
    }

    this.clearSession();
  }

  /**
   * التحقق من حالة الاتصال
   */
  isConnected(): boolean {
    // التأكد من أننا في بيئة المتصفح
    if (typeof window === 'undefined') {
      return false;
    }

    return !!(this.provider && this.signer && this.currentAccount &&
             this.sessionExpiry && Date.now() < this.sessionExpiry);
  }

  /**
   * الحصول على معلومات المحفظة الحالية
   */
  async getCurrentWalletInfo(): Promise<AdminWalletInfo | null> {
    if (!this.isConnected()) return null;

    try {
      const balance = await this.provider!.getBalance(this.currentAccount!);
      const network = await this.provider!.getNetwork();

      return {
        address: this.currentAccount!,
        balance: ethers.formatEther(balance),
        network: network.name,
        chainId: this.currentChainId!,
        isConnected: true,
        sessionId: this.sessionId!,
        sessionExpiry: this.sessionExpiry!
      };
    } catch (error) {
      console.error('Error getting wallet info:', error);
      return null;
    }
  }

  /**
   * تسجيل نشاط المدير
   */
  private logAdminActivity(action: string, data: any): void {
    try {
      // إرسال لوق النشاط إلى الخادم
      fetch('/api/admin/log-activity.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          data,
          timestamp: new Date().toISOString(),
          sessionId: this.sessionId
        })
      }).catch(console.error);
    } catch (error) {
      console.error('Error logging admin activity:', error);
    }
  }

  /**
   * معالجة أخطاء المحفظة
   */
  private handleWalletError(error: any): Error {
    if (error.code === 4001) {
      return new Error('تم رفض الاتصال من قبل المستخدم');
    } else if (error.code === -32002) {
      return new Error('طلب اتصال معلق بالفعل');
    } else if (error.message?.includes('unauthorized')) {
      return new Error('غير مخول للوصول');
    } else {
      return new Error(error.message || 'خطأ في الاتصال بالمحفظة');
    }
  }
}

// إنشاء مثيل واحد للخدمة مع فحص بيئة المتصفح
let adminWalletServiceInstance: AdminWalletService | null = null;

export const getAdminWalletService = (): AdminWalletService => {
  if (typeof window === 'undefined') {
    // في بيئة الخادم، إرجاع مثيل فارغ
    return {} as AdminWalletService;
  }

  if (!adminWalletServiceInstance) {
    adminWalletServiceInstance = new AdminWalletService();
  }

  return adminWalletServiceInstance;
};

export const adminWalletService = getAdminWalletService();
export default adminWalletService;
