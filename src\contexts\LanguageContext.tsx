'use client';

import React, { createContext, useState, useEffect, ReactNode } from 'react';

// استيراد ملفات الترجمة
import arTranslations from '@/locales/ar.json';
import enTranslations from '@/locales/en.json';

export type Language = 'ar' | 'en';

export interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  translations: {
    ar: typeof arTranslations;
    en: typeof enTranslations;
  };
  isInitialized: boolean;
}

export const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: ReactNode;
}

export function LanguageProvider({ children }: LanguageProviderProps) {
  const [language, setLanguageState] = useState<Language>('en'); // تغيير الافتراضي إلى الإنجليزية لتجنب مشاكل hydration
  const [isInitialized, setIsInitialized] = useState(false);

  // تحميل اللغة المحفوظة من localStorage عند بدء التطبيق
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedLanguage = localStorage.getItem('language') as Language;
      if (savedLanguage && (savedLanguage === 'ar' || savedLanguage === 'en')) {
        setLanguageState(savedLanguage);
      } else {
        // تحديد اللغة بناءً على لغة المتصفح
        const browserLanguage = navigator.language.toLowerCase();
        if (browserLanguage.includes('ar')) {
          setLanguageState('ar');
        } else {
          setLanguageState('en');
        }
      }
      setIsInitialized(true);
    }
  }, []);

  // تحديث اتجاه الصفحة واللغة عند تغيير اللغة
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const html = document.documentElement;
      const body = document.body;
      
      if (language === 'ar') {
        html.setAttribute('dir', 'rtl');
        html.setAttribute('lang', 'ar');
        body.style.fontFamily = 'var(--font-cairo), Cairo, Tajawal, system-ui, sans-serif';
      } else {
        html.setAttribute('dir', 'ltr');
        html.setAttribute('lang', 'en');
        body.style.fontFamily = 'system-ui, -apple-system, sans-serif';
      }
    }
  }, [language]);

  const setLanguage = (newLanguage: Language) => {
    setLanguageState(newLanguage);
    if (typeof window !== 'undefined') {
      localStorage.setItem('language', newLanguage);
    }
  };

  const translations = {
    ar: arTranslations,
    en: enTranslations
  };

  const value: LanguageContextType = {
    language,
    setLanguage,
    translations,
    isInitialized
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

// Hook لاستخدام context
export function useLanguage() {
  const context = React.useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

export default LanguageProvider;
// LanguageProvider مُصدر بالفعل أعلاه
