<?php
/**
 * API لإدارة النزاعات في لوحة الأدمن
 * Admin Disputes Management API
 */

require_once __DIR__ . '/../cors.php';
require_once __DIR__ . '/../database/connection.php';
require_once __DIR__ . '/../middleware/admin_auth.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // التحقق من صلاحيات الأدمن
    $adminAuth = checkAdminAuth();
    if (!$adminAuth['success']) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'error' => 'غير مصرح بالوصول',
            'error_en' => 'Unauthorized access'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    $adminId = $adminAuth['admin_id'];
    $method = $_SERVER['REQUEST_METHOD'];

    if ($method !== 'POST') {
        throw new Exception('طريقة غير مدعومة');
    }

    $input = json_decode(file_get_contents('php://input'), true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('بيانات JSON غير صحيحة');
    }

    $action = $input['action'] ?? '';

    // الحصول على اتصال قاعدة البيانات
    $pdo = getDatabaseConnection();

    switch ($action) {
        case 'get_disputes':
            $result = getDisputes($pdo, $input);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;

        case 'get_stats':
            $result = getDisputeStats($pdo);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;

        case 'resolve_dispute':
            $result = resolveDispute($pdo, $input, $adminId);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;

        case 'get_dispute_details':
            $result = getDisputeDetails($pdo, $input);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;

        case 'add_admin_note':
            $result = addAdminNote($pdo, $input, $adminId);
            echo json_encode($result, JSON_UNESCAPED_UNICODE);
            break;

        default:
            throw new Exception('إجراء غير صحيح: ' . $action);
    }

} catch (Exception $e) {
    error_log('Disputes API Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'error_en' => 'Server error',
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * الحصول على قائمة النزاعات
 */
function getDisputes($pdo, $input) {
    try {
        $page = max(1, intval($input['page'] ?? 1));
        $limit = min(50, max(5, intval($input['limit'] ?? 10)));
        $offset = ($page - 1) * $limit;
        
        $filter = $input['filter'] ?? [];
        $status = $filter['status'] ?? 'all';
        $search = trim($filter['search'] ?? '');
        $dateRange = $filter['dateRange'] ?? 'all';

        // بناء الاستعلام
        $whereConditions = ['1=1'];
        $params = [];

        // فلتر الحالة
        if ($status !== 'all') {
            $whereConditions[] = 'd.status = ?';
            $params[] = $status;
        }

        // فلتر البحث
        if (!empty($search)) {
            $whereConditions[] = '(
                u1.username LIKE ? OR
                u2.username LIKE ? OR
                d.description LIKE ? OR
                CAST(d.id AS CHAR) LIKE ? OR
                CAST(d.trade_id AS CHAR) LIKE ?
            )';
            $searchTerm = "%{$search}%";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        }

        // فلتر التاريخ
        if ($dateRange !== 'all') {
            switch ($dateRange) {
                case 'today':
                    $whereConditions[] = 'DATE(d.created_at) = CURDATE()';
                    break;
                case 'week':
                    $whereConditions[] = 'd.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                    break;
                case 'month':
                    $whereConditions[] = 'd.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                    break;
            }
        }

        $whereClause = implode(' AND ', $whereConditions);

        // الاستعلام الرئيسي من جدول النزاعات الجديد
        $sql = "
            SELECT
                d.id,
                d.trade_id as tradeId,
                t.blockchain_trade_id,
                t.seller_id,
                t.buyer_id,
                t.amount,
                t.currency,
                d.status,
                d.description as disputeReason,
                d.created_at as createdAt,
                d.updated_at as lastActivity,
                d.resolved_at as resolvedAt,
                dr.resolution_type as resolutionType,
                t.complete_transaction_hash as transactionHash,
                u1.username as sellerUsername,
                u1.wallet_address as sellerAddress,
                u2.username as buyerUsername,
                u2.wallet_address as buyerAddress,
                GROUP_CONCAT(DISTINCT dan.note SEPARATOR '; ') as adminNotes,
                dr.admin_notes as resolutionNotes,
                dr.resolution_amount,
                au.username as resolvedBy
            FROM disputes d
            LEFT JOIN trades t ON d.trade_id = t.id
            LEFT JOIN users u1 ON t.seller_id = u1.id
            LEFT JOIN users u2 ON t.buyer_id = u2.id
            LEFT JOIN dispute_admin_notes dan ON d.id = dan.dispute_id AND dan.is_private = 0
            LEFT JOIN dispute_resolutions dr ON d.id = dr.dispute_id
            LEFT JOIN users au ON dr.admin_id = au.id
            WHERE {$whereClause}
            GROUP BY d.id
            ORDER BY d.created_at DESC
            LIMIT {$limit} OFFSET {$offset}
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $disputes = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // حساب إجمالي النتائج
        $countSql = "
            SELECT COUNT(DISTINCT d.id) as total
            FROM disputes d
            LEFT JOIN trades t ON d.trade_id = t.id
            LEFT JOIN users u1 ON t.seller_id = u1.id
            LEFT JOIN users u2 ON t.buyer_id = u2.id
            WHERE {$whereClause}
        ";
        
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($params);
        $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

        // إضافة معلومات الرسائل والأدلة لكل نزاع
        foreach ($disputes as &$dispute) {
            // عدد الرسائل
            $msgStmt = $pdo->prepare("SELECT COUNT(*) as count FROM messages WHERE trade_id = ?");
            $msgStmt->execute([$dispute['tradeId']]);
            $dispute['messages'] = $msgStmt->fetch(PDO::FETCH_ASSOC)['count'];

            // عدد الأدلة (الملفات المرفوعة)
            $evidenceStmt = $pdo->prepare("
                SELECT COUNT(*) as count
                FROM messages
                WHERE trade_id = ? AND message_type IN ('image', 'file', 'payment_proof')
            ");
            $evidenceStmt->execute([$dispute['tradeId']]);
            $dispute['evidence'] = $evidenceStmt->fetch(PDO::FETCH_ASSOC)['count'];
        }

        return [
            'success' => true,
            'data' => [
                'disputes' => $disputes,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => ceil($totalCount / $limit),
                    'total_count' => $totalCount,
                    'per_page' => $limit
                ]
            ]
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'فشل في تحميل النزاعات: ' . $e->getMessage()
        ];
    }
}

/**
 * الحصول على إحصائيات النزاعات
 */
function getDisputeStats($pdo) {
    try {
        // إحصائيات أساسية للنزاعات
        $sql = "
            SELECT
                COUNT(*) as totalDisputes,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pendingDisputes,
                SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolvedDisputes,
                AVG(CASE
                    WHEN resolved_at IS NOT NULL AND created_at IS NOT NULL
                    THEN TIMESTAMPDIFF(HOUR, created_at, resolved_at)
                    ELSE NULL
                END) as averageResolutionTime
            FROM disputes
        ";

        $stmt = $pdo->query($sql);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);

        // إحصائيات أنواع الحلول من جدول dispute_resolutions
        $resolutionSql = "
            SELECT
                resolution_type,
                COUNT(*) as count
            FROM dispute_resolutions
            GROUP BY resolution_type
        ";

        $resolutionStmt = $pdo->query($resolutionSql);
        $resolutions = $resolutionStmt->fetchAll(PDO::FETCH_ASSOC);

        $resolutionCounts = [
            'seller_favor' => 0,
            'buyer_favor' => 0,
            'partial_refund' => 0
        ];

        foreach ($resolutions as $resolution) {
            $resolutionType = $resolution['resolution_type'];
            if (isset($resolutionCounts[$resolutionType])) {
                $resolutionCounts[$resolutionType] = intval($resolution['count']);
            }
        }

        return [
            'success' => true,
            'data' => [
                'totalDisputes' => intval($stats['totalDisputes'] ?? 0),
                'pendingDisputes' => intval($stats['pendingDisputes'] ?? 0),
                'resolvedDisputes' => intval($stats['resolvedDisputes'] ?? 0),
                'averageResolutionTime' => round(floatval($stats['averageResolutionTime'] ?? 0), 1),
                'sellerFavorCount' => $resolutionCounts['seller_favor'],
                'buyerFavorCount' => $resolutionCounts['buyer_favor'],
                'partialRefundCount' => $resolutionCounts['partial_refund']
            ]
        ];

    } catch (Exception $e) {
        error_log('getDisputeStats error: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => 'فشل في تحميل الإحصائيات: ' . $e->getMessage()
        ];
    }
}

/**
 * حل النزاع
 */
function resolveDispute($pdo, $input, $adminId) {
    try {
        $tradeId = intval($input['trade_id'] ?? 0);
        $favorBuyer = boolval($input['favor_buyer'] ?? false);
        $reason = trim($input['reason'] ?? '');
        $adminNotes = trim($input['admin_notes'] ?? '');
        $transactionHash = trim($input['transaction_hash'] ?? '');

        if (!$tradeId || !$reason) {
            throw new Exception('بيانات غير مكتملة');
        }

        $pdo->beginTransaction();

        // تحديث حالة الصفقة
        $updateSql = "
            UPDATE trades 
            SET 
                status = 'completed',
                dispute_resolved_at = NOW(),
                dispute_resolution = ?,
                complete_transaction_hash = ?
            WHERE id = ? AND status = 'disputed'
        ";

        $resolutionType = $favorBuyer ? 'buyer_favor' : 'seller_favor';
        $updateStmt = $pdo->prepare($updateSql);
        $updateStmt->execute([$resolutionType, $transactionHash, $tradeId]);

        if ($updateStmt->rowCount() === 0) {
            throw new Exception('لم يتم العثور على النزاع أو تم حله مسبقاً');
        }

        // تسجيل النشاط الإداري
        $activitySql = "
            INSERT INTO activity_logs
            (user_id, action, entity_type, entity_id, data)
            VALUES (?, 'resolve_dispute', 'trade', ?, ?)
        ";

        $activityStmt = $pdo->prepare($activitySql);
        $activityStmt->execute([
            $adminId,
            $tradeId,
            json_encode([
                'resolution_type' => $resolutionType,
                'reason' => $reason,
                'admin_notes' => $adminNotes,
                'transaction_hash' => $transactionHash
            ])
        ]);

        $pdo->commit();

        return [
            'success' => true,
            'message' => 'تم حل النزاع بنجاح',
            'data' => [
                'trade_id' => $tradeId,
                'resolution_type' => $resolutionType,
                'transaction_hash' => $transactionHash
            ]
        ];

    } catch (Exception $e) {
        $pdo->rollBack();
        return [
            'success' => false,
            'error' => 'فشل في حل النزاع: ' . $e->getMessage()
        ];
    }
}

/**
 * الحصول على تفاصيل النزاع
 */
function getDisputeDetails($pdo, $input) {
    try {
        $disputeId = intval($input['dispute_id'] ?? 0);
        
        if (!$disputeId) {
            throw new Exception('معرف النزاع مطلوب');
        }

        // معلومات النزاع الأساسية
        $sql = "
            SELECT
                t.*,
                u1.username as seller_username,
                u1.wallet_address as seller_address,
                u2.username as buyer_username,
                u2.wallet_address as buyer_address,
                u3.username as resolved_by
            FROM trades t
            LEFT JOIN users u1 ON t.seller_id = u1.id
            LEFT JOIN users u2 ON t.buyer_id = u2.id
            LEFT JOIN users u3 ON t.dispute_resolved_by = u3.id
            WHERE t.id = ?
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([$disputeId]);
        $dispute = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$dispute) {
            throw new Exception('لم يتم العثور على النزاع');
        }

        // الرسائل
        $messagesSql = "
            SELECT 
                m.*,
                u.username as sender_username
            FROM messages m
            LEFT JOIN users u ON m.sender_id = u.id
            WHERE m.trade_id = ?
            ORDER BY m.created_at ASC
        ";

        $messagesStmt = $pdo->prepare($messagesSql);
        $messagesStmt->execute([$disputeId]);
        $messages = $messagesStmt->fetchAll(PDO::FETCH_ASSOC);

        // الملاحظات الإدارية من سجل النشاطات
        $notesSql = "
            SELECT
                al.*,
                u.username as admin_username
            FROM activity_logs al
            LEFT JOIN users u ON al.user_id = u.id
            WHERE al.entity_type = 'trade' AND al.entity_id = ? AND al.action LIKE '%dispute%'
            ORDER BY al.created_at DESC
        ";

        $notesStmt = $pdo->prepare($notesSql);
        $notesStmt->execute([$disputeId]);
        $adminNotes = $notesStmt->fetchAll(PDO::FETCH_ASSOC);

        return [
            'success' => true,
            'data' => [
                'dispute' => $dispute,
                'messages' => $messages,
                'admin_notes' => $adminNotes
            ]
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'فشل في تحميل تفاصيل النزاع: ' . $e->getMessage()
        ];
    }
}

/**
 * إضافة ملاحظة إدارية
 */
function addAdminNote($pdo, $input, $adminId) {
    try {
        $tradeId = intval($input['trade_id'] ?? 0);
        $note = trim($input['note'] ?? '');
        $isInternal = boolval($input['is_internal'] ?? true);

        if (!$tradeId || !$note) {
            throw new Exception('بيانات غير مكتملة');
        }

        $sql = "
            INSERT INTO activity_logs
            (user_id, action, entity_type, entity_id, data)
            VALUES (?, 'admin_note', 'trade', ?, ?)
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $adminId,
            $tradeId,
            json_encode([
                'note' => $note,
                'is_internal' => $isInternal,
                'type' => 'admin_note'
            ])
        ]);

        return [
            'success' => true,
            'message' => 'تم إضافة الملاحظة بنجاح',
            'data' => [
                'note_id' => $pdo->lastInsertId()
            ]
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'فشل في إضافة الملاحظة: ' . $e->getMessage()
        ];
    }
}
?>
