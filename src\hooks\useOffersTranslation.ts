'use client';

import { useTranslation } from '@/hooks/useTranslation';
import { useEffect, useState } from 'react';

interface OffersTranslations {
  ar: any;
  en: any;
}

export function useOffersTranslation() {
  const { language, isInitialized } = useTranslation();
  const [offersTranslations, setOffersTranslations] = useState<OffersTranslations | null>(null);

  useEffect(() => {
    const loadTranslations = async () => {
      try {
        const [arTranslations, enTranslations] = await Promise.all([
          fetch('/locales/ar/offers.json').then(res => res.json()),
          fetch('/locales/en/offers.json').then(res => res.json())
        ]);

        setOffersTranslations({
          ar: arTranslations,
          en: enTranslations
        });
      } catch (error) {
        console.error('Failed to load offers translations:', error);
      }
    };

    if (isInitialized) {
      loadTranslations();
    }
  }, [isInitialized]);

  const t = (key: string, params?: Record<string, string | number>): string => {
    if (!offersTranslations || !isInitialized) {
      return key;
    }

    const keys = key.split('.');
    let value: any = offersTranslations[language];

    // البحث عن القيمة في الكائن المتداخل
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // إذا لم توجد الترجمة في اللغة الحالية، جرب الإنجليزية
        if (language !== 'en') {
          let fallbackValue: any = offersTranslations.en;
          for (const fallbackKey of keys) {
            if (fallbackValue && typeof fallbackValue === 'object' && fallbackKey in fallbackValue) {
              fallbackValue = fallbackValue[fallbackKey];
            } else {
              fallbackValue = null;
              break;
            }
          }
          if (typeof fallbackValue === 'string') {
            return fallbackValue;
          }
        }
        // إذا لم توجد الترجمة، أرجع المفتاح نفسه
        if (process.env.NODE_ENV === 'development') {
          console.warn(`Offers translation key not found: ${key} for language: ${language}`);
        }
        return key;
      }
    }

    // إذا كانت القيمة نص، قم بتطبيق المعاملات إن وجدت
    if (typeof value === 'string' && params) {
      return Object.entries(params).reduce((text, [param, val]) => {
        return text.replace(new RegExp(`{{${param}}}`, 'g'), String(val));
      }, value);
    }

    return typeof value === 'string' ? value : key;
  };

  return {
    t,
    isLoaded: !!offersTranslations,
    language
  };
}
