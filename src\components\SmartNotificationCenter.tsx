'use client';

import React, { useState, useEffect } from 'react';
import {
  Bell,
  <PERSON>Off,
  X,
  Filter,
  Search,
  Settings,
  <PERSON><PERSON><PERSON><PERSON>,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>U<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { smartNotificationService, SmartNotificationData, NotificationStats, NotificationPriority } from '@/services/smartNotificationService';
import { notificationService } from '@/services/notificationService';
import NotificationItem from './NotificationItem';
import NotificationSettings from './NotificationSettings';

interface SmartNotificationCenterProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export default function SmartNotificationCenter({
  isOpen,
  onClose,
  className = ''
}: SmartNotificationCenterProps) {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<SmartNotificationData[]>([]);
  const [stats, setStats] = useState<NotificationStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState({
    type: 'all',
    priority: 'all',
    status: 'all'
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    if (isOpen && user?.id) {
      loadNotifications();
      loadStats();
      
      // تهيئة الخدمة الذكية
      smartNotificationService.initialize();
    }
  }, [isOpen, user, filter, searchQuery, currentPage]);

  /**
   * تحميل الإشعارات
   */
  const loadNotifications = async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      
      const params = new URLSearchParams({
        user_id: user.id.toString(),
        page: currentPage.toString(),
        limit: '20',
        type: filter.type,
        priority: filter.priority,
        status: filter.status
      });

      if (searchQuery) {
        params.append('search', searchQuery);
      }

      const response = await fetch(`/api/notifications/index.php?${params}`);
      const result = await response.json();

      if (result.success) {
        if (currentPage === 1) {
          setNotifications(result.data || []);
        } else {
          setNotifications(prev => [...prev, ...(result.data || [])]);
        }
        
        setHasMore(result.pagination.current_page < result.pagination.total_pages);
      } else {
        throw new Error(result.error || 'فشل في تحميل الإشعارات');
      }
    } catch (error) {
      console.error('خطأ في تحميل الإشعارات:', error);
      notificationService.error('فشل في تحميل الإشعارات');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * تحميل الإحصائيات
   */
  const loadStats = async () => {
    if (!user?.id) return;

    try {
      const stats = smartNotificationService.getNotificationStats(parseInt(user.id.toString()));
      setStats(stats);
    } catch (error) {
      console.error('خطأ في تحميل إحصائيات الإشعارات:', error);
    }
  };

  /**
   * تمييز الإشعار كمقروء
   */
  const markAsRead = async (notificationId: string) => {
    try {
      const response = await fetch('/api/notifications/index.php', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'mark_read',
          id: notificationId
        })
      });

      if (response.ok) {
        setNotifications(prev =>
          prev.map(n => n.id === notificationId ? { ...n, isRead: true } : n)
        );
        
        smartNotificationService.markAsRead(notificationId);
        loadStats();
      }
    } catch (error) {
      console.error('خطأ في تمييز الإشعار كمقروء:', error);
    }
  };

  /**
   * تمييز جميع الإشعارات كمقروءة
   */
  const markAllAsRead = async () => {
    if (!user?.id) return;

    try {
      const response = await fetch('/api/notifications/index.php', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'mark_all_read',
          user_id: user.id
        })
      });

      if (response.ok) {
        setNotifications(prev =>
          prev.map(n => ({ ...n, isRead: true }))
        );
        
        smartNotificationService.markAllAsRead(parseInt(user.id.toString()));
        loadStats();
        notificationService.success('تم تمييز جميع الإشعارات كمقروءة');
      }
    } catch (error) {
      console.error('خطأ في تمييز الإشعارات كمقروءة:', error);
      notificationService.error('فشل في تمييز الإشعارات كمقروءة');
    }
  };

  /**
   * حذف إشعار
   */
  const deleteNotification = async (notificationId: string) => {
    try {
      const response = await fetch('/api/notifications/index.php', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: notificationId
        })
      });

      if (response.ok) {
        setNotifications(prev => prev.filter(n => n.id !== notificationId));
        loadStats();
        notificationService.success('تم حذف الإشعار');
      }
    } catch (error) {
      console.error('خطأ في حذف الإشعار:', error);
      notificationService.error('فشل في حذف الإشعار');
    }
  };

  /**
   * معالجة إجراء الإشعار
   */
  const handleNotificationAction = (notification: SmartNotificationData) => {
    if (notification.actionUrl) {
      window.open(notification.actionUrl, '_blank');
    }
  };

  /**
   * تحديث الفلتر
   */
  const updateFilter = (key: string, value: string) => {
    setFilter(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  /**
   * تحديث البحث
   */
  const updateSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1);
  };

  /**
   * تحميل المزيد من الإشعارات
   */
  const loadMore = () => {
    if (hasMore && !isLoading) {
      setCurrentPage(prev => prev + 1);
    }
  };

  /**
   * تحديث الإشعارات
   */
  const refreshNotifications = () => {
    setCurrentPage(1);
    loadNotifications();
    loadStats();
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 z-50 overflow-hidden ${className}`}>
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Panel */}
      <div className="absolute right-0 rtl:right-0 ltr:left-0 top-0 h-full w-full max-w-md bg-white dark:bg-gray-900 shadow-xl transform transition-transform">
        {showSettings ? (
          <NotificationSettings 
            onClose={() => setShowSettings(false)}
            className="h-full overflow-y-auto"
          />
        ) : (
          <>
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <Bell className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    الإشعارات
                  </h2>
                  {stats && (
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {stats.unread} غير مقروء من أصل {stats.total}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <button
                  onClick={refreshNotifications}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                  title="تحديث"
                >
                  <RefreshCw className="w-5 h-5" />
                </button>

                <button
                  onClick={() => setShowSettings(true)}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                  title="الإعدادات"
                >
                  <Settings className="w-5 h-5" />
                </button>

                <button
                  onClick={onClose}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Stats */}
            {stats && (
              <div className="p-4 bg-gray-50 dark:bg-gray-800/50 border-b border-gray-200 dark:border-gray-700">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                      {stats.last24Hours}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">آخر 24 ساعة</div>
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-orange-600 dark:text-orange-400">
                      {stats.byPriority.urgent + stats.byPriority.high}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">عالية الأولوية</div>
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                      {stats.byType.trade}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">إشعارات التداول</div>
                  </div>
                </div>
              </div>
            )}

            {/* Controls */}
            <div className="p-4 space-y-3 border-b border-gray-200 dark:border-gray-700">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 rtl:left-3 ltr:right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="البحث في الإشعارات..."
                  value={searchQuery}
                  onChange={(e) => updateSearch(e.target.value)}
                  className="w-full pl-10 rtl:pl-10 ltr:pr-10 pr-4 rtl:pr-4 ltr:pl-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Filters */}
              <div className="flex space-x-2 rtl:space-x-reverse">
                <select
                  value={filter.type}
                  onChange={(e) => updateFilter('type', e.target.value)}
                  className="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">جميع الأنواع</option>
                  <option value="trade">التداول</option>
                  <option value="security">الأمان</option>
                  <option value="system">النظام</option>
                  <option value="contract">العقد الذكي</option>
                </select>

                <select
                  value={filter.status}
                  onChange={(e) => updateFilter('status', e.target.value)}
                  className="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">الكل</option>
                  <option value="unread">غير مقروء</option>
                  <option value="read">مقروء</option>
                </select>
              </div>

              {/* Actions */}
              <div className="flex justify-between">
                <button
                  onClick={markAllAsRead}
                  className="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors"
                >
                  <CheckCheck className="w-4 h-4 ml-1 rtl:ml-1 ltr:mr-1" />
                  تمييز الكل كمقروء
                </button>

                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {notifications.length} إشعار
                </div>
              </div>
            </div>

            {/* Notifications List */}
            <div className="flex-1 overflow-y-auto">
              {isLoading && currentPage === 1 ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600 dark:text-gray-400">جاري التحميل...</p>
                </div>
              ) : notifications.length === 0 ? (
                <div className="p-8 text-center">
                  <BellOff className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">لا توجد إشعارات</p>
                </div>
              ) : (
                <div className="p-4 space-y-3">
                  {notifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                      onMarkAsRead={markAsRead}
                      onDelete={deleteNotification}
                      onAction={handleNotificationAction}
                    />
                  ))}

                  {/* Load More */}
                  {hasMore && (
                    <div className="text-center py-4">
                      <button
                        onClick={loadMore}
                        disabled={isLoading}
                        className="px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors disabled:opacity-50"
                      >
                        {isLoading ? 'جاري التحميل...' : 'تحميل المزيد'}
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
