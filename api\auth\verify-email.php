<?php
/**
 * API endpoint لتأكيد البريد الإلكتروني
 * Email Verification API Endpoint
 */

// تضمين إعدادات CORS المركزية
require_once __DIR__ . "/../cors.php";
require_once __DIR__ . "/../../config/database.php";

// التحقق من طريقة الطلب
validateRequestMethod(['POST', 'GET']);

/**
 * إنشاء رمز تأكيد البريد الإلكتروني
 */
function generateVerificationToken() {
    return bin2hex(random_bytes(32));
}

/**
 * إرسال بريد إلكتروني للتأكيد
 */
function sendVerificationEmail($email, $verificationToken, $username) {
    // TODO: تطبيق إرسال البريد الإلكتروني الفعلي
    
    $verificationLink = "https://ikaros-p2p.com/verify-email?token=" . $verificationToken;
    
    // تسجيل الرمز في السجلات للتطوير
    error_log("Email verification token for $email ($username): $verificationToken");
    error_log("Verification link: $verificationLink");
    
    // في الإنتاج، استبدل هذا بإرسال بريد إلكتروني فعلي
    return true;
}

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // إرسال رمز تأكيد جديد
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            sendErrorResponse('بيانات غير صحيحة');
        }
        
        $email = trim($input['email'] ?? '');
        
        if (empty($email)) {
            sendErrorResponse('البريد الإلكتروني مطلوب');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            sendErrorResponse('البريد الإلكتروني غير صحيح');
        }
        
        // الحصول على اتصال قاعدة البيانات
        $db = DatabaseManager::getInstance()->getDatabase();
        $connection = $db->getConnection();
        
        // البحث عن المستخدم
        $stmt = $connection->prepare("
            SELECT id, username, email, is_verified, is_active 
            FROM users 
            WHERE email = ? AND is_active = 1
        ");
        
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            sendErrorResponse('المستخدم غير موجود');
        }
        
        if ($user['is_verified']) {
            sendErrorResponse('البريد الإلكتروني مؤكد بالفعل');
        }
        
        // إنشاء رمز التأكيد
        $verificationToken = generateVerificationToken();
        $expiryTime = date('Y-m-d H:i:s', time() + (24 * 60 * 60)); // 24 ساعة
        
        // حفظ رمز التأكيد في قاعدة البيانات
        $insertStmt = $connection->prepare("
            INSERT INTO email_verifications (user_id, email, token, expires_at, created_at)
            VALUES (?, ?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE
            token = VALUES(token),
            expires_at = VALUES(expires_at),
            created_at = NOW(),
            verified = 0
        ");
        
        $insertStmt->execute([$user['id'], $email, $verificationToken, $expiryTime]);
        
        // إرسال البريد الإلكتروني
        $emailSent = sendVerificationEmail($email, $verificationToken, $user['username']);
        
        sendSuccessResponse([
            'message' => 'تم إرسال رمز التأكيد إلى بريدك الإلكتروني',
            'timestamp' => date('Y-m-d H:i:s')
        ], 'تم إرسال رمز التأكيد بنجاح');
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // تأكيد البريد الإلكتروني باستخدام الرمز
        $token = $_GET['token'] ?? '';
        
        if (empty($token)) {
            sendErrorResponse('رمز التأكيد مطلوب');
        }
        
        // الحصول على اتصال قاعدة البيانات
        $db = DatabaseManager::getInstance()->getDatabase();
        $connection = $db->getConnection();
        
        // البحث عن رمز التأكيد
        $stmt = $connection->prepare("
            SELECT ev.id, ev.user_id, ev.email, ev.expires_at, ev.verified,
                   u.username, u.is_verified as user_verified, u.is_active
            FROM email_verifications ev
            JOIN users u ON ev.user_id = u.id
            WHERE ev.token = ? AND ev.verified = 0 AND u.is_active = 1
        ");
        
        $stmt->execute([$token]);
        $verificationRecord = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$verificationRecord) {
            sendErrorResponse('رمز التأكيد غير صحيح أو مستخدم بالفعل');
        }
        
        // التحقق من انتهاء صلاحية الرمز
        if (strtotime($verificationRecord['expires_at']) < time()) {
            sendErrorResponse('رمز التأكيد منتهي الصلاحية');
        }
        
        // بدء المعاملة
        $connection->beginTransaction();
        
        try {
            // تأكيد البريد الإلكتروني للمستخدم
            $updateUserStmt = $connection->prepare("
                UPDATE users 
                SET is_verified = 1, updated_at = NOW() 
                WHERE id = ?
            ");
            
            $updateUserStmt->execute([$verificationRecord['user_id']]);
            
            // تمييز رمز التأكيد كمستخدم
            $markVerifiedStmt = $connection->prepare("
                UPDATE email_verifications 
                SET verified = 1, verified_at = NOW() 
                WHERE id = ?
            ");
            
            $markVerifiedStmt->execute([$verificationRecord['id']]);
            
            // تسجيل النشاط
            try {
                $checkTable = $connection->prepare("SHOW TABLES LIKE 'activity_logs'");
                $checkTable->execute();
                
                if ($checkTable->rowCount() > 0) {
                    $checkColumns = $connection->prepare("SHOW COLUMNS FROM activity_logs LIKE 'entity_type'");
                    $checkColumns->execute();
                    
                    if ($checkColumns->rowCount() > 0) {
                        $logStmt = $connection->prepare("
                            INSERT INTO activity_logs (user_id, action, entity_type, entity_id, ip_address, user_agent, data)
                            VALUES (?, 'email_verified', 'user', ?, ?, ?, ?)
                        ");
                        
                        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                        $verificationData = json_encode([
                            'verification_time' => date('Y-m-d H:i:s'),
                            'email' => $verificationRecord['email'],
                            'username' => $verificationRecord['username']
                        ]);
                        
                        $logStmt->execute([$verificationRecord['user_id'], $verificationRecord['user_id'], $ipAddress, $userAgent, $verificationData]);
                    }
                }
            } catch (Exception $logError) {
                // تجاهل أخطاء تسجيل النشاط
                error_log('Activity log error in verify-email: ' . $logError->getMessage());
            }
            
            // تأكيد المعاملة
            $connection->commit();
            
            sendSuccessResponse([
                'message' => 'تم تأكيد البريد الإلكتروني بنجاح',
                'user' => [
                    'id' => $verificationRecord['user_id'],
                    'email' => $verificationRecord['email'],
                    'username' => $verificationRecord['username'],
                    'isVerified' => true
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ], 'تم تأكيد البريد الإلكتروني بنجاح');
            
        } catch (Exception $e) {
            // التراجع عن المعاملة
            $connection->rollBack();
            throw $e;
        }
    }
    
} catch (Exception $e) {
    sendErrorResponse($e->getMessage());
} catch (PDOException $e) {
    error_log('Database error in verify-email.php: ' . $e->getMessage());
    sendErrorResponse('خطأ في قاعدة البيانات');
}
?>
