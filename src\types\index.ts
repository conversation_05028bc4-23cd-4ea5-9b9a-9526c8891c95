// أنواع البيانات للمشروع

export interface User {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  rating: number;
  totalTrades: number;
  completionRate: number;
  isVerified: boolean;
  isOnline: boolean;
  country: string;
  joinedAt: Date;
  lastSeen: Date;
}

export interface Offer {
  id: string;
  sellerId: string;
  seller: User;
  type?: 'buy' | 'sell';
  amount: number;
  minAmount: number;
  maxAmount: number;
  price: number;
  currency: string;
  stablecoin?: string; // العملة المستقرة (USDT, USDC, BUSD)
  paymentMethods: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  terms?: string;

  // معلومات إضافية للعرض
  rating?: number;
  trades?: number;
  completionRate?: number;
  avgTime?: string;
  isOnline?: boolean;
  country?: string;
  verified?: boolean;
  views?: number;

  // حقول التكامل مع العقد الذكي
  blockchain_trade_id?: number | null;
  transaction_hash?: string | null;
  contract_status?: 'pending' | 'created' | 'joined' | 'payment_sent' | 'completed' | 'cancelled' | 'disputed';
  escrow_amount?: number | null;
  platform_fee?: number;
  net_amount?: number;
  last_sync_at?: Date | null;
  contract_created_at?: Date | null;
  buyer_address?: string | null;
}

export interface Trade {
  id: string;
  offerId: string;
  sellerId: string;
  buyerId: string;
  amount: number;
  price: number;
  totalAmount: number;
  currency: string;
  stablecoin?: string;
  paymentMethod: string;
  status: TradeStatus;
  createdAt: Date;
  updatedAt: Date;
  paymentSentAt?: Date;
  completedAt?: Date;
  cancelledAt?: Date;
  disputedAt?: Date;
  messages: TradeMessage[];

  // حقول التكامل مع العقد الذكي
  blockchain_trade_id?: number | null;
  offer_blockchain_id?: number | null;
  contract_status?: 'Created' | 'Joined' | 'PaymentSent' | 'Completed' | 'Cancelled' | 'Disputed';
  platform_fee?: number;
  net_amount?: number;

  // معرفات المعاملات
  create_transaction_hash?: string | null;
  join_transaction_hash?: string | null;
  payment_sent_transaction_hash?: string | null;
  complete_transaction_hash?: string | null;
  cancel_transaction_hash?: string | null;

  // timestamps العقد الذكي
  contract_created_at?: Date | null;
  contract_joined_at?: Date | null;
  contract_payment_sent_at?: Date | null;
  contract_completed_at?: Date | null;

  // مزامنة البيانات
  last_sync_at?: Date | null;
  sync_status?: 'synced' | 'pending' | 'failed';
}

export enum TradeStatus {
  CREATED = 'CREATED',
  BUYER_JOINED = 'BUYER_JOINED',
  PAYMENT_SENT = 'PAYMENT_SENT',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  DISPUTED = 'DISPUTED'
}

export interface TradeMessage {
  id: string;
  tradeId: string;
  senderId: string;
  message: string;
  type: 'text' | 'system' | 'payment_proof';
  createdAt: Date;
}

export interface PaymentMethod {
  id: string;
  name: string;
  nameAr: string;
  icon: string;
  isActive: boolean;
}

export interface Country {
  code: string;
  name: string;
  nameAr: string;
  currency: string;
  flag: string;
}

export interface PlatformStats {
  totalTrades: number;
  totalUsers: number;
  totalVolume: number;
  averageRating: number;
  activeOffers: number;
  onlineUsers: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface FilterOptions {
  country?: string;
  paymentMethod?: string;
  currency?: string;
  minAmount?: number;
  maxAmount?: number;
  sortBy?: 'price' | 'rating' | 'trades' | 'created';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface NotificationSettings {
  email: boolean;
  sms: boolean;
  push: boolean;
  tradeUpdates: boolean;
  marketingEmails: boolean;
}

export interface UserSettings {
  language: 'ar' | 'en';
  currency: string;
  timezone: string;
  notifications: NotificationSettings;
  twoFactorEnabled: boolean;
}

export interface SecurityLog {
  id: string;
  userId: string;
  action: string;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
}

export interface Dispute {
  id: string;
  tradeId: string;
  reporterId: string;
  reason: string;
  description: string;
  status: 'open' | 'investigating' | 'resolved';
  resolution?: string;
  resolvedBy?: string;
  createdAt: Date;
  resolvedAt?: Date;
}

export interface Review {
  id: string;
  tradeId: string;
  reviewerId: string;
  revieweeId: string;
  rating: number;
  comment: string;
  createdAt: Date;
}

export interface Message {
  id: string;
  senderId: string;
  senderName: string;
  message: string;
  type: 'text' | 'system' | 'payment_proof';
  timestamp: string;
  isRead: boolean;
  attachment?: string;
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'trade' | 'system' | 'payment' | 'dispute' | 'security' | 'kyc' | 'admin';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  isRead: boolean;
  createdAt: string;
  timestamp?: string | Date; // للتوافق مع المكونات الأخرى
  actionUrl?: string;
  actionLabel?: string;
  data?: any;
}

export interface Admin {
  id: string;
  username: string;
  email: string;
  walletAddress?: string;
  fullName?: string;
  role: 'admin' | 'moderator' | 'support';
  permissions: string[];
  isActive: boolean;
  lastLogin: string;
  createdAt: string;
}

export interface AnalyticsData {
  totalUsers: number;
  activeUsers: number;
  totalTrades: number;
  totalVolume: number;
  platformRevenue: number;
  activeOffers: number;
  completedTrades: number;
  averageRating: number;
  monthlyGrowth: number;
  dailyStats: Array<{
    date: string;
    trades: number;
    volume: number;
    users: number;
  }>;
}

// أنواع النماذج
export interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

export interface RegisterFormData {
  fullName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  agreeToTerms: boolean;
}

export interface CreateOfferFormData {
  amount: number;
  minAmount: number;
  maxAmount: number;
  price: number;
  currency: string;
  paymentMethods: string[];
  terms?: string;
}

export interface TradeFormData {
  amount: number;
  paymentMethod: string;
  message?: string;
}

// أنواع الأحداث
export interface TradeEvent {
  type: 'trade_created' | 'trade_updated' | 'message_sent' | 'payment_confirmed';
  tradeId: string;
  data: any;
  timestamp: Date;
}

// أنواع الأخطاء
export interface ValidationError {
  field: string;
  message: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

// أنواع جديدة للتكامل مع العقد الذكي
export interface ContractEvent {
  id: string;
  event_type: 'TradeCreated' | 'TradeJoined' | 'PaymentSent' | 'TradeCompleted' | 'TradeCancelled' | 'TradeDisputed' | 'DisputeResolved';
  blockchain_trade_id: number;
  transaction_hash: string;
  block_number: number;
  block_hash: string;
  seller_address?: string;
  buyer_address?: string;
  amount?: number;
  fee_amount?: number;
  event_data?: any;
  gas_used?: number;
  gas_price?: number;
  processed: boolean;
  processed_at?: Date;
  error_message?: string;
  event_timestamp: Date;
  created_at: Date;
}

export interface SyncStatus {
  id: string;
  entity_type: 'offer' | 'trade' | 'contract_event';
  entity_id: number;
  blockchain_id?: number;
  sync_status: 'pending' | 'syncing' | 'synced' | 'failed' | 'conflict';
  last_sync_attempt?: Date;
  last_successful_sync?: Date;
  sync_attempts: number;
  max_attempts: number;
  error_message?: string;
  error_code?: string;
  local_hash?: string;
  blockchain_hash?: string;
  conflict_data?: any;
  created_at: Date;
  updated_at: Date;
}

export interface SyncResult {
  success: boolean;
  entityId: string;
  entityType: 'offer' | 'trade' | 'contract_event';
  changes: string[];
  errors: string[];
}

export interface SyncStats {
  totalEntities: number;
  syncedEntities: number;
  failedEntities: number;
  pendingEntities: number;
  lastSyncTime: Date;
  averageSyncTime: number;
}

// أنواع إعدادات العقد الذكي
export interface ContractSettings {
  escrow_contract_address: string;
  usdt_contract_address: string;
  admin_wallet_address: string;
  fee_wallet_address: string;
  network_chain_id: string;
  network_name: string;
  network_rpc_url: string;
  platform_fee: number;
  sync_interval: number;
  max_sync_attempts: number;
  event_sync_enabled: boolean;
  auto_sync_enabled: boolean;
}

// أنواع الأخطاء المحددة للعقد الذكي
export interface ContractError {
  code: 'INSUFFICIENT_FUNDS' | 'USER_REJECTED' | 'NETWORK_ERROR' | 'CONTRACT_ERROR' | 'SYNC_ERROR';
  message: string;
  details?: any;
  transaction_hash?: string;
  block_number?: number;
}