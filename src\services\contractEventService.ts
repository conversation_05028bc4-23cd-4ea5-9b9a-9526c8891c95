/**
 * خدمة مراقبة أحداث العقد الذكي
 * Smart Contract Event Monitoring Service
 */

import { ethers } from 'ethers';
import { notificationService } from './notificationService';
import { smartNotificationService } from './smartNotificationService';
import { ESCROW_ABI } from '../constants';
import { MultiRPCProvider, createMultiRPCProvider, RPC_CONFIG } from '../utils/rpcOptimizations';
import { OptimizedContractManager, getLogsWithChunking, optimizedRequest } from '../utils/rpcLimitFix';

// أنواع الأحداث
export interface ContractEventData {
  eventType: string;
  tradeId: number;
  transactionHash: string;
  blockNumber: number;
  blockHash: string;
  sellerAddress?: string;
  buyerAddress?: string;
  amount?: number;
  feeAmount?: number;
  eventData?: any;
  gasUsed?: number;
  gasPrice?: number;
  timestamp: Date;
}

// إحصائيات مراقبة الأحداث
export interface EventMonitoringStats {
  totalEventsProcessed: number;
  eventsProcessedToday: number;
  lastProcessedBlock: number;
  isMonitoring: boolean;
  errors: number;
  lastError?: string;
  uptime: number;
}

class ContractEventService {
  private provider: ethers.BrowserProvider | ethers.JsonRpcProvider | null = null;
  private multiRPCProvider: MultiRPCProvider | null = null;
  private contract: ethers.Contract | null = null;
  private optimizedContractManager: OptimizedContractManager | null = null;
  private isMonitoring: boolean = false;
  private lastProcessedBlock: number = 0;
  private eventListeners: Map<string, ethers.ContractEventName> = new Map();
  private stats: EventMonitoringStats = {
    totalEventsProcessed: 0,
    eventsProcessedToday: 0,
    lastProcessedBlock: 0,
    isMonitoring: false,
    errors: 0,
    uptime: 0
  };
  private startTime: number = 0;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;

  constructor() {
    this.initializeService();
  }

  /**
   * تهيئة الخدمة
   */
  private async initializeService(): Promise<void> {
    try {
      console.log('🔄 بدء تهيئة خدمة مراقبة الأحداث...');
      await this.setupProvider();
      await this.setupContract();
      await this.loadLastProcessedBlock();
      console.log('✅ تم تهيئة خدمة مراقبة الأحداث بنجاح');
    } catch (error) {
      console.warn('⚠️ خطأ في تهيئة خدمة مراقبة الأحداث:', error.message || error);
      // تهيئة قيم افتراضية للعمل في وضع محدود
      this.lastProcessedBlock = 56024928;
      this.stats.lastProcessedBlock = this.lastProcessedBlock;
      console.log('🔧 تم تهيئة الخدمة في الوضع المحدود');
    }
  }

  /**
   * إعداد المزود
   */
  private async setupProvider(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.ethereum) {
        // لا نطلب الاتصال، فقط نهيئ المزود
        this.provider = new ethers.BrowserProvider(window.ethereum);
        console.log('✅ تم تهيئة مزود MetaMask (بدون اتصال)');
      } else {
        // إعداد multi-RPC provider كبديل
        const isTestnet = process.env.NEXT_PUBLIC_NETWORK_NAME?.includes('Testnet') ?? true;
        this.multiRPCProvider = createMultiRPCProvider(isTestnet);
        this.provider = this.multiRPCProvider.getCurrentProvider();
        console.log('✅ تم إعداد multi-RPC provider');
      }
    } catch (error) {
      console.warn('⚠️ تحذير في إعداد مزود الشبكة:', error);
      // لا نرمي الخطأ لتجنب كسر التطبيق
      console.warn('⚠️ سيتم تجاهل خطأ الشبكة مؤقتاً');
    }
  }

  /**
   * إعداد العقد
   */
  private async setupContract(): Promise<void> {
    if (!this.provider) {
      throw new Error('المزود غير متوفر');
    }

    // استخدام العقد المحسن الجديد (Core Escrow)
    const contractAddress = process.env.NEXT_PUBLIC_CORE_ESCROW_ADDRESS || process.env.NEXT_PUBLIC_ESCROW_CONTRACT_ADDRESS;
    if (!contractAddress) {
      throw new Error('عنوان العقد المحسن غير محدد');
    }

    try {
      // التحقق من صحة العنوان (يجب أن يبدأ بـ 0x ويكون طوله 42 حرف)
      if (!contractAddress.startsWith('0x') || contractAddress.length !== 42) {
        throw new Error(`عنوان العقد غير صالح: ${contractAddress}`);
      }

      // تحويل العنوان إلى checksum صحيح
      // أولاً نحوله إلى أحرف صغيرة ثم نطبق checksum
      const lowercaseAddress = contractAddress.toLowerCase();
      const checksumAddress = ethers.getAddress(lowercaseAddress);

      this.contract = new ethers.Contract(checksumAddress, ESCROW_ABI, this.provider);

      // إنشاء مدير العقد المحسن
      this.optimizedContractManager = new OptimizedContractManager(checksumAddress, ESCROW_ABI);

      console.log(`✅ تم إعداد العقد بعنوان: ${checksumAddress}`);
    } catch (error) {
      console.error('❌ خطأ في عنوان العقد:', error);
      // لا نرمي خطأ هنا لتجنب كسر التطبيق
      console.warn('⚠️ سيتم تجاهل خطأ العقد الذكي مؤقتاً');
    }
  }

  /**
   * تحميل آخر بلوك تم معالجته
   */
  private async loadLastProcessedBlock(): Promise<void> {
    try {
      // جلب آخر بلوك من قاعدة البيانات
      const lastEvent = await this.getLastProcessedEvent();
      if (lastEvent && lastEvent.block_number) {
        this.lastProcessedBlock = lastEvent.block_number;
        console.log(`📍 آخر بلوك تم معالجته من قاعدة البيانات: ${this.lastProcessedBlock}`);
      } else {
        // إذا لم توجد أحداث أو فشل API، ابدأ من البلوك الحالي
        if (this.provider) {
          const currentBlock = await this.provider.getBlockNumber();
          this.lastProcessedBlock = currentBlock - 1000; // ابدأ من 1000 بلوك سابق
          console.log(`📍 بدء من البلوك الحالي ناقص 1000: ${this.lastProcessedBlock}`);
        } else {
          // إذا لم يكن هناك provider، استخدم قيمة افتراضية
          this.lastProcessedBlock = 56024928; // قيمة افتراضية
          console.log(`📍 استخدام البلوك الافتراضي: ${this.lastProcessedBlock}`);
        }
      }

      this.stats.lastProcessedBlock = this.lastProcessedBlock;
    } catch (error) {
      console.warn('⚠️ خطأ في تحميل آخر بلوك، استخدام قيمة افتراضية:', error);
      try {
        if (this.provider) {
          const currentBlock = await this.provider.getBlockNumber();
          this.lastProcessedBlock = currentBlock - 100;
        } else {
          this.lastProcessedBlock = 56024928; // قيمة افتراضية
        }
      } catch (providerError) {
        console.warn('⚠️ خطأ في الوصول للـ provider، استخدام قيمة افتراضية ثابتة');
        this.lastProcessedBlock = 56024928;
      }
      this.stats.lastProcessedBlock = this.lastProcessedBlock;
    }
  }

  /**
   * بدء مراقبة الأحداث
   */
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      console.log('⚠️ المراقبة قيد التشغيل بالفعل');
      return;
    }

    if (!this.contract || !this.provider) {
      throw new Error('العقد أو المزود غير متوفر');
    }

    this.isMonitoring = true;
    this.startTime = Date.now();
    this.stats.isMonitoring = true;
    this.reconnectAttempts = 0;

    console.log('🔍 بدء مراقبة أحداث العقد الذكي...');

    try {
      // معالجة الأحداث السابقة أولاً
      await this.processPastEvents();

      // بدء مراقبة الأحداث الجديدة
      await this.startRealTimeMonitoring();

      console.log('✅ تم بدء مراقبة الأحداث بنجاح');
    } catch (error) {
      console.error('❌ خطأ في بدء مراقبة الأحداث:', error);
      this.handleMonitoringError(error);
    }
  }

  /**
   * معالجة الأحداث السابقة
   */
  private async processPastEvents(): Promise<void> {
    if (!this.contract) return;

    try {
      const currentBlock = await this.provider!.getBlockNumber();
      const fromBlock = this.lastProcessedBlock + 1;

      if (fromBlock > currentBlock) {
        console.log('📝 لا توجد أحداث جديدة للمعالجة');
        return;
      }

      // تحديد نطاق آمن للبلوكات (حد أقصى 1000 بلوك في المرة الواحدة)
      const maxBlockRange = 1000;
      const blockRange = Math.min(currentBlock - fromBlock + 1, maxBlockRange);
      const toBlock = fromBlock + blockRange - 1;

      console.log(`🔄 معالجة الأحداث من البلوك ${fromBlock} إلى ${toBlock} (${blockRange} بلوك)`);

      // جلب الأحداث بشكل تدريجي
      const eventNames = [
        'TradeCreated',
        'BuyerJoined',
        'PaymentSent',
        'PaymentConfirmed',
        'TradeCompleted',
        'TradeCancelled',
        'TradeDisputed',
        'DisputeResolved'
      ];

      for (const eventName of eventNames) {
        try {
          await this.processEventsByChunks(eventName, fromBlock, toBlock);
        } catch (error) {
          console.error(`خطأ في معالجة أحداث ${eventName}:`, error);
        }
      }

      this.lastProcessedBlock = toBlock;
      this.stats.lastProcessedBlock = toBlock;
      await this.saveLastProcessedBlock(toBlock);

      // إذا كان هناك المزيد من البلوكات، معالجتها في المرة القادمة
      if (toBlock < currentBlock) {
        console.log(`📋 تبقى ${currentBlock - toBlock} بلوك للمعالجة في المرة القادمة`);
      }

    } catch (error) {
      console.error('خطأ في معالجة الأحداث السابقة:', error);
      // لا نرمي الخطأ لتجنب كسر التطبيق
      console.warn('⚠️ سيتم المحاولة مرة أخرى لاحقاً');
    }
  }

  /**
   * معالجة الأحداث بشكل تدريجي لتجنب حدود RPC (محسن)
   */
  private async processEventsByChunks(eventName: string, fromBlock: number, toBlock: number): Promise<void> {
    if (!this.optimizedContractManager) {
      console.warn('⚠️ مدير العقد المحسن غير متوفر، استخدام الطريقة التقليدية');
      return this.processEventsByChunksLegacy(eventName, fromBlock, toBlock);
    }

    try {
      console.log(`🔍 جلب أحداث ${eventName} من البلوك ${fromBlock} إلى ${toBlock} (محسن)`);

      const events = await this.optimizedContractManager.getEvents(eventName, fromBlock, toBlock);

      console.log(`📋 تم العثور على ${events.length} حدث من نوع ${eventName}`);

      for (const event of events) {
        await this.processEvent(event, eventName);
      }

    } catch (error) {
      console.error(`خطأ في جلب أحداث ${eventName} (محسن):`, error);

      // العودة للطريقة التقليدية في حالة الفشل
      console.log('🔄 العودة للطريقة التقليدية...');
      await this.processEventsByChunksLegacy(eventName, fromBlock, toBlock);
    }
  }

  /**
   * معالجة الأحداث بالطريقة التقليدية (احتياطي)
   */
  private async processEventsByChunksLegacy(eventName: string, fromBlock: number, toBlock: number): Promise<void> {
    if (!this.contract) return;

    const chunkSize = 100; // حجم أصغر للأمان

    for (let start = fromBlock; start <= toBlock; start += chunkSize) {
      const end = Math.min(start + chunkSize - 1, toBlock);

      try {
        console.log(`🔍 جلب أحداث ${eventName} من البلوك ${start} إلى ${end} (تقليدي)`);

        const events = await optimizedRequest(() =>
          this.contract!.queryFilter(
            this.contract!.filters[eventName](),
            start,
            end
          )
        );

        console.log(`📋 تم العثور على ${events.length} حدث من نوع ${eventName}`);

        for (const event of events) {
          await this.processEvent(event, eventName);
        }

        // تأخير أطول للأمان
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        console.error(`خطأ في جلب أحداث ${eventName} من ${start} إلى ${end}:`, error);

        // تخطي هذا الجزء في حالة الفشل
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('limit exceeded')) {
          console.warn(`⏭️ تخطي البلوكات ${start}-${end} بسبب حد RPC`);
          continue;
        }
      }
    }
  }

  /**
   * بدء مراقبة الأحداث الفورية
   */
  private async startRealTimeMonitoring(): Promise<void> {
    if (!this.contract) return;

    // إعداد مستمعي الأحداث
    this.setupEventListeners();

    // مراقبة دورية للتأكد من عدم فقدان أحداث
    setInterval(async () => {
      if (this.isMonitoring) {
        await this.checkForMissedEvents();
      }
    }, 30000); // كل 30 ثانية
  }

  /**
   * إعداد مستمعي الأحداث
   */
  private setupEventListeners(): void {
    if (!this.contract) return;

    // TradeCreated
    this.contract.on('TradeCreated', async (tradeId, seller, amount, event) => {
      await this.handleTradeCreated(tradeId, seller, amount, event);
    });

    // BuyerJoined
    this.contract.on('BuyerJoined', async (tradeId, buyer, event) => {
      await this.handleBuyerJoined(tradeId, buyer, event);
    });

    // PaymentSent
    this.contract.on('PaymentSent', async (tradeId, buyer, event) => {
      await this.handlePaymentSent(tradeId, buyer, event);
    });

    // PaymentConfirmed
    this.contract.on('PaymentConfirmed', async (tradeId, seller, event) => {
      await this.handlePaymentConfirmed(tradeId, seller, event);
    });

    // TradeCompleted
    this.contract.on('TradeCompleted', async (tradeId, buyerAmount, feeAmount, event) => {
      await this.handleTradeCompleted(tradeId, buyerAmount, feeAmount, event);
    });

    // TradeCancelled
    this.contract.on('TradeCancelled', async (tradeId, reason, event) => {
      await this.handleTradeCancelled(tradeId, reason, event);
    });

    // TradeDisputed
    this.contract.on('TradeDisputed', async (tradeId, caller, event) => {
      await this.handleTradeDisputed(tradeId, caller, event);
    });

    // DisputeResolved
    this.contract.on('DisputeResolved', async (tradeId, winner, event) => {
      await this.handleDisputeResolved(tradeId, winner, event);
    });

    console.log('🎧 تم إعداد مستمعي الأحداث');
  }

  /**
   * معالجة حدث إنشاء صفقة
   */
  private async handleTradeCreated(tradeId: any, seller: string, amount: any, event: any): Promise<void> {
    try {
      const eventData: ContractEventData = {
        eventType: 'TradeCreated',
        tradeId: Number(tradeId),
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
        blockHash: event.blockHash,
        sellerAddress: seller,
        amount: Number(ethers.formatUnits(amount, 18)),
        timestamp: new Date()
      };

      await this.processEvent(event, 'TradeCreated', eventData);

      // إشعار فوري
      notificationService.success(`تم إنشاء صفقة جديدة #${tradeId}`);

      // إشعار ذكي
      smartNotificationService.createContractEventNotification(
        'TradeCreated',
        tradeId,
        undefined, // سيتم إرسال للمستخدمين المعنيين
        {
          sellerAddress: seller,
          amount: Number(ethers.formatUnits(amount, 18))
        }
      );
      
    } catch (error) {
      console.error('خطأ في معالجة حدث إنشاء الصفقة:', error);
    }
  }

  /**
   * معالجة حدث انضمام المشتري
   */
  private async handleBuyerJoined(tradeId: any, buyer: string, event: any): Promise<void> {
    try {
      const eventData: ContractEventData = {
        eventType: 'BuyerJoined',
        tradeId: Number(tradeId),
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
        blockHash: event.blockHash,
        buyerAddress: buyer,
        timestamp: new Date()
      };

      await this.processEvent(event, 'BuyerJoined', eventData);

      // إشعار فوري
      notificationService.success(`انضم مشتري للصفقة #${tradeId}`);

      // إشعار ذكي
      smartNotificationService.createContractEventNotification(
        'BuyerJoined',
        tradeId,
        undefined,
        {
          buyerAddress: buyer
        }
      );

    } catch (error) {
      console.error('خطأ في معالجة حدث انضمام المشتري:', error);
    }
  }

  /**
   * معالجة حدث إرسال الدفع
   */
  private async handlePaymentSent(tradeId: any, buyer: string, event: any): Promise<void> {
    try {
      const eventData: ContractEventData = {
        eventType: 'PaymentSent',
        tradeId: Number(tradeId),
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
        blockHash: event.blockHash,
        buyerAddress: buyer,
        timestamp: new Date()
      };

      await this.processEvent(event, 'PaymentSent', eventData);

      // إشعار فوري
      notificationService.info(`تم إرسال الدفع للصفقة #${tradeId}`);

      // إشعار ذكي
      smartNotificationService.createContractEventNotification(
        'PaymentSent',
        tradeId,
        undefined,
        {
          buyerAddress: buyer
        }
      );

    } catch (error) {
      console.error('خطأ في معالجة حدث إرسال الدفع:', error);
    }
  }

  /**
   * معالجة حدث تأكيد الدفع
   */
  private async handlePaymentConfirmed(tradeId: any, seller: string, event: any): Promise<void> {
    try {
      const eventData: ContractEventData = {
        eventType: 'PaymentConfirmed',
        tradeId: Number(tradeId),
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
        blockHash: event.blockHash,
        sellerAddress: seller,
        timestamp: new Date()
      };

      await this.processEvent(event, 'PaymentConfirmed', eventData);

      // إشعار فوري
      notificationService.success(`تم تأكيد الدفع للصفقة #${tradeId}`);

      // إشعار ذكي
      smartNotificationService.createContractEventNotification(
        'PaymentConfirmed',
        tradeId,
        undefined,
        {
          sellerAddress: seller
        }
      );

    } catch (error) {
      console.error('خطأ في معالجة حدث تأكيد الدفع:', error);
    }
  }

  /**
   * معالجة حدث إتمام الصفقة
   */
  private async handleTradeCompleted(tradeId: any, buyerAmount: any, feeAmount: any, event: any): Promise<void> {
    try {
      const eventData: ContractEventData = {
        eventType: 'TradeCompleted',
        tradeId: Number(tradeId),
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
        blockHash: event.blockHash,
        amount: Number(ethers.formatUnits(buyerAmount, 18)),
        feeAmount: Number(ethers.formatUnits(feeAmount, 18)),
        timestamp: new Date()
      };

      await this.processEvent(event, 'TradeCompleted', eventData);

      // إشعار فوري
      notificationService.success(`تمت الصفقة #${tradeId} بنجاح! 🎉`);

      // إشعار ذكي
      smartNotificationService.createContractEventNotification(
        'TradeCompleted',
        tradeId,
        undefined,
        {
          buyerAmount: Number(ethers.formatUnits(buyerAmount, 18)),
          feeAmount: Number(ethers.formatUnits(feeAmount, 18))
        }
      );

    } catch (error) {
      console.error('خطأ في معالجة حدث إتمام الصفقة:', error);
    }
  }

  /**
   * معالجة حدث إلغاء الصفقة
   */
  private async handleTradeCancelled(tradeId: any, reason: string, event: any): Promise<void> {
    try {
      const eventData: ContractEventData = {
        eventType: 'TradeCancelled',
        tradeId: Number(tradeId),
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
        blockHash: event.blockHash,
        eventData: { reason },
        timestamp: new Date()
      };

      await this.processEvent(event, 'TradeCancelled', eventData);

      // إشعار فوري
      notificationService.warning(`تم إلغاء الصفقة #${tradeId}: ${reason}`);

      // إشعار ذكي
      smartNotificationService.createContractEventNotification(
        'TradeCancelled',
        tradeId,
        undefined,
        {
          reason: reason
        }
      );

    } catch (error) {
      console.error('خطأ في معالجة حدث إلغاء الصفقة:', error);
    }
  }

  /**
   * معالجة حدث النزاع
   */
  private async handleTradeDisputed(tradeId: any, caller: string, event: any): Promise<void> {
    try {
      const eventData: ContractEventData = {
        eventType: 'TradeDisputed',
        tradeId: Number(tradeId),
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
        blockHash: event.blockHash,
        eventData: { caller },
        timestamp: new Date()
      };

      await this.processEvent(event, 'TradeDisputed', eventData);

      // إشعار فوري
      notificationService.error(`نزاع على الصفقة #${tradeId} ⚠️`);

      // إشعار ذكي حرج
      smartNotificationService.createContractEventNotification(
        'TradeDisputed',
        tradeId,
        undefined,
        {
          caller: caller
        }
      );

    } catch (error) {
      console.error('خطأ في معالجة حدث النزاع:', error);
    }
  }

  /**
   * معالجة حدث حل النزاع
   */
  private async handleDisputeResolved(tradeId: any, winner: string, event: any): Promise<void> {
    try {
      const eventData: ContractEventData = {
        eventType: 'DisputeResolved',
        tradeId: Number(tradeId),
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
        blockHash: event.blockHash,
        eventData: { winner },
        timestamp: new Date()
      };

      await this.processEvent(event, 'DisputeResolved', eventData);

      // إشعار فوري
      notificationService.success(`تم حل النزاع للصفقة #${tradeId}`);

      // إشعار ذكي
      smartNotificationService.createContractEventNotification(
        'DisputeResolved',
        tradeId,
        undefined,
        {
          winner: winner
        }
      );

    } catch (error) {
      console.error('خطأ في معالجة حدث حل النزاع:', error);
    }
  }

  /**
   * معالجة حدث عام
   */
  private async processEvent(event: any, eventType: string, eventData?: ContractEventData): Promise<void> {
    try {
      // التحقق من عدم معالجة الحدث مسبقاً
      const existingEvent = await this.checkEventExists(event.transactionHash, eventType);
      if (existingEvent) {
        console.log(`⚠️ الحدث ${eventType} تم معالجته مسبقاً: ${event.transactionHash}`);
        return;
      }

      // إنشاء بيانات الحدث إذا لم تُمرر
      if (!eventData) {
        eventData = await this.createEventData(event, eventType);
      }

      // حفظ الحدث في قاعدة البيانات
      await this.saveEventToDatabase(eventData);

      // تحديث حالة الصفقة في قاعدة البيانات
      await this.updateTradeStatus(eventData);

      // تحديث الإحصائيات
      this.updateStats();

      console.log(`✅ تم معالجة حدث ${eventType} للصفقة #${eventData.tradeId}`);

    } catch (error) {
      console.error(`❌ خطأ في معالجة الحدث ${eventType}:`, error);
      this.stats.errors++;
      this.stats.lastError = error instanceof Error ? error.message : String(error);
      throw error;
    }
  }

  /**
   * إنشاء بيانات الحدث
   */
  private async createEventData(event: any, eventType: string): Promise<ContractEventData> {
    const block = await this.provider!.getBlock(event.blockNumber);

    return {
      eventType,
      tradeId: Number(event.args[0]), // أول معامل عادة هو tradeId
      transactionHash: event.transactionHash,
      blockNumber: event.blockNumber,
      blockHash: event.blockHash,
      timestamp: new Date(block!.timestamp * 1000),
      eventData: event.args
    };
  }

  /**
   * حفظ الحدث في قاعدة البيانات
   */
  private async saveEventToDatabase(eventData: ContractEventData): Promise<void> {
    try {
      const response = await fetch('/api/contract-events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event_type: eventData.eventType,
          blockchain_trade_id: eventData.tradeId,
          transaction_hash: eventData.transactionHash,
          block_number: eventData.blockNumber,
          block_hash: eventData.blockHash,
          seller_address: eventData.sellerAddress,
          buyer_address: eventData.buyerAddress,
          amount: eventData.amount,
          fee_amount: eventData.feeAmount,
          event_data: eventData.eventData,
          event_timestamp: eventData.timestamp.toISOString()
        })
      });

      if (!response.ok) {
        throw new Error(`فشل في حفظ الحدث: ${response.statusText}`);
      }

    } catch (error) {
      console.error('خطأ في حفظ الحدث في قاعدة البيانات:', error);
      throw error;
    }
  }

  /**
   * تحديث حالة الصفقة
   */
  private async updateTradeStatus(eventData: ContractEventData): Promise<void> {
    try {
      const statusMap: { [key: string]: string } = {
        'TradeCreated': 'created',
        'BuyerJoined': 'joined',
        'PaymentSent': 'payment_sent',
        'PaymentConfirmed': 'payment_received',
        'TradeCompleted': 'completed',
        'TradeCancelled': 'cancelled',
        'TradeDisputed': 'disputed',
        'DisputeResolved': 'completed'
      };

      const newStatus = statusMap[eventData.eventType];
      if (!newStatus) return;

      const response = await fetch('/api/trades/update-status', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          blockchain_trade_id: eventData.tradeId,
          status: newStatus,
          contract_status: eventData.eventType,
          transaction_hash: eventData.transactionHash,
          last_sync_at: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error(`فشل في تحديث حالة الصفقة: ${response.statusText}`);
      }

    } catch (error) {
      console.error('خطأ في تحديث حالة الصفقة:', error);
      throw error;
    }
  }

  /**
   * التحقق من وجود الحدث
   */
  private async checkEventExists(transactionHash: string, eventType: string): Promise<boolean> {
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost/ikaros-p2p/api';
      const response = await fetch(`${apiUrl}/contract-events/check?tx=${transactionHash}&type=${eventType}`);
      const data = await response.json();
      return data.exists || false;
    } catch (error) {
      console.error('خطأ في التحقق من وجود الحدث:', error);
      return false;
    }
  }

  /**
   * جلب آخر حدث تم معالجته
   */
  private async getLastProcessedEvent(): Promise<any> {
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost/ikaros-p2p/api';
      console.log(`🔍 محاولة جلب آخر حدث من: ${apiUrl}/contract-events?action=last`);

      const response = await fetch(`${apiUrl}/contract-events?action=last`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // إضافة timeout
        signal: AbortSignal.timeout(5000) // 5 ثوانٍ timeout
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ تم جلب آخر حدث بنجاح:', data);
        return data.data;
      } else {
        console.warn(`⚠️ API response not ok: ${response.status} ${response.statusText}`);
        return null;
      }
    } catch (error) {
      if (error.name === 'TimeoutError') {
        console.warn('⚠️ انتهت مهلة الاتصال بـ API أحداث العقد');
      } else if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        console.warn('⚠️ فشل الاتصال بـ API أحداث العقد - قد يكون الخادم غير متاح');
      } else {
        console.warn('⚠️ خطأ في جلب آخر حدث:', error.message);
      }
      return null;
    }
  }

  /**
   * حفظ آخر بلوك تم معالجته
   */
  private async saveLastProcessedBlock(blockNumber: number): Promise<void> {
    try {
      const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost/ikaros-p2p/api';
      const response = await fetch(`${apiUrl}/contract-events/last-block`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ block_number: blockNumber }),
        signal: AbortSignal.timeout(5000) // 5 ثوانٍ timeout
      });

      if (response.ok) {
        console.log(`💾 تم حفظ آخر بلوك: ${blockNumber}`);
      } else {
        console.warn(`⚠️ فشل حفظ آخر بلوك: ${response.status}`);
      }
    } catch (error) {
      if (error.name === 'TimeoutError') {
        console.warn('⚠️ انتهت مهلة حفظ آخر بلوك');
      } else {
        console.warn('⚠️ خطأ في حفظ آخر بلوك:', error.message || error);
      }
      // لا نرمي الخطأ لتجنب كسر العملية
    }
  }

  /**
   * فحص الأحداث المفقودة
   */
  private async checkForMissedEvents(): Promise<void> {
    try {
      const currentBlock = await this.provider!.getBlockNumber();
      const gap = currentBlock - this.lastProcessedBlock;

      if (gap > 10) { // إذا كان هناك فجوة أكبر من 10 بلوكات
        console.log(`🔍 فحص الأحداث المفقودة: فجوة ${gap} بلوك`);

        // إذا كان الفجوة كبيرة جداً، نحذر المستخدم
        if (gap > 5000) {
          console.warn(`⚠️ فجوة كبيرة جداً (${gap} بلوك)، قد تستغرق المعالجة وقتاً طويلاً`);
        }

        await this.processPastEvents();
      }
    } catch (error) {
      console.error('خطأ في فحص الأحداث المفقودة:', error);
      // لا نرمي الخطأ لتجنب كسر المراقبة
    }
  }

  /**
   * تحديث الإحصائيات
   */
  private updateStats(): void {
    this.stats.totalEventsProcessed++;
    this.stats.eventsProcessedToday++;
    this.stats.uptime = Date.now() - this.startTime;
  }

  /**
   * معالجة خطأ المراقبة
   */
  private handleMonitoringError(error: any): void {
    this.stats.errors++;
    this.stats.lastError = error.message;

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`🔄 محاولة إعادة الاتصال ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

      setTimeout(async () => {
        try {
          await this.initializeService();
          await this.startMonitoring();
        } catch (retryError) {
          console.error('فشل في إعادة الاتصال:', retryError);
        }
      }, 5000 * this.reconnectAttempts); // تأخير متزايد
    } else {
      console.error('❌ فشل في إعادة الاتصال بعد عدة محاولات');
      this.stopMonitoring();
    }
  }

  /**
   * إيقاف مراقبة الأحداث
   */
  async stopMonitoring(): Promise<void> {
    if (!this.isMonitoring) {
      console.log('⚠️ المراقبة متوقفة بالفعل');
      return;
    }

    this.isMonitoring = false;
    this.stats.isMonitoring = false;

    // إزالة جميع مستمعي الأحداث
    if (this.contract) {
      this.contract.removeAllListeners();
    }

    console.log('⏹️ تم إيقاف مراقبة الأحداث');
  }

  /**
   * إعادة تشغيل المراقبة
   */
  async restartMonitoring(): Promise<void> {
    console.log('🔄 إعادة تشغيل مراقبة الأحداث...');

    await this.stopMonitoring();
    await this.initializeService();
    await this.startMonitoring();
  }

  /**
   * الحصول على إحصائيات المراقبة
   */
  getStats(): EventMonitoringStats {
    return { ...this.stats };
  }

  /**
   * فرض معالجة حدث معين
   */
  async forceProcessEvent(transactionHash: string): Promise<void> {
    try {
      const receipt = await this.provider!.getTransactionReceipt(transactionHash);
      if (!receipt) {
        throw new Error('لم يتم العثور على المعاملة');
      }

      // معالجة جميع الأحداث في هذه المعاملة
      for (const log of receipt.logs) {
        try {
          const parsedLog = this.contract!.interface.parseLog(log);
          if (parsedLog) {
            await this.processEvent(parsedLog, parsedLog.name);
          }
        } catch (error) {
          // تجاهل الأحداث التي لا تنتمي لعقدنا
        }
      }

      console.log(`✅ تم فرض معالجة أحداث المعاملة: ${transactionHash}`);
    } catch (error) {
      console.error('خطأ في فرض معالجة الحدث:', error);
      throw error;
    }
  }

  /**
   * تنظيف الموارد
   */
  cleanup(): void {
    this.stopMonitoring();
    this.provider = null;
    this.contract = null;
    this.eventListeners.clear();
  }
}

// إنشاء مثيل واحد من الخدمة
export const contractEventService = new ContractEventService();

// تصدير الخدمة كافتراضي
export default contractEventService;
