'use client';

import { useState, useEffect } from 'react';
import {
  Wallet,
  CheckCircle,
  AlertCircle,
  Copy,
  ExternalLink,
  LogOut,
  ChevronDown,
  Settings,
  Loader2,
  Shield,
  Zap,
  WifiOff,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuth } from '@/contexts/AuthContext';
import { walletService } from '@/services/walletService';
import { notificationService } from '@/services/notificationService';
import { detectMetaMask, detectTrustWallet, getWalletErrorMessage } from '@/utils/walletDetection';

interface UnifiedWalletManagerProps {
  variant?: 'header' | 'card' | 'inline' | 'full' | 'compact' | 'mobile';
  showBalance?: boolean;
  showNetwork?: boolean;
  showActions?: boolean;
  showConnectionOptions?: boolean;
  autoConnect?: boolean;
  className?: string;
  onConnectionChange?: (isConnected: boolean, address?: string) => void;
  onError?: (error: string) => void;
}

export default function UnifiedWalletManager({
  variant = 'inline',
  showBalance = false,
  showNetwork = true,
  showActions = true,
  showConnectionOptions = true,
  autoConnect = false,
  className = '',
  onConnectionChange,
  onError
}: UnifiedWalletManagerProps) {
  const { t } = useTranslation();
  const {
    isWalletConnected,
    walletAddress,
    walletBalance,
    walletNetwork,
    connectWallet,
    disconnectWallet,
    isLoading
  } = useAuth();

  // حالات المكون المحلية
  const [isConnecting, setIsConnecting] = useState(false);
  const [showWalletOptions, setShowWalletOptions] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [showBalanceDetails, setShowBalanceDetails] = useState(false);
  const [error, setError] = useState<string>('');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // تأثيرات جانبية
  useEffect(() => {
    if (autoConnect && !isWalletConnected && !isConnecting) {
      handleConnectWallet('metamask');
    }
  }, [autoConnect, isWalletConnected, isConnecting]);

  // مسح الأخطاء عند تغيير حالة الاتصال
  useEffect(() => {
    if (isWalletConnected) {
      setError('');
    }
  }, [isWalletConnected]);

  // دوال المساعدة
  const formatAddress = (addr: string, length: number = 6) => {
    if (!addr) return '';
    return `${addr.slice(0, length)}...${addr.slice(-4)}`;
  };

  const formatBalance = (balance: string) => {
    const num = parseFloat(balance);
    if (num === 0) return '0';
    if (num < 0.001) return '< 0.001';
    if (num < 1) return num.toFixed(4);
    return num.toFixed(3);
  };

  // نسخ العنوان
  const copyAddress = async () => {
    if (walletAddress) {
      try {
        await navigator.clipboard.writeText(walletAddress);
        notificationService.success(t('wallet.addressCopied'));
      } catch (error) {
        notificationService.error(t('wallet.copyFailed'));
      }
    }
  };

  // فتح في المستكشف
  const openInExplorer = () => {
    if (walletAddress) {
      const explorerUrl = walletNetwork?.includes('testnet')
        ? `https://testnet.bscscan.com/address/${walletAddress}`
        : `https://bscscan.com/address/${walletAddress}`;
      window.open(explorerUrl, '_blank');
    }
  };

  // تحديث معلومات المحفظة
  const refreshWalletInfo = async () => {
    if (!isWalletConnected) return;

    setIsRefreshing(true);
    try {
      const walletInfo = await walletService.getWalletInfo();
      if (walletInfo) {
        // سيتم تحديث الحالة عبر AuthContext
        notificationService.success(t('wallet.refreshed'));
      }
    } catch (error: any) {
      const errorMessage = error.message || t('wallet.refreshFailed');
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsRefreshing(false);
    }
  };

  // الاتصال بمحفظة محددة
  const handleConnectWallet = async (walletType: 'metamask' | 'trustwallet') => {
    setIsConnecting(true);
    setError('');

    try {
      // التحقق من توفر المحفظة
      const walletStatus = walletType === 'metamask' ? detectMetaMask() : detectTrustWallet();

      if (!walletStatus.isInstalled || !walletStatus.isAvailable) {
        const errorMessage = getWalletErrorMessage(walletType, walletStatus, t);
        throw new Error(errorMessage);
      }

      // الاتصال الفعلي
      await connectWallet();
      setShowWalletOptions(false);
      setShowDetails(false);

      onConnectionChange?.(true, walletAddress);
      notificationService.success(t('wallet.connected.title'));

    } catch (error: any) {
      const errorMessage = error.message || t('wallet.connectionFailed');
      setError(errorMessage);
      onError?.(errorMessage);
      notificationService.error(errorMessage);
    } finally {
      setIsConnecting(false);
    }
  };

  // قطع الاتصال
  const handleDisconnect = () => {
    try {
      disconnectWallet();
      setShowDetails(false);
      setShowWalletOptions(false);
      setError('');

      onConnectionChange?.(false);
      notificationService.success(t('wallet.disconnected'));
    } catch (error: any) {
      const errorMessage = error.message || t('wallet.disconnectFailed');
      setError(errorMessage);
      onError?.(errorMessage);
    }
  };

  // التحقق من توفر المحافظ
  const getAvailableWallets = () => {
    const wallets = [
      {
        id: 'metamask',
        name: 'MetaMask',
        icon: '🦊',
        status: detectMetaMask(),
        description: t('wallet.metamask.description'),
        downloadUrl: 'https://metamask.io/download/'
      },
      {
        id: 'trustwallet',
        name: 'Trust Wallet',
        icon: '🛡️',
        status: detectTrustWallet(),
        description: t('wallet.trustwallet.description'),
        downloadUrl: 'https://trustwallet.com/download'
      }
    ];

    return wallets.filter(wallet => wallet.status.isInstalled && wallet.status.isAvailable);
  };

  // مكون اختيار المحفظة
  const WalletSelectionModal = () => {
    const availableWallets = getAvailableWallets();

    if (!showWalletOptions) return null;

    return (
      <>
        {/* خلفية شفافة */}
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
          onClick={() => setShowWalletOptions(false)}
        />

        {/* نافذة الاختيار */}
        <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-full max-w-md mx-4">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 p-6 animate-in zoom-in-95 duration-200">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Wallet className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                {t('wallet.connect.title')}
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                {t('wallet.connect.description')}
              </p>
            </div>

            {availableWallets.length > 0 ? (
              <div className="space-y-3">
                {availableWallets.map((wallet) => (
                  <button
                    key={wallet.id}
                    onClick={() => handleConnectWallet(wallet.id as 'metamask' | 'trustwallet')}
                    disabled={isConnecting}
                    className="w-full flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-xl hover:border-blue-300 dark:hover:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group"
                  >
                    <div className="flex items-center space-x-3 rtl:space-x-reverse">
                      <div className="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center text-xl group-hover:scale-110 transition-transform duration-200">
                        {wallet.icon}
                      </div>
                      <div className="text-left rtl:text-right">
                        <p className="font-semibold text-gray-900 dark:text-white">
                          {wallet.name}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {wallet.description}
                        </p>
                      </div>
                    </div>
                    {isConnecting ? (
                      <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-gray-400 ltr:rotate-[-90deg] rtl:rotate-90 group-hover:text-blue-500 transition-colors duration-200" />
                    )}
                  </button>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <AlertCircle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {t('wallet.noWalletsFound')}
                </p>
                <a
                  href="https://metamask.io/download/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                >
                  <ExternalLink className="w-4 h-4 ltr:mr-2 rtl:ml-2" />
                  {t('wallet.downloadMetaMask')}
                </a>
              </div>
            )}

            {error && (
              <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-center">
                  <AlertCircle className="w-4 h-4 text-red-500 ltr:mr-2 rtl:ml-2" />
                  <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
                </div>
              </div>
            )}

            <button
              onClick={() => setShowWalletOptions(false)}
              className="w-full mt-4 px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 transition-colors duration-200"
            >
              {t('common.cancel')}
            </button>
          </div>
        </div>
      </>
    );
  };

  // الحصول على أيقونة الحالة
  const getStatusIcon = () => {
    if (isLoading || isConnecting || isRefreshing) {
      return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
    }
    if (isWalletConnected) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
    if (error) {
      return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
    return <WifiOff className="w-4 h-4 text-gray-400" />;
  };

  // الحصول على نص الحالة
  const getStatusText = () => {
    if (isConnecting) return t('wallet.connecting');
    if (isRefreshing) return t('wallet.refreshing');
    if (isWalletConnected) return t('wallet.connected.title');
    if (error) return t('wallet.error');
    return t('wallet.notConnected.title');
  };

  // الحصول على لون الحالة
  const getStatusColor = () => {
    if (isWalletConnected) return 'text-green-600 dark:text-green-400';
    if (error) return 'text-red-600 dark:text-red-400';
    if (isConnecting || isRefreshing) return 'text-blue-600 dark:text-blue-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  // مكون Header Variant - للاستخدام في الهيدر
  const HeaderVariant = () => {
    if (!isWalletConnected) {
      return (
        <button
          onClick={() => setShowWalletOptions(true)}
          disabled={isConnecting}
          className="flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg hover:from-blue-100 hover:to-purple-100 dark:hover:from-blue-900/30 dark:hover:to-purple-900/30 transition-all duration-200 disabled:opacity-50"
        >
          {getStatusIcon()}
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {t('wallet.connect.button')}
          </span>
        </button>
      );
    }

    return (
      <div className="relative">
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="flex items-center space-x-2 rtl:space-x-reverse px-3 py-2 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border border-green-200 dark:border-green-800 rounded-lg hover:from-green-100 hover:to-blue-100 dark:hover:from-green-900/30 dark:hover:to-blue-900/30 transition-all duration-200"
        >
          {getStatusIcon()}
          <div className="text-left rtl:text-right">
            <p className="text-xs font-medium text-green-600 dark:text-green-400">
              {getStatusText()}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 font-mono">
              {formatAddress(walletAddress!, 4)}
            </p>
          </div>
          <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${showDetails ? 'rotate-180' : ''}`} />
        </button>

        {/* قائمة منسدلة للتفاصيل محسنة */}
        {showDetails && (
          <>
            <div className="fixed inset-0 z-40" onClick={() => setShowDetails(false)} />
            <div className="absolute ltr:right-0 rtl:left-0 mt-3 w-96 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden z-50 animate-in slide-in-from-top-2 duration-200">
              {/* رأس القائمة */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 px-6 py-4 border-b border-gray-200 dark:border-gray-600">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <Wallet className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-bold text-gray-900 dark:text-white">
                        {t('wallet.details')}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {t('wallet.connected.title')}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={refreshWalletInfo}
                    disabled={isRefreshing}
                    className="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-200 disabled:opacity-50"
                    title={t('wallet.refresh')}
                  >
                    <RefreshCw className={`w-5 h-5 ${isRefreshing ? 'animate-spin' : ''}`} />
                  </button>
                </div>
              </div>

              {/* معلومات المحفظة */}
              <div className="px-6 py-4 space-y-4">
                {/* العنوان */}
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {t('wallet.address')}
                    </span>
                    <button
                      onClick={copyAddress}
                      className="flex items-center space-x-1 rtl:space-x-reverse px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors duration-200"
                    >
                      <Copy className="w-3 h-3" />
                      <span>{t('common.copy')}</span>
                    </button>
                  </div>
                  <p className="text-sm font-mono text-gray-900 dark:text-white bg-white dark:bg-gray-800 px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-600 break-all">
                    {walletAddress}
                  </p>
                </div>

                {/* الشبكة والرصيد */}
                <div className="grid grid-cols-2 gap-3">
                  {showNetwork && walletNetwork && (
                    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-3">
                      <span className="text-xs font-medium text-gray-500 dark:text-gray-400 block mb-1">
                        {t('wallet.network')}
                      </span>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-semibold text-gray-900 dark:text-white">
                          {walletNetwork}
                        </span>
                      </div>
                    </div>
                  )}

                  {showBalance && (
                    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-3">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                          {t('wallet.balance')}
                        </span>
                        <button
                          onClick={() => setShowBalanceDetails(!showBalanceDetails)}
                          className="p-1 text-gray-400 hover:text-blue-500 transition-colors duration-200"
                        >
                          {showBalanceDetails ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                        </button>
                      </div>
                      <span className="text-sm font-bold text-gray-900 dark:text-white">
                        {showBalanceDetails ? formatBalance(walletBalance) : '••••'} BNB
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* الإجراءات */}
              {showActions && (
                <div className="px-4 py-3 bg-gray-50 dark:bg-gray-700/30 border-t border-gray-200 dark:border-gray-600">
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <button
                      onClick={openInExplorer}
                      className="flex-1 flex items-center justify-center space-x-2 rtl:space-x-reverse px-4 py-2.5 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors duration-200 font-medium"
                    >
                      <ExternalLink className="w-4 h-4" />
                      <span>{t('wallet.explorer')}</span>
                    </button>

                    <button
                      onClick={handleDisconnect}
                      className="flex-1 flex items-center justify-center space-x-2 rtl:space-x-reverse px-4 py-2.5 bg-red-600 text-white rounded-xl hover:bg-red-700 transition-colors duration-200 font-medium"
                    >
                      <LogOut className="w-4 h-4" />
                      <span>{t('wallet.disconnect')}</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    );
  };

  // مكون Card Variant - للاستخدام في البطاقات
  const CardVariant = () => {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <Wallet className="w-5 h-5 ltr:mr-2 rtl:ml-2" />
            {t('wallet.status')}
          </h3>
          {isWalletConnected && (
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-green-600 dark:text-green-400">
                {getStatusText()}
              </span>
            </div>
          )}
        </div>

        {isWalletConnected ? (
          <div className="space-y-4">
            {/* معلومات المحفظة */}
            <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('wallet.address')}
                  </span>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <span className="text-sm font-mono text-gray-900 dark:text-white">
                      {formatAddress(walletAddress!)}
                    </span>
                    <button
                      onClick={copyAddress}
                      className="p-1 text-gray-400 hover:text-blue-500 transition-colors duration-200"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {showNetwork && walletNetwork && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {t('wallet.network')}
                    </span>
                    <span className="text-sm px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full">
                      {walletNetwork}
                    </span>
                  </div>
                )}

                {showBalance && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {t('wallet.balance')}
                    </span>
                    <span className="text-sm font-semibold text-gray-900 dark:text-white">
                      {formatBalance(walletBalance)} BNB
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* الإجراءات */}
            {showActions && (
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={refreshWalletInfo}
                  disabled={isRefreshing}
                  className="flex items-center px-3 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200 disabled:opacity-50"
                >
                  <RefreshCw className={`w-4 h-4 ltr:mr-2 rtl:ml-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                  {t('wallet.refresh')}
                </button>

                <button
                  onClick={openInExplorer}
                  className="flex items-center px-3 py-2 bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200"
                >
                  <ExternalLink className="w-4 h-4 ltr:mr-2 rtl:ml-2" />
                  {t('wallet.viewInExplorer')}
                </button>

                <button
                  onClick={handleDisconnect}
                  className="flex items-center px-3 py-2 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors duration-200"
                >
                  <LogOut className="w-4 h-4 ltr:mr-2 rtl:ml-2" />
                  {t('wallet.disconnect')}
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-6">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Wallet className="w-8 h-8 text-white" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              {t('wallet.notConnected.title')}
            </h4>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              {t('wallet.notConnected.description')}
            </p>

            {error && (
              <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-center justify-center">
                  <AlertCircle className="w-4 h-4 text-red-500 ltr:mr-2 rtl:ml-2" />
                  <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
                </div>
              </div>
            )}

            {showConnectionOptions && (
              <button
                onClick={() => setShowWalletOptions(true)}
                disabled={isConnecting}
                className="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {isConnecting ? (
                  <>
                    <Loader2 className="w-5 h-5 ltr:mr-2 rtl:ml-2 animate-spin" />
                    {t('wallet.connecting')}
                  </>
                ) : (
                  <>
                    <Wallet className="w-5 h-5 ltr:mr-2 rtl:ml-2" />
                    {t('wallet.connect.button')}
                  </>
                )}
              </button>
            )}
          </div>
        )}
      </div>
    );
  };

  // مكون Inline Variant - للاستخدام المضمن
  const InlineVariant = () => {
    return (
      <div className={`flex items-center space-x-3 rtl:space-x-reverse ${className}`}>
        {isWalletConnected ? (
          <>
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              {getStatusIcon()}
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {getStatusText()}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                  {formatAddress(walletAddress!)}
                </p>
              </div>
            </div>

            {showNetwork && walletNetwork && (
              <span className="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded">
                {walletNetwork}
              </span>
            )}

            {showBalance && (
              <span className="text-sm font-semibold text-gray-900 dark:text-white">
                {formatBalance(walletBalance)} BNB
              </span>
            )}

            {showActions && (
              <div className="flex items-center space-x-1 rtl:space-x-reverse">
                <button
                  onClick={copyAddress}
                  className="p-1 text-gray-400 hover:text-blue-500 transition-colors duration-200"
                  title={t('wallet.copyAddress')}
                >
                  <Copy className="w-4 h-4" />
                </button>
                <button
                  onClick={openInExplorer}
                  className="p-1 text-gray-400 hover:text-blue-500 transition-colors duration-200"
                  title={t('wallet.viewInExplorer')}
                >
                  <ExternalLink className="w-4 h-4" />
                </button>
                <button
                  onClick={handleDisconnect}
                  className="p-1 text-gray-400 hover:text-red-500 transition-colors duration-200"
                  title={t('wallet.disconnect')}
                >
                  <LogOut className="w-4 h-4" />
                </button>
              </div>
            )}
          </>
        ) : (
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            {getStatusIcon()}
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {getStatusText()}
            </span>
            {showConnectionOptions && (
              <button
                onClick={() => setShowWalletOptions(true)}
                disabled={isConnecting}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50"
              >
                {t('wallet.connect.button')}
              </button>
            )}
          </div>
        )}
      </div>
    );
  };

  // مكون Compact Variant - للاستخدام المضغوط
  const CompactVariant = () => {
    return (
      <div className={`inline-flex items-center space-x-2 rtl:space-x-reverse ${className}`}>
        {getStatusIcon()}
        {isWalletConnected ? (
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-sm font-mono text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
          >
            {formatAddress(walletAddress!, 4)}
          </button>
        ) : (
          <button
            onClick={() => setShowWalletOptions(true)}
            disabled={isConnecting}
            className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 disabled:opacity-50"
          >
            {t('wallet.connect.button')}
          </button>
        )}
      </div>
    );
  };

  // مكون Mobile Variant - للاستخدام في الجوال
  const MobileVariant = () => {
    return (
      <div className={`w-full ${className}`}>
        {isWalletConnected ? (
          <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-xl p-4 border border-green-200 dark:border-green-800">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                {getStatusIcon()}
                <span className="text-sm font-medium text-green-600 dark:text-green-400">
                  {getStatusText()}
                </span>
              </div>
              {showActions && (
                <button
                  onClick={() => setShowDetails(!showDetails)}
                  className="p-1 text-gray-400 hover:text-blue-500 transition-colors duration-200"
                >
                  <Settings className="w-4 h-4" />
                </button>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {t('wallet.address')}
                </span>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <span className="text-xs font-mono text-gray-900 dark:text-white">
                    {formatAddress(walletAddress!)}
                  </span>
                  <button
                    onClick={copyAddress}
                    className="p-1 text-gray-400 hover:text-blue-500 transition-colors duration-200"
                  >
                    <Copy className="w-3 h-3" />
                  </button>
                </div>
              </div>

              {showNetwork && walletNetwork && (
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {t('wallet.network')}
                  </span>
                  <span className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded">
                    {walletNetwork}
                  </span>
                </div>
              )}

              {showBalance && (
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {t('wallet.balance')}
                  </span>
                  <span className="text-xs font-semibold text-gray-900 dark:text-white">
                    {formatBalance(walletBalance)} BNB
                  </span>
                </div>
              )}
            </div>

            {showDetails && showActions && (
              <div className="mt-3 pt-3 border-t border-green-200 dark:border-green-700 flex flex-wrap gap-2">
                <button
                  onClick={refreshWalletInfo}
                  disabled={isRefreshing}
                  className="flex items-center px-2 py-1 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded text-xs transition-colors duration-200 disabled:opacity-50"
                >
                  <RefreshCw className={`w-3 h-3 ltr:mr-1 rtl:ml-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                  {t('wallet.refresh')}
                </button>

                <button
                  onClick={openInExplorer}
                  className="flex items-center px-2 py-1 bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded text-xs transition-colors duration-200"
                >
                  <ExternalLink className="w-3 h-3 ltr:mr-1 rtl:ml-1" />
                  {t('wallet.explorer')}
                </button>

                <button
                  onClick={handleDisconnect}
                  className="flex items-center px-2 py-1 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded text-xs transition-colors duration-200"
                >
                  <LogOut className="w-3 h-3 ltr:mr-1 rtl:ml-1" />
                  {t('wallet.disconnect')}
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                {getStatusIcon()}
                <span className="text-sm font-medium text-yellow-800 dark:text-yellow-300">
                  {getStatusText()}
                </span>
              </div>
            </div>

            <p className="text-sm text-yellow-700 dark:text-yellow-400 mb-3">
              {t('wallet.notConnected.description')}
            </p>

            {error && (
              <div className="mb-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
                <p className="text-xs text-red-700 dark:text-red-300">{error}</p>
              </div>
            )}

            {showConnectionOptions && (
              <button
                onClick={() => setShowWalletOptions(true)}
                disabled={isConnecting}
                className="w-full px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors duration-200 disabled:opacity-50 flex items-center justify-center"
              >
                {isConnecting ? (
                  <>
                    <Loader2 className="w-4 h-4 ltr:mr-2 rtl:ml-2 animate-spin" />
                    {t('wallet.connecting')}
                  </>
                ) : (
                  <>
                    <Wallet className="w-4 h-4 ltr:mr-2 rtl:ml-2" />
                    {t('wallet.connect.button')}
                  </>
                )}
              </button>
            )}
          </div>
        )}
      </div>
    );
  };

  // العرض الرئيسي
  const renderVariant = () => {
    switch (variant) {
      case 'header':
        return <HeaderVariant />;
      case 'card':
        return <CardVariant />;
      case 'inline':
        return <InlineVariant />;
      case 'compact':
        return <CompactVariant />;
      case 'mobile':
        return <MobileVariant />;
      case 'full':
        return <CardVariant />; // Full يستخدم نفس Card مع إعدادات مختلفة
      default:
        return <InlineVariant />;
    }
  };

  return (
    <>
      {renderVariant()}
      <WalletSelectionModal />
    </>
  );
}
