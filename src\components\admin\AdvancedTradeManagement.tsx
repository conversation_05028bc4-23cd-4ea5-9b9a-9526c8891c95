'use client';

import React, { useState } from 'react';
import {
  TrendingUp,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  MessageSquare,
  FileText,
  Calendar,
  DollarSign,
  Users,
  Activity,
  Settings,
  RefreshCw,
  Upload,
  X,
  Check,
  ArrowUp,
  ArrowDown,
  Flag,
  Shield,
  Scale,
  Phone,
  Mail,
  Paperclip,
  Camera,
  Video,
  Star,
  ThumbsUp,
  ThumbsDown,
  Pause,
  Play,
  RotateCcw,
  Zap,
  Target,
  BarChart3,
  PieChart,
  LineChart
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';

interface Trade {
  id: string;
  tradeId: string;
  type: 'buy' | 'sell' | 'exchange' | 'p2p' | 'escrow' | 'instant' | 'scheduled';
  status: 'created' | 'joined' | 'paymentSent' | 'paymentConfirmed' | 'completed' | 'cancelled' | 'disputed' | 'resolved' | 'pending' | 'processing' | 'expired' | 'frozen' | 'escalated' | 'underReview';
  amount: number;
  currency: string;
  rate: number;
  fee: number;
  total: number;
  buyer: {
    id: string;
    name: string;
    email: string;
    rating: number;
    verified: boolean;
  };
  seller: {
    id: string;
    name: string;
    email: string;
    rating: number;
    verified: boolean;
  };
  paymentMethod: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  duration: number; // in minutes
  location?: string;
  terms?: string;
  notes?: string;
  riskScore: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  assignedTo?: string;
  tags: string[];
  messagesCount: number;
  documentsCount: number;
}

interface AdvancedTradeManagementProps {
  className?: string;
}

export default function AdvancedTradeManagement({ className = '' }: AdvancedTradeManagementProps) {
  const { t, language, isRTL, getDirectionClasses, formatNumber, formatCurrency, formatDate, formatRelativeTime } = useAdminTranslation();
  const dirClasses = getDirectionClasses();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedTrades, setSelectedTrades] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [selectedTrade, setSelectedTrade] = useState<Trade | null>(null);
  const [showTradeDetails, setShowTradeDetails] = useState(false);

  // Mock data - replace with real data
  const trades: Trade[] = [
    {
      id: '1',
      tradeId: 'TRD-2024-001',
      type: 'buy',
      status: 'paymentSent',
      amount: 1000,
      currency: 'USDT',
      rate: 3.75,
      fee: 15,
      total: 3765,
      buyer: {
        id: 'user1',
        name: 'أحمد محمد علي',
        email: '<EMAIL>',
        rating: 4.8,
        verified: true
      },
      seller: {
        id: 'user2',
        name: 'فاطمة أحمد',
        email: '<EMAIL>',
        rating: 4.5,
        verified: true
      },
      paymentMethod: 'Bank Transfer',
      createdAt: '2024-01-20T10:00:00Z',
      updatedAt: '2024-01-20T15:30:00Z',
      duration: 30,
      location: 'Saudi Arabia',
      riskScore: 15,
      priority: 'medium',
      assignedTo: 'admin1',
      tags: ['verified', 'bank-transfer'],
      messagesCount: 5,
      documentsCount: 2
    },
    {
      id: '2',
      tradeId: 'TRD-2024-002',
      type: 'sell',
      status: 'disputed',
      amount: 500,
      currency: 'BTC',
      rate: 45000,
      fee: 50,
      total: 22450,
      buyer: {
        id: 'user3',
        name: 'محمد عبدالله',
        email: '<EMAIL>',
        rating: 4.9,
        verified: true
      },
      seller: {
        id: 'user4',
        name: 'سارة أحمد',
        email: '<EMAIL>',
        rating: 4.2,
        verified: false
      },
      paymentMethod: 'Cash',
      createdAt: '2024-01-19T14:00:00Z',
      updatedAt: '2024-01-20T09:15:00Z',
      duration: 60,
      location: 'UAE',
      riskScore: 65,
      priority: 'high',
      tags: ['cash', 'disputed', 'high-risk'],
      messagesCount: 12,
      documentsCount: 8
    },
    {
      id: '3',
      tradeId: 'TRD-2024-003',
      type: 'exchange',
      status: 'completed',
      amount: 2000,
      currency: 'ETH',
      rate: 2500,
      fee: 25,
      total: 5025,
      buyer: {
        id: 'user5',
        name: 'خالد يوسف',
        email: '<EMAIL>',
        rating: 4.6,
        verified: true
      },
      seller: {
        id: 'user6',
        name: 'نور الدين',
        email: '<EMAIL>',
        rating: 4.8,
        verified: true
      },
      paymentMethod: 'Digital Wallet',
      createdAt: '2024-01-18T11:30:00Z',
      updatedAt: '2024-01-19T16:45:00Z',
      completedAt: '2024-01-19T16:45:00Z',
      duration: 45,
      location: 'Egypt',
      riskScore: 10,
      priority: 'low',
      tags: ['verified', 'digital-wallet', 'completed'],
      messagesCount: 3,
      documentsCount: 1
    }
  ];

  const filteredTrades = trades.filter(trade => {
    const matchesSearch = trade.tradeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         trade.buyer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         trade.seller.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         trade.currency.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = selectedFilter === 'all' ||
                         (selectedFilter === 'active' && ['created', 'joined', 'paymentSent', 'paymentConfirmed', 'processing'].includes(trade.status)) ||
                         (selectedFilter === 'completed' && trade.status === 'completed') ||
                         (selectedFilter === 'disputed' && trade.status === 'disputed') ||
                         (selectedFilter === 'cancelled' && trade.status === 'cancelled') ||
                         (selectedFilter === 'highValue' && trade.total > 10000) ||
                         (selectedFilter === 'highRisk' && trade.riskScore > 50) ||
                         (selectedFilter === 'todayOnly' && new Date(trade.createdAt).toDateString() === new Date().toDateString());
    
    return matchesSearch && matchesFilter;
  });

  const stats = {
    total: trades.length,
    active: trades.filter(t => ['created', 'joined', 'paymentSent', 'paymentConfirmed', 'processing'].includes(t.status)).length,
    completed: trades.filter(t => t.status === 'completed').length,
    disputed: trades.filter(t => t.status === 'disputed').length,
    cancelled: trades.filter(t => t.status === 'cancelled').length,
    pending: trades.filter(t => t.status === 'pending').length,
    highRisk: trades.filter(t => t.riskScore > 50).length,
    todayTrades: trades.filter(t => new Date(t.createdAt).toDateString() === new Date().toDateString()).length,
    totalVolume: trades.reduce((sum, t) => sum + t.total, 0),
    averageValue: trades.length > 0 ? trades.reduce((sum, t) => sum + t.total, 0) / trades.length : 0,
    successRate: trades.length > 0 ? (trades.filter(t => t.status === 'completed').length / trades.length) * 100 : 0
  };

  const handleTradeAction = (tradeId: string, action: string) => {
    console.log(`Action ${action} for trade ${tradeId}`);
    // Implement trade actions
  };

  const handleBulkAction = (action: string) => {
    console.log(`Bulk action ${action} for trades:`, selectedTrades);
    setShowBulkActions(false);
    setSelectedTrades([]);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green';
      case 'disputed': return 'red';
      case 'cancelled': return 'gray';
      case 'paymentSent': return 'blue';
      case 'paymentConfirmed': return 'purple';
      case 'processing': return 'yellow';
      case 'frozen': return 'indigo';
      case 'escalated': return 'red';
      default: return 'gray';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'green';
      case 'medium': return 'yellow';
      case 'high': return 'orange';
      case 'critical': return 'red';
      default: return 'gray';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'buy': return ArrowDown;
      case 'sell': return ArrowUp;
      case 'exchange': return RotateCcw;
      case 'p2p': return Users;
      case 'escrow': return Shield;
      case 'instant': return Zap;
      case 'scheduled': return Calendar;
      default: return TrendingUp;
    }
  };

  const StatusBadge = ({ status }: { status: string }) => {
    const color = getStatusColor(status);
    const StatusIcon = status === 'completed' ? CheckCircle : 
                      status === 'disputed' ? AlertTriangle :
                      status === 'cancelled' ? XCircle :
                      status === 'frozen' ? Pause : Clock;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${color}-100 text-${color}-800 dark:bg-${color}-900/30 dark:text-${color}-400`}>
        <StatusIcon className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
        {t(`trades.status.${status}`)}
      </span>
    );
  };

  const PriorityBadge = ({ priority }: { priority: string }) => {
    const color = getPriorityColor(priority);
    const PriorityIcon = priority === 'critical' ? Flag : 
                        priority === 'high' ? ArrowUp :
                        priority === 'medium' ? ArrowDown : Flag;
    
    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-${color}-100 text-${color}-800 dark:bg-${color}-900/30 dark:text-${color}-400`}>
        <PriorityIcon className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
        {priority.toUpperCase()}
      </span>
    );
  };

  const TypeBadge = ({ type }: { type: string }) => {
    const TypeIcon = getTypeIcon(type);
    
    return (
      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
        <TypeIcon className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
        {t(`trades.types.${type}`)}
      </span>
    );
  };

  const RiskScoreBadge = ({ score }: { score: number }) => {
    let color = 'green';
    let icon = CheckCircle;
    
    if (score > 70) {
      color = 'red';
      icon = AlertTriangle;
    } else if (score > 40) {
      color = 'yellow';
      icon = AlertTriangle;
    }
    
    const Icon = icon;
    
    return (
      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-${color}-100 text-${color}-800 dark:bg-${color}-900/30 dark:text-${color}-400`}>
        <Icon className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
        {score}%
      </span>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className={`flex flex-col sm:flex-row sm:items-center justify-between gap-4 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
        <div className={isRTL ? 'text-right' : 'text-left'}>
          <h3 className={`text-xl font-bold text-gray-900 dark:text-white flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <TrendingUp className={`w-6 h-6 text-green-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            {t('trades.title')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{t('trades.subtitle')}</p>
        </div>
        <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button 
            onClick={() => handleBulkAction('export')}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            <Download className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.export')}
          </button>
          <button className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors">
            <BarChart3 className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('trades.analytics.title')}
          </button>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('trades.totalTrades')}</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatNumber(stats.total)}</p>
            </div>
            <TrendingUp className="w-8 h-8 text-gray-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('trades.activeTrades')}</p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{formatNumber(stats.active)}</p>
            </div>
            <Activity className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('trades.completedTrades')}</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">{formatNumber(stats.completed)}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('trades.disputedTrades')}</p>
              <p className="text-2xl font-bold text-red-600 dark:text-red-400">{formatNumber(stats.disputed)}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-red-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('trades.totalVolume')}</p>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">{formatCurrency(stats.totalVolume)}</p>
            </div>
            <DollarSign className="w-8 h-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={isRTL ? 'text-right' : 'text-left'}>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('trades.successRate')}</p>
              <p className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">{stats.successRate.toFixed(1)}%</p>
            </div>
            <Target className="w-8 h-8 text-indigo-500" />
          </div>
        </div>
      </div>

      {/* Advanced Search and Filters */}
      <div className={`flex flex-col lg:flex-row gap-4 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
        <div className="relative flex-1">
          <Search className={`absolute top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 ${isRTL ? 'right-3' : 'left-3'}`} />
          <input
            type="text"
            placeholder={t('common.actions.search')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full ${isRTL ? 'pr-10 pl-4 text-right' : 'pl-10 pr-4 text-left'} py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent`}
          />
        </div>

        <div className="flex items-center gap-3">
          <select
            value={selectedFilter}
            onChange={(e) => setSelectedFilter(e.target.value)}
            className={`px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
          >
            <option value="all">{t('trades.filters.all')}</option>
            <option value="active">{t('trades.filters.active')}</option>
            <option value="completed">{t('trades.filters.completed')}</option>
            <option value="disputed">{t('trades.filters.disputed')}</option>
            <option value="cancelled">{t('trades.filters.cancelled')}</option>
            <option value="highValue">{t('trades.filters.highValue')}</option>
            <option value="highRisk">{t('trades.filters.highRisk')}</option>
            <option value="todayOnly">{t('trades.filters.todayOnly')}</option>
          </select>

          <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
            <Filter className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.filter')}
          </button>

          <button className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
            <RefreshCw className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('common.actions.refresh')}
          </button>
        </div>
      </div>

      {/* Advanced Trades Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  <input
                    type="checkbox"
                    className="rounded"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedTrades(filteredTrades.map(t => t.id));
                        setShowBulkActions(true);
                      } else {
                        setSelectedTrades([]);
                        setShowBulkActions(false);
                      }
                    }}
                  />
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('trades.details.tradeId')} & {t('common.labels.type')}
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('trades.participants.buyer')} / {t('trades.participants.seller')}
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('trades.details.amount')} & {t('trades.details.total')}
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('common.labels.status')} & Risk
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('common.labels.created')} / {t('common.labels.updated')}
                </th>
                <th className={`py-3 px-4 font-medium text-gray-900 dark:text-white ${dirClasses.textAlign}`}>
                  {t('trades.actions.view')}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredTrades.map((trade) => (
                <tr key={trade.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <td className="py-4 px-4">
                    <input
                      type="checkbox"
                      className="rounded"
                      checked={selectedTrades.includes(trade.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedTrades([...selectedTrades, trade.id]);
                          setShowBulkActions(true);
                        } else {
                          setSelectedTrades(selectedTrades.filter(id => id !== trade.id));
                          if (selectedTrades.length === 1) setShowBulkActions(false);
                        }
                      }}
                    />
                  </td>
                  <td className="py-4 px-4">
                    <div className={isRTL ? 'text-right' : 'text-left'}>
                      <div className="font-medium text-gray-900 dark:text-white mb-1">
                        {trade.tradeId}
                      </div>
                      <div className="flex items-center gap-2 mb-2">
                        <TypeBadge type={trade.type} />
                        <PriorityBadge priority={trade.priority} />
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        {trade.paymentMethod}
                      </div>
                      {trade.tags.length > 0 && (
                        <div className="flex items-center gap-1 mt-1">
                          {trade.tags.slice(0, 2).map((tag, index) => (
                            <span key={index} className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
                              {tag}
                            </span>
                          ))}
                          {trade.tags.length > 2 && (
                            <span className="text-xs text-gray-500">+{trade.tags.length - 2}</span>
                          )}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className={`space-y-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className={`w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center ${isRTL ? 'ml-2' : 'mr-2'}`}>
                          <span className="text-green-700 dark:text-green-400 font-semibold text-xs">
                            {trade.buyer.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <div className={`text-sm font-medium text-gray-900 dark:text-white flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            {trade.buyer.name}
                            {trade.buyer.verified && <Shield className={`w-3 h-3 text-green-500 ${isRTL ? 'mr-1' : 'ml-1'}`} />}
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-300">
                            ⭐ {trade.buyer.rating}/5 • {t('trades.participants.buyer')}
                          </div>
                        </div>
                      </div>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className={`w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center ${isRTL ? 'ml-2' : 'mr-2'}`}>
                          <span className="text-blue-700 dark:text-blue-400 font-semibold text-xs">
                            {trade.seller.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <div className={`text-sm font-medium text-gray-900 dark:text-white flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            {trade.seller.name}
                            {trade.seller.verified && <Shield className={`w-3 h-3 text-green-500 ${isRTL ? 'mr-1' : 'ml-1'}`} />}
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-300">
                            ⭐ {trade.seller.rating}/5 • {t('trades.participants.seller')}
                          </div>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className={isRTL ? 'text-right' : 'text-left'}>
                      <div className="text-lg font-bold text-gray-900 dark:text-white">
                        {formatNumber(trade.amount)} {trade.currency}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        @ {formatCurrency(trade.rate)}
                      </div>
                      <div className="text-sm font-medium text-purple-600 dark:text-purple-400">
                        {t('trades.details.total')}: {formatCurrency(trade.total)}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {t('trades.details.fee')}: {formatCurrency(trade.fee)}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className="space-y-2">
                      <StatusBadge status={trade.status} />
                      <RiskScoreBadge score={trade.riskScore} />
                      {trade.assignedTo && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400">
                          <Users className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                          Assigned
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="py-4 px-4 text-sm text-gray-600 dark:text-gray-300">
                    <div className={isRTL ? 'text-right' : 'text-left'}>
                      <div>{formatDate(trade.createdAt)}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {formatRelativeTime(trade.updatedAt)}
                      </div>
                      {trade.completedAt && (
                        <div className="text-xs text-green-600 dark:text-green-400">
                          ✓ {formatDate(trade.completedAt)}
                        </div>
                      )}
                      <div className="text-xs text-blue-600 dark:text-blue-400">
                        {trade.duration}min • {trade.location}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <button
                        onClick={() => {
                          setSelectedTrade(trade);
                          setShowTradeDetails(true);
                        }}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 p-1 rounded transition-colors"
                        title={t('trades.actions.view')}
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleTradeAction(trade.id, 'monitor')}
                        className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 p-1 rounded transition-colors"
                        title={t('trades.actions.monitor')}
                      >
                        <Activity className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleTradeAction(trade.id, 'message')}
                        className="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 p-1 rounded transition-colors"
                        title={t('trades.actions.sendMessage')}
                      >
                        <MessageSquare className="w-4 h-4" />
                      </button>
                      {trade.status === 'disputed' && (
                        <button
                          onClick={() => handleTradeAction(trade.id, 'resolve')}
                          className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 p-1 rounded transition-colors"
                          title={t('trades.actions.resolve')}
                        >
                          <Scale className="w-4 h-4" />
                        </button>
                      )}
                      {trade.status === 'processing' && (
                        <button
                          onClick={() => handleTradeAction(trade.id, 'pause')}
                          className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-700 dark:hover:text-yellow-300 p-1 rounded transition-colors"
                          title={t('trades.actions.pause')}
                        >
                          <Pause className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Empty State */}
        {filteredTrades.length === 0 && (
          <div className="text-center py-12">
            <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{t('common.messages.noData')}</h3>
            <p className="text-gray-600 dark:text-gray-400">{t('common.actions.search')}</p>
          </div>
        )}
      </div>

      {/* Bulk Actions Panel */}
      {showBulkActions && selectedTrades.length > 0 && (
        <div className={`fixed bottom-6 ${isRTL ? 'left-6' : 'right-6'} bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 z-50`}>
          <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {selectedTrades.length} {t('common.actions.selected')}
            </span>
            <button
              onClick={() => handleBulkAction('monitor')}
              className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors"
            >
              {t('trades.actions.monitor')}
            </button>
            <button
              onClick={() => handleBulkAction('freeze')}
              className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors"
            >
              {t('trades.actions.freeze')}
            </button>
            <button
              onClick={() => handleBulkAction('export')}
              className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm transition-colors"
            >
              {t('trades.actions.exportData')}
            </button>
            <button
              onClick={() => {
                setSelectedTrades([]);
                setShowBulkActions(false);
              }}
              className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition-colors"
            >
              {t('common.actions.cancel')}
            </button>
          </div>
        </div>
      )}

      {/* Trade Details Modal */}
      {showTradeDetails && selectedTrade && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className={`flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t('trades.actions.view')}: {selectedTrade.tradeId}
              </h3>
              <button
                onClick={() => setShowTradeDetails(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Trade Information */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                    {t('trades.title')} {t('common.labels.information')}
                  </h4>
                  <div className="space-y-3 text-sm">
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.type')}:</span>
                      <TypeBadge type={selectedTrade.type} />
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.status')}:</span>
                      <StatusBadge status={selectedTrade.status} />
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">Priority:</span>
                      <PriorityBadge priority={selectedTrade.priority} />
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('trades.details.amount')}:</span>
                      <span className="text-gray-900 dark:text-white font-semibold">
                        {formatNumber(selectedTrade.amount)} {selectedTrade.currency}
                      </span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('trades.details.rate')}:</span>
                      <span className="text-gray-900 dark:text-white">{formatCurrency(selectedTrade.rate)}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('trades.details.total')}:</span>
                      <span className="text-gray-900 dark:text-white font-semibold">{formatCurrency(selectedTrade.total)}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('trades.details.fee')}:</span>
                      <span className="text-gray-900 dark:text-white">{formatCurrency(selectedTrade.fee)}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('trades.details.method')}:</span>
                      <span className="text-gray-900 dark:text-white">{selectedTrade.paymentMethod}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">Risk Score:</span>
                      <RiskScoreBadge score={selectedTrade.riskScore} />
                    </div>
                  </div>
                </div>

                {/* Participants Information */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                    {t('trades.participants.buyer')} & {t('trades.participants.seller')}
                  </h4>
                  <div className="space-y-4">
                    <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div className="text-sm font-medium text-green-800 dark:text-green-300 mb-2">
                        {t('trades.participants.buyer')}
                      </div>
                      <div className="text-sm text-gray-700 dark:text-gray-300">
                        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                          {selectedTrade.buyer.name}
                          {selectedTrade.buyer.verified && <Shield className={`w-4 h-4 text-green-500 ${isRTL ? 'mr-2' : 'ml-2'}`} />}
                        </div>
                        <div>{selectedTrade.buyer.email}</div>
                        <div>⭐ {selectedTrade.buyer.rating}/5</div>
                      </div>
                    </div>
                    <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">
                        {t('trades.participants.seller')}
                      </div>
                      <div className="text-sm text-gray-700 dark:text-gray-300">
                        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                          {selectedTrade.seller.name}
                          {selectedTrade.seller.verified && <Shield className={`w-4 h-4 text-green-500 ${isRTL ? 'mr-2' : 'ml-2'}`} />}
                        </div>
                        <div>{selectedTrade.seller.email}</div>
                        <div>⭐ {selectedTrade.seller.rating}/5</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Timeline and Activity */}
              <div className="mt-6">
                <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                  {t('trades.timeline.created')} & Activity
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2 text-sm">
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.created')}:</span>
                      <span className="text-gray-900 dark:text-white">{formatDate(selectedTrade.createdAt)}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('common.labels.updated')}:</span>
                      <span className="text-gray-900 dark:text-white">{formatDate(selectedTrade.updatedAt)}</span>
                    </div>
                    {selectedTrade.completedAt && (
                      <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className="text-gray-600 dark:text-gray-400">{t('trades.timeline.completed')}:</span>
                        <span className="text-gray-900 dark:text-white">{formatDate(selectedTrade.completedAt)}</span>
                      </div>
                    )}
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">{t('trades.details.duration')}:</span>
                      <span className="text-gray-900 dark:text-white">{selectedTrade.duration} {t('common.labels.time')}</span>
                    </div>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">Messages:</span>
                      <span className="text-gray-900 dark:text-white">{selectedTrade.messagesCount}</span>
                    </div>
                    <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 dark:text-gray-400">Documents:</span>
                      <span className="text-gray-900 dark:text-white">{selectedTrade.documentsCount}</span>
                    </div>
                    {selectedTrade.location && (
                      <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className="text-gray-600 dark:text-gray-400">{t('trades.details.location')}:</span>
                        <span className="text-gray-900 dark:text-white">{selectedTrade.location}</span>
                      </div>
                    )}
                    {selectedTrade.assignedTo && (
                      <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className="text-gray-600 dark:text-gray-400">Assigned To:</span>
                        <span className="text-gray-900 dark:text-white">{selectedTrade.assignedTo}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Tags and Notes */}
              {(selectedTrade.tags.length > 0 || selectedTrade.notes) && (
                <div className="mt-6">
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                    Tags & {t('trades.details.notes')}
                  </h4>
                  {selectedTrade.tags.length > 0 && (
                    <div className="mb-3">
                      <div className="flex flex-wrap gap-2">
                        {selectedTrade.tags.map((tag, index) => (
                          <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                  {selectedTrade.notes && (
                    <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg text-sm text-gray-700 dark:text-gray-300">
                      {selectedTrade.notes}
                    </div>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className={`flex items-center gap-3 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <button
                  onClick={() => handleTradeAction(selectedTrade.id, 'monitor')}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                >
                  {t('trades.actions.monitor')}
                </button>
                {selectedTrade.status === 'disputed' && (
                  <button
                    onClick={() => handleTradeAction(selectedTrade.id, 'resolve')}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  >
                    {t('trades.actions.resolve')}
                  </button>
                )}
                {selectedTrade.status === 'processing' && (
                  <>
                    <button
                      onClick={() => handleTradeAction(selectedTrade.id, 'pause')}
                      className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors"
                    >
                      {t('trades.actions.pause')}
                    </button>
                    <button
                      onClick={() => handleTradeAction(selectedTrade.id, 'complete')}
                      className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                    >
                      {t('trades.actions.complete')}
                    </button>
                  </>
                )}
                <button
                  onClick={() => handleTradeAction(selectedTrade.id, 'message')}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  {t('trades.actions.sendMessage')}
                </button>
                <button
                  onClick={() => handleTradeAction(selectedTrade.id, 'freeze')}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                >
                  {t('trades.actions.freeze')}
                </button>
                <button
                  onClick={() => handleTradeAction(selectedTrade.id, 'export')}
                  className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                >
                  {t('trades.actions.exportData')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
