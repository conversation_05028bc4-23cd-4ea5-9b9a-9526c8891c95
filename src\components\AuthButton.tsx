'use client';

import { useState } from 'react';
import { User, LogIn, UserPlus, Wallet, ChevronDown, Loader2, CheckCircle } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useAuth } from '@/contexts/AuthContext';

interface AuthButtonProps {
  variant?: 'header' | 'mobile';
  className?: string;
}

export default function AuthButton({ variant = 'header', className = '' }: AuthButtonProps) {
  const { t } = useTranslation();
  const { isAuthenticated, user, isWalletConnected, connectWallet, isLoading } = useAuth();
  const [showAuthOptions, setShowAuthOptions] = useState(false);

  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 rtl:space-x-reverse ${className}`}>
        <Loader2 className="w-4 h-4 animate-spin text-primary-500" />
        <span className="text-sm text-gray-500 dark:text-gray-400">{t('common.loading')}</span>
      </div>
    );
  }

  // إذا كان المستخدم مسجل دخول، لا نعرض شيء (سيتم عرض قائمة المستخدم في الهيدر)
  if (isAuthenticated) {
    return null;
  }

  // Header variant - compact
  if (variant === 'header') {
    return (
      <div className={`relative ${className}`}>
        <button
          onClick={() => setShowAuthOptions(!showAuthOptions)}
          className="flex items-center space-x-1 sm:space-x-2 rtl:space-x-reverse bg-gradient-to-r from-blue-500 via-blue-600 to-purple-600 hover:from-blue-600 hover:via-blue-700 hover:to-purple-700 text-white px-3 sm:px-4 py-2 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 group"
        >
          <div className="flex items-center justify-center w-5 h-5 sm:w-6 sm:h-6 bg-white/20 rounded-full group-hover:bg-white/30 transition-colors duration-200">
            <User className="w-3 h-3 sm:w-4 sm:h-4" />
          </div>
          <span className="hidden sm:block text-sm font-medium">{t('auth.getStarted')}</span>
          <span className="sm:hidden text-xs font-medium">{t('auth.start')}</span>
          <ChevronDown className={`w-3 h-3 sm:w-4 sm:h-4 transition-transform duration-200 ${showAuthOptions ? 'rotate-180' : ''}`} />
        </button>

        {/* قائمة منسدلة للمصادقة */}
        {showAuthOptions && (
          <div className="absolute ltr:right-0 rtl:left-0 mt-3 w-72 sm:w-80 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 py-3 z-50 animate-in slide-in-from-top-2 duration-200">
            {/* خيار ربط المحفظة */}
            {!isWalletConnected && (
              <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                <div className="flex items-center space-x-2 rtl:space-x-reverse mb-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-orange-400 to-amber-500 rounded-lg flex items-center justify-center">
                    <Wallet className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-gray-900 dark:text-white">
                      {t('auth.quickStart')}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {t('auth.instantAccess')}
                    </p>
                  </div>
                </div>
                <button
                  onClick={connectWallet}
                  className="w-full flex items-center justify-center space-x-2 rtl:space-x-reverse bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white px-4 py-3 rounded-xl transition-all duration-200 text-sm font-medium shadow-lg hover:shadow-xl transform hover:scale-[1.02] group"
                >
                  <Wallet className="w-4 h-4 group-hover:scale-110 transition-transform duration-200" />
                  <span>{t('auth.connectWalletOnly')}</span>
                </button>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center leading-relaxed">
                  {t('auth.walletOnlyDesc')}
                </p>
              </div>
            )}

            {/* خيارات التسجيل التقليدية */}
            <div className="px-3 py-2 space-y-1">
              <a
                href="/login"
                className="flex items-center space-x-3 rtl:space-x-reverse w-full px-3 py-3 text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 rounded-xl transition-all duration-200 group"
                onClick={() => setShowAuthOptions(false)}
              >
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                  <LogIn className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="text-left rtl:text-right flex-1">
                  <p className="text-sm font-semibold text-gray-900 dark:text-white">{t('navigation.login')}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 leading-relaxed">{t('auth.loginDesc')}</p>
                </div>
              </a>

              <a
                href="/register"
                className="flex items-center space-x-3 rtl:space-x-reverse w-full px-3 py-3 text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 dark:hover:from-green-900/20 dark:hover:to-emerald-900/20 rounded-xl transition-all duration-200 group"
                onClick={() => setShowAuthOptions(false)}
              >
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                  <UserPlus className="w-4 h-4 text-green-600 dark:text-green-400" />
                </div>
                <div className="text-left rtl:text-right flex-1">
                  <p className="text-sm font-semibold text-gray-900 dark:text-white">{t('navigation.register')}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 leading-relaxed">{t('auth.registerDesc')}</p>
                </div>
              </a>
            </div>

            {/* معلومات إضافية */}
            <div className="px-4 py-3 border-t border-gray-200 dark:border-gray-600 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-700/50 dark:to-blue-900/20">
              <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
                <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-3 h-3 text-white" />
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400 text-center font-medium">
                  {t('auth.secureTrading')}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Mobile variant - full width buttons
  return (
    <div className={`space-y-3 ${className}`}>
      {/* ربط المحفظة السريع */}
      {!isWalletConnected && (
        <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-3">
          <p className="text-sm font-medium text-orange-800 dark:text-orange-300 mb-2">
            {t('auth.quickStart')}
          </p>
          <button
            onClick={connectWallet}
            className="w-full flex items-center justify-center space-x-2 rtl:space-x-reverse bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <Wallet className="w-4 h-4" />
            <span>{t('auth.connectWalletOnly')}</span>
          </button>
          <p className="text-xs text-orange-700 dark:text-orange-400 mt-1 text-center">
            {t('auth.walletOnlyDesc')}
          </p>
        </div>
      )}

      {/* أزرار التسجيل التقليدية */}
      <div className="space-y-2">
        <a
          href="/login"
          className="block w-full btn btn-secondary text-center"
        >
          <LogIn className="w-4 h-4 ltr:mr-2 rtl:ml-2" />
          {t('navigation.login')}
        </a>

        <a
          href="/register"
          className="block w-full btn btn-primary text-center"
        >
          <UserPlus className="w-4 h-4 ltr:mr-2 rtl:ml-2" />
          {t('navigation.register')}
        </a>
      </div>

      {/* معلومات إضافية */}
      <div className="text-center">
        <p className="text-xs text-gray-600 dark:text-gray-400">
          {t('auth.secureTrading')}
        </p>
      </div>
    </div>
  );
}
